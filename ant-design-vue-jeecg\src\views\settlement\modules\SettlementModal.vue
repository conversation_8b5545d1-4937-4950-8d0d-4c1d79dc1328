<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <!-- <template slot="closeIcon">
      <a-icon v-show="disableSubmit" type="close"/>
      <a-popconfirm title="确定放弃编辑？" @confirm="handleCancel">
        <div v-show="!disableSubmit" class="closeBtn" @click.stop="" >
          <a-icon type="close" />
        </div>
      </a-popconfirm>
    </template>
    <template slot="footer">
      <a-button v-show="disableSubmit" type="default" @click="handleCancel">关闭</a-button>
      <a-popconfirm title="确定放弃编辑？" @confirm="handleCancel">
        <a-button v-show="!disableSubmit" type="default">取消</a-button>
      </a-popconfirm>
      <a-button v-show="!disableSubmit" type="primary" @click="handleOk">保存</a-button>
    </template> -->
  <template slot="footer">
    <a-button v-show="disableSubmit" type="default" @click="handleCancel">关闭</a-button>
    <a-button v-show="!disableSubmit" type="default" @click="handleCancel">取消</a-button>
    <a-button v-show="!disableSubmit" type="primary" @click="handleOk">保存</a-button>
  </template>
    <SettlementForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></SettlementForm>
  </j-modal>
</template>

<script>
import SettlementForm from './SettlementForm'
export default {
  name: 'SettlementModal',
  components: {
    SettlementForm,
  },
  data() {
    return {
      title: '',
      width: 780,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>