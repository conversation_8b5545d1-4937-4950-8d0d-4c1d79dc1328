<template>
  <a-select
    ref="select"
    :id="id"
    :mode="mode"
    :disabled="disabled"
    :dropdownMenuStyle="{ overflowY: `scroll` }"
    :dictType="dictType"
    v-if="dictType"
    :style="styles"
    :dropdownStyle="{ minWidth: `min-content` }"
    :filter-option="item => item.hidden !== true"
    :placeholder="placeholder"
    :size="size"
    :autoFocus="autoFocus"
    :not-found-content="null"
    :keyFocusTo="keyFocusTo"
    v-focus="vfocusNum"
    @keyup.enter.native="keyFrom($event)"
    @change="returnBackFn"
    @dropdownVisibleChange="search"
    @search="search"
    @select="returnBackSelect"
    @blur="handleBlur"
    allowClear
    option-filter-prop="children"
    show-search
    option-label-prop="label"
    v-model="val"
  >
    <a-select-option
      :key="item.value"
      :label="formatterOptionItem(item)"
      v-for="item in options"
    >{{ item.title }}</a-select-option>
  </a-select>
  <a-select
    ref="select"
    :id="id"
    :mode="mode"
    :disabled="disabled"
    :style="styles"
    :dropdownMenuStyle="{ overflowY: `scroll` }"
    :maxTagTextLength="maxTagTextLength"
    :dictType="dictType"
    :dropdownStyle="{ minWidth: `min-content` }"
    v-else
    :filter-option="item => item.hidden !== true"
    :placeholder="placeholder"
    :size="size"
    @keyup.enter.native="keyFrom($event)"
    :autoFocus="autoFocus"
    :not-found-content="null"
    :keyFocusTo="keyFocusTo"
    v-focus="vfocusNum"
    @change="returnBackFn"
    @dropdownVisibleChange="search"
    @search="search"
    @select="returnBackSelect"
    @inputKeydown="inputKeydown"
    @blur="handleBlur"
    allowClear
    option-filter-prop="children"
    show-search
    option-label-prop="label"
    v-model="val"
  >
    <a-select-option
      :key="item.value"
      :label="formatterOptionItem(item)"
      v-for="item in options"
    >{{item.value + ' ' + item.title }}</a-select-option>
  </a-select>
</template>
<script>
import { axios } from '@/utils/request'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

/**
 * 所有的字典表
 * @type {{customsDict: string, units: string, sysDict: string}}
 */
const dictTableNames = {
  /**
   * 系统字典表
   */
  sysDict: 'sys_dict',
  /**
   * 海关参数
   */
  customsDict: 'SYS_DICT_ITEM',
  /**
   * 计量单位表
   */
  units: 'UNITS'
}
const configMap = []
const configMap1 = [
  // 注释
  {
    /**
     * 表名
     */
    tableName: '',
    dictKey: '',
    /**
     * dictCode解析
     * 用于非系统字典库的数据生成使用
     * 参数说明:
     * key: 去字典库查询所需, 如: 国别地区: GBDQ
     * value: 保存时使用的字段
     * name: 显示的字段
     * table: 字典所在的表名
     * pCode: key所在的列名
     * 传参三种情况
     * 1个参数: key                                    从sys_dict查询
     * 3个参数: table, name, pCode                     从指定表查询
     * 5个参数: table, name, value, key, pCode         从指定表查询
     * 示例: dictCode=CUSTOMS_DICT,ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE
     * CUSTOMS_DICT : 表名
     * ITEM_CODE    : this.value
     * GBDQ         : dictCode
     * P_DICT_CODE  : GBDQ所属列名(分类列名)
     */
    dictCode: '',
    /**
     * splicing解析
     * 数据库返回信息的字段拼接
     * 示例: ITEM_NAME,ITEM_CODE,STANDARD_CODE,MEMO
     * 返回: ITEM_NAME/ITEM_CODE/STANDARD_CODE/MEMO
     */
    splicing: '',
    /**
     * 是否需要通过租户id查询
     */
    byTenant: ''
  },

  // 计量单位
  {
    tableName: dictTableNames.units,
    dictKey: 'UNITS',
    dictCode: `${dictTableNames.units},CN_UNIT_NAME,EN_UNIT_NAME`,
    splicing: 'EN_UNIT_NAME,CN_UNIT_NAME',
    byTenant: true
  },
  // 计量单位-value为数字的情况
  {
    tableName: dictTableNames.units,
    dictKey: 'UNITS_NUM',
    dictCode: `${dictTableNames.units},CN_UNIT_NAME,DECLARE_UNIT_NAME`,
    splicing: 'DECLARE_UNIT_NAME,CN_UNIT_NAME',
    byTenant: true
  },
  // 币制代码
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'BZDM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,BZDM,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME`
  },

  // 币制代码-value为数字的情况
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'BZDM_NUM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_KEY,BZDM,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME`
  },
  // 国别地区-报关单
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GBDQ-DEC',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_NAME,ITEM_CODE`
  },
  // 目的地
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GNDQ',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GNDQ,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
  // 用途
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'YT',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,YT,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
  // 集装箱规格
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'JZXGG',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,JZXGG,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
  // 随附单证代码
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'SFDZDM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,SFDZDM,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
  // 征免方式
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'ZJMSFS',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,ZJMSFS,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
    // 货物属性
    {
        tableName: dictTableNames.customsDict,
        dictKey: 'HWSX',
        dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,HWSX,P_DICT_CODE`,
        splicing: `ITEM_CODE,ITEM_NAME`
    },
  // 国别地区
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GBDQ',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME`
  },
  // 国别地区-value为数字的情况
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GBDQ_NUM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_KEY,GBDQ,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME`
  },
  // 海关合规库计量单位
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'CJDW',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_KEY,CJDW,P_DICT_CODE`,
    splicing: `ITEM_KEY,ITEM_NAME`
  },
  // 海关合规库计量单位数字
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'CJDW_NUM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,CJDW,P_DICT_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  },
    //收件方式
    {
        tableName: dictTableNames.customsDict,
        dictKey: 'SJFS',
        dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,SJFS,P_DICT_CODE`,
        splicing: `ITEM_CODE,ITEM_NAME`
    },

  // 港口代码报关单
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GKDM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GKDM,P_DICT_CODE,STANDARD_CODE`,
    splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME,STANDARD_CODE`
  },
  // 海关代码
  {
    tableName: dictTableNames.customsDict,
    dictKey: 'GQDM',
    dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GQDM,P_DICT_CODE,STANDARD_CODE`,
    splicing: `ITEM_CODE,ITEM_NAME`
  }
]

/**
 * 表格内选择器, 可占用1-2个单元格
 */
export default {
  components: {
  },
  name: 'DictSelect',
  data () {
    return {
      val: undefined,
      // 全部的
      allOptions: [],
      // 显示的
      options: [],
      // 搜索输入框会清空，此字段暂存输入数据
      searchValue: ''
    }
  },
  props: {
    customInput: {
      type: Boolean,
      default: false
    },
    styles: {
      type: String
    },
    /**
     * | 取索引值
     */
    splitIndex: {
      type: Number
    },
    /**
     * 使用v-model, value双向绑定未处理
     */
    value: {},
    /**
     * 提示内容
     */
    placeholder: {
      type: String,
      default: ''
    },
    /**
     * 异步加载的下拉选项
     */
    dictKey: {
      type: String,
      required: true
    },
    /**
     * 表名(字典表表名)
     */
    tableName: {
      type: String,
      default: dictTableNames.sysDict
    },
    // 是否禁用
    disabled: {
      type: Boolean
    },
    //组件id
    id: {
      type: Number
    },
    autoFocus: {
      type: Boolean
    },
    vfocusNum: {
      type: Boolean
    },
    maxTagTextLength: {
      type: Number
    },
    //是否显示
    dictType: {
      type: Boolean
    },
    /**
     * 组件大小
     */
    size: {
      type: String,
      default: 'default'
    },
    /**
     * 是否允许用户创建下拉内容(其中value==title)
     */
    allowCreate: {
      type: Boolean
    },
    keyFocusTo: {
      type: String
    },
    mode: {
      type: String
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  methods: {
    formatterOptionItem (item) {
      if (!this.splitIndex) {
        return item.title
      }

      // 解决带|显示问题
      const title = item.title.split('|')
      if (item.title.includes(this.searchValue) &&
          item.title &&
          title.length > 0) {
        return title[this.splitIndex]
      }
    },
    // 解决下拉中不显示当前字段的问题和手动输入内容的功能
    solveDefaultValue (val) {
      // 可输入
      // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug start
      // if (!!val && this.allowCreate && !~this.options.findIndex((item) => item.title === val)) {
      if (
        !!val &&
        this.allowCreate &&
        !~this.options.findIndex(item => item.title === val) &&
        !~this.options.findIndex(item => item.value === val)
      ) {
        // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug start
        this.options.unshift({
          title: val,
          value: val
        })
      }
      // 下拉不显示当前字段的bug
      if (!this.val) {
        return
      }
      if (!~this.options.findIndex(item => item.value === this.val)) {
      if(this.allOptions){
        let last = this.allOptions.filter(item => item.val === this.val).pop()

        !!last &&
          this.options.push({
            ...last,
            hidden: true
          })

      }
       
      }
    },
    // 初步检索
    initSearch (value) {
			if (!this.allOptions || this.allOptions.length <= 0) {
				return
			}
      if (!value) {
        this.options = this.allOptions.slice(0, 100)
      }
      if (this.allOptions.length <= 100) {
        this.options = this.allOptions.filter(option => this.optionContain(option, value))
        return
      }
      let options = []
      for (let option of this.allOptions) {
        if (this.optionContain(option, value)) {
          options.push(option)
          if (options.length > 10) {
            break
          }
        }
      }

      if (this.mode === 'multiple' && this.val && this.val.length) {
        // 多选回填中文问题
        this.val.forEach(v => {
          let index = options.findIndex(opt => opt.value === v)
          if (index === -1) {
            let optionIndex = this.allOptions.findIndex(option => option.value === v)
            options.push(this.allOptions[optionIndex])
          }
        })
      }

      this.options = options
    },
    /**
     * 判断 option 是否包含value(忽略option大小写)
     * @param option   this.options.item
     * @param value    子串
     * @returns {boolean} 为 true 代表包含
     */
    optionContain (option, value) {
      // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug start
      // return !!~option.title.toLowerCase().indexOf(value) || !!~option.value.toLowerCase().indexOf(value)
      return (
        !!~option.title.toLowerCase().indexOf(value.toLowerCase()) ||
        !!~option.value.toLowerCase().indexOf(value.toLowerCase())
      )
      // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug end
    },
    // 检索
    search (value) {
      if (typeof (value) !== 'boolean' && !!value) {
        this.searchValue = value
      }

      // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug start
      if (!value || (value.trim && !value.trim()) ||
        typeof value !== 'string') {
        value = ''
      }

      // else {
      //     value = value.toLowerCase()
      // }
      // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug end
      this.initSearch(value)
      this.solveDefaultValue(value)
    },
    // v-model使用
    returnBackFn () {
      if (!this.val) {
        this.val = ''
      }
      this.searchValue = this.val
      this.$emit('change', this.val)
    },
    returnBackSelect (value) {
      let option = this.allOptions.filter(el => {
        return el.value == value
      })
      this.$emit('select', option)
    },
    inputKeydown (value) {
      this.$emit('inputKeydown', value)
    },
    // 对比字符串(忽略大小写)
    compareStr (a, b) {
      return a.toLowerCase() === b.toLowerCase()
    },
    /**
     * 海关参数库中的代码, 查询条件统一处理格式
     * @param config
     * @returns {string}
     */
    dictCodeCustomsDictGen (config) {
      // config为空
      if (!config || !config.dictCode) {
        return `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,${this.dictKey},P_DICT_CODE`
      }
      return config.dictCode
    },
    /**
     * 生成url中的dictCode字段
     */
    dictCodeGen () {
      const config = configMap.filter(
        item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName)
      )[0]
      if (this.tableName === dictTableNames.customsDict) {
        return this.dictCodeCustomsDictGen(config)
      }
      return (!!config && config.dictCode) || ''
    },
    /**
     * 生成url中的splicing字段
     */
    splicingGen () {
      const config = configMap.filter(
        item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName)
      )[0]
      return !!config ? config.splicing : ''
    },
    /**
     * 生成url中的byTenant字段
     */
    byTenantGen () {
      const config = configMap.filter(
        item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName)
      )[0]
      return !!config ? !!config.byTenant : false
    },
    /**
     * 加载下拉框可选值
     */
    async loadDict () {
      let url = ''
      if (this.tableName == 'sys_dict') {
        url = ``
        //TODO-DEC
        // url = `/sys/dict/getDictItems` + `/${this.dictKey}`
      } else  if (this.tableName == 'sys_dict_item'){
        url =
          `/sys/dictItem/listDictModelVO` +
          `?dictCode=${this.dictCodeGen()}`
        //TODO-DEC
        // url =
        //   `/sys/dict/getDictItems` +
        //   `?dictCode=${this.dictCodeGen()}` +
        //   `&splicing=${this.splicingGen()}` +
        //   `&byTenant=${this.byTenantGen()}`
      }else if (this.tableName == 'ERP_COUNTRIES'){

      }
      /*const surl = url

      const res = await axios.get(surl)
      this.allOptions = res.result*/
      this.allOptions = getDictItemsFromCache(this.dictKey)
			if (!this.allOptions) {
				ajaxGetDictItems(this.dictKey, null).then((res) => {
					if (res.success) {
						this.allOptions = res.result
						if (res.result) {
							this.allOptions = []
							res.result.forEach(item => {
								this.allOptions.push({
									title: item.value + ' | ' + item.title,
									value: item.value
								})
							})
						}
					}
				})
			}
      if (!this.allOptions && this.dictKey && this.dictKey.includes(',')) {
        //根据字典Code, 初始化字典数组
        await ajaxGetDictItems(this.dictKey, null).then((res) => {
          if (res.success) {
            this.allOptions = res.result
            if (res.result) {
              if (this.dictKey.includes('erp_customs_ports')) {
                this.allOptions = []
                res.result.forEach(item => {
                  this.allOptions.push({
                    title: item.value + ' | ' + item.title,
                    value: item.value
                  })
                })
              }
              if (this.dictKey.includes('erp_countries')) {
                this.allOptions = []
                res.result.forEach(item => {
                  this.allOptions.push({
                    title: item.value + ' | ' + item.title,
                    value: item.value
                  })
                })
              }
							if (this.dictKey.includes('erp_currencies')) {
								this.allOptions = []
								res.result.forEach(item => {
									this.allOptions.push({
										title: item.value + ' | ' + item.name + ' | ' + item.title,
										value: item.value
									})
								})
							}
							if (this.dictKey.includes('erp_units')) {
								this.allOptions = []
								res.result.forEach(item => {
									this.allOptions.push({
										title: item.value + ' | ' + item.title,
										value: item.value
									})
								})
							}
            }
          }
        })
      }
      this.search(this.val)

    },
    keyFrom (event) { },
    inputFocus () {
      this.$refs.select.focus()
      this.$refs.select.$el.click()
    },
    handleBlur () {
      if (this.splitIndex) {
        // 回填中文问题
        let tt
        for (let i = 0; i < this.allOptions.length; i++) {
          const title = this.allOptions[i].title.split('|')
          for (let j = 0; j < title.length; j++) {
            const t = title[j].trim()
            if (t === this.val) {
              tt = t
              return
            }
          }
        }
        this.searchValue = tt
      }

      if (this.customInput) {
        this.val = this.searchValue
        this.$refs.select.blur()
        this.$emit('change', this.val)
      }
    }
  },
  watch: {
    dictKey: {
      handler () {
        this.loadDict()
      }
    },
    value: {
      handler () {
        this.val = this.value
        if (this.mode === 'multiple' && this.val === '') {
          this.val = []
        }

        //修复重新选择后不显示对应字段的对应名称
        this.search(this.val)
      }
    }
  },
  created () {
    if (this.mode === 'multiple') {
      this.val = this.value
      if (!this.val) {
        this.val = []
      } else {
        this.val = [...this.val]
        this.val = this.val.filter((v) => {
          return v && v.trim()
        })
      }
    }
    else {
      if (this.value) {
        this.val = this.value
      }
    }
    if (!!this.dictKey) {
      this.loadDict()
    }
  }
}
</script>
