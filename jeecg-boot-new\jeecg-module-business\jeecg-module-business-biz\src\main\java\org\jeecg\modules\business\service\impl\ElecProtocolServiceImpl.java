package org.jeecg.modules.business.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.paramVo.ElecProtocolVO;
import org.jeecg.modules.business.mapper.DecHeadMapper;
import org.jeecg.modules.business.mapper.DecListMapper;
import org.jeecg.modules.business.mapper.ElecProtocolMapper;
import org.jeecg.modules.business.mapper.SysConfigMapper;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.ICustomerEnterpriseService;
import org.jeecg.modules.business.service.IElecProtocolService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.service.IEnterpriseInfoService;
import org.jeecg.modules.business.util.PrintUtil;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.ElecProtocolMessageUtil;
import org.jeecg.modules.business.util.message.FTPUtil;
import org.jeecg.modules.business.util.message.MessageFileUtil;
import org.jeecg.modules.business.util.message.SFTPUtil;
import org.jeecg.modules.business.util.printing.DataTools;
import org.jeecg.modules.business.vo.DictModelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.*;

/**
 * <p>
 * 电子委托协议管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
public class ElecProtocolServiceImpl extends ServiceImpl<ElecProtocolMapper, ElecProtocol> implements IElecProtocolService {
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Autowired
    private ICustomerEnterpriseService customerEnterpriseService;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendElecPath}") // /ImpPath/Sas/OutBox
//    private String remoteSendElecPath;
@Autowired
private SysConfigMapper sysConfigMapper;
    @Autowired
    private FtpProperties ftpProperties;
    @Value("${jeecg.path.upload}")
    private String upLoadPath;

    /**
     * 分页列表查询
     *
     * @param page
     * @param elecProtocolVO
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/17 14:37
     */
    @Override
    public IPage<ElecProtocol> queryPageList(Page<ElecProtocol> page, ElecProtocolVO elecProtocolVO, HttpServletRequest request) {
        LambdaQueryWrapper<ElecProtocol> queryWrapper = new LambdaQueryWrapper<>();
        if (isNotEmpty(elecProtocolVO)){
            queryWrapper.eq(isNotBlank(elecProtocolVO.getConsignor()), ElecProtocol::getConsignor, elecProtocolVO.getConsignor())
                    .eq(isNotBlank(elecProtocolVO.getElecStatus()),ElecProtocol::getElecStatus,elecProtocolVO.getElecStatus())
                    .like(isNotBlank(elecProtocolVO.getConsignNo()),ElecProtocol::getConsignNo,elecProtocolVO.getConsignNo())
                    .like(isNotBlank(elecProtocolVO.getBillCode()),ElecProtocol::getBillCode,elecProtocolVO.getBillCode());
            queryWrapper.apply(isNotBlank(elecProtocolVO.getStarCreateDate()),
                            "date_format (elec_protocol.CREATE_DATE,'%Y-%m-%d') >= date_format('" + elecProtocolVO.getStarCreateDate() + "','%Y-%m-%d')");
            queryWrapper.apply(isNotBlank(elecProtocolVO.getLastCreateDate()),
                            "date_format (elec_protocol.CREATE_DATE,'%Y-%m-%d') <= date_format('" + elecProtocolVO.getLastCreateDate() + "','%Y-%m-%d')");
        }
        queryWrapper.eq(ElecProtocol::getCustomerId, TenantContext.getTenant());
        IPage<ElecProtocol> elecProtocolIPage = baseMapper.queryPageList(page, queryWrapper);
        return elecProtocolIPage;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public Result<?> getElecProtocolById(String id) {
        ElecProtocol elecProtocol = baseMapper.selectById(id);
        return Result.ok(elecProtocol);
    }

    /**
     * 保存电子委托协议
     *
     * @param elecProtocol
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveElecProtocol(ElecProtocol elecProtocol) {
        // 获取当前登录用户
        LoginUser loginUser;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            loginUser = new LoginUser();
            loginUser.setUsername("recepit");
        }
        if (isBlank(elecProtocol.getCopCusCode())){//操作人单位海关10位编码 当前操作人
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
            if (isEmpty(enterpriseInfo) || isBlank(enterpriseInfo.getCustomsDeclarationCode())){
                return Result.error("未查询到当前操作企业信息的海关十位编码");
            }
            elecProtocol.setCopCusCode(enterpriseInfo.getCustomsDeclarationCode());
        }
        if (isBlank(elecProtocol.getTradeCode())){//经营单位海关10位编码 委托方
            CustomerEnterprise customerEnterprise = customerEnterpriseService.getOne(new LambdaQueryWrapper<CustomerEnterprise>()
                    .eq(CustomerEnterprise::getDepartName, elecProtocol.getConsignorName()));
            if (isEmpty(customerEnterprise) || isBlank(customerEnterprise.getDepartcd())){
                return Result.error("未查询到委托方的企业信息的海关十位编码");
            }
            elecProtocol.setTradeCode(customerEnterprise.getDepartcd());
        }
        if (isBlank(elecProtocol.getAgentCode())){//申报单位海关10位编码 委托方
            CustomerEnterprise customerEnterprise = customerEnterpriseService.getOne(new LambdaQueryWrapper<CustomerEnterprise>()
                    .eq(CustomerEnterprise::getDepartName, elecProtocol.getTrusteeName()));
            if (isEmpty(customerEnterprise) || isBlank(customerEnterprise.getDepartcd())){
                return Result.error("未查询到被委托方的企业信息的海关十位编码");
            }
            elecProtocol.setAgentCode(customerEnterprise.getDepartcd());
        }
        if (isEmpty(elecProtocol.getId())){
            elecProtocol.setCreateBy(loginUser.getUsername());
            elecProtocol.setCreateDate(new Date());
            elecProtocol.setCustomerId(Long.valueOf(TenantContext.getTenant()));
            baseMapper.insert(elecProtocol);
        }else {
            baseMapper.updateById(elecProtocol);
        }
        return Result.ok(elecProtocol);
    }

    /**
     * 批量发送电子委托协议报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/18 08:14
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> sendElecProtocolBatch(String ids) {
        List<ElecProtocol> elecProtocols = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(elecProtocols)) {
            return Result.error("未获取到电子委托协议数据！");
        }
        String errors = "";
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendElecPath());
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
//        MsgFtpConfig ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendElecPath);
        for (ElecProtocol elecProtocol : elecProtocols){
            String fileName = elecProtocol.getId()+".xml";
            boolean uploadFlag = false;
            try {
                if (SFTP.equals(ftpType)){
                    uploadFlag = new SFTPUtil(ftpConfig).upload(fileName,
                            new ByteArrayInputStream(MessageFileUtil.export(ElecProtocolMessageUtil.generateElecMessage(elecProtocol), fileName).toByteArray()));
                } else {
                    uploadFlag = new FTPUtil(ftpConfig).upload(fileName,
                            new ByteArrayInputStream(MessageFileUtil.export(ElecProtocolMessageUtil.generateElecMessage(elecProtocol), fileName).toByteArray()));
                }

            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
                if (isBlank(errors)){
                    errors = new StringBuilder("发送失败的电子委托协议流水号为：").append(elecProtocol.getId()).toString();
                }else {
                    errors = new StringBuilder(errors).append(",").append(elecProtocol.getId()).toString();
                }
                continue;
            }
            if (!uploadFlag){
                if (isBlank(errors)){
                    errors = new StringBuilder("发送失败的电子委托协议流水号为：").append(elecProtocol.getId()).toString();
                }else {
                    errors = new StringBuilder(errors).append(",").append(elecProtocol.getId()).toString();
                }
            }
        }

        if (isNotBlank(errors)){
            return Result.error(errors);
        }

        return Result.ok("发送成功");
    }

    /**
     * 根据报关单自动创建并发送电子委托协议
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/18 14:05
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveAndSendByDecId(String ids) {
        List<DecHead> decHeads = decHeadMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (decHeads == null || decHeads.isEmpty()){
            return Result.error("未找到对应的报关单信息");
        }
        // 获取当前登录用户
        LoginUser loginUser;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            loginUser = new LoginUser();
            loginUser.setUsername("recepit");
        }
        String errors = "";
        String existenceError = "";
        String sendError = "";
        String customerId = TenantContext.getTenant();
        // 国别地区
        Map<String, String> replacedDictMap0 = new HashMap<>();
        Map<String, String> dictMap3 = new HashMap<>();
        try {
            // 币制
            List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,code,currency,1=1");
//            Map<String, String> dictMap3 = new HashMap<>();
            if (isNotEmpty(dictModels3)) {
                dictModels3.forEach(dictModel -> {
                    dictMap3.put(dictModel.getValue(), dictModel.getText());
                });
            }

            List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
            List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
            Map<String, String> dictMap1 = new HashMap<>();
            Map<String, String> dictMap0 = new HashMap<>();
            // 将 dictModel 列表转换为字典 map
            if (isNotEmpty(dictModels1)) {
                dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
            }
            if (isNotEmpty(dictModels0)) {
                dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
            }
            // 使用 dictMap1 中的 value 替换 dictMap0 的 key
//            Map<String, String> replacedDictMap0 = new HashMap<>();
            dictMap0.forEach((key, value) ->
                    replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
            );
        } catch (Exception e) {
            log.error("获取字典出现异常：" + e.getMessage());
        }
        for (DecHead decHead : decHeads) {
            ElecProtocol getElecprotocol = baseMapper.selectOne(new QueryWrapper<ElecProtocol>().lambda()
                    .eq(ElecProtocol::getDecId,decHead.getId()));
            if (getElecprotocol != null){
                if (isBlank(existenceError)){
                    existenceError = new StringBuilder("已经派生过的电子委托协议的报关单：").append(decHead.getId()).toString();
                }else {
                    existenceError = new StringBuilder(existenceError).append(",").append(decHead.getId()).toString();
                }
                continue;
            }
            List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda().eq(DecList::getDecId,decHead.getId()));
            if (decLists == null || decLists.isEmpty()){
                String decListError = String.format("报关单：%s,表体信息为空",decHead.getId());
                if (isBlank(errors)){
                    errors = decListError;
                }else {
                    errors = new StringBuilder(errors).append(",").append(decListError).toString();
                }
                continue;
            }
            DecList decList = decLists.get(0);
            if (isEmpty(decHead.getDeclareUnit())){//申报企业海关十位
                String decListError = String.format("报关单：%s,申报企业海关十位不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = decListError;
                }else {
                    errors = new StringBuilder(errors).append(",").append(decListError).toString();
                }
                continue;
            }
            if (isEmpty(decHead.getTradeTypeCode())){//贸易方式
                String decListError = String.format("报关单：%s,贸易方式不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = decListError;
                }else {
                    errors = new StringBuilder(errors).append(",").append(decListError).toString();
                }
                continue;
            }
            if (isEmpty(decHead.getOptUnitId())){//境内收发货人海关十位
                String decListError = String.format("报关单：%s,境内收发货人海关十位不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = decListError;
                }else {
                    errors = new StringBuilder(errors).append(",").append(decListError).toString();
                }
                continue;
            }
            if (isEmpty(decList.getHsname())){//申报品名
                String error = String.format("报关单：%s,申报品名不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = error;
                }else {
                    errors = new StringBuilder(errors).append(",").append(error).toString();
                }
                continue;
            }
            if (isEmpty(decList.getHscode())){//申报税号
                String error = String.format("报关单：%s,申报税号不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = error;
                }else {
                    errors = new StringBuilder(errors).append(",").append(error).toString();
                }
                continue;
            }
            if (isEmpty(decList.getTotal())){//总价
                String error = String.format("报关单：%s,总价不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = error;
                }else {
                    errors = new StringBuilder(errors).append(",").append(error).toString();
                }
                continue;
            }
            if (isEmpty(decList.getDesCountry())){//原产国
                String error = String.format("报关单：%s,原产国不能为空",decHead.getId());
                if (isBlank(errors)){
                    errors = error;
                }else {
                    errors = new StringBuilder(errors).append(",").append(error).toString();
                }
                continue;
            }

            ElecProtocol elecProtocol = new ElecProtocol();
            elecProtocol.setDecId(Long.valueOf(decHead.getId()));
            elecProtocol.setCustomerId(isNotEmpty(customerId) ? Long.valueOf(customerId) : null);
//            elecProtocol.setSign("GName");//数字签名
//            elecProtocol.setOperType("1");//操作类型
            elecProtocol.setConsignorName(decHead.getOptUnitName());//委托方名称
            elecProtocol.setConsignor(decHead.getOptUnitId());//委托方海关编码
            elecProtocol.setTrusteeName(decHead.getDeclareUnitName());//被委托方名称
            elecProtocol.setCopCusCode(decHead.getDeclareUnit());//操作人单位海关10位编码
            elecProtocol.setIeDate(isNotEmpty(decHead.getOutDate()) ? decHead.getOutDate()
                    : new SimpleDateFormat("yyyyMMdd").format(new Date()));//进出口日期 su 2021-07-28 报关单进出口日期为空的时候取当前日期
            elecProtocol.setBillCode(decHead.getBillCode());//提运单号
            elecProtocol.setTradeMode(decHead.getTradeTypeCode());//贸易方式
            elecProtocol.setTradeCode(decHead.getOptUnitId());//经营单位海关10位编码
            elecProtocol.setAgentCode(decHead.getDeclareUnit());//申报单位海关10位编码
            elecProtocol.setPackingCondition(decHead.getPacksKinds());//包装情况
            elecProtocol.setGName(decList.getHsname());//主要货物名称
            elecProtocol.setCodeTs(decList.getHscode());//HS编码
            elecProtocol.setDeclTotal(decList.getTotal() != null ? decList.getTotal().stripTrailingZeros().setScale(4, BigDecimal.ROUND_HALF_UP).toString() : null);//货物总价
            elecProtocol.setOriCountry(replacedDictMap0.get(decList.getDesCountry()) != null
                    ? replacedDictMap0.get(decList.getDesCountry()) : null);//原产地/货源地
            elecProtocol.setCurr(dictMap3.get(decList.getCurrencyCode()) != null
                    ? dictMap3.get(decList.getCurrencyCode()) : null);//币制代码
            elecProtocol.setQtyOrWeight(decList.getGoodsCount().toString());//数（重）量
            if ("中国重汽集团国际有限公司".equals(decHead.getOptUnitName())){
                elecProtocol.setOtherNote(decHead.getContract());
                elecProtocol.setPaperinfo("011000");
            }

            elecProtocol.setCreateBy(loginUser.getUsername());
            elecProtocol.setCreateDate(new Date());

            //保存电子委托协议信息
            baseMapper.insert(elecProtocol);

            //发送报文
//            MsgFtpConfig ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendElecPath);
            SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
            FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
            MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendSasPath());
            String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
            String fileName = elecProtocol.getId()+".xml";
            boolean uploadFlag = false;
            try {
                if (FTP.equals(ftpType)) {
                    uploadFlag = new FTPUtil(ftpConfig).upload(fileName,
                            new ByteArrayInputStream(MessageFileUtil.export(ElecProtocolMessageUtil.generateElecMessage(elecProtocol), fileName).toByteArray()));
                } else {
                    uploadFlag = new SFTPUtil(ftpConfig).upload(fileName,
                            new ByteArrayInputStream(MessageFileUtil.export(ElecProtocolMessageUtil.generateElecMessage(elecProtocol), fileName).toByteArray()));
                }
            } catch (JAXBException e) {
                e.printStackTrace();
                log.error(e.getMessage());
                if (StringUtils.isBlank(sendError)){
                    sendError = new StringBuilder("电子委托协议发送失败的流水号为：").append(elecProtocol.getId()).toString();
                }else {
                    sendError = new StringBuilder(sendError).append(",").append(elecProtocol.getId()).toString();
                }
                continue;
            }
            if (!uploadFlag){
                if (StringUtils.isBlank(sendError)){
                    sendError = new StringBuilder("电子委托协议发送失败的流水号为：").append(elecProtocol.getId()).toString();
                }else {
                    sendError = new StringBuilder(sendError).append(",").append(elecProtocol.getId()).toString();
                }
                continue;
            }
        }
        if (isNotBlank(errors) || isNotBlank(existenceError) || isNotBlank(sendError)){
            errors = new StringBuilder(errors).append(" ").append(existenceError).append(" ").append(sendError).toString();
            return Result.error(errors);
        }

        return Result.ok("生成电子委托协议成功");
    }

    /**
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        baseMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        return Result.ok("删除成功");
    }

    /**
     * 批量打印电子委托协议
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/1/16 08:54
     */
    @Override
    public void exportElecProtocolBatch(String ids, HttpServletResponse response) {
        try {
            // 获取协议列表
            List<ElecProtocol> elecProtocolList = fetchProtocolsByIds(ids);
            if (isEmpty(elecProtocolList)) {
                throw new RuntimeException("未获取到电子委托协议数据");
            }

            if (elecProtocolList.size() == 1) {
                // 单个文件导出
                ElecProtocol protocol = elecProtocolList.get(0);
                Map<String, Object> data = prepareDataMap(protocol);
                byte[] pdfBytes = generatePdf(data);

                // 设置响应头
                String pdfFileName = URLEncoder.encode(
                        "电子委托协议_" + (isNotBlank(protocol.getConsignNo()) ? protocol.getConsignNo() : protocol.getId()) + ".pdf",
                        "UTF-8");
                response.setContentType("application/pdf");
                response.setHeader("Content-Disposition", "attachment; filename=" + pdfFileName);

                try (ServletOutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(pdfBytes);
                    outputStream.flush();
                }
            } else {
                // 多个文件压缩成 ZIP 导出
                response.setContentType("application/zip");
                String zipFileName = URLEncoder.encode("电子委托协议导出.zip", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);

                try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                    for (ElecProtocol protocol : elecProtocolList) {
                        Map<String, Object> data = prepareDataMap(protocol);
                        byte[] pdfBytes = generatePdf(data);

                        String pdfFileName = "电子委托协议_" +
                                (isNotBlank(protocol.getConsignNo()) ? protocol.getConsignNo() : protocol.getId()) + ".pdf";
                        ZipEntry zipEntry = new ZipEntry(pdfFileName);
                        zipOut.putNextEntry(zipEntry);
                        zipOut.write(pdfBytes);
                        zipOut.closeEntry();
                    }
                }
            }
        } catch (Exception e) {
            log.error("导出电子委托协议失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出失败，请稍后再试");
        }
    }

    /**
     * 获取数据
     *
     * @param ids
     * @return java.util.List<org.jeecg.modules.business.entity.ElecProtocol>
     * <AUTHOR>
     * @date 2025/1/16 16:24
     */
    private List<ElecProtocol> fetchProtocolsByIds(String ids) {
        return baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
    }

    /**
     * 处理数据
     *
     * @param elecProtocol
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/1/16 16:23
     */
    private Map<String, Object> prepareDataMap(ElecProtocol elecProtocol) {
        Map<String, Object> map = new HashMap<>();
        map.put("consignorName", elecProtocol.getConsignorName());
        map.put("trusteeName", elecProtocol.getTrusteeName());
        map.put("gName", elecProtocol.getGName());
        if (isNotBlank(elecProtocol.getEntryId())) {
            map.put("entryId", "No." + elecProtocol.getEntryId());
        }
        map.put("codeTs", elecProtocol.getCodeTs());
        map.put("receiveDate", elecProtocol.getReceiveDate());
        map.put("ieDate", elecProtocol.getIeDate());
        map.put("billCode", elecProtocol.getBillCode());
        if (isNotBlank(elecProtocol.getTradeMode())) {
            List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
            List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getValue()
                            .equals(elecProtocol.getTradeMode()))
                    .collect(Collectors.toList());
            map.put("tradeMode", isNotEmpty(dictModelVO1) ? dictModelVO1.get(0).getText() : "");
        }
        map.put("qtyOrWeight", elecProtocol.getQtyOrWeight());
        map.put("packingCondition", elecProtocol.getPackingCondition());
        map.put("paperinfo", elecProtocol.getPaperinfo());
        if (isNotBlank(elecProtocol.getOriCountry())) {
            List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_countries,name,code");
            Map<String, String> dictMap2 = new HashMap<>();
            if (isNotEmpty(dictModels2)) {
                dictModels2.forEach(dictModel -> {
                    dictMap2.put(dictModel.getValue(), dictModel.getText());
                });
            }
            if(dictMap2.containsKey(elecProtocol.getOriCountry())) {
                map.put("oriCountry", dictMap2.get(elecProtocol.getOriCountry()));
            }
        }
        if (isNotBlank(elecProtocol.getDeclarePrice())) {
            if (isNotBlank(elecProtocol.getCurr())) {
                // 币制
                List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,name,code,1=1");
                Map<String, String> dictMap3 = new HashMap<>();
                if (isNotEmpty(dictModels3)) {
                    dictModels3.forEach(dictModel -> {
                        dictMap3.put(dictModel.getValue(), dictModel.getText());
                    });
                }
                if(dictMap3.containsKey(elecProtocol.getCurr())) {
                    map.put("declarePrice", dictMap3.get(elecProtocol.getCurr()) + ": " + elecProtocol.getDeclarePrice() + "元");
                } else {
                    map.put("declarePrice", elecProtocol.getDeclarePrice());
                }
            } else {
                map.put("declarePrice", elecProtocol.getDeclarePrice());
            }
        }
        map.put("otherNote", elecProtocol.getOtherNote());
        map.put("promiseNote", elecProtocol.getPromiseNote());
        String dateStr = DateUtil.format(new Date(), DatePattern.CHINESE_DATE_PATTERN);
        map.put("dateStr", dateStr);
        map.put("printTime", dateStr + " " + DateUtil.format(new Date(), DatePattern.NORM_TIME_PATTERN));

        // 处理选框逻辑
        String paperinfo = elecProtocol.getPaperinfo();
        if (isNotBlank(paperinfo) && paperinfo.length() == 6) {
            for (int i = 0; i < paperinfo.length(); i++) {
                if (paperinfo.charAt(i) == '1') {
                    map.put("gou" + (i + 1), "On");
                }
            }
        }
        return map;
    }

    /**
     * 生成PDF
     *
     * @param data
     * @return byte[]
     * <AUTHOR>
     * @date 2025/1/16 16:24
     */
    private byte[] generatePdf(Map<String, Object> data) throws Exception {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            PdfReader reader = new PdfReader(this.getClass().getResourceAsStream("/templates/pdf/电子委托协议模板.pdf"));
            PdfStamper stamper = new PdfStamper(reader, bos);
            AcroFields form = stamper.getAcroFields();

            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            ArrayList<BaseFont> fontList = new ArrayList<>();
            fontList.add(bf);
            form.setSubstitutionFonts(fontList);

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (entry.getKey().contains("gou")) {
                    form.setField(entry.getKey(), isNotEmpty(entry.getValue()) ? entry.getValue().toString() : "", true);
                } else {
                    form.setField(entry.getKey(), isNotEmpty(entry.getValue()) ? entry.getValue().toString() : "");
                }
            }

            stamper.setFormFlattening(true);
            stamper.close();
            return bos.toByteArray();
        }
    }
}
