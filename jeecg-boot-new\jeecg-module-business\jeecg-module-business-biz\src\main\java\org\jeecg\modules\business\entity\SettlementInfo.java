package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * SettlementInfo
 * <pre>
 *   结算单位信息实体类
 * </pre>
 *
 * <AUTHOR>  2025/4/23 13:21
 * @version 1.0
 */
@Data
@TableName("settlement_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SettlementInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 结算单位类型(应收单位/应付单位)
     */
    private String settlementType;

    /**
     * 结算单位名称
     */
    private String settlementName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户ID
     */
    private String tenantId;
}
