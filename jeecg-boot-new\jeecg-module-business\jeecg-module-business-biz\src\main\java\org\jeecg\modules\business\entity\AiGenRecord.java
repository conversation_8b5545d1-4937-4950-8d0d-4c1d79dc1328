package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * AI生成票据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
@Accessors(chain = true)
@TableName("ai_gen_record")
public class AiGenRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 类型（1报关单）
     */
    @TableField("TYPE")
    private String type;

    /**
     * 境内收发货人名称
     */
    @TableField("OPT_UNIT_NAME")
    private String optUnitName;

    /**
     * 消费使用单位名称
     */
    @TableField("DELIVER_UNIT_NAME")
    private String deliverUnitName;

    /**
     * 单据流水号
     */
    @TableField("REL_ID")
    private String relId;

    /**
     * 进出口标识 I进口E出口
     */
    @TableField("IE_FLAG")
    private String ieFlag;

    /**
     * 发票号
     */
    @TableField("INVOICE_NO")
    private String invoiceNo;

    /**
     * 合同协议号
     */
    @TableField("CONTRACT")
    private String contract;

    /**
     * 提运单号
     */
    @TableField("BILL_CODE")
    private String billCode;

    /**
     * 租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 是否同步到SSO认证中心
     */
    @TableField("IS_SYNC")
    private Boolean isSyNC;

    /**
     * 是否外部
     */
    @TableField("IS_OUT")
    private Boolean isOut;

    /**
     * 是否重新制单
     */
    @TableField("IS_REPRINT")
    private Boolean isReprint;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;
}
