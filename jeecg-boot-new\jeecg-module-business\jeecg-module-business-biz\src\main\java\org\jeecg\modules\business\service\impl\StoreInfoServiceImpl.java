package org.jeecg.modules.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.StoreAreaMapper;
import org.jeecg.modules.business.mapper.StoreInfoMapper;
import org.jeecg.modules.business.mapper.StoreSpaceMapper;
import org.jeecg.modules.business.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;

/**
 * <p>
 * 仓库基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
@Service
public class StoreInfoServiceImpl extends ServiceImpl<StoreInfoMapper, StoreInfo> implements IStoreInfoService {
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private IStoreSpaceService storeSpaceService;
    @Autowired
    private IAttachmentsInfoService attachmentsInfoService;
    @Autowired
    private StoreAreaMapper storeAreaMapper;
    @Autowired
    private StoreSpaceMapper storeSpaceMapper;
//    @Autowired
//    private IPtsEmsHeadService emsHeadService;

    /**
     * 保存仓库信息
     *
     * @param storeInfo 保存的仓库信息
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStoreInfo(StoreInfo storeInfo) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(storeInfo.getId())) {
            // 2024/1/5 18:20@ZHANGCHAO 追加/变更/完善：保税仓库逻辑！！
            String storeCode;
            if ("1".equals(storeInfo.getPurpose())) {
                StoreInfo selectOne = baseMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                        .eq(StoreInfo::getPurpose, "1"));
                if (isNotEmpty(selectOne)) {
                    return Result.error("该租户下已存在保税用途的仓库[" + selectOne.getStoreCode() + "]，无法继续新建！");
                }
                if (isNotBlank(storeInfo.getStoreCode())) {
                    Long count = baseMapper.selectCount(new LambdaQueryWrapper<StoreInfo>()
                            .eq(StoreInfo::getStoreCode, storeInfo.getStoreCode()));
                    if (count > 0) {
                        return Result.error("该仓库编码已存在，请重新输入！");
                    }
                    storeCode = storeInfo.getStoreCode();
                } else {
                    storeCode = serialNumberService.getSerialnumberByCustomerCode("CK", 4);
                }
            } else {
                storeCode = serialNumberService.getSerialnumberByCustomerCode("CK", 4);
            }
            storeInfo.setStoreCode(storeCode);
            storeInfo.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storeInfo.setCreateDate(new Date());
            baseMapper.insert(storeInfo);
            // 编辑
        } else {
            if ("1".equals(storeInfo.getPurpose())) {
                if (isNotBlank(storeInfo.getStoreCode())) {
                    Long count = baseMapper.selectCount(new LambdaQueryWrapper<StoreInfo>()
                            .eq(StoreInfo::getStoreCode, storeInfo.getStoreCode())
                            .ne(StoreInfo::getId, storeInfo.getId()));
                    if (count > 0) {
                        return Result.error("该仓库编码已存在，请重新输入！");
                    }
                }
                StoreInfo selectOne = baseMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                        .eq(StoreInfo::getPurpose, "1")
                        .ne(StoreInfo::getId, storeInfo.getId()));
                if (isNotEmpty(selectOne)) {
                    return Result.error("该租户下已存在保税用途的仓库[" + selectOne.getStoreCode() + "]，请更改用途！");
                }
            }
            storeInfo.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storeInfo.setUpdateDate(new Date());
            baseMapper.updateById(storeInfo);
        }
        if (isNotEmpty(storeInfo.getStoreSpaceList())) {
            List<StoreSpace> oldStoreSpaceList = storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>()
                    .eq(StoreSpace::getStoreId, storeInfo.getId()));
            if (isNotEmpty(oldStoreSpaceList)) {
                // 使用 Stream 进行过滤
                List<StoreSpace> dels = oldStoreSpaceList.stream()
                        .filter(item -> storeInfo.getStoreSpaceList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (StoreSpace storeSpace : dels) {
                        storeSpaceService.removeById(storeSpace.getId());
                    }
                }
            }
            for (StoreSpace storeSpace : storeInfo.getStoreSpaceList()) {
                if (isBlank(storeSpace.getId())) {
                    storeSpace.setStoreId(storeInfo.getId());
                    storeSpace.setStoreCode(storeInfo.getStoreCode());
                    storeSpace.setSpaceCode(serialNumberService.getSerialnumber("CW", TenantContext.getTenant(), 4));
                    storeSpace.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storeSpace.setCreateDate(new Date());
                    storeSpaceService.save(storeSpace);
                } else {
                    storeSpace.setStoreCode(storeInfo.getStoreCode());
                    storeSpace.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storeSpace.setUpdateDate(new Date());
                    storeSpaceService.updateById(storeSpace);
                }
            }
        }
        StoreInfo resultStoreInfo = baseMapper.selectById(storeInfo.getId());
        storeInfo.setStoreSpaceList(storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>().eq(StoreSpace::getStoreId, resultStoreInfo.getId())));
        return Result.ok(resultStoreInfo);
    }

    /**
     * 根据ID查询仓库信息
     *
     * @param id 要查询的仓库ID
     * @return 查询结果
     */
    @Override
    public Result<?> getStoreInfoById(String id) {
        StoreInfo storeInfo = baseMapper.selectById(id);
        if (isEmpty(storeInfo)) {
            return Result.error("未找到ID为" + id + "的仓库信息，请刷新页面重试！");
        }
        storeInfo.setStoreSpaceList(storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>()
                .eq(StoreSpace::getStoreId, storeInfo.getId())));
        return Result.ok(storeInfo);
    }

    /**
     * 批量删除数据
     *
     * @param ids 要删除的数据的ID集合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        for (String id : ids.split(",")) {
            List<StoreSpace> storeSpaceList = storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>()
                    .eq(StoreSpace::getStoreId, id));
            if (isNotEmpty(storeSpaceList)) {
                for (StoreSpace storeSpace : storeSpaceList) {
                    storeSpaceService.removeById(storeSpace.getId());
                }
            }
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 获取保税仓库信息
     *
     * @param isBondedWarehouse 是否为保税仓库
     * @return 结果对象
     */
    @Override
    public Result<?> getBondedWarehouseInfo(String isBondedWarehouse) {
        List<StoreInfo> storeInfoList = baseMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getPurpose, "1")
                .eq(StoreInfo::getTenantId, TenantContext.getTenant()));
        if (isEmpty(storeInfoList)) {
            return Result.ok(null);
        }
        StoreInfo storeInfo = storeInfoList.get(0);
        storeInfo.setStoreSpaceList(storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>()
                .eq(StoreSpace::getStoreId, storeInfo.getId())));
        List<AttachmentsInfo> attachmentList = attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, storeInfo.getId())
                .orderByDesc(AttachmentsInfo::getCreateTime));
        storeInfo.setAttachmentsInfoList(attachmentList);
//        Result<?> result = emsHeadService.getEmsHeadByStoreCode(storeInfo.getStoreCode());
//        if (result.isSuccess() && result.getResult() != null) {
//            storeInfo.setPtsEmsHead((PtsEmsHead) result.getResult());
//        }
        return Result.ok(storeInfo);
    }

    /**
     * 保存店铺附件
     *
     * @param storeInfo 店铺信息
     * @return 结果对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStoreAttachment(StoreInfo storeInfo) {
        if (isEmpty(storeInfo.getAttachmentsInfoList())) {
            return Result.error("未获取到上传的附件信息！");
        }
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (AttachmentsInfo attachmentsInfo : storeInfo.getAttachmentsInfoList()) {
            attachmentsInfo.setAttachmentsFileType(attachmentsInfo.getAttachmentsFileName().substring(attachmentsInfo.getAttachmentsFileName().lastIndexOf(".") + 1));
            attachmentsInfo.setCreateBy(sysUser.getUsername());
            attachmentsInfo.setCreateTime(new Date());
            attachmentsInfo.setRelationId(String.valueOf(storeInfo.getId()));
            attachmentsInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
            attachmentsInfo.setFileName(attachmentsInfo.getAttachmentsFileName().substring(attachmentsInfo.getAttachmentsFileName().lastIndexOf("/")+1, attachmentsInfo.getAttachmentsFileName().lastIndexOf("_")));
            attachmentsInfoService.save(attachmentsInfo);
        }
        List<AttachmentsInfo> attachmentList = attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, storeInfo.getId())
                .orderByDesc(AttachmentsInfo::getCreateTime));
        return Result.ok(attachmentList);
    }

    /**
     * 保存店铺区域
     *
     * @param storeArea 店铺区域
     * @return 结果对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStoreArea(StoreArea storeArea) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(storeArea.getId())) {
            String areaCode = serialNumberService.getSerialnumberByCustomerCode("KQ", 4);
            storeArea.setAreaCode(areaCode);
            storeArea.setCreateBy(sysUser.getUsername());
            storeArea.setCreateDate(new Date());
            storeAreaMapper.insert(storeArea);
            // 编辑
        } else {
            storeArea.setUpdateBy(sysUser.getUsername());
            storeArea.setUpdateDate(new Date());
            storeAreaMapper.updateById(storeArea);
        }
        return Result.ok(storeArea);
    }

    /**
     * 保存储位
     *
     * @param storeSpace
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/11 13:53
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveSpace(StoreSpace storeSpace) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(storeSpace.getId())) {
            storeSpace.setSpaceCode(serialNumberService.getSerialnumberByCustomerCode("CW", 4));
            storeSpace.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storeSpace.setCreateDate(new Date());
            storeSpaceMapper.insert(storeSpace);
            // 编辑
        } else {
            storeSpace.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storeSpace.setUpdateDate(new Date());
            storeSpaceMapper.updateById(storeSpace);
        }
        return Result.ok(storeSpace);
    }

    /**
     * 储位列表
     *
     * @param pageNo
     * @param pageSize
     * @param storeSpace
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/11 14:13
     */
    @Override
    public Result<?> listSpace(Integer pageNo, Integer pageSize, StoreSpace storeSpace, HttpServletRequest req) {
        Page<StoreSpace> page = new Page<>(pageNo, pageSize);
        IPage<StoreSpace> pageList = storeSpaceService.page(page, new LambdaQueryWrapper<StoreSpace>()
                .eq(StoreSpace::getStoreId, storeSpace.getStoreId())
                .eq(StoreSpace::getTenantId, TenantContext.getTenant())
                .like(isNotBlank(storeSpace.getCabinCode()) , StoreSpace::getCabinCode, storeSpace.getCabinCode())
                .like(isNotBlank(storeSpace.getSpaceName()) , StoreSpace::getSpaceName, storeSpace.getSpaceName())
                .orderByDesc(StoreSpace::getCreateDate));
        return Result.ok(pageList);
    }

}
