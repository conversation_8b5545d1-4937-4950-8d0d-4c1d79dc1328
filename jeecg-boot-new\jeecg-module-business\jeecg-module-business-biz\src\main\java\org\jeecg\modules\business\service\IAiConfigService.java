package org.jeecg.modules.business.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.AiConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * AI配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface IAiConfigService extends IService<AiConfig> {
    /**
     * 保存AI配置
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/18 23:59
     */
    Result<?> saveAiSetting(AiConfig aiConfig);

    /**
     * 设置默认AI配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/30 14:19
     */
    Result<?> setDefaultAiConfig(String ids);

    /**
     * 根据ID查询AI配置
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:09
     */
    Result<?> getAiSettingById(String id);

    /**
     * AI配置列表
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:14
     */
    Result<?> listAiSettings(AiConfig aiConfig);

    /**
     * 删除Ai配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:21
     */
    Result<?> deleteAiConfigBatch(String ids);
}
