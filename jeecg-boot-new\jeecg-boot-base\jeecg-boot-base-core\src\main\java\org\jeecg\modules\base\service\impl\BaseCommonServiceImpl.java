package org.jeecg.modules.base.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.modules.base.mapper.BaseCommonMapper;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.IpUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * @Description: common实现类
 * @author: jeecg-boot
 */
@Service
@Slf4j
public class BaseCommonServiceImpl implements BaseCommonService {

    @Resource
    private BaseCommonMapper baseCommonMapper;

    @Override
    public void addLog(LogDTO logDTO) {
        if(oConvertUtils.isEmpty(logDTO.getId())){
            logDTO.setId(String.valueOf(IdWorker.getId()));
        }
        try {
            //获取request
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            //设置IP地址
            logDTO.setIp(IpUtils.getIpAddr(request));
        } catch (Exception e) {
            logDTO.setIp("127.0.0.1");
        }
        //获取登录用户信息
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            logDTO.setUserid(user.getUsername());
            logDTO.setUsername(user.getRealname());
        } catch (Exception e) {
            //e.printStackTrace();
        }
        logDTO.setCreateTime(new Date());
        //保存日志（异常捕获处理，防止数据太大存储失败，导致业务失败）JT-238
        try {
            baseCommonMapper.saveLog(logDTO);
        } catch (Exception e) {
            log.warn(" LogContent length : "+logDTO.getLogContent().length());
            log.warn(e.getMessage());
        }
    }

    @Override
    public void addLog(String logContent, Integer logType, Integer operatetype, LoginUser user) {
        LogDTO sysLog = new LogDTO();
        sysLog.setId(String.valueOf(IdWorker.getId()));
        //注解上的描述,操作日志内容
        sysLog.setLogContent(logContent);
        sysLog.setLogType(logType);
        sysLog.setOperateType(operatetype);
        try {
            //获取request
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            //设置IP地址
            sysLog.setIp(IpUtils.getIpAddr(request));
        } catch (Exception e) {
            sysLog.setIp("127.0.0.1");
        }
        //获取登录用户信息
        if(user==null){
            try {
                user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            } catch (Exception e) {
                //e.printStackTrace();
            }
        }
        if(user!=null){
            sysLog.setUserid(user.getUsername());
            sysLog.setUsername(user.getRealname());
        }
        sysLog.setCreateTime(new Date());
        //保存日志（异常捕获处理，防止数据太大存储失败，导致业务失败）JT-238
        try {
            baseCommonMapper.saveLog(sysLog);
        } catch (Exception e) {
            log.warn(" LogContent length : "+sysLog.getLogContent().length());
            log.warn(e.getMessage());
        }
    }

    /**
     * 保存日志
     *
     * @param logContent
     * @param logType
     * @param operateType
     * @param user
     * @param sourceId
     */
    @Override
    public void addLog(String logContent, Integer logType, Integer operateType, String user, String sourceId, boolean isSuccess) {
        LogDTO sysLog = new LogDTO();
        sysLog.setId(String.valueOf(IdWorker.getId()));
        //注解上的描述,操作日志内容
        sysLog.setLogContent(logContent);
        sysLog.setLogType(logType);
        sysLog.setOperateType(operateType);
        sysLog.setSourceId(sourceId);
        sysLog.setIsSuccess(isSuccess);
        try {
            //获取request
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            //设置IP地址
            sysLog.setIp(IpUtils.getIpAddr(request));
        } catch (Exception e) {
            sysLog.setIp("127.0.0.1");
        }
        //获取登录用户信息
        if(isNotBlank(user)){
            sysLog.setUserid(user);
            sysLog.setUsername(user);
        } else {
            try {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                if (isNotEmpty(loginUser)) {
                    sysLog.setUserid(loginUser.getUsername());
                    sysLog.setUsername(loginUser.getRealname());
                } else {
                    sysLog.setUsername("unknow");
                }
            } catch (Exception e) {
                ExceptionUtil.getMessage(e);
                sysLog.setUserid("unknow");
                sysLog.setUsername("unknow");
            }
        }
        sysLog.setCreateTime(new Date());
        //保存日志（异常捕获处理，防止数据太大存储失败，导致业务失败）JT-238
        try {
            baseCommonMapper.saveLog(sysLog);
        } catch (Exception e) {
            log.warn(" LogContent length : "+sysLog.getLogContent().length());
            log.warn(e.getMessage());
        }
    }

    /**
     * 保存日志
     *
     * @param logDTO
     */
    @Override
    public LogDTO saveLog(LogDTO logDTO) {
        if(oConvertUtils.isEmpty(logDTO.getId())){
            logDTO.setId(String.valueOf(IdWorker.getId()));
        }
        try {
            //获取request
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            //设置IP地址
            logDTO.setIp(IpUtils.getIpAddr(request));
        } catch (Exception e) {
            logDTO.setIp("127.0.0.1");
        }
        //获取登录用户信息
        if(isNotBlank(logDTO.getUserid())){
            logDTO.setUserid(logDTO.getUserid());
            logDTO.setCreateBy(logDTO.getUserid());
            logDTO.setUsername(logDTO.getUserid());
        } else {
            try {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                if (isNotEmpty(loginUser)) {
                    logDTO.setUserid(loginUser.getUsername());
                    logDTO.setCreateBy(loginUser.getUsername());
                    logDTO.setUsername(loginUser.getRealname());
                } else {
                    logDTO.setUserid("unknow");
                    logDTO.setCreateBy("unknow");
                    logDTO.setUsername("unknow");
                }
            } catch (Exception e) {
                ExceptionUtil.getMessage(e);
                logDTO.setUserid("unknow");
                logDTO.setCreateBy("unknow");
                logDTO.setUsername("unknow");
            }
        }
        logDTO.setCreateTime(new Date());
        //保存日志（异常捕获处理，防止数据太大存储失败，导致业务失败）JT-238
        try {
            baseCommonMapper.saveLog(logDTO);
        } catch (Exception e) {
            log.warn(" LogContent length : "+logDTO.getLogContent().length());
            log.warn(e.getMessage());
        }
        return logDTO;
    }

    @Override
    public void addLog(String logContent, Integer logType, Integer operateType) {
        addLog(logContent, logType, operateType, null);
    }

    /**
     * @param remark
     * @param isSuccess
     */
    @Override
    public void updateLog(Integer operateType, String remark, boolean isSuccess, String id) {
        baseCommonMapper.updateLog(operateType, remark, isSuccess, id);
    }


}
