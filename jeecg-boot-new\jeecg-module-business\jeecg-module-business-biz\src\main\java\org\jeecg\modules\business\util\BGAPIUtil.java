package org.jeecg.modules.business.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @create 2024/12/31 14:31
 */
@Slf4j
public class BGAPIUtil {
    //货物申报资源包地址
    private static String HWSBBaseURL="http://39.97.55.7:5000/api/sw/";
    //加贸业务资源包地址
    private static String JMSBBaseURL="http://39.97.55.7:5000/api/pt/";

    private static String HWSBAPPID = "ECF1C87F-FEF6-49C6-884D-BDEE79A8D06F";
    private static String JMSBAPPID = "15B22E65-7FD0-40B5-9EE6-7F12D1417B67";
    private static String userid = "SDJGSOFT";
    private static String swid = "2100090037794";

    private static String MD5Encrypt(String EncryptString){
        try {
            // 获取MD5 MessageDigest实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 使用UTF-8编码将字符串转为字节数组
            byte[] bytes = EncryptString.getBytes(StandardCharsets.UTF_8);

            // 执行加密
            byte[] md5Bytes = md.digest(bytes);

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5Bytes) {
                hexString.append(String.format("%02x", b));
            }

//           System.out.println("MD5加密结果: " + hexString.toString());
            return  hexString.toString();
        }  catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * OCR识别报关单
     * @param links
     * @return
     */
    public static String Ydt_OcrRecognition(String links){
        String url="http://auth.feiliankeji.cn/Fly_BgApi/Ydt_OcrRecognition?fileUrl="+links;
        Map<String, Object> param = new HashMap<>();
        String resp = HttpRequest.post(url)
                .header("Token", "Hz9/k8KP8n42YuKPwkd0k1KG8fI94IHPezW/L9xFn3Qm6Wbp7Izn5Z9qNGqN+HtTruuP5yNMZWIrxMAmIVhNWRCeQjnYUGfkV2tJsKqh5wWPKqhKuygz81BJse8JJPR3NwAWui14pRDlEO4u1xVD7Um9LvGoR1lowyo+D7XXPK8=")
                .timeout(1000*60)
                .execute()
                .body();
        return resp;
    }
    /**
     * 通过料号/序号获取手/账册备案信息
     * @param systemId 子系统代码（必填） 可选范围为：SC/ZC/WL，分别代表加贸手册，加贸账册，物流账册
     * @param tradeCode 企业的 10 位海关代码（必填）
     * @param manualNo 手/账册编号（必填
     * @param gdsMtNo 料号（精确查询，选填）
     * @param gdsSeqNo 序号（精确查询，选填）
     * @param flag 1=料件 2=成品（默认为 1，物流账册中 flag 参数无效）
     * @return
     */
    public static String GetManualItem(String systemId, String tradeCode,
                                       String manualNo, String gdsMtNo,
                                       String gdsSeqNo,String flag,String swidC){

        String params1 = systemId +
                tradeCode +
                manualNo +
                Objects.toString(gdsMtNo,"") +
                Objects.toString(gdsSeqNo,"") +
                flag;
        String token = MD5Encrypt(JMSBAPPID + swidC + params1).toLowerCase();

        Map<String, Object> data = new LinkedHashMap<>();
        data.put("systemId", systemId);
        data.put("tradeCode", tradeCode);
        data.put("manualNo", manualNo);
        data.put("gdsMtNo", gdsMtNo);
        data.put("gdsSeqNo", gdsSeqNo);
        data.put("flag",flag);//1=料件 2=成品（默认为 1，物流账册中 flag 参数无效）

        // 构造外部的JSON对象
        Map<String, Object> jsonMap =  new LinkedHashMap<>();
        jsonMap.put("userid", userid);
        jsonMap.put("token", token);
        jsonMap.put("swid", swidC);
        jsonMap.put("data", data);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String resp = HttpUtil.post(JMSBBaseURL+"GetManualItem", JSON.toJSONString(jsonMap));


        return resp;
    }
}
