package org.jeecg.modules.business.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Collections;

/**
 * 代码生成器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/3/14 9:53
 */
public class AutoGenerator {
    /**
     * 数据源配置
     */
    private static final DataSourceConfig.Builder DATA_SOURCE_CONFIG = new DataSourceConfig
            .Builder("*****************************************************************************************************************************************************************************************",
            "root",
            "2020jieguan2023")
            .typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                int typeCode = metaInfo.getJdbcType().TYPE_CODE;
//                System.out.println(metaInfo.getJdbcType() + " = " + typeCode);
                if (typeCode == Types.TINYINT) {
                    // 自定义类型转换
                    return DbColumnType.BOOLEAN;
                }
                if (typeCode == Types.TIMESTAMP) {
                    // 自定义类型转换
                    return DbColumnType.DATE;
                }
                return typeRegistry.getColumnType(metaInfo);
            });
//    private static final DataSourceConfig.Builder DATA_SOURCE_CONFIG = new DataSourceConfig
//            .Builder("*********************************************************", "JGLog", "JGLog123");

    /**
     * 执行 run
     */
    public static void main(String[] args) throws SQLException {

        String projectPath = System.getProperty("user.dir"); // D:\yorma-project-new\TradeServicePlatform
        System.out.println(projectPath);

        FastAutoGenerator.create(DATA_SOURCE_CONFIG)
                .globalConfig(builder -> {
                    builder.author("ZHANGCHAO") // 设置作者
                            .disableOpenDir()
//                            .dateType(DateType.ONLY_DATE)
                            .outputDir(projectPath + "/jeecg-boot-new/jeecg-module-business/jeecg-module-business-biz" + "/src/main/java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("org.jeecg.modules") // 设置父包名
                            .moduleName("business") // 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/jeecg-boot-new/jeecg-module-business/jeecg-module-business-biz" + "/src/main/java/org/jeecg/modules/business/mapper/xml")
                            ); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("ai_log") // 设置需要生成的表名
//                            .addTablePrefix("pts_")
                            .entityBuilder()
                            .enableLombok()
                            .enableChainModel()
                            .enableTableFieldAnnotation()
                            .idType(IdType.ASSIGN_ID)
                            .enableFileOverride()
                            .mapperBuilder().enableFileOverride()
                            .serviceBuilder().enableFileOverride()
                            .controllerBuilder().enableFileOverride()
                            .enableRestStyle();
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
