package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.InOrOutStorageDetailDTO;
import org.jeecg.modules.business.entity.dto.StoreStocksDTO;
import org.jeecg.modules.business.entity.enums.OptTypeEnum;
import org.jeecg.modules.business.entity.enums.StockTypeEnum;
import org.jeecg.modules.business.entity.excel.InStorageDetailEntity;
import org.jeecg.modules.business.entity.excel.OutStorageDetailEntity;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.ICommissionerService;
import org.jeecg.modules.business.service.IStoreStocksService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.service.ISysStocksFlowLogService;
import org.jeecg.modules.business.util.excel.ExcelExportStylerBorderImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.modules.business.util.StockUtil.*;

/**
 * <p>
 * 仓库库存建账表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
@Service
public class StoreStocksServiceImpl extends ServiceImpl<StoreStocksMapper, StoreStocks> implements IStoreStocksService {
    @Autowired
    private StoreStocksFlowMapper storeStocksFlowMapper;
    @Autowired
    private StorageDetailMapper storageDetailMapper;
    @Autowired
    private ISysStocksFlowLogService stocksFlowLogService;
    @Autowired
    private ICommissionerService commissionerService;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private StoreInfoMapper storeInfoMapper;
    @Autowired
    private StorageInfoMapper storageInfoMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysConfigMapper sysConfigMapper;

    /**
     * 保存或更新库存和流量
     *
     * @param storageDetail
     * @return string
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveOrUpdateStocksAndFlow(StorageDetail storageDetail) {
        StringBuilder sb = new StringBuilder();
        // 入库
        if ("I".equals(storageDetail.getIeFlag())) {
            // 库存记账/核增库存处理器
            Result<?> addStockHandleResult = addStockHandle(storageDetail);
            if (!addStockHandleResult.isSuccess()) {
                throw new RuntimeException(addStockHandleResult.getMessage());
            }
            // 出库
        } else if ("E".equals(storageDetail.getIeFlag())) {
            // 核减库存/占用处理器
//            Result<?> reduceStockHandleResult = reduceStockHandle(storageDetail);
            Result<?> reduceStockHandleResult = reduceStockHandleOneToMany(storageDetail);
            if (reduceStockHandleResult.isSuccess()) {
                storageDetailMapper.update(null, new UpdateWrapper<StorageDetail>().lambda()
                        .set(StorageDetail::getStocksFlowId, null)
                        .eq(StorageDetail::getId, storageDetail.getId()));
            } else {
                throw new RuntimeException(reduceStockHandleResult.getMessage());
            }
        } else {
            throw new RuntimeException("未知的出入库标识，执行失败！");
        }
        log.info("最终处理结果：" + sb);
        return String.valueOf(sb);
    }

    /**
     * 查询出库单商品库存
     *
     * @param storeCode
     * @param customer
     * @param itemNumber
     * @param copGno
     * @param batchNo
     * @param spaceName
     * @return
     */
    @Override
    public Result<?> getStoreStocksBy4Cond(String storeCode, String customer, String itemNumber, String copGno, String batchNo, String spaceName) {
        StoreStocks storeStocks = new StoreStocks();
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, storeCode)
                .eq(StoreStocks::getCustomer, customer)
                .eq(StoreStocks::getCopGno, copGno)
                .eq(StoreStocks::getItemNumber, itemNumber)
                .eq(isNotBlank(batchNo), StoreStocks::getBatchNo, batchNo)
                .eq(isNotBlank(spaceName), StoreStocks::getSpaceName, spaceName)
                .eq(StoreStocks::getTenantId, TenantContext.getTenant());
        List<StoreStocks> storeStockss = baseMapper.selectList(lambdaQueryWrapper);
        if (isNotEmpty(storeStockss)) {
            BigDecimal beginQty = BigDecimal.ZERO;
            BigDecimal occupyQty = BigDecimal.ZERO;
            for (StoreStocks stockss : storeStockss) {
                beginQty = beginQty.add(isNotEmpty(stockss.getBeginQty()) ? stockss.getBeginQty() : BigDecimal.ZERO);
                occupyQty = occupyQty.add(isNotEmpty(stockss.getOccupyQty()) ? stockss.getOccupyQty() : BigDecimal.ZERO);
            }
            storeStocks.setBeginQty(beginQty); // 实际库存数量
            storeStocks.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
            storeStocks.setOccupyQty(occupyQty); // 占用数量
            storeStocks.setBatchNo(storeStockss.get(0).getBatchNo());
            storeStocks.setAreaCode(storeStockss.get(0).getAreaCode());
            storeStocks.setSpaceCode(storeStockss.get(0).getSpaceCode());
        }
        return Result.ok(storeStocks);
    }

    /**
     * @param storageDetail
     * @return
     */
    @Override
    public Result<?> getStoreStocksBy4ModelCond(StorageDetail storageDetail) {
        return getStoreStocksBy4Cond(storageDetail.getStoreCode(), storageDetail.getCustomer(), storageDetail.getItemNumber(), storageDetail.getCopGno(), storageDetail.getBatchNo(), storageDetail.getSpaceName());
    }

    /**
     * 根据条件获取指定商品信息的库存
     *
     * @param batchNo 批次号
     * @param copGno 物料号
     * @param storeCode      商店代码
     * @return 获取库存的结果
     */
    @Override
    public Result<?> getStoreStocksByCond(String customer, String batchNo, String copGno, String storeCode) {
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, storeCode)
                .eq(StoreStocks::getCustomer, customer)
                .eq(StoreStocks::getCopGno, copGno)
                .eq(StoreStocks::getTenantId, TenantContext.getTenant());
        if (isNotBlank(batchNo)) {
            lambdaQueryWrapper.eq(StoreStocks::getBatchNo, batchNo);
        } else {
            lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
        }
        StoreStocks storeStocks = baseMapper.selectOne(lambdaQueryWrapper);
        if (isNotEmpty(storeStocks)) {
            BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
            BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
            storeStocks.setBeginQty(beginQty); // 实际库存数量
            storeStocks.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
            storeStocks.setOccupyQty(occupyQty); // 占用数量
        }
        return Result.ok(storeStocks);
    }

    /**
     * 根据条件获取指定商品信息的库存
     *
     * @param storeCode      商店代码
     * @return 获取库存的结果
     */
    @Override
    public Result<?> getStoreStocksByCond_(String customer, String batchNos, String copGnos, String storeCode) {
        LambdaQueryWrapper<StoreStocks> queryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, storeCode)
                .eq(StoreStocks::getCustomer, customer)
                .eq(StoreStocks::getTenantId, TenantContext.getTenant())
                .in(StoreStocks::getCopGno, Arrays.asList(copGnos.split(",")));
        if (isNotBlank(batchNos)) {
            queryWrapper.in(StoreStocks::getBatchNo, Arrays.asList(batchNos.split(",")));
        } else {
            queryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
        }
        List<StoreStocks> storeStocksList = baseMapper.selectList(queryWrapper);
        Map<String, String> stockQtyMap = new HashMap<>();
        if (isNotEmpty(storeStocksList)) {
            Map<String, List<StoreStocks>> storeStocksMap = storeStocksList.stream().collect(Collectors.groupingBy(i -> customer + i.getBatchNo() + i.getCopGno() + storeCode));
            for (Map.Entry<String, List<StoreStocks>> entry : storeStocksMap.entrySet()) {
                BigDecimal sumQty = entry.getValue().stream().map(StoreStocks::getBeginQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal occupyQty = entry.getValue().stream().map(StoreStocks::getOccupyQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                stockQtyMap.put(entry.getKey(), sumQty.stripTrailingZeros().toPlainString() + "|" + (sumQty.subtract(occupyQty)).stripTrailingZeros().toPlainString() + "|" + occupyQty.stripTrailingZeros().toPlainString());
            }
        }
        return Result.ok(stockQtyMap);
    }

    /**
     * 根据分页信息和查询条件查询店铺库存列表
     *
     * @param storeStocksDTO 店铺库存条件
     * @return 分页后的店铺库存列表
     */
    @Override
    public Result<?> queryPageList(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize) {
        /*
         * 企业数据隔离
         * 2024/8/21 下午3:46@ZHANGCHAO
         */
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
            storeStocksDTO.setCustomer(loginUser.getUserTypeRel());
        }
        Page<StoreStocks> page = new Page<>(pageNo, pageSize);
        storeStocksDTO.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<StoreStocks> pageList = baseMapper.queryPageList(page, storeStocksDTO);
        if (isNotEmpty(pageList.getRecords())) {
            Result<?> summaryResult = this.loadTotalSummary(storeStocksDTO);
            Map<String, Object> summaryData = null;
            if (summaryResult.isSuccess() && summaryResult.getResult() != null) {
                summaryData = (Map<String, Object>) summaryResult.getResult();
            }
            List<StoreStocks> records = pageList.getRecords();
            for (StoreStocks storeStocks : records) {
                // 设置汇总数据到每条记录
                if (summaryData != null) {
                    storeStocks.setSummary(summaryData);
                }
                if (isNotBlank(storeStocks.getCustomer())) {
                    String tenantName = null;
                    try {
                        // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                        Result<Tenant> tenant = sysBaseApi.getTenantById(storeStocks.getCustomer());
                        if (isNotEmpty(tenant.getResult())) {
                            tenantName = tenant.getResult().getName();
                        }
                    } catch (Exception e) {
                        log.error("获取租户名出现异常：{}", e.getMessage());
                    }
                    Commissioner commissioner = commissionerService.getById(storeStocks.getCustomer());
                    if (isNotEmpty(commissioner)) {
                        storeStocks.setCustomerStr(commissioner.getCommissionerFullName());
                    } else {
                        storeStocks.setCustomerStr(tenantName);
                    }
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 根据分页信息和查询条件查询店铺库存列表
     *
     * @param storeStocksDTO 店铺库存条件
     * @return 分页后的店铺库存列表
     */
    @Override
    public Result<?> queryPageList_(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize) {
        /*
         * 企业数据隔离
         * 2024/8/21 下午3:46@ZHANGCHAO
         */
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
            storeStocksDTO.setCustomer(loginUser.getUserTypeRel());
        }
        Page<StoreStocks> page = new Page<>(pageNo, pageSize);
        storeStocksDTO.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<StoreStocks> pageList = baseMapper.queryPageList_(page, storeStocksDTO);
        if (isNotEmpty(pageList.getRecords())) {
            List<StoreStocks> records = pageList.getRecords();
            for (StoreStocks storeStocks : records) {
                storeStocks.setAvailableQty((isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO)
                        .subtract((isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO)));
                if (isNotBlank(storeStocks.getCustomer())) {
                    String tenantName = null;
                    try {
                        // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                        Result<Tenant> tenant = sysBaseApi.getTenantById(storeStocks.getCustomer());
                        if (isNotEmpty(tenant.getResult())) {
                            tenantName = tenant.getResult().getName();
                        }
                    } catch (Exception e) {
                        log.error("获取租户名出现异常：{}", e.getMessage());
                    }
                    Commissioner commissioner = commissionerService.getById(storeStocks.getCustomer());
                    if (isNotEmpty(commissioner)) {
                        storeStocks.setCustomerStr(commissioner.getCommissionerFullName());
                    } else {
                        storeStocks.setCustomerStr(tenantName);
                    }
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 获取商品流转页面
     *
     * @param page          分页对象
     * @param storeStocksDTO     店铺名称
     * @return 商品流转页面对象
     */
    @Override
    public IPage<StoreStocksFlow> listGoodsFlow(Page<StoreStocksFlow> page, StoreStocksDTO storeStocksDTO) {
        storeStocksDTO.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<StoreStocksFlow> pageList = storeStocksFlowMapper.listGoodsFlow(page, storeStocksDTO);
        try {
            if (isNotEmpty(pageList.getRecords())) {
                Page<StoreStocksFlow> page1 = new Page<>(1, 999999);
                IPage<StoreStocksFlow> pageList1 = storeStocksFlowMapper.listGoodsFlow(page1, storeStocksDTO);
                BigDecimal beginQty = BigDecimal.ZERO;
                BigDecimal occupancyQty = BigDecimal.ZERO;
                for (StoreStocksFlow storeStocksFlow : pageList1.getRecords()) {
                    beginQty = beginQty.add(isNotEmpty(storeStocksFlow.getStockVar()) ? storeStocksFlow.getStockVar() : BigDecimal.ZERO);
                    occupancyQty = occupancyQty.add(isNotEmpty(storeStocksFlow.getOccupyVar()) ? storeStocksFlow.getOccupyVar() : BigDecimal.ZERO);
                }
                log.info("流水展示：商品【{}-{}-{}-{}】库存数量：{}，占用数量：{}", storeStocksDTO.getItemNumber(), storeStocksDTO.getBatchNo(), storeStocksDTO.getCopGno(), storeStocksDTO.getSpaceName(), beginQty, occupancyQty);
            }
        } catch (Exception e) {
            log.error("流水展示失败哟~~~");
        }
        return pageList;
    }

    /**
     * 根据出入库单表体获取对应的仓库库存信息
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    @Override
    public Result<StoreStocks> getStockByDetail(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer())) {
            return Result.error("未知客户类型！");
        }
        // 2024/8/23 上午7:39@ZHANGCHAO 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, storageDetail.getStoreCode())
                .eq(StoreStocks::getCustomer, storageDetail.getCustomer())
                .eq(StoreStocks::getCopGno, storageDetail.getCopGno())
                .eq(StoreStocks::getItemNumber, storageDetail.getItemNumber())
//                .eq(StoreStocks::getBatchNo, storageDetail.getBatchNo())
//                .eq(StoreStocks::getSpaceName, storageDetail.getSpaceName())
                .eq(StoreStocks::getTenantId, (isBlank(TenantContext.getTenant()) || "0".equals(TenantContext.getTenant())) ? storageDetail.getTenantId() : Long.parseLong(TenantContext.getTenant()));
        if (isNotBlank(storageDetail.getBatchNo())) {
            lambdaQueryWrapper.eq(StoreStocks::getBatchNo, storageDetail.getBatchNo());
        } else {
            lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
        }
        if (isNotBlank(storageDetail.getSpaceName())) {
            lambdaQueryWrapper.eq(StoreStocks::getSpaceName, storageDetail.getSpaceName());
        } else {
            lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getSpaceName).or().eq(StoreStocks::getSpaceName, ""));
        }
        StoreStocks storeStocks = baseMapper.selectOne(lambdaQueryWrapper);
        return Result.ok(storeStocks);
    }

    /**
     * 根据维修单表体获取对应的仓库库存信息
     *
     * @param repairOrderDetail
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StoreStocks>
     * <AUTHOR>
     * @date 2024/8/9 下午3:35
     */
    @Override
    public Result<StoreStocks> getStockByRepairDetail(StorageRepairOrderDetail repairOrderDetail) {
        if (isBlank(repairOrderDetail.getCustomer())) {
            return Result.error("未知客户类型！");
        }
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, repairOrderDetail.getStoreCode())
                .eq(StoreStocks::getCustomer, repairOrderDetail.getCustomer())
                .eq(StoreStocks::getCopGno, repairOrderDetail.getCopGno())
                .eq(StoreStocks::getItemNumber, repairOrderDetail.getItemNumber())
                .eq(StoreStocks::getBatchNo, repairOrderDetail.getBatchNo())
                .eq(StoreStocks::getSpaceName, repairOrderDetail.getSpaceName())
                .eq(StoreStocks::getTenantId, TenantContext.getTenant());
//        if (isNotBlank(repairOrderDetail.getBatchNo())) {
//            lambdaQueryWrapper.eq(StoreStocks::getBatchNo, repairOrderDetail.getBatchNo());
//        } else {
//            lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
//        }
        StoreStocks storeStocks = baseMapper.selectOne(lambdaQueryWrapper);
        return Result.ok(storeStocks);
    }

    /**
     * 根据调拨单表体获取对应的仓库库存信息
     *
     * @param transferDetail
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StoreStocks>
     * <AUTHOR>
     * @date 2024/8/26 11:31
     */
    @Override
    public Result<StoreStocks> getStockByTransferDetail(StorageTransferDetail transferDetail) {
        if (isBlank(transferDetail.getCustomer())) {
            return Result.error("未知客户类型！");
        }
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, transferDetail.getStoreCode())
                .eq(StoreStocks::getCustomer, transferDetail.getCustomer())
                .eq(StoreStocks::getCopGno, transferDetail.getCopGno())
                .eq(StoreStocks::getItemNumber, transferDetail.getItemNumber())
                .eq(StoreStocks::getBatchNo, transferDetail.getBatchNo())
                .eq(StoreStocks::getSpaceName, transferDetail.getSpaceNameBefore())
                .eq(StoreStocks::getTenantId, TenantContext.getTenant()); // 调拨前储位
//        if (isNotBlank(repairOrderDetail.getBatchNo())) {
//            lambdaQueryWrapper.eq(StoreStocks::getBatchNo, repairOrderDetail.getBatchNo());
//        } else {
//            lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
//        }
        StoreStocks storeStocks = baseMapper.selectOne(lambdaQueryWrapper);
        return Result.ok(storeStocks);
    }

    /**
     * 根据出入库单表体获取对应的仓库库存信息
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    @Override
    public Result<StoreStocksFlow> getStockFlowByDetail(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer())) {
            return Result.error("未知客户类型！");
        }
        LambdaQueryWrapper<StoreStocksFlow> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocksFlow>()
                .eq(StoreStocksFlow::getStoreCode, storageDetail.getStoreCode())
                .eq(StoreStocksFlow::getCustomer, storageDetail.getCustomer())
                .eq(StoreStocksFlow::getCopGno, storageDetail.getCopGno())
                .eq(StoreStocksFlow::getStorageDetailId, storageDetail.getId())
                .eq(StoreStocksFlow::getItemNumber, storageDetail.getItemNumber()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
//                .eq(StoreStocksFlow::getBatchNo, storageDetail.getBatchNo()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
//                .eq(StoreStocksFlow::getSpaceName, storageDetail.getSpaceName()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
                .eq(StoreStocksFlow::getTenantId, (isBlank(TenantContext.getTenant()) || "0".equals(TenantContext.getTenant())) ? storageDetail.getTenantId() : Long.parseLong(TenantContext.getTenant()));
//                .eq(isNotEmpty(storageDetail.getStockTypeEnum()), StoreStocksFlow::getOptType, storageDetail.getStockTypeEnum().getV());
        if (isNotBlank(storageDetail.getBatchNo())) {
            lambdaQueryWrapper.eq(StoreStocksFlow::getBatchNo, storageDetail.getBatchNo());
        } else {
            lambdaQueryWrapper.and(i -> i.isNull(StoreStocksFlow::getBatchNo).or().eq(StoreStocksFlow::getBatchNo, ""));
        }
        if (isNotBlank(storageDetail.getSpaceName())) {
            lambdaQueryWrapper.eq(StoreStocksFlow::getSpaceName, storageDetail.getSpaceName());
        } else {
            lambdaQueryWrapper.and(i -> i.isNull(StoreStocksFlow::getSpaceName).or().eq(StoreStocksFlow::getSpaceName, ""));
        }
        if (isNotEmpty(storageDetail.getStockTypeEnum())) {
            // 针对入库记账和入库核增
            if ((StockTypeEnum.GOODS_ADD.getV() == storageDetail.getStockTypeEnum().getV())
                    || (StockTypeEnum.GOODS_INCR.getV() == storageDetail.getStockTypeEnum().getV())) {
                lambdaQueryWrapper.and(i -> i.eq(StoreStocksFlow::getOptType, StockTypeEnum.GOODS_ADD.getV())
                        .or().eq(StoreStocksFlow::getOptType, StockTypeEnum.GOODS_INCR.getV()));
                // 其他的
            } else {
                lambdaQueryWrapper.eq(StoreStocksFlow::getOptType, storageDetail.getStockTypeEnum().getV());
            }
        }
        StoreStocksFlow storeStocksFlow = storeStocksFlowMapper.selectOne(lambdaQueryWrapper);
        return Result.ok(storeStocksFlow);
    }

    /**
     * 根据出入库单表体获取对应的仓库库存信息 - 一对多
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    @Override
    public Result<List<StoreStocksFlow>> getStockFlowByDetailOneToMany(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer())) {
            return Result.error("未知客户类型！");
        }
        LambdaQueryWrapper<StoreStocksFlow> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocksFlow>()
                .eq(StoreStocksFlow::getStoreCode, storageDetail.getStoreCode())
                .eq(StoreStocksFlow::getCustomer, storageDetail.getCustomer())
                .eq(StoreStocksFlow::getCopGno, storageDetail.getCopGno())
                .eq(StoreStocksFlow::getStorageDetailId, storageDetail.getId())
                .eq(StoreStocksFlow::getItemNumber, storageDetail.getItemNumber()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
//                .eq(StoreStocksFlow::getBatchNo, storageDetail.getBatchNo()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
//                .eq(StoreStocksFlow::getSpaceName, storageDetail.getSpaceName()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
                .eq(StoreStocksFlow::getTenantId, TenantContext.getTenant())
                .eq(isNotEmpty(storageDetail.getStockTypeEnum()), StoreStocksFlow::getOptType, storageDetail.getStockTypeEnum().getV());
        List<StoreStocksFlow> storeStocksFlows = storeStocksFlowMapper.selectList(lambdaQueryWrapper);
        return Result.ok(storeStocksFlows);
    }

    /**
     * 记账/核增处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> addStockHandle(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            throw new RuntimeException("未知客户/出入库类型！");
        }
        Result<StoreStocks> result = getStockByDetail(storageDetail);
        if (!result.isSuccess()) {
            throw new RuntimeException(result.getMessage());
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("sync");
        }
        StoreStocks storeStocks = result.getResult();
        StringBuilder msg = new StringBuilder();
        if (isEmpty(storeStocks)) {
            log.info("对应无库存！商品：[{}]", getGoodsMark(storageDetail));
            // 1.增流水
            storageDetail.setStockVar(storageDetail.getActualQty());
            storageDetail.setOccupyVar(BigDecimal.ZERO);
            int addFlow = addStoreFlow(setStocksFlowByDetail(storageDetail, StockTypeEnum.GOODS_ADD));
            if (addFlow <= 0) {
                log.info("增加库存记账流水失败！商品：[{}]", getGoodsMark(storageDetail));
                throw new RuntimeException("增加库存记账流水失败！商品：[" + getGoodsMark(storageDetail) + "]");
            }
            log.info("增加库存记账流水！商品：[{}]", getGoodsMark(storageDetail));
            msg.append("库存记账成功！商品：[").append(getGoodsMark(storageDetail)).append("]");
            // 2.加库存记账
            int addStock = baseMapper.insert(setStocksByDetail(storageDetail));
            if (addStock <= 0) {
                log.info("增加库存记账失败！商品：[{}]", getGoodsMark(storageDetail));
                throw new RuntimeException("增加库存记账失败！商品：[" + getGoodsMark(storageDetail) + "]");
            }
            log.info("增加库存记账！商品：[{}]", getGoodsMark(storageDetail));
        } else {
            log.info("对应存在库存！商品：[{}]", getGoodsMark(storageDetail));
            // 1.先增流水
            storageDetail.setStockVar(storageDetail.getActualQty());
            storageDetail.setOccupyVar(BigDecimal.ZERO);
            int addFlow = addStoreFlow(setStocksFlowByDetail(storageDetail, StockTypeEnum.GOODS_INCR));
            if (addFlow <= 0) {
                log.info("增加库存核增流水失败！商品：[{}]", getGoodsMark(storageDetail));
                throw new RuntimeException("增加库存核增流水失败！商品：[" + getGoodsMark(storageDetail) + "]");
            }
            log.info("增加库存核增流水！商品：[{}]", getGoodsMark(storageDetail));
            // 2.增库存
            storageDetail.setChangeQty(storageDetail.getActualQty());
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            int update = baseMapper.updateQtySafe(storageDetail);
            if (update <= 0) {
                log.info("核增库存失败！商品：[{}]；变动数量：{}；原库存数量：{}", getGoodsMark(storageDetail), storageDetail.getChangeQty(), storeStocks.getBeginQty());
                throw new RuntimeException("核增库存失败！商品：[" + getGoodsMark(storageDetail) + "]；变动数量：" + storageDetail.getChangeQty() + "；原库存数量：" + storeStocks.getBeginQty());
            }
            log.info("核增库存！商品：[{}]；变动数量：{}；原库存数量：{}", getGoodsMark(storageDetail), storageDetail.getChangeQty(), storeStocks.getBeginQty());
            msg.append("核增库存成功！商品：[").append(getGoodsMark(storageDetail)).append("]");
        }
        return Result.ok(msg.toString());
    }

    /**
     * 增加占用处理器
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 15:05
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> addOccupyHandle(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            return Result.error("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("sync");
        }
        // 1.增流水
        storageDetail.setStockVar(BigDecimal.ZERO);
        storageDetail.setOccupyVar(storageDetail.getActualQty());
        StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(storageDetail, StockTypeEnum.GOODS_OCC);
        int addFlow = addStoreFlow(storeStocksFlow);
        if (addFlow <= 0) {
            log.info("增加占用，增加流水记录时失败！商品：[" + getGoodsMark(storageDetail) + "]；需增加占用数量：" + storageDetail.getOccupyVar());
            throw new RuntimeException("增加占用，增加流水记录时失败！" + getGoodsDesc(storageDetail) + "；需增加占用数量：" + storageDetail.getOccupyVar());
        }
        // 2.增加占用
        storageDetail.setChangeQty(storageDetail.getActualQty());
        storageDetail.setUpdateBy(sysUser.getUsername());
        storageDetail.setUpdateDate(new Date());
        int add = baseMapper.addOccQtySafe(storageDetail);
        if (add <= 0) {
            log.info("增加占用失败，可占用数量不足！商品：[" + getGoodsMark(storageDetail) + "]；变动数量：" + storageDetail.getChangeQty());
            throw new RuntimeException("增加占用失败，可占用数量不足！" + getGoodsDesc(storageDetail) + "；变动数量：" + storageDetail.getChangeQty());
        }
        return Result.ok(storeStocksFlow.getId());
    }

    /**
     * 增加占用处理器 - 一对多
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/21 15:05
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> addOccupyHandleOneToMany(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            return Result.error("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 物料号、项号查库存，库存>0，创建时间正序，先进先出
        LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getStoreCode, storageDetail.getStoreCode())
                .eq(StoreStocks::getCustomer, storageDetail.getCustomer())
                .eq(StoreStocks::getCopGno, storageDetail.getCopGno())
                .eq(StoreStocks::getItemNumber, storageDetail.getItemNumber())
                .gt(StoreStocks::getBeginQty, BigDecimal.ZERO)
                .eq(isNotBlank(storageDetail.getBatchNo()), StoreStocks::getBatchNo, storageDetail.getBatchNo())
                .eq(isNotBlank(storageDetail.getSpaceName()), StoreStocks::getSpaceName, storageDetail.getSpaceName())
                .eq(StoreStocks::getTenantId, (isBlank(TenantContext.getTenant()) || "0".equals(TenantContext.getTenant())) ? storageDetail.getTenantId() : Long.parseLong(TenantContext.getTenant()))
                .orderByAsc(StoreStocks::getCreateDate);
        List<StoreStocks> storeStockss = baseMapper.selectList(lambdaQueryWrapper);
        if (isEmpty(storeStockss)) {
            throw new RuntimeException("未获取到商品[" + getGoodsDesc(storageDetail) + "]的库存信息！");
        }
        // 获取符合条件的库存数据
        List<StoreStocks> storeStocksOkList = new LinkedList<>();
        BigDecimal accumulatedQty = BigDecimal.ZERO; // 累加数量
        BigDecimal outboundQty = storageDetail.getActualQty(); // 总出库数量
        BigDecimal lastStockQtyNeeded = BigDecimal.ZERO; // 最后一个库存需要出库的数量
        for (StoreStocks stock : storeStockss) {
            storeStocksOkList.add(stock);
            BigDecimal remainingQty = outboundQty.subtract(accumulatedQty); // 剩余需要的数量
            if (stock.getBeginQty().compareTo(remainingQty) >= 0) {
                lastStockQtyNeeded = remainingQty; // 当前库存满足剩余需要的数量
                accumulatedQty = accumulatedQty.add(remainingQty);   // 累加总量
                stock.setBeginQty(remainingQty); // 更新最后一个库存的出库数量，便于后续操作！！！
                break; // 满足出库数量，退出循环
            } else {
                accumulatedQty = accumulatedQty.add(stock.getBeginQty()); // 不足时，取完当前库存
            }
        }
        // 检查库存是否足够
        if (accumulatedQty.compareTo(outboundQty) < 0) {
            throw new RuntimeException("库存不足，无法满足占用需求！");
        }
        // 输出最后库存详情
        if (!storeStocksOkList.isEmpty()) {
            StoreStocks lastStock = storeStocksOkList.get(storeStocksOkList.size() - 1);
            log.info("最后一个库存详情：[{}], 实际出库数量：[{}]", lastStock, lastStockQtyNeeded);
        }
        log.info("获取到的库存集合个数：{}", storeStocksOkList.size());
        log.info("获取到的库存集合：{}", storeStocksOkList);
        log.info("最后一个库存需要的出库数量：{}", lastStockQtyNeeded);

        if (isEmpty(storeStocksOkList)) {
            throw new RuntimeException("增加占用处理，无符合条件的库存数据！");
        }
        List<String> ids = new ArrayList<>();
        for (StoreStocks storeStocks : storeStocksOkList) {
            StorageDetail detail = new StorageDetail();
            BeanUtil.copyProperties(storageDetail, detail, CopyOptions.create().ignoreNullValue());
            detail.setActualQty(storeStocks.getBeginQty());
            detail.setBatchNo(storeStocks.getBatchNo());
            detail.setSpaceName(storeStocks.getSpaceName());
            detail.setSpaceCode(storeStocks.getSpaceCode());
            detail.setAreaCode(isNotBlank(detail.getAreaCode()) ? detail.getAreaCode() : storeStocks.getAreaCode());
            detail.setAreaName(isNotBlank(detail.getAreaName()) ? detail.getAreaName() : storeStocks.getAreaName());
            // 1.增流水
            detail.setStockVar(BigDecimal.ZERO);
            detail.setOccupyVar(detail.getActualQty());
            StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(detail, StockTypeEnum.GOODS_OCC);
            int addFlow = addStoreFlow(storeStocksFlow);
            if (addFlow <= 0) {
                log.info("增加占用，增加流水记录时失败！商品：[" + getGoodsMark(detail) + "]；需增加占用数量：" + detail.getOccupyVar());
                throw new RuntimeException("增加占用，增加流水记录时失败！" + getGoodsDesc(detail) + "；需增加占用数量：" + detail.getOccupyVar());
            }
            // 2.增加占用
            detail.setChangeQty(detail.getActualQty());
            detail.setUpdateBy(sysUser.getUsername());
            detail.setUpdateDate(new Date());
            detail.setStockId(storeStocks.getId()); // 占用时直接根据库存ID去占用！
            int add = baseMapper.addOccQtySafeOneToMany(detail);
            if (add <= 0) {
                log.info("增加占用失败，可占用数量不足！商品：[" + getGoodsMark(detail) + "]；变动数量：" + detail.getChangeQty());
                throw new RuntimeException("增加占用失败，可占用数量不足！" + getGoodsDesc(detail) + "；变动数量：" + detail.getChangeQty());
            }
            ids.add(storeStocksFlow.getId());
        }
        return Result.ok(CollUtil.join(ids, ","));
    }

    /**
     * 解除占用处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deOccupyHandle(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            return Result.error("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("sync");
        }
        // 找出占用的流水记录
        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_OCC); // 类型为占用
        Result<StoreStocksFlow> storeStocksFlowResult = getStockFlowByDetail(storageDetail);
        StoreStocksFlow occFlow = isNotEmpty(storeStocksFlowResult.getResult()) ? storeStocksFlowResult.getResult() : null;
        if (isNotEmpty(occFlow)) {
            storageDetail.setChangeQty(occFlow.getOccupyVar());
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            int update = baseMapper.updateOccQtySafe(storageDetail);
            if (update <= 0) {
                log.info("解除占用失败，数量不足！商品：[" + getGoodsMark(storageDetail) + "]；冲正数量：" + storageDetail.getChangeQty());
                throw new RuntimeException("解除占用失败，数量不足！" + getGoodsDesc(storageDetail) + "；冲正数量：" + storageDetail.getChangeQty());
            }
            // 2.流水
            // 2024/1/30 17:10@ZHANGCHAO 追加/变更/完善：解除占用不用加流水了，要把原来占用的流水删掉！！
            storeStocksFlowMapper.deleteById(occFlow.getId());
        } else {
            log.info("无占用记录，无需解除占用！商品：[" + storageDetail.getId() + "-" + getGoodsMark(storageDetail) + "]");
        }
        return Result.ok("解除占用成功！" + getGoodsMark(storageDetail));
    }

    /**
     * 解除占用处理器 - 一对多
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deOccupyHandleOneToMany(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            return Result.error("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 找出占用的流水记录
        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_OCC); // 类型为占用
        Result<List<StoreStocksFlow>> storeStocksFlowResult = getStockFlowByDetailOneToMany(storageDetail);
        List<StoreStocksFlow> occFlows = isNotEmpty(storeStocksFlowResult.getResult()) ? storeStocksFlowResult.getResult() : null;
        if (isNotEmpty(occFlows)) {
            for (StoreStocksFlow occFlow : occFlows) {
                StorageDetail detail = new StorageDetail();
                BeanUtil.copyProperties(storageDetail, detail, CopyOptions.create().ignoreNullValue());
                detail.setBatchNo(occFlow.getBatchNo());
                detail.setSpaceName(occFlow.getSpaceName());
//                detail.setSpaceCode(occFlow.getSpaceCode());
//                detail.setAreaCode(occFlow.getAreaCode());
//                detail.setAreaName(occFlow.getAreaName());
                detail.setChangeQty(occFlow.getOccupyVar());
                detail.setUpdateBy(sysUser.getUsername());
                detail.setUpdateDate(new Date());
                int update = baseMapper.updateOccQtySafe(detail);
                if (update <= 0) {
                    log.info("解除占用失败，数量不足！商品：[" + getGoodsMark(detail) + "]；冲正数量：" + detail.getChangeQty());
                    throw new RuntimeException("解除占用失败，数量不足！" + getGoodsDesc(detail) + "；冲正数量：" + detail.getChangeQty());
                }
                // 2.流水
                // 2024/1/30 17:10@ZHANGCHAO 追加/变更/完善：解除占用不用加流水了，要把原来占用的流水删掉！！
                storeStocksFlowMapper.deleteById(occFlow.getId());
            }
        } else {
            log.info("无占用记录，无需解除占用！商品：[" + storageDetail.getId() + "-" + getGoodsMark(storageDetail) + "]");
        }
        return Result.ok("解除占用成功！" + getGoodsMark(storageDetail));
    }

    /**
     * 核减库存/占用处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> reduceStockHandle(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            throw new RuntimeException("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("sync");
        }
        StringBuilder msg = new StringBuilder();

        // 1.先看有没有占用的流水记录
        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_OCC); // 类型为占用
        Result<StoreStocksFlow> storeStocksFlowResult = getStockFlowByDetail(storageDetail);
        StoreStocksFlow occFlow = isNotEmpty(storeStocksFlowResult.getResult()) ? storeStocksFlowResult.getResult() : null;
        // 有占用
        if (isNotEmpty(occFlow)) {
            // 2.增流水
            storageDetail.setStockVar(storageDetail.getActualQty().negate());
            storageDetail.setOccupyVar(occFlow.getOccupyVar().negate());
            StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(storageDetail, StockTypeEnum.GOODS_REDUCE);
            int addFlow = addStoreFlow(storeStocksFlow);
            if (addFlow <= 0) {
                log.info("核减库存和占用新增流水失败！商品：[" + getGoodsMark(storageDetail) + "]");
                throw new RuntimeException("核减库存和占用新增流水失败！" + getGoodsDesc(storageDetail));
            }
            msg.append("核减库存和占用成功！商品：[" + getGoodsMark(storageDetail) + "]");
            // 3.核减库存，核减占用
            storageDetail.setChangeQty(storageDetail.getActualQty());
            storageDetail.setOccupyQty(occFlow.getOccupyVar());
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            int update = baseMapper.updateQtySafe(storageDetail);
            if (update <= 0) {
                log.info("核减库存和占用失败，数量不足！商品：[" + getGoodsMark(storageDetail) + "]；核减库存数量：" + storageDetail.getChangeQty() + "；核减占用数量：" + storageDetail.getOccupyQty());
                throw new RuntimeException("核减库存和占用失败，数量不足！" + getGoodsDesc(storageDetail) + "；核减库存数量：" + storageDetail.getChangeQty() + "；核减占用数量：" + storageDetail.getOccupyQty());
            }
            // 无占用
        } else {
            // 2.增流水
            storageDetail.setStockVar(storageDetail.getActualQty().negate());
            storageDetail.setOccupyVar(BigDecimal.ZERO);
            StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(storageDetail, StockTypeEnum.GOODS_REDUCE);
            int addFlow = addStoreFlow(storeStocksFlow);
            if (addFlow <= 0) {
                log.info("核减库存新增流水失败！商品：[" + getGoodsMark(storageDetail) + "]");
                throw new RuntimeException("核减库存新增流水失败！" + getGoodsDesc(storageDetail));
            }
            msg.append("核减库存成功！商品：[" + getGoodsMark(storageDetail) + "]");
            // 3.核减库存
            storageDetail.setChangeQty(storageDetail.getActualQty());
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            int update = baseMapper.updateStockQtySafe(storageDetail);
            if (update <= 0) {
                log.info("核减库存失败，数量不足！商品：[" + getGoodsMark(storageDetail) + "]；核减库存数量：" + storageDetail.getChangeQty());
                throw new RuntimeException("核减库存失败，数量不足！" + getGoodsDesc(storageDetail) + "；核减库存数量：" + storageDetail.getChangeQty());
            }
        }
        return Result.ok(msg.toString());
    }

     /**
     * 核减库存/占用处理器 - 一对多
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> reduceStockHandleOneToMany(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            throw new RuntimeException("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        StringBuilder msg = new StringBuilder();

        // 1.先看有没有占用的流水记录
        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_OCC); // 类型为占用
        Result<List<StoreStocksFlow>> storeStocksFlowResult = getStockFlowByDetailOneToMany(storageDetail);
        List<StoreStocksFlow> occFlows = isNotEmpty(storeStocksFlowResult.getResult()) ? storeStocksFlowResult.getResult() : null;
        // 有占用
        if (isNotEmpty(occFlows)) {
            for (StoreStocksFlow occFlow : occFlows) {
                StorageDetail detail = new StorageDetail();
                BeanUtil.copyProperties(storageDetail, detail, CopyOptions.create().ignoreNullValue());
                detail.setActualQty(occFlow.getOccupyVar());
                detail.setBatchNo(occFlow.getBatchNo());
                detail.setSpaceName(occFlow.getSpaceName());
                detail.setSpaceCode(occFlow.getSpaceCode());
                detail.setAreaCode(isNotBlank(detail.getAreaCode()) ? detail.getAreaCode() : occFlow.getAreaCode());
                detail.setAreaName(isNotBlank(detail.getAreaName()) ? detail.getAreaName() : occFlow.getAreaName());
                // 2.增流水
                detail.setStockVar(detail.getActualQty().negate());
                detail.setOccupyVar(occFlow.getOccupyVar().negate());
                StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(detail, StockTypeEnum.GOODS_REDUCE);
                int addFlow = addStoreFlow(storeStocksFlow);
                if (addFlow <= 0) {
                    log.info("核减库存和占用新增流水失败！商品：[" + getGoodsMark(detail) + "]");
                    throw new RuntimeException("核减库存和占用新增流水失败！" + getGoodsDesc(detail));
                }
                msg.append("核减库存和占用成功！商品：[" + getGoodsMark(detail) + "]");
                // 3.核减库存，核减占用
                detail.setChangeQty(detail.getActualQty());
                detail.setOccupyQty(occFlow.getOccupyVar());
                detail.setUpdateBy(sysUser.getUsername());
                detail.setUpdateDate(new Date());
                int update = baseMapper.updateQtySafe(detail);
                if (update <= 0) {
                    log.info("核减库存和占用失败，数量不足！商品：[" + getGoodsMark(detail) + "]；核减库存数量：" + detail.getChangeQty() + "；核减占用数量：" + detail.getOccupyQty());
                    throw new RuntimeException("核减库存和占用失败，数量不足！" + getGoodsDesc(detail) + "；核减库存数量：" + detail.getChangeQty() + "；核减占用数量：" + detail.getOccupyQty());
                }
            }
            // 无占用
        } else {
            //////////////////////一对多查库存START/////////////////////////
            // 物料号、项号查库存，库存>0，创建时间正序，先进先出
            LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
                    .eq(StoreStocks::getStoreCode, storageDetail.getStoreCode())
                    .eq(StoreStocks::getCustomer, storageDetail.getCustomer())
                    .eq(StoreStocks::getCopGno, storageDetail.getCopGno())
                    .eq(StoreStocks::getItemNumber, storageDetail.getItemNumber())
                    .gt(StoreStocks::getBeginQty, BigDecimal.ZERO)
                    .eq(isNotBlank(storageDetail.getBatchNo()), StoreStocks::getBatchNo, storageDetail.getBatchNo())
                    .eq(isNotBlank(storageDetail.getSpaceName()), StoreStocks::getSpaceName, storageDetail.getSpaceName())
                    .eq(StoreStocks::getTenantId, TenantContext.getTenant())
                    .orderByAsc(StoreStocks::getCreateDate);
            List<StoreStocks> storeStockss = baseMapper.selectList(lambdaQueryWrapper);
            if (isEmpty(storeStockss)) {
                throw new RuntimeException("未获取到商品[" + getGoodsDesc(storageDetail) + "]的库存信息！");
            }
            // 获取符合条件的库存数据
            List<StoreStocks> storeStocksOkList = new LinkedList<>();
            BigDecimal accumulatedQty = BigDecimal.ZERO; // 累加数量
            BigDecimal outboundQty = storageDetail.getActualQty(); // 总出库数量
            BigDecimal lastStockQtyNeeded = BigDecimal.ZERO; // 最后一个库存需要出库的数量
            for (StoreStocks stock : storeStockss) {
                storeStocksOkList.add(stock);
                BigDecimal remainingQty = outboundQty.subtract(accumulatedQty); // 剩余需要的数量
                if (stock.getBeginQty().compareTo(remainingQty) >= 0) {
                    lastStockQtyNeeded = remainingQty; // 当前库存满足剩余需要的数量
                    accumulatedQty = accumulatedQty.add(remainingQty);   // 累加总量
                    stock.setBeginQty(remainingQty); // 更新最后一个库存的出库数量，便于后续操作！！！
                    break; // 满足出库数量，退出循环
                } else {
                    accumulatedQty = accumulatedQty.add(stock.getBeginQty()); // 不足时，取完当前库存
                }
            }
            // 检查库存是否足够
            if (accumulatedQty.compareTo(outboundQty) < 0) {
                throw new RuntimeException("库存不足，无法满足核减库存需求！");
            }
            // 输出最后库存详情
            if (!storeStocksOkList.isEmpty()) {
                StoreStocks lastStock = storeStocksOkList.get(storeStocksOkList.size() - 1);
                log.info("最后一个库存详情：[{}], 实际出库数量：[{}]", lastStock, lastStockQtyNeeded);
            }
            log.info("获取到的库存集合个数：{}", storeStocksOkList.size());
            log.info("获取到的库存集合：{}", storeStocksOkList);
            log.info("最后一个库存需要的出库数量：{}", lastStockQtyNeeded);
            if (isEmpty(storeStocksOkList)) {
                throw new RuntimeException("增加占用处理，无符合条件的库存数据！");
            }
            ////////////////////////一对多查库存END///////////////////////
            for (StoreStocks storeStocks : storeStocksOkList) {
                StorageDetail detail = new StorageDetail();
                BeanUtil.copyProperties(storageDetail, detail, CopyOptions.create().ignoreNullValue());
                detail.setActualQty(storeStocks.getBeginQty());
                detail.setBatchNo(storeStocks.getBatchNo());
                detail.setSpaceName(storeStocks.getSpaceName());
                detail.setSpaceCode(storeStocks.getSpaceCode());
                detail.setAreaCode(isNotBlank(detail.getAreaCode()) ? detail.getAreaCode() : storeStocks.getAreaCode());
                detail.setAreaName(isNotBlank(detail.getAreaName()) ? detail.getAreaName() : storeStocks.getAreaName());
                // 2.增流水
                detail.setStockVar(detail.getActualQty().negate());
                detail.setOccupyVar(BigDecimal.ZERO);
                StoreStocksFlow storeStocksFlow = setStocksFlowByDetail(detail, StockTypeEnum.GOODS_REDUCE);
                int addFlow = addStoreFlow(storeStocksFlow);
                if (addFlow <= 0) {
                    log.info("核减库存新增流水失败！商品：[" + getGoodsMark(detail) + "]");
                    throw new RuntimeException("核减库存新增流水失败！" + getGoodsDesc(detail));
                }
                msg.append("核减库存成功！商品：[").append(getGoodsMark(detail)).append("]");
                // 3.核减库存
                detail.setChangeQty(detail.getActualQty());
                detail.setUpdateBy(sysUser.getUsername());
                detail.setUpdateDate(new Date());
                int update = baseMapper.updateStockQtySafe(detail);
                if (update <= 0) {
                    log.info("核减库存失败，数量不足！商品：[" + getGoodsMark(detail) + "]；核减库存数量：" + detail.getChangeQty());
                    throw new RuntimeException("核减库存失败，数量不足！" + getGoodsDesc(detail) + "；核减库存数量：" + detail.getChangeQty());
                }
            }
        }
        return Result.ok(msg.toString());
    }

    /**
     * 冲正处理器
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/10 10:46
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> rectify(StorageDetail storageDetail) {
        if (isBlank(storageDetail.getCustomer()) || isBlank(storageDetail.getIeFlag())) {
            return Result.error("未知客户/出入库类型！");
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("sync");
        }
        // 获取库存
        Result<StoreStocks> result = getStockByDetail(storageDetail);
        if (!result.isSuccess()) {
            throw new RuntimeException(result.getMessage());
        }
        StoreStocks storeStocks = result.getResult();
        if (isEmpty(storeStocks)) {
            throw new RuntimeException("库存不存在！:" + getGoodsMark(storageDetail));
        }
        StringBuilder msg = new StringBuilder();
        // 找出需要冲正的类型的流水
        Result<StoreStocksFlow> storeStocksFlowResult = getStockFlowByDetail(storageDetail);
        StoreStocksFlow originalFlow = isNotEmpty(storeStocksFlowResult.getResult()) ? storeStocksFlowResult.getResult() : null;
        if (isEmpty(originalFlow)) {
            log.info("未找到需要冲正的流水！商品：[{}]", getGoodsMark(storageDetail));
            msg.append("未找到需要冲正的流水！商品：[").append(getGoodsMark(storageDetail)).append("]");
            throw new RuntimeException(msg.toString());
        }
//        else {
//            StoreStocksFlow rectifyFlow = storeStocksFlowMapper.selectOne(new LambdaQueryWrapper<StoreStocksFlow>()
//                    .eq(StoreStocksFlow::getStorageDetailId, originalFlow.getId())
//                    .eq(StoreStocksFlow::getOptType, StockTypeEnum.RECTIFY.getV()));
//            if (isNotEmpty(rectifyFlow)) {
//                log.info("已存在冲正流水，无需处理！商品：[{}]", getGoodsMark(storageDetail));
//                msg.append("已存在冲正流水，无需处理！商品：[").append(getGoodsMark(storageDetail)).append("]");
//                throw new RuntimeException(msg.toString());
//            }
//        }
        // 入库记账和入库核增 - 冲正
        if ((StockTypeEnum.GOODS_ADD.getV() == originalFlow.getOptType()) || (StockTypeEnum.GOODS_INCR.getV() == originalFlow.getOptType())) {
            // 1.加冲正流水
//            storageDetail.setStockVar(originalFlow.getStockVar().negate());
//            storageDetail.setOccupyVar(BigDecimal.ZERO);
//            int addFlow = addStoreFlow(setStocksFlowByDetailRectify(storageDetail, originalFlow, StockTypeEnum.RECTIFY));
//            if (addFlow <= 0) {
//                log.info("冲正新增流水失败！商品：[{}]", getGoodsMark(storageDetail));
//                throw new RuntimeException("冲正新增流水失败！" + getGoodsDesc(storageDetail));
//            }
//            msg.append("冲正新增流水成功！商品：[").append(getGoodsMark(storageDetail)).append("]");
            // 2.减库存
            int currentVersion = isNotEmpty(storeStocks.getVersion()) ? storeStocks.getVersion() : 0;
            storageDetail.setChangeQty(originalFlow.getStockVar().negate()); // 冲正取负值
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            storageDetail.setVersion(currentVersion);
            int update = baseMapper.updateQtySafeRectify(storageDetail);
            if (update <= 0) {
                log.info("冲正[核增]库存失败！商品：[{}]；变动数量：{}；库存数量：{}", getGoodsMark(storageDetail), storageDetail.getChangeQty(), storeStocks.getBeginQty());
                if ((storeStocks.getBeginQty().add(storageDetail.getChangeQty())).compareTo(BigDecimal.ZERO) < 0) {
                    throw new RuntimeException("库存不足，请先回退出库单！商品：[" + getGoodsMark(storageDetail) + "]；变动数量：" + storageDetail.getChangeQty() + "；库存数量：" + storeStocks.getBeginQty());
                }
                throw new RuntimeException("冲正[核增]库存失败！商品：[" + getGoodsMark(storageDetail) + "]；变动数量：" + storageDetail.getChangeQty() + "；库存数量：" + storeStocks.getBeginQty());
            }
            log.info("冲正[核增]库存！商品：[{}]；变动数量：{}；库存数量：{}", getGoodsMark(storageDetail), storageDetail.getChangeQty(), storeStocks.getBeginQty());
            msg.append("冲正[核增]库存成功！商品：[").append(getGoodsMark(storageDetail)).append("]");
            // 出库核减库存 - 冲正
        } else if (StockTypeEnum.GOODS_REDUCE.getV() == originalFlow.getOptType()) {
            // 2.+库存，+占用
            int currentVersion = isNotEmpty(storeStocks.getVersion()) ? storeStocks.getVersion() : 0;
            storageDetail.setVersion(currentVersion);
            storageDetail.setChangeQty(originalFlow.getStockVar());
            storageDetail.setOccupyQty(originalFlow.getOccupyVar());
            storageDetail.setUpdateBy(sysUser.getUsername());
            storageDetail.setUpdateDate(new Date());
            int update = baseMapper.updateQtySafeRectify(storageDetail);
            if (update <= 0) {
                log.info("冲正[核减]库存失败！商品：[{}]；库存变动数量：{}；占用变动数量：{}", getGoodsMark(storageDetail), storageDetail.getChangeQty().negate(), storageDetail.getOccupyQty().negate());
                throw new RuntimeException("冲正[核减]库存失败！商品：[" + getGoodsMark(storageDetail) + "]；库存变动数量：" + storageDetail.getChangeQty().negate() + "；占用变动数量：" + storageDetail.getOccupyQty().negate());
            }
        }

        storeStocksFlowMapper.deleteById(originalFlow.getId()); // 删除原流水
        return Result.ok("冲正成功！" + getGoodsMark(storageDetail));
    }


    /**
     * 导出库存Excel
     *
     * @param request
     * @param response
     * @param storeStocksDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/9 15:59
     */
    @Override
    public void exportStocks(HttpServletRequest request, HttpServletResponse response, StoreStocksDTO storeStocksDTO) {
        List<StoreStocks> storeStocksList;
        Result<?> result = this.queryPageList(storeStocksDTO, 1, 1000000);
        if (result.isSuccess()) {
            storeStocksList = ((IPage<StoreStocks>) result.getResult()).getRecords();
        } else {
            throw new RuntimeException(result.getMessage());
        }
        List<DictModel> dictModels = sysBaseApi.getDictItems("STORE_DETAIL_TYPE");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getValue(), dictModel.getText());
            });
        }
        if (isNotEmpty(storeStocksList)) {
            for (StoreStocks storeStocks : storeStocksList) {
                // 仓库
                if (isNotBlank(storeStocks.getStoreCode())) {
                    StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                            .eq(StoreInfo::getStoreCode, storeStocks.getStoreCode()));
                    storeStocks.setStoreCode(isNotEmpty(storeInfo) ? storeInfo.getStoreName() : "");
                }
                // 货主
                storeStocks.setCustomer(commonService.getCustomerNameById(storeStocks.getCustomer()));
                // 付款方式
                storeStocks.setDetailType(isNotEmpty(dictMap) ? dictMap.get(storeStocks.getDetailType()) : "");
                storeStocks.setQunit(isNotEmpty(dictMap1) ? dictMap1.get(storeStocks.getQunit()) : "");
            }
        }
        ExportParams params = new ExportParams();
        params.setType(ExcelType.XSSF);
        params.setStyle(ExcelExportStylerBorderImpl.class);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> mapInv = new HashMap<>();
        mapInv.put("title", params);
        mapInv.put("entity", StoreStocks.class);
        mapInv.put("data", storeStocksList);
        list.add(mapInv);
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导出出入库明细Excel
     *
     * @param request
     * @param response
     * @param inOrOutStorageDetailDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/13 14:51
     */
    @Override
    public void exportStoreDetail(HttpServletRequest request, HttpServletResponse response, InOrOutStorageDetailDTO inOrOutStorageDetailDTO) {
        List<InOrOutStorageDetailDTO> inOrOutStorageDetailDTOS;
        inOrOutStorageDetailDTO.setTenantId(TenantContext.getTenant());
        IPage<InOrOutStorageDetailDTO> pageList = storageInfoMapper.bondedWarehouseInDetailList(new Page<>(1, 9999999), inOrOutStorageDetailDTO);
        if (isNotEmpty(pageList.getRecords())) {
            inOrOutStorageDetailDTOS = pageList.getRecords();
        } else {
            throw new RuntimeException("导出失败，未获取到数据！");
        }
        List<DictModel> dictModels = sysBaseApi.getDictItems("STORE_DETAIL_TYPE");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        Map<String, String> dictMap2 = new HashMap<>();
        if (isNotEmpty(dictModels2)) {
            dictModels2.forEach(dictModel -> {
                dictMap2.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<DictModel> dictModels3 = sysBaseApi.getDictItems("JGFS");
        Map<String, String> dictMap3 = new HashMap<>();
        if (isNotEmpty(dictModels3)) {
            dictModels3.forEach(dictModel -> {
                dictMap3.put(dictModel.getValue(), dictModel.getText());
            });
        }


        List<InStorageDetailEntity> inStorageDetailEntityList = new ArrayList<>();
        List<OutStorageDetailEntity> outStorageDetailEntityList =  new ArrayList<>();
        Map<String, String> customerMap = new HashMap<>();
        for (InOrOutStorageDetailDTO ioosd : inOrOutStorageDetailDTOS) {
            // 货主
            if (isNotBlank(ioosd.getCustomer())) {
                if (customerMap.containsKey(ioosd.getCustomer())) {
                    ioosd.setCustomer(customerMap.get(ioosd.getCustomer()));
                } else {
                    String customerName = commonService.getCustomerNameById_(ioosd.getCustomer());
                    customerMap.put(ioosd.getCustomer(), customerName);
                    ioosd.setCustomer(customerName);
                }
            }
//            ioosd.setCustomer(commonService.getCustomerNameById(ioosd.getCustomer()));
            ioosd.setDetailType(isNotEmpty(dictMap) ? dictMap.get(ioosd.getDetailType()) : "");
            ioosd.setUnit(isNotEmpty(dictMap1) ? dictMap1.get(ioosd.getUnit()) : "");
            ioosd.setCountryCode(isNotEmpty(dictMap2) ? dictMap2.get(ioosd.getCountryCode()) : "");
            ioosd.setSupvModecd(isNotEmpty(dictMap3) ? dictMap3.get(ioosd.getSupvModecd()) : "");
            if ("I".equals(inOrOutStorageDetailDTO.getIeFlag())) {
                InStorageDetailEntity inStorageDetailEntity = new InStorageDetailEntity();
                BeanUtil.copyProperties(ioosd, inStorageDetailEntity, CopyOptions.create().ignoreNullValue());
                inStorageDetailEntity.setPn(ioosd.getGName());
                inStorageDetailEntity.setModel(ioosd.getGModel());
                inStorageDetailEntity.setIeFlag("入库");
                inStorageDetailEntityList.add(inStorageDetailEntity);
            } else if ("E".equals(inOrOutStorageDetailDTO.getIeFlag())) {
                OutStorageDetailEntity outStorageDetail = new OutStorageDetailEntity();
                BeanUtil.copyProperties(ioosd, outStorageDetail, CopyOptions.create().ignoreNullValue());
                outStorageDetail.setPn(ioosd.getGName());
                outStorageDetail.setModel(ioosd.getGModel());
                outStorageDetail.setIeFlag("出库");
                outStorageDetailEntityList.add(outStorageDetail);
            }
        }

        ExportParams params = new ExportParams();
        params.setType(ExcelType.XSSF);
        params.setSheetName("I".equals(inOrOutStorageDetailDTO.getIeFlag()) ? "入库明细" : "出库明细");
        params.setStyle(ExcelExportStylerBorderImpl.class);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> mapInv = new HashMap<>();
        mapInv.put("title", params);
        mapInv.put("entity", "I".equals(inOrOutStorageDetailDTO.getIeFlag()) ? InStorageDetailEntity.class : OutStorageDetailEntity.class);
        mapInv.put("data", "I".equals(inOrOutStorageDetailDTO.getIeFlag()) ? inStorageDetailEntityList : outStorageDetailEntityList);
        list.add(mapInv);
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 增库存流水
     *
     * @param storeStocksFlow
     * @return int
     * <AUTHOR>
     * @date 2024/1/30 16:57
     */
    public int addStoreFlow(StoreStocksFlow storeStocksFlow) {
        // 先删旧流水
        LambdaQueryWrapper<StoreStocksFlow> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocksFlow>()
                .eq(StoreStocksFlow::getStoreCode, storeStocksFlow.getStoreCode())
                .eq(StoreStocksFlow::getCustomer, storeStocksFlow.getCustomer())
                .eq(StoreStocksFlow::getCopGno, storeStocksFlow.getCopGno())
                .eq(StoreStocksFlow::getItemNumber, storeStocksFlow.getItemNumber()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
                .eq(StoreStocksFlow::getBatchNo, storeStocksFlow.getBatchNo())
                .eq(StoreStocksFlow::getSpaceName, storeStocksFlow.getSpaceName()) // 20240823 追加/变更/完善：库存粒度:仓库、贷主、料号、项号、批次、储位
                .eq(StoreStocksFlow::getOptType, storeStocksFlow.getOptType())
                .eq(StoreStocksFlow::getStorageDetailId, storeStocksFlow.getStorageDetailId());
        StoreStocksFlow oldFlow = storeStocksFlowMapper.selectOne(lambdaQueryWrapper);
        if (isNotEmpty(oldFlow)) {
            // 获取当前登录用户
            LoginUser sysUser;
            try {
                sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            } catch (Exception e) {
                sysUser = new LoginUser();
                sysUser.setUsername("sync");
            }
            // 放重放
            if (oldFlow.getOptType() == StockTypeEnum.GOODS_ADD.getV()) { // 记账
                // 记账的不需要手动删除流水，直接通过唯一索引处理！
            } else if (oldFlow.getOptType() == StockTypeEnum.GOODS_INCR.getV()) { // 核增
                StorageDetail storageDetail = new StorageDetail();
                BeanUtil.copyProperties(oldFlow, storageDetail, CopyOptions.create().ignoreNullValue());
                storageDetail.setChangeQty(oldFlow.getStockVar()); // 减库存
                storageDetail.setUpdateBy(sysUser.getUsername());
                storageDetail.setUpdateDate(new Date());
                int update = baseMapper.updateStockQtySafe(storageDetail);
                if (update <= 0) {
                    log.info("核增回滚失败：[{}]", getGoodsMark(storageDetail));
                    throw new RuntimeException("核增回滚失败：[" + getGoodsMark(storageDetail) + "]");
                }
                storeStocksFlowMapper.deleteById(oldFlow.getId());
            } else if (oldFlow.getOptType() == StockTypeEnum.GOODS_REDUCE.getV()) { // 核减
                StorageDetail storageDetail = new StorageDetail();
                BeanUtil.copyProperties(oldFlow, storageDetail, CopyOptions.create().ignoreNullValue());
                storageDetail.setChangeQty(oldFlow.getStockVar().negate());
                storageDetail.setOccupyQty(oldFlow.getOccupyVar().negate());
                storageDetail.setUpdateBy(sysUser.getUsername());
                storageDetail.setUpdateDate(new Date());
                int update = baseMapper.addOccAndStockQtySafe(storageDetail);
                if (update <= 0) {
                    log.info("核减回滚失败：[{}]", getGoodsMark(storageDetail));
                    throw new RuntimeException("核减回滚失败：[" + getGoodsMark(storageDetail) + "]");
                }
                storeStocksFlowMapper.deleteById(oldFlow.getId());
            } else if (oldFlow.getOptType() == StockTypeEnum.GOODS_OCC.getV()) { // 占用
                StorageDetail storageDetail = new StorageDetail();
                BeanUtil.copyProperties(oldFlow, storageDetail, CopyOptions.create().ignoreNullValue());
                storageDetail.setChangeQty(oldFlow.getOccupyVar()); // 减占用
                storageDetail.setUpdateBy(sysUser.getUsername());
                storageDetail.setUpdateDate(new Date());
                int update = baseMapper.updateOccQtySafe(storageDetail);
                if (update <= 0) {
                    log.info("占用回滚失败：[{}]", getGoodsMark(storageDetail));
                    throw new RuntimeException("占用回滚失败：[" + getGoodsMark(storageDetail) + "]");
                }
                storeStocksFlowMapper.deleteById(oldFlow.getId());
            } else if (oldFlow.getOptType() == StockTypeEnum.RECTIFY.getV()) { // 冲正
                // TODO 不处理冲正！！
            }
        }
        int insert = storeStocksFlowMapper.insert(storeStocksFlow);
        if (insert > 0) {
            stocksFlowLogService.saveFlowLog(storeStocksFlow, null);
        }
        return insert;
    }

    @Override
    public Result<?> limitedTermWarningList(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize) {
        //查询当前租户是否有配置 保税库存有限期预警
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "LimitedTermWarning")
                .eq(SysConfig::getTenantId,TenantContext.getTenant()));
        if(!sysConfigs.isEmpty()&&isNotBlank(sysConfigs.get(0).getConfigValue())){
            Page<StoreStocks> page = new Page<>(pageNo, pageSize);
            LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(isNotBlank(storeStocksDTO.getPn()),StoreStocks::getPn,storeStocksDTO.getPn());
            lambdaQueryWrapper.like(isNotBlank(storeStocksDTO.getCopGno()),StoreStocks::getCopGno,storeStocksDTO.getCopGno());
            lambdaQueryWrapper.between(isNotBlank(storeStocksDTO.getExpirationDateStart()),StoreStocks::getExpirationDate,
                    storeStocksDTO.getExpirationDateStart(),storeStocksDTO.getExpirationDateEnd());

            lambdaQueryWrapper.gt(StoreStocks::getBeginQty,0)
                            .apply(" DATEDIFF(EXPIRATION_DATE, CURDATE())<"+sysConfigs.get(0).getConfigValue())
                    .orderByAsc(StoreStocks::getExpirationDate);
            Page<StoreStocks> storeStocksPage = baseMapper.selectPage(page, lambdaQueryWrapper);
            return Result.OK(storeStocksPage);

        }else {
            return Result.OK(new Page<StoreStocks>());
        }
    }

    /**
     * 获取库存统计汇总数据
     * @param storeStocksDTO
     * @return 返回统计汇总数据：beginQty、availableQty、occupyQty、weight
     */
    @Override
    public Result<?> loadTotalSummary(StoreStocksDTO storeStocksDTO) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
            storeStocksDTO.setCustomer(loginUser.getUserTypeRel());
        }
        Page<StoreStocks> page = new Page<>(1, 99999999);
        storeStocksDTO.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<StoreStocks> pageList = baseMapper.queryPageList(page, storeStocksDTO);
        
        // 在后端直接计算统计数据
        List<StoreStocks> allData = pageList.getRecords();
        Map<String, Object> summary = new HashMap<>();
        
        if (allData != null && !allData.isEmpty()) {
            // 计算四个统计字段的总和
            BigDecimal totalBeginQty = allData.stream()
                .map(StoreStocks::getBeginQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            BigDecimal totalAvailableQty = allData.stream()
                .map(StoreStocks::getAvailableQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            BigDecimal totalOccupyQty = allData.stream()
                .map(StoreStocks::getOccupyQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            BigDecimal totalWeight = allData.stream()
                .map(StoreStocks::getWeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 将统计结果放入返回对象
            summary.put("beginQty", totalBeginQty);
            summary.put("availableQty", totalAvailableQty);
            summary.put("occupyQty", totalOccupyQty);
            summary.put("weight", totalWeight);
        } else {
            // 如果没有数据，返回0值
            summary.put("beginQty", BigDecimal.ZERO);
            summary.put("availableQty", BigDecimal.ZERO);
            summary.put("occupyQty", BigDecimal.ZERO);
            summary.put("weight", BigDecimal.ZERO);
        }
        
        return Result.OK(summary);
    }
}
