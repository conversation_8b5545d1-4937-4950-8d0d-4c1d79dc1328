<template>
  <div class="optimized-loading">
    <div class="loading-container">
      <!-- 优化的loading动画 -->
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      
      <!-- 加载进度指示 -->
      <div class="loading-progress">
        <div class="progress-bar">
          <div :style="{ width: progress + '%' }" class="progress-fill"></div>
        </div>
        <div class="progress-text">{{ loadingText }}</div>
      </div>
      
      <!-- 加载提示 -->
      <div class="loading-tips">
        <p v-if="showTips">{{ currentTip }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OptimizedLoading',
  props: {
    progress: {
      type: Number,
      default: 0
    },
    loadingText: {
      type: String,
      default: '系统加载中，请耐心等待...'
    },
    showTips: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentTip: '',
      tips: [
        '正在初始化系统配置...',
        '正在加载核心组件...',
        '正在建立网络连接...',
        '正在验证用户权限...',
        '即将完成，请稍候...'
      ],
      tipIndex: 0
    }
  },
  mounted() {
    this.startTipRotation()
  },
  methods: {
    startTipRotation() {
      this.currentTip = this.tips[0]
      this.tipInterval = setInterval(() => {
        this.tipIndex = (this.tipIndex + 1) % this.tips.length
        this.currentTip = this.tips[this.tipIndex]
      }, 2000)
    }
  },
  beforeDestroy() {
    if (this.tipInterval) {
      clearInterval(this.tipInterval)
    }
  }
}
</script>

<style scoped>
.optimized-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-container {
  text-align: center;
  color: white;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 30px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  animation-delay: -0.5s;
  border-top-color: rgba(255, 255, 255, 0.6);
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  animation-delay: -1s;
  border-top-color: rgba(255, 255, 255, 0.4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-progress {
  margin-bottom: 20px;
}

.progress-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin: 0 auto 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10px;
}

.loading-tips {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  min-height: 20px;
}

.loading-tips p {
  margin: 0;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container {
    padding: 20px;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
  
  .progress-bar {
    width: 150px;
  }
}
</style>
