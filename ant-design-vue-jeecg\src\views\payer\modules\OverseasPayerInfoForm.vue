<template>
  <a-spin :spinning="confirmLoading">
		<a-collapse :activeKey="activeKey" :bordered="false">
      <a-collapse-panel key="1" header="境外付款方信息" style="padding-bottom: 50px;">
				<j-form-container :disabled="formDisabled">
					<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
						<a-row>
							<a-col :span="8">
								<a-form-model-item
									label="付款方名称"
									:labelCol="labelCol"
									:wrapperCol="wrapperCol"
									prop="overseasPayerName"
								>
									<j-remarks-component
										placeholder="请输入境外付款方名称"
										v-model="model.overseasPayerName"
										:maxLength="256"
										:readOnly="formDisabled"
									/>
								</a-form-model-item>
							</a-col>
							<a-col :span="8">
								<a-form-model-item label="国别地区" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="countryRegion">
									<j-search-select-tag v-model="model.countryRegion" dict="erp_countries,name,code,isenabled=1" :async="true" :pageSize="50"/>
								</a-form-model-item>
							</a-col>
              <a-col :span="8">
                <a-form-model-item  :labelCol="labelCol" :wrapperCol="wrapperCol" prop="compayTelphone">
                  <template #label>
                    <span>公司电话</span>
                    <a-tooltip slot="suffix" title="请输入10到16位带区号的电话">
                      <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                    </a-tooltip>
                  </template>
                  <a-input v-model="model.compayTelphone" placeholder="请输入公司电话" :maxLength="16"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="8" >
                <a-form-model-item label="客户邮箱" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="postNumber">
                  <a-input v-model="model.postNumber" placeholder="请输入客户邮箱" :maxLength="16"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="8" >
                <a-form-model-item
                  label="境外付款方详细地址"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="overseasPayerAddressDetail"
                >
                  <j-remarks-component
                    v-model="model.overseasPayerAddressDetail"
                    placeholder="请输入境外付款方详细地址"
                    :maxLength="256"
                    :readOnly="formDisabled"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remarks">
                  <j-remarks-component
                    placeholder="请输入备注"
                    v-model="model.remarks"
                    :maxLength="256"
                    :readOnly="formDisabled"
                  />
                </a-form-model-item>
              </a-col>
							<a-col :span="8">
								<a-form-model-item label="公章" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="officialSeal">
									<j-image-upload bizPath="overseasPayerInfo" v-model="model.officialSeal"></j-image-upload>
								</a-form-model-item>
							</a-col>
						</a-row>
					</a-form-model>
				</j-form-container>
      </a-collapse-panel>
      <a-collapse-panel
        key="2"
        header="境外付款方银行账户信息"
        :disabled="collapseDisabled"
        destroyInactivePanel
      >
        <bank-info :codeInfo="codeInfo" :isShow="formDisabled"/>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="境外付款方联系人信息" :disabled="collapseDisabled">
        <contact-info :codeInfo="codeInfo" :isShow="formDisabled"/>
      </a-collapse-panel>
		</a-collapse>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JSearchSelectTag from '../../../components/dict/JSearchSelectTag.vue'
import BankInfo from '@/views/bank/BankAccountInfoList.vue'
import contactInfo from '../OverseasPayerCotactInfoList.vue'

export default {
  name: 'OverseasPayerInfoForm',
  components: { contactInfo, BankInfo, JSearchSelectTag },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
			imgLabelCol: {
				xs: { span: 24 },
				sm: { span: 24 },
			},
			imgWrapperCol: {
				xs: { span: 24 },
				sm: { span: 18 },
			},
      activeKey: ['1'],
      codeInfo: '',
      model: {
        isEffectiveFlag: 1,
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 10 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
      },
      confirmLoading: false,
      validatorRules: {
        overseasPayerName: [{ required: true, message: '请输入境外付款方名称!' }],
        countryRegion: [{ required: true, message: '请选择国别地区!' }]
      },
      url: {
        add: '/OverseasPayerInfo/overseasPayerInfo/add',
        edit: '/OverseasPayerInfo/overseasPayerInfo/edit',
        queryById: '/OverseasPayerInfo/overseasPayerInfo/queryById',
      },
      collapseDisabled: true
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      if (record.id) {
        this.collapseDisabled = false
      }
      this.codeInfo = record.id
      this.model = Object.assign({}, record)
      this.visible = true
    },

    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
					let flag = 0
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
						flag = 1
          }
          httpAction(httpurl, this.model, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.model.id = res.result
                this.codeInfo = res.result
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              this.collapseDisabled = false
              this.activeKey = ['2', '3']
              // if (method == 'put') {
              if (flag === 1) {
                this.$emit('isVisible', false)
              } else if (flag === 0) {
                getAction('/OverseasPayerInfo/overseasPayerInfo/queryById?id=' + this.model.id).then((res) => {
                  if (res.success) {
                    this.model.updateTime = res.result.updateTime
                  }
                })
              }
            })
        }
      })
    },
  },
}
</script>