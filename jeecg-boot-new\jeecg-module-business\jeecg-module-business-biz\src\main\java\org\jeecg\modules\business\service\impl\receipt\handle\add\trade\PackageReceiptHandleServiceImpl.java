package org.jeecg.modules.business.service.impl.receipt.handle.add.trade;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.business.entity.receipt.DTO.HandleResultDTO;
import org.jeecg.modules.business.entity.receipt.DTO.ReceiptHandleDTO;
import org.jeecg.modules.business.entity.receipt.DTO.Resp;
import org.jeecg.modules.business.entity.receipt.bo.ReceiptHandleDtoToEdiReceipt;
import org.jeecg.modules.business.entity.receipt.bo.ReceiptHandleDtoToEdiStatusHistory;
import org.jeecg.modules.business.entity.receipt.vo.trade.PackageReceipt;
import org.jeecg.modules.business.entity.receipt.vo.trade.PackageReceiptBusiness;
import org.jeecg.modules.business.entity.receipt.vo.trade.PackageReceiptToEdiReceipt;
import org.jeecg.modules.business.entity.receipt.vo.trade.PackageReceiptToEdiStatusHistory;
import org.jeecg.modules.business.service.impl.receipt.AbstractReceiptHandleService;
import org.jeecg.modules.business.service.impl.receipt.service.IHandleMessageTypeService;
import org.jeecg.modules.business.util.message.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 核放单、核注清单业务回执
 *
 * <AUTHOR>
 *
 * @date 2020年9月2日 下午3:16:00
 */
@Slf4j
@Service
public class PackageReceiptHandleServiceImpl extends
		AbstractReceiptHandleService<PackageReceipt, PackageReceiptToEdiReceipt, PackageReceiptToEdiStatusHistory, ReceiptHandleDtoToEdiReceipt, ReceiptHandleDtoToEdiStatusHistory> {

	/**
	 * 处理MessageType为INV201的业务Service
	 */
	@Autowired
	private IHandleMessageTypeService inv201HandleMessageTypeService;

	/**
	 * 处理MessageType为INV202的业务Service
	 */
	@Autowired
	private IHandleMessageTypeService inv202HandleMessageTypeService;

	/**
	 * 处理MessageType为INV211的业务Service
	 */
	@Autowired
	private IHandleMessageTypeService inv211HandleMessageTypeService;

	@Autowired
	private Sas201HandleMessageTypeServiceImpl sas201HandleMessageTypeService;
	@Autowired
	private Sas211HandleMessageTypeServiceImpl sas211HandleMessageTypeService;
	@Autowired
	private Sas221HandleMessageTypeServiceImpl sas221HandleMessageTypeService;
	@Autowired
	private Sas223HandleMessageTypeServiceImpl sas223HandleMessageTypeService;
	@Autowired
	private Sas000HandleMessageTypeServiceImpl sas000HandleMessageTypeService;
	@Autowired
	private Eml211HandleMessageTypeServiceImpl eml211HandleMessageTypeService;

	@Override
	public HandleResultDTO handleReceipt(ReceiptHandleDTO receiptHandle) {
		// 获取加工贸易报文回执的业务实体
		PackageReceiptBusiness packageReceipt = BeanUtil.copyBeanToNewBean(receiptHandle.getReceiptMessage(),
				PackageReceiptBusiness.class);
		log.info("[{}业务回执]回执文件名称:{},回执来源类型:{},回执类型:{},统一编号:{},业务类型:{},处理结果:{}",
				packageReceipt.getReceiptType(), receiptHandle.getFileName(),
				receiptHandle.getSourceType(), packageReceipt.getReceiptType(), packageReceipt.getSeqNo(),
				packageReceipt.getEnvelopInfo().getMessageType(), packageReceipt.getManageResult());
		// 存储异常信息
		List<Resp> respList = new ArrayList<>(16);
		int index = 0;
		switch (packageReceipt.getEnvelopInfo().getMessageType()) {
		case "INV201":
			return inv201HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
		case "INV202":
			return inv202HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
		case "INV211":
			return inv211HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
		//Message_Type == SAS000 (核放单技术回执)
		case "SAS000":
			return sas000HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
		//Message_Type == SAS211 (出入库单审核回执)
		case "SAS201":
			return sas201HandleMessageTypeService.handleMessageType(packageReceipt,
					receiptHandle.getFileName(), receiptHandle.getSourceType());
		case "SAS211":
			return sas211HandleMessageTypeService.handleMessageType(packageReceipt,
					receiptHandle.getFileName(), receiptHandle.getSourceType());
		//Message_Type == SAS221 (核放单审核回执)
		case "SAS221":
			return sas221HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
		// Message_Type == SAS223 (核放单过卡回执)
		case "SAS223":
			return sas223HandleMessageTypeService.handleMessageType(packageReceipt, receiptHandle.getFileName(),
					receiptHandle.getSourceType());
			//加贸手册回执处理 EML211
		case "EML211":
			return eml211HandleMessageTypeService.handleMessageType(
					packageReceipt, receiptHandle.getFileName(),receiptHandle.getSourceType());
		default:
		logger.info("[核放单、核注清单业务回执]回执文件名称:{},回执来源类型:{},统一编号:{},未知回执类型:{}", receiptHandle.getFileName(),
				receiptHandle.getSourceType(), packageReceipt.getSeqNo(), packageReceipt.getReceiptType());
			Resp resp = new Resp();
			resp.setMsg(++index + ".核放单、核注清单业务回执：").setStatus("No");
			respList.add(resp);
		return new HandleResultDTO(2, String.format("[核放单、核注清单业务回执,未知回执文件,未作处理;]回执类型:%s,未知回执类型", packageReceipt.getReceiptType()), respList);
		}
	}
}
