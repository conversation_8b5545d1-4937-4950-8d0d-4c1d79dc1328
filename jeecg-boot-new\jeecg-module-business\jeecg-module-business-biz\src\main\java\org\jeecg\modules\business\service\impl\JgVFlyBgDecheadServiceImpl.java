package org.jeecg.modules.business.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.business.entity.JgVFlyBgDechead;
import org.jeecg.modules.business.mapper.JgVFlyBgDecheadMapper;
import org.jeecg.modules.business.service.IJgVFlyBgDecheadService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@DS("adms7pro")
@Slf4j
@Service
public class JgVFlyBgDecheadServiceImpl extends ServiceImpl<JgVFlyBgDecheadMapper, JgVFlyBgDechead> implements IJgVFlyBgDecheadService {

    /**
     * @param page
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public IPage<JgVFlyBgDechead> queryPageList(Page<JgVFlyBgDechead> page, String customerName, String starDate, String lastDate) {
        return baseMapper.queryPageList(page, customerName, starDate, lastDate);
    }

    /**
     * @param page
     * @param ownerCode
     * @param ownerName
     * @return
     */
    @Override
    public IPage<JgVFlyBgDechead> queryPageListCommon(Page<JgVFlyBgDechead> page, String ownerCode, String ownerName, String startDate, String lastDate, String isAll) {
        return baseMapper.queryPageListCommon(page, ownerCode, ownerName, startDate, lastDate, isAll);
    }

    /**
     *
     * @param page
     * @param agentCode
     * @param agentName
     * @param startDate
     * @param lastDate
     * @param isAll
     * @return
     */
    @Override
    public IPage<JgVFlyBgDechead> queryPageListSBCommon(Page<JgVFlyBgDechead> page, String agentCode, String agentName, String startDate, String lastDate, String isAll) {
        return baseMapper.queryPageListSBCommon(page, agentCode, agentName, startDate, lastDate, isAll);
    }
}
