<template>
	<div>
		<a-card
			style="width: 100%; min-height: 900px; margin-top: 0"
			:bordered="true"
			:headStyle="{height:'14px'}"
			:bodyStyle="{ padding: '10px 10px 5px 20px' }"
		>
			<!--顶部信息-->
			<template>
				<!--左侧按钮-->
				<span slot="title">
						     <a-button
									 size="small"
									 type="primary"
									 icon="plus"
									 @click="handleAdd"
								 >新增</a-button>
						   <a-button
								 style="margin-left: 4px"
								 size="small"
								 type="primary"
								 icon="save"
								 @click="onSave"
							 >暂存</a-button>
						 		   <a-button
									:disabled="!emsHead.id"
										 style="margin-left: 4px"
										 size="small"
										 type="primary"
										 icon="delete"
										 @click="handleDelete"
									 >删除</a-button>
									          <!-- 添加导入按钮 -->
				 <a-button
                    style="margin-left: 4px"
                    size="small"
                    type="primary"
                    icon="upload"
                     :disabled="!emsHead.id || !['2', '3', '4'].includes(currentTabKey)"
                    @click="handleImport"
                  >导入</a-button>

	
				</span>
<!--				右侧按钮-->
				<span slot="extra">
					<a-button type="primary" style="margin-right: 4px" :disabled="!emsHead.id"
					icon="sound" :loading='pushLoading' @click="handlePush('S')">推送</a-button>
						     <a-button
							 :disabled="!emsHead.id"
									 size="small"
									 type="primary"
									 icon="bulb"
									 :loading='dclLoading'
									@click="handlePush('D')"
								 >申报</a-button>
				   <!-- <a-button
						 style="margin-left: 4px"
						 size="small"
						 type="primary"
						 icon="printer"
						 @click="onSave"
						 v-has="'invt:edit'"
					 >打印</a-button>
				 	   <a-button
							 style="margin-left: 4px"
							 size="small"
							 type="primary"
							 icon="download"
							 @click="onSave"
							 v-has="'invt:edit'"
						 >导出</a-button> -->
				</span>


			</template>
      <!--标签页-->
      <a-tabs @change="callbacktab" size="small">
        <!--表头-->
        <a-tab-pane key="1" tab="表头">
          <list-ems-header ref="listEmsHeader" style="margin-top: -12px"
          @getEmsHead="getEmsHead" />
        </a-tab-pane>
        <!--料件-->
        <a-tab-pane key="2" tab="料件">
					<list-ems-aimg ref="listEmsAimg" style="margin-top: -12px"
          :emsHead="emsHead" />
        </a-tab-pane>
        <!--成品-->
        <a-tab-pane key="3" tab="成品">
					<list-ems-aexg ref="listEmsAexg" style="margin-top: -12px"
												 :emsHead="emsHead"/>
        </a-tab-pane>
        <!--单损耗-->
        <a-tab-pane key="4" tab="单损耗">
					<list-ems-cm ref="listEmsCm" style="margin-top: -12px"
											 :emsHead="emsHead"/>
        </a-tab-pane>
        <!--随附单据-->
        <!-- <a-tab-pane key="5" tab="随附单据">
        </a-tab-pane> -->

      </a-tabs>



		</a-card>
			<import-modal ref="importModal" :downLoadUrl="url.downLoadTempUrl" 
		:importUrl="url.importExcelUrl" 
		downLoadButtonText="下载加工贸易手册表体导入模板"
		ie-flag="E" title="加工贸易手册表体导入" @closeUploadModal="closeUploadModal" @loadData="loadData(1)">
		</import-modal>
	</div>



</template>
<script>
import listEmsHeader from '@/views/AddTradeHandbook/list-ems-header.vue'
import listEmsAimg from '@/views/AddTradeHandbook/list-ems-aimg.vue'
import listEmsAexg from '@/views/AddTradeHandbook/list-ems-aexg.vue'
import listEmsCm from '@/views/AddTradeHandbook/list-ems-cm.vue'
import { deleteAction,_postAction  } from '@/api/manage'
import ImportModal from '@/components/ImportModal/ImportModal'
export default {
	name: 'AddTradeHandbookEdit',
  components: {
    listEmsHeader,
		listEmsAimg,
		listEmsAexg,
		listEmsCm,
		ImportModal
  },
	data() {
		return {
			 // 添加当前选中的 tab 键
      currentTabKey: '',
			pushLoading:false,
			dclLoading:false,
			emsHead:{},
			url: {
				push: '/business/ems/handlePush',
				downLoadTempUrl: '/template/加工贸易手册表体导入模板.xlsx',
				importExcelUrl: '/business/ems/importDetailsExcelUrl',
			},
		}
		},
	created() {},
	computed: {
		// 通过计算属性更新页签
		routeTab() {
			// let lastid = this.captureTitle(this.currentDetailHeader.id)
			return {
				title: '加工贸易手册',
				tips: `加工贸易手册`
			}
		}
	},
	methods: {
	loseUploadModal() {
			this.$refs.importModal.visible = false
			this.$refs.importModal.fileList = []
			// this.loadData(1)
			// this.onClearSelected()
		},
	handleImport() {
		this.$message.info('功能尚在开发中，敬请期待。')
		// this.$refs.importModal.visible = true

      console.log('点击了导入按钮');
    },
    callbacktab(key) {
		this.currentTabKey = key;
			if(key == '2'){
				this.$nextTick(() => {
					this.$refs.listEmsAimg.init()
				})

			}else if(key == '3'){
				this.$nextTick(() => {
					this.$refs.listEmsAexg.init()
				})
			}else if(key == '4'){
				this.$nextTick(() => {
				this.$refs.listEmsCm.init()
				})
			}
		},
		getEmsHead(val){
			this.emsHead = val
		},
    onSave(){
      this.$refs.listEmsHeader.handleSave()
    },
	//新增
	handleAdd(){
		//新增时 新增
		if (!this.emsHead.id) {
				this.$tabs.refresh()
			}else{
				this.$tabs.close({
				to: `/AddTradeHandbook/AddTradeHandbookEdit`
			})
			}
	},
	handleDelete(){
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除该数据?',
					onOk: function () {
						deleteAction('/business/ems/deleteBatch', { ids: that.emsHead.id })
							.then(res => {
								if (res.success) {
									//关闭当前页签
									that.$tabs.close({
										to: `/AddTradeHandbook/AddTradeHandbookEdit`
									})
									that.$message.success(res.message)
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
							})
					}
				})
			

	},
	//推送/申报
	handlePush(flag){
		if (!this.emsHead.id) {
				this.$message.warning(flag=='S'?'请先保存表头信息后再进行推送。':'请先保存表头信息后再进行申报。')
				return
			}
		
			let msg = flag=='S'?`确定要推送报文吗?`:`确认操作申报吗？`
			
			let that = this
			this.$confirm({
				title: flag=='S'?'确认推送':'确认申报',
				content: msg,
				onOk: function() {
					that.pushLoading = true
					that.dclLoading = true
					_postAction(that.url.push, {
						ids: that.emsHead.id,
						type:'1',
						flag:flag
					})
						.then((res) => {
							if (res.success){
								that.$message.success(flag=='S'?"推送成功":"申报成功")
								that.$tabs.refresh()
							}else {
								that.$message.warning(res.message)
							}
						}).finally(() => {
						that.pushLoading = false
						that.dclLoading = false
					})
				}
			})

	},

	}
}


</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';

/deep/.ant-card-head {
  min-height: 31px !important;
}
.ant-card /deep/ .ant-card-head-wrapper {
  height: 34px;
}
/deep/.ant-tabs-nav-container{
  margin-top: -10px;
}
.ant-card /deep/ .ant-card-head {
  height: 40px;
}
.ant-card /deep/ .ant-card-head-title {
  height: 40px;
  padding: 4px 0px 0px 0px;
}

/deep/ table.my-table {
  border-radius: 4px 4px 0 0;
  border-collapse: collapse;
  width: 100%;
  line-height: 1.5;
  text-align: right;
}

/deep/ table.my-table td {
  box-sizing: border-box;
  border: 1px solid #e8e8e8;
  padding: 0 8px;
}

/deep/ table.my-table tr td:nth-child(odd) {
  background: #eff4ff4d;
  width: 15%;
}

/deep/ table.my-table tr td:nth-child(even) {
  width: 18.3333333333%;
}

.logo {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  margin-right: 16px;
  vertical-align: middle;
}

/deep/ .ant-form-item {
  margin-bottom: 0;
}

/deep/ div.ant-col.ant-form-item-control-wrapper {
  display: inline-block;
}

/*用以兼容ie下fieldset-disabled无效情况*/
/deep/ fieldset {
  /* to set absolute position for :after content */
  position: relative;
}

/* this will 'screen' all fieldset content from clicks */
/deep/ fieldset[disabled]:after {
  content: ' ';
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  /* i don't know... it was necessary to set background */
  background: url(data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==);
}
/deep/ ant-layout-footer {
  height: 300px;
}


</style>
