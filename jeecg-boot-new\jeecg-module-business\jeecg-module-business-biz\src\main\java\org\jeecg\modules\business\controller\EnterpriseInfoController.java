package org.jeecg.modules.business.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.Contract;
import org.jeecg.modules.business.entity.CustomerEnterprise;
import org.jeecg.modules.business.entity.EnterpriseInfo;
import org.jeecg.modules.business.entity.StoreStocks;
import org.jeecg.modules.business.service.ICustomerEnterpriseService;
import org.jeecg.modules.business.service.IEnterpriseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date: 2022-02-18
 * @Version: V1.0
 */
@Api(tags = "企业信息表")
@RestController
@RequestMapping("/EnterpriseInfo/enterpriseInfo")
@Slf4j
public class EnterpriseInfoController extends JeecgController<EnterpriseInfo, IEnterpriseInfoService> {
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Autowired
    private ICustomerEnterpriseService customerEnterpriseService;

    /**
     * 添加
     *
     * @param enterpriseInfo
     * @return
     */
    @AutoLog(value = "企业信息表-添加")
    @ApiOperation(value = "企业信息表-添加", notes = "企业信息表-添加")
    @PostMapping(value = "/add")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> add(@RequestBody EnterpriseInfo enterpriseInfo) {
        enterpriseInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
        enterpriseInfo.setUpdateTime(new Date());
        enterpriseInfoService.save(enterpriseInfo);
        enterpriseInfoService.updateTenantName(Long.valueOf(TenantContext.getTenant()), enterpriseInfo.getEnterpriseFullName());
        Result<String> result = new Result<String>();
        try {
            result.setResult(enterpriseInfo.getId());
            result.setMessage("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("查询失败！");
        }
        return result;

    }

    /**
     * 编辑
     *
     * @param enterpriseInfo
     * @return
     */
    @AutoLog(value = "企业信息表-编辑")
    @ApiOperation(value = "企业信息表-编辑", notes = "企业信息表-编辑")
    @PostMapping(value = "/edit")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> edit(@RequestBody EnterpriseInfo enterpriseInfo) {
        enterpriseInfoService.updateById(enterpriseInfo);
        enterpriseInfoService.updateTenantName(Long.valueOf(TenantContext.getTenant()), enterpriseInfo.getEnterpriseFullName());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过租户id查询
     *
     * @param
     * @return
     */

    @AutoLog(value = "企业信息表-通过租户id查询")
    @ApiOperation(value = "企业信息表-通过租户id查询", notes = "企业信息表-通过租户id查询")
    @GetMapping(value = "/getCollectionEnterpriseList")
    public Result<Map<String, Object>> getCollectionAccountList(EnterpriseInfo enterpriseInfo) {
        enterpriseInfo.setTenantId(Long.parseLong(TenantContext.getTenant()));
        Result<Map<String, Object>> result = new Result<Map<String, Object>>();
        try {
            List<EnterpriseInfo> list = enterpriseInfoService.getCollectionEnterpriseList(enterpriseInfo);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", list);
            result.setSuccess(true);
            result.setResult(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("查询失败！");
        }
        return result;
    }

    /**
     * 企业备案信息列表
     *
     * @param customerEnterprise
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.EnterpriseInfo>
     * <AUTHOR>
     * @date 2023/11/9 17:38
     */
    @AutoLog(value = "企业备案信息列表")
    @ApiOperation(value = "企业备案信息列表", notes = "企业备案信息列表")
    @GetMapping(value = "/recordInfoList")
    public Result<?> recordInfoList(CustomerEnterprise customerEnterprise,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<CustomerEnterprise> page = new Page<>(pageNo, pageSize);
        IPage<CustomerEnterprise> pageList = customerEnterpriseService.recordInfoList(page, customerEnterprise);
        return Result.OK(pageList);
    }

    /**
     * 根据ID获取企业备案信息
     *
     * @param id
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @AutoLog(value = "企业备案信息列表")
    @ApiOperation(value = "企业备案信息列表", notes = "企业备案信息列表")
    @GetMapping(value = "/getRecordInfoById")
    public Result<?> getRecordInfoById(@RequestParam("id") String id, HttpServletRequest req) {
        return Result.OK(customerEnterpriseService.getById(id));
    }

//    @AutoLog(value = "海关注册企业信息")
//    @ApiOperation(value = "海关注册企业信息", notes = "海关注册企业信息根据信用代码或海关十位")
//    @GetMapping(value = "/getCustomerEnterpriseByQueryParams")
//    public Result<CustomerEnterprise> getCustomerEnterpriseByQueryParams(@RequestParam("socialCode") String socialCode,
//                                                                         @RequestParam("departcd") String departcd,
//                                                                         @RequestParam("tradeCiqCode") String tradeCiqCode,
//                                                                         @RequestParam("customerName") String customerName){
//         if(StringUtils.isBlank(socialCode)&&StringUtils.isBlank(departcd)&&StringUtils.isBlank(tradeCiqCode)&&StringUtils.isBlank(customerName)){
//             return Result.OK();
//         }else {
//             List<CustomerEnterprise> customerEnterprises = customerEnterpriseService.list(new LambdaQueryWrapper<CustomerEnterprise>()
//                     .and(i -> i.eq(isNotBlank(socialCode), CustomerEnterprise::getSocialCode, socialCode)
//                             .eq(isNotBlank(departcd),CustomerEnterprise::getDepartcd, departcd)
//                             .eq(isNotBlank(tradeCiqCode),CustomerEnterprise::getCiqCode, tradeCiqCode)
//                             .eq(isNotBlank(customerName),CustomerEnterprise::getDepartName, customerName)));
//             // 2024/11/25 11:20@ZHANGCHAO 追加/变更/完善：去调用树毛接口！！
//             CustomerEnterprise customerEnterprise = null;
//             if (isEmpty(customerEnterprises)) {
//                 customerEnterprise = new CustomerEnterprise();
//                 Result<?> result = enterpriseInfoService.getCustomerEnterpriseBySm(socialCode, departcd, tradeCiqCode, customerName);
//                 if (result.isSuccess()) {
//                     customerEnterprise = (CustomerEnterprise) result.getResult();
//                     if (isNotEmpty(customerEnterprise)) {
//                         if (isBlank(customerEnterprise.getDepartcd())
//                                 && isBlank(customerEnterprise.getDepartName())
//                                 && isBlank(customerEnterprise.getCiqCode())
//                                 && isBlank(customerEnterprise.getSocialCode())) {
//                             // ...
//                         } else {
//                             customerEnterpriseService.save(customerEnterprise);
//                         }
//                     }
//                 }else {
//                     return Result.error(result.getMessage());
//                 }
//             } else {
//                 customerEnterprise = customerEnterprises.get(0);
//             }
//             return Result.OK(customerEnterprise);
//         }
//    }

    @AutoLog(value = "海关注册企业信息")
    @ApiOperation(value = "海关注册企业信息", notes = "海关注册企业信息根据信用代码或海关十位")
    @GetMapping(value = "/getCustomerEnterpriseByQueryParams")
    public Result<CustomerEnterprise> getCustomerEnterpriseByQueryParams(@RequestParam("socialCode") String socialCode,
                                                                         @RequestParam("departcd") String departcd,
                                                                         @RequestParam("tradeCiqCode") String tradeCiqCode,
                                                                         @RequestParam("customerName") String customerName){
        // 检查是否所有参数都为空
        if (StringUtils.isBlank(socialCode) && StringUtils.isBlank(departcd)
                && StringUtils.isBlank(tradeCiqCode) && StringUtils.isBlank(customerName)) {
            return Result.OK();
        }

        List<CustomerEnterprise> customerEnterprises = customerEnterpriseService.list(new LambdaQueryWrapper<CustomerEnterprise>()
                .and(i -> i.eq(isNotBlank(socialCode), CustomerEnterprise::getSocialCode, socialCode)
                        .eq(isNotBlank(departcd),CustomerEnterprise::getDepartcd, departcd)
                        .eq(isNotBlank(tradeCiqCode),CustomerEnterprise::getCiqCode, tradeCiqCode)
                        .eq(isNotBlank(customerName),CustomerEnterprise::getDepartName, customerName)));
        CustomerEnterprise customerEnterprise = isNotEmpty(customerEnterprises) ? customerEnterprises.get(0) : new CustomerEnterprise();

        // 调用远程接口获取数据
        Result<?> remoteResult = enterpriseInfoService.getCustomerEnterpriseBySm(socialCode, departcd, tradeCiqCode, customerName);
        if (remoteResult.isSuccess() && remoteResult.getResult() != null) {
            CustomerEnterprise remoteEnterprise = (CustomerEnterprise) remoteResult.getResult();
            // 检查远程返回的数据是否有效
            if (isNotEmpty(remoteEnterprise) &&
                    !(isBlank(remoteEnterprise.getDepartcd()) &&
                            isBlank(remoteEnterprise.getDepartName()) &&
                            isBlank(remoteEnterprise.getCiqCode()) &&
                            isBlank(remoteEnterprise.getSocialCode()))) {
                if (isNotEmpty(customerEnterprise.getId())) {
                    // 本地更新
                    BeanUtil.copyProperties(remoteEnterprise, customerEnterprise, CopyOptions.create().ignoreNullValue());
                    customerEnterpriseService.updateById(customerEnterprise);
                } else {
                    // 本地保存
                    customerEnterpriseService.save(remoteEnterprise);
                }
                return Result.ok(remoteEnterprise);
            }
        } else {
            return Result.ok(customerEnterprise);
        }
        return Result.ok(customerEnterprise);
    }

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @AutoLog(value = "海关备案企业信息查询")
    @ApiOperation(value = "海关备案企业信息查询", notes = "海关备案企业信息查询")
    @GetMapping(value = "/customsRecordEnterpriseInformationInquiry")
    public Result<?> customsRecordEnterpriseInformationInquiry() {
        return enterpriseInfoService.customsRecordEnterpriseInformationInquiry();
    }

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @AutoLog(value = "海关备案企业信息查询")
    @ApiOperation(value = "海关备案企业信息查询", notes = "海关备案企业信息查询")
    @GetMapping(value = "/customsRecordEnterpriseInformationInquiry_")
    public Result<?> customsRecordEnterpriseInformationInquiry_() {
        return enterpriseInfoService.customsRecordEnterpriseInformationInquiry_();
    }

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @AutoLog(value = "海关备案企业信息查询")
    @ApiOperation(value = "海关备案企业信息查询", notes = "海关备案企业信息查询")
    @GetMapping(value = "/customsRecordEnterpriseInformationInquiry__")
    public Result<?> customsRecordEnterpriseInformationInquiry__() {
        return enterpriseInfoService.customsRecordEnterpriseInformationInquiry__();
    }

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @PostMapping(value = "/updateFileTempl")
    public Result<?> updateFileTempl(@RequestParam("templ") String templ,
                                     @RequestParam("type") String type) {
        return enterpriseInfoService.updateFileTempl(templ, type);
    }

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2023/11/9 18:28
     */
    @PostMapping(value = "/removeFileTempl")
    public Result<?> removeFileTempl(@RequestParam("templ") String templ,
                                     @RequestParam("type") String type) {
        return enterpriseInfoService.removeFileTempl(templ, type);
    }
}
