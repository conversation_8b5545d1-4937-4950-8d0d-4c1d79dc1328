package org.jeecg.modules.business.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.service.IDockingEasyPassService;
import org.jeecg.modules.business.service.INemsInvtHeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * 业务模块定时任务处理类 -- 核注单类树毛接口
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/11/28 23:40
 */
@Component
@Slf4j
public class BusinessInvtSmJobHandler {

    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;

    /**
     * 同步树毛核注单详情用定时任务
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/29 10:55
     */
    @XxlJob(value = "SyncSmInvtDataJob")
    public ReturnT<String> SyncSmInvtDataJob(String params) {
        log.info("同步树毛核注单详情用定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String swid = jsonObject.getString("swid");
        String systemId = jsonObject.getString("systemId");
        String seqNo = jsonObject.getString("seqNo");
        log.info("【SyncSmInvtDataJob】获取到的参数：{}", params);
        Result<?> result = nemsInvtHeadService.GetInvtData(swid, systemId, seqNo);
        log.info("【同步树毛核注单详情用定时任务】执行结果：{}", result.getResult());
        log.info("同步树毛核注单详情用定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }

    /**
     * 同步树毛核注单列表用定时任务
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/29 10:55
     */
    @XxlJob(value = "SyncSmInvtJob")
    public ReturnT<String> SyncSmInvtJob(String params) {
        log.info("同步树毛核注单列表用定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String swid = jsonObject.getString("swid");
        String systemId = jsonObject.getString("systemId");
        String status = jsonObject.getString("status");
        String etpsCategory = jsonObject.getString("etpsCategory");
        String tradeCode = jsonObject.getString("tradeCode");
        String ieFlag = jsonObject.getString("ieFlag");
        String dclTypecd = jsonObject.getString("dclTypecd");
        String invtNo = jsonObject.getString("invtNo");
        String seqNo = jsonObject.getString("seqNo");
        String etpsNo = jsonObject.getString("etpsNo");
        String putrecNo = jsonObject.getString("putrecNo");
        String vrfdedMarkcd = jsonObject.getString("vrfdedMarkcd");
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        String dclStartDate = jsonObject.getString("dclStartDate");
        String dclEndDate = jsonObject.getString("dclEndDate");
        log.info("【SyncSmInvtJob】获取到的参数：{}", params);
        Result<?> result = nemsInvtHeadService.GetInvtList(swid, systemId, status, etpsCategory, tradeCode, ieFlag, dclTypecd, invtNo,
                seqNo, etpsNo, putrecNo, vrfdedMarkcd, startDate, endDate, dclStartDate, dclEndDate);
        log.info("【同步树毛核注单列表用定时任务】执行结果：{}", result.getResult());
        log.info("同步树毛核注单列表用定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }

    /**
     * 同步核注单用 -- 森锋用
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/29 10:55
     */
    @XxlJob(value = "SyncSmInvtUnClosedJob")
    public ReturnT<String> SyncSmInvtUnClosedJob(String params) {
        log.info("同步树毛核注单详情未结关用定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String swid = jsonObject.getString("swid");
        String systemId = jsonObject.getString("systemId");
        String tradeCode = jsonObject.getString("tradeCode");
        String startTime = jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");
        String isAll = jsonObject.getString("isAll");
        log.info("【SyncSmInvtUnClosedJob】获取到的参数：{}", params);
        Result<?> result = nemsInvtHeadService.syncInvtVrfdedMarkcd(swid, systemId, tradeCode, startTime, endTime, isAll);
        log.info("【同步树毛核注单详情未结关用定时任务】执行结果：{}", result.getResult());
        log.info("同步树毛核注单详情未结关用定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }
}
