<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.AiConfigMapper">

    <select id="listAiSettings" resultType="org.jeecg.modules.business.entity.AiConfig">
        SELECT
            *
        FROM
            `ai_config`
        <where>
            `TENANT_ID` = #{tenantId}
            <if test="tplName != null and tplName != ''">
                AND `tpl_name` LIKE CONCAT('%', #{tplName}, '%')
            </if>
            <if test="tplNameC != null and tplNameC != ''">
                AND `tpl_name` = #{tplNameC}
            </if>
            <if test="ieFlag != null and ieFlag != ''">
                AND `ie_flag` = #{ieFlag}
            </if>
            <if test="shipTypeCode != null and shipTypeCode != ''">
                AND `ship_type_code` = #{shipTypeCode}
            </if>
            <if test="declarePlace != null and declarePlace != ''">
                AND `declare_place` = #{declarePlace}
            </if>
            <if test="outPortCode != null and outPortCode != ''">
                AND `out_port_code` = #{outPortCode}
            </if>
            <if test="isDefault != null">
                AND `is_default` = #{isDefault}
            </if>
        </where>
        ORDER BY
            `create_date` DESC
    </select>
</mapper>
