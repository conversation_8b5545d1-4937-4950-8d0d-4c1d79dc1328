<template>
	<!--规格型号-->
	<a-modal v-enterToNext :centered="true" :mask="mask" :maskClosable="maskClosable" :title="title"
		:visible="showModal" cancel-text='' width="58%" @cancel="handleCancel" @ok="handleOk">
		<a-card :bodyStyle="{ padding: '0px' }" :bordered="false">
			<!-- 添加智能填写功能区域 -->
			<div class="smart-fill-container">
				<!--				<a-alert type="info" message="智能填写" description="输入商品描述文本，系统将智能解析出规格型号信息" show-icon style="margin-bottom: 8px;"-->
				<!--					:closable="true" />-->
				<div class="smart-fill-input">
					<a-textarea v-model="smartFillText" :disabled="smartFillLoading"
						:rows="3" placeholder="输入商品描述文本，系统将智能解析出规格型号信息，如：盛放调料的盒子，PC+ABS+PP 无型号 无牌 不享惠" />
					<a-button :loading="smartFillLoading" icon="robot" type="primary" @click="handleSmartFill">
						智能填写
					</a-button>
				</div>
			</div>

			<div>
				<!--列表-->
				<a-table :bordered="true" :columns="columns" :dataSource="dataSource" :loading="loading" :pagination="false"
					:scroll="{ x: false, y: 280, scrollToFirstRowOnChange: true, }" rowKey="AppCertCode">

					<!-- 申报规范 -->
					<template slot="decHsModel" slot-scope="text,record">
						<div v-if="record.element === '品牌类型'">
<!--							<j-search-select-tag ref="refhsModel" style="width: 80%" v-model="record.hsModel" dict="PPLX"-->
<!--								placeholder="" :disabled="disabled" :isBgInput="true" />-->

							<decSelect-Item  v-model='record.hsModel' :readonly='disabled'
															:styleSrt="styleSrtSelect"  :styleType="1" dictKey="PPLX" selectType="decgoods"
															 style="width: 80%"
															tableName="sys_dict"></decSelect-Item>
						</div>
						<div v-else-if="record.element === '出口享惠情况'">
<!--							<j-search-select-tag style="width: 80%" v-model="record.hsModel" dict="XHQK" placeholder=""-->
<!--								:disabled="disabled" :isBgInput="true" />-->
							<decSelect-Item  v-model='record.hsModel' :readonly='disabled'
															 :styleSrt="styleSrtSelect"  :styleType="1" dictKey="XHQK" selectType="decgoods"
															 style="width: 80%"
															 tableName="sys_dict"></decSelect-Item>
						</div>

						<div v-else-if="dataSourceRequired.indexOf(record.no) > -1">
							<a-tooltip placement="right">
								<template slot="title">
									<span>该申报要素不能为空</span>
								</template>
								<a-input v-model="record.hsModel" :disabled="disabled" placeholder=""
												 style="width: 80%;background:#FAFFBD"
									@change="inputChange" @focus="handleFocus" />

							</a-tooltip>

						</div>
						<div v-else>
							<a-input v-model="record.hsModel" :disabled="disabled" placeholder="" style="width: 80%"
											 @focus="handleFocus"
								@change="inputChange" />

						</div>
					</template>
					<template slot="decHsRequire" slot-scope="text,record">

					</template>

				</a-table>
				<table class='inlineTable'>
					<tr class='inlineTd'>
						<td style="width: 10%; text-align:right">规格型号</td>
						<td>
							<a-input v-model="hsModelStr" :disabled="hsModelType" placeholder="请输入规格型号"></a-input>
						</td>
						<td style="width: 10%" v-html="hsModelZj"></td>
					</tr>
				</table>
			</div>


		</a-card>


	</a-modal>
</template>

<script>
import { decHsCode } from "@/api/dec/dec"
import DictSelect from '@views/_components/dict-select'
 import decSelectItem from '@views/declaration/component/m-table-select-item-modal'
//import decSelectItem from '@/views/Business/customs-declaration/m-table-select-item.vue'
import { getAction } from "@/api/manage";

export default {
	name: 'dec-cert-type',
	components: { decSelectItem},
	data() {
		return {
			styleSrtSelect: 'background:#FAFFBD',
			decHsmodelOk: false,
			mask: true,
			maskClosable: false,
			formItem: {
				labelCol: {
					span: 0,
				},
				wrapperCol: {
					span: 0,
				},
			},
			hsModelType: true,
			hsModelPPLX: "hsModelPPLX",
			// 已选的键
			selectedRowKeys: [],
			// 已选的内容
			selectedRows: [],
			// value副本
			val: '',
			// show副本
			showModal: false,
			//hscode副本
			hscodeModal: '',
			// imSignShowLists
			imSignShowModal: '',
			// hintDialog副本
			// hintDialog: {
			//     decHsmodelDialog: '',
			// },
			decHsmodelDialog1: {
				decHsmodelDialog: '',
			},
			dataSource: [],
			columns: [{
				title: '序号',
				key: 'no',
				width: 80,
				align: 'center',
				dataIndex: 'no',
				customRender: function (text) {
					return Number.parseFloat(text) + 1
				}

			},
			{
				title: '申报规范',
				width: 240,
				dataIndex: 'element',
				key: 'element',
				customRender: function (text) {
					return text.replace('(', '').replace(')', '')
				}
			},
			{
				title: '申报要素',
				dataIndex: 'hsModel',
				key: 'hsModel',
				scopedSlots: { customRender: 'decHsModel' },
			},

			],
			loading: false,
			zzlbList: [],
			copLimitTypeList1: [],
			txtrequestCertType1: '',
			//申报规范
			decHsauditRessList: [],
			//页面状态
			pageType: [],
			//判断是否是必选的集合
			dataSourceRequired: [],
			//显示hsmodelInput框
			hsModelStr: '',
			//显示hsmodelInput框字节数
			hsModelZj: '',
			//智能填写功能相关
			smartFillText: '',
			smartFillLoading: false,
			url: {
				smartFill: '/DecHead/dec-head/smartFill',
			}
		}
	},
	created() {
	},

	props: {
		value: {
			type: String,
			require: true,
		},
		show: {
			type: Boolean,
		},
		imSignShowList: {
			type: Boolean,
		},
		disabled: {
			type: Boolean,
			default: false
		},
		hintDialog: {
			type: Object,
			default: () => []
		},
		showPage: {
			type: Object,
			default: () => []
		},
		txtrequestCertType: {
			type: Object,
			default: () => []
		},
		title: {
			type: String,
			default: '编辑规格型号信息'
		}
	},
	model: {
		prop: 'value',
		event: 'change',
	},
	watch: {
		value: {
			handler(val) {
				console.log(val)
				this.hscodeModal = ""
				//规格型号
				if (val) {
					//移到模态框这再追加
					//处理申报要素字符串
					let hsModelList = val.toString().split("|")
					if (isNaN(hsModelList[0]) && !isNaN(hsModelList[1])) { //第一位不为数字，但第二位为数字
						let newHsmodel = "|" + val
						val = newhsmodel;
					}
					if (isNaN(hsModelList[0]) && isNaN(hsModelList[1])) {  //第一位不为数字，第二位也不为数字
						let newhsmodel = "||" + val
						val = newhsmodel;
					}
					console.log('111')
					console.log(val)

					let hsmodelStr = val.toString().split("|")
					this.decHsCodeMessage(this.txtrequestCertType.hscode, hsmodelStr)

				}

			},
		},
		val: {
			handler() {
				this.$emit('change', this.val)
			},
		},
		show: {
			handler(val) {
				console.log('????????????')
				this.showModal = !this.showModal

				this.$nextTick(() => {
					let hsmodelStr = ''
					//重新定义变量解决数值联动问题
					let hsmodelDec = this.txtrequestCertType.hsmodel;
					console.log('1111111')
					console.log(hsmodelDec)
					if (this.txtrequestCertType.hsmodel && (this.hscodeModal == this.txtrequestCertType.hscode || this.hscodeModal == "")) {
						//移到模态框这再追加
						//处理申报要素字符串
						let hsModelList = this.txtrequestCertType.hsmodel.toString().split("|")
						if (isNaN(hsModelList[0]) && !isNaN(hsModelList[1])) { //第一位不为数字，但第二位为数字
							let newHsmodel = "|" + this.txtrequestCertType.hsmodel
							hsmodelDec = newhsmodel;
						}
						if (isNaN(hsModelList[0]) && isNaN(hsModelList[1])) {  //第一位不为数字，第二位也不为数字
							let newhsmodel = "||" + this.txtrequestCertType.hsmodel
							hsmodelDec = newhsmodel;
						}
						console.log('222')
						console.log(hsmodelDec)
						hsmodelStr = hsmodelDec.toString().split("|");
					}
					setTimeout(() => {
						// if(hsmodelStr){
						this.hsModelStr = hsmodelDec
						this.decHsCodeMessage(this.txtrequestCertType.hscode, hsmodelStr);
						// }
					}, 200)
					// setTimeout(() => {
					// 	//默认初始化的光标
					// 	if (this.$refs.refhsModel) {
					// 		this.$refs.refhsModel.$refs.select.focus()
					// 	}
					// 	//进口报关单需要默认出口享惠为(3)，
					// 	if (this.imSignShowList && (this.$refs.refhsModelXHQK.val == "" || typeof (this.$refs.refhsModelXHQK.val) === "undefined")) {
					// 		this.$refs.refhsModelXHQK.val = "3"
					// 	}
					// }, 500)
				});

			},
		},
		imSignShowList: {
			handler(val) {
				this.imSignShowModal = val
			},
		},
		hintDialog: {
			handler(val) {
				this.decHsmodelDialog1 = val
			},
		},

		decHsmodelDialog1: {
			handler(val) {
				this.$emit("upadte:hintDialog", val)
			}
		},
		showPage: {
			handler(val) {
				this.pageType = val
			},
		},
		pageType: {
			handler(val) {
				this.$emit("upadte:showPage", val)
			},
		},
		'txtrequestCertType.hscode'(newValue, oldValue) {
			this.hscodeModal = oldValue

		},
		txtrequestCertType: {
			handler(val) {
				this.hscodeModal = ""
				this.txtrequestCertType1 = val
			},
		},
		txtrequestCertType1: {
			handler(val) {
				this.$emit("upadte:txtrequestCertType", val)
			}
		},

	},


	methods: {
		handleFocus(event){
			event.currentTarget.select();
		},
		async decHsCodeMessage(hscode, hsModel) {
			console.log('[[[[[[[[[[[[[[[[[[[')
			console.log(hsModel)
			let hscodeLists = []
			hscodeLists.push(hscode)
			const auditRess = await decHsCode(hscodeLists.join(',')).catch(reason => this.$message.error(reason));
			//清空
			this.dataSource = []
			this.decHsauditRessList = []
			//申报要素
			let decHsmodelList = []
			console.log(auditRess)
			if (auditRess.success && auditRess.result.length > 0) {
				this.dataSource = auditRess.result
				//存在申报要素映射上
				if (hsModel) {
					for (var i = 0; i < hsModel.length; i++) {
						for (let da of this.dataSource) {
							if (da.no == i) {
								da.hsModel = hsModel[i]
							}
						}

					}
					console.log('aaaaaaaaaa')
					console.log(this.dataSource)
					this.handleOkMethod(false)
				}
				this.dataSourceRequired = []
				//循环得出集合最后三位
				for (let i = 0; i < this.dataSource.length; i++) {
					if (i <= this.dataSource.length - 4) {
						this.dataSourceRequired.push(this.dataSource[i].no)
					}
				}
			}
		},
		/**
		 * 取消修改
		 */
		handleCancel() {
			this.showModal = !this.showModal
			this.smartFillText = ''
			setTimeout(() => {
				this.$emit('keyFromPromise', "decHsmodel")
			}, 20)
		},
		/**
		 * 确认修改
		 */
		handleOk() {
			//标识 申报要素的确定，用于判断商品名称之后的回车。
			//否则会引起商品名称回车错乱，选完商品名称回车一直到申报要素再回车会继续弹出选择历史商品列表
			this.decHsmodelOk = true,
				this.handleOkMethod(true)
			this.hscodeModal = ""
			this.smartFillText = ''
			setTimeout(() => {
				this.$emit('keyFromPromise', "decHsmodel")
			}, 80)
			// this.showPage.decHsmodelPage = true
		},

		/**
		 * 列选择变更
		 * @param selectedRowKeys 选中列的rowKey
		 */
		handleTableSelectionChange(selectedRowKeys) {

		},
		handleOkMethod(e) {
			let hsmodelStr = ""
			hsmodelStr = this.hsmodeGroup(hsmodelStr, e)
			//true 是确认保存
			if (e) {
				// if (this.hsModelStr.length >= 255) {
				if (this.onCheckStrByte(this.hsModelStr) > 255) {
					this.$message.error("字节以超长无法保存!")
				} else {
					//末尾|的处理没值则不用加|
					this.hsmodeSubstr(hsmodelStr, true)
					this.showModal = !this.showModal
				}
			} else {
				//末尾|的处理没值则不用加|
				this.hsmodeSubstr(hsmodelStr, false)
				// if (this.hsModelStr.length >= 255) {
				if (this.onCheckStrByte(this.hsModelStr) > 255) {
					this.$message.error("字节以超长无法保存!")
				}
				// this.hsModelZj = "(" + hsmodelStr.length + "/255字节)"
				let strLength = this.onCheckStrByte(hsmodelStr);
				this.hsModelZj = "(" + strLength + "/255字节)"
			}
		},
		inputChange(e) {
			this.handleOkMethod(false)
		},
		//末尾|的处理没值则不用加|
		hsmodeSubstr(hsmodelStr, type) {

			let hsmodelList = hsmodelStr.split("|")

			for (let i = hsmodelList.length - 1; i >= 0; i--) {
				if (hsmodelList[i] == '') {
					hsmodelStr = hsmodelStr.substring(0, hsmodelStr.length - 1)
				} else {
					break
				}
			}
			if (type) {
				this.val = hsmodelStr
			} else {
				this.hsModelStr = hsmodelStr
			}
		},
		//组合hsmodel字符串
		hsmodeGroup(hsmodelStr, e) {
			console.log('@@@',this.dataSource)
			for (let i = 0; i < this.dataSource.length; i++) {
				if (i < this.dataSource.length - 1) {
					if (this.dataSourceRequired.indexOf(this.dataSource[i].no) > -1) {
						if (!!this.dataSource[i].hsModel) {
							hsmodelStr = hsmodelStr + this.dataSource[i].hsModel + "|"
						}else {
							hsmodelStr = hsmodelStr  + "|"
						}
						// else {
						//     if (e) {
						//         this.$message.error(this.dataSource[i].hsRequire + "不能为空")
						//         return
						//     }
						// }
					} else {
						let hsmodels = this.dataSource[i].hsModel == undefined ? "" : this.dataSource[i].hsModel
						hsmodelStr = hsmodelStr + hsmodels + "|"
					}
				} else {
					let hsmodels = this.dataSource[i].hsModel == undefined ? "" : this.dataSource[i].hsModel
					hsmodelStr = hsmodelStr + hsmodels
				}

			}
			let hsmodelNum = hsmodelStr.split("|").length
			for (let i = 0; i < this.decHsauditRessList.length - hsmodelNum; i++) {
				hsmodelStr = hsmodelStr + "|"
			}
			return hsmodelStr
		},
		onCheckStrByte(str) {
			if (str) {
				console.log("onCheckStrByte==>", str.replace(/[\u4e00-\u9fa5]/g, '**'))
				return str.replace(/[\u4e00-\u9fa5]/g, '**').length;
			}
		},
		//智能填写功能相关
		async handleSmartFill() {
			if (!this.smartFillText) {
				this.$message.warning('请输入商品描述文本');
				return;
			}
			this.smartFillLoading = true;
			await getAction(this.url.smartFill, {
				hscode: this.txtrequestCertType.hscode,
				text: this.smartFillText
			}).then(res => {
				if (res && res.result) {
					const result = res.result.result;
					console.log('result：', result);

					let hsmodelStr = ''
					let hsmodelDec = result;
					console.log('1111111')
					console.log(hsmodelDec)
					if (result && (this.hscodeModal == this.txtrequestCertType.hscode || this.hscodeModal == "")) {
						//移到模态框这再追加
						//处理申报要素字符串
						let hsModelList = result.toString().split("|")
						if (isNaN(hsModelList[0]) && !isNaN(hsModelList[1])) { //第一位不为数字，但第二位为数字
							let newHsmodel = "|" + result
							hsmodelDec = newhsmodel;
						}
						if (isNaN(hsModelList[0]) && isNaN(hsModelList[1])) {  //第一位不为数字，第二位也不为数字
							let newhsmodel = "||" + result
							hsmodelDec = newhsmodel;
						}
						hsmodelStr = hsmodelDec.toString().split("|");
					}
					setTimeout(() => {
						// if(hsmodelStr){
						this.hsModelStr = hsmodelDec
						this.decHsCodeMessage(this.txtrequestCertType.hscode, hsmodelStr);
						// }
					}, 200)

					this.$message.success('智能填写成功');
				} else {
					this.$message.error('智能填写失败：' + (res.message || '未知错误'));
				}
			}).catch(error => {
				console.error('智能填写接口调用失败', error);
				this.$message.error('智能填写接口调用失败：' + (error.message || '未知错误'));
			}).finally(() => {
				this.smartFillLoading = false;
			});
		}
	},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';

.inlineTable {
	width: 100%;
	margin-top: 10px;
	table-layout: fixed;
	border: solid #E8E8E8;
	border-width: 1px 1px 1px 1px;

}

.inlineTd {
	border: solid #E8E8E8;
	border-width: 1px 1px 1px 1px;

}

/deep/ .ant-table-tbody>tr>td {
	padding: 0px;
}

/deep/ .ant-table-thead>tr>th {
	padding: 8px;
}

/* 智能填写功能相关样式 */
.smart-fill-container {
	margin-bottom: 16px;
	background: #f8f8f8;
	border-radius: 4px;
	padding: 12px;
}

.smart-fill-input {
	display: flex;
	align-items: flex-start;
}

.smart-fill-input .ant-input {
	flex: 1;
	margin-right: 12px;
	border-color: #d9d9d9;
}

.smart-fill-input .ant-btn {
	margin-top: 8px;
	height: 40px;
}
</style>