package org.jeecg.modules.business.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 核注清单表头
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
@TableName("nems_invt_head")
public class NemsInvtHead implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 核注单流水号
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 委托流水号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyNumber;

    /**
     * 清单编号 （返填 - 海关审批通过后系统自动返填）
     */
    @Excel(name = "清单编号")
    private String bondInvtNo;

    /**
     * 分票序号
     */
    @Excel(name = "分票序号")
    private String partId;

    /**
     * 清单预录入统一编号 （返填 - 第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号）
     */
    @Excel(name = "清单预录入统一编号")
    private String seqNo;

    /**
     * 变更次数
     */
    private Integer chgTmsCnt;

    /**
     * 备案编号
     */
    @Excel(name = "备案编号")
    private String putrecNo;

    /**
     * 企业内部清单编号 （由企业自行编写）
     */
    @Excel(name = "企业内部清单编号")
    private String etpsInnerInvtNo;

    /**
     * 经营企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "经营企业社会信用代码")
    private String bizopEtpsSccd;

    /**
     * 经营企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "经营企业编号")
    private String bizopEtpsno;

    /**
     * 经营企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "经营企业名称")
    private String bizopEtpsNm;

    /**
     * 收货企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "收货企业编号")
    private String rcvgdEtpsno;

    /**
     * 收发货企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "收发货企业社会信用代码")
    private String rvsngdEtpsSccd;

    /**
     * 收货企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    @Excel(name = "收货企业名称")
    private String rcvgdEtpsNm;

    /**
     * 申报企业社会信用代码
     */
    @Excel(name = "申报企业社会信用代码")
    private String dclEtpsSccd;

    /**
     * 申报企业编号
     */
    @Excel(name = "申报企业编号")
    private String dclEtpsno;

    /**
     * 申报企业名称
     */
    @Excel(name = "申报企业名称")
    private String dclEtpsNm;

    /**
     * 清单申报时间 （返填 - 系统自动反填）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "清单申报时间",format = "yyyy-MM-dd HH:mm:ss", width = 15)
    private Date invtDclTime;

    /**
     * 报关单申报日期 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "报关单申报日期",format = "yyyy-MM-dd HH:mm:ss", width = 15)
    private Date entryDclTime;

    /**
     * 对应报关单编号 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
     */
    @Excel(name = "对应报关单编号")
    private String entryNo;

    /**
     * 关联清单编号 （结转类专用，检控要求复杂，见需求文档）
     */
    @Excel(name = "关联清单编号")
    private String rltinvtNo;

    /**
     * 关联备案编号 （结转类专用）
     */
    @Excel(name = "关联备案编号")
    private String rltputrecNo;

    /**
     * 关联报关单编号 （可录入或者系统自动生成报关单后返填二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    @Excel(name = "关联报关单编号")
    private String rltEntryNo;

    /**
     * 关联报关单消费使用单位社会信用代码
     */
    @Excel(name = "关联报关单经营企业社会信用代码")
    private String rltEntryBizopEtpsSccd;

    /**
     * 关联报关单消费使用单位编号
     */
    @Excel(name = "关联报关单经营企业编号")
    private String rltEntryBizopEtpsno;

    /**
     * 关联报关单消费使用单位名称
     */
    @Excel(name = "关联报关单经营企业名称")
    private String rltEntryBizopEtpsNm;

    /**
     * 关联报关单境内收发货单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    @Excel(name = "关联报关单收发货单位社会统一信用代码")
    private String rltEntryRvsngdEtpsSccd;

    /**
     * 关联报关单境内收发货单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填	报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。）
     */
    @Excel(name = "关联报关单海关收发货单位编码")
    private String rltEntryRcvgdEtpsno;

    /**
     * 关联报关单境内收发货单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    @Excel(name = "关联报关单收发货单位名称")
    private String rltEntryRcvgdEtpsNm;

    /**
     * 关联报关单申报单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    @Excel(name = "关联报关单申报单位社会统一信用代码")
    private String rltEntryDclEtpsSccd;

    /**
     * 关联报关单海关申报单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    @Excel(name = "关联报关单海关申报单位编码")
    private String rltEntryDclEtpsno;

    /**
     * 关联报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    @Excel(name = "关联报关单申报单位名称")
    private String rltEntryDclEtpsNm;

    /**
     * 进出口口岸代码
     */
    @Excel(name = "进出口口岸代码")
    private String impexpPortcd;

    /**
     * 申报地关区代码
     */
    @Excel(name = "申报地关区代码")
    private String dclplcCuscd;

    /**
     * 进出口标记代码 （返填 - I：进口，E：出口）
     */
    @Excel(name = "进出口标记代码",replace = {"进口_I","出口_E"})
    private String impexpMarkcd;

    /**
     * 料件成品标记代码 （I：料件，E：成品）
     */
    @Excel(name = "料件成品标记代码",replace = {"料件_I","成品_E"})
    private String mtpckEndprdMarkcd;

    /**
     * 监管方式代码
     */
    @Excel(name = "监管方式代码")
    private String supvModecd;

    /**
     * 运输方式代码
     */
    @Excel(name = "运输方式代码")
    private String trspModecd;

    /**
     * 是否报关标志 （1.报关 2.非报关）
     */
    @Excel(name = "是否报关标志",replace = {"报关_1","非报关_2"})
    private String dclcusFlag;

    /**
     * 报关类型代码 （1.关联报关 2.对应报关；当报关标志为“1.报关”时，企业可选择“关联报关单”/“对应报关单”；当报关标志填写为“2.非报关”时，报关标志填写为“2.非报关”该项不可填。）
     */
    @Excel(name = "报关类型代码",replace = {"关联报关_1","对应报关_2"})
    private String dclcusTypecd;

    /**
     * 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
     */
    @Excel(name = "核扣标记代码",replace = {"未核扣_0","预核扣_1","已核扣_2"})
    private String vrfdedMarkcd;

    /**
     * 核扣类型 （0回执核扣 ；1手动核扣）
     */

    private String vrfdedType;

    /**
     * 清单进出卡口状态代码 （系统自动反填。未出卡口，已出卡口。需求不明确，暂留）
     */
    @Excel(name = "清单进出卡口状态代码")
    private String invtIochkptStucd;

    /**
     * 预核扣时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预核扣时间",format = "yyyy-MM-dd HH:mm:ss")
    private Date prevdTime;

    /**
     * 正式核扣时间 （返填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "正式核扣时间",format = "yyyy-MM-dd HH:mm:ss")
    private Date formalVrfdedTime;

    /**
     * 申请表编号
     */
    @Excel(name = "申请表编号")
    private String applyNo;

    /**
     * 流转类型 （非流转类不填写，流转类填写：A：加工贸易深加工结转、B：加工贸易余料结转、C：不作价设备结转、D：区间深加工结转、E：区间料件结转）
     */
    @Excel(name = "流转类型")
    private String listType;

    /**
     * 录入企业编号 （保存首次暂存时IC卡的企业信息）
     */
    @Excel(name = "录入企业编号")
    private String inputCode;

    /**
     * 录入企业社会信用代码 （返填 - 保存首次暂存时IC卡的企业信息）
     */
    @Excel(name = "录入企业社会信用代码")
    private String inputCreditCode;

    /**
     * 录入单位名称 （保存首次暂存时IC卡的企业信息）
     */
    @Excel(name = "录入单位名称")
    private String inputName;

    /**
     * 申报人IC卡号 （企业端专用）
     */
    @Excel(name = "申报人IC卡号")
    private String icCardNo;

    /**
     * 录入日期 （企业端专用）
     */
    @Excel(name = "录入日期",format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date inputTime;

    /**
     * 清单状态 （系统自动反填。1-已申报、C-退单、改单、删单、审核通过）
     */
    @Excel(name = "清单状态",replace = {"已申报_1","退单_C"})
    private String listStat;

    /**
     * 对应报关单申报单位社会统一信用代码
     */
    @Excel(name = "对应报关单申报单位社会统一信用代码")
    private String corrEntryDclEtpsSccd;

    /**
     * 对应报关单申报单位代码 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
     */
    @Excel(name = "对应报关单申报单位代码")
    private String corrEntryDclEtpsno;

    /**
     * 对应报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
     */
    @Excel(name = "对应报关单申报单位名称")
    private String corrEntryDclEtpsNm;

    /**
     * 报关单类型 （1-进口报关单	2-出口报关单	3-进境备案清单	4-出境备案清单	5-进境两单一审备案清单	6-出境两单一审备案清单	7-进境备案清单（简化）	8-出境备案清单（简化）	9-转关提前进口报关单	A-转关提前出口报关单	B-转关提前进境备案清单	C-转关提前出境备案清单	D-转关提前进境备案清单（简化）	E-转关提前出境备案清单（简化）	F-出口二次转关单）
     */
    @Excel(name = "报关单类型",replace = {"进口报关单_1","出口报关单_2","进境备案清单_3","出境备案清单_4"})
    private String decType;

    /**
     * 入库时间 （返填）
     */
    @Excel(name = "入库时间",format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /**
     * 起运运抵国别代码
     */
    @Excel(name = "起运运抵国别代码")
    private String stshipTrsarvNatcd;

    /**
     * 清单类型 （(SAS项目新增)	标识清单类别，0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：异常补录，默认为0：普通清单）
     */
    @Excel(name = "清单类型",replace = {"普通清单_0","集报清单_1","先入区后报关_3","简单加工_4","保税展示交易_5","区内流转_6","异常补录_7"})
    private String invtType;

    /**
     * 报关状态 （(SAS项目新增)	标明对应（关联）报关单放行状态，目前只区分 0：未放行，1：已放行。该字段用于区域或物流账册的清单，该类型清单满足两个条件才能核扣：报关单被放行+货物全部过卡）
     */
    @Excel(name = "报关状态",replace = {"未放行_0","已放行_1"})
    private String entryStucd;

    /**
     * 核放单生成标志代码 （(SAS项目新增)	1：未生成、2：部分生成、3：已生成，核放单生成时系统返填）
     */
    @Excel(name = "核放单生成标志代码",replace = {"未生成_1","部分生成_2","已生成_3"})
    private String passportUsedTypeCd;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String rmk;

    /**
     * 创建人员
     */
    @Excel(name = "创建人员")
    private String createPerson;

    /**
     * 创建日期
     */
    @Excel(name = "创建日期",format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 0-暂存、1-申报成功、4成功发送海关、5-海关接收成功、6-海关接收失败、B-海关终审通过、C-海关退单、E-删除、N-待导入其他报文、P-预审批通过
     */
    private String status;

    /**
     * 是否审核
     */
    @Excel(name = "是否审核", replace = {"是_true", "否_false"})
    private Boolean audited;

    /**
     * 分运单号
     */
    @Excel(name = "分运单号")
    private String hawb;

    /**
     * 是否核对
     */
    @Excel(name = "是否核对", replace = {"是_true", "否_false"})
    private Boolean checked;

    /**
     * 核对时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "核对时间",format = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;

    /**
     * 申报日期 (报关：报关单申报，非报关：核注单申报日期)
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Excel(name = "申报日期",format = "yyyy-MM-dd HH:mm:ss")
    private Date declarationDate;

    /**
     * 货物类别
     */
    @Excel(name = "货物列表")
    private String goodsType;

    /**
     * 剩余数量
     */
    private BigDecimal balanceQty;

    /**
     * 子系统ID(95 :加工贸易账册系统B1: 加工贸易手册系统B2 :加工贸易担保管理系统B3: 保税货物流转系统二期Z7: 海关特殊监管区域管理系统Z8 :保税物流管理系统)
     */
    private String sysId;

    /**
     * 模板标识
     */
    private String mouldId;

    /**
     * 是否系统生成报关单,报文使用: 1生成,2不生成
     */
    @Excel(name = "是否生成报关单",replace = {"生成_1","不生成_2"})
    private String genDecFlag;

    /**
     * 是否为自己的单子（true;自己，false:他人）
     */
    private Boolean nonBusiness;

    /**
     * 是否发送
     */
    private Boolean send;

    /**
     * 申报类型代码(1-备案、2-变更、3-作废）
     */
    @Excel(name = "申报类型代码",replace = {"备案_1","作废_3"})
    private String dclTypecd;

    /**
     * 占用信息id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emsFlowsId;

    /**
     * 业务流水
     */
    private String applyId;

    /**
     * 创建人租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tenantId;

    /**
     * 申请人租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dclTenantId;

    /**
     * 计费重量
     */
    private BigDecimal chargedWeight;

    /**
     * 更新人
     */
    @Excel(name = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间",format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 空客导入报关单统一编号
     */
    private String importSeqNo;

    /**
     * 权限字段
     */
    private String rbacIds;

    /**
     * 录入企业ID 创建人的企业ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long inputId;

    /**
     * 是否推核注单
     */
    private String decPushStatus;

    /**
     * 不再推送核注单（1否 1是）
     */
    private String doNotPush;

    /**
     * 出入库单表头流水号
     */
    private String stockHeadId;

    /**
     * 是否生成核放单
     */
    private Boolean createPassPort;

    /**
     * 已核扣日期(已核扣时添加)
     */
    @Excel(name = "已核扣日期",format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date warehousingDate;

    /**
     * 飞机注册号
     */
    private String aircraftRegistrationNumber;

    /**
     * 飞机类别
     */
    private String aircraftType;

    /**
     * 初复审状态（0未审核、1已初审/未复审、2已复核）
     */
    private String initialReviewStatus;

    /**
     * 初审人
     */
    private String firstTrialBy;

    /**
     * 初审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date firstTrialDate;
    /**
     * 初审意见
     */
    private String firstOpinion;

    /**
     * 复审人
     */
    private String reviewBy;

    /**
     * 复审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date reviewDate;
    /**
     * 复审意见
     */
    private String reviewOpinion;
    /**
     * 关联易通关的HeadId
     */
    private String flyId;

    /**
     * 是否确认收货
     */
    private Boolean isReceipt;

    /**
     * 收货时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date receiptDate;

    @TableField(exist = false)
    private List<NemsInvtList> nemsInvtLists;

    @TableField(exist = false)
    private String hasSC;
    @TableField(exist = false)
    private String storageNos;

    /** 申报数量 */
    @TableField(exist = false)
    private BigDecimal dclQty;

    @TableField(exist = false)
    private Boolean fromStock;
    /** 进出口标识 */
    @TableField(exist = false)
    private  String imSign;
    @TableField(exist = false)
    private String vid;
    /**
     * 以下字段为报关单字段附加字段
     */
    /** 提运单号 */
    private String billCode ;
    /** 合同号 */
    private String contract ;
    /** 征免性质 */
    private String taxTypeCode ;
    /** 成交方式 */
    private String termsTypeCode ;
    /** 毛重 */
    private BigDecimal grossWeight ;
    /** 净重 */
    private String netWeight ;
    /** 包装种类 */
    private String packsKinds ;
    /** 件数 */
    private String packs ;
    /** 许可证号 */
    private String licenceNumber ;
    /** 运费币制 */
    private String shipCurrencyCode ;
    /** 运费代码 */
    private String shipFeeCode ;
    /** 运费值 */
    private BigDecimal shipFee ;
    /** 保费币制 */
    private String insuranceCurr ;
    /** 保险费标记 */
    private String insuranceCode ;
    /** 保险费/率 */
    private BigDecimal insurance ;
    /** 杂费币制 */
    private String otherCurr ;
    /** 杂费标志 */
    private String extrasCode ;
    /** 杂费/率 */
    private String extras ;
    /** 贸易国 */
    private String tradeCountry ;
    /** 启运港代码 */
    private String despPortCode ;
    /** 经停港/指运港 */
    private String desPort ;
    /** 货物存放地点 */
    private String goodsPlace ;
    /** 运输工具名称 */
    private String shipName ;
    /** 入境口岸/离境口岸 */
    private String entyPortCode ;
    /** 境外发货人代码 */
    private String overseasConsignorCode ;
    /** 境外发货人名称（外文） */
    private String overseasConsignorEname ;
    /** 境外收货人代码 */
    private String overseasConsigneeCode ;
    /** 境外收货人名称（外文） */
    /**
     * 以上字段为报关单字段附加字段
     */
    private String overseasConsigneeEname ;

}
