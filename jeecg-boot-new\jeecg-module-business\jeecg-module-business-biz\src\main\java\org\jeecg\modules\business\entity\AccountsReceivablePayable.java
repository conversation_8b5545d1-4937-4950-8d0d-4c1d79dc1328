package org.jeecg.modules.business.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("accounts_receivable_payable")
public class AccountsReceivablePayable implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @TableField("tenant_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tenantId;

    /**
     * 登记类型: 1-按业务, 2-按报关单, 3-按集装箱
     */
    @TableField("registration_type")
    private Integer registrationType;

    /**
     * 状态，暂定0已登记
     */
    private String status;

    /**
     * 进出口标识
     */
    private String ieFlag;

    /**
     * 单号/报关单号/箱号
     */
    @TableField("document_number")
    private String documentNumber;

    /**
     * 关联单据ID（关联其他业务单据的主键）
     */
    @TableField("related_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long relatedId;
    /**
     * 费用id
     */
    @TableField("fee_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feeId;

    /**
     * 费用名称
     */
    @TableField("fee_name")
    private String feeName;

    /**
     * 费用值（精确到小数点后2位）
     */
    @TableField("fee_amount")
    private BigDecimal feeAmount;

    /**
     * 币制（如：CNY/USD/EUR）
     */
    @TableField("currency")
    private String currency;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("creator")
    private String creator;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("updater")
    private String updater;
    private String type;

    /**
     * 结算单位id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long settlementId;
    /**
     * 结算单位名称
     */
    private String settlementName;

    @TableField(exist = false)
    private String startCreateTimeDate;

    @TableField(exist = false)
    private String lastCreateTimeDate;
    /**附件列表*/
    @ApiModelProperty(value = "附件列表")
    @TableField(exist = false)
    private List<AttachmentsInfo> attachmentList;

}
