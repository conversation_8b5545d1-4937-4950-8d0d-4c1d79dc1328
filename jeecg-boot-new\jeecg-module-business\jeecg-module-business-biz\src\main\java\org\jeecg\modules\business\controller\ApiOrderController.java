package org.jeecg.modules.business.controller;

import icu.develop.apiwrap.WrapRequest;
import icu.develop.apiwrap.annotation.ApiWrap;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.business.entity.DefaultWrapData;
import org.jeecg.modules.business.entity.Order;
import org.jeecg.modules.business.entity.OrderInfo;
import org.jeecg.modules.business.entity.dto.ApiCustomerDTO;
import org.jeecg.modules.business.entity.dto.AiMakerRequest;
import org.jeecg.modules.business.interceptor.AuthKit;
import org.jeecg.modules.business.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * 对外接口API
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/6/13 下午3:20
 */
@RestController
@RequestMapping("/open-api")
@Slf4j
public class ApiOrderController extends JeecgController<OrderInfo, IOrderInfoService> {
    @Autowired
    private IApiService apiService;
    @Autowired
    private IAiService aiService;
    @Autowired
    private IStorageRepairOrderService storageRepairOrderService;
    private static final String CACHED_REQUEST_BODY = "cachedRequestBody";
    @Autowired
    private IErpHscodesService erpHscodesService;

    /**
     * 第三方注册
     *
     * @param apiCustomerDTO
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/3 10:00
     */
    @PostMapping("/thirdParty/v1/register")
    public Result<?> register(@RequestBody ApiCustomerDTO apiCustomerDTO) {
        return apiService.ThirdPartyRegister(apiCustomerDTO);
    }

    /**
     * 第三方企业修改
     *
     * @param apiCustomerDTO
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/6 10:22
     */
    @PostMapping("/thirdParty/v1/update")
    public Result<?> update(@RequestBody ApiCustomerDTO apiCustomerDTO) {
        return apiService.ThirdPartyUpdate(apiCustomerDTO);
    }

    /**
     * 第三方获取Token
     *
     * @param creditCode
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/1 10:00
     */
    @GetMapping("/thirdParty/v1/getToken")
    public Result<?> login(@RequestParam("creditCode") String creditCode) {
        return apiService.ThirdPartyLogin(creditCode);
    }

    /**
     * 第三方Token登录
     *
     * @param token
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/5 8:20
     */
    @GetMapping("/thirdParty/v1/tokenLogin")
    public Result<?> tokenLogin(@RequestParam("token") String token) {
        return apiService.ThirdPartyTokenLogin(token);
    }

    /**
     * 导入采购订单
     *
     * @param order
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/13 下午3:32
     */
    @PostMapping("/order/v1/importOrders")
    public Result<?> importOrders(@RequestBody Order order, HttpServletRequest request) {
        return apiService.importOrders(order, request);
    }

    /**
     * 导入商品库数据
     *
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/13 下午3:32
     */
    @ApiWrap
    @PostMapping("/order/v1/importGoods")
    public Result<?> importGoods(@RequestBody WrapRequest<DefaultWrapData> request) {
        return apiService.importGoods(request);
    }

    /**
     * 获取保税维修数据
     *
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/13 下午3:32
     */
    @ApiWrap
    @PostMapping("/repair/v1/getBondedMaintenance")
    public Result<?> getBondedMaintenance(@RequestBody WrapRequest<DefaultWrapData> request) {
//        DefaultWrapData data = request.getData();
//        if (isEmpty(data) || isBlank(data.getData())) {
//            return Result.error("接口参数不能为空");
//        }
//        String jsonObject = data.getData();
//        String startDate = JSONObject.parseObject(jsonObject).getString("startDate");
//        String endDate = isNotBlank(JSONObject.parseObject(jsonObject).getString("endDate")) ? JSONObject.parseObject(jsonObject).getString("endDate") : DateUtil.today();
        // 判断开始日期不能大于结束日期
//        if (isBlank(startDate)) {
//            return Result.error("开始日期不能为空！");
//        }
//        if (DateUtil.compare(DateUtil.parseDate(startDate), DateUtil.parseDate(endDate)) > 0) {
//            return Result.error("开始日期不得大于结束日期！");
//        }
//        log.info("参数：开始日期：{} 结束日期：{}", startDate, endDate);
        Result<?> result = storageRepairOrderService.getBondedMaintenance("56");
        log.info("返回结果：{}", result);
        return result;
    }

    /**
     * 获取保税维修数据 - 森锋
     *
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/14 下午1:25
     */
    @ApiWrap
    @PostMapping("/repair/v1/getBondedMaintenanceSF")
    public Result<?> getBondedMaintenanceSF(@RequestBody WrapRequest<DefaultWrapData> request) {
//        DefaultWrapData data = request.getData();
//        if (isEmpty(data) || isBlank(data.getData())) {
//            return Result.error("接口参数不能为空");
//        }
//        String jsonObject = data.getData();
//        String startDate = JSONObject.parseObject(jsonObject).getString("startDate");
//        String endDate = isNotBlank(JSONObject.parseObject(jsonObject).getString("endDate")) ? JSONObject.parseObject(jsonObject).getString("endDate") : DateUtil.today();
        // 判断开始日期不能大于结束日期
//        if (isBlank(startDate)) {
//            return Result.error("开始日期不能为空！");
//        }
//        if (DateUtil.compare(DateUtil.parseDate(startDate), DateUtil.parseDate(endDate)) > 0) {
//            return Result.error("开始日期不得大于结束日期！");
//        }
//        log.info("参数：开始日期：{} 结束日期：{}", startDate, endDate);
        Result<?> result = storageRepairOrderService.getBondedMaintenance("59");
        log.info("返回结果：{}", result);
        return result;
    }

    /**
     * 税则接收接口
     * http://***************:5200/jgsoft/open-api/hscode/v1/basicTariffs
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/3/25 14:21
     */
    @PostMapping("/hscode/v1/basicTariffs")
    public Result<?> basicTariffs(HttpServletRequest request) {
        Map<String, Object> requestData = (Map<String, Object>) request.getAttribute(CACHED_REQUEST_BODY);
        log.info("consumer params:{}", requestData);
//        return erpHscodesService.getBasicTariffs(requestData);
        return apiService.basicTariffs(requestData);
    }

    /**
     * 出口船舶计划推送
     * http://***************:5200/jgsoft/open-api/receive/v1/shipExportPlan
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/1 10:05
     */
    @PostMapping("/receive/v1/shipExportPlan")
    public Result<?> shipExportPlan(HttpServletRequest request) {
        Map<String, Object> requestData = (Map<String, Object>) request.getAttribute(CACHED_REQUEST_BODY);
        log.info("shipExportPlan params:{}", requestData);
        return apiService.shipExportPlan(requestData);
    }

    /**
     * 出口箱货节点推送
     * http://***************:5200/jgsoft/open-api/receive/v1/containerCargoNode
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/1 10:05
     */
    @PostMapping("/receive/v1/containerCargoNode")
    public Result<?> containerCargoNode(HttpServletRequest request) {
        Map<String, Object> requestData = (Map<String, Object>) request.getAttribute(CACHED_REQUEST_BODY);
        log.info("containerCargoNode params:{}", requestData);
        return apiService.containerCargoNode(requestData);
    }

    /**
     * 进口船舶计划推送
     * http://***************:5200/jgsoft/open-api/receive/v1/shipExportPlanI
     * @param request
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/1 10:05
     */
    @PostMapping("/receive/v1/shipExportPlanI")
    public Result<?> shipExportPlanI(HttpServletRequest request) {
        Map<String, Object> requestData = (Map<String, Object>) request.getAttribute(CACHED_REQUEST_BODY);
        log.info("shipExportPlanI params:{}", requestData);
        return apiService.shipExportPlanI(requestData);
    }

    /**
     * AI制单 -- 对外
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/6/9 15:58
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @PostMapping("/ai/v1/aiMakerOut")
    public Result<?> aiMaker(@RequestParam(value = "clientId") String clientId,
                             @RequestParam(value = "files") MultipartFile[] files,
                             @RequestParam(value = "ieFlag", defaultValue = "E") String ieFlag,
                             @RequestParam(value = "customerName", required = false) String customerName,
                             @RequestParam(value = "declarePlace", required = false) String declarePlace,
                             @RequestParam(value = "outPortCode", required = false) String outPortCode,
                             @RequestParam(value = "shipTypeCode", required = false) String shipTypeCode,
                             @RequestParam(value = "decId", required = false) String decId,
                             @RequestParam(value = "modelProvider", defaultValue = "doubao") String modelProvider) throws IOException {
        return aiService.aiMakerOut(files, ieFlag, customerName, declarePlace, outPortCode, shipTypeCode, modelProvider, clientId, decId);
    }

}
