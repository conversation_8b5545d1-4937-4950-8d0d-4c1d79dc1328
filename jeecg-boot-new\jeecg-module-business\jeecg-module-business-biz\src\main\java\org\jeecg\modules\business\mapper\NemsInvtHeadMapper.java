package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.business.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.dto.MonthlyListEntity;
import org.jeecg.modules.business.entity.dto.NemsInvtHeadDTO;
import org.jeecg.modules.business.entity.dto.ReportManagerDTO;
import org.jeecg.modules.business.entity.paramVo.NemsListByWriteOffBalanceVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 核注清单表头 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface NemsInvtHeadMapper extends BaseMapper<NemsInvtHead> {

    IPage<NemsInvtHead> queryPageList(Page<NemsInvtHead> page, NemsInvtHeadDTO nemsInvtHeadDTO);
    void insertBatchSomeColumn(@Param("addHeads") List<NemsInvtHead> addHeads);
    void insertBatchLists(@Param("addLists") List<NemsInvtList> addLists);

    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtHead> listBySeqNos(@Param("seqNoList") List<String> seqNoList);

    IPage<NemsInvtHead> listDeclarationRecord(String putrecNo, String putrecSeqno, String mtpckEndprdMarkcd, String vrfdedMarkcd, Page<Object> objectPage);

    IPage<DecHead> listDecByEmsNo(Page<DecHead> page, String emsNo, String putrecSeqno, String impexpMarkcd);

    IPage<NemsInvtHead> listInvtByEmsNo(Page<NemsInvtHead> page, String emsNo, String putrecSeqno, String impexpMarkcd);

    /**
     * AutoPoi字典翻译
     *
     * @param dictCode
     * @return java.lang.String[]
     * <AUTHOR>
     * @date 2021/2/9 10:10
     **/
    @InterceptorIgnore(tenantLine = "true")
    String[] queryDict(String dictCode);
    @InterceptorIgnore(tenantLine = "true")
    String[] queryDict1(String dicTable, String dicText, String dicCode);

    List<ReportManagerDTO> listMonthlyReport(ReportManagerDTO reportManagerDTO);
    Long listMonthlyListEntitySize(ReportManagerDTO reportManagerDTO);

    List<MonthlyListEntity> listMonthlyListEntity(String startInvtDclMonth,
                                                  String endInvtDclMonth, String flag);

    //统计核销平衡的数据
    List<NemsListByWriteOffBalanceVO> listByWriteOffBalance(String startDate,String lastDate,String tenantId);

    List<PtsEmsAimg> listAimgByTenantId(String tenantId);
    List<PtsEmsAexg> listAexgByTenantId(String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtHead> listNemsInvtHead(String seqNo, Long tenantId);
    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtHead> selectUnClosed(String tenantId, String startTime, String endTime);
}
