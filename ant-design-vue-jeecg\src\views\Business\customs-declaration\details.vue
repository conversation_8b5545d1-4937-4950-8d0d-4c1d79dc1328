<template>
	<div>
		<a-card :bodyStyle="{ padding: '0px 10px 0px 10px' }">
			<!--id最大已经97-->
			<!--顶部信息-->
			<template>
				<!--头部的流水号显示-->
				<div class="topSpan">
					<div class="topSpanDiv">
						<img alt='' class='logo' src='@/assets/title.png'>
						<b style="font-size: 15px" v-html="decTitle"></b>&emsp;&emsp;
						&emsp;&emsp;
						<!--						报关单类型-->
						<span class="dclTrnRelFlagClass">
							<a-tag v-show="!!record.dclTrnRelFlagName" color="cyan" v-html="record.dclTrnRelFlagName"></a-tag>
						</span>
						<!--						两步申报相关的四个tag-->
						<a-tag color="cyan" v-show="dectypeSzNum != 3 && $route.query.twoStep">
							<span class="dclTrnRelFlagSpen">涉证</span>
							<span class="dclTrnRelFlagSpen"> <a-icon v-show="dectypeSzNum == 1" type="check-circle" /><a-icon
									v-show="dectypeSzNum == 0" type="close-circle" />
							</span>
						</a-tag>
						<a-tag color="cyan" v-show="dectypeSzNum != 3 && $route.query.twoStep">
							<span class="dclTrnRelFlagSpen" v-show="dectypeSzNum != 3">涉检</span><span
								class="dclTrnRelFlagSpen"><a-icon v-show="dectypeSjNum == 1" class="dectypeSjSs"
									type="check-circle" /><a-icon v-show="dectypeSjNum == 0" type="close-circle" />
							</span>&nbsp;&nbsp;
						</a-tag>
						<a-tag color="cyan" v-show="dectypeSzNum != 3 && $route.query.twoStep">
							<span class="dclTrnRelFlagSpen" v-show="dectypeSzNum != 3">涉税</span><span
								class="dclTrnRelFlagSpen"><a-icon v-show="dectypeSsNum == 1" class="dectypeSjSs"
									type="check-circle" /><a-icon v-show="dectypeSsNum == 0" type="close-circle" /></span>&emsp;
						</a-tag>
						<a-tag color="cyan" v-show="!!dectypeYcrr && $route.query.twoStep">
							<span class="dclTrnRelFlagSpen" v-html="dectypeYcrr"></span>
						</a-tag>
						<a-tag color="cyan" v-show="record.hasCd == '1'">
							<span class="dclTrnRelFlagSpen">有舱单</span>
						</a-tag>

					</div>
					<!-- 打印导出等功能-->
					<div class="topSpanextra">
						<a-button-group>
							<!--							<a-button @click='startDecTypeClick' type='primary'-->
							<!--												v-show='!disableEdit && decTypeState '-->
							<!--												icon="unlock" size="small" >-->
							<!--								开启两步申报-->
							<!--							</a-button>-->
							<!--							<a-button @click='offDecTypeClick' type='primary'-->
							<!--												v-show='!disableEdit &&  !decTypeState'-->
							<!--												icon="lock" size="small"  >-->
							<!--								取消两步申报-->
							<!--							</a-button>-->

							<!--							手动新增-->
							<a-dropdown class="sc">
								<a-menu slot="overlay">
									<a-menu-item key="1" @click="aiSettingHandle">
										报关单AI配置
									</a-menu-item>
									<a-menu-item key="2" @click="aiAuditHandle">
										关智宝
									</a-menu-item>
								</a-menu>
								<a-button size="small" type="primary">
									<j-gpt-icon :size="16" style="margin-right: 5px;margin-bottom: 3px" />
									关智宝
									<a-icon type="down" />
								</a-button>
							</a-dropdown>
							<a-button v-show="!xiangqing" id="ocrButton" size="small" type='primary' @click="aiCheckHandle">
								<j-ai-check-icon :size="16" style="margin-right: 5px;margin-bottom: 3px" />
								AI审单
							</a-button>
							<a-button v-show="!xiangqing" id="ocrButton" size="small" type='primary' @click="aiOneCheckHandle">
								<j-ai-check-other-icon :size="16" style="margin-right: 5px;margin-bottom: 3px" />
								AI复审
							</a-button>
							<a-button v-show="!xiangqing" id="saveButton" v-has="'dec:edit'" icon="plus" size="small" type='primary'
								@click='addDec'>
								新增
							</a-button>
							<a-dropdown v-if="this.record.id" class="sc">
								<a-menu slot="overlay">
									<a-menu-item v-if="this.record.ieFlag == 'I'" @click="handleDownloadTax" key="1">
										下载税单核对单PDF
									</a-menu-item>
									<a-menu-item v-if="this.record.ieFlag == 'E'" @click="handleDownloadRax" key="2">
										下载退税联PDF
									</a-menu-item>
									<a-menu-item @click="handleDownloadReleaseNote" key="3">
										下载放行通知PDF
									</a-menu-item>
									<a-menu-item @click="handleDownloadCheck" key="4">
										下载查验通知PDF
									</a-menu-item>
								</a-menu>
								<a-button v-has="'dec:down'" size="small" icon="export" type="primary">
									下载申报相关PDF文件
									<a-icon type="down" />
								</a-button>
							</a-dropdown>
							<!--							<a-button size="small" type="primary" icon="export" :loading="handleTaxLoading" @click="handleDownloadTax">下载税单核对单PDF文件</a-button>-->
							<a-button v-has="'dec:edit'" v-if="this.record.id && this.record.ieFlag == 'I'" size="small"
								:loading="handleTaxLoading" icon="property-safety" type="primary" @click="handleTax">查看税单信息</a-button>
							<a-button v-if="this.record.id" v-has="'dec:edit'" :loading="decLogicVerificationLoading"
								icon="check-circle" size="small" type='primary' @click='validate'>
								逻辑校验
							</a-button>
							<a-button @click='decHeadDocumentMethod' type='primary' icon="file" size="small">
								随附单据
							</a-button>
							<a-button v-has="'dec:edit'" id="saveButton" @click='auditHandleAdd' type='primary'
								v-if="!record.decStatus || record.decStatus == '1'" v-show="!xiangqing" :loading="saveSubmitLoading"
								icon="save" size="small">
								保存
							</a-button>

							<a-button v-has="'dec:edit'" size="small" type="primary" icon="copy" v-if="this.record.id"
								@click="copyDecHead">复制</a-button>
							<!--							20241030添加-->

							<a-dropdown v-if="this.record.id" class="sc">
								<a-menu slot="overlay">
									<a-menu-item @click="handleCreateInvtByDec('B1')" key="1">
										生成核注单(手册)
									</a-menu-item>
									<a-menu-item @click="handleCreateInvtByDec('95')" key="2">
										生成核注单(账册)
									</a-menu-item>
									<a-menu-item @click="handleCreateInvtByDec('Z7')" key="3">
										生成核注单(特殊区域)
									</a-menu-item>
									<a-menu-item @click="handleCreateInvtByDec('Z8')" key="4">
										生成核注单(保税物流)
									</a-menu-item>
								</a-menu>
								<a-button v-has="'dec:schzd'" icon="edit" size="small" type="primary">
									生成核注单
									<a-icon type="down" />
								</a-button>
							</a-dropdown>
							<a-dropdown v-if="this.record.id" class="sc">
								<a-menu slot="overlay">
									<a-menu-item key="1" @click="pricecheck">
										价格检查
									</a-menu-item>
									<a-menu-item key="2" @click="Brandrecord">
										知识产权检查
									</a-menu-item>
									<a-menu-item key="3" @click="pushCgpt">
										推送采购平台
									</a-menu-item>
								</a-menu>
								<a-button v-has="'dec:sccg'" :loading="pushCgptLoading" icon="edit" size="small" type="primary">
									市场采购
									<a-icon type="down" />
								</a-button>
							</a-dropdown>

							<a-dropdown v-if="this.record.id && !xiangqing" class="sc">
								<a-menu slot="overlay">
									<a-menu-item @click="handleFirstTrial" key="1">
										初审
									</a-menu-item>
									<a-menu-item key="1-1" @click="handleFirstTrial">
										取消初审
									</a-menu-item>
									<a-menu-item @click="handleReview" key="2">
										复审
									</a-menu-item>
									<a-menu-item key="2-1" @click="handleReview">
										取消复审
									</a-menu-item>
								</a-menu>
								<a-button v-has="'dec:edit'" :loading="auditLoading" icon="edit" size="small" type="primary">
									初复审
									<a-icon type="down" />
								</a-button>
							</a-dropdown>
							<a-dropdown v-if="this.record.id && !xiangqing" class="sc">
								<a-menu slot="overlay">
									<a-menu-item @click="handleExportDecComplete('EXCEL')" key="1">
										下载EXCEL
									</a-menu-item>
									<a-menu-item @click="handleExportDecComplete('PDF')" key="2">
										下载PDF
									</a-menu-item>
									<a-menu-item key="2-1" @click="handleDecPrint('PDF')">
										打印PDF
									</a-menu-item>
									<a-menu-item @click="handleExportDecComplete('SHDPDF')" key="3">
										下载审核单PDF
									</a-menu-item>
									<a-menu-item key="3-1" @click="handleDecPrint('SHDPDF')">
										打印审核单PDF
									</a-menu-item>

								</a-menu>
								<a-button v-has="'dec:down'" :loading="printButtonLoading" icon="printer" size="small" type="primary">
									下载
									<a-icon type="down" />
								</a-button>
							</a-dropdown>
							<a-button v-show="this.record.id && !xiangqing" id="handlePushDec" v-has="'dec:push'" icon="to-top" size="small"
								type='primary' @click='handlePushDec'>
								推送
							</a-button>
							<a-button v-has="'dec:edit'" id="saveButton" @click='handleDelete' type='primary'
								v-show="this.record.id && !xiangqing" :loading="saveSubmitLoading" icon="delete" size="small">
								删除
							</a-button>

						</a-button-group>
					</div>
				</div>
				<!-- 涉检涉税涉税 -->
				<a-modal v-model="decTypesTwo" :maskClosable="maskClosable" title="确定两步申报" @ok="handleOkTwoStep"
					:closable="false" :keyboard="false" @cancel="decTypesTwo = false">

					<div style="margin-left: 10%">
						是否涉证
						<a-radio-group style="margin-left: 15%" v-model="dectypeSzNum">
							<a-radio :value="1">
								是
							</a-radio>
							<a-radio :value="0">
								否
							</a-radio>
						</a-radio-group>
					</div>

					<div style="margin-left: 10%;margin-top: 5%">
						是否涉检
						<a-radio-group style="margin-left: 15%" v-model="dectypeSjNum">
							<a-radio :value="1">
								是
							</a-radio>
							<a-radio :value="0">
								否
							</a-radio>
						</a-radio-group>
						<br />
					</div>

					<div style="margin-left: 10%;margin-top: 5%">
						是否涉税
						<a-radio-group style="margin-left: 15%" v-model="dectypeSsNum">
							<a-radio :value="1">
								是
							</a-radio>
							<a-radio :value="0">
								否
							</a-radio>
						</a-radio-group>
						<br />
					</div>

					<div style="margin-left: 10%;margin-top: 5%">
						申报模式
						<a-select v-model="dectypeYcrrModal" style="margin-left:7%;width: 40%">
							<!--							<a-select-option-->
							<!--								v-for="(unit,unitIndex) in unitList"-->
							<!--								:key="unitIndex"-->
							<!--								:value="unit.code"-->
							<!--								@click="dectypeYcrrClick"-->
							<!--							>{{unit.name}}-->
							<!--							</a-select-option>-->
							<a-select-option v-for="(unit, unitIndex) in unitList" :key="unitIndex" :value="unit.code">{{ unit.name }}
							</a-select-option>
						</a-select>
						<br />
					</div>

					<template slot="footer">
						<a-button type="primary" @click="handleOkTwoStep">确认</a-button>
					</template>
				</a-modal>
			</template>
			<template>
				<!--标签页overflow:visible;float:left;-->
				<a-tabs style='padding-top: 0;overflow:visible;width:100%;float:left;margin-top: -10px' size="small">
					<!--详情-->
					<a-tab-pane key='1' tab='详情' style="margin-top: -10px">
						<a-form-model :model='record' ref='details' v-enterElList>
							<a-row>
								<a-col :span='19'>
									<details-info-left ref="detailsInfoLeft" :focusIndexs.sync="focusIndexs"
										v-model='record' :dec-lists='record.decLists' :disable-edit='disableEdit'
										:downDecStatusType.sync="downDecStatusType" :imSignId="imSignId"
										:txt-apply-type='txtApplyType' @downDecStatusTypeAnswer="downDecStatusTypeAnswer"
										@focusIndexsUpadte='focusIndexsUpadte' @focusUpadte='focusUpadte' @isAuditDecEditmethod='isAuditDecEditmethod' />
									<br><br>
								</a-col>

								<a-col :span='5'>
									<!--集装箱-->
									<details-info-right-top ref="detailsInfoRightTop" :focusIndexs.sync="focusIndexs"
										v-model='record.decContainers' :disable-edit='disableEdit' :recordContainerNum.sync='record'
										:txt-apply-type='txtApplyType' @focusIndexsUpadte='focusIndexsUpadte' />
									<br>
									<!--单证代码-->
									<details-info-right-bottom v-model='record.decLicenseDocuses' :decLicenseDocuses.sync='record'
										:disable-edit='disableEdit' :txt-apply-type='txtApplyType' />
									<div class='detailsRightDiv'>
										<div class='detailsRightDivtips'>tips:</div>
										<div class="detailsRightDiv-div">总价:
											<span v-html="addTotal"></span><span> ({{ addCurrency }})</span>
										</div>
										<div class="detailsRightDiv-div">总净重:
											<span v-html="addWeight"></span>
										</div>
										<div class="detailsRightDiv-div">成交数量合计:
											<span v-html="addGoodsCount"></span>
										</div>
										<div class="detailsRightDiv-div">法定第一数量合计:
											<span v-html="addCount1"></span>
										</div>
										<div class="detailsRightDiv-div">法定第二数量合计:
											<span v-html="addCount2"></span>
										</div>
									</div>
									<!--									商铺和采购商信息显示，只有报关制单进来的才会显示-->
									<div v-if="$route.query.isMarketProcurement" class='detailsRightShopsPurchaserInfoDiv'>
										<div class='detailsRightDivtips'></div>
										<div class="detailsRightDiv-div">商铺:
											<span v-html="$route.query.shopName ? $route.query.shopName : record.shopName"></span>
										</div>
										<div class="detailsRightDiv-div">采购商:
											<span v-html="$route.query.purchaserName ? $route.query.purchaserName :
												record.purchaserName"></span>
										</div>

									</div>
								</a-col>
							</a-row>
						</a-form-model>
					</a-tab-pane>
					<a-tab-pane key='3' tab='转关运输申报单' v-if="$route.query.ZGTQBG" :disabled="!this.id">
						<detailsZGTQBG>
						</detailsZGTQBG>
					</a-tab-pane>
					<a-tab-pane key='2' tab='附件管理'>
						<attachment-list ref="attachmentList" :cid="String(this.id)" :saveType="!disableEdit" :showSubmit="false"
							attachmenttype="DEC" />
					</a-tab-pane>
				</a-tabs>
			</template>
		</a-card>
		<a-card :bordered='true' style='min-height:75px;margin-top:5px'>
			<template>
				<a-descriptions :column='{ xxl: "5", xl: "5", lg: "3", md: "2", sm: "2", xs: 1 }' span='{2}'>
					<a-descriptions-item label='报关员卡号'>{{ record.icNumber }}</a-descriptions-item>
					<a-descriptions-item label='创建人'>{{ record.createPerson }}</a-descriptions-item>
					<a-descriptions-item label='创建时间'>{{ record.createTime }}</a-descriptions-item>
					<a-descriptions-item label='最后修改人'>{{ record.updateBy }}</a-descriptions-item>
					<a-descriptions-item label='修改时间'>{{ record.updateDate }}</a-descriptions-item>
					<a-descriptions-item label=''></a-descriptions-item>
					<a-descriptions-item label='初审人'>{{ record.firstTrialBy }}</a-descriptions-item>
					<a-descriptions-item label='初审时间'>{{ record.firstTrialDate }}</a-descriptions-item>
					<a-descriptions-item label='复审人'>{{ record.reviewBy }}</a-descriptions-item>
					<a-descriptions-item label='复审时间'>{{ record.reviewDate }}</a-descriptions-item>
				</a-descriptions>
			</template>
		</a-card>
		<!--  随附单据      -->
		<dec-head-document ref="decHeadAttachment" :show='decHeadDocument' :saveShow="saveShow"
			v-model='record'></dec-head-document>

		<!--xiala-->
		<!--初复审modal-->
		<j-modal :title="title" :width="600" :visible="visible" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
			<template slot="footer">
				<a-button type="default" @click="handleCancel">取消</a-button>
				<a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
			</template>
			<a-spin :spinning="confirmLoading">
				<a-form-model ref="form" :model="model" :rules="validatorRules">
					<a-row>
						<a-card size="small" :bordered="false" title="">
							<a-col v-if="isFirst">
								<a-form-model-item :labelCol="labelCol1" :wrapperCol="wrapperCol1" label="初审意见" prop="firstOpinion">
									<j-remarks-component v-model="model.firstOpinion" placeholder="请输入初审意见"
										:max-length="500"></j-remarks-component>
								</a-form-model-item>
							</a-col>
							<a-col v-if="!isFirst">
								<a-form-model-item :labelCol="labelCol1" :wrapperCol="wrapperCol1" label="复审意见" prop="reviewOpinion">
									<j-remarks-component v-model="model.reviewOpinion" placeholder="请输入复审意见"
										:max-length="500"></j-remarks-component>
								</a-form-model-item>
							</a-col>
						</a-card>
					</a-row>
				</a-form-model>
			</a-spin>
		</j-modal>

		<!--		报关单逻辑校验弹出框-->
		<a-modal title="报关单逻辑校验结果" :width="600" :visible="visibleLogicVerification"
			:maskClosable="false" cancelText="关闭" @cancel="visibleLogicVerification = false">
			<template slot="footer">
				<a-button type="primary" @click="visibleLogicVerification = false">关闭</a-button>
			</template>

			<!-- table区域-begin -->
			<div>
				<a-table ref="table" :columns="columnsLogicVerification" :dataSource="dataSourceLogicVerification" :pagination="false" :scroll="{ x: true }" bordered
					class="j-table-force-nowrap" rowKey="id" size="small">
				</a-table>
			</div>

		</a-modal>
		<a-modal :maskClosable="false" :visible="visibleYdt_validate" :width="600"
						 cancelText="关闭" title="报关单逻辑校验结果" @cancel="visibleYdt_validate = false">
			<template slot="footer">
				<a-button type="primary" @click="visibleYdt_validate = false">关闭</a-button>
			</template>

			<div >
				<div style="overflow:auto;height: 650px">
					<div style=
								 "background-color:#fff; border-radius: 3px; margin-bottom: 6px; border: 1px solid #e8e8e8;">
						<div style=
									 "margin-bottom: 6px; padding: 2px 6px; background-color:#f3f0f0;">
							<img :src="require('@/assets/cuowu.png')">以下内容为必填项，请填写完整再提交
						</div>

						<div id="requiredCheckItemList" class="validate-body" style=
							"padding: 6px;">
							<div v-for="(item, index) in Ydt_validateData.requiredCheckItemList"
									 :key="index" style="margin-bottom: 6px; white-space: normal;">
								{{item.name}}
							</div>
						</div>
					</div>

					<div style=
								 " background-color:#fff; border-radius: 3px; margin-bottom: 6px; border: 1px solid #e8e8e8;">
						<div style=
									 " margin-bottom: 6px; padding: 2px 6px; background-color:#f3f0f0;">
							<img src="../../../assets/cuowu.png">以下信息存在问题，请全部修复后再提交
						</div>

						<div id="invalidCheckItemList" class="validate-body" style=
							"padding: 6px;">
							<div v-for="(item, index) in Ydt_validateData.invalidCheckItemList"
									 :key="index" style="margin-bottom: 6px; white-space: normal;">
								{{item.name}}
							</div>

						</div>
					</div>

					<div style=
								 " background-color:#fff; border-radius: 3px; margin-bottom: 6px; border: 1px solid #e8e8e8;">
						<div style=
									 " margin-bottom: 6px; padding: 2px 6px; background-color:#f3f0f0;">
							<img src="../../../assets/warning.png">以下信息可能存在问题，请仔细核对
						</div>

						<div id="recommendedCheckItemList" class="validate-body" style=
							"padding: 6px;">
							<div v-for="(item, index) in Ydt_validateData.recommendedCheckItemList"
									 :key="index" style="margin-bottom: 6px; white-space: normal;">
								{{item.name}}
							</div>
						</div>
					</div>
				</div>
			</div>

		</a-modal>
		<!-- OCR识别 -->
		<ocrUploadIdentification v-model="ocrvisible" @resp="ocrUploadIdentificationResp"></ocrUploadIdentification>

		<logic-check-result-modal ref="logicCheckResultModal" />
		<div style="position:fixed; z-index:999; bottom:2px;width:92.5%;height:18px;background:#D0D0D0; color:#FF0000;">
			<span style="margin-left:2.3% ;font-size: 11px;line-height:1px" v-html="decPrompt"></span>
		</div>

		<!-- 查看报关单税单信息 -->
		<tax-modal ref="taxModal" @taxmodalEmit="taxmodalEmit" />
		<bind-cgs-modal ref="bindCgsModal" @resp="bindCgsModalResp"></bind-cgs-modal>

		<!-- AI制单 -->
		<ai-maker-modal ref="aiMakerModal" @ok="tolist" />
		<!-- 报关单AI配置 -->
		<ai-setting-modal ref="aiSettingModal" />
		<!-- AI审单 -->
		<ai-check-modal ref="aiCheckModal" />
	</div>
	<!--固定-->
</template>

<script>
import store from '@/store'
import detailsZGTQBG from '@views/Business/customs-declaration/details-ZGTQBG'
import DetailsInfoLeft from '@views/Business/customs-declaration/details-info-left'
import DetailsInfoRightTop from "@views/Business/customs-declaration/details-info-right-top"
import DetailsInfoRightBottom from "@views/Business/customs-declaration/details-info-right-bottom"
import SelectItem from "@views/declaration/component/m-table-select-item"
import DecHeadDocument from '@views/Business/component/modal/dec-head-document'
import ocrUploadIdentification from '@views/Business/customs-declaration/ocrUploadIdentification'

import {
	getDecById,
	saveDec, Ydt_validate,
} from '@/api/dec/dec'
import {
	getAction,
	deleteAction,
	downloadFile,
	postAction,
	downFile,
	_postAction,
	putAction,
	downloadFileBase64, openPdfInBrowser
} from '@/api/manage'
import LogicCheckResultModal from '@views/Business/customs-declaration/LogicCheckConfig/moudles/LogicCheckResultModal'
import lodash from 'lodash'
import AttachmentList from "@views/Business/component/attachment/AttachmentList.vue";
import Vue from 'vue'
import { ENTERPRISE_INFO, TENANT_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'
import TaxModal from "@/views/Business/customs-declaration/components/TaxModal.vue";
import BindCgsModal from "@/views/Business/customs-declaration/components/BindCgsModal.vue";
import { createCgptInfo, BrandrecordCheck, PRICECHECK } from '@api/cgpt/cgpt'
import AiMakerModal from "@/views/Business/customs-declaration/components/AiMakerModal.vue";
import AiSettingModal from "@/views/Business/customs-declaration/components/AiSettingModal.vue";
import AiCheckModal from "@/views/Business/customs-declaration/components/AiCheckModal.vue";
import { validateAndShowResult } from '@/views/Business/customs-declaration/utils/decValidator';
import JGptIcon from '@/components/jeecg/JGptIcon.vue';
import JAiCheckIcon from "@/components/jeecg/JAiCheckIcon.vue";
import JAiCheckOtherIcon from "@/components/jeecg/JAiCheckOtherIcon.vue";
import {ajaxGetDictItems} from "@/api/api";

export default {
	name: 'Details',
	inject: ['closeCurrent'],
	components: {
		AiMakerModal, AiSettingModal, AiCheckModal,
		TaxModal,
		AttachmentList,
		DetailsInfoRightBottom,
		DetailsInfoRightTop,
		DetailsInfoLeft,
		SelectItem,
		DecHeadDocument,
		LogicCheckResultModal,
		detailsZGTQBG,
		ocrUploadIdentification,
		BindCgsModal,
		JGptIcon,
		JAiCheckIcon,
		JAiCheckOtherIcon
	},

	data() {
		return {
			pushCgptLoading: false,
			auditLoading: false,
			createInvtByDecLoading: false,
			spinningLoading: false,
			handleTaxLoading: false,
			visibleLogicVerification: false,
			visibleYdt_validate: false,
			Ydt_validateData:{},
			ocrvisible: false,
			decLogicVerificationLoading: false,
			printButtonLoading: false,
			decTypeState: true,//开启两步申报按钮显示状态
			model: { firstOpinion: '', reviewOpinion: '' },
			confirmLoading: false,
			visible: false,
			isFirst: false,
			title: '初审意见',
			validatorRules: {
				firstOpinion: [{ required: true, message: '请输入审核意见!' }],
				reviewOpinion: [{ required: true, message: '请输入审核意见!' }]
			},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			decTitle: '',
			//初始化数据（作为比较）
			sourceRecord: "",
			//作为比较初始化数据和
			endCompareRecord: {},
			addTotal: "",//总价合计
			addCurrency: '',//币制
			addWeight: '',//净重合计
			addGoodsCount: '',//成交数量合计.
			addCount1: '',//法定第一数量合计合计
			addCount2: '',//法定第二数量合计合计
			placeholder: "s",
			nodatatext: "s",
			focusIndexs: 0,//组件下标标识值
			decPrompt: '申报地海关：输入4位代码或名称（如‘北京海关’应输入‘0100’或‘北京海关’）',//input输入提示
			// 禁用表单
			disableEdit: false,
			//两步申报的单子不限制发送：如果未同步、DEC_TYPE不为空，且第三位不是空或不是1的，且PROFILE_TYPE=1的，不限制发送。
			sendType: false,//申报状态为1（暂存）或者空的可以发送
			show: {
				decPrintType: false,
			},
			id: this.$route.query.id,
			columns: [],
			columnsLogicVerification: [
				{ dataIndex: 'type', title: '校验模块', width: 160 },
				{ dataIndex: 'reason', title: '校验结果', width: 160 },
			],
			dataSourceLogicVerification: [],
			// 当前
			currentDetail: {},
			currentDetailHeader: {},

			dectypes: '一般报关单',
			//随附单据
			decHeadDocument: false,
			saveShow: true,
			currentDetailBody: {},
			dectypeYcrr: '',
			record: {
				//  id
				id: "",
				orderId: '',
				// 委托单号
				applyNumber: "",
				// 报关单分类 （0：一般报关单 1：转关提前报关单 2：备案清单 3：转关提前备案清单 4：出口二次转关）
				dclTrnRelFlag: "",
				dclTrnRelFlagName: "",
				// 申报状态
				decStatus: "",
				// 分票序号
				partId: "",
				// 电子委托号 暂时未使用
				elecDelNo: "",
				// 客户端编号 原CUSTOMS_CODE
				clientSeqNo: "",
				// 统一编号
				seqNo: "",
				// 报关单号
				clearanceNo: "",
				// 备案号
				recordNumber: "",
				// 进出口标识
				ieFlag: "",
				// 进出日期
				outDate: "",
				// 申报日期
				appDate: "",
				// 运输方式 海运2航空5
				shipTypeCode: "",
				// 运输工具名称
				shipName: "",
				// 航次
				voyage: "",
				// 合同协议号
				contract: "",
				// 提运单号
				billCode: "",
				// 申报地海关
				declarePlace: "",
				// 进出境关别
				outPortCode: "",
				// 入境口岸/离境口岸
				entyPortCode: "",
				// 境外收货人代码
				overseasConsigneeCode: "",
				// 境外收货人名称（外文）
				overseasConsigneeEname: "",
				// 境外发货人代码
				overseasConsignorCode: "",
				// 境外发货人名称（外文）
				overseasConsignorEname: "",
				// 境内收发货人社会统一信用代码
				optUnitSocialCode: "",
				// 境内收发货人海关代码
				optUnitId: "",
				// 境内收发货人检验检疫编码
				tradeCiqCode: "",
				// 境内收发货人名称
				optUnitName: "",
				// 消费使用单位社会统一信用代码
				deliverUnitSocialCode: "",
				// 消费使用单位海关代码
				deliverUnit: "",
				// 消费使用单位检验检疫编码
				ownerCiqCode: "",
				// 消费使用单位名称
				deliverUnitName: "",
				// 申报单位社会统一信用代码
				declareUnitSocialCode: "",
				// 申报单位海关代码
				declareUnit: "",
				// 申报单位检验检疫编码
				declCiqCode: "",
				// 申报单位名称
				declareUnitName: "",
				// 贸易国
				tradeCountry: "",
				// 启运国
				arrivalArea: "",
				// 启运港代码
				despPortCode: "",
				// 经停港/指运港
				desPort: "",
				// 成交方式
				termsTypeCode: "",
				// 监管方式
				tradeTypeCode: "",
				// 征免性质
				taxTypeCode: "",
				// 运费代码
				shipFeeCode: "",
				// 运费值
				shipFee: "",
				// 运费币制
				shipCurrencyCode: "",
				// 保费代码
				insuranceCode: "",
				// 保费值
				insurance: "",
				// 保费币制
				insuranceCurr: "",
				// 杂费代码
				extrasCode: "",
				// 杂费值
				extras: "",
				// 杂费币制
				otherCurr: "",
				// 件数
				packs: "",
				// 毛重
				grossWeight: "",
				// 净重
				netWeight: "",
				// 许可证号
				licenceNumber: "",
				// 包装种类
				packsKinds: "",
				// 其他包装
				packType: "",
				// 集装箱数
				containerNum: "",
				// 报关单类型
				clearanceType: "",
				// 货物存放地点
				goodsPlace: "",
				// 随附单证
				contractAtt: "",
				// 备注
				markNumber: "",
				// 标记唛码
				markNo: "",
				// 是否担保验放 ？？？？？
				chkSurety: true,
				// 特殊关系
				promiseItmes: "",
				// 关联报关单号
				relId: "",
				// 关联备案号
				relManNo: "",
				// 保税监管场地
				bonNo: "",
				// 场地代码
				cusFie: "",
				// 体积 ？？？？？
				volume: "",
				// 总金额 ？？？？？
				total: "",
				// 总数量 ？？？？？
				goodsCount: "",
				// 检验检疫受理机关 商检信息
				orgCode: "",
				// 口岸检验检疫机关 商检信息
				inspOrgCode: "",
				// 目的地检验检疫机关 商检信息
				purpOrgCode: "",
				// 领证机关 商检信息
				vsaOrgCode: "",
				// B/LNO 商检信息
				blNo: "",
				// 特殊业务标识 商检信息
				specDeclFlag: "",
				// 启运日期 格式为：yyyyMMdd 商检信息
				despDate: "",
				// 卸毕日期 格式为：yyyyMMdd ？？？？？？
				cmplDschrgDt: "",
				// 关联号码 商检信息
				correlationNo: "",
				// 关联理由 商检信息
				correlationReasonFlag: "",
				// 原集装箱标识 商检信息
				origBoxFlag: "",
				// 企业资质信息 商检信息[{"EntQualifNo":"","EntQualifTypeCode":"456"}]
				copLimitType: "",
				// 使用人信息表 商检信息[{"UseOrgPersonCode":"","UseOrgPersonTel":"456"}]
				decUserType: "",
				// 检验检疫签证申报要素 商检信息[{"AppCertCode":"","ApplOri","","ApplCopyQuan":"789"}]
				requestCertType: "",
				// 报关员
				declarant: "",
				// 报关人员证号
				declarantNo: "",
				// 报关单状态 保存；发送
				status: "",
				// 转关类型 报文用
				tranferType: "",
				// 通关模式 报文用
				clearanceMode: "",
				// 申报单类型 报文用属地报关SD；备案清单：ML。LY：两单一审备案清单。CL:汇总征税报关单。SS:”属地申报，属地验放”
				decType: "",
				// 报关标志 1：普通报关 3：北方转关提前 5：南方转关提前 6：普通报关，运输工具名称以‘◎’开头，南方H2000直转 报文用
				ediId: "",
				// 企业内部清单编号 （由企业自行编写） 与核注单关联
				etpsInnerInvtNo: "",
				// 表体
				decLists: [],
				// 集装箱信息
				decContainers: [],
				// 附属单据
				decAttachments: [],
				// 随附单证
				decLicenseDocuses: [],
				//进出口标识
				imSign: "",
				//报文发送状态
				send: '',
				//最后修改人
				updateBy: '',
				//最后修改时间
				updateDate: '',
				shopName: '',//商铺名称
				purchaserName: '',//采购商名称
				hasCd: '',
			},
			decAuditType: true,//审核暂时调用保存的接口去严重数据
			logicCheck: false,//审核逻辑校验状态
			logicCheckLoading: false,//审核逻辑校验状态 转圈
			imSignId: this.$route.query.imSignId == null || this.$route.query.imSignId == undefined ? '' : this.$route.query.imSignId,
			saveBtnLoading: false,//打印按钮
			//企业用户平台已经是否是当前登录用户创建的报关单的查询权限字段
			txtApplyType: false,
			//在外面打开复制
			copyHead: true,
			saveSubmitLoading: false,//按钮保存点击 转圈
			auditDecMethodLoading: false,//审核点击 转圈
			isAuditSendLoading: false,//发送报文点击 转圈
			isAuditSendTwo: false,//发送报文是否是多次发送
			auditDecPrintLoading: false,//导出 转圈
			isSeqNoType: false,//判断统一编号是否是null
			seqCodeType: false,//判断是否判断了统一编号
			url: {
				// exportXlsUrl:"http://172.31.1.191:15558/dcl/dec/printingDec"
				getCDPdfRtx: '/DecHead/dec-head/getCDPdfRtx',
				getCDReleaseNote: '/DecHead/dec-head/getCDReleaseNote',
				getCDCheckNote: '/DecHead/dec-head/getCDCheckNote',
				updatePushStatusById: '/DecHead/dec-head/updatePushStatusById',
			},
			downType: false,
			downDecStatusType: false,//下行数据控制
			maskClosable: false,
			delLoadingBill: false,
			classifyGridOptions: {
				columns: [
					{ field: 'templeteName', title: '模板名称', },
					// {field: '33', title: '警告',},
					{ field: 'mouldId', title: '模板编号', },
					{ field: 'ieFlag', title: '进出口', formatter: this.formatterimport },
					{ field: 'optUnitName', title: '境内收发货人', },
					{ field: 'templeteByCreate', title: '制单人', },
				],
				// pagerConfig: null,
				toolbarConfig: null,
				pagerConfig: {
					pageSize: 15,
					pageSizes: [15, 50, 100, 200]
				}
			},
			passageway: "",
			xiangqing: this.$route.query.xiangqing == null || this.$route.query.imSignId == undefined ? false : this.$route.query.xiangqing,
			dectypeYcrrModal: '',
			//涉证涉检涉税
			dectypeSzNum: '',//涉检涉税涉证页面显示 3代表不选择
			dectypeSjNum: '',//3代表不选择
			dectypeSsNum: '',//3代表不选择
			//两步申报模态框
			decTypesTwo: false,
			unitList: [
				{
					code: '1',
					name: '概要申报',
				},
				{
					code: '2',
					name: '完整申报',
				},
				{
					code: '3',
					name: '一次录入',
				},
			],
			headers: {}
		}
	},
	created() {
		// 页面加载时检查字典缓存
		this.checkAndLoadDictCache()
		this.handleInit()
		//两部申报进入的新增弹出模态框
		if (this.$route.query.twoStep && this.$route.query.decNew) {
			this.startDecTypeClick()
		}
		let tenantInfo = store.getters.tenantInfo
		this.headers['X-Access-Token'] = store.getters.token
		this.headers['Rest-Flag'] = 1
		this.headers['Tenant-Id'] = tenantInfo.id
		this.setTwoMenuData()

	},
	computed: {
		routeTab() {
			let id = this.isEmpty(this.record.id) ? "NEW" : String(this.record.id).substring(String(this.record.id).length - 4)
			let lastid = this.isEmpty(this.record.seqNo) && this.isEmpty(id) ? "NEW" : this.record.seqNo ? this.record.seqNo : id
			return {
				title: "报关单-" + lastid,
				tips: `报关单-${this.record.seqNo}`
			}
		}
	},
	mounted() {
		// 页面加载时检查字典缓存
		this.checkAndLoadDictCache()
	},
	methods: {
		/**
		 * 检查字典缓存是否存在，如果不存在则加载
		 */
		checkAndLoadDictCache() {
			const dictCache = Vue.ls.get(UI_CACHE_DB_DICT_DATA)
			if (!dictCache || Object.keys(dictCache).length === 0) {
				console.log('字典缓存不存在，开始加载...')
				this.refleshCache()
			} else {
				console.log('字典缓存已存在')
			}
		},
		/**
		 * 刷新字典缓存
		 */
		refleshCache() {
			getAction("sys/dict/refleshCache").then((res) => {
				if (res.success) {
					getAction("sys/dict/queryAllDictItems").then((res) => {
						if (res.success) {
							Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
							Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
							console.log('字典缓存加载完成')
						}
					})
					// this.$message.success("刷新缓存完成！")
				}
			}).catch(e => {
				// this.$message.warn("刷新缓存失败！")
				console.log("刷新失败", e)
			})
		},
		tolist() {
			// this.$message.success('AI制单中，稍后请刷新页面查看最新数据');
			// setTimeout(() => {
			// 	// 刷新当前页面
			// 	this.$router.go(0);
			// }, 1000);
		},
		/**
		 * AI检查
		 */
		aiOneCheckHandle() {
			// 获取数据后进行AI校验
			this.aiValidateRecord(true)
		},
		/**
		 * AI审单
		 */
		aiCheckHandle() {
			this.$refs.aiCheckModal.show(this.record.id)
		},
		/**
		 * 报关单AI配置
		 */
		aiSettingHandle() {
			this.$refs.aiSettingModal.show()
		},
		/**
		 * AI制单
		 */
		aiAuditHandle() {
			this.$refs.aiMakerModal.title = '关智宝'
			this.$refs.aiMakerModal.decId = this.record.id
			this.$refs.aiMakerModal.show()
		},
		//设置两步申报菜单数据
		setTwoMenuData() {
			const path = this.$route.path
			const lastPart = path.split('/').pop()
			console.log(lastPart)

			// 定义一个映射对象，将路径片段与配置参数对应
			const configMap = {
				'details_TWO_IBG': '2',       // (分次录入)进口报关单
				'details_TWO_IQD': '2',       // (分次录入)进境备案清单
				'details_TWO_IBG_once': '3',  // (一次录入)进口报关单
				'details_TWO_IQD_once': '3'   // (一次录入)进境备案清单
			};

			// 检查 lastPart 是否在映射中
			if (configMap.hasOwnProperty(lastPart)) {
				this.$route.query.twoStep = true;
				this.$route.query.decNew = true;
				this.imSignId = '1';
				this.startDecTypeClick();
				this.dectypeYcrrModal = configMap[lastPart];
			}

		},
		ocrUploadIdentificationResp(ocrData) {
			// if (this.record.imSign === '1') 进口
			const decHead = ocrData.DecHead
			const decList = ocrData.DecList
			this.record.declarePlace = decHead.customMaster
			this.record.optUnitSocialCode = decHead.rcvgdTradeScc
			this.record.optUnitId = decHead.rcvgdTradeCode
			this.record.tradeCiqCode = decHead.consigneeCode
			this.record.optUnitName = decHead.consigneeCname
			if (this.record.imSign === '1') {//进口
				this.record.overseasConsignorCode = decHead.consignorCode
				this.record.overseasConsignorEname = decHead.consignorEname
			} else {
				this.record.overseasConsigneeCode = decHead.consignorCode
				this.record.overseasConsigneeEname = decHead.consignorEname
			}
			this.record.declareUnitSocialCode = decHead.agentScc
			this.record.declareUnit = decHead.agentCode
			this.record.declCiqCode = decHead.declRegNo
			this.record.declareUnitName = decHead.agentName
			this.record.outPortCode = decHead.iEPort
			this.record.recordNumber = decHead.manualNo
			this.record.contract = decHead.contrNo
			this.record.shipTypeCode = decHead.cusTrafMode
			this.record.shipName = decHead.trafName
			this.record.voyage = decHead.cusVoyageNo
			this.record.billCode = decHead.billNo
			this.record.licenceNumber = decHead.licenseNo
			this.record.tradeTypeCode = decHead.supvModeCdde
			this.record.taxTypeCode = decHead.cutMode
			this.record.arrivalArea = decHead.cusTradeCountry
			this.record.desPort = decHead.distinatePort
			this.record.termsTypeCode = decHead.transMode
			this.record.shipFeeCode = decHead.feeMark
			this.record.shipFee = decHead.feeRate
			this.record.shipCurrencyCode = decHead.feeCurr
			this.record.insuranceCode = decHead.insurMark
			this.record.insurance = decHead.insurRate
			this.record.extrasCode = decHead.otherMark
			this.record.extras = decHead.otherRate
			this.record.otherCurr = decHead.otherCurr
			this.record.packs = decHead.packNo
			this.record.packsKinds = decHead.wrapType
			this.record.grossWeight = decHead.grossWt
			this.record.netWeight = decHead.netWt
			this.record.tradeCountry = decHead.cusTradeNationCode
			this.record.entyPortCode = decHead.despPortCode
			this.record.clearanceType = decHead.entryType
			this.record.markNo = decHead.markNo
			this.record.markNumber = decHead.noteS
			if (decList) {
				let idGenerator = -1
				this.record.decLists = []
				decList.forEach(item => {
					this.record.decLists.push({
						id: idGenerator--,
						item: item.gNo,
						hscode: item.codeTs,
						hsname: item.gName,
						hsmodel: item.gModel,
						goodsCount: item.gQty,
						unitCode: item.gUnit,
						price: item.declPrice,
						total: item.declTotal,
						currencyCode: item.tradeCurr,
						desCountry: item.cusOriginCountry,
						destinationCountry: item.destinationCountry,
						faxTypeCode: item.dutyMode,
						count1: item.qty1,
						unit1: item.unit1,
						count2: item.qty2,
						unit2: item.unit2,
						districtCode: item.districtCode,
						destCode: item.ciqDestCode,
						purpose: item.purpose
					})
				})

			}
		},
		//报关单生成核注单
		// sysId子系统ID(95 :加工贸易账册系统B1: 加工贸易手册系统B2 :加工贸易担保管理系统B3: 保税货物流转系统二期Z7: 海关特殊监管区域管理系统Z8 :保税物流管理系统)
		handleCreateInvtByDec(sysId) {
			if (this.record.invId) {
				this.$message.error('该报关单已经生成核注单，无法继续生成')
				return
			}
			this.$confirm({
				title: '操作确认',
				content: '确定要根据此报关单，生成核注单吗?',
				onOk: async () => {
					this.createInvtByDecLoading = true
					try {
						const response = await _postAction('/DecHead/dec-head/handleCreateInvtByDec', {
							id: this.record.id,
							sysId: sysId
						})
						if (response.success) {
							this.$message.success(response.result ? ('操作成功！核注单流水号：' + response.result.id) : '操作成功！')
							// this.$tabs.close({
							// 	to: `/Business/NemsInvt/nemsInvtDetailsEdit?id=${this.currentDetailHeader.id}`
							// })
							this.$tabs.refresh()
							// this.$router.push({
							// 	path: '/invt/all-search'
							// })
							//跳转到进出口 核注单列表
							if (this.record.ieFlag == 'E') {
								this.$router.push({
									path: '/Business/NemsInvt/NemsInvtIndexE'
								})
							} else {
								this.$router.push({
									path: '/Business/NemsInvt/NemsInvtIndexI'
								})
							}


						} else {
							this.$message.error(response.message)
						}
					} catch (error) {
						this.$message.error('喔噢，出错啦，请重试！')
						console.error(error)
					} finally {
						this.createDecByInvtLoading = false
					}
				}
			})


		},
		//价格检查
		pricecheck() {
			var verifyTradeGoodsPrices = [];
			this.record.decLists.forEach(item=>{
				console.log(item)
				verifyTradeGoodsPrices.push({ goodsNo: item.hscode, goodsPrice: item.price, goodsUnit: item.unitCode, goodsValueUnit: item.currencyCode})
			})

			PRICECHECK(verifyTradeGoodsPrices,this.record.optUnitSocialCode).then(res=>{
				if(res.result && res.result.length>0){
					var msg = '';
					res.result.forEach(item=>{
						if (item.data.goodsPriceSys != null && item.data.goodsPriceSys != "" && item.data.goodsPriceSys != undefined) {
							msg += "第" + (item.C_count+1) + "项商品价格高出系统价格，建议单价小于" + item.data.goodsPriceSys + "美元<br/>";
						}
					})
					if(msg != ''){
						this.$success({
							title: '价格检查',
							// JSX support
							content:h=>{
								return h('div', { domProps: { innerHTML: msg } })
							},
						});
					}else{
						this.$success({
							title: '价格检查',
							// JSX support
							content: (
								<span>检查正常！</span>
							),
						});
					}

				}else{
					this.$success({
						title: '价格检查',
						// JSX support
						content: (
							<span>检查正常！</span>
						),
					});
				}
				console.log(res)
			})

		},
		//知识产权检查
		Brandrecord() {
			BrandrecordCheck(this.record.decLists).then(res => {
				if (res.code == 203) {
					this.$success({
						title: '知识产权检查',
						// JSX support
						content: (
							<span>{res.message}</span>
						),
					});
				} else {
					this.$success({
						title: '知识产权检查',
						// JSX support
						content: (
							<span>检查正常！</span>
						),
					});
				}
			})

		},
		//推送采购平台
		pushCgpt() {
			this.$refs.bindCgsModal.show(this.record.optUnitSocialCode)
		},
		bindCgsModalResp(respData) {
			this.pushCgptLoading = true
			createCgptInfo(this.record.id, respData).then(res => {
				if (!res.success) {
					this.$message.error(res.message)
					return
				}
				if (res.message == '正在生成中。。。。') {
					this.$message.success(res.message)
					return
				}
				this.$message.error('发送成功')
			}).finally(() => {
				this.pushCgptLoading = false

			})

		},
		taxmodalEmit(e) {
			this.handleTaxLoading = e
		},
		//手动新增
		addDec() {
			//新增时 新增
			if (!this.record.id) {
				this.$tabs.refresh()
				// this.record={}

				// if(this.$route.query.imSignId === '1'){
				// 	let curDate = new Date();
				// 	let curYear = curDate.getFullYear(); //获取完整的年份(4位,1970-????)
				// 	let curMonth = curDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
				// 	let curDay = curDate.getDate(); //获取当前日(1-31)
				// 	if (curMonth.toString().length == 1) {
				// 		curMonth = "0" + curMonth.toString()
				// 	}
				// 	if (curDay.toString().length == 1) {
				// 		curDay = "0" + curDay.toString()
				// 	}
				// 	this.record.outDate = curYear.toString() + curMonth + curDay // 	进出口日期 (默认当天)OUT_DATE
				// }

			} else {
				//编辑时新增
				this.$router.push({
					path: '/Business/customs-declaration/details_I',
					query: {
						imSignId: this.record.ieFlag == 'I' ? '1' : '2',
						decNew: true,
						dclTrnRelFlag: this.record.dclTrnRelFlag
					}
				})

			}
			this.$message.info('请输入报关单数据继续新增')

		},
		//处理路由参数，首次跳转获取不到query参数
		handleRouterUrl(url) {
			let arr1 = url.split('?')
			if (arr1[1]) {
				let arr2 = arr1[1].split('&')
				let map = new Map()
				for (let s of arr2) {
					map.set(s.split('=')[0], s.split('=')[1])
				}
				return map
			}
		},
		/**
		 * 报关单查验通知(PDF)
		 */
		handleDownloadCheck() {
			if (!this.record.clearanceNo && !this.record.seqNo) {
				this.$message.warning('统一编号或报关单号必须有其一 !')
				return false
			}
			// this.handleCheckLoading = true
			downloadFileBase64(this.url.getCDCheckNote, "查验通知_" + (this.record.clearanceNo || this.record.seqNo) + ".pdf", {
				cusCiqNo: this.record.clearanceNo || this.record.seqNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在查验通知文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				// this.handleCheckLoading = false
			})
		},
		/**
		 * 报关单放行通知(PDF)
		 */
		handleDownloadReleaseNote() {
			if (!this.record.clearanceNo && !this.record.seqNo) {
				this.$message.warning('统一编号或报关单号必须有其一 !')
				return false
			}
			// this.handleReleaseNoteLoading = true
			downloadFileBase64(this.url.getCDReleaseNote, "放行通知_" + (this.record.clearanceNo || this.record.seqNo) + ".pdf", {
				cusCiqNo: this.record.clearanceNo || this.record.seqNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在放行通知文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				// this.handleReleaseNoteLoading = false
			})
		},
		/**
		 * 报关单文件退税联(PDF)
		 */
		handleDownloadRax() {
			if (!this.record.clearanceNo) {
				this.$message.warning('无报关单号，无法下载文件!')
				return false
			}
			// this.handleRaxLoading = true
			downloadFileBase64(this.url.getCDPdfRtx, "退税联_" + this.record.clearanceNo + ".pdf", {
				entryId: this.record.clearanceNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在退税联文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				// this.handleRaxLoading = false
			})
		},
		/**
		 * 下载税单PDF
		 */
		async handleDownloadTax() {
			if (!this.record.clearanceNo) {
				this.$message.warning('无报关单号，无法下载税单核对单PDF文件!')
				return false
			}
			// this.handleTaxLoading = true
			downloadFileBase64('/DecHead/dec-head/getCDTaxPdf', "税单_" + this.record.clearanceNo + ".pdf", {
				entryId: this.record.clearanceNo
			}).then((res) => {
				if (res.success) {
				} else {
					this.$message.warn(`导出失败!${res.message}`)
				}
			}).finally(() => {
				// this.handleTaxLoading=false
			})
		},
		/**
		 * 查看税单信息
		 */
		handleTax() {
			if (!this.record.clearanceNo) {
				this.$message.warning('无报关单号，无法查询税单信息!')
				return false
			}
			this.$refs.taxModal.open(this.record)
		},
		//报关单逻辑校验
		decLogicVerification() {
			this.decLogicVerificationLoading = true
			getAction("/DecHead/dec-head/decLogicVerification",
				{ id: this.record.id })
				.then((res) => {
					if (res.success) {
						const result = res.result
						if (!result || result.length == 0) {
							this.$message.success('逻辑校验全部通过')
						} else {
							this.$message.error('逻辑校验存在未通过项')
							this.dataSourceLogicVerification = result
							this.visibleLogicVerification = true
						}
					} else {
						this.$message.error('逻辑校验失败，请联系管理员')
					}
				}).finally(() => {
					this.decLogicVerificationLoading = false
				})
		},
		async validate() {
			this.decLogicVerificationLoading = true
			const preDecHeadVo = this.record
			const decListTableData = this.record.decLists
			const decContainerTableData = this.record.decContainers
			const decLicenseTable = this.record.decLicenseDocuses
			const cusIEFlag=this.record.ieFlag
			const promiseItems=this.record.promiseItmes //价格说明
			let specialRelationConfirm = "";
			let specialRelationConfirmCode = "";
			let priceImpactConfirm = "";
			let priceImpactConfirmCode = "";
			let royaltiesPaidConfirm = "";
			let royaltiesPaidConfirmCode = "";
			let formulaPriceConfirm = "";
			let formulaPriceConfirmCode = "";
			let tentativePriceConfirm = "";
			let tentativePriceConfirmCode = "";
			if (promiseItems && promiseItems.length >= 5) {
				specialRelationConfirmCode = promiseItems.substring(0, 1);
				if (specialRelationConfirmCode == "9") {
					specialRelationConfirm = "空";
				}
				else if (specialRelationConfirmCode == "0") {
					specialRelationConfirm = "否";
				}
				else if (specialRelationConfirmCode == "1") {
					specialRelationConfirm = "是";
				}
				priceImpactConfirmCode = promiseItems.substring(1, 2);
				if (priceImpactConfirmCode == "9") {
					priceImpactConfirm = "空";
				}
				else if (priceImpactConfirmCode == "0") {
					priceImpactConfirm = "否";
				}
				else if (priceImpactConfirmCode == "1") {
					priceImpactConfirm = "是";
				}
				royaltiesPaidConfirmCode = promiseItems.substring(2, 3);
				if (royaltiesPaidConfirmCode == "9") {
					royaltiesPaidConfirm = "空";
				}
				else if (royaltiesPaidConfirmCode == "0") {
					royaltiesPaidConfirm = "否";
				}
				else if (royaltiesPaidConfirmCode == "1") {
					royaltiesPaidConfirm = "是";
				}
				formulaPriceConfirmCode = promiseItems.substring(3, 4);
				if (formulaPriceConfirmCode == "9") {
					formulaPriceConfirm = "空";
				}
				else if (formulaPriceConfirmCode == "0") {
					formulaPriceConfirm = "否";
				}
				else if (formulaPriceConfirmCode == "1") {
					formulaPriceConfirm = "是";
				}
				tentativePriceConfirmCode = promiseItems.substring(4, 5);
				if (tentativePriceConfirmCode == "9") {
					tentativePriceConfirm = "空";
				}
				else if (tentativePriceConfirmCode == "0") {
					tentativePriceConfirm = "否";
				}
				else if (tentativePriceConfirmCode == "1") {
					tentativePriceConfirm = "是";
				}
			}


			var GQDMData=sessionStorage.getItem('GQDM')
			if (GQDMData != 'undefined' && GQDMData){
				GQDMData=JSON.parse(GQDMData)
			}

			var BZZLData=sessionStorage.getItem('erp_packages_types,name,code,isenabled=1')
			if (BZZLData != 'undefined' && BZZLData){
				BZZLData=JSON.parse(BZZLData)
			}

			var CJDWData=sessionStorage.getItem('erp_units,name,code,1=1')
			if (CJDWData != 'undefined' && CJDWData){
				CJDWData=JSON.parse(CJDWData)
			}

			var GNDQData=sessionStorage.getItem('erp_districts,name,code,del_Flag=0')
			if (GNDQData != 'undefined' && GNDQData){
				GNDQData=JSON.parse(GNDQData)
			}

			var GBDQDEC=sessionStorage.getItem('GBDQ-DEC')
			if (GBDQDEC != 'undefined' && GBDQDEC){
				GBDQDEC=JSON.parse(GBDQDEC)
				GBDQDEC=GBDQDEC.map(item => ({
					...item,
					text: item.text.split(' | ')[2]  // 提取中文部分
				}))
			}
			const enumList = [
				'JGFS', 'ZMXZ', 'GKDM', 'trading_type',
				'JGLX', 'BZDM', 'BGDLX', 'ZJMSFS',
				'XZQH', 'YT', 'JZXGG', 'SFDZDM'
			];

			const resultMap = {};
			await Promise.all(
				enumList.map(async enumitem=>{
					const res = await ajaxGetDictItems(enumitem, null);
					if (res.success){
						resultMap[enumitem] = res.result;
					}
				})
			)
			var {
				JGFS,
				ZMXZ,
				GKDM,
				trading_type,
				JGLX,
				BZDM,
				BGDLX,
				ZJMSFS,
				XZQH,
				YT,
				JZXGG,
				SFDZDM
			} = resultMap

			GKDM=GKDM.map(item=>({
				...item,
				text: item.text.split('|')[0]
			}))

			// var GetDictJGFS =await ajaxGetDictItems('JGFS', null)
			// const JGFS=GetDictJGFS.result
			//
			// var GetDictZMXZ =await ajaxGetDictItems('ZMXZ', null)
			// const ZMXZ=GetDictZMXZ.result
			//
			// var GetDictGKDM =await ajaxGetDictItems('GKDM', null)
			// const GKDM=GetDictGKDM.result
			//
			// var CJFSDict =await ajaxGetDictItems('trading_type', null)
			// const CJFS=CJFSDict.result
			//
			// var JGLXDict =await ajaxGetDictItems('JGLX', null)
			// const JGLX=JGLXDict.result
			//
			// var BZDMDict =await ajaxGetDictItems('BZDM', null)
			// const BZDM=BZDMDict.result
			//
			// var BGDLXDict =await ajaxGetDictItems('BGDLX', null)
			// const BGDLX=BGDLXDict.result
			//
			// var ZJMSFSDict =await ajaxGetDictItems('ZJMSFS', null)
			// const ZJMSFS=ZJMSFSDict.result
			//
			// var XZQHDict =await ajaxGetDictItems('XZQH', null)
			// const XZQH=XZQHDict.result
			//
			// var YTDict =await ajaxGetDictItems('YT', null)
			// const YT=YTDict.result
			//
			// var JZXGGDict =await ajaxGetDictItems('JZXGG', null)
			// const JZXGG=JZXGGDict.result
			//
			// var SFDZDMDict =await ajaxGetDictItems('SFDZDM', null)
			// const SFDZDM=SFDZDMDict.result



			const ysfs=this.$refs['detailsInfoLeft'].dictOptions.ysfs


			var param = {
				"id": "12345678",
				"declarationContentDto": {
					"header": {},
					"productList": [],
					"containerList": [],
					"licenseList": [],
					"otherPackList": [],
					"trnContainer": [],
					"trnContaGoodsList": []
				},
				"customsDeclarationType": cusIEFlag == "I" ? "importDeclaration" : "exportDeclaration"
			};
			let header = {
				"declarationCustomsName": IsEmptyToNull(GetText(preDecHeadVo.declarePlace,GQDMData)),//申报地海关
				"declarationCustomsCode": IsEmptyToNull(preDecHeadVo.declarePlace),
				"declarationState": "草稿",
				"entryCustomsName": IsEmptyToNull(GetText(preDecHeadVo.outPortCode,GQDMData)),//出境关别
				"entryCustomsCode": IsEmptyToNull(preDecHeadVo.outPortCode),
				"manualNo": IsEmptyToNull(preDecHeadVo.recordNumber),//备案号
				"contractNo": IsEmptyToNull(preDecHeadVo.contrNo),
				"domesticConsignorSccCode": IsEmptyToNull(preDecHeadVo.optUnitSocialCode),
				"domesticConsignorCustomsCode": IsEmptyToNull(preDecHeadVo.optUnitId),
				"domesticConsignorCiqCode": IsEmptyToNull(preDecHeadVo.tradeCiqCode),
				"domesticConsignorName": IsEmptyToNull(preDecHeadVo.optUnitName ),
				"overseaConsigneeEnglishName": IsEmptyToNull(preDecHeadVo.overseasConsignorEname),
				"manufacturerOrSellerSccCode": IsEmptyToNull(preDecHeadVo.deliverUnitSocialCode),
				"manufacturerOrSellerCustomsCode": IsEmptyToNull(preDecHeadVo.deliverUnit),
				"manufacturerOrSellerCiqCode": IsEmptyToNull(preDecHeadVo.ownerCiqCode),
				"manufacturerOrSellerName": IsEmptyToNull(preDecHeadVo.deliverUnitName),
				"agentSccCode": IsEmptyToNull(preDecHeadVo.declareUnitSocialCode),
				"agentCustomsCode": IsEmptyToNull(preDecHeadVo.declareUnit),
				"agentCiqCode": IsEmptyToNull(preDecHeadVo.declCiqCode),
				"agentName": IsEmptyToNull(preDecHeadVo.declareUnitName),
				"trafficModeName": IsEmptyToNull(GetText(preDecHeadVo.shipTypeCode,ysfs)),
				"trafficModeCode": IsEmptyToNull(preDecHeadVo.shipTypeCode),//运输方式
				"trafficName": IsEmptyToNull(preDecHeadVo.shipName),
				"cusVoyageNo": IsEmptyToNull(preDecHeadVo.voyage),
				"billNo": IsEmptyToNull(preDecHeadVo.billCode),
				"licenseNo": IsEmptyToNull(preDecHeadVo.licenceNumber),
				"supervisionModeName": IsEmptyToNull(GetText(preDecHeadVo.tradeTypeCode,JGFS)),
				"supervisionModeCode": IsEmptyToNull(preDecHeadVo.tradeTypeCode),//监管方式
				"levyExemptionKindName": IsEmptyToNull(GetText(preDecHeadVo.taxTypeCode,ZMXZ)),
				"levyExemptionKindCode": IsEmptyToNull(preDecHeadVo.taxTypeCode),
				"destinationCountryName": IsEmptyToNull(GetText(preDecHeadVo.arrivalArea,GBDQDEC)),
				"destinationCountryCode": IsEmptyToNull(preDecHeadVo.arrivalArea),//启运国
				"destinationPortName": IsEmptyToNull(GetText(preDecHeadVo.desPort,GKDM)),
				"destinationPortCode": IsEmptyToNull(preDecHeadVo.desPort),//经停港
				"transactionMode": IsEmptyToNull(GetText(preDecHeadVo.termsTypeCode,trading_type)),
				"transactionModeCode": IsEmptyToNull(preDecHeadVo.termsTypeCode),//成交方式
				"freightFeeMarkName": IsEmptyToNull(GetText(preDecHeadVo.shipFeeCode,JGLX)),
				"freightFeeMarkCode": IsEmptyToNull(preDecHeadVo.shipFeeCode),
				"freightFeePriceOrRate": IsEmptyToNull(preDecHeadVo.shipFee),
				"freightFeeCurrencyName": IsEmptyToNull(GetText(preDecHeadVo.shipCurrencyCode,BZDM)),
				"freightFeeCurrencyCode": IsEmptyToNull(preDecHeadVo.shipCurrencyCode),//运费币制
				"insuranceFeeMarkName": IsEmptyToNull(GetText(preDecHeadVo.insuranceCode,JGLX)),
				"insuranceFeeMarkCode": IsEmptyToNull(preDecHeadVo.insuranceCode),
				"insuranceFeePriceOrRate": IsEmptyToNull(preDecHeadVo.insurance),
				"insuranceFeeCurrencyName": IsEmptyToNull(GetText(preDecHeadVo.insuranceCurr,BZDM)),
				"insuranceFeeCurrencyCode": IsEmptyToNull(preDecHeadVo.insuranceCurr),
				"otherFeeMarkName": IsEmptyToNull(GetText(preDecHeadVo.extrasCode,JGLX)),
				"otherFeeMarkCode": IsEmptyToNull(preDecHeadVo.extrasCode),
				"otherFeePriceOrRate": IsEmptyToNull(preDecHeadVo.extras),
				"otherFeeCurrencyName": IsEmptyToNull(GetText(preDecHeadVo.otherCurr,BZDM)),
				"otherFeeCurrencyCode": IsEmptyToNull(preDecHeadVo.otherCurr),
				"PackagesQuantity": IsEmptyToNull(preDecHeadVo.packs),
				"packagesTypeName": IsEmptyToNull(GetText(preDecHeadVo.packsKinds,BZZLData)),
				"packagesTypeCode": IsEmptyToNull(preDecHeadVo.packsKinds),
				"grossWeight": IsEmptyToNull(preDecHeadVo.grossWeight),
				"netWeight": IsEmptyToNull(preDecHeadVo.netWeight),
				"tradingNationName": IsEmptyToNull(GetText(preDecHeadVo.tradeCountry,GBDQDEC)),
				"tradingNationCode": IsEmptyToNull(preDecHeadVo.tradeCountry),
				"entryPortCode": IsEmptyToNull(preDecHeadVo.despPortCode),
				"entryPortName": IsEmptyToNull(GetText(preDecHeadVo.despPortCode,GKDM)),
				"customsDeclarationTypeName": IsEmptyToNull(GetText(preDecHeadVo.clearanceType,BGDLX)),
				"customsDeclarationTypeCode": IsEmptyToNull(preDecHeadVo.clearanceType),
				"markNo": IsEmptyToNull(preDecHeadVo.markNo),
				"specialRelationConfirm": IsEmptyToNull(specialRelationConfirm),
				"specialRelationConfirmCode": IsEmptyToNull(specialRelationConfirmCode),
				"priceImpactConfirm": IsEmptyToNull(priceImpactConfirm),
				"priceImpactConfirmCode": IsEmptyToNull(priceImpactConfirmCode),
				"royaltiesPaidConfirm": IsEmptyToNull(royaltiesPaidConfirm),
				"royaltiesPaidConfirmCode": IsEmptyToNull(royaltiesPaidConfirmCode),
				"formulaPriceConfirmCode": IsEmptyToNull(formulaPriceConfirmCode),
				"tentativePriceConfirmCode": IsEmptyToNull(tentativePriceConfirmCode)
			};
			let productList = [];
			let containerList = [];
			let licenseList = [];
			for (var i = 0; i < decListTableData.length; i++) {
				let productInfo = {
					"lineNo": IsEmptyToNull(decListTableData[i].item),
					"goodsHsCode": IsEmptyToNull(decListTableData[i].hscode),
					"goodsName": IsEmptyToNull(decListTableData[i].hsname),
					"goodsModel": IsEmptyToNull(decListTableData[i].hsmodel),
					"goodsModelDetail": IsEmptyToNull(decListTableData[i].hsmodel),
					"transactionQuantity": IsEmptyToNull(decListTableData[i].goodsCount),
					"transactionUnitName": IsEmptyToNull(GetText(decListTableData[i].unitCode,CJDWData)),
					"transactionUnitCode": IsEmptyToNull(decListTableData[i].unitCode),//成交单位
					"transactionUnitPrice": IsEmptyToNull(decListTableData[i].price),
					"transactionTotalPrice": IsEmptyToNull(decListTableData[i].total),
					"transactionCurrencyName": IsEmptyToNull(GetText(decListTableData[i].currencyCode,BZDM)),
					"transactionCurrencyCode": IsEmptyToNull(decListTableData[i].currencyCode),
					"originCountryName": IsEmptyToNull(GetText(decListTableData[i].desCountry,GBDQDEC)),
					"originCountryCode": IsEmptyToNull(decListTableData[i].desCountry),//原产国
					"finalDestinationCountryName": IsEmptyToNull(GetText(decListTableData[i].destinationCountry,GBDQDEC)),
					"finalDestinationCountryCode": IsEmptyToNull(decListTableData[i].destinationCountry),
					"levyExemptionModeName": IsEmptyToNull(GetText(decListTableData[i].faxTypeCode,ZJMSFS)),
					"levyExemptionModeCode": IsEmptyToNull(decListTableData[i].faxTypeCode),//征免方式
					"legalFirstQuantity": IsEmptyToNull(decListTableData[i].count1),
					"legalFirstUnitName": IsEmptyToNull(GetText(decListTableData[i].unit1,CJDWData)),
					"legalFirstUnitCode": IsEmptyToNull(decListTableData[i].unit1),
					"legalSecondQuantity": IsEmptyToNull(decListTableData[i].count2),
					"legalSecondUnitName": IsEmptyToNull(GetText(decListTableData[i].unit2,CJDWData)),
					"legalSecondUnitCode": IsEmptyToNull(decListTableData[i].unit2),
					"supplyPlaceName": IsEmptyToNull(GetText(decListTableData[i].districtCode,GNDQData)),
					"supplyPlaceCode": IsEmptyToNull(decListTableData[i].districtCode),
					"originPlaceName": IsEmptyToNull(GetText(decListTableData[i].destCode,XZQH)),
					"originPlaceCode": IsEmptyToNull(decListTableData[i].destCode),//产地代码
					"purposeName": IsEmptyToNull(GetText(decListTableData[i].purpose,YT)),
					"purpose": IsEmptyToNull(decListTableData[i].purpose)
				};
				productList.push(productInfo);
			}
			for (var i = 0; i < decContainerTableData.length; i++) {
				let containerInfo = {
					"lineNo": IsEmptyToNull(i+1),
					"containerNumber": IsEmptyToNull(decContainerTableData[i].containerId),
					"containerModelName": IsEmptyToNull(GetText(decContainerTableData[i].containerMd,JZXGG)),
					"containerModel": IsEmptyToNull(decContainerTableData[i].containerMd),
					"containerTareWeight": IsEmptyToNull(decContainerTableData[i].goodsContaWt),
					"lclFlagName": IsEmptyToNull(decContainerTableData[i].lclFlag?
						(decContainerTableData[i].lclFlag=='0'?'否':'是'):null
					),
					"lclFlag": IsEmptyToNull(decContainerTableData[i].lclFlag),
					"productLineNoList": IsEmptyToNull(decContainerTableData[i].goodsNo)
				};
				containerList.push(containerInfo);
			}
			for (var i = 0; i < decLicenseTable.length; i++) {
				let licenseInfo = {
					"lineNo": IsEmptyToNull(i+1),
					"licenseTypeName": IsEmptyToNull(GetText(decLicenseTable[i].docuCode,SFDZDM)),
					"licenseTypeCode": IsEmptyToNull(decLicenseTable[i].docuCode),
					"licenseNumber": IsEmptyToNull(decLicenseTable[i].certCode)
				};
				licenseList.push(licenseInfo);
			}
			param["declarationContentDto"]["header"] = header;
			param["declarationContentDto"]["productList"] = productList;
			param["declarationContentDto"]["containerList"] = containerList;
			param["declarationContentDto"]["licenseList"] = licenseList;

			const params = new URLSearchParams();
			params.append('param', "json=" + JSON.stringify(param));
			const validateRes = await Ydt_validate(params).catch(reason => this.$message.error(reason));

			this.decLogicVerificationLoading = false
			if(validateRes.code!=200){
				this.$message.error(validateRes.message)
				return
			}
			var res=JSON.parse(validateRes.result)
			if (res.code == 0){
				if (
					(!res.data.invalidCheckItemList
						&& !res.data.lclCheckItemList
						&& !res.data.recommendedCheckItemList
						&& !res.data.requiredCheckItemList) ||
					(res.data.invalidCheckItemList.length == 0
						&& res.data.lclCheckItemList.length == 0
						&& res.data.recommendedCheckItemList.length == 0
						&& res.data.requiredCheckItemList.length == 0)
				) {
					this.$message.error("逻辑校验通过");
				}else {
					this.Ydt_validateData=res.data
					this.visibleYdt_validate=true
				}

			}else {
				this.$message.error('逻辑校验失败，请联系管理员')
			}

			console.log('@@',res)
		},
		// 两步申报确认
		handleOkTwoStep() {
			//申报类型

			if (this.dectypeYcrrModal == '1') {
				this.dectypeYcrr = "概要申报"
			} else if (this.dectypeYcrrModal == '2') {
				this.dectypeYcrr = "完整申报"
			} else if (this.dectypeYcrrModal == '3') {
				this.dectypeYcrr = "一次录入"
			}
			//字段赋值
			let dectypeYcrrStr = ''
			if (this.record.decType != null && this.record.decType != '') {
				dectypeYcrrStr = this.record.decType.replace(/[^a-zA-Z]/g, '')
				if (dectypeYcrrStr == "") {
					dectypeYcrrStr = " " + " "
				} else if (dectypeYcrrStr.length == 1) {
					dectypeYcrrStr = dectypeYcrrStr + " "
				}
			} else {
				dectypeYcrrStr = " " + " "
			}
			if (!!this.dectypeYcrrModal) {
				dectypeYcrrStr = dectypeYcrrStr + this.dectypeYcrrModal

				if (this.dectypeYcrrModal != 0) {
					if (this.dectypeSzNum != '' || this.dectypeSzNum == 0) {
						dectypeYcrrStr = dectypeYcrrStr + this.dectypeSzNum
					} else {
						dectypeYcrrStr = dectypeYcrrStr + 0
					}

					if (this.dectypeSjNum != '' || this.dectypeSjNum == 0) {
						dectypeYcrrStr = dectypeYcrrStr + this.dectypeSjNum
					} else {
						dectypeYcrrStr = dectypeYcrrStr + 0
					}

					if (this.dectypeSsNum != '' || this.dectypeSsNum == 0) {
						dectypeYcrrStr = dectypeYcrrStr + this.dectypeSsNum
					} else {
						dectypeYcrrStr = dectypeYcrrStr + 0
					}
				}
			}
			this.record.decType = dectypeYcrrStr

			// }
			this.decTypesTwo = !this.decTypesTwo

			this.decTypeState = !this.decTypeState
			// this.record.profileType = this.dectypeYcrrModal
			this.record.profileType = "1"
		},
		startDecTypeClick() {
			this.dectypeYcrrModal = '3'
			this.dectypeSzNum = 0
			this.dectypeSjNum = 0
			this.dectypeSsNum = 0
			this.decTypesTwo = !this.decTypesTwo
		},
		offDecTypeClick() {
			let that = this
			that.$confirm({
				title: '操作确认',
				content: '确定取消两步申报',
				onOk: function () {
					that.handleReject()
				},
				onCancel: function () {
				}
			})
		},
		//取消两步申报
		handleReject() {
			this.dectypeSzNum = 3

			this.dectypeSjNum = 3

			this.dectypeSsNum = 3

			this.dectypeYcrrModal = ''
			this.dectypeYcrr = ''
			this.record.decType
			//字段赋值
			if (this.record.decType != null && this.record.decType != '') {

				if (this.record.decType.length >= 2) {
					this.record.decType = this.record.decType.replace(/[^a-zA-Z]/g, '')
				}
			}
			this.decTypeState = !this.decTypeState
			this.record.profileType = ""
		},
		/**
		 * 复制报关单
		 */
		copyDecHead() {
			let record = this.record
			let imSignId = ''
			if (record.ieFlag === 'I') {
				imSignId = '1'
				this.$router.push({
					path: '/Business/customs-declaration/details_I',
					query: {
						id: record.id,
						imSignId: imSignId,
						isCopy: true
					}
				})
			} else if (record.ieFlag === 'E') {
				imSignId = '2'
				this.$router.push({
					path: '/Business/customs-declaration/details_E',
					query: {
						id: record.id,
						imSignId: imSignId,
						isCopy: true
					}
				})
			}
		},
		/**
		 * 初始化数据
		 * @returns {Promise<void>}
		 */
		async handleInit() {
			let map = new Map()
			if (this.$route.path.indexOf('?') > -1) {
				map = this.handleRouterUrl(this.$route.path)
				this.$route.query.imSignId = map.get('imSignId')
				this.$route.query.decNew = map.get('decNew')
				this.$route.query.dclTrnRelFlag = map.get('dclTrnRelFlag')
			}
			let imSignStr = ''
			//判断进出口页面显示
			if (!!this.$route.query.imSignId) {
				this.record.imSign = this.$route.query.imSignId
				if (this.record.imSign === '1') {
					imSignStr = '进口'
					this.record.ieFlag = 'I'
				} else if (this.record.imSign === '2') {
					imSignStr = '出口'
					this.record.ieFlag = 'E'
				}
			}
			if (this.$route.query.dclTrnRelFlag) {
				this.record.dclTrnRelFlag = this.$route.query.dclTrnRelFlag
			}
			//市场采购默认出口
			if (this.$route.query.isMarketProcurement) {
				this.record.ieFlag = 'E'
			}
			this.record.orderProtocolNo = this.$route.query.orderProtocolNo
			if (!!this.$route.query.id || !!this.record.id) {
				const res = await getDecById(!!this.$route.query.id ? this.$route.query.id : this.record.id).catch(reason => this.$message.error('加载信息失败'))
				if (res.code != 200) {
					this.$message.error(res.message)
					return
				}
				this.record = res.result
				// 获取数据后进行AI校验
				if (this.record.isAi) {
					this.aiValidateRecord(false)
				}
				//赋值消费字段
				if (this.record.promiseItmes) {
					const promiseItmesList = this.record.promiseItmes.split('|')
					if (promiseItmesList.length == 6) {
						this.record.tmp6 = promiseItmesList[5]
					}
				}
				this.sourceRecord = JSON.parse(JSON.stringify(this.record))
				//报关单顶部显示
				if (this.record.ieFlag === 'I') {
					imSignStr = '进口'
				} else if (this.record.ieFlag === 'E') {
					imSignStr = '出口'
				}
				//两步申报加载显示
				this.decTopShow()
				// this.decTitle = imSignStr + '报关单 ' + (this.$route.query.isCopy ? 'NEW' : this.record.id)
				this.decTitle = imSignStr + '报关单 ' + (this.$route.query.isCopy ? 'NEW' : '')
				if (!this.record.dclTrnRelFlag) {
					this.record.dclTrnRelFlag = '0'
				}
				//报关单类型转换
				if (this.record.dclTrnRelFlag) {
					switch (this.record.dclTrnRelFlag) {
						case '0':
							this.record.dclTrnRelFlagName = '一般报关单'
							break
						case '1':
							this.record.dclTrnRelFlagName = '转关提前报关单'
							break
						case '2':
							this.record.dclTrnRelFlagName = '备案清单'
							break
						case '3':
							this.record.dclTrnRelFlagName = '转关提前备案清单'
							break
						case '4':
							this.record.dclTrnRelFlagName = '出口二次转关'
							break
					}
				}

				// 2024/1/24 8:54@ZHANGCHAO 追加/变更/完善：复制报关单！！
				if (this.$route.query.isCopy) {
					//表头信息
					this.record.id = ""
					this.record.backup = "" //是否备份
					this.record.synchronismDate = "" //同步日期
					this.record.synchronism = "" //是否同步
					this.record.etpsInnerInvtNo = ""//企业内部清单编号
					this.record.status = ""//报关单状态
					this.record.audited = ""//是否审核
					this.record.createDate = ""//录入日期
					this.record.createPerson = ""//录入人员
					this.record.seqNo = ""//数据中心统一编号
					this.record.clearanceNo = ""//报关单号CLEARANCE_NO
					this.record.customsCode = ""// 海关编号(客户端编号)CUSTOMS_CODE
					this.record.hawb = ""// 	分运单号HAWB
					this.record.appDate = ""// 	申报日期APP_DATE
					this.record.invId = ""// 	删除核注单关联id
					this.record.accountNo = ""// 	删除台账号
					this.record.initialReviewStatus = '0'//初复审状态
					this.record.firstTrialBy = ''//初审人
					this.record.firstTrialDate = ''//初审时间
					this.record.reviewBy = ''//复审人
					this.record.reviewDate = ''//复审时间
					this.record.doNotPush = ""//不再推送报关单
					this.record.isSynchronizeTax = "0"//是否同步税单标志
					this.record.hasCd = '0'//是否有舱单
					this.record.hasYd = '0'//是否有运抵
					if (this.record.ieFlag == "I") {
						let curDate = new Date();
						let curYear = curDate.getFullYear(); //获取完整的年份(4位,1970-????)
						let curMonth = curDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
						let curDay = curDate.getDate(); //获取当前日(1-31)
						if (curMonth.toString().length == 1) {
							curMonth = "0" + curMonth.toString()
						}
						if (curDay.toString().length == 1) {
							curDay = "0" + curDay.toString()
						}
						this.record.outDate = curYear.toString() + curMonth + curDay// 	进出口日期 (默认当天)OUT_DATE
					} else if (this.record.ieFlag == "E") {
						this.record.outDate = ""// 	进出口日期 (默认当天)OUT_DATE
					}
					this.record.applyId = ""// 	旧业务编号APPLY_ID
					this.record.partId = ""// 	 ELEC_DEL_NO
					this.record.elecDelNo = ""// ELEC_DEL_NO
					this.record.send = "0"        // SEND
					this.record.decStatus = ""
					this.record.updateBy = ""//最后修改人
					this.record.updateDate = ""//最后修改日期
					this.record.decPushStatus = 0//清空推送标志
					this.record.pushStatus = 0//清空推送标志
					this.record.guaranteeNo = ""//清空保函编号GUARANTEE_NO
					if (this.record.profileType == '2') {
						this.record.profileType = '1'
					}
					//表体数据
					for (let i = 0; i < this.record.decLists.length; i++) {
						this.record.decLists[i].applyId = ""
						this.record.decLists[i].mergeSequence = ""
						this.record.decLists[i].goodsId = ""
						this.record.decLists[i].hstype = ""
						this.record.decLists[i].supvModecd = ""
						this.record.decLists[i].netWeight = ""
						this.record.decLists[i].grossWeight = ""
						this.record.decLists[i].opt = "I"
						if (!!this.record.decLists[i].decEcoRelation) {
							this.record.decLists[i].decEcoRelation.opt = "I"
							this.record.decLists[i].decEcoRelation.id = ''
							this.record.decLists[i].decEcoRelation.decId = this.record.id
						}
					}
					//集装箱
					for (let i = 0; i < this.record.decContainers.length; i++) {
						this.record.decContainers[i].id = ""
						this.record.decContainers[i].opt = "I"
					}
					//单证代码
					for (let i = 0; i < this.record.decLicenseDocuses.length; i++) {
						this.record.decLicenseDocuses[i].id = ""
						this.record.decLicenseDocuses[i].opt = "I"
					}
				}
			} else if (this.$route.query.decNew) {
				const enterpriseInfo = Vue.ls.get(ENTERPRISE_INFO)
				this.record.declareUnitSocialCode = enterpriseInfo.unifiedSocialCreditCode //申报单位18位社会信用代码
				this.record.declareUnit = enterpriseInfo.customsDeclarationCode //申报单位10位海关代码
				this.record.declareUnitName = enterpriseInfo.enterpriseFullName //申报单位名称

				if (this.$route.query.orderProtocolNo) {
					getAction("/export/orderInfo/generateDecHeadByOrderNo", { orderInfoNo: this.$route.query.orderProtocolNo })
						.then((res) => {
							if (res.success) {
								this.record = res.result
								if (!this.isEmpty(this.$route.query.dclTrnRelFlag)) {
									this.record.dclTrnRelFlag = this.$route.query.dclTrnRelFlag
									switch (this.record.dclTrnRelFlag) {
										case '0':
											this.record.dclTrnRelFlagName = '一般报关单'
											break
										case '1':
											this.record.dclTrnRelFlagName = '转关提前报关单'
											break
										case '2':
											this.record.dclTrnRelFlagName = '备案清单'
											break
										case '3':
											this.record.dclTrnRelFlagName = '转关提前备案清单'
											break
										case '4':
											this.record.dclTrnRelFlagName = '出口二次转关'
											break
									}
								}
								this.record.orderId = res.result ? res.result.orderId : ''
								//商品库价格检查未通过时弹框提醒
								if (res.message) {
									this.$warning({
										title: '报关单商品库价格检查异常',
										content: res.message,
									});
								}
							}
						})
				}
				this.decTitle = imSignStr + '报关单 新增'
				this.record.markNo = 'N/M'
				let curDate = new Date();
				let curYear = curDate.getFullYear();  //获取完整的年份(4位,1970-????)
				let curMonth = curDate.getMonth() + 1;  //获取当前月份(0-11,0代表1月)
				let curDay = curDate.getDate(); //获取当前日(1-31)
				if (curMonth.toString().length == 1) {
					curMonth = "0" + curMonth.toString()
				}
				if (curDay.toString().length == 1) {
					curDay = "0" + curDay.toString()
				}
				if (this.record.imSign === '1') {
					this.record.outDate = curYear.toString() + curMonth + curDay // 	进出口日期 (默认当天)OUT_DATE
				}
				if (!this.record.dclTrnRelFlag) {
					this.record.dclTrnRelFlag = '0'
				}
				/**
				 * 如果报关制单地区选择的为烟台三站，
				 * 默认报关单申报地海关4201，运输方式2水路运输，
				 监管方式 1039市场采购，征免性质101一般征税，境内货源地37069-烟台其他，报关类型：M通关无纸化，备注不退税
				 */
				if (this.$route.query.district == 1) {
					this.record.declarePlace = '4201' //申报地海关
					this.record.shipTypeCode = '2' //运输方式
					this.record.tradeTypeCode = '1039' //运输方式
					this.record.taxTypeCode = '101' //运输方式
					this.record.clearanceType = 'M' //报关类型
					this.record.markNumber = '不退税' //备注
				} else {

					this.record.clearanceType = 'M' //报关类型
				}
				//市场采购根据选择的代理商带出境内收发货人和生产销售单位
				// if (this.$route.query.isMarketProcurement) {
				// 	getAction("/customer/commissioner/queryById",
				// 		{ id: this.$route.query.commissionerId })
				// 		.then((res) => {
				// 			if (res.success) {
				// 				console.log(res.result)
				// 				const commissioner = res.result
				// 				this.record.optUnitSocialCode = commissioner.unifiedSocialCreditCode //境内收货人18位社会信用代码
				// 				this.record.optUnitId = commissioner.departcd //境内收货人10位海关代码
				// 				this.record.optUnitName = commissioner.commissionerFullName //境内收货人企业名称(中文)
				// 				this.record.deliverUnitSocialCode = commissioner.unifiedSocialCreditCode //生产销售单位18位社会信用代码
				// 				this.record.deliverUnit = commissioner.departcd //生产销售单位10位海关代码
				// 				this.record.deliverUnitName = commissioner.commissionerFullName //生产销售单位企业名称(中文)
				// 			}
				// 		})
				// 	//申报单位默认当前登录企业
				// 	const enterpriseInfo = Vue.ls.get(ENTERPRISE_INFO)
				// 	this.record.declareUnitSocialCode = enterpriseInfo.unifiedSocialCreditCode //申报单位18位社会信用代码
				// 	this.record.declareUnit = enterpriseInfo.customsDeclarationCode //申报单位10位海关代码
				// 	this.record.declareUnitName = enterpriseInfo.enterpriseFullName //申报单位名称
				// }
				//根据路由渲染报关单类型
				if (this.$route.query.dclTrnRelFlag) {
					console.log(this.$route.query.dclTrnRelFlag)
					this.record.dclTrnRelFlag = this.$route.query.dclTrnRelFlag
					switch (this.record.dclTrnRelFlag) {
						case '0':
							this.record.dclTrnRelFlagName = '一般报关单'
							break
						case '1':
							this.record.dclTrnRelFlagName = '转关提前报关单'
							break
						case '2':
							this.record.dclTrnRelFlagName = '备案清单'
							console.log(this.record.dclTrnRelFlagName)
							break
						case '3':
							this.record.dclTrnRelFlagName = '转关提前备案清单'
							break
						case '4':
							this.record.dclTrnRelFlagName = '出口二次转关'
							break
					}
				}

			}
		},
		// AI校验报关单数据
		aiValidateRecord(flag) {
			validateAndShowResult(this.record, (errorHtml) => {
				if (!errorHtml || errorHtml.trim() === '') {
					// 无异常情况下显示成功提示
					this.$success({
						title: (h) => {
							return h('div', {
								style: {
									display: 'flex',
									alignItems: 'center',
									gap: '8px'
								}
							}, [
								h(JGptIcon, {
									props: {
										size: 24
									}
								}),
								h('span', `关智宝：AI检查通过`)
							])
						},
						content: '报关单数据检查无异常',
						okText: '知道了'
					});
					return;
				}
				// 简单根据文字长度估算宽度
				const textLength = errorHtml.replace(/<[^>]*>/g, '').length; // 去除HTML标签后的文字长度

				// 每个字符大约占用8-12像素，这里用10像素估算
				const estimatedWidth = textLength * 8 + 150; // 150是额外的边距和按钮空间

				// 设置最小和最大宽度限制
				const minWidth = 400;
				const maxWidth = 800; // 设置最大宽度为800px，超过就换行
				const modalWidth = Math.min(maxWidth, Math.max(minWidth, estimatedWidth));
				this.$info({
					title: (h) => {
						return h('div', {
							style: {
								display: 'flex',
								alignItems: 'center',
								gap: '8px'
							}
						}, [
							h(JGptIcon, {
								props: {
									size: 24 // 适当缩小图标尺寸
								}
							}),
							h('span', `关智宝：AI检查不通过`)
						])
					},
					// icon: null, // 关键：设置为null去除默认图标
					width: modalWidth, // 使用动态计算的宽度
					content: (h) => {
						return h('div', {
							style: {
								maxHeight: '400px', // 设置最大高度
								overflowY: 'auto',  // 垂直滚动
								paddingRight: '8px', // 避免滚动条遮挡内容
								whiteSpace: 'pre-wrap', // 保持换行并允许自动换行
								wordWrap: 'break-word', // 长单词自动换行
								wordBreak: 'break-all', // 强制换行（处理很长的连续字符）
								lineHeight: '1.5' // 设置行高，让多行文字更易读
							},
							domProps: {
								innerHTML: errorHtml
							}
						})
					},
					okText: '知道了',
					onOk() { }
				})
			}, flag);
		},
		decTopShow(imSignStr) {
			//报关单 顶部显示 进出口 id 报关单类型  两步申报模式
			let decType = this.record.decType
			debugger
			if (!!decType) {
				if (decType.length > 2) {

					let num = decType.replace(/[^0-9]/ig, "");//获取全部数字

					if (num.length >= 1) {

						const converToArray = number => [...`${number}`].map(el => parseInt(el))
						let numList = converToArray(num) //字符串转数组
						let decTypeNum1 = '' //申报模式
						let decTypeNum2 = ''  //涉检涉税涉证
						let decTypeNum3 = ''
						let decTypeNum4 = ''

						for (let i = 0; i < numList.length; i++) {
							if (i === 0) {
								decTypeNum1 = numList[0]
							}
							if (i === 1) {
								decTypeNum2 = numList[1]
							}
							if (i === 2) {
								decTypeNum3 = numList[2]
							}
							if (i === 3) {
								decTypeNum4 = numList[3]
							}
						}
						if (decTypeNum1 === 1) {
							this.dectypeYcrr = '概要申报'
							this.dectypeYcrrModal = '1'
							this.decTypeState = false
							this.decTypeNum = true
						} else if (decTypeNum1 === 2) {
							this.dectypeYcrr = '完整申报'
							this.dectypeYcrrModal = '2'
							this.decTypeState = false
							this.decTypeNum = true
						} else if (decTypeNum1 === 3) {
							this.dectypeYcrr = '一次录入'
							this.dectypeYcrrModal = '3'
							this.decTypeState = false
							this.decTypeNum = true
						}

						if (decTypeNum2 === 0) {
							this.dectypeSzNum = 0
						} else {
							this.dectypeSzNum = 1
						}

						if (decTypeNum3 === 0) {
							this.dectypeSjNum = 0
						} else {
							this.dectypeSjNum = 1
						}
						if (decTypeNum4 === 0) {
							this.dectypeSsNum = 0
						} else {
							this.dectypeSsNum = 1
						}

					} else if (num.length === 1) {
						this.initdectype()
					}
				} else {
					this.initdectype()
				}
			} else {
				this.initdectype()
			}
		},
		initdectype() {
			this.dectypeSsNum = 3
			this.dectypeSjNum = 3
			this.dectypeSzNum = 3
		},
		handleFirstTrial(key) {
			if (key.key === '1') {
				this.model = { firstOpinion: '', reviewOpinion: '' }
				if (this.record.initialReviewStatus != '0' && this.record.initialReviewStatus != '') {
					this.$message.error('只有未审核状态的才能进行初审，请重新选择!')
					return
				}
				if (this.record.decStatus && this.record.decStatus != '1') {
					this.$message.error('已申报的报关单，不允许再进行初复审，请重新选择!')
					return
				}
				this.title = '初审意见'
				this.isFirst = true
				this.visible = true
			} else if (key.key === '1-1') {
				if (this.record.initialReviewStatus != '1') {
					this.$message.error('只有已初审/未复审状态的才能取消初审!')
					return
				}
				let that = this
				that.$confirm({
					title: '操作确认',
					content: '确定要取消初审吗？',
					onOk: function () {
						that.auditLoading = true
						_postAction('/DecHead/dec-head/handleInitialReview', {
							ids: that.record.id,
							initialReviewStatus: '1-1',
							opinion: '取消初审'
						}).then(res => {
							if (res.success) {
								that.$message.success(res.message)
								that.handleInit()
							} else {
								that.$message.warning(res.message)
							}
							that.auditLoading = false
						})
					},
					onCancel: function () {
					}
				})
			}
		},
		handleReview(key) {
			if (key.key === '2') {
				if (this.record.decStatus && this.record.decStatus != '1') {
					this.$message.error('已申报的报关单，不允许再进行初复审，请重新选择!')
					return
				}
				this.model = { firstOpinion: '', reviewOpinion: '' }
				this.title = '复审意见'
				this.isFirst = false
				this.visible = true
			} else if (key.key === '2-1') {
				if (this.record.initialReviewStatus != '2') {
					this.$message.error('只有已复审状态的才能取消复审!')
					return
				}
				let that = this
				that.$confirm({
					title: '操作确认',
					content: '确定要取消复审吗？',
					onOk: function () {
						that.auditLoading = true
						_postAction('/DecHead/dec-head/handleInitialReview', {
							ids: that.record.id,
							initialReviewStatus: '2-1',
							opinion: '取消复审'
						}).then(res => {
							if (res.success) {
								that.$message.success(res.message)
								that.handleInit()
							} else {
								that.$message.warning(res.message)
							}
							that.auditLoading = false
						})
					},
					onCancel: function () {
					}
				})
			}
		},
		handleOk() {
			if (this.isFirst) {
				if (this.record.initialReviewStatus != '0' && this.record.initialReviewStatus != '') {
					this.$message.error('只有未审核状态的才能进行初审，请重新选择!')
					return
				}
			}
			if (this.record.decStatus && this.record.decStatus != '1') {
				this.$message.error('已申报的报关单，不允许再进行初复审，请重新选择!')
				return
			}
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					this.confirmLoading = true
					_postAction('/DecHead/dec-head//handleInitialReview', {
						ids: this.record.id,
						initialReviewStatus: this.isFirst ? '1' : '2',
						opinion: this.isFirst ? this.model.firstOpinion : this.model.reviewOpinion
					}).then(res => {
						if (res.success) {
							this.$message.success(res.message)
							this.handleInit()
						} else {
							this.$message.warning(res.message)
						}
						this.confirmLoading = false
						this.visible = false
					})
				}
			})
		},
		handleDelete: function () {
			/*
	申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，11查验通知，S公自用物品核准通过，
	T公自用物品退单，U公自用物品待核准）1或空可以删  其他都不能删.
	 */
			if (this.record.decStatus == 1 || !this.record.decStatus) {

			} else {
				this.$message.error('该报关单无法进行删除')
				return
			}


			var that = this
			this.$confirm({
				title: '确认删除',
				content: '是否删除数据?',
				onOk: () => {
					deleteAction("/DecHead/dec-head/delete", { id: this.id }).then((res) => {
						if (res.success) {
							//重新计算分页问题
							// that.reCalculatePage(1)
							that.$message.success(res.message)
						} else {
							that.$message.warning(res.message)
						}
					})
						.catch((e) => {
							console.log("删除报关单失败===》", e)
							// that.$message.error('删除失败！')
						})

				}
			})
		},
		async auditHandleAdd() {
			this.saveSubmitLoading = true
			let that = this
			that.handleAdd(true)
		},

		async handleAdd(decType) {
			/*
			🔺验证集装箱重量
			①20英尺标准集装箱: 最大装载量约为28吨,
②40英尺标准集装箱:1最大装载量约为30吨。
③45英尺标准集装箱:最大装载量约为33吨
420英尺高箱集装箱(High Cube): 最大装载量约为28吨。
40英尺高箱集装箱(High Cube): 最大装载量约为30吨。
⑥20英尺冷藏集装箱(Reefer): 最大装载量约为28吨
⑦40英尺冷藏集装箱(Reefer):)最大装载量约为30吨。
⑧20英尺平板集装箱(Flat Rack): 最大装载量约为28吨
20241106需求 只需要验证大小箱子即可

*/
			const sList = ['31', '23', '22', '21']
			const lList = ['32', '13', '12', '11']
			for (let dc of this.record.decContainers) {
				if (sList.indexOf(dc.containerMd) > -1 && Number(dc.goodsContaWt) > 28000) {
					this.$message.error('20英尺集装箱: 最大装载量约为28吨。请检查集装箱。')
					this.saveSubmitLoading = false
					return
				}
				if (lList.indexOf(dc.containerMd) > -1 && Number(dc.goodsContaWt) > 30000) {
					this.$message.error('40英尺集装箱: 最大装载量约为30吨。请检查集装箱。')
					this.saveSubmitLoading = false
					return
				}

				//
				// if(dc.containerMd=='21'&&Number(dc.goodsContaWt)>28000){
				// 	this.$message.error('20英尺标准集装箱: 最大装载量约为28吨。请检查集装箱。')
				// 	return
				// }
				// if(dc.containerMd=='11'&&Number(dc.goodsContaWt)>30000){
				// 	this.$message.error('40英尺标准集装箱:1最大装载量约为30吨。请检查集装箱。')
				// 	return
				// }
				// if(dc.containerMd=='22'&&Number(dc.goodsContaWt)>28000){
				// 	this.$message.error('20英尺冷藏集装箱(Reefer): 最大装载量约为28吨。请检查集装箱。')
				// 	return
				// }
				// if(dc.containerMd=='12'&&Number(dc.goodsContaWt)>30000){
				// 	this.$message.error('40英尺冷藏集装箱(Reefer):最大装载量约为30吨。请检查集装箱。')
				// 	return
				// }
			}

			let declarePlaceType = true//表头验证

			if (this.record.declarePlace == "" || this.record.declarePlace == null) {
				declarePlaceType = false
			}

			if (!declarePlaceType) {
				this.$message.warning("申报地海关为空！")
				this.decAuditType = false
			} else {
				//id 付null
				if (this.record.decLists != null) {
					for (let i = 0; i < this.record.decLists.length; i++) {
						//清空ciqName 一起清空ciqCode
						if (this.record.decLists[i].ciqName == "" || this.record.decLists[i].ciqName == null) {
							this.record.decLists[i].ciqCode = ""
						}
						//小于0的都是自己赋值的
						if (this.record.decLists[i].id < 0) {
							this.record.decLists[i].id = null
						}

					}
				}
				//小于0的都是自己赋值的

				if (!!this.record.decContainers) {
					for (let i = 0; i < this.record.decContainers.length; i++) {
						if (this.record.decContainers[i].id < 0) {
							this.record.decContainers[i].id = null
						}
					}
				}

				//小于0的都是自己赋值的
				if (!!this.record.decLicenseDocuses) {
					for (let i = 0; i < this.record.decLicenseDocuses.length; i++) {
						if (this.record.decLicenseDocuses[i].id < 0) {
							this.record.decLicenseDocuses[i].id = null
						}
					}
				}


				if (this.$route.query.isCopy) {
					this.record.send = "0"
					this.record.inputId = ''
					this.record.doNotPush = '0'
					this.record.repairFlag = '0'
					this.copyHead = true
					for (let i = 0; i < this.record.decLists.length; i++) {
						this.record.decLists[i].id = ""
						this.record.decLists[i].opt = "I"
					}
				}

				this.downType = this.record.downLinkMark

				if (this.record.decStatus == "") {
					this.record.decStatus = null
				}
				if (!!this.addCount1) {
					this.record.goodsCount = this.addCount1
				}
				if (!!this.addTotal) {
					this.record.total = this.addTotal
				}
				if (!!this.addCurrency) {
					this.record.currency = this.addCurrency
				}
				//追加实施预防性消毒逻辑，说明事项的第六位拼接
				if (this.record.promiseItmes) {
					const promiseItmesStr = this.record.promiseItmes.split('|')
					/*//处理已实施预防性消毒
					if(promiseItmesStr.length==5){
						if(this.record.tmp6){
							promiseItmesStr.push(this.record.tmp6)
						}else {
							promiseItmesStr.push('9')
						}
					}else if(promiseItmesStr.length==6){
						if(this.record.tmp6){
							promiseItmesStr[5]=this.record.tmp6
						}else {
							promiseItmesStr[5]='9'
						}
					}*/
					this.record.promiseItmes = promiseItmesStr.join('|')

				} else {
					//只录消毒，其他事项都没填
					if (this.record.tmp6) {
						this.record.promiseItmes = '9|9|9|9|9' + '|' + this.record.tmp6
					}
				}
				//20240108报关制单的添加商铺和采购商信息
				if (this.$route.query.shopsId && this.$route.query.purchaserId) {
					this.record.shopsId = this.$route.query.shopsId
					this.record.purchaserId = this.$route.query.purchaserId
				}


				const res = await saveDec(this.record).catch(reason => this.$message.error(reason));
				//decType这个字段是false则是审核不保存

				if (res.success && decType) {
					this.decAuditType = true
					this.$message.success('保存成功!')
					// this.record = res.result
					this.record.id = res.result.id
					this.record.seqNo = res.result.seqNo
					this.id = res.result.id

					if (this.$route.query.isCopy) {
						this.$route.query.isCopy = false
						if (this.record.ieFlag == 'I') {
							//普通报关单跳转 进口
							this.$tabs.close({
								to: `/Business/customs-declaration/details_I?id=${this.record.id}&imSignId=1
									&twoStep=${this.$route.query.twoStep ? true : false}`
							})
						} else {
							//普通报关单跳转 出口
							this.$tabs.close({
								to: `/Business/customs-declaration/details_E?id=${this.record.id}&imSignId=2
									&twoStep=${this.$route.query.twoStep ? true : false}`
							})
						}
						this.saveSubmitLoading = false
						return
					}
					this.$route.query.isCopy = false
					let imSignStr = ''
					if (this.record.ieFlag === 'I') {
						imSignStr = '进口'
					} else if (this.record.ieFlag === 'E') {
						imSignStr = '出口'
					}
					// this.$forceUpdate()
					this.$refs.detailsInfoLeft.changeRightBottom()

					// if (this.$route.path == '/Business/customs-declaration/details'){
					//   this.saveSubmitLoading = false
					//   return
					// }
					if (!this.$route.query.id) {

						// this.$router.push({
						//   path: '/Business/customs-declaration/details',
						//   query: {
						//     id: record.id,
						//     imSignId: record.ieFlag == 'I' ? '1' : '2'
						//   }
						// })
						if (this.record.shopsId) {
							//报关制单跳转
							this.$tabs.close({
								to: `/Business/customs-declaration/details_marketProcurement?id=${this.record.id}&imSignId=${this.record.imSign}
            &isMarketProcurement=${true}`
							})
						} else {

							if (this.record.ieFlag == 'I') {
								//普通报关单跳转 进口
								this.$tabs.close({
									to: `/Business/customs-declaration/details_I?id=${this.record.id}&imSignId=${this.record.imSign}
									&twoStep=${this.$route.query.twoStep ? true : false}`
								})
							} else {
								//普通报关单跳转 出口
								this.$tabs.close({
									to: `/Business/customs-declaration/details_E?id=${this.record.id}&imSignId=${this.record.imSign}
									&twoStep=${this.$route.query.twoStep ? true : false}`
								})
							}

						}

						/*this.$tabs.close({
							to: `/Business/customs-declaration/details?id=${this.record.id}&imSignId=${this.record.imSign}`
							}
						)*/

					}
					this.handleInit()
				} else if (!res.success) {

					this.decAuditType = false
					this.$message.error(res.message)
				}
			}
			this.saveSubmitLoading = false
		},
		focusIndexsUpadte(data) {
			this.focusIndexs = data
			//下拉以后提示正确的输入提示
			this.decPromptInput(data)
		},
		isAuditDecEditmethod(e) {

			this.isAuditDecEdit = e
		},
		focusUpadte(data) {
			//下拉以后提示正确的输入提示
			this.decPromptInput(data)
		},
		//输入提示
		decPromptInput(data) {
			if (data == 0) {
				this.decPrompt = '申报地海关：输入4位代码或名称（如‘北京海关’应输入‘0100’或‘北京海关’）'
			} else if (data == 1) {
				this.decPrompt = '进/出境关别：输入4位代码或名称（如‘北京海关’应输入‘0100’或‘北京海关’）'
			} else if (data == 2) {
				this.decPrompt = '备案号：请输入12位备案号'
			} else if (data == 3) {
				this.decPrompt = '合同协议号：请输入合同的全部字头和号码'
			} else if (data == 3.1) {
				this.decPrompt = '进（出）口日期：输入进（出）口日期，格式为‘年月日’，如：‘20180712’'
			} else if (data == 4) {
				this.decPrompt = '境内收发货人统一社会信用代码：请输入统一社会信用代码'
			} else if (data == 5) {
				this.decPrompt = '境内收发货人海关编码：境内收发货人在海关备案的10位代码'
			} else if (data == 7) {
				this.decPrompt = '境内收发货人名称：输入30个字以内海关注册单位名称'
			} else if (data == 8) {
				this.decPrompt = '境外收发货人：对于AEO互认国家（地区）企业的，编码填报AEO编码，特殊情况下无境外收发货人的，填报‘NO’'
			} else if (data == 9) {
				this.decPrompt = '境外收发货人名称（外文）：名称一般填报英文名称，检验检疫要求填报其他外文名称的，在英文名称后填报，以半角括号分隔，特殊情况下无境外收发货人的，填报‘NO’'
			} else if (data == 10) {
				this.decPrompt = '消费使用单位：请输入统一社会信用代码'
			} else if (data == 11) {
				this.decPrompt = '消费使用单位：请输入消费使用单位在海关备案的10位代码'
			} else if (data == 13) {
				this.decPrompt = '消费使用单位：输入30个字以内海关注册单位名称'
			}
			//////////////////////////////////////

			else if (data == 14) {
				this.decPrompt = '申报单位统一社会信用代码：请输入统一社会信用代码'
			} else if (data == 15) {
				this.decPrompt = '申报单位海关编码：申报单位在海关备案的10位代码'
			} else if (data == 17) {
				this.decPrompt = '申报单位名称：输入30个字以内海关注册单位名称'
			} else if (data == 18) {
				this.decPrompt = '运输方式：输入运输代码（1位）或名称'
			} else if (data == 19) {
				this.decPrompt = '运输工具名称：请输入运输工具名称，转关运输的格式为：@+载货清单号'
			} else if (data == 20) {
				this.decPrompt = '航次号：根据业务类型填写运输工具的航次编号，无实际进出境的货物不填'
			} else if (data == 21) {
				this.decPrompt = '提运单号：填报进出口货物提单或运单的编号'
			} else if (data == 22) {
				this.decPrompt = '监管方式：输入贸易代码（4位，不够请在前面补0）或名称（如，‘一般贸易’应输入‘0110’或‘一般贸易’）'
			} else if (data == 23) {
				this.decPrompt = '征免性质：输入征免性质代码（3位）或名称，可以为空'
			} else if (data == 24) {
				this.decPrompt = '许可证号：输入许可证号（许可证号格式：年-XX-顺序号，例经贸部发：00-AA-000001）'
			} else if (data == 25) {
				this.decPrompt = '启运国/运抵国(地区）：输入启运国/运抵国代码（3位）或名称'
			} else if (data == 26) {
				this.decPrompt = '经停港/指运港：输入经停港/指运港代码（6位）或名称'
			} else if (data == 27) {
				this.decPrompt = '成交方式：输入成交方式代码（成交方式代码：1-CIF,2-C&F,3-FOB,4-C&I,5-市场价,6-垫仓,7-EXW）'
			}
			//////////////////////////////////
			else if (data == 28) {
				this.decPrompt = '杂费标志：输入杂费标志：1-杂费率；3-杂费总价'
			} else if (data == 29) {
				this.decPrompt = '杂费：输入杂费/率'
			} else if (data == 30) {
				this.decPrompt = '杂费币制：输入杂费币制'
			}
			//////////
			else if (data == 37) {
				this.decPrompt = '件数：输入件数，不得填报0，散装货物填报1'
			} else if (data == 38) {
				this.decPrompt = '包装种类：输入包装种类（2位）或名称'
			} else if (data == 39) {
				this.decPrompt = '毛重：进出口货物实际毛重，计算单位为千克，不足一千克的填报为‘1’'
			} else if (data == 40) {
				this.decPrompt = '净重：进出口货物实际净重，计算单位为千克，不足一千克的填报为‘1’'
			} else if (data == 41) {
				this.decPrompt = '贸易国别(地区)：输入贸易国别（地区）代码（3位）或名称'
			} else if (data == 42) {
				this.decPrompt = '入境口岸：输入入境口岸代码（6位）或名称'
			} else if (data == 43) {
				this.decPrompt = '货物存放地点：填报货物进境后存放的场所或地点，包括海关监管作业场所、分拨仓库、定点加工厂、隔离检疫场、企业自有仓库等'
			} else if (data == 44) {
				this.decPrompt = '启运港/离境口岸：输入启运港/离境口岸代码（6位）或名称'
			} else if (data == 45) {
				this.decPrompt = '报关单类型：请输入报关单类型'
			} else if (data == 46) {
				this.decPrompt = '备注：请输入报关单的备注信息'
			}
			/////////////////////////
			else if (data == 55) {
				this.decPrompt = '检验检疫受理机关：填报提交报关单和随附单据的检验检疫机关，输入代码（6位）或名称'
			} else if (data == 56) {
				this.decPrompt = '领证机关：填报领取证单的检验检疫机关，输入代码（6位）或名称'
			} else if (data == 57) {
				this.decPrompt = '口岸检验检疫机关：填报对入境货物实施检验检疫的检验检疫机关，输入代码（6位）或名称'
			} else if (data == 58) {
				this.decPrompt = 'B/L号：填报入境货物的提货单或出库单号码。当运输方式为“航空运输”时无需填写'
			} else if (data == 59) {
				this.decPrompt = '目的地检验检疫机关：需要在目的地检验检疫机关实施检验检疫的，在本栏填写对应的检验检疫机关，输入代码（6位）或名称'
			} else if (data == 60) {
				this.decPrompt = '关联号码：录入关联号码'
			} else if (data == 61) {
				this.decPrompt = '关联理由：在下拉菜单中选择关联报关单的关联理由'
			} else if (data == 62) {
				this.decPrompt = '原箱运输：申报使用集装箱运输的货物, 根据是否原集装箱原箱运输，勾选‘是’或‘否’'
			} else {
				this.decPrompt = ""
			}

		},
		decHeadDocumentMethod() {
			let saveButton = document.getElementById('saveButton')
			let outerHTML = saveButton.outerHTML
			if (outerHTML.indexOf("display: none") > -1) {//说明隐藏了
				this.saveShow = false
			}

			this.decHeadDocument = !this.decHeadDocument
			this.$refs.decHeadAttachment.recordVal = this.record;
		},

		downDecStatusTypeAnswer(e) {

			this.downDecStatusType = e
		},

		formatterimport({ cellValue, row, column }) {
			if (cellValue == 'I') {
				return '进口'
			} else if (cellValue == 'E') {
				return '出口'
			} else {
				return ''
			}
		},
		handleCancel() {
			this.visible = false
		},
		/**
		 * 打印报关单
		 * @param flag
		 */
		handleDecPrint(flag) {
			let params = {}
			params.decId = this.record.id
			params.flag = flag
			this.printButtonLoading = true
			// 直接在浏览器中打开PDF
			openPdfInBrowser('/DecHead/dec-head/exportDecComplete', params)
				.then(res => {
					if (!res.success) {
						this.$message.warning(`导出失败! ${res.message}`)
					}
				})
				.finally(() => {
					this.printButtonLoading = false
				})
		},
		//打印下载
		handleExportDecComplete(flag) {
			let params = {}
			params.decId = this.record.id
			params.flag = flag
			this.printButtonLoading = true
			let fileName = ''
			if ('EXCEL' == flag) {
				fileName = '报关单' + (this.record.clearanceNo ? this.record.clearanceNo :
					this.record.id) + '.xlsx'
			} else if ('PDF' == flag) {
				fileName = '报关单' + (this.record.clearanceNo ? this.record.clearanceNo :
					this.record.id) + '.pdf'
			} else {
				fileName = '报关单审核单' + (this.record.clearanceNo ? this.record.clearanceNo :
					this.record.id) + '.pdf'
			}
			downloadFile('/DecHead/dec-head/exportDecComplete', fileName,
				params).then((res) => {
					if (res.success) {
					} else {
						this.$message.warn(`导出失败!${res.message}`)
					}
				}).finally(() => {
					this.printButtonLoading = false
				})

		},

		//推送
		handlePushDec() {
			let that = this
			that.$confirm({
				title: '操作确认',
				content: '确定推送该报关单？',
				onOk: function () {
					let params = {
						pushStatus: '1',
						id: that.record.id
					}
					putAction(that.url.updatePushStatusById, params)
						.then((res) => {
							if (res.success) {
								that.$message.success("推送成功")
							} else {
								that.$message.warning(res.message)
							}
						})
				},
				onCancel: function () {
				}
			})
		}

	},
	watch: {

		record: {
			handler(val) {
				//addCount2  addCount1 addGoodsCount addWeight addTotal  035 036 070
				this.record = val
				let addTotalNum = 0
				let addWeightNum = 0
				let addGoodsCountNum = 0
				let addCount1Num = 0
				let addCount2Num = 0
				if (this.record && this.record.decLists) {
					for (let i = 0; i < this.record.decLists.length; i++) {
						if (!!this.record.decLists[i].total) {
							addTotalNum = addTotalNum + Number.parseFloat(this.record.decLists[i].total)
						}
						if (!!this.record.decLists[i].goodsCount) {
							addGoodsCountNum = addGoodsCountNum + Number.parseFloat(this.record.decLists[i].goodsCount)
						}
						if (!!this.record.decLists[i].count1) {
							addCount1Num = addCount1Num + Number.parseFloat(this.record.decLists[i].count1)
						}
						if (!!this.record.decLists[i].count2) {
							addCount2Num = addCount2Num + Number.parseFloat(this.record.decLists[i].count2)
						}

						//报关单详情页面合计功能,克、吨单位需要转换为千克再累计净重！
						if (this.record.decLists[i].unit1 == '035' || this.record.decLists[i].unit1 == '036' || this.record.decLists[i].unit1 == '070') {

							if (!!this.record.decLists[i].count1) {
								if (this.record.decLists[i].unit1 == '036') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].count1) / 1000)
								} else if (this.record.decLists[i].unit1 == '035') {
									addWeightNum = addWeightNum + Number.parseFloat(this.record.decLists[i].count1)
								} else if (this.record.decLists[i].unit1 == '070') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].count1) * 1000)
								}
							}

						} else if (this.record.decLists[i].unit2 == '035' || this.record.decLists[i].unit2 == '036' || this.record.decLists[i].unit2 == '070') {

							if (!!this.record.decLists[i].count2) {
								if (this.record.decLists[i].unit2 == '036') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].count2) / 1000)
								} else if (this.record.decLists[i].unit2 == '035') {
									addWeightNum = addWeightNum + Number.parseFloat(this.record.decLists[i].count2)
								} else if (this.record.decLists[i].unit2 == '070') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].count2) * 1000)
								}
							}

						} else if (this.record.decLists[i].unitCode == '035' || this.record.decLists[i].unitCode == '036' || this.record.decLists[i].unitCode == '070') {
							if (!!this.record.decLists[i].goodsCount) {
								if (this.record.decLists[i].unitCode == '036') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].goodsCount) / 1000)
								} else if (this.record.decLists[i].unitCode == '035') {
									addWeightNum = addWeightNum + Number.parseFloat(this.record.decLists[i].goodsCount)
								} else if (this.record.decLists[i].unitCode == '070') {
									addWeightNum = addWeightNum + (Number.parseFloat(this.record.decLists[i].goodsCount) * 1000)
								}
							}
						}
					}
				}

				this.addTotal = addTotalNum.toFixed(4)
				this.addTotal = lodash.round(this.addTotal, 4)

				this.addWeight = addWeightNum.toFixed(4)
				this.addWeight = lodash.round(this.addWeight, 4)

				this.addGoodsCount = addGoodsCountNum.toFixed(4)
				this.addGoodsCount = lodash.round(this.addGoodsCount, 4)

				this.addCount1 = addCount1Num.toFixed(4)
				this.addCount1 = lodash.round(this.addCount1, 4)

				this.addCount2 = addCount2Num.toFixed(4)
				this.addCount2 = lodash.round(this.addCount2, 4)
				//总价后面放置币制
				if (this.record.decLists && this.record.decLists.length > 0) {
					this.addCurrency = this.record.decLists[0].currencyCode
				} else {
					this.addCurrency = ''
				}
			},
			deep: true
		},
	},
}

//空字符转为null
function IsEmptyToNull(value) {
	if (!value) {
		return null;
	}
	try {
		return value.replace("&", "%26");
	} catch (e) {
		console.log(e.message, value)
		return value
	}

}
function GetText(code,list){
	return list.find(p=>p.value==code)?list.find(p=>p.value==code).text:null
}
</script>

<style scoped>
>>>.ant-btn-primary {
	background-color: #096AC6;
}

>>>table.my-table {
	border-radius: 4px 4px 0 0;
	border-collapse: collapse;
	text-align: right;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 12px;
	table-layout: fixed;
}

>>>table.my-table>tr>td {
	box-sizing: border-box;
	border: 1px solid rgba(38, 38, 38, 0.3);
	padding: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 10px;
}

>>>table.my-table input {
	border: none;
	outline: none;
	height: 18px;
	padding-left: 1px;
}

>>>table.my-table .ant-select-selection {
	border: none !important;
}

.logo {
	width: 28px;
	height: 28px;
	border-radius: 4px;
	margin-right: 16px;
	vertical-align: middle;
}

>>>.ant-form-item {
	margin-bottom: 0;
}

>>>div.ant-col.ant-form-item-control-wrapper {
	display: inline-block;
}


/*用以兼容ie下fieldset-disabled无效情况*/
>>>fieldset {
	/* to set absolute position for :after content */
	position: relative;
	z-index: 2
}

/* this will 'screen' all fieldset content from clicks */
>>>fieldset[disabled]:after {
	content: ' ';
	position: absolute;
	z-index: 1;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	/* i don't know... it was necessary to set background */
	background: url(data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==);
}

>>>table.my-table tr td.simple-td {
	white-space: nowrap;

	text-overflow: ellipsis;

	overflow: hidden;
}


>>>.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
	padding: 0px 0px;
}

>>>.ant-table-tbody .ant-table-row td {
	height: 26px;
}

.applyNumberSpan {
	font-size: larger;
	vertical-align: middle;
}

.dclTrnRelFlagSpen {
	font-size: 12px;
	margin-top: 0.5%;
	padding-right: 3px;
	padding-left: 0.5px;
}

>>>button {
	font-size: 10px;
}

.dclTrnRelFlagClass {
	/*margin-left: -10px;*/
}

.dectypeSjSs {
	margin-top: 0.5%
}

.topSpan {
	margin-top: 10px;
	margin-left: 5px;
	min-height: 32px
}

.topSpanDiv {
	float: left;
}

.topSpanextra {
	text-align: right;
	margin-right: 5px;
}

.topSpanextra button {
	font-size: 13px;
}

.detailsRightDiv {
	width: 100%;
	height: 100px;
	border: 1px solid #BDBDBD;
	background-color: #E6F7FF;
	float: left;
	margin-top: 2px;
	font-size: 11px;
}

.detailsRightShopsPurchaserInfoDiv {
	width: 100%;
	height: 129px;
	border: 1px solid #BDBDBD;
	background-color: #E6F7FF;
	float: left;
	margin-top: 2px;
	font-size: 11px;
}

.detailsRightDiv>span {
	margin-left: 5px;
}

.detailsRightDiv-div {
	width: 80%;
	height: 20px;
	float: left;
}

.detailsRightDivtips {
	width: 13%;
	height: 100px;
	float: left;
	margin-left: 2px;
}
</style>
