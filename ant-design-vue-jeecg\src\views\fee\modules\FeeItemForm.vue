<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <!-- <a-card title="费用明细" size="small" :bordered="false" class="card-box"> -->
          <!-- <a-col :span="8" >
            <a-form-model-item label="费用类别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="costCategory">
              <j-dict-select-tag v-model="model.costCategory" :allowClear="true" :disabled="formDisabled"
                                 placeholder="请选择费用类别" dictCode="cost_category"/>
            </a-form-model-item>
          </a-col>
            <a-col :span="16" :push="1">
              <a-form-model-item
                label=""
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                prop="feeToWho"
                class="supervision"
              >
                <div>
                  <span id="checkBoxRequired">*</span>
                  <check-box
                    v-model="model.feeToBroker"
                    dictCode="true_or_false"
                    placeholder="请选择付报关行"
                    checkBoxType="付报关行"
                    @click="handleCheckBoxChanged"
                  />
                  <check-box
                    v-model="model.feeToLogistics"
                    dictCode="true_or_false"
                    placeholder="请选择付货代公司"
                    checkBoxType="付货代公司"
                    @click="handleCheckBoxChanged"
                  />
                   <check-box
                    v-model="model.feeToPlatform"
                    dictCode="true_or_false"
                    placeholder="请选择付平台"
                    checkBoxType="付平台"
                    @click="handleCheckBoxChanged"
                  /> 
                  <check-box
                    v-model="model.feeToOthers"
                    dictCode="true_or_false"
                    placeholder="请选择付其他"
                    checkBoxType="付其他"
                    @click="handleCheckBoxChanged"
                  />
                </div>
              </a-form-model-item>
            </a-col> -->
          <a-col :span="8" >
            <a-form-model-item label="费用项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="feeName">
              <j-remarks-component
                v-model="model.feeName"
                placeholder="请输入费用项目名称"
                :maxLength="64"
                :readOnly="formDisabled"
              />
            </a-form-model-item>
          </a-col>
            <a-col :span="8" >
              <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remarks">
                <j-remarks-component
                  placeholder="请输入备注"
                  v-model="model.remarks"
                  :maxLength="256"
                  :readOnly="formDisabled"
                />
              </a-form-model-item>
            </a-col>
          <!-- </a-card> -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import CheckBox from '@views/fee/modules/CheckBox'

export default {
  name: 'FeeItemForm',
  components: {
    CheckBox,
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      model: {
        feeToBroker: '',
        feeToLogistics: '',
        feeToPlatform: '',
        feeToOthers: '',
      },

      labelCol: {
        xs: { span: 24 },
        sm: { span: 10 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
      confirmLoading: false,
      validatorRules: {
        costCategory: [{ required: true, message: '请选择费用类别!' }],
        feeToWho: [
          { required: false, message: '请至少选中一项!' },
          {
            validator: (rule, value, callback) => {
              try {
                console.log(1444)
                console.dir(this.model)
                console.log(value)
                if (
                  this.model.feeToBroker === '1' ||
                  this.model.feeToLogistics === '1' ||
                  this.model.feeToPlatform === '1' ||
                  this.model.feeToOthers === '1'
                ) {
                  callback()
                } else {
                  throw new Error('Check Wrong!')
                }
              } catch (err) {
                callback(err)
                return
              }
            },
            message: '请至少选中一项!',
          },
        ],
        feeName: [{ required: true, message: '请输入费用项目名称!' }],

      },
      url: {
        add: '/fee/feeItem/add',
        edit: '/fee/feeItem/edit',
        queryById: '/fee/feeItem/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  updated() {},
  methods: {
    handleCheckBox() {},
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.model.feeToBroker = this.model.feeToBroker ? '1' : '0'
      this.model.feeToLogistics = this.model.feeToLogistics ? '1' : '0'
      this.model.feeToPlatform = this.model.feeToPlatform ? '1' : '0'
      this.model.feeToOthers = this.model.feeToOthers ? '1' : '0'
      this.visible = true
    },

    submitForm() {
      const that = this
      // 触发表单验证

      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }

          httpAction(httpurl, this.model, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleCheckBoxChanged() {
      this.model.feeToBroker = this.model.feeToBroker ? '1' : '0'
      this.model.feeToLogistics = this.model.feeToLogistics ? '1' : '0'
      this.model.feeToPlatform = this.model.feeToPlatform ? '1' : '0'
      this.model.feeToOthers = this.model.feeToOthers ? '1' : '0'
      console.dir(this.model)
    },
  },
}
</script>

<style scoped>
.ant-col >>> .fee_check_box {
  vertical-align: text-bottom;
  line-height: 40px;
}

/*/deep/ .ant-checkbox-wrapper {*/
/*  color: red;*/
/*}*/

#checkBoxRequired {
  color: red;
  font-size: 16px;
  vertical-align: middle;
  margin-right: 6px;
}
.ant-form-item{
  margin-bottom: 12px !important;
}
</style>
