package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.business.entity.NemsInvtList;
import org.jeecg.modules.business.entity.PtsEmsAimg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.StorageDetail;
import org.jeecg.modules.business.entity.dto.AimgMonthQtyExportDTO;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO;
import org.jeecg.modules.business.entity.paramVo.StockParamVO;
import org.jeecg.modules.business.vo.PtsEmsAimgWeightStatisticsVO;

import java.util.List;

/**
 * <p>
 * 手账册归并后料件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface PtsEmsAimgMapper extends BaseMapper<PtsEmsAimg> {

    /**
     * 查询Ems的详细信息
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAimg> listEmsDetail(Page page, EmsQueryDto emsQueryDto);

    List<PtsEmsAimg> listAimgList(String emsNo, List<?> gNoList);

    @InterceptorIgnore(tenantLine = "true")
    List<String> isDclCusFlag(String emsNo);

    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAimg> emsStatistics(String emsNo);

    PtsEmsAimg selectOneBy3Cond(StockParamVO stockParamVO);

    int updateQtySafe(StockParamVO stockParamVO);

    int addOccQtySafe(StockParamVO stockParamVO);

    int updateOccQtySafe(StockParamVO stockParamVO);

    int reduceQtySafe(StockParamVO stockParamVO);

    int reduceStockQtySafe(StockParamVO stockParamVO);

    int rectifyQtySafe(StockParamVO stockParamVO);

    int rectifyAllQtySafe(StockParamVO stockParamVO);

    int addOccAndStockQtySafe(StockParamVO stockParamVO);

    @InterceptorIgnore(tenantLine = "true")
    IPage<InventoryFlowsVO> listInventoryFlows(IPage<InventoryFlowsVO> page, InventoryFlowsVO inventoryFlowsVO);

    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAimg> listBondedWarning(IPage<PtsEmsAimg> page, EmsQueryDto emsQueryDto);

    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAimg> getBondedWarning(String type, String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    PtsEmsAimg getOneByCond(String copGno, String emsNo, String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtList> listInvtList(String key, String emsNo);
    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtList> listInvtList1(String key, String emsNo);
    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtList> listInvtList2(String key, String emsNo);
    /**
     * 获取导出月库存表数据
     * @return
     */
    List<AimgMonthQtyExportDTO> listEmsAimgByMonthQty(String tenantId);

    /**
     * 查询Ems的详细信息-报表中心显示
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAimg> listEmsAimgByReport(Page page, EmsQueryDto emsQueryDto);
    /**
     * 查询Ems的详细信息-报表中心显示
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAimg> listEmsAimgByReportC(String emsNo,String tenantId,List<Integer> gNoList);
    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAimgWeightStatisticsVO> listEmsDetailWeightStatisticsAimg(String emsNo,String tenantId);

    /**
     * 进口报税料件金额统计-报表中心显示
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAimg> listEmsDetailAimgAmountByReport(Page page, EmsQueryDto emsQueryDto);
    /**
     * 获取EMS详细信息列表单价比较
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAimg> listEmsDetailByUnitPriceComparison(Page page, EmsQueryDto emsQueryDto);

}
