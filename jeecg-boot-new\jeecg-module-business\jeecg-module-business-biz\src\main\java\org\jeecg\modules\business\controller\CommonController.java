package org.jeecg.modules.business.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.business.entity.Contract;
import org.jeecg.modules.business.entity.ErpHscodes;
import org.jeecg.modules.business.entity.SysConfig;
import org.jeecg.modules.business.entity.excel.ExcelImportEntity;
import org.jeecg.modules.business.service.IErpDistrictsService;
import org.jeecg.modules.business.service.IErpHscodesService;
import org.jeecg.modules.business.service.ISysConfigService;
import org.jeecg.modules.business.service.impl.CommonService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * 共通Controller
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/7/3 上午9:22
 */
@RestController
@RequestMapping("/common")
@Slf4j
public class CommonController {
    @Autowired
    private IErpHscodesService service;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 第三方获取Token
     *
     * @param creditCode
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/5 12:10
     */
    @GetMapping("/getThirdToken")
    public Result<?> getThirdToken(@RequestParam("creditCode") String creditCode) {
        return commonService.getThirdToken(creditCode);
    }

    /**
     * 获取系统设置
     *
     * @param configKey
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/2/28 11:53
     */
    @AutoLog(value = "获取系统设置")
    @ApiOperation(value = "获取系统设置", notes = "获取系统设置")
    @GetMapping(value = "/getSysConfig")
    public Result<?> getSysConfig(@RequestParam("configKey") String configKey) {
        return sysConfigService.getSysConfig(configKey);
    }

    /**
     * 保存系统设置
     *
     * @param sysConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/2/28 11:53
     */
    @AutoLog(value = "保存系统设置")
    @ApiOperation(value = "保存系统设置", notes = "保存系统设置")
    @PostMapping(value = "/saveSysConfig")
    public Result<?> saveSysConfig(@RequestBody SysConfig sysConfig) {
        return sysConfigService.saveSysConfig(sysConfig);
    }

    /**
     * 导入Excel
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/3 上午9:23
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
//            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(false);
            try {
                List<ExcelImportEntity> list = ExcelImportUtil.importExcel(file.getInputStream(), ExcelImportEntity.class, params);
                //update-begin-author:taoyan date:20190528 for:批量插入数据
                long start = System.currentTimeMillis();
                /********************************************数据处理START****************************************/
                List<ErpHscodes> all = service.list();
                Map<String, List<ErpHscodes>> map = all.stream().collect(Collectors.groupingBy(ErpHscodes::getHscode));
                for (ExcelImportEntity excelImport : list) {
                    List<ErpHscodes> erpHscodes = map.get(excelImport.getHscode());
                    if (isNotEmpty(erpHscodes)) {
//                        if (isNotBlank(excelImport.getBacktaxrate())) {
                            service.update(null, new LambdaUpdateWrapper<ErpHscodes>()
                                    .set(ErpHscodes::getImpcustomrate, excelImport.getImpcustomrate())
                                    .set(ErpHscodes::getImpcustomrate2, excelImport.getImpcustomrate2())
                                    .set(ErpHscodes::getAddtaxrate, excelImport.getAddtaxrate())
                                    .set(ErpHscodes::getConsumerate, excelImport.getConsumerate())
                                    .set(ErpHscodes::getImpcustomrate3, excelImport.getImpcustomrate3())
                                    .set(ErpHscodes::getExpcustomrate, excelImport.getExpcustomrate())
                                    .set(ErpHscodes::getImpcustomrate4, excelImport.getImpcustomrate4())
                                    .set(ErpHscodes::getBacktaxrate, excelImport.getBacktaxrate())
                                    .eq(ErpHscodes::getHscode, excelImport.getHscode()));
//                        }
                    }
                }
                /******************************************数据处理END******************************************/
                //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
                //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
                log.info("消耗时间{}毫秒", System.currentTimeMillis() - start);
                //update-end-author:taoyan date:20190528 for:批量插入数据
                return Result.ok("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                //update-begin-author:taoyan date:20211124 for: 导入数据重复增加提示
                String msg = e.getMessage();
                log.error(msg, e);
                if (msg != null && msg.indexOf("Duplicate entry") >= 0) {
                    return Result.error("文件导入失败:有重复数据！");
                } else {
                    return Result.error("文件导入失败:" + e.getMessage());
                }
                //update-end-author:taoyan date:20211124 for: 导入数据重复增加提示
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /**
     * 获取日志状态记录
     *
     * @param sourceId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/16 15:18
     */
    @GetMapping("/getStatusRecords")
    public Result<?> getStatusRecords(@RequestParam(value = "logType", required = false) String logType,
                                      @RequestParam(value = "sourceId", required = false) String sourceId) {
        return commonService.getStatusRecords(logType, sourceId);
    }

    /**
     * 文档逻辑校验
     *
     * @param decId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/22 14:33
     */
    @AutoLog(value = "文档逻辑校验")
    @RequestMapping(value = "/verify")
    public Result<?> verify(@RequestParam("decId") String decId,
                            @RequestParam(value = "isQz", required = false) String isQz) {
        return commonService.verify(decId, isQz);
    }

}
