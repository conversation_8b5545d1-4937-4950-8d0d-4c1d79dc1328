package org.jeecg.modules.business.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.PasswordUtil;
import org.junit.jupiter.api.Test;

/**
 * 用户测试类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/5/26 09:22
 */
@Slf4j
public class UserTest {

    @Test
    void test() {
        String passwordEncode = PasswordUtil.encrypt("zhangchao", "chao1987", "rvjuiaji");
        log.info("生成的密码是：{}", passwordEncode);
    }
}
