package org.jeecg.modules.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.AppGoodsTypeMapper;
import org.jeecg.modules.business.mapper.AppHeadTypeMapper;
import org.jeecg.modules.business.mapper.SysConfigMapper;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.IAppGoodsTypeService;
import org.jeecg.modules.business.service.IAppHeadTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.service.IAppSasAcmprListTypeService;
import org.jeecg.modules.business.service.IAppUcnsTypeService;
import org.jeecg.modules.business.util.message.AppTypeMessageUtil;
import org.jeecg.modules.business.util.message.FTPUtil;
import org.jeecg.modules.business.util.message.MessageFileUtil;
import org.jeecg.modules.business.util.message.SFTPUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;
import static org.jeecg.common.constant.CommonConstant.HAS_OWN_FTP;
import static org.jeecg.common.constant.CommonConstant.SFTP;

/**
 * <p>
 * 业务申报表表头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Slf4j
@Service
public class AppHeadTypeServiceImpl extends ServiceImpl<AppHeadTypeMapper, AppHeadType> implements IAppHeadTypeService {
    @Autowired
    private IAppGoodsTypeService appGoodsTypeService;
    @Autowired
    private IAppUcnsTypeService appUcnsTypeService;
    @Autowired
    private IAppSasAcmprListTypeService appSasAcmprListService;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendSasPath}") // /ImpPath/Sas/OutBox
//    private String remoteSendSasPath;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private FtpProperties ftpProperties;

    /**
     * 查询分页列表
     *
     * @param page        分页对象
     * @param appHeadType 应用头部类型
     * @param request     请求对象
     * @return 分页列表
     */
    @Override
    public IPage<AppHeadType> queryPageList(Page<AppHeadType> page, AppHeadType appHeadType, HttpServletRequest request) {
        IPage<AppHeadType> pageList = baseMapper.queryPageList(page, appHeadType);
        return pageList;
    }

    /**
     * 根据id获取应用头部类型
     *
     * @param id 应用头部类型id
     * @return 应用头部类型
     */
    @Override
    public Result<?> getAppHeadTypeById(String id) {
        AppHeadType appHeadType = this.getById(id);
        return Result.ok(appHeadType);
    }

    /**
     * 保存应用头部类型
     *
     * @param appHeadType 应用头部类型
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveAppHeadType(AppHeadType appHeadType) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isEmpty(appHeadType.getId())) {
            appHeadType.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            appHeadType.setCreateDate(new Date());
            if ("1".equals(appHeadType.getDclTbStucd())) {
                Long isHave = baseMapper.selectCount(new LambdaQueryWrapper<AppHeadType>()
                        .eq(AppHeadType::getDclTbStucd, "1"));
                if (isHave > 0) {
                    return Result.error("系统中已存在「正常执行」的业务申报表，请检查！");
                }
            }
            baseMapper.insert(appHeadType);
            // 编辑
        } else {
            if ("1".equals(appHeadType.getDclTbStucd())) {
                Long isHave = baseMapper.selectCount(new LambdaQueryWrapper<AppHeadType>()
                        .eq(AppHeadType::getDclTbStucd, "1")
                        .ne(AppHeadType::getId, appHeadType.getId()));
                if (isHave > 0) {
                    return Result.error("系统中已存在「正常执行」的业务申报表，请检查！");
                }
            }
            appHeadType.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            appHeadType.setUpdateDate(new Date());
            baseMapper.updateById(appHeadType);
        }
        // 处理表体
        if (isNotEmpty(appHeadType.getAppGoodsTypeList())) {
            List<AppGoodsType> oldAppGoodsList = appGoodsTypeService.list(new LambdaQueryWrapper<AppGoodsType>()
                    .eq(AppGoodsType::getHeadId, appHeadType.getId()));
            if (isNotEmpty(oldAppGoodsList)) {
                // 使用 Stream 进行过滤
                List<AppGoodsType> dels = oldAppGoodsList.stream()
                        .filter(item -> appHeadType.getAppGoodsTypeList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    appGoodsTypeService.removeBatchByIds(dels.stream().map(AppGoodsType::getId).collect(Collectors.toList()));
                }
            }
            for (AppGoodsType appGoodsType : appHeadType.getAppGoodsTypeList()) {
                if (isEmpty(appGoodsType.getId())) {
                    appGoodsType.setHeadId(appHeadType.getId());
                    appGoodsTypeService.save(appGoodsType);
                } else {
                    appGoodsType.setHeadId(appHeadType.getId());
                    appGoodsTypeService.updateById(appGoodsType);
                }
            }
        }
        // 处理单耗
        if (isNotEmpty(appHeadType.getAppUcnsTypeList())) {
            List<AppUcnsType> oldAppUcnsTypeList = appUcnsTypeService.list(new LambdaQueryWrapper<AppUcnsType>()
                    .eq(AppUcnsType::getHeadId, appHeadType.getId()));
            if (isNotEmpty(oldAppUcnsTypeList)) {
                // 使用 Stream 进行过滤
                List<AppUcnsType> dels = oldAppUcnsTypeList.stream()
                        .filter(item -> appHeadType.getAppUcnsTypeList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    appUcnsTypeService.removeBatchByIds(dels.stream().map(AppUcnsType::getId).collect(Collectors.toList()));
                }
            }
            for (AppUcnsType appUcnsType : appHeadType.getAppUcnsTypeList()) {
                if (isEmpty(appUcnsType.getId())) {
                    appUcnsType.setHeadId(appHeadType.getId());
                    appUcnsTypeService.save(appUcnsType);
                } else {
                    appUcnsType.setHeadId(appHeadType.getId());
                    appUcnsTypeService.updateById(appUcnsType);
                }
            }
        }
        // 处理随附单据
        if (isNotEmpty(appHeadType.getAppSasAcmprListTypeList())) {
            List<AppSasAcmprListType> oldAppSasAcmprListTypeList = appSasAcmprListService.list(new LambdaQueryWrapper<AppSasAcmprListType>()
                    .eq(AppSasAcmprListType::getHeadId, appHeadType.getId()));
            if (isNotEmpty(oldAppSasAcmprListTypeList)) {
                // 使用 Stream 进行过滤
                List<AppSasAcmprListType> dels = oldAppSasAcmprListTypeList.stream()
                        .filter(item -> appHeadType.getAppSasAcmprListTypeList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    appSasAcmprListService.removeBatchByIds(dels.stream().map(AppSasAcmprListType::getId).collect(Collectors.toList()));
                }
            }
            for (AppSasAcmprListType appSasAcmprListType : appHeadType.getAppSasAcmprListTypeList()) {
                if (isEmpty(appSasAcmprListType.getId())) {
                    appSasAcmprListType.setHeadId(appHeadType.getId());
                    appSasAcmprListService.save(appSasAcmprListType);
                } else {
                    appSasAcmprListType.setHeadId(appHeadType.getId());
                    appSasAcmprListService.updateById(appSasAcmprListType);
                }
            }
        }

        AppHeadType returnHead = baseMapper.selectById(appHeadType.getId());
        return Result.ok(returnHead);
    }

    /**
     * 批量发送消息。
     *
     * @param ids 以字符串形式表示的消息ID列表，多个ID之间通常使用逗号分隔。
     * @return 返回一个Result对象，其中包含了操作的结果信息，例如成功与否、错误码等。
     */
    @Override
    public Result<?> sendMessageBatch(String ids) {
        List<String> headId = Arrays.asList(ids.split(","));
        List<AppHeadType> appHeadTypes= baseMapper.selectBatchIds(headId);
        if (appHeadTypes == null || appHeadTypes.isEmpty()){
            return Result.error("未找到对应的业务申报表信息");
        }
        List<AppGoodsType> appGoodsTypes = appGoodsTypeService.list(new QueryWrapper<AppGoodsType>().lambda()
                .in(AppGoodsType::getHeadId,headId)
                .and(i->i.isNull(AppGoodsType::getSend).or().eq(AppGoodsType::getSend,false)));
        if (appGoodsTypes == null || appGoodsTypes.isEmpty()){
            return Result.error("未找到未发送的业务申报表表体信息");
        }
        Map<Long,List<AppGoodsType>> appGoodsTypeMap = new HashMap<>();
        appGoodsTypes.forEach(v->{
            if (v.getSend() == null || !v.getSend()){
                if (appGoodsTypeMap.containsKey(v.getHeadId())){
                    appGoodsTypeMap.get(v.getHeadId()).add(v);
                }else {
                    List<AppGoodsType> goodsTypes = new ArrayList<>();
                    goodsTypes.add(v);
                    appGoodsTypeMap.put(v.getHeadId(),goodsTypes);
                }
            }
        });
        String errorId = "";
        for (AppHeadType appHeadType : appHeadTypes) {
            Result<AppHeadType> appHeadTypeYmMsg = this.sendMessageByAppType(appHeadType,appGoodsTypeMap);
            if (!appHeadTypeYmMsg.isSuccess()) {
                if (isBlank(errorId)) {
                    errorId = new StringBuilder().append(appHeadTypeYmMsg.getMessage()).append(",申报表流水号：")
                            .append(appHeadType.getId()).toString();
                } else {
                    errorId = new StringBuilder().append(errorId).append(";").append(appHeadTypeYmMsg.getMessage())
                            .append(",申报表流水号：").append(appHeadType.getId()).toString();
                }
            }
        }
        if (isNotBlank(errorId)) {
            return Result.error(errorId);
        }
        return Result.ok("发送成功！");
    }

    /**
     * 根据统一编号更新状态
     *
     * @param appHeadStatus
     * @param seqNo
     * @return
     */
    @Override
    public Result<?> updateAppHeadStatusBySeqNo(String appHeadStatus, String seqNo) {
        if (isEmpty(seqNo)){
            return Result.error("统一编号不允许为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        AppHeadType appHeadType = baseMapper.selectOne(new QueryWrapper<AppHeadType>().lambda().eq(AppHeadType::getSeqNo,seqNo));
        if (appHeadType == null){
            return Result.error(String.format("未找到对应的业务申报表信息,统一编号为：%s",seqNo));
        }
        boolean result = this.update(new UpdateWrapper<AppHeadType>().lambda().set(AppHeadType::getAppHeadStatus,appHeadStatus)
                .eq(AppHeadType::getSeqNo,seqNo));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return result ? Result.ok("更新状态成功!") : Result.ok(String.format("更新状态失败,统一编号为：%s",seqNo));
    }

    /**
     * 根据回执信息更新出入库单
     *
     * @param appHeadType
     * @return
     */
    @Override
    public Result<AppHeadType> saveAppTypeForReceipt(AppHeadType appHeadType) {
        if (appHeadType == null){
            return Result.error("传入申报表信息为空");
        }
        if (isEmpty(appHeadType.getSeqNo())){
            return Result.error("传入申报表的统一编号不允许为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());

        AppHeadType oldHeadType = baseMapper.selectOne(new QueryWrapper<AppHeadType>().lambda().eq(AppHeadType::getSeqNo,appHeadType.getSeqNo()));
        if (oldHeadType == null){
            return Result.error(String.format("未找到对应的申报表信息,统一编号为：%s",appHeadType.getSeqNo()));
        }
        appHeadType.setId(oldHeadType.getId());
        appHeadType.setCreateBy(oldHeadType.getCreateBy());
//        appHeadType.setCreateDate(oldHeadType.getCreateDate());
        appHeadType.setTenantId(oldHeadType.getTenantId());


        if (appHeadType.getAppGoodsTypeList() != null && appHeadType.getAppGoodsTypeList().size()>0){
            List<Integer> sasDclSeqnos = new ArrayList<>();
            appHeadType.getAppGoodsTypeList().forEach(v->{
                sasDclSeqnos.add(v.getSasDclSeqno());
            });
            List<AppGoodsType> oldGoodsTypes= appGoodsTypeService.list(new QueryWrapper<AppGoodsType>().lambda()
                    .eq(AppGoodsType::getHeadId,oldHeadType.getId()).in(AppGoodsType::getSasDclSeqno,sasDclSeqnos));
            Map<Integer,AppGoodsType> oldGoodsMap = new HashMap<>();
            if (oldGoodsTypes != null && oldGoodsTypes.size()>0){
                oldGoodsMap = oldGoodsTypes.stream().collect(Collectors.toMap(AppGoodsType::getSasDclSeqno,a->a,(k1,k2)->k1));
            }
            for(AppGoodsType v : appHeadType.getAppGoodsTypeList()){
                v.setSend(true);
                v.setHeadId(oldHeadType.getId());

                if (oldGoodsMap.containsKey(v.getSasDclSeqno())){
                    AppGoodsType oldGoods = oldGoodsMap.get(v.getSasDclSeqno());
                    v.setId(oldGoods.getId());
                    v.setGdsMtno(isNotEmpty(v.getGdsMtno()) ? v.getGdsMtno() : oldGoods.getGdsMtno());
                }

            }

            appGoodsTypeService.saveOrUpdateBatch(appHeadType.getAppGoodsTypeList());
        }
        this.saveOrUpdate(appHeadType);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("更新成功");
    }

    public Result<AppHeadType> sendMessageByAppType(AppHeadType appHeadType, Map<Long,List<AppGoodsType>> appGoodsTypeMap){

        List<AppGoodsType> appGoodsTypes = appGoodsTypeMap.get(appHeadType.getId());
        if (appGoodsTypes == null || appGoodsTypes.isEmpty()){
            return Result.error("未找到对应的申报表表体，请核对数据");
        }
//        MsgFtpConfig ftpConfig = null;
//        ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendSasPath);
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendSasPath());
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
        String fileName = new StringBuilder(appHeadType.getId().toString()).append("-SAS001").toString();//文件名称
        boolean uploadFlag = false;
        try {
            if (CommonConstant.FTP.equals(ftpType)) {
                uploadFlag = new FTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(
                                MessageFileUtil.exportZip(
                                        AppTypeMessageUtil.generateSignature(appHeadType, appGoodsTypes)
                                        ,  String.format("%s.xml", fileName)).toByteArray()));
            } else if (SFTP.equals(ftpType)) {
                uploadFlag = new SFTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(
                                MessageFileUtil.exportZip(
                                        AppTypeMessageUtil.generateSignature(appHeadType, appGoodsTypes)
                                        ,  String.format("%s.xml", fileName)).toByteArray()));
            }
        } catch (Exception e) {
            e.getMessage();
            log.info(e.getMessage());
        }
        if (!uploadFlag) {
            return Result.error("发送失败");
        }

        List<Long> goodsTypsIds = new ArrayList<>();
        appGoodsTypes.forEach(v->{
            goodsTypsIds.add(v.getId());
        });
        appGoodsTypeService.update(new UpdateWrapper<AppGoodsType>().lambda()
                .set(AppGoodsType::getSend,true)
                .in(AppGoodsType::getId,goodsTypsIds));

        return Result.ok("发送成功!");

    }

    /**
     * 删除业务申报表
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        appGoodsTypeService.remove(new LambdaQueryWrapper<AppGoodsType>()
                .in(isNotNull(idList), AppGoodsType::getHeadId, idList));
        appUcnsTypeService.remove(new LambdaQueryWrapper<AppUcnsType>()
                .in(isNotNull(idList), AppUcnsType::getHeadId, idList));
        appSasAcmprListService.remove(new LambdaQueryWrapper<AppSasAcmprListType>()
                .in(isNotNull(idList), AppSasAcmprListType::getHeadId, idList));
        this.removeBatchByIds(idList);
        return Result.ok("删除成功！");
    }

}
