<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PtsEmsAimgMapper">

    <update id="updateQtySafe">
        UPDATE pts_ems_aimg
        SET STOCK_QTY = STOCK_QTY + #{changeQty},
            IMPORTED_QTY = IMPORTED_QTY + #{changeQty},
            VERSION = VERSION + 1
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND VERSION = #{version}
    </update>

    <update id="addOccQtySafe">
        UPDATE pts_ems_aimg
        SET OCCUPY_QTY = OCCUPY_QTY + #{changeQty}
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND STOCK_QTY >= (
            OCCUPY_QTY + #{changeQty})
    </update>

    <update id="updateOccQtySafe">
        UPDATE pts_ems_aimg
        SET OCCUPY_QTY = OCCUPY_QTY - #{changeQty}
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
    </update>

    <update id="reduceQtySafe">
        UPDATE pts_ems_aimg
        SET
            STOCK_QTY = STOCK_QTY - #{changeQty},
            OCCUPY_QTY = OCCUPY_QTY - #{occupyQty}
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND STOCK_QTY >= #{changeQty}
          AND OCCUPY_QTY >= #{occupyQty}
    </update>

    <update id="reduceStockQtySafe">
        UPDATE pts_ems_aimg
        SET
            STOCK_QTY = STOCK_QTY - #{changeQty}
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND STOCK_QTY >= #{changeQty}
    </update>

    <update id="rectifyQtySafe">
        UPDATE pts_ems_aimg
        SET STOCK_QTY = STOCK_QTY + #{changeQty},
            IMPORTED_QTY = IMPORTED_QTY + #{changeQty},
            VERSION = VERSION + 1
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND VERSION = #{version}
          AND STOCK_QTY >= #{changeQty}
    </update>

    <update id="rectifyAllQtySafe">
        UPDATE pts_ems_aimg
        SET
            STOCK_QTY = STOCK_QTY + #{changeQty},
            OCCUPY_QTY = OCCUPY_QTY + #{occupyQty},
            VERSION = VERSION + 1
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND VERSION = #{version}
    </update>

    <update id="addOccAndStockQtySafe">
        UPDATE pts_ems_aimg
        SET
            STOCK_QTY = STOCK_QTY + #{changeQty},
            OCCUPY_QTY = OCCUPY_QTY + #{occupyQty}
        WHERE
            EMS_ID = #{emsId}
          AND EMS_NO = #{emsNo}
          AND G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
    </update>

    <select id="listEmsDetail" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            *
        FROM
            pts_ems_aimg LEFT JOIN pts_ems_head on pts_ems_head.ID = pts_ems_aimg.EMS_ID
        <where>
            <if test="emsQueryDto.tenantId != null and emsQueryDto.tenantId != ''">
                AND pts_ems_head.TENANT_ID = #{emsQueryDto.tenantId}
            </if>
            <if test="emsQueryDto.emsId != null and emsQueryDto.emsId != ''">
                AND  pts_ems_aimg.EMS_ID = #{emsQueryDto.emsId}
            </if>
            <if test="emsQueryDto.emsNo != null and emsQueryDto.emsNo != ''">
                AND pts_ems_aimg.EMS_NO = #{emsQueryDto.emsNo}
            </if>
            <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                AND pts_ems_aimg.G_NO = #{emsQueryDto.gNo}
            </if>
            <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                AND pts_ems_aimg.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
            </if>
            <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                AND pts_ems_aimg.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
            </if>
            <if test="emsQueryDto.expiryDateStart != null and emsQueryDto.expiryDateStart !=''
             and emsQueryDto.expiryDateEnd != null and emsQueryDto.expiryDateEnd !='' ">
                AND date_format(pts_ems_aimg.EXPIRY_DATE, '%Y-%m-%d')
                    BETWEEN date_format(#{emsQueryDto.expiryDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.expiryDateEnd}, '%Y-%m-%d')
            </if>
            <if test="emsQueryDto.internalNo != null and emsQueryDto.internalNo != ''">
                AND pts_ems_aimg.INTERNAL_NO LIKE CONCAT('%', #{emsQueryDto.internalNo}, '%')
            </if>
            <if test="emsQueryDto.clearanceNo != null and emsQueryDto.clearanceNo != ''">
                AND pts_ems_aimg.CLEARANCE_NO LIKE CONCAT('%', #{emsQueryDto.clearanceNo}, '%')
            </if>
            <if test="emsQueryDto.codet != null and emsQueryDto.codet != ''">
                AND pts_ems_aimg.CODET LIKE CONCAT('%', #{emsQueryDto.codet}, '%')
            </if>
            <if test="emsQueryDto.appDateStart != null and emsQueryDto.appDateStart !=''
             and emsQueryDto.appDateEnd != null and emsQueryDto.appDateEnd !='' ">
                AND date_format(pts_ems_aimg.IAPP_DATE, '%Y-%m-%d')  BETWEEN date_format(#{emsQueryDto.appDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.appDateEnd}, '%Y-%m-%d')
            </if>
            <if test="emsQueryDto.warehousingDateStart != null and emsQueryDto.warehousingDateStart !=''
             and emsQueryDto.warehousingDateEnd != null and emsQueryDto.warehousingDateEnd !='' ">
                AND date_format(pts_ems_aimg.WAREHOUSING_DATE, '%Y-%m-%d')  BETWEEN date_format(#{emsQueryDto.warehousingDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.warehousingDateEnd}, '%Y-%m-%d')
            </if>
            <if test="emsQueryDto.stockQtyZero != null and emsQueryDto.stockQtyZero != '' and emsQueryDto.stockQtyZero == 1">
                AND pts_ems_aimg.STOCK_QTY > 0
            </if>
            <if test="emsQueryDto.stockQtyZero != null and emsQueryDto.stockQtyZero != '' and emsQueryDto.stockQtyZero == 2">
                AND pts_ems_aimg.STOCK_QTY = 0
            </if>
        </where>
        ORDER BY pts_ems_aimg.G_NO DESC
    </select>
    <select id="isDclCusFlag" resultType="java.lang.String">
        SELECT
            BOND_INVT_NO
        FROM
            `NEMS_INVT_HEAD`
        WHERE
            PUTREC_NO = #{emsNo}
          AND DCLCUS_FLAG = '2'
          AND BOND_INVT_NO IS NOT NULL
        GROUP BY
            BOND_INVT_NO
    </select>
    <select id="emsStatistics" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT pea.CURR,
               SUM(pea.STOCK_QTY * pea.DEC_PRICE) decPrice,
               SUM(pea.STOCK_QTY * pea.NET_WEIGHT) netWeight
        FROM PTS_EMS_AIMG pea
        WHERE pea.EMS_NO = #{emsNo}
          AND pea.STOCK_QTY &gt; 0
        GROUP BY pea.CURR
    </select>
    <select id="listAimgList" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            *
        FROM
            pts_ems_aimg a
                LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
        WHERE
            b.EMS_NO = #{emsNo}
          AND a.G_NO IN
        <foreach collection="gNoList" item="gNo" index="index"
                 open="(" close=")" separator=",">
            #{gNo}
        </foreach>
    </select>
    <select id="selectOneBy3Cond" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            a.*
        FROM
            pts_ems_aimg a
                LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
        WHERE
          a.EMS_ID = #{emsId}
          AND a.EMS_NO = #{emsNo}
          AND a.G_NO = #{gNo}
        <if test="copGno != null and copGno != ''">
            AND COP_GNO = #{copGno}
        </if>
          AND b.TENANT_ID = #{tenantId}
        GROUP BY
            a.ID
    </select>
    <select id="listInventoryFlows" resultType="org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO">
        SELECT
            a.EMS_NO,
            a.G_NO,
            a.G_NAME,
            a.CODET,
            a.CLEARANCE_NO,
            a.BOND_INVT_NO,
            CASE
                WHEN b.KEEPING_MODE = 1 THEN
                    '' ELSE d.STORAGE_NO
                END storageNo,
            a.COP_GNO,
            e.SPACE_CODE,
            IFNULL( a.STOCK_QTY, 0 ) stockQty,
            IFNULL( c.BEGIN_QTY, 0 ) beginQty
        FROM
            pts_ems_aimg a
                LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
                LEFT JOIN store_stocks c ON c.STORE_CODE = b.OWNER_CODE
                AND a.G_NO = c.ITEM_NUMBER
                AND a.COP_GNO = c.COP_GNO
                LEFT JOIN store_stocks_flow d ON d.STORE_CODE = b.OWNER_CODE
                AND a.G_NO = d.ITEM_NUMBER
                AND a.COP_GNO = d.COP_GNO
                AND d.IE_FLAG = 'I'
                AND d.OPT_TYPE = 0
                LEFT JOIN storage_detail e ON e.id = d.STORAGE_DETAIL_ID
        <where>
            b.TENANT_ID = #{inventoryFlowsVO.tenantId}
            AND b.EMS_TYPE IN ('TW', 'L')
            <if test="inventoryFlowsVO.gNo != null and inventoryFlowsVO.gNo != ''">
                AND a.G_NO = #{inventoryFlowsVO.gNo}
            </if>
            <if test="inventoryFlowsVO.gName != null and inventoryFlowsVO.gName != ''">
                AND a.G_NAME LIKE CONCAT('%', #{inventoryFlowsVO.gName}, '%')
            </if>
            <if test="inventoryFlowsVO.codet != null and inventoryFlowsVO.codet != ''">
                AND a.CODET LIKE CONCAT('%', #{inventoryFlowsVO.codet}, '%')
            </if>
            <if test="inventoryFlowsVO.clearanceNo != null and inventoryFlowsVO.clearanceNo != ''">
                AND a.CLEARANCE_NO LIKE CONCAT('%', #{inventoryFlowsVO.clearanceNo}, '%')
            </if>
            <if test="inventoryFlowsVO.bondInvtNo != null and inventoryFlowsVO.bondInvtNo != ''">
                AND a.BOND_INVT_NO LIKE CONCAT('%', #{inventoryFlowsVO.bondInvtNo}, '%')
            </if>
            <if test="inventoryFlowsVO.storageNo != null and inventoryFlowsVO.storageNo != ''">
                AND d.STORAGE_NO LIKE CONCAT('%', #{inventoryFlowsVO.storageNo}, '%')
            </if>
            <if test="inventoryFlowsVO.copGno != null and inventoryFlowsVO.copGno != ''">
                AND a.COP_GNO LIKE CONCAT('%', #{inventoryFlowsVO.copGno}, '%')
            </if>
        </where>
        ORDER BY
            e.CREATE_DATE DESC
    </select>
    <select id="listBondedWarning" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            a.*,
        CASE
        WHEN ( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) ) THEN
        '1' ELSE '0'
        END isDyq,
        CASE
        WHEN ( CURDATE() >= DATE( a.EXPIRY_DATE ) ) THEN
        '1' ELSE '0'
        END isYgq
        FROM
            pts_ems_aimg a
        LEFT JOIN pts_ems_head b ON b.ID = a.EMS_ID
        WHERE
            a.EXPIRY_DATE IS NOT NULL
            AND b.TENANT_ID = #{emsQueryDto.tenantId}
            AND a.STOCK_QTY > 0
            <choose>
                <when test="emsQueryDto.warnStatus != null and emsQueryDto.warnStatus != '' and emsQueryDto.warnStatus == 0">
                    AND ( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) )
                </when>
                <when test="emsQueryDto.warnStatus != null and emsQueryDto.warnStatus != '' and emsQueryDto.warnStatus == 1">
                    AND CURDATE() >= DATE( a.EXPIRY_DATE )
                </when>
                <otherwise>
                    AND (( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) )
                    OR CURDATE() >= DATE( a.EXPIRY_DATE ) )
                </otherwise>
            </choose>
            <if test="emsQueryDto.emsNo != null and emsQueryDto.emsNo != ''">
                AND a.EMS_NO LIKE CONCAT('%', #{emsQueryDto.emsNo}, '%')
            </if>
            <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                AND a.G_NO = #{emsQueryDto.gNo}
            </if>
            <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                AND a.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
            </if>
            <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                AND a.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
            </if>
            <if test="emsQueryDto.internalNo != null and emsQueryDto.internalNo != ''">
                AND a.INTERNAL_NO LIKE CONCAT('%', #{emsQueryDto.internalNo}, '%')
            </if>
            <if test="emsQueryDto.clearanceNo != null and emsQueryDto.clearanceNo != ''">
                AND a.CLEARANCE_NO LIKE CONCAT('%', #{emsQueryDto.clearanceNo}, '%')
            </if>
            <if test="emsQueryDto.expiryDateStart != null and emsQueryDto.expiryDateStart !=''
                 and emsQueryDto.expiryDateEnd != null and emsQueryDto.expiryDateEnd !='' ">
                AND date_format(a.EXPIRY_DATE, '%Y-%m-%d')  BETWEEN date_format(#{emsQueryDto.expiryDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.expiryDateEnd}, '%Y-%m-%d')
            </if>
            <if test="emsQueryDto.appDateStart != null and emsQueryDto.appDateStart !=''
                 and emsQueryDto.appDateEnd != null and emsQueryDto.appDateEnd !='' ">
                AND date_format(a.IAPP_DATE, '%Y-%m-%d')  BETWEEN date_format(#{emsQueryDto.appDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.appDateEnd}, '%Y-%m-%d')
            </if>
            <if test="emsQueryDto.warehousingDateStart != null and emsQueryDto.warehousingDateStart !=''
                 and emsQueryDto.warehousingDateEnd != null and emsQueryDto.warehousingDateEnd !='' ">
                AND date_format(a.WAREHOUSING_DATE, '%Y-%m-%d')  BETWEEN date_format(#{emsQueryDto.warehousingDateStart}, '%Y-%m-%d')
                AND date_format(#{emsQueryDto.warehousingDateEnd}, '%Y-%m-%d')
            </if>
        GROUP BY
            a.ID
        ORDER BY
            a.EXPIRY_DATE
    </select>
    <select id="getBondedWarning" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            CASE
            WHEN ( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) ) THEN
            '1' ELSE '0'
            END isDyq,
            CASE
            WHEN ( CURDATE() >= DATE( a.EXPIRY_DATE ) ) THEN
            '1' ELSE '0'
            END isYgq
        FROM
        pts_ems_aimg a
        LEFT JOIN pts_ems_head b ON b.ID = a.EMS_ID
        WHERE
        a.EXPIRY_DATE IS NOT NULL
        AND b.TENANT_ID = #{tenantId}
        <choose>
            <when test="type != null and type != '' and type == 0">
                AND ( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) )
            </when>
            <when test="type != null and type != '' and type == 1">
                AND CURDATE() >= DATE( a.EXPIRY_DATE )
            </when>
            <otherwise>
                AND (( CURDATE() >= DATE_SUB( DATE( a.EXPIRY_DATE ), INTERVAL 45 DAY ) AND CURDATE() &lt; DATE( a.EXPIRY_DATE ) )
                OR CURDATE() >= DATE( a.EXPIRY_DATE ) )
            </otherwise>
        </choose>
        GROUP BY
            a.ID
    </select>
    <select id="getOneByCond" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            a.*
        FROM
            pts_ems_aimg a
                LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
        WHERE
            a.EMS_NO = #{emsNo}
          AND a.COP_GNO = #{copGno}
          AND b.TENANT_ID = #{tenantId}
        LIMIT 1
    </select>
    <select id="listInvtList2" resultType="org.jeecg.modules.business.entity.NemsInvtList">
        SELECT
            *
        FROM
            `nems_invt_list` b
                LEFT JOIN nems_invt_head a ON a.ID = b.INV_ID
        WHERE
           a.PUTREC_NO = #{emsNo}
          AND a.IMPEXP_MARKCD = 'I'
          AND a.VRFDED_MARKCD = '2'
          AND CONCAT(b.PUTREC_SEQNO, '|', b.GDS_MTNO, '|', b.HSCODE) = #{key}
    </select>
    <select id="listInvtList" resultType="org.jeecg.modules.business.entity.NemsInvtList">
        SELECT
            *
        FROM
            `nems_invt_list` b
                LEFT JOIN nems_invt_head a ON a.ID = b.INV_ID
        WHERE
         a.PUTREC_NO = #{emsNo}
          AND a.IMPEXP_MARKCD = 'E'
          AND a.VRFDED_MARKCD = '2'
          AND CONCAT(b.PUTREC_SEQNO, '|', b.GDS_MTNO, '|', b.HSCODE) = #{key}
    </select>
    <select id="listInvtList1" resultType="org.jeecg.modules.business.entity.NemsInvtList">
        SELECT
            *
        FROM
            `nems_invt_list` b
                LEFT JOIN nems_invt_head a ON a.ID = b.INV_ID
        WHERE
          a.PUTREC_NO = #{emsNo}
          AND a.IMPEXP_MARKCD = 'E'
          AND a.VRFDED_MARKCD = '1'
          AND CONCAT(b.PUTREC_SEQNO, '|', b.GDS_MTNO, '|', b.HSCODE) = #{key}
    </select>
    <select id="listEmsAimgByMonthQty" resultType="org.jeecg.modules.business.entity.dto.AimgMonthQtyExportDTO">
        select ANY_VALUE(store_stocks.ITEM_NUMBER) gNo,
               ANY_VALUE(nems_invt_head.BOND_INVT_NO) bondInvtNo,
               ANY_VALUE(store_stocks.COP_GNO) copGno,
               ANY_VALUE(store_stocks.BATCH_NO) clearanceNo,
               ANY_VALUE(store_stocks.BEGIN_QTY) stockQty,
               ANY_VALUE(store_stocks.QUNIT) unit,
               ANY_VALUE(store_stocks.SPACE_CODE) cfhw,
               ANY_VALUE(nems_invt_head.PUTREC_NO) emsNo,
               ANY_VALUE(store_stocks.PN) pn,
               ANY_VALUE(store_stocks.BEGIN_AMOUNT) beginAmount,
               ANY_VALUE((case when nems_invt_list.DCL_UNITCD = '035' then nems_invt_list.DCL_QTY
                     when nems_invt_list.LAWF_UNITCD = '035' then nems_invt_list.LAWF_QTY
                     else nems_invt_list.SECD_LAWF_QTY end)) as weight
        from store_stocks left join nems_invt_head on store_stocks.BOND_INVT_NO = nems_invt_head.BOND_INVT_NO
        left join nems_invt_list on nems_invt_list.INV_ID = nems_invt_head.ID
                                        and nems_invt_list.PUTREC_SEQNO = store_stocks.ITEM_NUMBER
        where
        store_stocks.TENANT_ID = #{tenantId}
        AND nems_invt_head.TENANT_ID = #{tenantId}
        AND store_stocks.BEGIN_QTY > 0
        GROUP BY store_stocks.ITEM_NUMBER

    </select>

    <select id="listEmsAimgByReport" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
            emsNo,
            gNo,
            copGno,
            gName,
            codet,
            gModel,
            qty,
            zjjksl,
            ( qty - zjjksl ) AS scyl,
            ROUND(( qty - zjjksl ) / qty * 100, 4 ) AS ylbl,
            sjgjzjksl,
            yljzjksl,
            ljthjksl,
            ljthcksl,
            nxzssl,
            zfcsl,
            ylckjzsl,
            xhsl,
            ( zjjksl + sjgjzjksl + yljzjksl + ljthjksl - ljthcksl - nxzssl - zfcsl - ylckjzsl - xhsl ) AS sjjksl,
        bjlybsl,
        bjlfcsl
        FROM
            (
                SELECT
                    ANY_VALUE ( pea.EMS_NO ) emsNo,
                    ANY_VALUE ( pea.G_NO ) gNo,
                    ANY_VALUE ( pea.COP_GNO ) copGno,
                    ANY_VALUE ( pea.G_NAME ) gName,
                    ANY_VALUE ( pea.CODET ) codet,
                    ANY_VALUE ( pea.G_MODEL ) gModel,
                    ANY_VALUE ( pea.QTY ) qty,
                    ANY_VALUE (
        IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ), 0)) zjjksl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END )) sjgjzjksl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' THEN nl.DCL_QTY ELSE 0 END )) yljzjksl,
                    ANY_VALUE (
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'I'
                                                AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            )) ljthjksl,
                    ANY_VALUE (
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            )) ljthcksl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '0245' OR nh.SUPV_MODECD = '0644' THEN nl.DCL_QTY ELSE 0 END )) nxzssl,
                    ANY_VALUE (
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            )) zfcsl,
                    ANY_VALUE (
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            )) ylckjzsl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '0200' THEN nl.DCL_QTY ELSE 0 END )) xhsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '0844' OR nh.SUPV_MODECD = '0845' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) bjlybsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0864' OR nh.SUPV_MODECD = '0865' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) bjlfcsl
                FROM
                    pts_ems_aimg pea
                        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
                        AND ( nh.VRFDED_MARKCD = '2' )
                        AND nh.PUTREC_NO = #{emsQueryDto.emsNo}

                        AND nh.TENANT_ID = #{emsQueryDto.tenantId}

                        AND (
                                                           nh.IMPEXP_MARKCD = 'I'
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' ))
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' ))
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )))
                        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
                        AND nl.PUTREC_SEQNO = pea.G_NO
                WHERE
                    pea.EMS_NO = #{emsQueryDto.emsNo}
                <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                    AND pea.G_NO = #{emsQueryDto.gNo}
                </if>
                <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                    AND pea.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
                </if>
                <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                    AND pea.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
                </if>

                GROUP BY
                    pea.G_NO
                ORDER BY
                    pea.G_NO
            ) subquery

    </select>


    <select id="listEmsAimgByReportC" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
        gNo,
        qty,
        zjjksl,
        ( qty - zjjksl ) AS scyl,
        ROUND(( qty - zjjksl ) / qty * 100, 4 ) AS ylbl,
        sjgjzjksl,
        yljzjksl,
        ljthjksl,
        ljthcksl,
        nxzssl,
        zfcsl,
        ylckjzsl,
        xhsl,
        ( zjjksl + sjgjzjksl + yljzjksl + ljthjksl - ljthcksl - nxzssl - zfcsl - ylckjzsl - xhsl ) AS sjjksl
        FROM
        (
        SELECT
        ANY_VALUE ( pea.G_NO ) gNo,
        ANY_VALUE ( pea.QTY ) qty,
        ANY_VALUE (
        IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ), 0) ) zjjksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END )) sjgjzjksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' THEN nl.DCL_QTY ELSE 0 END )) yljzjksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) ljthjksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) ljthcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0245' OR nh.SUPV_MODECD = '0644' THEN nl.DCL_QTY ELSE 0 END )) nxzssl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) zfcsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) ylckjzsl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0200' THEN nl.DCL_QTY ELSE 0 END )) xhsl
        FROM
        pts_ems_aimg pea
        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
        AND ( nh.VRFDED_MARKCD = '2')
        AND nh.PUTREC_NO = #{emsNo}

        AND nh.TENANT_ID = #{tenantId}

        AND (
        nh.IMPEXP_MARKCD = 'I'
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' ))
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' ))
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )))
        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
        AND nl.PUTREC_SEQNO = pea.G_NO
        WHERE
        pea.EMS_NO = #{emsNo}
        AND pea.G_NO IN
        <foreach collection="gNoList" item="gNo" index="index"
                 open="(" close=")" separator=",">
            #{gNo}
        </foreach>


        GROUP BY
        pea.G_NO
        ORDER BY
        pea.G_NO
        ) subquery

    </select>

    <select id="listEmsDetailWeightStatisticsAimg" resultType="org.jeecg.modules.business.vo.PtsEmsAimgWeightStatisticsVO">
        SELECT
            zjjkWeight,
            yljzjkWeight,
            sjgjzjkWeight,
            ljthjkWeight,
            ljnxWeight,
            yljzckWeight,
            ljfcWeight,
            ljthckWeight,
            bjlnxWeight,
            bjlfcWeight,
            bjlxhWeight,
            ljxhWeight,
            ( zjjkWeight + yljzjkWeight + sjgjzjkWeight + ljthjkWeight - ljnxWeight - yljzckWeight - ljfcWeight - ljthckWeight - bjlnxWeight - bjlfcWeight - bjlxhWeight - ljxhWeight ) jkbsljWeightSum
        FROM
            (
                SELECT
                    IFNULL(
                            SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ),
                            0
                    ) zjjkWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' THEN nl.DCL_QTY ELSE 0 END ), 0 ) yljzjkWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END ), 0 ) sjgjzjkWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' THEN nl.DCL_QTY ELSE 0 END ), 0 ) ljthjkWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0245' OR nh.SUPV_MODECD = '0644' THEN nl.DCL_QTY ELSE 0 END ), 0 ) ljnxWeight,
                    IFNULL(
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            ),
                            0
                    ) yljzckWeight,
                    IFNULL(
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            ),
                            0
                    ) ljfcWeight,
                    IFNULL(
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            ),
                            0
                    ) ljthckWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0844' OR nh.SUPV_MODECD = '0845' THEN nl.DCL_QTY ELSE 0 END ), 0 ) bjlnxWeight,
                    IFNULL(
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'E'
                                                AND ( nh.SUPV_MODECD = '0864' OR nh.SUPV_MODECD = '0865' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            ),
                            0
                    ) bjlfcWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0400' THEN nl.DCL_QTY ELSE 0 END ), 0 ) bjlxhWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0200' THEN nl.DCL_QTY ELSE 0 END ), 0 ) ljxhWeight
                FROM
                    nems_invt_head nh
                        LEFT JOIN nems_invt_list nl ON nl.INV_ID = nh.ID
                        AND ( nh.VRFDED_MARKCD = '2')
                        AND nh.PUTREC_NO =  #{emsNo}
                        AND nh.TENANT_ID =  #{tenantId}
                        AND (
                                                           nh.IMPEXP_MARKCD = 'I'
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' ))
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' ))
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' ))
                                                               OR (
                                                               nh.IMPEXP_MARKCD = 'E'
                                                                   AND ( nh.SUPV_MODECD = '0864' OR nh.SUPV_MODECD = '0865' )))
            ) subquery


    </select>

    <select id="listEmsDetailAimgAmountByReport" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
        emsNo,
        gNo,
        copGno,
        gName,
        codet,
        gModel,
        qty,
        unit,
        decPrice,
        curr,
        zjjkslQty,
        zjjksl,
        sjgjzjksl,
        yljzjksl,
        ljthjksl,
        ljthcksl,
        (zjjksl+sjgjzjksl+yljzjksl+ljthjksl-ljthcksl) jkjehj,
        nxzssl,
        zfcsl,
        ylckjzsl,
        xhsl,
        (zjjksl+sjgjzjksl+yljzjksl+ljthjksl-ljthcksl-nxzssl-zfcsl-ylckjzsl-xhsl) sjjkjehj,
        bjlybsl,
        bjlfcsl
        FROM
        (
        SELECT
        ANY_VALUE ( nh.PUTREC_NO ) emsNo,
        ANY_VALUE ( pea.G_NO ) gNo,
        ANY_VALUE ( pea.COP_GNO ) copGno,
        ANY_VALUE ( pea.G_NAME ) gName,
        ANY_VALUE ( pea.CODET ) codet,
        ANY_VALUE ( pea.G_MODEL ) gModel,
        ANY_VALUE ( pea.QTY ) qty,
        ANY_VALUE ( pea.UNIT ) unit,
        ANY_VALUE (pea.DEC_PRICE) decPrice,
        ANY_VALUE(nl.DCL_CURRCD) curr,
        ANY_VALUE (
        IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ), 0)) zjjkslQty,
        ANY_VALUE (
        IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_TOTALAMT ELSE 0 END ), 0)) zjjksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_TOTALAMT ELSE 0 END )) sjgjzjksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' THEN nl.DCL_TOTALAMT ELSE 0 END )) yljzjksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) ljthjksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) ljthcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0245' OR nh.SUPV_MODECD = '0644' THEN nl.DCL_TOTALAMT ELSE 0 END )) nxzssl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) zfcsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) ylckjzsl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0200' THEN nl.DCL_TOTALAMT ELSE 0 END )) xhsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '0844' OR nh.SUPV_MODECD = '0845' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) bjlybsl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0864' OR nh.SUPV_MODECD = '0865' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) bjlfcsl
        FROM
        pts_ems_aimg pea
        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
        AND ( nh.VRFDED_MARKCD = '2')
        AND nh.PUTREC_NO = #{emsQueryDto.emsNo}

        AND nh.TENANT_ID = #{emsQueryDto.tenantId}

        AND (
        nh.IMPEXP_MARKCD = 'I'
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0300' OR nh.SUPV_MODECD = '0700' ))
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0265' OR nh.SUPV_MODECD = '0664' ))
        OR (
        nh.IMPEXP_MARKCD = 'E'
        AND ( nh.SUPV_MODECD = '0258' OR nh.SUPV_MODECD = '0657' )))
        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
        AND nl.PUTREC_SEQNO = pea.G_NO
        WHERE
        pea.EMS_NO = #{emsQueryDto.emsNo}
          <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
              AND pea.G_NO = #{emsQueryDto.gNo}
          </if>
           <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                             AND pea.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
                        </if>
           <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                            AND pea.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
          </if>

        GROUP BY
        pea.G_NO,nl.DCL_CURRCD
        ORDER BY
        pea.G_NO
        ) subquery
    </select>

    <select id="listEmsDetailByUnitPriceComparison" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        SELECT
        ANY_VALUE ( pea.G_NO ) gNo,
        ANY_VALUE ( pea.COP_GNO ) copGno,
        ANY_VALUE ( pea.G_NAME ) gName,
        ANY_VALUE ( pea.UNIT ) unit,
        ANY_VALUE ( pea.DEC_PRICE ) decPrice,
        ANY_VALUE ( nl.DCL_CURRCD ) curr,
        ANY_VALUE (
        MIN( nl.DCL_UPRCAMT )) tgzddj,
        ANY_VALUE (
        MAX( nl.DCL_UPRCAMT )) tgzgdj
        FROM
        `pts_ems_aimg` pea
        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
        AND ( nh.VRFDED_MARKCD = '2')
        AND nh.PUTREC_NO = #{emsQueryDto.emsNo}
        AND nh.TENANT_ID = #{emsQueryDto.tenantId}
        AND nh.IMPEXP_MARKCD = 'I'
        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
        AND nl.PUTREC_SEQNO = pea.G_NO
        WHERE
        pea.EMS_NO = #{emsQueryDto.emsNo}
           <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                  AND pea.G_NO = #{emsQueryDto.gNo}
            </if>
            <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                             AND pea.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
           </if>
            <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                              AND pea.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
           </if>

        GROUP BY
        pea.G_NO,
        nl.DCL_CURRCD
        ORDER BY
        pea.G_NO
    </select>
</mapper>
