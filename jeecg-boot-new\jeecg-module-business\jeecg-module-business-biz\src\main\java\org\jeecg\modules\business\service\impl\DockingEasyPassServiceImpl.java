package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.config.mqtoken.UserTokenContext;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysTenant;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Slf4j
@Service
public class DockingEasyPassServiceImpl implements IDockingEasyPassService {

    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private NemsInvtHeadMapper nemsInvtHeadMapper;
    @Autowired
    private IJgVFlyBgDecheadService jgVFlyBgDecheadService;
    @Autowired
    private IJgVFlyBgDeclistService jgVFlyBgDeclistService;
    @Autowired
    private IJgVFlyBgDeccontainerService jgVFlyBgDeccontainerService;
    @Autowired
    private IJgVFlyBgDeclicenseService jgVFlyBgDeclicenseService;
    @Autowired
    private IJgVFlyBgDecdocvoService jgVFlyBgDecdocvoService;
    @Autowired
    private IDecHeadService decHeadService;
    @Autowired
    private IDecListService decListService;
    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;
    @Autowired
    private INemsInvtListService nemsInvtListService;
    @Autowired
    private IJgVFlyHzqdHeadService jgVFlyHzqdHeadService;
    @Autowired
    private IJgVFlyHzqdDetailService jgVFlyHzqdDetailService;
    @Autowired
    private IJgVFlyBgUnsafebusinessService jgVFlyBgUnsafebusinessService;
    @Autowired
    private IJgVFlySjZqHwsbCxdXxcxService jgVFlySjZqHwsbCxdXxcxService;
    @Autowired
    private RepairCancellationOrdersMapper repairCancellationOrdersMapper;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;

    /**
     * 远程报关单-分页列表查询
     *
     * @param page
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public IPage<JgVFlyBgDechead> listDec(Page<JgVFlyBgDechead> page, String customerName, String starDate, String lastDate) {
        IPage<JgVFlyBgDechead> jgVFlyBgDecheadIPage = jgVFlyBgDecheadService.queryPageList(page, customerName, starDate, lastDate);
        return jgVFlyBgDecheadIPage;
    }
    /**
     * 远程修撤单-分页列表查询
     *
     * @param page
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public IPage<JgVFlySjZqHwsbCxdXxcx> listJgVFlySjZqHwsbCxdXxcx(Page<JgVFlySjZqHwsbCxdXxcx> page, String customerName, String starDate, String lastDate) {
        IPage<JgVFlySjZqHwsbCxdXxcx> jgVFlySjZqHwsbCxdXxcxIPage = jgVFlySjZqHwsbCxdXxcxService.queryPageList(page, customerName, starDate, lastDate);
        return jgVFlySjZqHwsbCxdXxcxIPage;
    }


    /**
     * @param page
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public IPage<JgVFlyHzqdHead> listInvt(Page<JgVFlyHzqdHead> page, String customerName, String starDate, String lastDate) {
        IPage<JgVFlyHzqdHead> jgVFlyHzqdHeadIPage = jgVFlyHzqdHeadService.queryPageList(page, customerName, starDate, lastDate);
        return jgVFlyHzqdHeadIPage;
    }

    /**
     * @param page
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public IPage<JgVFlyBgUnsafebusiness> listTaxBill(Page<JgVFlyBgUnsafebusiness> page, String customerName, String starDate, String lastDate) {
        IPage<JgVFlyBgUnsafebusiness> jgVFlyBgUnsafebusinessIPage = jgVFlyBgUnsafebusinessService.queryPageList(page, customerName, starDate, lastDate);
        return jgVFlyBgUnsafebusinessIPage;
    }

    /**
     * 同步远程报关单
     *
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @DSTransactional
    @Override
    public Result<?> dockingDecSync(String customerName, String starDate, String lastDate) {
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Page<JgVFlyBgDechead> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<JgVFlyBgDechead> jgVFlyBgDecheadIPage = jgVFlyBgDecheadService.queryPageList(page, customerName, starDate, lastDate);
        if (isEmpty(jgVFlyBgDecheadIPage.getRecords())) {
            log.info("【dockingDecSync】未获取到远程报关单数据！");
            return Result.ok("未获取到远程报关单数据！");
        }
        List<JgVFlyBgDechead> jgVFlyBgDecheadList = jgVFlyBgDecheadIPage.getRecords();
        log.info("【dockingDecSync】获取到远程报关单数据共：" + jgVFlyBgDecheadList.size() + "条！");
        List<DecHead> decHeadList = new ArrayList<>(16);
        Result<Tenant> tenant = sysBaseApi.getTenantByName(customerName);
        Long customerTenantId;
        if (isNotEmpty(tenant.getResult())) {
            customerTenantId = tenant.getResult().getId();
        } else {
            customerTenantId = null;
        }
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> headIdList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> headIdListList = CollUtil.split(headIdList, 1000);
        List<JgVFlyBgDeclist> jgVFlyBgDeclistTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseTotalList = new ArrayList<>(16);
        List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoTotalList = new ArrayList<>(16);
        // 2022/3/10 9:39@ZHANGCHAO 追加/变更/完善：每一票都单独发送消息！
        ThreadUtil.execAsync(() -> {
            log.info("-----------异步批量插入START-----------");
            for (List<String> list : headIdListList) {
                List<JgVFlyBgDeclist> jgVFlyBgDeclistList = jgVFlyBgDeclistService.list(new LambdaQueryWrapper<JgVFlyBgDeclist>()
                        .in(JgVFlyBgDeclist::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclistList)) {
                    jgVFlyBgDeclistTotalList.addAll(jgVFlyBgDeclistList);
                }
                List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerList = jgVFlyBgDeccontainerService.list(new LambdaQueryWrapper<JgVFlyBgDeccontainer>()
                        .in(JgVFlyBgDeccontainer::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeccontainerList)) {
                    jgVFlyBgDeccontainerTotalList.addAll(jgVFlyBgDeccontainerList);
                }
                List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseList = jgVFlyBgDeclicenseService.list(new LambdaQueryWrapper<JgVFlyBgDeclicense>()
                        .in(JgVFlyBgDeclicense::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclicenseList)) {
                    jgVFlyBgDeclicenseTotalList.addAll(jgVFlyBgDeclicenseList);
                }
                List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoList = jgVFlyBgDecdocvoService.list(new LambdaQueryWrapper<JgVFlyBgDecdocvo>()
                        .in(JgVFlyBgDecdocvo::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDecdocvoList)) {
                    jgVFlyBgDecdocvoTotalList.addAll(jgVFlyBgDecdocvoList);
                }
            }
            Map<String, List<JgVFlyBgDeclist>> jgVFlyBgDeclistMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclistTotalList)) {
                jgVFlyBgDeclistMap = jgVFlyBgDeclistTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclist::getHeadId));
            }
            Map<String, List<JgVFlyBgDeccontainer>> jgVFlyBgDeccontainerMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeccontainerTotalList)) {
                jgVFlyBgDeccontainerMap = jgVFlyBgDeccontainerTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeccontainer::getHeadId));
            }
            Map<String, List<JgVFlyBgDeclicense>> jgVFlyBgDeclicenseMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclicenseTotalList)) {
                jgVFlyBgDeclicenseMap = jgVFlyBgDeclicenseTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclicense::getHeadId));
            }
            Map<String, List<JgVFlyBgDecdocvo>> jgVFlyBgDecdocvoMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDecdocvoTotalList)) {
                jgVFlyBgDecdocvoMap = jgVFlyBgDecdocvoTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDecdocvo::getHeadId));
            }
            List<String> cusCiqNoList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getCusCiqNo).collect(Collectors.toList());
    //        DynamicDataSourceContextHolder.push("master"); //手动切换
            List<DecHead> decHeads = decHeadMapper.listBySeqNos(cusCiqNoList);
            Map<String, List<DecHead>> decHeadsMap = new HashMap<>();
            Map<String, List<DecList>> decListsMap = new HashMap<>();
            if (isNotEmpty(decHeads)) {
                decHeadsMap = decHeads.stream().collect(Collectors.groupingBy(DecHead::getSeqNo));
                List<String> headIds = decHeads.stream().map(DecHead::getId).collect(Collectors.toList());
                List<DecList> decLists = decListService.list(new LambdaQueryWrapper<DecList>()
                        .in(DecList::getDecId, headIds));
                if (isNotEmpty(decLists)) {
                    decListsMap = decLists.stream().collect(Collectors.groupingBy(DecList::getDecId));
                }
            }
            for (JgVFlyBgDechead jgVFlyBgDechead : jgVFlyBgDecheadList) {
                jgVFlyBgDechead.setJgVFlyBgDeclistList(jgVFlyBgDeclistMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeccontainerList(jgVFlyBgDeccontainerMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeclicenseList(jgVFlyBgDeclicenseMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDecdocvoList(jgVFlyBgDecdocvoMap.get(jgVFlyBgDechead.getId()));
                DecHead decHead;
                if (isNotEmpty(decHeadsMap) && isNotEmpty(decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()))) {
                    decHead = decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()).get(0);
                    decHead.setDecLists(decListsMap.get(decHead.getId()));
                } else {
                    decHead = new DecHead();
                }
                if (isEmpty(decHead.getId())) {
                    decHead.setDclTenantId(isNotEmpty(customerTenantId) ? String.valueOf(customerTenantId) : null);
                    decHead.setTenantId(customerTenantId);
                }
                // 转换为平台的报关单
                try {
                    setDecHeadFromJgVFly(decHead, jgVFlyBgDechead);
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error("报关单转换失败，报关单号：" + jgVFlyBgDechead.getCusCiqNo() + "，错误信息：" + e.getMessage());
                }
                //20240219存在表体则取第一条表体的币制放入到表头币制字段，为了统计后续添加的字段 by zhengliansong
                decHead.setCurrency(isNotEmpty(decHead.getDecLists())?decHead.getDecLists().get(0).getCurrencyCode():null);
                decHeadList.add(decHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的报关单集合
            if (isNotEmpty(decHeadList)) {
                // 分片
                List<List<DecHead>> decHeadListList = CollUtil.split(decHeadList, 50);
                log.info("报关单数据分片，每片50条，共" + decHeadListList.size() +"片");
                long start = System.currentTimeMillis();
                try {
                    // 初始化线程数量，最多10个吧
                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(decHeadListList.size(), 10));
                    for (List<DecHead> headList : decHeadListList) {
                        ThreadUtil.execute(() -> {
                            decHeadService.saveDecHeadBatch(headList);
                        });
                        // 调用线程计数器-1
                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
                    countDownLatch.await(30, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
            log.info("-----------异步批量插入END-----------");
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyBgDecheadList.size() + "票报关单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @DSTransactional
    @Override
    public Result<?> dockingInvtSync(String customerName, String starDate, String lastDate) {
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Page<JgVFlyHzqdHead> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<JgVFlyHzqdHead> jgVFlyHzqdHeadIPage = jgVFlyHzqdHeadService.queryPageList(page, customerName, starDate, lastDate);
        if (isEmpty(jgVFlyHzqdHeadIPage.getRecords())) {
            log.info("【dockingInvtSync】未获取到远程核注单数据！");
            return Result.ok("未获取到远程核注单数据！");
        }
        List<JgVFlyHzqdHead> jgVFlyHzqdHeadList = jgVFlyHzqdHeadIPage.getRecords();
        log.info("【dockingDecSync】获取到远程核注单数据共：" + jgVFlyHzqdHeadList.size() + "条！");
        List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>(16);
        Result<Tenant> tenant = sysBaseApi.getTenantByName(customerName);
        Long customerTenantId;
        if (isNotEmpty(tenant.getResult())) {
            customerTenantId = tenant.getResult().getId();
        } else {
            customerTenantId = -1L;
        }
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> invtIdList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> invtIdListList = CollUtil.split(invtIdList, 1000);
        List<JgVFlyHzqdDetail> jgVFlyHzqdDetailTotalList = new ArrayList<>(16);
        // 2023/11/20 10:00@ZHANGCHAO 追加/变更/完善：走异步处理！！
        ThreadUtil.execAsync(() -> {
            for (List<String> list : invtIdListList) {
                List<JgVFlyHzqdDetail> jgVFlyHzqdDetailList = jgVFlyHzqdDetailService.list(new LambdaQueryWrapper<JgVFlyHzqdDetail>()
                        .in(JgVFlyHzqdDetail::getHeadId, list));
                if (isNotEmpty(jgVFlyHzqdDetailList)) {
                    jgVFlyHzqdDetailTotalList.addAll(jgVFlyHzqdDetailList);
                }
            }
            Map<String, List<JgVFlyHzqdDetail>> jgVFlyHzqdDetailMap = new HashMap<>();
            if (isNotEmpty(jgVFlyHzqdDetailTotalList)) {
                jgVFlyHzqdDetailMap = jgVFlyHzqdDetailTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyHzqdDetail::getHeadId));
            }
            List<String> seqNoList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getSeqNo).collect(Collectors.toList());
            List<NemsInvtHead> nemsInvtHeads = nemsInvtHeadMapper.listBySeqNos(seqNoList);
            Map<String, List<NemsInvtHead>> nemsInvtHeadsMap =  new HashMap<>();
            Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
            if (isNotEmpty(nemsInvtHeads)) {
                nemsInvtHeadsMap = nemsInvtHeads.stream().collect(Collectors.groupingBy(NemsInvtHead::getSeqNo));
                List<Long> headIds = nemsInvtHeads.stream().map(NemsInvtHead::getId).collect(Collectors.toList());
                List<NemsInvtList> nemsInvtLists = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
                        .in(NemsInvtList::getInvId, headIds));
                if (isNotEmpty(nemsInvtLists)) {
                    nemsInvtListMap = nemsInvtLists.stream().collect(Collectors.groupingBy(NemsInvtList::getInvId));
                }
            }
            for (JgVFlyHzqdHead jgVFlyHzqdHead : jgVFlyHzqdHeadList) {
                jgVFlyHzqdHead.setJgVFlyHzqdDetailList(jgVFlyHzqdDetailMap.get(jgVFlyHzqdHead.getId()));
                NemsInvtHead nemsInvtHead;
                if (isNotEmpty(nemsInvtHeadsMap) && isNotEmpty(nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()))) {
                    nemsInvtHead = nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()).get(0);
                    nemsInvtHead.setNemsInvtLists(nemsInvtListMap.get(nemsInvtHead.getId()));
                } else {
                    nemsInvtHead = new NemsInvtHead();
                }
                if (isEmpty(nemsInvtHead.getId())) {
                    nemsInvtHead.setDclTenantId(Long.valueOf(customerTenantId));
                    nemsInvtHead.setTenantId(Long.valueOf(customerTenantId));
                }
                /*
                 * 转换为平台核注单
                 */
                setNemsInvtByJgVFly(nemsInvtHead, jgVFlyHzqdHead);
                nemsInvtHeadList.add(nemsInvtHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的核注单集合
            if (isNotEmpty(nemsInvtHeadList)) {
                // 分片
                List<List<NemsInvtHead>> nemsInvtHeadListList = CollUtil.split(nemsInvtHeadList, 50);
                log.info("核注单数据分片，每片50条，共" + nemsInvtHeadListList.size() +"片");
                long start = System.currentTimeMillis();
                try {
                    // 初始化线程数量，最多10个吧
                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(nemsInvtHeadListList.size(), 10));
                    for (List<NemsInvtHead> headList : nemsInvtHeadListList) {
                        ThreadUtil.execute(() -> {
                            nemsInvtHeadService.saveNemsInvtHeadBatch(headList);
                        });
                        // 调用线程计数器-1
                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyHzqdHeadList.size() + "票核注单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 同步修撤单数据
     * @param customerName
     * @param starDate
     * @param lastDate
     * @return
     */
    @DSTransactional
    @Override
    public Result<?> dockingRepairCancellationOrdersSync(String customerName, String starDate, String lastDate){
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Page<JgVFlySjZqHwsbCxdXxcx> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<JgVFlySjZqHwsbCxdXxcx> jgVFlySjZqHwsbCxdXxcxIPage =
                jgVFlySjZqHwsbCxdXxcxService.queryPageList(page, customerName, starDate, lastDate);
        if (isEmpty(jgVFlySjZqHwsbCxdXxcxIPage.getRecords())) {
            log.info("【dockingRepairCancellationOrdersSync】未获取到远程修撤单数据！");
            return Result.error("未获取到远程修撤单数据！");
        }
        List<JgVFlySjZqHwsbCxdXxcx> jgVFlySjZqHwsbCxdXxcxList = jgVFlySjZqHwsbCxdXxcxIPage.getRecords();
        log.info("【dockingRepairCancellationOrdersSync】获取到远程修撤单数据共：" + jgVFlySjZqHwsbCxdXxcxList.size() + "条！");
        Result<Tenant> tenant = sysBaseApi.getTenantByName(customerName);
        Long customerTenantId;
        if (isNotEmpty(tenant.getResult())) {
            customerTenantId = tenant.getResult().getId();
        } else {
            customerTenantId = -1L;
        }
        //异步处理
        ThreadUtil.execAsync(() -> {
            for(JgVFlySjZqHwsbCxdXxcx jgVFlySjZqHwsbCxdXxcx:jgVFlySjZqHwsbCxdXxcxList){
                RepairCancellationOrders repairCancellationOrders=new RepairCancellationOrders();
                BeanUtil.copyProperties(jgVFlySjZqHwsbCxdXxcx,repairCancellationOrders);
                repairCancellationOrders.setTenantId(customerTenantId);
                repairCancellationOrders.setCreateBy("yunying");
                repairCancellationOrders.setCreateTime(new Date());
                repairCancellationOrders.setId(null);
                repairCancellationOrdersMapper.insert(repairCancellationOrders);
            }
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlySjZqHwsbCxdXxcxList.size() + "票修撤单!";
        log.info(msg);
        return Result.ok(msg);

    }

    /**
     * @param bizopEtpsNm
     * @param supvModecd
     * @param startDate
     * @param lastDate
     * @return
     */
    @Override
    public Result<?> syncHzqdForJob(String bizopEtpsNm, String supvModecd, String putrecNo, String startDate, String lastDate, String isAll) {
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Page<JgVFlyHzqdHead> page = new Page<>(1, Integer.MAX_VALUE);
        if (isBlank(startDate)) {
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.PURE_DATE_PATTERN);
        }
        if (isBlank(lastDate)) {
            lastDate = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        }
        IPage<JgVFlyHzqdHead> jgVFlyHzqdHeadIPage = jgVFlyHzqdHeadService.listHzqdData(page, bizopEtpsNm, supvModecd, putrecNo, startDate, lastDate, isAll);
        if (isEmpty(jgVFlyHzqdHeadIPage.getRecords())) {
            log.info("【dockingInvtSync】未获取到远程核注单数据！");
            return Result.ok("未获取到远程核注单数据！");
        }
        List<JgVFlyHzqdHead> jgVFlyHzqdHeadList = jgVFlyHzqdHeadIPage.getRecords();
        log.info("【dockingDecSync】获取到远程核注单数据共：" + jgVFlyHzqdHeadList.size() + "条！");
        List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>(16);

        // jgVFlyHzqdHeadList根据bizopEtpsNm分组
        Map<String, List<JgVFlyHzqdHead>> jgVFlyHzqdHeadMap = jgVFlyHzqdHeadList.stream().collect(Collectors.groupingBy(JgVFlyHzqdHead::getBizopEtpsNm));
        //1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        Map<String, Long> customerTenantIdMap = new HashMap<>(16);
        jgVFlyHzqdHeadMap.forEach((k, v) -> {
            Result<Tenant> tenant = sysBaseApi.getTenantByName(k);
            Long customerTenantId;
            if (isNotEmpty(tenant) && isNotEmpty(tenant.getResult())) {
                customerTenantId = tenant.getResult().getId();
            } else {
                customerTenantId = -1L;
            }
            customerTenantIdMap.put(k, customerTenantId);
        });
        //2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> invtIdList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> invtIdListList = CollUtil.split(invtIdList, 1000);
        List<JgVFlyHzqdDetail> jgVFlyHzqdDetailTotalList = new ArrayList<>(16);
        // 2023/11/20 10:00@ZHANGCHAO 追加/变更/完善：走异步处理！！
        ThreadUtil.execAsync(() -> {
            for (List<String> list : invtIdListList) {
                List<JgVFlyHzqdDetail> jgVFlyHzqdDetailList = jgVFlyHzqdDetailService.list(new LambdaQueryWrapper<JgVFlyHzqdDetail>()
                        .in(JgVFlyHzqdDetail::getHeadId, list));
                if (isNotEmpty(jgVFlyHzqdDetailList)) {
                    jgVFlyHzqdDetailTotalList.addAll(jgVFlyHzqdDetailList);
                }
            }
            Map<String, List<JgVFlyHzqdDetail>> jgVFlyHzqdDetailMap = new HashMap<>();
            if (isNotEmpty(jgVFlyHzqdDetailTotalList)) {
                jgVFlyHzqdDetailMap = jgVFlyHzqdDetailTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyHzqdDetail::getHeadId));
            }
            List<String> seqNoList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getSeqNo).collect(Collectors.toList());
            List<NemsInvtHead> nemsInvtHeads = nemsInvtHeadMapper.listBySeqNos(seqNoList);
            Map<String, List<NemsInvtHead>> nemsInvtHeadsMap =  new HashMap<>();
            Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
            if (isNotEmpty(nemsInvtHeads)) {
                nemsInvtHeadsMap = nemsInvtHeads.stream().collect(Collectors.groupingBy(NemsInvtHead::getSeqNo));
                List<Long> headIds = nemsInvtHeads.stream().map(NemsInvtHead::getId).collect(Collectors.toList());
                List<NemsInvtList> nemsInvtLists = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
                        .in(NemsInvtList::getInvId, headIds));
                if (isNotEmpty(nemsInvtLists)) {
                    nemsInvtListMap = nemsInvtLists.stream().collect(Collectors.groupingBy(NemsInvtList::getInvId));
                }
            }
            for (JgVFlyHzqdHead jgVFlyHzqdHead : jgVFlyHzqdHeadList) {
                jgVFlyHzqdHead.setJgVFlyHzqdDetailList(jgVFlyHzqdDetailMap.get(jgVFlyHzqdHead.getId()));
                NemsInvtHead nemsInvtHead;
                if (isNotEmpty(nemsInvtHeadsMap) && isNotEmpty(nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()))) {
                    nemsInvtHead = nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()).get(0);
                    nemsInvtHead.setNemsInvtLists(nemsInvtListMap.get(nemsInvtHead.getId()));
                } else {
                    nemsInvtHead = new NemsInvtHead();
                }
                /*
                 * 转换为平台核注单
                 */
                setNemsInvtByJgVFly(nemsInvtHead, jgVFlyHzqdHead);
                if (isEmpty(nemsInvtHead.getId())) {
                    nemsInvtHead.setDclTenantId(Long.valueOf(customerTenantIdMap.get(nemsInvtHead.getBizopEtpsNm())));
                    nemsInvtHead.setTenantId(Long.valueOf(customerTenantIdMap.get(nemsInvtHead.getBizopEtpsNm())));
                }
                nemsInvtHeadList.add(nemsInvtHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的核注单集合
            if (isNotEmpty(nemsInvtHeadList)) {
                // 分片
                List<List<NemsInvtHead>> nemsInvtHeadListList = CollUtil.split(nemsInvtHeadList, 50);
                log.info("核注单数据分片，每片50条，共" + nemsInvtHeadListList.size() +"片");
                long start = System.currentTimeMillis();
                try {
                    // 初始化线程数量，最多10个吧
                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(nemsInvtHeadListList.size(), 10));
                    for (List<NemsInvtHead> headList : nemsInvtHeadListList) {
                        ThreadUtil.execute(() -> {
                            nemsInvtHeadService.saveNemsInvtHeadBatch(headList);
                        });
                        // 调用线程计数器-1
                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyHzqdHeadList.size() + "票核注单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 同步远程核注单通用 -- 根据经营单位编码
     *
     * @param bizopEtpsno
     * @param bondInvtNo
     * @param startDate
     * @param lastDate
     * @param isAll
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/5 16:50
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> syncHzqdCommonJob_new(String bizopEtpsno, String bondInvtNo, String startDate, String lastDate, String isAll) {
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());

        // 设置默认时间范围
        if (isBlank(startDate)) {
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(lastDate)) {
            lastDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }

        try {
            // 1. 首先获取总记录数
            long total = jgVFlyHzqdHeadService.countHzqdDataCommon(bizopEtpsno, bondInvtNo, startDate, lastDate, isAll);
            if (total == 0) {
                log.info("【syncHzqdCommonJob】未获取到远程核注单数据！");
                return Result.ok("未获取到远程核注单数据！");
            }

            // 2. 设置分页大小，根据实际情况调整
            final int pageSize = 500;
            final int totalPages = (int) Math.ceil((double) total / pageSize);
            log.info("【syncHzqdCommonJob】总数据量：{}，分页大小：{}，总页数：{}", total, pageSize, totalPages);

            int successCount = 0;
            int failCount = 0;
            StringBuilder failureMsg = new StringBuilder();

            // 3. 顺序处理每一页数据
            for (int pageNo = 1; pageNo <= totalPages; pageNo++) {
                log.info("开始处理第 {} 页数据，共 {} 页", pageNo, totalPages);

                try {
                    // 分页查询数据
                    Page<JgVFlyHzqdHead> page = new Page<>(pageNo, pageSize);
                    IPage<JgVFlyHzqdHead> pageResult = jgVFlyHzqdHeadService.listHzqdDataCommon(
                        page, bizopEtpsno, bondInvtNo, startDate, lastDate, isAll
                    );

                    if (isEmpty(pageResult.getRecords())) {
                        continue;
                    }

                    List<String> invtIdList = pageResult.getRecords().stream().map(JgVFlyHzqdHead::getId).collect(Collectors.toList());
                    List<JgVFlyHzqdDetail> jgVFlyHzqdDetailList = jgVFlyHzqdDetailService.list(new LambdaQueryWrapper<JgVFlyHzqdDetail>()
                        .in(JgVFlyHzqdDetail::getHeadId, invtIdList));
                    Map<String, List<JgVFlyHzqdDetail>> jgVFlyHzqdDetailMap = new HashMap<>();
                    if (isNotEmpty(jgVFlyHzqdDetailList)) {
                        jgVFlyHzqdDetailMap = jgVFlyHzqdDetailList.stream()
                                .collect(Collectors.groupingBy(JgVFlyHzqdDetail::getHeadId));
                    } else {
                        log.info("未获取到核注单明细数据！");
                    }

                    // 4. 处理当前页的每条数据
                    for (JgVFlyHzqdHead head : pageResult.getRecords()) {
                        try {
                            head.setJgVFlyHzqdDetailList(jgVFlyHzqdDetailMap.get(head.getId()));
                            DockingEasyPassServiceImpl currentProxy = (DockingEasyPassServiceImpl) AopContext.currentProxy();
                            currentProxy.processingSingleHead(head);
                            successCount++;
                        } catch (Exception e) {
                            log.error("处理核注单失败，单号：{}，原因：{}", head.getSeqNo(), e.getMessage());
                            failureMsg.append("单号：").append(head.getSeqNo())
                                     .append("，失败原因：").append(e.getMessage()).append("\n");
                            failCount++;
                        }
                    }

                    log.info("第 {} 页处理完成，当前成功：{}，失败：{}", pageNo, successCount, failCount);

                } catch (Exception e) {
                    log.error("处理第{}页数据时发生异常", pageNo, e);
                    failCount += pageSize; // 保守估计
                }
            }
            long end1 = System.currentTimeMillis();
            String msg = String.format("导入完成：成功%d条，失败%d条，总耗时：%d ms",
                successCount, failCount, (end1 - start1));

            if (failCount > 0) {
                // 记录失败详情到文件
                String fileName = "sync_failure_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".log";
                FileUtil.writeString(failureMsg.toString(), new File("/logs/sync/" + fileName), "UTF-8");
                msg += "，失败详情已记录到文件：" + fileName;
            }

            log.info(msg);
            return failCount > 0 ? Result.error(msg) : Result.ok(msg);
        } catch (Exception e) {
            log.error("同步核注单数据失败", e);
            return Result.error("同步失败：" + e.getMessage());
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
    }

    /**
     * 处理单条核注单数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void processingSingleHead(JgVFlyHzqdHead head) throws Exception {
        // 1. 获取明细数据
        List<JgVFlyHzqdDetail> details = head.getJgVFlyHzqdDetailList();

        if (isEmpty(details)) {
            log.warn("核注单{}没有明细数据", head.getSeqNo());
            return;
        }

        // 2. 获取租户信息
        Result<Tenant> tenant = sysBaseApi.getTenantByName(head.getBizopEtpsNm());
        Long customerTenantId = isNotEmpty(tenant) && isNotEmpty(tenant.getResult())
            ? tenant.getResult().getId()
            : -1;

        // 3. 检查是否已存在
        List<NemsInvtHead> existingHeads = nemsInvtHeadMapper.listBySeqNos(
            Collections.singletonList(head.getSeqNo())
        );

        NemsInvtHead nemsInvtHead;
        if (isNotEmpty(existingHeads)) {
            nemsInvtHead = existingHeads.get(0);
            // 获取现有明细
            List<NemsInvtList> existingLists = nemsInvtListService.list(
                new LambdaQueryWrapper<NemsInvtList>()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId())
            );
            nemsInvtHead.setNemsInvtLists(existingLists);
        } else {
            nemsInvtHead = new NemsInvtHead();
        }

        // 4. 设置核注单数据
        head.setJgVFlyHzqdDetailList(details);
        setNemsInvtByJgVFly(nemsInvtHead, head);

        if (isEmpty(nemsInvtHead.getId())) {
            nemsInvtHead.setDclTenantId(Long.valueOf(customerTenantId));
            nemsInvtHead.setTenantId(Long.valueOf(customerTenantId));
        }

        // 5. 保存数据并处理库存
        List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>();
        nemsInvtHeadList.add(nemsInvtHead);
        nemsInvtHeadService.saveNemsInvtHeadBatch(nemsInvtHeadList);
    }

    /**
     * 同步远程核注单通用 -- 根据经营单位编码
     *
     * @param bizopEtpsno
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/5 16:50
     */
    @Override
    public Result<?> syncHzqdCommonJob(String bizopEtpsno, String bondInvtNo, String startDate, String lastDate, String isAll) {
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Page<JgVFlyHzqdHead> page = new Page<>(1, Integer.MAX_VALUE);
        if (isBlank(startDate)) {
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(lastDate)) {
            lastDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }
        IPage<JgVFlyHzqdHead> jgVFlyHzqdHeadIPage = jgVFlyHzqdHeadService.listHzqdDataCommon(page, bizopEtpsno, bondInvtNo, startDate, lastDate, isAll);
        if (isEmpty(jgVFlyHzqdHeadIPage.getRecords())) {
            log.info("【syncHzqdCommonJob】未获取到远程核注单数据！");
            return Result.ok("未获取到远程核注单数据！");
        }
        List<JgVFlyHzqdHead> jgVFlyHzqdHeadList = jgVFlyHzqdHeadIPage.getRecords();
        log.info("【syncHzqdCommonJob】获取到远程核注单数据共：" + jgVFlyHzqdHeadList.size() + "条！");
        List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>(16);

        // jgVFlyHzqdHeadList根据bizopEtpsNm分组
        Map<String, List<JgVFlyHzqdHead>> jgVFlyHzqdHeadMap = jgVFlyHzqdHeadList.stream().collect(Collectors.groupingBy(JgVFlyHzqdHead::getBizopEtpsNm));
        //1.设置线程会话Token
//        UserTokenContext.setToken(getTemporaryToken());
        Map<String, Long> customerTenantIdMap = new HashMap<>(16);
        jgVFlyHzqdHeadMap.forEach((k, v) -> {
            String name = k.trim();
            if ("青岛集美客公用型保税仓库".equals(k.trim())) {
                name = "青岛集美客国际商品交易中心有限公司";
            }
//            Result<Tenant> tenant = sysBaseApi.getTenantByName(name);
            List<SysTenant> sysTenants = commonMapper.getTenantByName(name);
            Long customerTenantId;
            if (isNotEmpty(sysTenants)) {
                customerTenantId = sysTenants.get(0).getId();
            } else {
                if (bizopEtpsno.contains("37029699TU") || bizopEtpsno.contains("370299D008")) {
                    name = "青岛集美客国际商品交易中心有限公司";
                    List<SysTenant> sysTenants_ = commonMapper.getTenantByName(name);
                    customerTenantId = isNotEmpty(sysTenants_) ? sysTenants_.get(0).getId() : -1L;
                } else if (bizopEtpsno.contains("3702269875")) {
                    name = "青岛双龙汇国际贸易有限公司";
                    List<SysTenant> sysTenants_ = commonMapper.getTenantByName(name);
                    customerTenantId = isNotEmpty(sysTenants_) ? sysTenants_.get(0).getId() : -1L;
                } else {
                    customerTenantId = -1L;
                }
            }
            customerTenantIdMap.put(k, customerTenantId);
        });
        //2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
//        UserTokenContext.remove();
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> invtIdList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> invtIdListList = CollUtil.split(invtIdList, 1000);
        List<JgVFlyHzqdDetail> jgVFlyHzqdDetailTotalList = new ArrayList<>(16);
        // 2023/11/20 10:00@ZHANGCHAO 追加/变更/完善：走异步处理！！
        ThreadUtil.execAsync(() -> {
            for (List<String> list : invtIdListList) {
                List<JgVFlyHzqdDetail> jgVFlyHzqdDetailList = jgVFlyHzqdDetailService.list(new LambdaQueryWrapper<JgVFlyHzqdDetail>()
                        .in(JgVFlyHzqdDetail::getHeadId, list));
                if (isNotEmpty(jgVFlyHzqdDetailList)) {
                    jgVFlyHzqdDetailTotalList.addAll(jgVFlyHzqdDetailList);
                }
            }
            Map<String, List<JgVFlyHzqdDetail>> jgVFlyHzqdDetailMap = new HashMap<>();
            if (isNotEmpty(jgVFlyHzqdDetailTotalList)) {
                jgVFlyHzqdDetailMap = jgVFlyHzqdDetailTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyHzqdDetail::getHeadId));
            }
            List<String> seqNoList = jgVFlyHzqdHeadList.stream().map(JgVFlyHzqdHead::getSeqNo).collect(Collectors.toList());
            List<NemsInvtHead> nemsInvtHeads = nemsInvtHeadMapper.listBySeqNos(seqNoList);
            Map<String, List<NemsInvtHead>> nemsInvtHeadsMap =  new HashMap<>();
            Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
            if (isNotEmpty(nemsInvtHeads)) {
                nemsInvtHeadsMap = nemsInvtHeads.stream().collect(Collectors.groupingBy(NemsInvtHead::getSeqNo));
                List<Long> headIds = nemsInvtHeads.stream().map(NemsInvtHead::getId).collect(Collectors.toList());
                List<NemsInvtList> nemsInvtLists = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
                        .in(NemsInvtList::getInvId, headIds));
                if (isNotEmpty(nemsInvtLists)) {
                    nemsInvtListMap = nemsInvtLists.stream().collect(Collectors.groupingBy(NemsInvtList::getInvId));
                }
            }
            for (JgVFlyHzqdHead jgVFlyHzqdHead : jgVFlyHzqdHeadList) {
                jgVFlyHzqdHead.setJgVFlyHzqdDetailList(jgVFlyHzqdDetailMap.get(jgVFlyHzqdHead.getId()));
                NemsInvtHead nemsInvtHead;
                if (isNotEmpty(nemsInvtHeadsMap) && isNotEmpty(nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()))) {
                    nemsInvtHead = nemsInvtHeadsMap.get(jgVFlyHzqdHead.getSeqNo()).get(0);
                    nemsInvtHead.setNemsInvtLists(nemsInvtListMap.get(nemsInvtHead.getId()));
                } else {
                    nemsInvtHead = new NemsInvtHead();
                }
                /*
                 * 转换为平台核注单
                 */
                setNemsInvtByJgVFly(nemsInvtHead, jgVFlyHzqdHead);
                if (isEmpty(nemsInvtHead.getId())) {
                    nemsInvtHead.setDclTenantId(Long.valueOf(customerTenantIdMap.get(nemsInvtHead.getBizopEtpsNm())));
                    nemsInvtHead.setTenantId(Long.valueOf(customerTenantIdMap.get(nemsInvtHead.getBizopEtpsNm())));
                }
                nemsInvtHeadList.add(nemsInvtHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的核注单集合
            if (isNotEmpty(nemsInvtHeadList)) {
                // 分片
                List<List<NemsInvtHead>> nemsInvtHeadListList = CollUtil.split(nemsInvtHeadList, 50);
                log.info("核注单数据分片，每片50条，共" + nemsInvtHeadListList.size() +"片");
                long start = System.currentTimeMillis();
//                try {
                    // 2024/11/21 09:32@ZHANGCHAO 追加/变更/完善：因为后续要处理库存等，去掉多线程！！
                    // 初始化线程数量，最多10个吧
//                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(nemsInvtHeadListList.size(), 10));
                    for (List<NemsInvtHead> headList : nemsInvtHeadListList) {
//                        ThreadUtil.execute(() -> {
                        try {
                            Result<?> result = nemsInvtHeadService.saveNemsInvtHeadBatch(headList);
                            if (result.isSuccess()) {
                                log.info((String) result.getResult());
                            } else {
                                log.error(result.getMessage());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("保存或更新下行的核注单时出现异常:", e.getMessage());
                        }
//                        });
                        // 调用线程计数器-1
//                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
//                    countDownLatch.await();
//                } catch (Exception e) {
//                    ExceptionUtil.getFullStackTrace(e);
//                    log.error(e.getMessage(), e);
//                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyHzqdHeadList.size() + "票核注单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * @param ownerCode
     * @param ownerName
     * @return
     */
    @Override
    public Result<?> dockingDecCommonSync(String ownerCode, String ownerName, String startDate, String lastDate, String isAll) {
        if (isBlank(ownerCode) && isBlank(ownerName)) {
            return Result.error("消费使用单位编码和消费使用单位名称必有其一！");
        }
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if (isBlank(startDate)) {
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(lastDate)) {
            lastDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }
        Page<JgVFlyBgDechead> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<JgVFlyBgDechead> jgVFlyBgDecheadIPage = jgVFlyBgDecheadService.queryPageListCommon(page, ownerCode, ownerName, startDate, lastDate, isAll);
        if (isEmpty(jgVFlyBgDecheadIPage.getRecords())) {
            log.info("【dockingDecSync】未获取到远程报关单数据！");
            return Result.ok("未获取到远程报关单数据！");
        }
        List<JgVFlyBgDechead> jgVFlyBgDecheadList = jgVFlyBgDecheadIPage.getRecords();
        log.info("【dockingDecSync】获取到远程报关单数据共：" + jgVFlyBgDecheadList.size() + "条！");
        List<DecHead> decHeadList = new ArrayList<>(16);
        // 2024/11/7 10:15@ZHANGCHAO 追加/变更/完善：特殊处理！！
        if ("370226D913".equals(ownerCode) || "青岛双龙汇公司公用型保税仓库".equals(ownerName)) {
            ownerName = "青岛双龙汇国际贸易有限公司";
        }
        if ("370299D008".equals(ownerCode) || "青岛集美客公用型保税仓库".equals(ownerName)) {
            ownerName = "青岛集美客国际商品交易中心有限公司";
        }
        if (isBlank(ownerName)) {
            ownerName = jgVFlyBgDecheadList.get(0).getOwnerName();
        }
        if (isBlank(ownerName)) {
            log.info("未获取到租户名：{}", ownerName);
            return Result.error("未获取到租户信息！");
        }
        // 1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        Result<Tenant> tenant = sysBaseApi.getTenantByName(ownerName);
        Long customerTenantId;
        if (isNotEmpty(tenant.getResult())) {
            customerTenantId = tenant.getResult().getId();
        } else {
//            customerTenantId = null;
            log.info("未获取到租户名：{}", ownerName);
            return Result.error("未获取到租户信息！");
        }
        //2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> headIdList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> headIdListList = CollUtil.split(headIdList, 1000);
        List<JgVFlyBgDeclist> jgVFlyBgDeclistTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseTotalList = new ArrayList<>(16);
        List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoTotalList = new ArrayList<>(16);
        // 2022/3/10 9:39@ZHANGCHAO 追加/变更/完善：每一票都单独发送消息！
        ThreadUtil.execAsync(() -> {
            log.info("-----------异步批量插入START-----------");
            for (List<String> list : headIdListList) {
                List<JgVFlyBgDeclist> jgVFlyBgDeclistList = jgVFlyBgDeclistService.list(new LambdaQueryWrapper<JgVFlyBgDeclist>()
                        .in(JgVFlyBgDeclist::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclistList)) {
                    jgVFlyBgDeclistTotalList.addAll(jgVFlyBgDeclistList);
                }
                List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerList = jgVFlyBgDeccontainerService.list(new LambdaQueryWrapper<JgVFlyBgDeccontainer>()
                        .in(JgVFlyBgDeccontainer::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeccontainerList)) {
                    jgVFlyBgDeccontainerTotalList.addAll(jgVFlyBgDeccontainerList);
                }
                List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseList = jgVFlyBgDeclicenseService.list(new LambdaQueryWrapper<JgVFlyBgDeclicense>()
                        .in(JgVFlyBgDeclicense::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclicenseList)) {
                    jgVFlyBgDeclicenseTotalList.addAll(jgVFlyBgDeclicenseList);
                }
                List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoList = jgVFlyBgDecdocvoService.list(new LambdaQueryWrapper<JgVFlyBgDecdocvo>()
                        .in(JgVFlyBgDecdocvo::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDecdocvoList)) {
                    jgVFlyBgDecdocvoTotalList.addAll(jgVFlyBgDecdocvoList);
                }
            }
            Map<String, List<JgVFlyBgDeclist>> jgVFlyBgDeclistMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclistTotalList)) {
                jgVFlyBgDeclistMap = jgVFlyBgDeclistTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclist::getHeadId));
            }
            Map<String, List<JgVFlyBgDeccontainer>> jgVFlyBgDeccontainerMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeccontainerTotalList)) {
                jgVFlyBgDeccontainerMap = jgVFlyBgDeccontainerTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeccontainer::getHeadId));
            }
            Map<String, List<JgVFlyBgDeclicense>> jgVFlyBgDeclicenseMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclicenseTotalList)) {
                jgVFlyBgDeclicenseMap = jgVFlyBgDeclicenseTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclicense::getHeadId));
            }
            Map<String, List<JgVFlyBgDecdocvo>> jgVFlyBgDecdocvoMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDecdocvoTotalList)) {
                jgVFlyBgDecdocvoMap = jgVFlyBgDecdocvoTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDecdocvo::getHeadId));
            }
            List<String> cusCiqNoList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getCusCiqNo).collect(Collectors.toList());
            //        DynamicDataSourceContextHolder.push("master"); //手动切换
            List<DecHead> decHeads = decHeadMapper.listBySeqNos(cusCiqNoList);
            Map<String, List<DecHead>> decHeadsMap = new HashMap<>();
            Map<String, List<DecList>> decListsMap = new HashMap<>();
            if (isNotEmpty(decHeads)) {
                decHeadsMap = decHeads.stream().collect(Collectors.groupingBy(DecHead::getSeqNo));
                List<String> headIds = decHeads.stream().map(DecHead::getId).collect(Collectors.toList());
                List<DecList> decLists = decListService.list(new LambdaQueryWrapper<DecList>()
                        .in(DecList::getDecId, headIds));
                if (isNotEmpty(decLists)) {
                    decListsMap = decLists.stream().collect(Collectors.groupingBy(DecList::getDecId));
                }
            }
            for (JgVFlyBgDechead jgVFlyBgDechead : jgVFlyBgDecheadList) {
                jgVFlyBgDechead.setJgVFlyBgDeclistList(jgVFlyBgDeclistMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeccontainerList(jgVFlyBgDeccontainerMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeclicenseList(jgVFlyBgDeclicenseMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDecdocvoList(jgVFlyBgDecdocvoMap.get(jgVFlyBgDechead.getId()));
                DecHead decHead;
                if (isNotEmpty(decHeadsMap) && isNotEmpty(decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()))) {
                    decHead = decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()).get(0);
                    decHead.setDecLists(decListsMap.get(decHead.getId()));
                } else {
                    decHead = new DecHead();
                }
                if (isEmpty(decHead.getId())) {
                    decHead.setDclTenantId(isNotEmpty(customerTenantId) ? String.valueOf(customerTenantId) : null);
                    decHead.setTenantId(customerTenantId);
                }
                // 转换为平台的报关单
                try {
                    setDecHeadFromJgVFly(decHead, jgVFlyBgDechead);
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error("转换报关单时出现异常:", e.getMessage());
                }
                //20240219存在表体则取第一条表体的币制放入到表头币制字段，为了统计后续添加的字段 by zhengliansong
                decHead.setCurrency(isNotEmpty(decHead.getDecLists())?decHead.getDecLists().get(0).getCurrencyCode():null);
                decHeadList.add(decHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的报关单集合
            if (isNotEmpty(decHeadList)) {
                // 分片
                List<List<DecHead>> decHeadListList = CollUtil.split(decHeadList, 50);
                log.info("报关单数据分片，每片50条，共" + decHeadListList.size() +"片");
                long start = System.currentTimeMillis();
                try {
                    // 2024/11/21 09:32@ZHANGCHAO 追加/变更/完善：因为后续要处理库存等，去掉多线程！！
                    // 初始化线程数量，最多10个吧
//                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(decHeadListList.size(), 10));
                    for (List<DecHead> headList : decHeadListList) {
//                        ThreadUtil.execute(() -> {
                        try {
                            Result<?> result = decHeadService.saveDecHeadBatch(headList);
                            if (result.isSuccess()) {
                                log.info((String) result.getResult());
                            } else {
                                log.error(result.getMessage());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("保存或更新下行的报关单时出现异常:", e.getMessage());
                        }
//                        });
                        // 调用线程计数器-1
//                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
//                    countDownLatch.await(30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error(e.getMessage(), e);
                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
            log.info("-----------异步批量插入END-----------");
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyBgDecheadList.size() + "票报关单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     *
     * @param agentCode
     * @param agentName
     * @param startDate
     * @param lastDate
     * @param isAll
     * @return
     */
    @Override
    public Result<?> dockingDecSBCommonSync(String agentCode, String agentName, String startDate, String lastDate, String isAll) {
        if (isBlank(agentCode) && isBlank(agentName)) {
            return Result.error("申报单位编码和申报单位名称必有其一！");
        }
        long start1 = System.currentTimeMillis();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if (isBlank(startDate)) {
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(lastDate)) {
            lastDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }
        Page<JgVFlyBgDechead> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<JgVFlyBgDechead> jgVFlyBgDecheadIPage = jgVFlyBgDecheadService.queryPageListSBCommon(page, agentCode, agentName, startDate, lastDate, isAll);
        if (isEmpty(jgVFlyBgDecheadIPage.getRecords())) {
            log.info("【dockingDecSBCommonSync】未获取到远程报关单数据！");
            return Result.ok("未获取到远程报关单数据！");
        }
        List<JgVFlyBgDechead> jgVFlyBgDecheadList = jgVFlyBgDecheadIPage.getRecords();
        log.info("【dockingDecSBCommonSync】获取到远程报关单数据共：" + jgVFlyBgDecheadList.size() + "条！");
        List<DecHead> decHeadList = new ArrayList<>(16);
        if (isBlank(agentName)) {
            agentName = jgVFlyBgDecheadList.get(0).getAgentName();
        }
        if (isBlank(agentName)) {
            log.info("未获取到租户名：{}", agentName);
            return Result.error("未获取到租户信息！");
        }
        // 1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        Result<Tenant> tenant = sysBaseApi.getTenantByName(agentName);
        Long customerTenantId;
        if (isNotEmpty(tenant.getResult())) {
            customerTenantId = tenant.getResult().getId();
        } else {
//            customerTenantId = null;
            log.info("未获取到租户名：{}", agentName);
            return Result.error("未获取到租户信息！");
        }
        //2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        // 2023/11/17 16:20@ZHANGCHAO 追加/变更/完善：优化代码，加快速度！！！
        List<String> headIdList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getId).collect(Collectors.toList());
        // 2023/11/17 16:46@ZHANGCHAO 追加/变更/完善：对方服务器最大支持2100，分片请求！！！
        List<List<String>> headIdListList = CollUtil.split(headIdList, 1000);
        List<JgVFlyBgDeclist> jgVFlyBgDeclistTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerTotalList = new ArrayList<>(16);
        List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseTotalList = new ArrayList<>(16);
        List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoTotalList = new ArrayList<>(16);
        // 2022/3/10 9:39@ZHANGCHAO 追加/变更/完善：每一票都单独发送消息！
        ThreadUtil.execAsync(() -> {
            log.info("-----------异步批量插入START-----------");
            for (List<String> list : headIdListList) {
                List<JgVFlyBgDeclist> jgVFlyBgDeclistList = jgVFlyBgDeclistService.list(new LambdaQueryWrapper<JgVFlyBgDeclist>()
                        .in(JgVFlyBgDeclist::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclistList)) {
                    jgVFlyBgDeclistTotalList.addAll(jgVFlyBgDeclistList);
                }
                List<JgVFlyBgDeccontainer> jgVFlyBgDeccontainerList = jgVFlyBgDeccontainerService.list(new LambdaQueryWrapper<JgVFlyBgDeccontainer>()
                        .in(JgVFlyBgDeccontainer::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeccontainerList)) {
                    jgVFlyBgDeccontainerTotalList.addAll(jgVFlyBgDeccontainerList);
                }
                List<JgVFlyBgDeclicense> jgVFlyBgDeclicenseList = jgVFlyBgDeclicenseService.list(new LambdaQueryWrapper<JgVFlyBgDeclicense>()
                        .in(JgVFlyBgDeclicense::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDeclicenseList)) {
                    jgVFlyBgDeclicenseTotalList.addAll(jgVFlyBgDeclicenseList);
                }
                List<JgVFlyBgDecdocvo> jgVFlyBgDecdocvoList = jgVFlyBgDecdocvoService.list(new LambdaQueryWrapper<JgVFlyBgDecdocvo>()
                        .in(JgVFlyBgDecdocvo::getHeadId, list));
                if (isNotEmpty(jgVFlyBgDecdocvoList)) {
                    jgVFlyBgDecdocvoTotalList.addAll(jgVFlyBgDecdocvoList);
                }
            }
            Map<String, List<JgVFlyBgDeclist>> jgVFlyBgDeclistMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclistTotalList)) {
                jgVFlyBgDeclistMap = jgVFlyBgDeclistTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclist::getHeadId));
            }
            Map<String, List<JgVFlyBgDeccontainer>> jgVFlyBgDeccontainerMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeccontainerTotalList)) {
                jgVFlyBgDeccontainerMap = jgVFlyBgDeccontainerTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeccontainer::getHeadId));
            }
            Map<String, List<JgVFlyBgDeclicense>> jgVFlyBgDeclicenseMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDeclicenseTotalList)) {
                jgVFlyBgDeclicenseMap = jgVFlyBgDeclicenseTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDeclicense::getHeadId));
            }
            Map<String, List<JgVFlyBgDecdocvo>> jgVFlyBgDecdocvoMap = new HashMap<>();
            if (isNotEmpty(jgVFlyBgDecdocvoTotalList)) {
                jgVFlyBgDecdocvoMap = jgVFlyBgDecdocvoTotalList.stream()
                        .collect(Collectors.groupingBy(JgVFlyBgDecdocvo::getHeadId));
            }
            List<String> cusCiqNoList = jgVFlyBgDecheadList.stream().map(JgVFlyBgDechead::getCusCiqNo).collect(Collectors.toList());
            //        DynamicDataSourceContextHolder.push("master"); //手动切换
            List<DecHead> decHeads = decHeadMapper.listBySeqNos(cusCiqNoList);
            Map<String, List<DecHead>> decHeadsMap = new HashMap<>();
            Map<String, List<DecList>> decListsMap = new HashMap<>();
            if (isNotEmpty(decHeads)) {
                decHeadsMap = decHeads.stream().collect(Collectors.groupingBy(DecHead::getSeqNo));
                List<String> headIds = decHeads.stream().map(DecHead::getId).collect(Collectors.toList());
                List<DecList> decLists = decListService.list(new LambdaQueryWrapper<DecList>()
                        .in(DecList::getDecId, headIds));
                if (isNotEmpty(decLists)) {
                    decListsMap = decLists.stream().collect(Collectors.groupingBy(DecList::getDecId));
                }
            }
            for (JgVFlyBgDechead jgVFlyBgDechead : jgVFlyBgDecheadList) {
                jgVFlyBgDechead.setJgVFlyBgDeclistList(jgVFlyBgDeclistMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeccontainerList(jgVFlyBgDeccontainerMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDeclicenseList(jgVFlyBgDeclicenseMap.get(jgVFlyBgDechead.getId()));
                jgVFlyBgDechead.setJgVFlyBgDecdocvoList(jgVFlyBgDecdocvoMap.get(jgVFlyBgDechead.getId()));
                DecHead decHead;
                if (isNotEmpty(decHeadsMap) && isNotEmpty(decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()))) {
                    decHead = decHeadsMap.get(jgVFlyBgDechead.getCusCiqNo()).get(0);
                    decHead.setDecLists(decListsMap.get(decHead.getId()));
                } else {
                    decHead = new DecHead();
                }
//                if (isEmpty(decHead.getId())) {
                    decHead.setDclTenantId(isNotEmpty(customerTenantId) ? String.valueOf(customerTenantId) : null);
                    decHead.setTenantId(customerTenantId);
//                }
                // 转换为平台的报关单
                try {
                    setDecHeadFromJgVFly(decHead, jgVFlyBgDechead);
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error("转换报关单失败", e.getMessage());
                }
                //20240219存在表体则取第一条表体的币制放入到表头币制字段，为了统计后续添加的字段 by zhengliansong
                decHead.setCurrency(isNotEmpty(decHead.getDecLists())?decHead.getDecLists().get(0).getCurrencyCode():null);
                decHeadList.add(decHead);
            }
            long end1 = System.currentTimeMillis();
            log.info("查询组装数据共耗时：" + (end1 - start1));
            // 转换后的报关单集合
            if (isNotEmpty(decHeadList)) {
                // 分片
                List<List<DecHead>> decHeadListList = CollUtil.split(decHeadList, 50);
                log.info("报关单数据分片，每片50条，共" + decHeadListList.size() +"片");
                long start = System.currentTimeMillis();
                try {
                    // 2024/11/21 09:32@ZHANGCHAO 追加/变更/完善：因为后续要处理库存等，去掉多线程！！
                    // 初始化线程数量，最多10个吧
//                    CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(Math.min(decHeadListList.size(), 10));
                    for (List<DecHead> headList : decHeadListList) {
//                        ThreadUtil.execute(() -> {
                        try {
                            Result<?> result = decHeadService.saveDecHeadBatch(headList);
                            if (result.isSuccess()) {
                                log.info((String) result.getResult());
                            } else {
                                log.error(result.getMessage());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("保存或更新下行的报关单时出现异常:", e.getMessage());
                        }
//                        });
                        // 调用线程计数器-1
//                        countDownLatch.countDown();
                    }
                    // 唤醒主线程
//                    countDownLatch.await(30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error(e.getMessage(), e);
                }
                long end = System.currentTimeMillis();
                log.info("导入数据共耗时：" + (end - start));
            }
            log.info("-----------异步批量插入END-----------");
        });
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        String msg = "导入数据：共查询并导入" + jgVFlyBgDecheadList.size() + "票报关单!";
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 赋值报关单
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2023/7/5 16:19
     */
    private void setDecHeadFromJgVFly(DecHead decHead, JgVFlyBgDechead jgVFlyBgDechead) {
        try {
            decHead.setTotal(isNotBlank(jgVFlyBgDechead.getAlldeclTotal()) ? new BigDecimal(jgVFlyBgDechead.getAlldeclTotal()) : null);
            decHead.setIsDocking("1");
            decHead.setDecStatus(jgVFlyBgDechead.getCusDecStatus()); // 申报状态
            if (isNotBlank(jgVFlyBgDechead.getCusDecStatus())) {
                Integer status = null;
                if ("10".equals(jgVFlyBgDechead.getCusDecStatus())){
                    status = 49;
                }else if ("1".equals(jgVFlyBgDechead.getCusDecStatus())){
                    status = 10;
                }else if ("2".equals(jgVFlyBgDechead.getCusDecStatus()) || "4".equals(jgVFlyBgDechead.getCusDecStatus())){
                    status = 44;
                }else if ("9".equals(jgVFlyBgDechead.getCusDecStatus()) || "7".equals(jgVFlyBgDechead.getCusDecStatus())){
                    status = 48;
                }
                decHead.setStatus(status);
            }
            decHead.setSeqNo(jgVFlyBgDechead.getCusCiqNo()); // 统一编号
            decHead.setClearanceNo(jgVFlyBgDechead.getEntryId()); // 报关单号 ？？entryId海关编号也是，用哪个？
            decHead.setRecordNumber(jgVFlyBgDechead.getManualNo()); // 备案号
            decHead.setIeFlag(jgVFlyBgDechead.getCusIEFlag()); // 进出口标识
            decHead.setOutDate(jgVFlyBgDechead.getIEDate()); // 进出日期
            try {
                decHead.setAppDate(isNotBlank(jgVFlyBgDechead.getDDate()) ? DateUtil.parseDate(jgVFlyBgDechead.getDDate()) : null); // 申报日期
            } catch (Exception e) {
                log.error("转换申报日期出现异常：{}", e.getMessage());
            }
            decHead.setShipTypeCode(jgVFlyBgDechead.getCusTrafMode()); // 运输方式
            decHead.setShipName(jgVFlyBgDechead.getTrafName()); // 运输工具名称
            decHead.setVoyage(jgVFlyBgDechead.getCusVoyageNo()); // 航次号
            decHead.setContract(jgVFlyBgDechead.getContrNo()); // 合同协议号
            decHead.setBillCode(jgVFlyBgDechead.getBillNo()); // 提运单号
            decHead.setDeclarePlace(jgVFlyBgDechead.getCustomMaster()); // 申报地海关
            decHead.setOutPortCode(jgVFlyBgDechead.getIEPort()); // 进出境关别
            if ("I".equals(decHead.getIeFlag())) {
                decHead.setOverseasConsignorCode(jgVFlyBgDechead.getConsignorCode()); // 境外收发货人代码
                decHead.setOverseasConsignorEname(jgVFlyBgDechead.getConsignorEname()); // 境外收发货人英文名称
                decHead.setOptUnitSocialCode(jgVFlyBgDechead.getRcvgdTradeScc()); // 境内收发货人社会统一信用代码
                decHead.setOptUnitId(jgVFlyBgDechead.getRcvgdTradeCode()); // 境内收发货人海关编码
                decHead.setTradeCiqCode(jgVFlyBgDechead.getConsigneeCode()); // 境内收发货人检验检疫代码
                decHead.setOptUnitName(jgVFlyBgDechead.getConsigneeCname()); // 境内收发货人名称

                decHead.setEntyPortCode(jgVFlyBgDechead.getCiqEntyPortCode()); // 入境口岸
                decHead.setDespPortCode(jgVFlyBgDechead.getDespPortCode()); // 启运港代码--进口
                decHead.setDesPort(jgVFlyBgDechead.getDistinatePort()); // 经停港--进口
            } else {
                decHead.setOverseasConsigneeCode(jgVFlyBgDechead.getConsigneeCode()); // 境外收发货人代码
                decHead.setOverseasConsigneeEname(jgVFlyBgDechead.getConsigneeEname()); // 境外收发货人名称
                decHead.setOptUnitSocialCode(jgVFlyBgDechead.getCnsnTradeScc()); // 境内收发货人社会统一信用代码
                decHead.setOptUnitId(jgVFlyBgDechead.getCnsnTradeCode()); // 境内收发货人海关编码
                decHead.setTradeCiqCode(jgVFlyBgDechead.getConsignorCode()); // 境内收发货人检验检疫代码
                decHead.setOptUnitName(jgVFlyBgDechead.getConsignorCname()); // 境内收发货人名称

                decHead.setEntyPortCode(jgVFlyBgDechead.getDespPortCode()); // 离境口岸
                decHead.setDesPort(jgVFlyBgDechead.getDistinatePort()); // 指运港--出口
            }
            decHead.setDeliverUnitSocialCode(jgVFlyBgDechead.getOwnerScc()); // 消费使用单位社会统一信用代码
            decHead.setDeliverUnit(jgVFlyBgDechead.getOwnerCode()); // 消费使用单位海关代码
            decHead.setOwnerCiqCode(jgVFlyBgDechead.getOwnerCiqCode()); // 消费使用单位检验检疫编码
            decHead.setDeliverUnitName(jgVFlyBgDechead.getOwnerName()); // 消费使用单位名称
            decHead.setDeclareUnitSocialCode(jgVFlyBgDechead.getAgentScc()); // 申报单位社会统一信用代码
            decHead.setDeclareUnit(jgVFlyBgDechead.getAgentCode()); // 申报单位海关代码
            decHead.setDeclCiqCode(jgVFlyBgDechead.getDeclRegNo()); // 申报单位检验检疫编码
            decHead.setDeclareUnitName(jgVFlyBgDechead.getAgentName()); // 申报单位名称
            decHead.setTradeCountry(jgVFlyBgDechead.getCusTradeNationCode()); // 贸易国
            decHead.setArrivalArea(jgVFlyBgDechead.getCusTradeCountry()); // 启运国
            decHead.setTermsTypeCode(jgVFlyBgDechead.getTransMode()); // 成交方式
            decHead.setTradeTypeCode(jgVFlyBgDechead.getSupvModeCdde()); // 监管方式
            decHead.setTaxTypeCode(jgVFlyBgDechead.getCutMode()); // 征免性质
            decHead.setShipFeeCode(jgVFlyBgDechead.getFeeMark()); // 运费代码
            decHead.setShipFee(isNotBlank(jgVFlyBgDechead.getFeeRate()) ? new BigDecimal(jgVFlyBgDechead.getFeeRate()) : null); // 运费值
            decHead.setShipCurrencyCode(jgVFlyBgDechead.getFeeCurr()); // 运费币制
            decHead.setInsuranceCode(jgVFlyBgDechead.getInsurMark()); // 保费代码
            decHead.setInsurance(isNotBlank(jgVFlyBgDechead.getInsurRate()) ? new BigDecimal(jgVFlyBgDechead.getInsurRate()) : null); // 保费值
            decHead.setInsuranceCurr(jgVFlyBgDechead.getInsurCurr()); // 保费币制
            decHead.setExtrasCode(jgVFlyBgDechead.getOtherMark()); // 杂费代码
            decHead.setExtras(isNotBlank(jgVFlyBgDechead.getOtherRate()) ? new BigDecimal(jgVFlyBgDechead.getOtherRate()) : null); // 杂费值
            decHead.setOtherCurr(jgVFlyBgDechead.getOtherCurr()); // 杂费币制
            decHead.setPacks(isNotBlank(jgVFlyBgDechead.getPackNo()) ? Integer.valueOf(jgVFlyBgDechead.getPackNo()) : null); // 件数
            decHead.setGrossWeight(isNotBlank(jgVFlyBgDechead.getGrossWt()) ? new BigDecimal(jgVFlyBgDechead.getGrossWt()) : null); // 毛重
            decHead.setNetWeight(isNotBlank(jgVFlyBgDechead.getNetWt()) ? new BigDecimal(jgVFlyBgDechead.getNetWt()) : null); // 净重
            decHead.setLicenceNumber(jgVFlyBgDechead.getLicenseNo()); // 许可证号
            decHead.setPacksKinds(jgVFlyBgDechead.getWrapType()); // 包装种类
            decHead.setPackType(jgVFlyBgDechead.getDecOtherPacksVo()); // 其他包装
            if (isNotBlank(jgVFlyBgDechead.getDecOtherPacksVo())) {
                try {
                    JSONArray jsonArray = JSONArray.parseArray(jgVFlyBgDechead.getDecOtherPacksVo());
                    List<Map<String, String>> strList = JSONArray.parseObject(jsonArray.toString(), List.class);
                    String packType = "";
                    for (Map<String, String> map : strList) {
                        for (Map.Entry<String, String> entryMap : map.entrySet()) {
                            if ("packType".equals(entryMap.getKey())) {
                                if ("".equals(packType)) {
                                    packType = entryMap.getValue();
                                } else {
                                    packType = new StringBuffer().append(packType).append(',').append(entryMap.getValue()).toString();
                                }
                            }
                        }
                    }
                    decHead.setPackType(packType);
                } catch (Exception e) {
                    log.error("处理[其他包装]出现异常：" + e.getMessage());
                }
            }
            decHead.setContainerNum(jgVFlyBgDechead.getContaCount()); // 集装箱数
            decHead.setClearanceType(jgVFlyBgDechead.getEntryType()); // 报关单类型
            decHead.setGoodsPlace(jgVFlyBgDechead.getGoodsPlace()); // 货物存放地点
            decHead.setContractAtt(jgVFlyBgDechead.getAttaDocuCdstr()); // ？随附单证
            decHead.setMarkNo(jgVFlyBgDechead.getMarkNo()); // 标记唛码
            try {
                decHead.setPromiseItmes(isNotBlank(jgVFlyBgDechead.getPromiseItems()) ? String.join("|", jgVFlyBgDechead.getPromiseItems().split("")) : null); // 特殊关系/价格说明
            } catch (Exception e) {
                log.error("处理特殊关系/价格说明出现异常：" + e.getMessage());
            }
            decHead.setBillType(jgVFlyBgDechead.getBillType()); // 备案清单类型
            decHead.setRelId(jgVFlyBgDechead.getRelativeId()); // 关联报关单号
            decHead.setRelManNo(jgVFlyBgDechead.getRelmanNo()); // 关联备案号
            decHead.setBonNo(jgVFlyBgDechead.getBonNo()); // 保税监管场地
            decHead.setCusFie(jgVFlyBgDechead.getCustomsField()); // 场地代码
            decHead.setOrgCode(jgVFlyBgDechead.getOrgCode()); // 检验检疫受理机关 商检信息
            decHead.setInspOrgCode(jgVFlyBgDechead.getInspOrgCode()); // 口岸检验检疫机关 商检信息
            decHead.setPurpOrgCode(jgVFlyBgDechead.getPurpOrgCode()); // 目的地检验检疫机关 商检信息
            decHead.setVsaOrgCode(jgVFlyBgDechead.getVsaOrgCode()); // 领证机关 商检信息
            decHead.setBlNo(jgVFlyBgDechead.getCiqBillNo()); // B/LNO 商检信息
            decHead.setSpecDeclFlag(jgVFlyBgDechead.getSpecDeclFlag()); // 特殊业务标识 商检信息
            decHead.setDespDate(jgVFlyBgDechead.getDespDate()); // 启运日期 格式为：yyyyMMdd 商检信息
            decHead.setCmplDschrgDt(jgVFlyBgDechead.getCmplDschrgDt()); // 卸毕日期 格式为：yyyyMMdd ？？？？？？
            decHead.setCorrelationNo(jgVFlyBgDechead.getCorrelationDeclNo()); // 关联号码 商检信息
            decHead.setCorrelationReasonFlag(jgVFlyBgDechead.getCorrelationReasonFlag()); // 关联理由 商检信息
            decHead.setOrigBoxFlag(jgVFlyBgDechead.getOrigBoxFlag()); // 原集装箱标识 商检信息
//        decHead.setCopLimitType(jgVFlyBgDechead.getPreDecEntQualifListVo());
            // 企业资质信息 商检信息[{"EntQualifNo":"123","EntQualifTypeCode":"456"}]
            if (isNotBlank(jgVFlyBgDechead.getPreDecEntQualifListVo())) {
                String coplimittype = jgVFlyBgDechead.getPreDecEntQualifListVo()
                        .replaceAll("entQualifTypeCode", "EntQualifTypeCode")
                        .replaceAll("entQualifNo", "EntQualifNo")
                        .replaceAll("entQualifTypeName", "EntQualifTypeName");
                decHead.setCopLimitType(coplimittype);
            }
//        decHead.setDecUserType(jgVFlyBgDechead.getPreDecUserList());
            // 使用人信息表 商检信息[{"UseOrgPersonCode":"123","UseOrgPersonTel":"456"}]
            if (isNotBlank(jgVFlyBgDechead.getPreDecUserList())) {
                String decuserlist = jgVFlyBgDechead.getPreDecUserList()
                        .replaceAll("useOrgPersonCode", "UseOrgPersonCode")
                        .replaceAll("useOrgPersonTel", "UseOrgPersonTel");
                decHead.setDecUserType(decuserlist);
            }
//        decHead.setRequestCertType(jgVFlyBgDechead.getPreDecRequCertList());
            // 检验检疫签证申报要素 商检信息[{"AppCertCode":"123","ApplOri","456","ApplCopyQuan":"789"}]
            if (isNotBlank(jgVFlyBgDechead.getPreDecRequCertList())) {
                String requcertlist = jgVFlyBgDechead.getPreDecRequCertList()
                        .replaceAll("appCertCode", "AppCertCode")
                        .replaceAll("applOri", "ApplOri")
                        .replaceAll("applCopyQuan", "ApplCopyQuan");
                decHead.setRequestCertType(requcertlist);
            }
            decHead.setDeclarantNo(jgVFlyBgDechead.getDclrNo()); // 报关人员证号
            decHead.setClearanceMode(jgVFlyBgDechead.getSpecPassFlag()); // 通关模式 报文用
            decHead.setEdiId(jgVFlyBgDechead.getEdiId()); // 报关标志 1：普通报关 3：北方转关提前 5：南方转关提前 6：普通报关，运输工具名称以‘◎’开头，南方H2000直转 报文用
            decHead.setAudited(isNotEmpty(jgVFlyBgDechead.getIsAudit()) && 1 == jgVFlyBgDechead.getIsAudit()); // 是否审核
//        decHead.setEtpsInnerInvtNo(jgVFlyBgDechead.getCopNo()); // 企业内部清单编号 （由企业自行编写） 与核注单关联
            decHead.setInputErName(jgVFlyBgDechead.getInputErName()); // 录入人
            try {
                decHead.setReleaseDate(isNotBlank(jgVFlyBgDechead.getFxsj()) ? DateUtil.parse(jgVFlyBgDechead.getFxsj(), "yyyy-MM-dd HH:mm:ss") : null); // 放行时间
                decHead.setFinalDate(isNotBlank(jgVFlyBgDechead.getJgsj()) ? DateUtil.parse(jgVFlyBgDechead.getJgsj(), "yyyy-MM-dd HH:mm:ss") : null); // 结关时间
            } catch (Exception e) {
                log.error("转换放行/结关日期出现异常：" + e.getMessage());
            }
            // 2023/7/20 9:49@ZHANGCHAO 追加/变更/完善：备注！！！
            decHead.setMarkNumber(jgVFlyBgDechead.getNoteS());
            if (isNotBlank(jgVFlyBgDechead.getDclTrnRelFlag())) {
                if ("2".equals(jgVFlyBgDechead.getDclTrnRelFlag())) {// 备案清单
                    decHead.setBillType("1");
                    decHead.setDecType("ML");
                }
            }
            decHead.setDclTrnRelFlag(jgVFlyBgDechead.getDclTrnRelFlag());
            // 2023/7/21 17:03@ZHANGCHAO 追加/变更/完善：ic卡号 报关人员证号
            decHead.setIcNumber(jgVFlyBgDechead.getTypistNo()); // Ic卡号
            decHead.setDeclarantNo(jgVFlyBgDechead.getDclrNo()); // 报关人员证号

            // 业务事项(单据类型) TODO:有问题,根据报关单类型判断
            String decType = "";
            Boolean chkSurety = false;
            String value = jgVFlyBgDechead.getCusRemark();
            if (isNotBlank(value)) {
                if (value.toString().length() < 12) {
                    value = value + String.format(
                            new StringBuffer("%0").append(12 - value.toString().length()).append("d").toString(), 0);
                }
                if ("1".equals(value.toString().substring(0, 1))) {
                    decType = "Z ";// 自报自缴
                } else if ("1".equals(value.toString().substring(1, 2))) {
                    decType = "CL";// 汇总征税
                } else if ("1".equals(value.toString().substring(3, 4))) {
                    decType = "SZ";// 水运中转一般报关单
                    if ("2".equals(jgVFlyBgDechead.getDclTrnRelFlag())) {
                        decType = "SM";// 水运中转保税区进出境备案清单
                    }
                } else if ("1".equals(value.toString().substring(5, 6))) {
                    chkSurety = true;// 担保验放
                } else if ("1".equals(value.toString().substring(11, 12))) {
                    // decType = decType + "CL";//跨境电商海外仓
                }
                if ("11".equals(value.toString().subSequence(0, 2))) {
                    decType = "ZC";// 自报自缴，汇总征税报关单
                }
                decHead.setDecType(decType);
                decHead.setChkSurety(chkSurety);// 担保验放
            }
            // 同步设定
            decHead.setSynchronism(true);
            decHead.setSynchronismDate(new Date());
            decHead.setDownLinkMark(true);
            decHead.setSend("1");
            decHead.setPushStatus("1");
//        decHead.setFromWhere("New");
            decHead.setFlyId(jgVFlyBgDechead.getId());
            LoginUser loginUser;
            try {
                loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            } catch (Exception e) {
    //            log.error("获取登录用户信息失败！");
                loginUser = new LoginUser();
                loginUser.setUsername("admin");
            }
            if (isNotBlank(decHead.getId())) {
                decHead.setUpdateBy(isNotBlank(decHead.getInputErName()) ? decHead.getInputErName() : loginUser.getUsername());
                decHead.setUpdateDate(new Date());
            } else {
                decHead.setTenantName(jgVFlyBgDechead.getAgentName());
                decHead.setCreatePerson(isNotBlank(decHead.getCreatePerson()) ? decHead.getCreatePerson() : (isNotBlank(decHead.getInputErName()) ? decHead.getInputErName() : loginUser.getUsername()));
                decHead.setCustomsCode(decHead.getSeqNo());
                decHead.setInputId(decHead.getDclTenantId());
                decHead.setCreateTime(decHead.getCreateTime() == null ? new Date() : decHead.getCreateTime());
            }
            /*
             * 转换报关单表体
             */
            setDecListFromJgVFly(decHead, jgVFlyBgDechead);
            /*
             * 转换报关单集装箱
             */
            setDecContainerFromJgVFly(decHead, jgVFlyBgDechead);
            /*
             * 转换报关单随附单证
             */
            setDecLicenseDocusFromJgVFly(decHead, jgVFlyBgDechead);
            /*
             * 转换报关单随附单据
             */
            setDecDocvoFromJgVFly(decHead, jgVFlyBgDechead);

            if (isNotEmpty(decHead.getDecLicenseDocuses())) {
                try {
                    String contractAtt = "";
                    for (DecLicenseDocus licenseDocus : decHead.getDecLicenseDocuses()){
                        if (isBlank(contractAtt)){
                            contractAtt = licenseDocus.getDocuCode();
                        }else {
                            contractAtt = contractAtt + "," + licenseDocus.getDocuCode();
                        }
                    }
                    decHead.setContractAtt(contractAtt);
                } catch (Exception e) {
                    log.error("处理这个啥子的时候报错：contractAtt" + e.getMessage());
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("转换报关单头失败：" + e.getMessage());
        }
    }

    /**
     * 转换报关单表体
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2023/7/7 14:18
     */
    private void setDecListFromJgVFly(DecHead decHead, JgVFlyBgDechead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getJgVFlyBgDeclistList())) {
            Map<String, DecList> decListMap = new HashMap<>(16);
            if (isNotEmpty(decHead.getId())) {
                List<DecList> decLists = decHead.getDecLists();
                if (isEmpty(decLists)) {
//                    decLists = decListService.list(new LambdaQueryWrapper<DecList>().eq(DecList::getDecId, decHead.getId()));
                }
                if (isNotEmpty(decLists)) {
                    for (DecList decList : decLists) {
                        decListMap.put(decList.getDecId()+"|"+decList.getItem(), decList);
                    }
                }
            }
            List<DecList> decListList = new ArrayList<>(16);
            for (JgVFlyBgDeclist jgVFlyBgDeclist : jgVFlyBgDechead.getJgVFlyBgDeclistList()) {
                DecList decList = isNotEmpty(decListMap.get(decHead.getId()+"|"+jgVFlyBgDeclist.getGNo())) ? decListMap.get(decHead.getId()+"|"+jgVFlyBgDeclist.getGNo()) : new DecList();
//                Long origId = decList.getId();
//                BeanUtil.copyProperties(jgVFlyBgDeclist, decList, CopyOptions.create().ignoreNullValue());
//                decList.setId(origId);
                decList.setDecId(isNotEmpty(decList.getDecId()) ? decList.getDecId() : decHead.getId());
                decList.setCustomsCode(decHead.getCustomsCode());
                decList.setItem(isNotBlank(jgVFlyBgDeclist.getGNo()) ? Integer.valueOf(jgVFlyBgDeclist.getGNo()) : null); // 项号
                decList.setRecordItem(isNotBlank(jgVFlyBgDeclist.getContrItem()) ? Integer.valueOf(jgVFlyBgDeclist.getContrItem()) : null); // 备案序号
                decList.setHscode(jgVFlyBgDeclist.getCodeTs()); // 商品编码 税号
                decList.setHsname(jgVFlyBgDeclist.getGName()); // 商品名称 申报品名
                decList.setHsmodel(jgVFlyBgDeclist.getGModel()); // 规格型号 申报要素
                decList.setGoodsCount(isNotBlank(jgVFlyBgDeclist.getGQty()) ? new BigDecimal(jgVFlyBgDeclist.getGQty()) : null); // 成交数量
                decList.setUnitCode(jgVFlyBgDeclist.getGUnit()); // 成交单位
                decList.setDesCountry(jgVFlyBgDeclist.getCusOriginCountry()); // 原产国
                decList.setPrice(isNotBlank(jgVFlyBgDeclist.getDeclPrice()) ? new BigDecimal(jgVFlyBgDeclist.getDeclPrice()) : null); // 单价
                decList.setTotal(isNotBlank(jgVFlyBgDeclist.getDeclTotal()) ? new BigDecimal(jgVFlyBgDeclist.getDeclTotal()) : null); // 总价
                decList.setCurrencyCode(jgVFlyBgDeclist.getTradeCurr()); // 币制
                decList.setFaxTypeCode(jgVFlyBgDeclist.getDutyMode()); // 征免方式
                decList.setUnit1(jgVFlyBgDeclist.getUnit1()); // 法定单位
                decList.setCount1(isNotBlank(jgVFlyBgDeclist.getQty1()) ? new BigDecimal(jgVFlyBgDeclist.getQty1()) : null); // 法定数量
                decList.setUnit2(jgVFlyBgDeclist.getUnit2()); // 第二法定单位
                decList.setCount2(isNotBlank(jgVFlyBgDeclist.getQty2()) ? new BigDecimal(jgVFlyBgDeclist.getQty2()) : null); // 第二法定数量
                decList.setDestinationCountry(jgVFlyBgDeclist.getDestinationCountry()); // 最终目的国
                decList.setOrigPlaceCode(jgVFlyBgDeclist.getOrigPlaceCode()); // 原产地区代码
                decList.setPurpose(jgVFlyBgDeclist.getPurpose()); // 用途代码
                decList.setMnufctrRegName(jgVFlyBgDeclist.getEngManEntCnm()); // 2023/7/24 9:51@ZHANGCHAO 追加/变更/完善：// 境外生产企业名称 检验检疫货物规格
                decList.setDistrictCode(jgVFlyBgDeclist.getDistrictCode()); // 境内目的地/境内货源地
                decList.setDistrictName(jgVFlyBgDeclist.getDistrictCodeName()); // 2025/5/12 11:48@ZHANGCHAO 追加/变更/完善：境内目的地/境内货源地名称
                decList.setDestCode(jgVFlyBgDeclist.getCiqDestCode()); // 目的地代码 境内目的地/境内货源地辅助字段
                decList.setDestName(jgVFlyBgDeclist.getCiqDestCodeName()); // 2025/5/12 11:48@ZHANGCHAO 追加/变更/完善：目的地代码 境内目的地/境内货源地辅助字段名称
                decList.setGoodsAttr(jgVFlyBgDeclist.getGoodsAttr()); // 货物属性代码
                decList.setStuff(jgVFlyBgDeclist.getStuff()); // 成份/原料/组份 检验检疫货物规格
                decList.setGoodsSpec(jgVFlyBgDeclist.getGoodsSpec()); // 货物规格 检验检疫货物规格
                decList.setGoodsModel(jgVFlyBgDeclist.getGoodsModel()); // 货物型号 检验检疫货物规格
                decList.setGoodsBrand(jgVFlyBgDeclist.getGoodsBrand()); // 货物品牌 检验检疫货物规格
                decList.setProdValidDt(jgVFlyBgDeclist.getProdValidDt()); // 产品有效期 格式：yyyyMMdd 检验检疫货物规格
                decList.setProduceDate(jgVFlyBgDeclist.getProduceDate()); // 生产日期 格式：yyyy-MM-dd,多个日期用英文半角分号分隔 检验检疫货物规格
                decList.setProdBatchNo(jgVFlyBgDeclist.getProdBatchNo()); // 生产批号 检验检疫货物规格
                decList.setProdQgp(jgVFlyBgDeclist.getProdQgp()); // 产品保质期 检验检疫货物规格
                decList.setEngManEntCnm(jgVFlyBgDeclist.getEngManEntCnm()); // 境外生产企业名称 检验检疫货物规格 ???
                decList.setNoDangFlag(jgVFlyBgDeclist.getNoDangFlag()); // 非危险化学品 危险货物信息
                decList.setUncode(jgVFlyBgDeclist.getUnCode()); // UN编码 危险货物信息
                decList.setDangName(jgVFlyBgDeclist.getDangName()); // 危险货物名称 危险货物信息
                decList.setDangPackType(jgVFlyBgDeclist.getPackType()); // 危包类别 危险货物信息
                decList.setDangPackSpec(jgVFlyBgDeclist.getPackSpec()); // 危包规格 危险货物信息
//                decList.setGoodsLimitType(jgVFlyBgDeclist.getPreDecCiqGoodsLimit()); // 许可证信息
                if (isNotBlank(jgVFlyBgDeclist.getPreDecCiqGoodsLimit())) {
                    String decRequCertList = jgVFlyBgDeclist.getPreDecCiqGoodsLimit()
                            .replaceAll("licenceNo", "LicenceNo")
                            .replaceAll("licTypeCode", "LicTypeCode")
                            .replaceAll("licWrtofDetailNo", "LicWrtofDetailNo")
                            .replaceAll("licWrtofQty", "LicWrtofQty")
                            .replaceAll("licWrtofQtyUnit", "LicWrtofQtyUnit")
                            .replaceAll("licTypeName", "LicTypeName");
                    decList.setGoodsLimitType(decRequCertList);
                }
                // 2023/7/24 10:31@ZHANGCHAO 追加/变更/完善：协定享惠！！！
                if (isNotBlank(jgVFlyBgDeclist.getPreDecCiqXiangHui()) && !"[]".equals(jgVFlyBgDeclist.getPreDecCiqXiangHui())) {
                    try {
                        JSONObject o = JSONObject.parseObject(jgVFlyBgDeclist.getPreDecCiqXiangHui());
                        if (isNotEmpty(o)) {
                            DecEcoRelation decEcoRelation = new DecEcoRelation();
                            decEcoRelation.setDecId(decHead.getId());
                            decEcoRelation.setCertType(isNotEmpty(o.get("preTradeAgreeCode")) ? o.get("preTradeAgreeCode").toString() : null);
                            decEcoRelation.setEcoCertCode(isNotEmpty(o.get("oriCertType")) ? o.get("oriCertType").toString() : null);
                            decEcoRelation.setEcoCertNo(isNotEmpty(o.get("certOriCode")) ? o.get("certOriCode").toString() : null);
                            decEcoRelation.setEcoGno(isNotEmpty(o.get("certOriModItemNum")) ? o.get("certOriModItemNum").toString() : null);
                            decEcoRelation.setCertCode("Y");
                            decList.setRcepOrigPlaceCode(isNotEmpty(o.get("rcepOrigPlaceDocCode")) ? o.get("rcepOrigPlaceDocCode").toString() : null);
                            if (StringUtils.isEmpty(decEcoRelation.getEcoGno())){
                                decList.setRcepOrigPlaceCode(null);
                            }else {
                                decEcoRelation.setDecGno(decList.getItem().toString());
                                decList.setDecEcoRelation(decEcoRelation);
                            }
                            decList.setDecEcoRelation(decEcoRelation);
                        }
                    } catch (Exception e) {
                       ExceptionUtil.getFullStackTrace(e);
                       log.error("解析协定享惠失败：{}", e.getMessage());
                    }
                }

                decList.setSupvModecd(jgVFlyBgDeclist.getCusSupvDmd()); // 监管条件 系统用
                decList.setNetWeight(isNotBlank(jgVFlyBgDeclist.getCiqWeight()) ? new BigDecimal(jgVFlyBgDeclist.getCiqWeight()) : null); // 净重 系统用
                decList.setDgFlag("Y".equals(jgVFlyBgDeclist.getDangerFlag())); // 危险品标志
                decList.setRcepOrigPlaceCode(jgVFlyBgDeclist.getRcepOrigPlaceCode()); // 优惠贸易协定项下原产地
                // 2023/7/20 16:31@ZHANGCHAO 追加/变更/完善：检验检疫名称
                decList.setCiqCode(jgVFlyBgDeclist.getCiqCode());
                decList.setCiqName(jgVFlyBgDeclist.getCiqName());
                decListList.add(decList);
            }
            decHead.setDecLists(decListList);
        }
    }

    /**
     * 处理报关单集装箱
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2023/7/7 16:18
     */
    private void setDecContainerFromJgVFly(DecHead decHead, JgVFlyBgDechead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getJgVFlyBgDeccontainerList())) {
            List<DecContainer> decContainerList = new ArrayList<>(16);
            for (JgVFlyBgDeccontainer jgVFlyBgDeccontainer : jgVFlyBgDechead.getJgVFlyBgDeccontainerList()) {
                DecContainer decContainer = new DecContainer();
                decContainer.setDecId(decHead.getId());
                decContainer.setCustomsCode(decHead.getCustomsCode());
                decContainer.setContainerId(jgVFlyBgDeccontainer.getContainerNo());
                decContainer.setContainerMd(isNotBlank(jgVFlyBgDeccontainer.getContainerMdCode()) ? jgVFlyBgDeccontainer.getContainerMdCode() : jgVFlyBgDeccontainer.getCntnrModeCode());
                decContainer.setGoodsNo(jgVFlyBgDeccontainer.getGoodsNo());
                decContainer.setLclFlag(jgVFlyBgDeccontainer.getLclFlag());
                decContainer.setGoodsContaWt(isNotBlank(jgVFlyBgDeccontainer.getContainerWt()) ? new BigDecimal(jgVFlyBgDeccontainer.getContainerWt()) : null);
                decContainerList.add(decContainer);
            }
            decHead.setDecContainers(decContainerList);
        }
    }

    /**
     * 处理报关单随附单证
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2023/7/10 8:30
     */
    private void setDecLicenseDocusFromJgVFly(DecHead decHead, JgVFlyBgDechead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getJgVFlyBgDeclicenseList())) {
            List<DecLicenseDocus> decLicenseDocusList = new ArrayList<>(16);
            for (JgVFlyBgDeclicense jgVFlyBgDeclicense : jgVFlyBgDechead.getJgVFlyBgDeclicenseList()) {
                DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
                decLicenseDocus.setDecId(decHead.getId());
                decLicenseDocus.setCustomsCode(decHead.getCustomsCode());
                decLicenseDocus.setDocuCode(jgVFlyBgDeclicense.getAcmpFormCode());
                decLicenseDocus.setCertCode(jgVFlyBgDeclicense.getAcmpFormNo());
                decLicenseDocusList.add(decLicenseDocus);
            }
            decHead.setDecLicenseDocuses(decLicenseDocusList);
        }
    }

    /**
     * 处理报关单随附单据
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2023/7/10 10:43
     */
    private void setDecDocvoFromJgVFly(DecHead decHead, JgVFlyBgDechead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getJgVFlyBgDecdocvoList())) {
            List<DecAttachment> decAttachmentList = new ArrayList<>(16);
            for (JgVFlyBgDecdocvo jgVFlyBgDecdocvo : jgVFlyBgDechead.getJgVFlyBgDecdocvoList()) {
                DecAttachment decAttach = new DecAttachment();
                decAttach.setAttachmentName(jgVFlyBgDecdocvo.getEntOrigFileName());
                decAttach.setAttachmentNo(jgVFlyBgDecdocvo.getAttEdocNo());
                decAttach.setAttachmentType(jgVFlyBgDecdocvo.getAttTypeCode());
                decAttach.setRelatedId(jgVFlyBgDecdocvo.getCusCiqNo());
                decAttach.setEdocFomatType(jgVFlyBgDecdocvo.getAttFmtTypeCode());
                decAttach.setRemark(jgVFlyBgDecdocvo.getBelongWkunitCode());
                try {
                    decAttach.setUploadTime(isNotBlank(jgVFlyBgDecdocvo.getUpdateTime()) ? DateUtil.parse(jgVFlyBgDecdocvo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss") : null);
                } catch (Exception e) {
                    log.error("转换上传日期出现异常：" + e.getMessage());
                }
                decAttach.setUser(jgVFlyBgDecdocvo.getUpdateUser());
                decAttach.setGNoStr(jgVFlyBgDecdocvo.getGNoStr());
                decAttach.setDclType("DEC");
                if(decAttach.getAttachmentName()!=null) {
                    decAttach.setSuffix(decAttach.getAttachmentName().replaceFirst(".*\\.(.+)$","$1"));
                }
                decAttachmentList.add(decAttach);
            }
            decHead.setDecAttachments(decAttachmentList);
        }
    }

    /**
     * 转换为平台核注单
     *
     * @param nemsInvtHead
     * @param jgVFlyHzqdHead
     * @return void
     * <AUTHOR>
     * @date 2023/7/11 9:44
     */
    private void setNemsInvtByJgVFly(NemsInvtHead nemsInvtHead, JgVFlyHzqdHead jgVFlyHzqdHead) {
        nemsInvtHead.setBondInvtNo(jgVFlyHzqdHead.getBondInvtNo()); // 清单编号 （返填 - 海关审批通过后系统自动返填）
        nemsInvtHead.setSeqNo(jgVFlyHzqdHead.getSeqNo()); // 清单预录入统一编号 （返填 - 第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号）
        nemsInvtHead.setChgTmsCnt(isNotBlank(jgVFlyHzqdHead.getChgTmsCnt()) ? Integer.valueOf(jgVFlyHzqdHead.getChgTmsCnt()) : null); // 变更次数
        nemsInvtHead.setPutrecNo(jgVFlyHzqdHead.getPutrecNo()); // 备案编号
        nemsInvtHead.setEtpsInnerInvtNo(jgVFlyHzqdHead.getEtpsInnerInvtNo()); // 企业内部清单编号 （由企业自行编写）
        nemsInvtHead.setBizopEtpsSccd(jgVFlyHzqdHead.getBizopEtpsSccd()); // 经营企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setBizopEtpsno(jgVFlyHzqdHead.getBizopEtpsno()); // 经营企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setBizopEtpsNm(jgVFlyHzqdHead.getBizopEtpsNm()); // 经营企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRcvgdEtpsno(jgVFlyHzqdHead.getRcvgdEtpsno()); // 收货企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRvsngdEtpsSccd(jgVFlyHzqdHead.getRvsngdEtpsSccd()); // 收发货企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRcvgdEtpsNm(jgVFlyHzqdHead.getRcvgdEtpsNm()); // 收货企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setDclEtpsSccd(jgVFlyHzqdHead.getDclEtpsSccd()); // 申报企业社会信用代码
        nemsInvtHead.setDclEtpsno(jgVFlyHzqdHead.getDclEtpsno()); // 申报企业编号
        nemsInvtHead.setDclEtpsNm(jgVFlyHzqdHead.getDclEtpsNm()); // 申报企业名称
        try {
            nemsInvtHead.setInvtDclTime(isNotBlank(jgVFlyHzqdHead.getInvtDclTime())
                    ? DateUtil.parse(jgVFlyHzqdHead.getInvtDclTime(), DatePattern.PURE_DATE_PATTERN) : null); // 清单申报时间 （返填 - 系统自动反填）
            nemsInvtHead.setEntryDclTime(isNotBlank(jgVFlyHzqdHead.getEntryDclTime())
                    ? DateUtil.parse(jgVFlyHzqdHead.getEntryDclTime(), DatePattern.PURE_DATE_PATTERN) : null); // 报关单申报日期 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
        } catch (Exception e) {
            log.error("转换清单申报时间/报关单申报日期出现异常：" + e.getMessage());
        }
        nemsInvtHead.setEntryNo(jgVFlyHzqdHead.getEntryNo()); // 对应报关单编号 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
        nemsInvtHead.setRltinvtNo(jgVFlyHzqdHead.getRltInvtNo()); // 关联清单编号 （结转类专用，检控要求复杂，见需求文档）
        nemsInvtHead.setRltputrecNo(jgVFlyHzqdHead.getRltPutrecNo()); // 关联备案编号 （结转类专用）
        nemsInvtHead.setRltEntryNo(jgVFlyHzqdHead.getRltEntryNo()); // 关联报关单编号 （可录入或者系统自动生成报关单后返填二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）

        nemsInvtHead.setRltEntryBizopEtpsSccd(jgVFlyHzqdHead.getRltEntryRvsngdEtpsSccd()); // 关联报关单消费使用单位社会信用代码
        nemsInvtHead.setRltEntryBizopEtpsno(jgVFlyHzqdHead.getRltEntryRcvgdEtpsno()); // 关联报关单消费使用单位编号
        nemsInvtHead.setRltEntryBizopEtpsNm(jgVFlyHzqdHead.getRltEntryRcvgdEtpsNm()); // 关联报关单消费使用单位名称
        nemsInvtHead.setRltEntryRvsngdEtpsSccd(jgVFlyHzqdHead.getRltEntryBizopEtpsSccd()); // 关联报关单境内收发货单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
        nemsInvtHead.setRltEntryRcvgdEtpsno(jgVFlyHzqdHead.getRltEntryBizopEtpsno()); // 关联报关单境内收发货单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填，报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。）
        nemsInvtHead.setRltEntryRcvgdEtpsNm(jgVFlyHzqdHead.getRltEntryBizopEtpsNm()); // 关联报关单境内收发货单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
        nemsInvtHead.setRltEntryDclEtpsSccd(jgVFlyHzqdHead.getRltEntryDclEtpsSccd()); // 关联报关单申报单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
        nemsInvtHead.setRltEntryDclEtpsno(jgVFlyHzqdHead.getRltEntryDclEtpsno()); // 关联报关单海关申报单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
        nemsInvtHead.setRltEntryDclEtpsNm(jgVFlyHzqdHead.getRltEntryDclEtpsNm()); // 关联报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）

        nemsInvtHead.setImpexpPortcd(jgVFlyHzqdHead.getImpexpPortcd()); // 进出口口岸代码
        nemsInvtHead.setDclplcCuscd(jgVFlyHzqdHead.getDclPlcCuscd()); // 申报地关区代码
        nemsInvtHead.setImpexpMarkcd(jgVFlyHzqdHead.getImpexpMarkcd()); // 进出口标记代码 （返填 - I：进口，E：出口）
        nemsInvtHead.setMtpckEndprdMarkcd(jgVFlyHzqdHead.getMtpckEndprdMarkcd()); // 料件成品标记代码 （I：料件，E：成品）
        nemsInvtHead.setSupvModecd(jgVFlyHzqdHead.getSupvModecd()); // 监管方式代码
        nemsInvtHead.setTrspModecd(jgVFlyHzqdHead.getTrspModecd()); // 运输方式代码
        nemsInvtHead.setDclcusFlag(jgVFlyHzqdHead.getDclcusFlag()); // 是否报关标志 （1.报关 2.非报关）
        nemsInvtHead.setDclcusTypecd(jgVFlyHzqdHead.getDclcusTypecd()); // 报关类型代码 （1.关联报关 2.对应报关；当报关标志为“1.报关”时，企业可选择“关联报关单”/“对应报关单”；当报关标志填写为“2.非报关”时，报关标志填写为“2.非报关”该项不可填。）
        nemsInvtHead.setVrfdedMarkcd(jgVFlyHzqdHead.getVrfdedMarkcd()); // 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
        nemsInvtHead.setInvtIochkptStucd(jgVFlyHzqdHead.getInvtIochkptStucd()); // 清单进出卡口状态代码 （系统自动反填。未出卡口，已出卡口。需求不明确，暂留）
        try {
            nemsInvtHead.setPrevdTime(isNotBlank(jgVFlyHzqdHead.getPrevdTime()) ? DateUtil.parse(jgVFlyHzqdHead.getPrevdTime(), DatePattern.PURE_DATE_PATTERN) : null); // 预核扣时间
        } catch (Exception e) {
            log.error("转换预核扣时间出现异常：" + e.getMessage());
        }
        try {
            nemsInvtHead.setFormalVrfdedTime(isNotBlank(jgVFlyHzqdHead.getFormalVrfdedTime()) ? DateUtil.parse(jgVFlyHzqdHead.getFormalVrfdedTime(), DatePattern.PURE_DATE_PATTERN) : null); // 正式核扣时间 （返填）
        } catch (Exception e) {
            log.error("转换正式核扣时间 （返填）出现异常：" + e.getMessage());
        }
        nemsInvtHead.setApplyNo(jgVFlyHzqdHead.getApplyNo()); // 申请表编号
        nemsInvtHead.setListType(jgVFlyHzqdHead.getListType()); // 流转类型 （非流转类不填写，流转类填写：A：加工贸易深加工结转、B：加工贸易余料结转、C：不作价设备结转、D：区间深加工结转、E：区间料件结转）
        nemsInvtHead.setInputCode(jgVFlyHzqdHead.getInputCode()); // 录入企业编号 （保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setInputCreditCode(jgVFlyHzqdHead.getInputCreditCode()); // 录入企业社会信用代码 （返填 - 保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setInputName(jgVFlyHzqdHead.getInputName()); // 录入单位名称 （保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setIcCardNo(jgVFlyHzqdHead.getIcCardNo()); // 申报人IC卡号 （企业端专用）
        try {
            nemsInvtHead.setInputTime(isNotBlank(jgVFlyHzqdHead.getInputTime()) ? DateUtil.parse(jgVFlyHzqdHead.getInputTime(), DatePattern.PURE_DATE_PATTERN) : null); // 录入日期 （企业端专用）
        } catch (Exception e) {
            log.error("转换录入日期 （企业端专用）出现异常：" + e.getMessage());
        }
        try {
            nemsInvtHead.setAddTime(isNotBlank(jgVFlyHzqdHead.getAddTime()) ? DateUtil.parse(jgVFlyHzqdHead.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null); // 录入日期 （企业端专用）
        } catch (Exception e) {
            log.error("转换AddTime出现异常：" + e.getMessage());
        }
        nemsInvtHead.setListStat(jgVFlyHzqdHead.getListStat()); // 清单状态 （系统自动反填。1-已申报、C-退单、改单、删单、审核通过）
        nemsInvtHead.setCorrEntryDclEtpsSccd(jgVFlyHzqdHead.getCorrEntryDclEtpsSccd()); // 对应报关单申报单位社会统一信用代码
        nemsInvtHead.setCorrEntryDclEtpsno(jgVFlyHzqdHead.getCorrEntryDclEtpsNo()); // 对应报关单申报单位代码 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
        nemsInvtHead.setCorrEntryDclEtpsNm(jgVFlyHzqdHead.getCorrEntryDclEtpsNm()); // 对应报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
        nemsInvtHead.setDecType(jgVFlyHzqdHead.getDecType()); // 报关单类型 （1-进口报关单
        nemsInvtHead.setStshipTrsarvNatcd(jgVFlyHzqdHead.getStshipTrsarvNatcd()); // 起运运抵国别代码
        nemsInvtHead.setInvtType(jgVFlyHzqdHead.getInvtType()); // 清单类型 （(SAS项目新增)标识清单类别，0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：异常补录，默认为0：普通清单）
        nemsInvtHead.setEntryStucd(jgVFlyHzqdHead.getEntryStucd()); // 报关状态 （(SAS项目新增)标明对应（关联）报关单放行状态，目前只区分 0：未放行，1：已放行。该字段用于区域或物流账册的清单，该类型清单满足两个条件才能核扣：报关单被放行+货物全部过卡）
        nemsInvtHead.setRmk(jgVFlyHzqdHead.getRmk()); // 备注
        nemsInvtHead.setGenDecFlag(jgVFlyHzqdHead.getGenDecFlag()); // 是否系统生成报关单,报文使用: 1生成,2不生成
//        nemsInvtHead.setStatus(jgVFlyHzqdHead.getStatus()); // 0-暂存、1-申报成功、4成功发送海关、5-海关接收成功、6-海关接收失败、B-海关终审通过、C-海关退单、E-删除、N-待导入其他报文、P-预审批通过
        nemsInvtHead.setStatus(jgVFlyHzqdHead.getListStat()); // 0-暂存、1-申报成功、4成功发送海关、5-海关接收成功、6-海关接收失败、B-海关终审通过、C-海关退单、E-删除、N-待导入其他报文、P-预审批通过
        nemsInvtHead.setFlyId(jgVFlyHzqdHead.getId());
//        nemsInvtHead.setTenantId(isNotEmpty(nemsInvtHead.getTenantId()) ? nemsInvtHead.getTenantId() : 1310053879995457537L);
//        nemsInvtHead.setInputId(isNotEmpty(nemsInvtHead.getInputId()) ? nemsInvtHead.getInputId() : 1310053881811587074L);
        LoginUser loginUser;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            log.error("获取登录用户信息失败！");
            loginUser = new LoginUser();
            loginUser.setUsername("admin");
        }
        if (isNotEmpty(nemsInvtHead.getId())) {
            nemsInvtHead.setUpdateBy(isNotBlank(nemsInvtHead.getCreatePerson()) ? nemsInvtHead.getCreatePerson() : loginUser.getUsername());
            nemsInvtHead.setUpdateDate(new Date());
        } else {
            nemsInvtHead.setCreatePerson(isNotBlank(nemsInvtHead.getCreatePerson()) ? nemsInvtHead.getCreatePerson() : loginUser.getUsername()); // 创建人员
            nemsInvtHead.setCreateDate(new Date()); // 创建日期
        }
        //下行回来的自动默认已推送
        nemsInvtHead.setSend(true);


//        nemsInvtHead.setFromWhere("docking");
        /*
         * 转换报关单表体
         */
        setNemsInvtListFromJgVFly(nemsInvtHead, jgVFlyHzqdHead);
    }

    /**
     * 赋值核注单表体
     *
     * @param nemsInvtHead
     * @param jgVFlyHzqdHead
     * @return void
     * <AUTHOR>
     * @date 2023/7/11 13:54
     */
    private void setNemsInvtListFromJgVFly(NemsInvtHead nemsInvtHead, JgVFlyHzqdHead jgVFlyHzqdHead) {
        if (isNotEmpty(jgVFlyHzqdHead.getJgVFlyHzqdDetailList())) {
            Map<String, NemsInvtList> invtListMap = new HashMap<>(16);
            if (isNotEmpty(nemsInvtHead.getId())) {
                List<NemsInvtList> nemsInvtListList = nemsInvtHead.getNemsInvtLists();
                if (isNotEmpty(nemsInvtListList)) {
                    for (NemsInvtList nemsInvtList : nemsInvtListList) {
                        invtListMap.put(nemsInvtList.getInvId() + "|" + nemsInvtList.getGdsseqNo(), nemsInvtList);
                    }
                }
            }
            List<NemsInvtList> nemsInvtListList = new ArrayList<>(16);
            for (JgVFlyHzqdDetail jgVFlyHzqdDetail : jgVFlyHzqdHead.getJgVFlyHzqdDetailList()) {
                NemsInvtList nemsInvtList = isNotEmpty(invtListMap.get(nemsInvtHead.getId() + "|" + jgVFlyHzqdDetail.getGdsSeqno())) ? invtListMap.get(nemsInvtHead.getId() + "|" + jgVFlyHzqdDetail.getGdsSeqno()) : new NemsInvtList();
                nemsInvtList.setSeqNo(jgVFlyHzqdDetail.getSeqNo()); // 中心统一编号 (返填 - 系统暂存时自动生成并返填）
                nemsInvtList.setGdsseqNo(isNotBlank(jgVFlyHzqdDetail.getGdsSeqno()) ? Integer.valueOf(jgVFlyHzqdDetail.getGdsSeqno()) : null); // 商品序号
                nemsInvtList.setPutrecSeqno(isNotBlank(jgVFlyHzqdDetail.getPutrecSeqno()) ? Integer.valueOf(jgVFlyHzqdDetail.getPutrecSeqno()) : null); // 备案序号(对应底账序号）
                // 2024/11/6 18:50@ZHANGCHAO 追加/变更/完善：自动备案序号！！
                nemsInvtList.setAutoNo(isNotBlank(jgVFlyHzqdDetail.getParam3()) ? jgVFlyHzqdDetail.getParam3() : null);
                if (isEmpty(nemsInvtList.getPutrecSeqno())) {
                    nemsInvtList.setPutrecSeqno(isNotBlank(nemsInvtList.getAutoNo()) ? Integer.valueOf(nemsInvtList.getAutoNo()) : null);
                }
                nemsInvtList.setGdsMtno(jgVFlyHzqdDetail.getGdsMtno()); // 商品料号 （企业可录入，也可根据企业录入的备案序号从备案数据中获取并返填）
                nemsInvtList.setHscode(jgVFlyHzqdDetail.getGdecd()); // 商品编码 （系统自动返填。参数值如下：0-未修改1-修改2-删除3-增加）
                nemsInvtList.setHsname(jgVFlyHzqdDetail.getGdsNm()); // 商品名称 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setHsmodel(jgVFlyHzqdDetail.getGdsSpcfModelDesc()); // 商品规格型号 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setDclUnitcd(jgVFlyHzqdDetail.getDclUnitcd()); // 申报计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setLawfUnitcd(jgVFlyHzqdDetail.getLawfUnitcd()); // 法定计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setSecdlawfUnitcd(jgVFlyHzqdDetail.getSecdLawfUnitcd()); // 法定第二计量 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setNatcd(jgVFlyHzqdDetail.getDestinationNatcd()); // 最终目的国
                nemsInvtList.setDclUprcamt(isNotBlank(jgVFlyHzqdDetail.getDclUprcAmt()) ? new BigDecimal(jgVFlyHzqdDetail.getDclUprcAmt()) : null); // 企业申报单价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setDclTotalamt(isNotBlank(jgVFlyHzqdDetail.getDclTotalAmt()) ? new BigDecimal(jgVFlyHzqdDetail.getDclTotalAmt()) : null); // 企业申报总价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setUsdstatTotalamt(isNotBlank(jgVFlyHzqdDetail.getUsdStatTotalAmt()) ? new BigDecimal(jgVFlyHzqdDetail.getUsdStatTotalAmt()) : null); // 美元统计总金额
                nemsInvtList.setDclCurrcd(jgVFlyHzqdDetail.getDclCurrcd()); // 币制
                nemsInvtList.setLawfQty(isNotBlank(jgVFlyHzqdDetail.getLawfQty()) ? new BigDecimal(jgVFlyHzqdDetail.getLawfQty()) : null); // 法定数量
                nemsInvtList.setSecdLawfQty(isNotBlank(jgVFlyHzqdDetail.getSecdLawfQty()) ? new BigDecimal(jgVFlyHzqdDetail.getSecdLawfQty()) : null); // 第二法定数量 （当法定第二计量单位为空时，该项为非必填）
                nemsInvtList.setWtsfVal(isNotBlank(jgVFlyHzqdDetail.getWtSfVal()) ? new BigDecimal(jgVFlyHzqdDetail.getWtSfVal()) : null); // 重量比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setFstsfVal(isNotBlank(jgVFlyHzqdDetail.getFstSfVal()) ? new BigDecimal(jgVFlyHzqdDetail.getFstSfVal()) : null); // 第一比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setSecdsfVal(isNotBlank(jgVFlyHzqdDetail.getSecdSfVal()) ? new BigDecimal(jgVFlyHzqdDetail.getSecdSfVal()) : null); // 第二比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setDclQty(isNotBlank(jgVFlyHzqdDetail.getDclQty()) ? new BigDecimal(jgVFlyHzqdDetail.getDclQty()) : null); // 申报数量* （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setGrossWt(isNotBlank(jgVFlyHzqdDetail.getGrossWt()) ? new BigDecimal(jgVFlyHzqdDetail.getGrossWt()) : null); // 毛重
                nemsInvtList.setNetWt(isNotBlank(jgVFlyHzqdDetail.getNetWt()) ? new BigDecimal(jgVFlyHzqdDetail.getNetWt()) : null); // 净重
                nemsInvtList.setUseCd(jgVFlyHzqdDetail.getUseCd()); // 用途代码*
                nemsInvtList.setLvyrlfModecd(jgVFlyHzqdDetail.getLvyrlfModecd()); // 征免方式
                nemsInvtList.setUcnsVerno(jgVFlyHzqdDetail.getUcnsVerno()); // 单耗版本号 （成品可填。手册不填，账册由开关控制是否必填。需看单耗该字段如何定义）（E02-04）
                nemsInvtList.setEntryGdsSeqno(isNotBlank(jgVFlyHzqdDetail.getEntryGdsSeqno()) ? Integer.valueOf(jgVFlyHzqdDetail.getEntryGdsSeqno()) : null); // 报关单商品序号  （企业可录入，如果企业不录入，系统自动返填）（E02-05）
                nemsInvtList.setClymarkcd(jgVFlyHzqdDetail.getClyMarkcd()); // 归类标志
                nemsInvtList.setApplyTbSeqnoA(isNotBlank(jgVFlyHzqdDetail.getApplyTbSeqno()) ? Integer.valueOf(jgVFlyHzqdDetail.getApplyTbSeqno()) : null); // 流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
                nemsInvtList.setApplyTbSeqnoB(jgVFlyHzqdDetail.getApplyTbSeqno()); // 流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
                try {
                    nemsInvtList.setAddTime(isNotBlank(jgVFlyHzqdHead.getAddTime()) ? DateUtil.parse(jgVFlyHzqdHead.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null); // 入库时间 （返填）
                } catch (Exception e) {
                    log.error("转换入库时间 （返填）出现异常：{}", e.getMessage());
                }
                nemsInvtList.setPassPortusedQty(isNotBlank(jgVFlyHzqdDetail.getPassportUsedQty()) ? new BigDecimal(jgVFlyHzqdDetail.getPassportUsedQty()) : null); // 核放单已用数量 （(SAS项目新增) 已生成核放单的商品数量，用于控制核放单商品数量超量）
                nemsInvtList.setRmk(jgVFlyHzqdDetail.getRmk()); // 备注
                nemsInvtList.setOriginCountry(jgVFlyHzqdDetail.getNatcd()); // 原产国
                nemsInvtListList.add(nemsInvtList);
            }
            nemsInvtHead.setNemsInvtLists(nemsInvtListList);
        }
        /*
         * 处理下表体，防止下行时对方视图里表体有删除的，本地还存在！！
         * 2025/1/6 10:15@ZHANGCHAO
         */
        if (isNotEmpty(nemsInvtHead.getId()) && isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
            List<NemsInvtList> oldInvtLists = nemsInvtListService.list(new QueryWrapper<NemsInvtList>().lambda()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
            if (isNotEmpty(oldInvtLists)) {
                // 使用 Stream 进行过滤
                List<NemsInvtList> dels = oldInvtLists.stream()
                        .filter(item -> nemsInvtHead.getNemsInvtLists().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (NemsInvtList del : dels) {
                        nemsInvtListService.removeById(del.getId());
                        log.info("【setNemsInvtListFromJgVFly】删除本地多余表体：{} - {} - {} - {} - {}", nemsInvtHead.getBondInvtNo(), del.getGdsseqNo(), del.getGdsMtno(), del.getHscode(), del.getHsname());
                    }
                }
            }
        }
    }

    /**
     * 获取临时令牌
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/19 上午11:16
     */
    public String getTemporaryToken() {
        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        LoginUser sysUser = commonMapper.getUserByName("admin");
        // 模拟登录生成Token
        String token = JwtUtil.sign(sysUser.getUsername(), sysUser.getPassword());
        // 设置Token缓存有效时间为 5 分钟
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, 5 * 60 * 1000);
        return token;
    }

}
