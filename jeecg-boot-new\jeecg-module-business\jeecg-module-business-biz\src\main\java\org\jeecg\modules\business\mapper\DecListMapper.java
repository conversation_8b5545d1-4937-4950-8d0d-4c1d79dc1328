package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.business.entity.DecList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.OrderInfo;
import org.jeecg.modules.business.vo.DictModelVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Mapper
public interface DecListMapper extends BaseMapper<DecList> {

    List<DictModelVO> getDictItemByCode(String dictCode);

    DictModelVO getDictItemByCodeSpecific(String dictCode,String code);

    List<DecList> getDecListByInvtIdAndItem(String invId, String item);

    List<DecList> getDecListByClearanceNoAndItem(String entryNo, List<String> item);

    IPage<DecList> queryPageList(Page<DecList> page,
                                   @Param("createPerson") String createPerson,
                                 @Param("tenantId") String tenantId,
                                 @Param("hscode") String hscode,
                                 @Param("hsname") String hsname,
                                 @Param("declareUnitSocialCode")String declareUnitSocialCode,
                                 @Param("IE_FLAG")String IE_FLAG,
                                 @Param("HSMODEL")String HSMODEL,
                                 @Param("deliverUnitName")String deliverUnitName,
                                 @Param("optUnitSocialCode") String optUnitSocialCode,
                                 @Param("optUnitId") String optUnitId,
                                 @Param("optUnitName") String optUnitName);
    List<DecList> getDecListHsnamePriceByHaname(@Param("hsnameList")List<String> hsnameList,@Param("tenantId")String tenantId);

    List<DecList> getDecListHsnameNetWeightByHaname(@Param("hsnameList")List<String> hsnameList,@Param("tenantId")String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    List<DecList> listDecListsByCond(String decId);

    void updateHide(String id);
}
