<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DecListMapper">

    <select id="getDictItemByCode" resultType="org.jeecg.modules.business.vo.DictModelVO">
        SELECT item.ITEM_TEXT AS TEXT,item.ITEM_VALUE AS VALUE
        FROM
            SYS_DICT dict, SYS_DICT_ITEM item
        WHERE dict.ID = item.DICT_ID
            AND dict.DICT_CODE = #{dictCode}
    </select>

    <select id="getDictItemByCodeSpecific" resultType="org.jeecg.modules.business.vo.DictModelVO">
        SELECT item.ITEM_TEXT AS TEXT,item.ITEM_VALUE AS VALUE
        FROM
            SYS_DICT dict, SYS_DICT_ITEM item
        WHERE dict.ID = item.DICT_ID
            AND dict.DICT_CODE = #{dictCode} AND item.ITEM_VALUE = #{code}
    </select>
    <select id="getDecListByInvtIdAndItem" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            DEC_LIST.*,DEC_HEAD.SEQ_NO,
            DEC_HEAD.IE_FLAG
        FROM DEC_LIST,DEC_HEAD
        WHERE DEC_ID = DEC_HEAD.ID
            AND INV_ID = #{invId}
            AND ITEM = #{item}
    </select>
    <select id="getDecListByClearanceNoAndItem" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            DEC_LIST.*,DEC_HEAD.SEQ_NO,
            DEC_HEAD.IE_FLAG
        FROM DEC_LIST,DEC_HEAD,nems_invt_head
        WHERE DEC_LIST.DEC_ID = DEC_HEAD.ID
          AND DEC_HEAD.CLEARANCE_NO = nems_invt_head.ENTRY_NO
          AND DEC_HEAD.CLEARANCE_NO = #{entryNo}
        AND DEC_LIST.ITEM IN
        <foreach item="item" collection="item" open="(" close=")" separator=",">
            #{item}
        </foreach>


    </select>

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.DecList">
        select dl.*,dh.OPT_UNIT_NAME optUnitName,dh.MARK_NUMBER markNumber,dh.DELIVER_UNIT_NAME deliverUnitName
        from
             dec_head dh inner join dec_list dl on dh.ID = dl.DEC_ID
<where>
    ISHIDE ='0'
    <if test="tenantId != null and tenantId != ''">
        and dh.TENANT_ID = #{tenantId}
    </if>
    <if test="declareUnitSocialCode != null and declareUnitSocialCode != ''">
        and dh.DECLARE_UNIT_SOCIAL_CODE = #{declareUnitSocialCode}
    </if>
    <if test="IE_FLAG != null and IE_FLAG != ''">
        and dh.IE_FLAG = #{IE_FLAG}
    </if>
    <if test="createPerson != null and createPerson != ''">
        and dh.CREATE_PERSON = #{createPerson}
    </if>
    <if test="hscode != null and hscode != ''">
        and dl.HSCODE LIKE concat('%',#{hscode},'%')
    </if>
    <if test="hsname != null and hsname != ''">
        and dl.HSNAME = #{hsname}
    </if>
    <if test="optUnitSocialCode != null and optUnitSocialCode != ''">
        and dh.OPT_UNIT_SOCIAL_CODE LIKE concat('%',#{optUnitSocialCode},'%')
    </if>
    <if test="optUnitId != null and optUnitId != ''">
        and dh.OPT_UNIT_ID LIKE concat('%',#{optUnitId},'%')
    </if>
    <if test="optUnitName != null and optUnitName != ''">
        and dh.OPT_UNIT_NAME LIKE concat('%',#{optUnitName},'%')
    </if>
    <if test="HSMODEL != null and HSMODEL != ''">
        and dh.HSMODEL LIKE concat('%',#{HSMODEL},'%')
    </if>
    <if test="deliverUnitName != null and deliverUnitName != ''">
        and dl.DELIVER_UNIT_NAME LIKE concat('%',#{deliverUnitName},'%')
    </if>
</where>
        order by dh.CREATE_TIME desc
    </select>

    <select id="getDecListHsnamePriceByHaname" resultType="org.jeecg.modules.business.entity.DecList">
        select dl.HSNAME,
               MAX(dl.PRICE) total,
               MIN(dl.PRICE) price
        from
        dec_List dl inner join dec_head dh
        on dh.id = dl.DEC_ID
        where dh.TENANT_ID = #{tenantId}
        AND dl.HSNAME IN
        <foreach collection="hsnameList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY dl.HSNAME
    </select>
    <select id="getDecListHsnameNetWeightByHaname" resultType="org.jeecg.modules.business.entity.DecList">
        select dl.HSNAME,
        MAX(dl.NET_WEIGHT) total,
        MIN(dl.NET_WEIGHT) price
        from
        dec_List dl inner join dec_head dh
        on dh.id = dl.DEC_ID
        where dh.TENANT_ID = #{tenantId}
        AND dl.HSNAME IN
        <foreach collection="hsnameList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY dl.HSNAME
    </select>
    <select id="listDecListsByCond" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            *
        FROM
            `dec_list`
        WHERE
            DEC_ID = #{decId}
    </select>
    <update id="updateHide">
        UPDATE
        dec_list
        SET
        ISHIDE = '1'
        <where>
            ID = #{id}
        </where>
    </update>
</mapper>
