package org.jeecg.modules.business.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiOperation;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.DecContainerFields;
import org.jeecg.common.aspect.annotation.DecHeadFields;
import org.jeecg.common.base.BaseMap;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.common.modules.redis.client.JeecgRedisClient;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.base.entity.SysAnnouncement;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DecLogicVerificationDTO;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.excel.ExportDecExcel;
import org.jeecg.modules.business.entity.marketProcurement.DTO.MarketProcurementTransferDTO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.PrintUtil;
import org.jeecg.modules.business.util.marketProcurement.CustomsDeclarationDataProcessingUtil;
import org.jeecg.modules.business.vo.DecStatisticsVO;
import org.jeecg.modules.business.vo.DictModelVO;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static org.jeecg.common.constant.CommonConstant.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Slf4j
@RestController
@RequestMapping("/DecHead/dec-head")
public class DecHeadController extends JeecgController<DecHead, IDecHeadService> {

    @Autowired
    private IDecHeadService decHeadService;
    @Autowired
    private IDecListService decListService;
    @Autowired
    private IDecLicenseDocusService decLicenseDocusService;
    @Autowired
    private IErpCiqService erpCiqService;
    @Autowired
    private IEdiStatusHistoryService ediStatusHistoryService;
    @Autowired
    private ShopsInfoMapper shopsInfoMapper;
    @Autowired
    private PurchaserInfoMapper purchaserInfoMapper;
    @Autowired
    private DecShopsPurchaserRelMapper decShopsPurchaserRelMapper;
    @Autowired
    private CommissionerMapper commissionerMapper;
    //关别
    @Autowired
    private IErpCustomsPortsService erpCustomsPortsService;
    //运输方式
    @Autowired
    private IErpTransportTypesService erpTransportTypesService;
    //国家地区
    @Autowired
    private IErpCountriesService erpCountriesService;
    //港口
    @Autowired
    private IErpCityportsService erpCityportsService;
    //包装
    @Autowired
    private IErpPackagesTypesService erpPackagesTypesService;
    //币制
    @Autowired
    private IErpCurrenciesService erpCurrenciesService;
    //单位
    @Autowired
    private IErpUnitsService erpUnitsService;
    //国内地区
    @Autowired
    private IErpDistrictsService erpDistrictsService;
    //国内口岸
    @Autowired
    private IErpChinaPortsService erpChinaPortsService;

    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private ErpReportElementsMapper erpReportElementsMapper;
    @Autowired
    private ErpHscodesMapper erpHscodesMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private IRateInfoService rateInfoService;
    @Autowired
    private TradeInfoMapper tradeInfoMapper;
    @Autowired
    private TradeGoodsListMapper tradeGoodsListMapper;
    @Autowired
    private GroupingInfoMapper groupingInfoMapper;
    @Autowired
    private GroupingGoodsListMapper groupingGoodsListMapper;
    @Autowired
    private PackingInfoMapper packingInfoMapper;
    @Autowired
    private PackingContainerListMapper packingContainerListMapper;
    @Autowired
    private PackingGoodsListMapper packingGoodsListMapper;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private DecContainerMapper decContainerMapper;
    @Autowired
    private ErpHscodeDataMapper erpHscodeDataMapper;
    @Resource
    private JeecgRedisClient jeecgRedisClient;
    @Autowired
    private IErpHscodesService erpHscodesService;
    @Autowired
    private IAiConfigService aiConfigService;
    @Autowired
    private IAiService aiService;
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;
    //逻辑校验接口登录名
    private final String account = "jgtd";
    //逻辑校验接口登录密码
    private final String passWord = "jgtd123456";
    private static Map<String, String> fieldsMap = new HashMap<>();
    static {
        fieldsMap.put("seqNo", "SEQ_NO");
        fieldsMap.put("clearanceNo", "CLEARANCE_NO");
        fieldsMap.put("decStatus", "DEC_STATUS");
        fieldsMap.put("billCode", "BILL_CODE");
        fieldsMap.put("recordNumber", "RECORD_NUMBER");
        fieldsMap.put("optUnitName", "OPT_UNIT_NAME");
        fieldsMap.put("ieFlag", "IE_FLAG");
        fieldsMap.put("tradeTypeName", "TRADE_TYPE_CODE");
        fieldsMap.put("appDate", "APP_DATE");
        fieldsMap.put("applyNumber", "APPLY_NUMBER");
        fieldsMap.put("audited", "AUDITED");
        fieldsMap.put("clearanceTypeName", "CLEARANCE_TYPE");
        fieldsMap.put("shipTypeName", "SHIP_TYPE_CODE");
        fieldsMap.put("arrivalAreaName", "ARRIVAL_AREA");
        fieldsMap.put("createPerson", "CREATE_PERSON");
        fieldsMap.put("outPortName", "OUT_PORT_CODE");
        fieldsMap.put("contract", "CONTRACT");
        fieldsMap.put("customsCode", "CUSTOMS_CODE");
        fieldsMap.put("etpsInnerInvtNo", "ETPS_INNER_INVT_NO");
        fieldsMap.put("licenceNumber", "LICENCE_NUMBER");
        fieldsMap.put("inspMonitorCond", "INSP_MONITOR_COND");
        fieldsMap.put("inspMonitorCond", "INSP_MONITOR_COND");
        fieldsMap.put("billTypeName", "BILL_TYPE");
        fieldsMap.put("synchronism", "SYNCHRONISM");
        fieldsMap.put("createDate", "CREATE_DATE");
        fieldsMap.put("elecDelNo", "ELEC_DEL_NO");
        fieldsMap.put("inputErName", "INPUT_ER_NAME");
        fieldsMap.put("declarantNo", "DECLARANT_NO");
        fieldsMap.put("applyCreateName", "APPLY_CREATE_NAME");
        fieldsMap.put("deliverUnitName", "DELIVER_UNIT_NAME");
        fieldsMap.put("accountNo", "ACCOUNT_NO");
        fieldsMap.put("hstypeSign", "HSTYPE_SIGN");
        fieldsMap.put("decPushStatus", "DEC_PUSH_STATUS");
        fieldsMap.put("updateBy", "UPDATE_BY");
        fieldsMap.put("synchronismDate", "SYNCHRONISM_DATE");
        fieldsMap.put("repairFlag", "REPAIR_FLAG");
        fieldsMap.put("hasCd", "HAS_CD");
        fieldsMap.put("hasYd", "HAS_YD");
        fieldsMap.put("tradeTypeCode_dictText", "TRADE_TYPE_CODE");
        fieldsMap.put("packs", "PACKS");
        fieldsMap.put("grossWeight", "GROSS_WEIGHT");
        fieldsMap.put("total", "TOTAL");
        fieldsMap.put("shipName", "SHIP_NAME");
        fieldsMap.put("createTime", "CREATE_TIME");
        fieldsMap.put("releaseDate", "RELEASE_DATE");
        fieldsMap.put("finalDate", "FINAL_DATE");
        fieldsMap.put("declarePlace_dictText", "DECLARE_PLACE");
        fieldsMap.put("entyPortCode_dictText", "ENTY_PORT_CODE");
        fieldsMap.put("tradeCountry_dictText", "TRADE_COUNTRY");
        fieldsMap.put("arrivalArea_dictText", "ARRIVAL_AREA");
        fieldsMap.put("dclTrnRelFlag", "DCL_TRN_REL_FLAG");
        fieldsMap.put("orderProtocolNo", "ORDER_PROTOCOL_NO");
        fieldsMap.put("initialReviewStatus", "INITIAL_REVIEW_STATUS");
        fieldsMap.put("firstTrialDate", "FIRST_TRIAL_DATE");
        fieldsMap.put("reviewDate", "REVIEW_DATE");
        fieldsMap.put("outPortCode_dictText", "OUT_PORT_CODE");
    }

    /**
     * 分页列表查询
     *
     * @param decHead
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DecHead decHead,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        if (!"0".equals(TenantContext.getTenant())) {
            decHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
        }
        QueryWrapper<DecHead> queryWrapper = QueryGenerator.initQueryWrapper(decHead, req.getParameterMap());
        Page<DecHead> page = new Page<>(pageNo, pageSize);
        //处理报关单号
        if(isNotBlank(decHead.getClearanceNos())){
            //只保留decHead.getClearanceNos()的数字内容
            decHead.setClearanceNos(decHead.getClearanceNos().replaceAll("[^0-9]", ""));
            //继续将decHead.getClearanceNos()转为list<String>,每18个字符分割
            List<String> clearanceNosList = Arrays.asList(decHead.getClearanceNos().split("(?<=\\G.{18})"));
            if(clearanceNosList.size()>1){
                queryWrapper.in("CLEARANCE_NO",clearanceNosList);
            }else{
                queryWrapper.like("CLEARANCE_NO",decHead.getClearanceNos());
            }
        }
        if(StringUtils.isNotBlank(decHead.getTwoStep())){
            queryWrapper.apply(" SUBSTR(DEC_HEAD.DEC_TYPE,3,1) >0 ");//两步申报
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUsername()) && isNotBlank(loginUser.getRealname()) && loginUser.getUsername().startsWith("bgh_user_")) {
            queryWrapper.eq("DECLARE_UNIT_NAME", loginUser.getRealname().replace("_AUTO", ""));
        }


        //20241213追加
        queryWrapper.between(isNotBlank(decHead.getStartAppDate()),
                "APP_DATE",decHead.getStartAppDate(),decHead.getLastAppDate());//申报日期
        queryWrapper.between(isNotBlank(decHead.getStartCreateTime()),
                "CREATE_TIME",decHead.getStartCreateTime(),decHead.getLastCreateTime());//创建日期
        queryWrapper.between(isNotBlank(decHead.getStartUpdateTime()),
                "UPDATE_DATE",decHead.getStartUpdateTime(),decHead.getLastUpdateTime());//最近操作日期
        if("0".equals(decHead.getPushStatusFlag())){
            queryWrapper.isNull("PUSH_STATUS").or().eq("PUSH_STATUS","").or()
                    .eq("PUSH_STATUS","0");
        }
        if("1".equals(decHead.getPushStatusFlag())){
            queryWrapper.eq("PUSH_STATUS","1");
        }
        //表体商品名称
        queryWrapper.apply(isNotBlank(decHead.getHsname()),"DEC_LIST.HSNAME LIKE '%"
                +(isNotBlank(decHead.getHsname())?decHead.getHsname().replaceAll("\\*",""):"")+"%'");
        queryWrapper.groupBy("DEC_HEAD.ID");

        // zhengliansong@2024/12/18 16:27 追加/变更/完善：表头排序！
        if (isNotBlank(decHead.getFields()) && isNotBlank(decHead.getSortType())) {
            if (isNotBlank(fieldsMap.get(decHead.getFields()))) {
                if ("asc".equalsIgnoreCase(decHead.getSortType())) {
                    queryWrapper.orderByAsc(fieldsMap.get(decHead.getFields()));
                } else {
                    queryWrapper.orderByDesc( fieldsMap.get(decHead.getFields()));
                }
            }
        } else {
            queryWrapper.orderByDesc("DEC_HEAD.create_time");
        }


//        IPage<DecHead> pageList = decHeadService.page(page, queryWrapper);

        IPage<DecHead> pageList = decHeadMapper.queryDecHeadPageList(page, queryWrapper);
        //查询该页的所有表体，所有箱子。列表用到
        if(pageList.getRecords().size()>0){
            List<String> decHeadIds= pageList.getRecords().stream().map(DecHead::getId)
                    .collect(Collectors.toList());
            //全部的客户端编号
            List<String> customsCodeList = pageList.getRecords().stream().map(DecHead::getCustomsCode)
                    .collect(Collectors.toList());
            //全部的统一编号
            List<String> seqNoList = pageList.getRecords().stream().map(DecHead::getSeqNo)
                    .collect(Collectors.toList());

            List<DecList> decListList=decListMapper.selectList(new LambdaQueryWrapper<DecList>()
            .in(DecList::getDecId,decHeadIds));
            List<DecContainer> decContainerList=decContainerMapper.selectList(new LambdaQueryWrapper<DecContainer>()
            .in(DecContainer::getDecId,decHeadIds));
            List<EdiStatusHistory> ediStatusHistoryList=ediStatusHistoryService.list(new LambdaQueryWrapper<EdiStatusHistory>()
                    .in(EdiStatusHistory::getBusinessId,customsCodeList).or().in(EdiStatusHistory::getSeqNo,seqNoList));

            for(DecHead decHead1:pageList.getRecords()){
                if(!decListList.isEmpty()){
                    //赋值表体相关信息
                    //该报关单的所有表体
                    List<DecList> decListList1=decListList.stream().filter(i->i.getDecId().equals(decHead1.getId()))
                            .collect(Collectors.toList());
                    if(!decListList1.isEmpty()){
                        //商品首项名称
                        decHead1.setFirstGoodsName(decListList1.get(0).getHsname());
                        //商品全部名称汇总
                        List<String> hsnameList=decListList1.stream().map(DecList::getHsname)
                                .collect(Collectors.toList());
                        decHead1.setAllGoodsName(String.join(",",hsnameList));
                        //商品项数
                        decHead1.setGoodsItemCount(decListList1.size());
                    }
                    //该报关单的所有箱子
                    List<DecContainer> decContainerList1=decContainerList.stream().filter(i->i.getDecId().equals(decHead1.getId()))
                            .collect(Collectors.toList());
                    if(!decContainerList1.isEmpty()){
                        //集装箱数
                        decHead1.setContainerIdCount(decContainerList1.size());
                        //集装箱号汇总
                        List<String> containerIdList=decContainerList1.stream().map(DecContainer::getContainerId)
                                .collect(Collectors.toList());
                        decHead1.setAllContainerId(String.join(",",containerIdList));
                    }


                }
                //赋值最新的edi信息
                if(!ediStatusHistoryList.isEmpty()&&
                        (isNotBlank(decHead1.getCustomsCode())||isNotBlank(decHead1.getSeqNo()))){
                    List<EdiStatusHistory> ediStatusHistoryList1=ediStatusHistoryList.stream().filter(
                            i->isNotBlank(i.getBusinessId())&&i.getBusinessId().equals(decHead1.getCustomsCode())||
                                    isNotBlank(i.getSeqNo())&&i.getSeqNo().equals(decHead1.getSeqNo())
                    ).sorted(Comparator.comparing(EdiStatusHistory::getReceiverTime).reversed())
                            .collect(Collectors.toList());
                    if(!ediStatusHistoryList1.isEmpty()){
                        decHead1.setEdiInfo(ediStatusHistoryList1.get(0).getNote());
                    }
                }
            }
        }
        return Result.OK(pageList);
    }

    @RequestMapping(value = "/saveDecHead", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<?> saveDecHead(@RequestBody DecHead decHead) {
        String decHeadId = decHeadService.saveDecHead(decHead);
        if(decHeadId.contains("无法保存")){
            return Result.error(decHeadId);
        }else {
            HashMap data = new HashMap();
            data.put("id",decHeadId);
            data.put("seqNo",decHead.getSeqNo());

            return Result.OK("保存成功！",data);
        }
    }

    @GetMapping(value = "/getDecHeadById")
    public Result<?> getDecHeadById(@RequestParam(name="decHeadId",required=true) String decHeadId) {
        DecHead decHead = decHeadService.getDecHeadById(decHeadId);
        if (decHead == null){
            return Result.error("未找到对应数据");
        }
        return Result.OK(decHead);
    }

    @RequestMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        decHeadService.deleteDecHead(id);
        return Result.OK("删除成功！");
    }
    @RequestMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        List<String> idList=Arrays.asList(ids.split(","));
        for(String id:idList){
            decHeadService.deleteDecHead(id);
        }
        return Result.OK("删除成功！");
    }
    @PutMapping(value = "/updatePushStatusById")
    public Result<?> updatePushStatusById(@RequestBody JSONObject jsonObject){
        String pushStatus = jsonObject.getObject("pushStatus",String.class);
        String id = jsonObject.getObject("id",String.class);
        DecHead decHead = decHeadService.getById(id);
        if (decHead == null){
            return Result.error("报关单信息为空,请刷新！");
        }
        if (!StringUtils.isEmpty(decHead.getDecStatus()) && !"1".equals(decHead.getDecStatus())) {
            return Result.error("已申报的报关单不允许重复发送报文");
        }
        boolean status = decHeadService.updatePushStatusById(pushStatus,id);
        if(!status){
            return Result.error("操作失败！");
        }
//        sendMessageByPush(decHead.getBillCode(),decHead.getIeFlag());
        return Result.OK("操作成功！");
    }

    @GetMapping(value = "/listCiq")
    public Result<?> listCiq(String codeTs) {
        List<ErpCiq> erpCiqs = erpCiqService.list(new QueryWrapper<ErpCiq>().lambda().like(ErpCiq::getCodeTs,codeTs));
        return Result.OK(erpCiqs);
    }

    @RequestMapping(value = "/exportDecPDF")
    public void queryPrintPdfList(@RequestParam Map<String,Object> inMap, HttpServletResponse response){
        String id = String.valueOf(inMap.get("id"));
        DecHead decHead = decHeadService.queryPrintPdfList(id);
        String filePath = new StringBuilder(uploadpath).append(File.separator).append("printTemporary").append(File.separator)
                .append(decHead.getId()).toString();
        new PrintUtil().listPrintPdfByDec(decHead,decHead.getId().toString(),filePath,response);


    }

    @GetMapping(value = "/listEdi")
    public Result<?> listEdi(String businessId,String seqNo,
                             @RequestParam(name = "pageNo",defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize) {
        if(isBlank(businessId)&&isBlank(seqNo)){
            return Result.error("申报单编号和关联业务单号都为空，无法查询回执信息");
        }
        Page<EdiStatusHistory> page = new Page<EdiStatusHistory>(pageNo, pageSize);
        QueryWrapper queryWrapper = new QueryWrapper<EdiStatusHistory>()
                .eq(StringUtils.isNotEmpty(businessId),"BUSINESS_ID",businessId)
                .or().eq(StringUtils.isNotEmpty(businessId),"RELATED_ID",businessId)
                .or().eq(StringUtils.isNotEmpty(seqNo),"SEQ_NO",seqNo).orderByDesc("receiver_time");
        IPage<EdiStatusHistory> pageList = ediStatusHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 新增商品时发送实时消息
     *
     * @param billCode
     * @return void
     */
    private void sendMessageByPush(String billCode,String ieFlag) {
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        sysAnnouncement.setTitile("报关单推送通知");
        //消息类型为业务消息
        sysAnnouncement.setMsgCategory("3");
        sysAnnouncement.setMsgType(MSG_TYPE_TENANT);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        sysAnnouncement.setMsgContent("报关单推送！提运单号：" + billCode+ (isNotEmpty(sysUser) ? ("，操作人：" + sysUser.getRealname()) : ""));
        sysAnnouncement.setBusType("DecHead");
        sysAnnouncement.setOpenType("component");
        if ("E".equals(ieFlag)){
            sysAnnouncement.setOpenPage("/Business/customs-declaration/DeclarationList");
        }else {
            sysAnnouncement.setOpenPage("/Business/customs-declaration/DeclarationListForI");
        }

        sysAnnouncement.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
        sysAnnouncement.setSender(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
        BaseMap baseMap = new BaseMap();
        baseMap.put("message", JSON.toJSONString(sysAnnouncement));
        jeecgRedisClient.sendMessage(REDIS_BUSINESS_HANDLER, baseMap);
    }

    /**
     * 报关单初复审
     *
     * @param ids
     * @param initialReviewStatus
     * @param opinion
     * @return
     */
    @AutoLog(value = "报关单初复审")
    @ApiOperation(value = "报关单初复审", notes = "报关单初复审")
    @PostMapping(value = "/handleInitialReview")
    public Result<?> handleInitialReview(@RequestParam("ids") String ids,
                                         @RequestParam("initialReviewStatus") String initialReviewStatus,
                                         @RequestParam("opinion") String opinion) {
        if (!"1".equals(initialReviewStatus) && !"1-1".equals(initialReviewStatus)
                && !"2".equals(initialReviewStatus) && !"2-1".equals(initialReviewStatus)) {
            return Result.error("未知的审核类型！");
        }
        return decHeadService.handleInitialReview(ids, initialReviewStatus, opinion);
    }

    /**
     * 报关单推送到市场采购交易平台
     * @apiNote
     * <pre>
     *   报关单推送到市场采购交易平台
     * </pre>
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     *
     * <AUTHOR>  2024/1/19 10:58
     * @version 1.0
     */
    @AutoLog(value = "报关单推送到市场采购交易平台")
    @ApiOperation(value = "报关单推送到市场采购交易平台", notes = "报关单推送到市场采购交易平台")
    @PostMapping(value = "/pushMarketProcurementById")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> pushMarketProcurementById(@RequestParam("id") String id) throws JsonProcessingException {
        DecHead decHead=decHeadService.getDecHeadById(id);
        //获取报关单的报关行代理商，商铺，采购商信息（目前报关行和代理商为同一个对应 委托方数据）
        DecShopsPurchaserRel decShopsPurchaserRel=decShopsPurchaserRelMapper.selectOne(
                new LambdaQueryWrapper<DecShopsPurchaserRel>()
                .eq(DecShopsPurchaserRel::getDecId,decHead.getId())
        );
        if(isNotEmpty(decShopsPurchaserRel)){
            //报关行信息：当前登陆的企业
            EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
            //委托方信息根据社会信用代码查询 报关单的境内收发货人
            Commissioner commissioner=commissionerMapper.selectOne(new LambdaQueryWrapper<Commissioner>()
            .eq(Commissioner::getUnifiedSocialCreditCode,isNotEmpty(decHead.getOptUnitSocialCode())?
                    decHead.getOptUnitSocialCode():"0"));
            //商铺信息
            ShopsInfo shopsInfo=shopsInfoMapper.selectById(decShopsPurchaserRel.getShopsId());
            //采购商信息
            PurchaserInfo purchaserInfo=purchaserInfoMapper.selectById(decShopsPurchaserRel.getPurchaserId());
            if(isNotEmpty(commissioner)&&isNotEmpty(shopsInfo)&&isNotEmpty(purchaserInfo)){
                /**
                 * 需要到的全部字典参数
                 */
                List<ErpCustomsPorts> erpCustomsPortsList=erpCustomsPortsService.list();
                List<ErpTransportTypes> erpTransportTypesList=erpTransportTypesService.list();
                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                List<ErpPackagesTypes> erpPackagesTypesList=erpPackagesTypesService.list();
                List<ErpCurrencies> erpCurrenciesList=erpCurrenciesService.list();
                List<ErpUnits> erpUnitsList=erpUnitsService.list();
                List<ErpDistricts> erpDistrictsList=erpDistrictsService.list();
                List<DictModelVO> tradingTypeList = decListMapper.getDictItemByCode("trading_type");
                List<DictModelVO> bgdlxList = decListMapper.getDictItemByCode("BGDLX");
                List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");
                List<DictModelVO> jzxgg = decListMapper.getDictItemByCode("JZXGG");
                List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
                List<ErpChinaPorts> erpChinaPortsList=erpChinaPortsService.list();
                List<DictModelVO> yfzl = decListMapper.getDictItemByCode("freight_amount_type");
                List<DictModelVO> bfzl = decListMapper.getDictItemByCode("premium_amount_type");
                List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
                //组装数据
                MarketProcurementTransferDTO marketProcurementTransferDTO =
                        CustomsDeclarationDataProcessingUtil.operationAssembly(enterpriseInfo,decHead,commissioner,shopsInfo,purchaserInfo,
                                erpCustomsPortsList,erpTransportTypesList,erpCountriesList,erpCityportsList,erpPackagesTypesList,
                                erpCurrenciesList,erpUnitsList,tradingTypeList,bgdlxList,zjmsfsList,erpDistrictsList,jzxgg,zmxz,
                                erpChinaPortsList,yfzl,bfzl,jgfs);
                String bodyString = JSONObject.toJSONString(marketProcurementTransferDTO,SerializerFeature.WriteMapNullValue);
                ObjectMapper objectMapper=new ObjectMapper();
                //手动拼接+转义
                String bodyStringRequest = "{\"fly_CgptHelper\":"+objectMapper.writeValueAsString(bodyString)+"}";
                String response = HttpRequest.post("https://etg.feiliankeji.cn/ApiInterface/AddDocument")
                        .header("Content-Type","application/json")
                        .body(bodyStringRequest)//表单内容
                        .timeout(30000)//超时，毫秒
                        .execute().body();
                log.info("请求信息：：：");
                log.info(bodyStringRequest);
                log.info("返回信息：：：");
                log.info(response);
                // 使用json库将String转换为JSONObject
                JSONObject jsonObject = JSONObject.parseObject(response);
                if("200".equals(jsonObject.get("code").toString())){
                    //推送成功更新报关单的推送状态
                    decHeadService.update(new LambdaUpdateWrapper<DecHead>()
                    .set(DecHead::getPushMarketProcurementStatus,"1" )
                    .eq(DecHead::getId,decHead.getId()));
                    //将返回的信息保存交易单，组货，装箱
                    try{
                        processingData(response,decHead,commissioner,shopsInfo,purchaserInfo);
                    }catch (Exception e){
                        //回滚事务
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        e.printStackTrace();
                        log.error(e.getMessage(), e);
                        return Result.error("推送成功。但保存返回信息出错，详情联系管理员");
                    }
                    return Result.ok("推送成功！请到交易平台查看");
                }else {
                    return Result.error("推送失败。"+jsonObject.get("info"));
                }

            }else {
                return Result.error("未获取到报关单关联的委托方，商铺，采购商信息，无法推送");
            }

        }

        return Result.error("未获取到报关单关联的委托方，商铺，采购商信息，无法推送");
    }

    private void processingData (String response,DecHead decHead,Commissioner commissioner,
                                 ShopsInfo shopsInfo,PurchaserInfo purchaserInfo) {
        String str1 = response.replaceAll("\\\\","");
        String str2 = str1.replaceAll("\"\\{","{");
        String str3 = str2.replaceAll("}\"","}");
        JSONObject jsonObject = JSONObject.parseObject(str3);
        JSONObject info = jsonObject.getJSONObject("info");
        TradeInfo transactionOrder_Res = info.getObject("transactionOrder_Res",TradeInfo.class);
        JSONObject transactionOrder = info.getJSONObject("transactionOrder");
        TradeInfo trade = transactionOrder.getObject("trade",TradeInfo.class);
        //★★★★★★交易单表头
        TradeInfo tradeInfo=new TradeInfo();
        CopyOptions copyOption = CopyOptions.create(null, true);
        BeanUtil.copyProperties(transactionOrder_Res,tradeInfo,copyOption);
        BeanUtil.copyProperties(trade,tradeInfo,copyOption);
        tradeInfo.setDecId(decHead.getId());
        tradeInfo.setBlNo(decHead.getBillCode());
        tradeInfo.setAgentId(commissioner.getId());
        tradeInfo.setAgentName(commissioner.getCommissionerFullName());
        tradeInfo.setShopId(shopsInfo.getId());
        tradeInfo.setShopName(shopsInfo.getShopName());
        //添加交易订单表头
        tradeInfoMapper.insert(tradeInfo);
        //★★★★★★交易单表体
        List<TradeGoodsList> tradeGoodsList=transactionOrder.getJSONArray("tradeGoodsList").toJavaList(TradeGoodsList.class);
        for(TradeGoodsList tradeGoodsList1:tradeGoodsList){
            tradeGoodsList1.setTradeInfoId(tradeInfo.getId());
            //添加交易订单表体
            tradeGoodsListMapper.insert(tradeGoodsList1);
        }
        //★★★★★★组货表头
        GroupingInfo groupingInfo=new GroupingInfo();
        GroupingInfo groupCargo_Res = info.getObject("groupCargo_Res",GroupingInfo.class);
        BeanUtil.copyProperties(groupCargo_Res,groupingInfo,copyOption);
        JSONObject groupCargo = info.getJSONObject("groupCargo");
        GroupingInfo grouping = groupCargo.getObject("grouping",GroupingInfo.class);
        BeanUtil.copyProperties(grouping,groupingInfo,copyOption);
        groupingInfo.setDecId(decHead.getId());
        groupingInfo.setTradeInfoId(tradeInfo.getId());
        groupingInfo.setForwarderId(commissioner.getId());
        groupingInfo.setForwarderName(commissioner.getCommissionerFullName());
        groupingInfo.setBrokerId(commissioner.getId());
        groupingInfo.setBrokerName(commissioner.getCommissionerFullName());
        groupingInfo.setPurchaserInfoId(purchaserInfo.getId());
        groupingInfo.setPurchaserNumber(purchaserInfo.getIdNumber());
        //添加组货表头
        groupingInfoMapper.insert(groupingInfo);

        //★★★★★★组货表体
        List<GroupingGoodsList> groupingGoodsList=groupCargo.getJSONArray("groupingGoodsList")
                .toJavaList(GroupingGoodsList.class);
        for(GroupingGoodsList groupingGoodsList1:groupingGoodsList){
            groupingGoodsList1.setGroupingInfoId(groupingInfo.getId());
            //添加组货表体
            groupingGoodsListMapper.insert(groupingGoodsList1);
        }
        //★★★★★★装箱表头
        PackingInfo packingInfo=new PackingInfo();
        PackingInfo packingHead_ResModel=info.getObject("packingHead_ResModel",PackingInfo.class);
        BeanUtil.copyProperties(packingHead_ResModel,packingInfo,copyOption);
        PackingInfo packingHead=info.getObject("packingHead",PackingInfo.class);
        BeanUtil.copyProperties(packingHead,packingInfo,copyOption);
        packingInfo.setDecId(decHead.getId());
        packingInfo.setTradeInfoId(tradeInfo.getId());

        //添加装箱表头
        packingInfoMapper.insert(packingInfo);
        //★★★★★★装箱 集装箱 一级子表
        PackingContainerList packingContainerList=new PackingContainerList();
        PackingContainerList packingBody_ResModel=info.getObject("packingBody_ResModel",PackingContainerList.class);
        BeanUtil.copyProperties(packingBody_ResModel,packingContainerList,copyOption);
        PackingContainerList packingContainer=info.getJSONObject("packingBody").getJSONObject("data")
                .getObject("packingContainer",PackingContainerList.class);
        BeanUtil.copyProperties(packingContainer,packingContainerList,copyOption);
        packingContainerList.setPackingInfoId(packingInfo.getId());
        //添加集装箱 一级表体
        packingContainerListMapper.insert(packingContainerList);
        //★★★★★★装箱 商品 二级子表
        List<PackingGoodsList> packingGoodsListList=info.getJSONObject("packingBody").getJSONObject("data")
                .getJSONArray("packingGoodsList").toJavaList(PackingGoodsList.class);
        for(PackingGoodsList packingGoodsList:packingGoodsListList){
            packingGoodsList.setPackingContainerId(packingContainerList.getId());
            packingGoodsList.setPackingInfoId(packingInfo.getId());
            //添加装箱表体数据
            packingGoodsListMapper.insert(packingGoodsList);
        }
    }



    /**
     * 根据申报税号获取申报规范
     * @apiNote
     * <pre>
     *   根据申报税号获取申报规范
     * </pre>
     *
     * @param hscode
     * @return org.jeecg.common.api.vo.Result<?>
     *
     * <AUTHOR>  2024/1/29 15:05
     * @version 1.0
     */
    @AutoLog(value = "根据申报税号获取申报规范")
    @ApiOperation(value = "根据申报税号获取申报规范", notes = "根据申报税号获取申报规范")
    @GetMapping(value = "/listSpecByHscodes")
    public Result<?> listSpecByHscodes(@RequestParam("hscode")String hscode){
//        List<ErpReportElements> erpReportElementsList = erpReportElementsMapper.selectList(
//                new QueryWrapper<ErpReportElements>().lambda()
//                .in(ErpReportElements::getHscode, Arrays.asList(hscode.split(",")))
//                        .orderByDesc(ErpReportElements::getHscode)
//        .orderByAsc(ErpReportElements::getNo));
        List<ErpReportElements> erpReportElementsList = new ArrayList<>();
        //20250218修改 直接取税则表的申报要素字段
        List<ErpHscodeData> erpHscodeData = erpHscodeDataMapper.selectList(new LambdaQueryWrapper<ErpHscodeData>()
                .in(ErpHscodeData::getHscode, Arrays.asList(hscode.split(",")))
                .eq(ErpHscodeData::getIsEnable,1));
        if(erpHscodeData.isEmpty()){
            return Result.error("未获取到该税号信息");
        }else {
            ErpHscodeData erpHscodeData1=  erpHscodeData.get(0);
            String sbys = erpHscodeData1.getSbys();
            //示例：0:品牌类型;1:出口享惠情况;2:材质（瓦楞纸或纸板制）;3:GTIN;4:CAS;5:其他;
            //手动组装
            if(isNotBlank(sbys)){
                String[] arr1 = sbys.split(";");
                for(String s:arr1){
                    String[] arr2 = s.split(":");
                    ErpReportElements erpReportElements = new ErpReportElements();
                    erpReportElements.setElement(arr2[1]);
                    erpReportElements.setNo(Integer.valueOf(arr2[0]));
                    erpReportElements.setHscode(Arrays.asList(hscode.split(",")).get(0));
                    erpReportElementsList.add(erpReportElements);
                }
            }
            return Result.OK(erpReportElementsList);
        }
    }
    @AutoLog(value = "根据租户id获取全部的申报单表体信息")
    @ApiOperation(value = "根据租户id获取全部的申报单表体信息", notes = "根据租户id获取全部的申报单表体信息")
    @GetMapping(value = "/listDecListByTenant")
    public Result<?> listDecListByTenant(@RequestParam(name = "createPerson")String createPerson,
                                          @RequestParam(name = "hscode")String hscode,
                                         @RequestParam(name = "hsname")String hsname,
                                         @RequestParam(name = "declareUnitSocialCode")String declareUnitSocialCode,
                                         @RequestParam(name = "IE_FLAG")String IE_FLAG,
                                         @RequestParam(name = "HSMODEL")String HSMODEL,
                                         @RequestParam(name = "deliverUnitName")String deliverUnitName,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                         @RequestParam(name = "optUnitSocialCode")String optUnitSocialCode,
                                         @RequestParam(name = "optUnitId")String optUnitId,
                                         @RequestParam(name = "optUnitName")String optUnitName){

        Page<DecList> page = new Page<DecList>(pageNo, pageSize);
        IPage<DecList> pageList = decListMapper.queryPageList(page, createPerson,TenantContext.getTenant(),
                hscode,hsname,declareUnitSocialCode,IE_FLAG,HSMODEL,deliverUnitName,optUnitSocialCode,optUnitId,optUnitName);
        return Result.OK(pageList);
    }
    @AutoLog(value = "检查选中的商品申报规范与税则库是否一致")
    @ApiOperation(value = "检查选中的商品申报规范与税则库是否一致", notes = "检查选中的商品申报规范与税则库是否一致")
    @PostMapping(value = "/checkDeclarationStandards")
    public Result<?> checkDeclarationStandards(@RequestBody JSONObject jsonObject){
        String hscode=jsonObject.getString("hscode");
        String historyDeclarationSpecification=jsonObject.getString("historyDeclarationSpecification");
        //historyDeclarationSpecification示例
        //0:(品牌类型)|1:(出口享惠情况)|2:(功能)|3:(检测对象)|4:(品牌[中文及外文名称])|5:(型号)|6:(GTIN)|7:(CAS)|
        // 8:(其他[非必报要素，请根据实际情况填报])
        if(isNotEmpty(historyDeclarationSpecification)&&historyDeclarationSpecification.contains("|")){
            List<String> hsmodelList=Arrays.asList(historyDeclarationSpecification.split("\\|"));
            //根据申报税号获取申报规范
            List<ErpReportElements> erpReportElementsList = erpReportElementsMapper.selectList(
                    new QueryWrapper<ErpReportElements>().lambda()
                            .in(ErpReportElements::getHscode, Arrays.asList(hscode.split(",")))
                            .orderByDesc(ErpReportElements::getHscode)
                            .orderByAsc(ErpReportElements::getNo));
            if(hsmodelList.size()==erpReportElementsList.size()){
                //项数相同继续验证每项内容
                for(String hsmodelC:hsmodelList){
                    String[] hsmodelCs = hsmodelC.split(":");
                    List<ErpReportElements> erpReportElementsListByNo=erpReportElementsList.stream().filter(
                            i->i.getNo().toString().equals(hsmodelCs[0])
                    ).collect(Collectors.toList());
                    if(!erpReportElementsListByNo.get(0).getElement().equals(hsmodelCs[1])){
                        //不通过成交单位，法一单位，法二单位直接用税则库的
                        ErpHscodes erpHscodes=erpHscodesMapper.selectOne(new LambdaQueryWrapper<ErpHscodes>()
                                .eq(ErpHscodes::getHscode,hscode));
                        return Result.error("税号:"+hscode+"的申报规范第"+(Integer.valueOf(hsmodelCs[0])+1)+"项，已由【"+hsmodelCs[1]+
                                "】变更为【"+erpReportElementsListByNo.get(0).getElement()+
                                "】,请重新录入申报要素",erpHscodes);
                    }
                }
                return Result.ok("验证通过");
            }else {
                //不通过成交单位，法一单位，法二单位直接用税则库的
                ErpHscodes erpHscodes=erpHscodesMapper.selectOne(new LambdaQueryWrapper<ErpHscodes>()
                .eq(ErpHscodes::getHscode,hscode));
                if(null==erpHscodes){
                    return Result.error("选择的商品税号在税则中不存在，请重新选择");
                }
                return Result.error("税号:"+hscode+"的申报规范已经由"+hsmodelList.size()+"项变更为"+erpReportElementsList.size()+"项，" +
                        "请重新录入申报要素",erpHscodes);
            }
        }
        return Result.error("不符合规范，请手动录入申报要素");
    }


    @AutoLog(value = "出口报关统计")
    @ApiOperation(value = "出口报关统计", notes = "出口报关统计")
    @GetMapping(value = "/exportDecStatisticsList")
    public Result<List<DecStatisticsVO>> exportDecStatisticsList(@RequestParam(name = "statisticalContent")String statisticalContent,
                                             @RequestParam(name = "statisticalItems")String statisticalItems,
                                             @RequestParam(name = "declarationDateStart")String declarationDateStart,
                                             @RequestParam(name = "declarationDateEnd")String declarationDateEnd){
        if(isBlank(statisticalContent)||isBlank(statisticalItems)
                ||isBlank(declarationDateStart)||isBlank(declarationDateEnd)){
            return Result.error("查询条件不完整，请检查后再次查询");
        }else {
            List<DecStatisticsVO> decStatisticsVOList=new ArrayList<>();
            //统计内容===
            switch (statisticalContent){
                //票量
                case "TICKET_QTY":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("TRADE_COUNTRY","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                for(DecStatisticsVO decStatisticsVO:decStatisticsVOList){
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(decStatisticsVO.getStatisticalItemsValue())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            decStatisticsVO.getStatisticalItemsValue());
                                }
                            }
                        break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("DES_PORT","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                for(DecStatisticsVO decStatisticsVO:decStatisticsVOList){
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->
                                            i.getCityportCode().equals(decStatisticsVO.getStatisticalItemsValue()))
                                            .collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            decStatisticsVO.getStatisticalItemsValue());
                                }
                            }
                            break;
                        //货主 出口:境内收发货人
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("OPT_UNIT_NAME","E",
                                            declarationDateStart,declarationDateEnd);
                            break;
                    }
                    break;
                //贸易额
                case "TRADE_VOLUME":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("TRADE_COUNTRY","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                //根据国家分组，可能一个贸易国多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                .like(RateInfo::getRateDate, yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(key)
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            decStatisticsVO.getStatisticalItemsValue());
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                            .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });

                            }
                            break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("DES_PORT","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                //根据港口分组，可能一个港口多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                        .like(RateInfo::getRateDate, yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->
                                            i.getCityportCode().equals(key))
                                            .collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            decStatisticsVO.getStatisticalItemsValue());
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                                    .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                            }
                            break;
                        //货主 出口:境内收发货人
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("OPT_UNIT_NAME","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //根据境内收发货人分组，可能一个境内收发货人多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                        .like(RateInfo::getRateDate,yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(key);
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                                    .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                            }
                            break;
                    }
                    break;
                //箱量
                case "TEU":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("TRADE_COUNTRY","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                //根据贸易国分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    //转换国家代码
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(key)
                                    ).collect(Collectors.toList());
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                            new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);

                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }
                            break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("DES_PORT","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                //根据港口分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    //转换港口代码
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(
                                            i->i.getCityportCode().equals(key)
                                    ).collect(Collectors.toList());
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                            new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);

                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }

                            break;
                        //货主 出口:境内收发货人
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("OPT_UNIT_NAME","E",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //根据货主分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                            new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }
                            break;
                    }
                    break;
            }
            return Result.OK(decStatisticsVOList);
        }

    }

    @AutoLog(value = "进口报关统计")
    @ApiOperation(value = "进口报关统计", notes = "进口报关统计")
    @GetMapping(value = "/importDecStatisticsList")
    public Result<List<DecStatisticsVO>> importDecStatisticsList(@RequestParam(name = "statisticalContent")String statisticalContent,
                                                                 @RequestParam(name = "statisticalItems")String statisticalItems,
                                                                 @RequestParam(name = "declarationDateStart")String declarationDateStart,
                                                                 @RequestParam(name = "declarationDateEnd")String declarationDateEnd){
        if(isBlank(statisticalContent)||isBlank(statisticalItems)
                ||isBlank(declarationDateStart)||isBlank(declarationDateEnd)){
            return Result.error("查询条件不完整，请检查后再次查询");
        }else {
            List<DecStatisticsVO> decStatisticsVOList=new ArrayList<>();
            //统计内容===
            switch (statisticalContent){
                //票量
                case "TICKET_QTY":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("TRADE_COUNTRY","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                for(DecStatisticsVO decStatisticsVO:decStatisticsVOList){
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(decStatisticsVO.getStatisticalItemsValue())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            decStatisticsVO.getStatisticalItemsValue());
                                }
                            }
                            break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("DESP_PORT_CODE","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                for(DecStatisticsVO decStatisticsVO:decStatisticsVOList){
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->
                                            i.getCityportCode().equals(decStatisticsVO.getStatisticalItemsValue()))
                                            .collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            decStatisticsVO.getStatisticalItemsValue());
                                }
                            }
                            break;
                        //货主 出口:境内收发货人 进口
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTicketQty("OPT_UNIT_NAME","I",
                                            declarationDateStart,declarationDateEnd);
                            break;
                    }
                    break;
                //贸易额
                case "TRADE_VOLUME":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("TRADE_COUNTRY","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                //根据国家分组，可能一个贸易国多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                        .like(RateInfo::getRateDate, yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(key)
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            decStatisticsVO.getStatisticalItemsValue());
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                                    .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                            }
                            break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("DESP_PORT_CODE","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                //根据港口分组，可能一个港口多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                        .like(RateInfo::getRateDate, yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->
                                            i.getCityportCode().equals(key))
                                            .collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            decStatisticsVO.getStatisticalItemsValue());
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                                    .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                            }
                            break;
                        //货主 进口:境内收发货人
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTradeVolume("OPT_UNIT_NAME","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //根据境内收发货人分组，可能一个境内收发货人多个币制
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                decStatisticsVOList.clear();
                                //查询昨天的汇率
                                LocalDate yesterday = LocalDate.now().minusDays(1);
                                List<RateInfo> rateInfoList=rateInfoService.list(new LambdaQueryWrapper<RateInfo>()
                                        .like(RateInfo::getRateDate,yesterday));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(key);
                                    //处理币值统计量
                                    BigDecimal statisticalContentValue=new BigDecimal(0);
                                    for(DecStatisticsVO decStatisticsVO1:value){
                                        //人民币直接累加
                                        if("RMB".equals(decStatisticsVO1.getCurrency())){
                                            statisticalContentValue=statisticalContentValue.
                                                    add(decStatisticsVO1.getStatisticalContentValue());
                                        }else {
                                            //转换币制
                                            List<RateInfo> rateInfos=rateInfoList.stream().filter(i->i.getCurrency()
                                                    .equals(decStatisticsVO1.getCurrency())).collect(Collectors.toList());
                                            if(rateInfos.size()>0){
                                                statisticalContentValue=statisticalContentValue.add(
                                                        rateInfos.get(0).getRmb().multiply(
                                                                decStatisticsVO1.getStatisticalContentValue())
                                                );
                                            }
                                        }
                                    }
                                    decStatisticsVO.setStatisticalContentValue(statisticalContentValue.setScale(
                                            2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                            }
                            break;
                    }
                    break;
                //箱量
                case "TEU":
                    //统计项目===
                    switch (statisticalItems){
                        //贸易国
                        case "TRADING_NATIONS":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("TRADE_COUNTRY","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换国家代码
                                List<ErpCountries> erpCountriesList=erpCountriesService.list();
                                //根据贸易国分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    //转换国家代码
                                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(
                                            i->i.getCode().equals(key)
                                    ).collect(Collectors.toList());
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(erpCountries.size()>0?erpCountries.get(0).getName():
                                            key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                           new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);

                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }
                            break;
                        //港口
                        case "PORT":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("DESP_PORT_CODE","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //转换港口代码
                                List<ErpCityports> erpCityportsList=erpCityportsService.list();
                                //根据港口分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    //转换港口代码
                                    List<ErpCityports> erpCityports=erpCityportsList.stream().filter(
                                            i->i.getCityportCode().equals(key)
                                    ).collect(Collectors.toList());
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(erpCityports.size()>0?erpCityports.get(0).getCnname():
                                            key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                            new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);

                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }

                            break;
                        //货主 出口:境内收发货人
                        case "CARGO_OWNER":
                            decStatisticsVOList=
                                    decHeadMapper.exportDecStatisticsListTeu("OPT_UNIT_NAME","I",
                                            declarationDateStart,declarationDateEnd);
                            if(decStatisticsVOList.size()>0){
                                //根据货主分组
                                Map<String,List<DecStatisticsVO>> map =
                                        decStatisticsVOList.stream().collect(
                                                Collectors.groupingBy(DecStatisticsVO::getStatisticalItemsValue));
                                //清空list，再放入返回的数据
                                decStatisticsVOList.clear();
                                //视为两个箱子的规格
                                String[] containerMd={"11","12","13","32"};
                                List<String> containerMdList = new ArrayList<>(Arrays.asList(containerMd));
                                List<DecStatisticsVO> finalDecStatisticsVOList = decStatisticsVOList;
                                map.forEach((key, value) -> {
                                    // 处理key和value
                                    DecStatisticsVO decStatisticsVO=new DecStatisticsVO();
                                    decStatisticsVO.setStatisticalItemsValue(key);
                                    //集装箱类型为 11，12，13，32的视为两个箱子
                                    List<DecStatisticsVO> qtyDouble=value.stream().filter(
                                            i->containerMdList.contains(i.getStatisticalContentValue().toString())
                                    ).collect(Collectors.toList());
                                    decStatisticsVO.setStatisticalContentValue(
                                            new BigDecimal(qtyDouble.size()*2+value.size()-qtyDouble.size()));
                                    finalDecStatisticsVOList.add(decStatisticsVO);
                                });
                                //进行箱量排序
                                decStatisticsVOList=decStatisticsVOList.stream()
                                        .sorted(Comparator.comparing(DecStatisticsVO::getStatisticalContentValue).reversed())
                                        .collect(Collectors.toList());
                            }
                            break;
                    }
                    break;
            }
            return Result.OK(decStatisticsVOList);
        }


    }
    @AutoLog(value = "获取导出报关单的列")
    @ApiOperation(value = "获取导出报关单的列", notes = "获取导出报关单的列")
    @GetMapping(value = "/getExportDecFields")
    public Result<?> getExportDecFields(){
        HashMap<String,HashMap> map=new HashMap<>();
        LinkedHashMap<String,String> mapDecHeadFields=new LinkedHashMap<>();
        LinkedHashMap<String,String> mapDecListFields=new LinkedHashMap<>();
        LinkedHashMap<String,String> mapDecContainerFields=new LinkedHashMap<>();
        Field[] fields;
        fields = ExportDecExcel.class.getDeclaredFields();
        if (isNotEmpty(fields)) {
            for (Field field : fields) {
                if (null != field.getAnnotation(Excel.class)&&null != field.getAnnotation(DecHeadFields.class)) {
                    mapDecHeadFields.put(field.getName(),field.getAnnotation(Excel.class).name());
                }else if (null != field.getAnnotation(Excel.class)&&null != field.getAnnotation(DecContainerFields.class)) {
                    mapDecContainerFields.put(field.getName(),field.getAnnotation(Excel.class).name());
                }else if (null != field.getAnnotation(Excel.class)){
                    mapDecListFields.put(field.getName(),field.getAnnotation(Excel.class).name());
                }
            }
        }
        map.put("decHeadFields",mapDecHeadFields);
        map.put("decListFields",mapDecListFields);
        map.put("decContainerFields",mapDecContainerFields);
        return Result.ok(map);
    }

    /**
     * 导出报关单详情Excel
     *
     * @param request
     * @param
     */
    @RequestMapping(value = "/customExportDec")
    public void customExportDec(DecHead decHead,
                                        HttpServletRequest request, HttpServletResponse response) {
        decHeadService.customExportDec(decHead, request, response);
    }
    /**
     * 下载报关单的excel或pdf
     *
     * @param request
     * @param
     */
    @RequestMapping(value = "/exportDecComplete")
    public void exportDecComplete(String decId,String flag,
                                HttpServletRequest request, HttpServletResponse response) {
        decHeadService.exportDecComplete(decId,flag, request, response);
    }
    @AutoLog(value = "价格检查(申报历史检查)")
    @ApiOperation(value = "价格检查(申报历史检查)", notes = "价格检查(申报历史检查)")
    @PostMapping(value = "/checkDecListForHistory")
    public Result<String> checkDecListForHistory(@RequestBody JSONObject jsonObject){
        //前端传入的报关单表体
        List<DecList> decLists=jsonObject.getJSONArray("decLists").toJavaList(DecList.class);
        //报关单流水
        String decHeadId = jsonObject.getString("decHeadId");
        //去重品名
        List<String> hsnameList=decLists.stream().map(DecList::getHsname)
                .distinct().collect(Collectors.toList());
//        //查询所有品名的历史申报单价最大值和最小值
        List<DecList> decListList=decListMapper.getDecListHsnamePriceByHaname(hsnameList,TenantContext.getTenant());
        if(decListList.size()>0){
            //未通过检测的申报品名
            List<String> failHsname=new ArrayList<>();
        for(String hsname:hsnameList){
            //前端传入的单个品名最大值
            Optional<DecList> decListListByHsnameMax=decLists.stream().filter(i->i.getHsname().equals(hsname))
                    .max(Comparator.comparing(DecList::getPrice));
            //前端传入的单个品名最小值
            Optional<DecList> decListListByHsnameMin=decLists.stream().filter(i->i.getHsname().equals(hsname))
                    .min(Comparator.comparing(DecList::getPrice));
            DecList decListMax = decListListByHsnameMax.get();
            DecList decListMin = decListListByHsnameMin.get();
            List<DecList> decListsByQuery=decListList.stream().filter(i->i.getHsname().equals(hsname))
                    .collect(Collectors.toList());
            if(decListsByQuery.size()>0){
                DecList decListQuery=decListsByQuery.get(0);
            //本次传入的最大值大于历史最大值或最小值小于历史最小值 返回信息提示
            if(decListMax.getPrice().compareTo(decListQuery.getTotal())>0||
            decListMin.getPrice().compareTo(decListQuery.getPrice())<0){
                failHsname.add(hsname);
            }
            }
        }
        if(failHsname.size()==0){
            return Result.ok("价格检查通过");
        }else {
            //报关单价格检查后，有异常的产生一条异常预警记录；
            String msgContent="报关单流水号:"+decHeadId+",以下品名申报历史价格检查未通过，申报单价低于同品名的历史申报单价的最低值或高于历史申报单价的最高值。" +
                    System.lineSeparator()+"【"+
                    String.join(",",failHsname)+"】";
            String titile="报关单申报历史价格检查异常";
//            saveSysAnnouncement(titile,msgContent);
            return Result.error(msgContent);
        }
        }else {
            return Result.ok("价格检查通过");
        }
    }
    @AutoLog(value = "价格检查(商品库检查)")
    @ApiOperation(value = "价格检查(商品库检查)", notes = "价格检查(商品库检查)")
    @PostMapping(value = "/checkDecListForGoods")
    public Result<String> checkDecListForGoods(@RequestBody JSONObject jsonObject){
        //前端传入的报关单表体
        List<DecList> decLists=jsonObject.getJSONArray("decLists").toJavaList(DecList.class);
        //报关单流水
        String decHeadId = jsonObject.getString("decHeadId");
        //获取全部报关单表体的产品id
        List<String> productInfoIdList=decLists.stream().map(DecList::getProductInfoId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if(isNotEmpty(productInfoIdList)){
            //未通过检测的申报品名
            List<String> failItem=new ArrayList<>();
            List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                    .in(ProductInfo::getId, productInfoIdList));
            for(ProductInfo productInfo:productInfoList){
                //产品的价格和波动范围不为空
                if(isNotEmpty(productInfo.getPrice())&&isNotEmpty(productInfo.getPriceFluctuationRatio())){
                    //该产品最高价格
                    BigDecimal maxPrice=productInfo.getPrice().multiply(new BigDecimal(1)
                    .add(productInfo.getPriceFluctuationRatio().divide(new BigDecimal(100))));
                    //该产品最低价格
                    BigDecimal minPrice=productInfo.getPrice().multiply(new BigDecimal(1)
                            .subtract(productInfo.getPriceFluctuationRatio().divide(new BigDecimal(100))));
                    //该产品的所有表体
                    List<DecList> decListList=decLists.stream().filter(i->i.getProductInfoId()
                            .equals(productInfo.getId())).collect(Collectors.toList());
                    for(DecList decList:decListList){
                        //小于范围最低价或高于最高价
                        if(decList.getPrice().compareTo(minPrice)<0||
                                decList.getPrice().compareTo(maxPrice)>0){
                            failItem.add(String.valueOf(decList.getItem()));
                        }

                    }
                }
            }
            if(failItem.size()>0){
                String msgContent="报关单流水号:"+decHeadId+",以下项数的商品库价格检查未通过，申报单价超出对应商品的价格上下波动范围。" +
                        System.lineSeparator()+"【"+
                        String.join(",",failItem)+"】";
                String titile="报关单商品库价格检查异常";
//                saveSysAnnouncement(titile,msgContent);
                return Result.error(msgContent);
            }else {
                return Result.ok("价格检查通过");
            }

        }else {
            return Result.error("未获取到关联的商品信息，请尝试申报历史检查。");
        }
    }
    @AutoLog(value = "净重检查(商品库检查)")
    @ApiOperation(value = "净重检查(商品库检查)", notes = "净重检查(商品库检查)")
    @PostMapping(value = "/checkDecListNetWeightForGoods")
    public Result<String> checkDecListNetWeightForGoods(@RequestBody JSONObject jsonObject){
        //前端传入的报关单表体
        List<DecList> decLists=jsonObject.getJSONArray("decLists").toJavaList(DecList.class);
        //报关单流水
        String decHeadId = jsonObject.getString("decHeadId");
        //获取全部报关单表体的产品id
        List<String> productInfoIdList=decLists.stream().map(DecList::getProductInfoId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if(isNotEmpty(productInfoIdList)){
            //未通过检测的申报品名
            List<String> failItem=new ArrayList<>();
            List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                    .in(ProductInfo::getId, productInfoIdList));
            for(ProductInfo productInfo:productInfoList){
                //产品的净重和波动范围不为空
                if(isNotEmpty(productInfo.getNetWeight())&&isNotEmpty(productInfo.getNetWeightFluctuationRatio())){
                    //该产品最高净重
                    BigDecimal maxNetWeight=productInfo.getNetWeight().multiply(new BigDecimal(1)
                            .add(productInfo.getNetWeightFluctuationRatio().divide(new BigDecimal(100))));
                    //该产品最低净重
                    BigDecimal minNetWeight=productInfo.getNetWeight().multiply(new BigDecimal(1)
                            .subtract(productInfo.getNetWeightFluctuationRatio().divide(new BigDecimal(100))));
                    //该产品的所有表体
                    List<DecList> decListList=decLists.stream().filter(i->i.getProductInfoId()
                            .equals(productInfo.getId())).collect(Collectors.toList());
                    for(DecList decList:decListList){
                        //小于范围最低价或高于最高价
                        if(null!=decList.getNetWeight()&&null!=minNetWeight
                                &&null!=decList.getNetWeight()&&null!=maxNetWeight){
                        if(decList.getNetWeight().compareTo(minNetWeight)<0||
                                decList.getNetWeight().compareTo(maxNetWeight)>0){
                            failItem.add(String.valueOf(decList.getItem()));
                        }
                        }

                    }
                }
            }
            if(failItem.size()>0){
                String msgContent="报关单流水号:"+decHeadId+",以下项数的商品库净重检查未通过，净重超出对应商品的净重上下波动范围。" +
                        System.lineSeparator()+"【"+
                        String.join(",",failItem)+"】";
                String titile="报关单商品库净重检查异常";
//                saveSysAnnouncement(titile,msgContent);
                return Result.error(msgContent);
            }else {
                return Result.ok("净重检查通过");
            }
        }else {
            return Result.error("未获取到关联的商品信息，请尝试申报历史检查。");
        }
    }

    @AutoLog(value = "净重检查(申报历史检查)")
    @ApiOperation(value = "净重检查(申报历史检查)", notes = "净重检查(申报历史检查)")
    @PostMapping(value = "/checkDecListNetWeightForHistory")
    public Result<String> checkDecListNetWeightForHistory(@RequestBody JSONObject jsonObject){
        //前端传入的报关单表体
        List<DecList> decLists=jsonObject.getJSONArray("decLists").toJavaList(DecList.class);
        //报关单流水
        String decHeadId = jsonObject.getString("decHeadId");
        //去重品名
        List<String> hsnameList=decLists.stream().map(DecList::getHsname)
                .distinct().collect(Collectors.toList());
//        //查询所有品名的历史申报净重最大值和最小值
        List<DecList> decListList=decListMapper.getDecListHsnameNetWeightByHaname(hsnameList,TenantContext.getTenant());
        if(decListList.size()>0){
            //未通过检测的申报品名
            List<String> failHsname=new ArrayList<>();
            for(String hsname:hsnameList){
                //前端传入的单个品名最大值
                Optional<DecList> decListListByHsnameMax=decLists.stream().filter(i->i.getHsname().equals(hsname))
                        .max(Comparator.comparing(DecList::getNetWeight));
                //前端传入的单个品名最小值
                Optional<DecList> decListListByHsnameMin=decLists.stream().filter(i->i.getHsname().equals(hsname))
                        .min(Comparator.comparing(DecList::getNetWeight));
                DecList decListMax = decListListByHsnameMax.get();
                DecList decListMin = decListListByHsnameMin.get();
                List<DecList> decListsByQuery=decListList.stream().filter(i->i.getHsname().equals(hsname))
                        .collect(Collectors.toList());
                if(decListsByQuery.size()>0){
                    DecList decListQuery=decListsByQuery.get(0);
                    //本次传入的最大值大于历史最大值或最小值小于历史最小值 返回信息提示
                    if(null!=decListMax.getNetWeight()&&null!= decListMin.getNetWeight()&&
                    null!=decListQuery.getTotal()&&null!=decListQuery.getPrice()){
                    if(decListMax.getNetWeight().compareTo(decListQuery.getTotal())>0||
                            decListMin.getNetWeight().compareTo(decListQuery.getPrice())<0){
                        failHsname.add(hsname);
                    }
                    }
                }
            }
            if(failHsname.size()==0){
                return Result.ok("净重检查通过");
            }else {
                //报关单价格检查后，有异常的产生一条异常预警记录；
                String msgContent="报关单流水号:"+decHeadId+",以下品名净重检查未通过，净重低于同品名的历史净重的最低值或高于历史净重的最高值。" +
                        System.lineSeparator()+"【"+
                        String.join(",",failHsname)+"】";
                String titile="报关单净重检查异常";
//                saveSysAnnouncement(titile,msgContent);
                return Result.error(msgContent);
            }
        }else {
            return Result.ok("净重检查通过");
        }

    }

    @AutoLog(value = "海关知识产权备案检验")
    @ApiOperation(value = "海关知识产权备案检验", notes = "海关知识产权备案检验")
    @PostMapping(value = "/checkDecListForIntellectualProperty")
    public Result<String> checkDecListForIntellectualProperty(@RequestBody JSONObject jsonObject){
        //前端传入的报关单表体
        List<DecList> decLists=jsonObject.getJSONArray("decLists").toJavaList(DecList.class);
        //报关单表头境内发货人
        String optUnitName = jsonObject.getString("optUnitName");
        //筛选出表体的所有品牌
        List<String> brandList=new ArrayList<>();
        for(DecList decList:decLists){
            String[] historyDeclarationSpecificationArr = decList.getHistoryDeclarationSpecification().split("\\|");
            Integer index=IntStream.range(0, historyDeclarationSpecificationArr.length)
                    .filter(i -> historyDeclarationSpecificationArr[i].contains("品牌[中文及外文名称]"))
                    .findFirst()
                    .orElse(-1);
            if(index>=0){
                String[] hsmodelArr = decList.getHsmodel().split("\\|");
                brandList.add(hsmodelArr[index]);
            }
        }
        //品牌去重后循环请求
        //未通过检验的品牌
        List<String> brandNotCheck=new ArrayList<>();
        for(String brand:brandList.stream().distinct().collect(Collectors.toList())){
            Boolean checkResult=sendRequest(optUnitName,brand);
            if(!checkResult){
                brandNotCheck.add(brand);
            }
        }
        if(brandNotCheck.size()>0){
            return Result.error("以下品牌未通过海关知识产权备案检验：【"+String.join(",",brandNotCheck)
            +"】");
        }else {
            return Result.ok();
        }
    }

    @AutoLog(value = "报关单逻辑校验")
    @ApiOperation(value = "报关单逻辑校验", notes = "报关单逻辑校验")
    @GetMapping(value = "/decLogicVerification")
    public Result<?> decLogicVerification(String id){

        if(StringUtils.isBlank(id)){
            return Result.error("未获取到报关单信息，请刷新后重新");
        }
        DecHead decHead=decHeadService.getDecHeadById(id);
        if(isEmpty(decHead)){
            return Result.error("未获取到报关单信息，请刷新后重新");
        }
        //获取请求接口的token
        SysConfig sysConfig=sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
        .eq(SysConfig::getConfigKey,"DEC_LOGIC_VERIFICATION"));
        //如果数据库没有这个配置
        if(isEmpty(sysConfig)){
            sysConfig=new SysConfig();
            String token = loginInvoiceApi();
            sysConfig.setConfigValue(token);
        }
        //组装数据
        String requestParam=setPreDecFrom(decHead);
//        String requestParam="";
        log.info("requestParam==="+requestParam);

        //执行请求
        String response=HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/Etg_validate")
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("Token",sysConfig.getConfigValue())
                .body("param="+requestParam)
                .timeout(30000)//超时，毫秒
                .execute().body();
        log.info("response==="+response);
        JSONArray jsonArray=JSON.parseArray(response);
        //获取登录信息失败重新登录
//        if(401==jsonObject.getInteger("code")){
//            String tokenNew=loginInvoiceApi();
//            if(isBlank(tokenNew)){
//                return Result.error("校验错误，请联系管理员");
//            }
//            this.decLogicVerification(id);
//        }
        return Result.OK(jsonArray);
    }
    @AutoLog(value = "逻辑校验")
    @ApiOperation(value = "逻辑校验", notes = "逻辑校验")
    @PostMapping(value = "/Ydt_validate")
    public Result<?> Ydt_validate(@RequestParam String param){

        String response=decHeadService.Ydt_validate(param);
        return Result.OK(response);
    }
    //登录报关单校验接口
    private String loginInvoiceApi(){
        String response= HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/GetToken")
                .header("Content-Type","application/x-www-form-urlencoded")
                .body("Account="+account+"&PassWord="+passWord)//表单内容
                .timeout(30000)//超时，毫秒
                .execute().body();
        JSONObject jsonObject=JSON.parseObject(response);
        if(200==jsonObject.getInteger("code")){
            //先删除再添加
            sysConfigMapper.delete(new LambdaQueryWrapper<SysConfig>()
            .eq(SysConfig::getConfigKey,"DEC_LOGIC_VERIFICATION"));
            SysConfig sysConfig=new SysConfig();
            sysConfig.setConfigKey("DEC_LOGIC_VERIFICATION");
            sysConfig.setConfigName("报关单逻辑校验请求token");
            sysConfig.setConfigValue(jsonObject.getString("data"));
            sysConfigMapper.insert(sysConfig);
            return jsonObject.getString("data");
        }
        return null;
    }

    //将本地报关单数据组装成请求的数据
    private String setPreDecFrom(DecHead decHead){
        //全部数据的dto
        DecLogicVerificationDTO decLogicVerificationDTO=new DecLogicVerificationDTO();
        //报关单表头
        JgVFlyBgDechead preDecHeadVo=new JgVFlyBgDechead();
        //报关单表体
        List<JgVFlyBgDeclist> preDecListVo=new ArrayList<>();
        //报关单集装箱
        List<JgVFlyBgDeccontainer> preDecContainerVo=new ArrayList<>();
        //报关单随附单证
        List<JgVFlyBgDeclicense> preDecLicenseVo=new ArrayList<>();
        //报关单随附单据
        List<JgVFlyBgDecdocvo> preDecDocVo=new ArrayList<>();
//        表头数据处理
        setPreDecHead(decHead,preDecHeadVo);
        //表体数据处理
        setPreDecList(decHead.getDecLists(),preDecListVo);
        //集装箱处理
        setPreDecContainerVo(decHead.getDecContainers(),preDecContainerVo);

//        setPreDecLicenseVo(decHead.getDecLicenseDocuses(),)


        decLogicVerificationDTO.setPreDecHeadVo(preDecHeadVo);
        decLogicVerificationDTO.setPreDecListVo(preDecListVo);
        decLogicVerificationDTO.setPreDecContainerVo(preDecContainerVo);
        decLogicVerificationDTO.setPreDecLicenseVo(preDecLicenseVo);
        decLogicVerificationDTO.setPreDecDocVo(preDecDocVo);
        return JSONObject.toJSONString(decLogicVerificationDTO);

    }
    //        表头数据处理
    private void setPreDecHead(DecHead decHead,JgVFlyBgDechead preDecHeadVo){
        List<DictModelVO> bgdlxList = decListMapper.getDictItemByCode("BGDLX");//报关单类型字典
        List<ErpCustomsPorts> erpCustomsPortsList=erpCustomsPortsService.list();
        List<ErpTransportTypes> erpTransportTypesList=erpTransportTypesService.list();
        List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
        List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
        List<ErpCountries> erpCountriesList=erpCountriesService.list();
        List<DictModelVO> tradingTypeList = decListMapper.getDictItemByCode("trading_type");
        List<ErpPackagesTypes> erpPackagesTypesList=erpPackagesTypesService.list();
        List<ErpCityports> erpCityportsList=erpCityportsService.list();
        List<ErpCurrencies> erpCurrenciesList=erpCurrenciesService.list();
        List<ErpChinaPorts> erpChinaPortsList=erpChinaPortsService.list();
        List<DictModelVO> jyjg = decListMapper.getDictItemByCode("JYJG");
        preDecHeadVo.setCustomMaster(decHead.getDeclarePlace());// 申报地海关
        //申报地海关名称
        if(StringUtils.isNotBlank(decHead.getDeclarePlace())){
            List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                    .equals(decHead.getDeclarePlace()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCustomMasterName(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
        }
        preDecHeadVo.setCusIEFlag(decHead.getIeFlag());// 进出口标识
        preDecHeadVo.setCusCiqNo(decHead.getSeqNo()); // 统一编号
        preDecHeadVo.setIEPort(decHead.getOutPortCode()); // 进出境关别
        //出境关别名称
        if(StringUtils.isNotBlank(decHead.getOutPortCode())){
            List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                    .equals(decHead.getOutPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setIEPortName(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
        }
        preDecHeadVo.setContrNo(decHead.getContract()); // 合同协议号
        preDecHeadVo.setIEDate(decHead.getOutDate()); // 进出日期
        preDecHeadVo.setRcvgdTradeCode(decHead.getOptUnitId()); // 境内收发货人海关编码
        preDecHeadVo.setConsigneeCname(decHead.getOptUnitName()); // 境内收发货人名称
        preDecHeadVo.setAgentCode(decHead.getDeclareUnit()); // 申报单位海关代码
        preDecHeadVo.setCusTrafMode(decHead.getShipTypeCode()); // 运输方式
        //运输方式名称
        if(StringUtils.isNotBlank(decHead.getShipTypeCode())){
            List<ErpTransportTypes> erpTransportTypes=erpTransportTypesList.stream().filter(i->i.getCode()
                    .equals(decHead.getShipTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCusTrafModeName(erpTransportTypes.size()>0?erpTransportTypes.get(0).getName():"");
        }
        preDecHeadVo.setTrafName(decHead.getShipName()); // 运输工具名称
        preDecHeadVo.setCusVoyageNo(decHead.getVoyage()); // 航次号
        preDecHeadVo.setBillNo(decHead.getBillCode()); // 提运单号
        preDecHeadVo.setSupvModeCdde(decHead.getTradeTypeCode()); // 监管方式
        //监管方式名称
        if(StringUtils.isNotBlank(decHead.getTradeTypeCode())){
            List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getValue()
                    .equals(decHead.getTradeTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setSupvModeCddeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }

        preDecHeadVo.setCutMode(decHead.getTaxTypeCode()); // 征免性质
        //征免性质名称
        if(StringUtils.isNotBlank(decHead.getTaxTypeCode())){
            List<DictModelVO> dictModelVO1=zmxz.stream().filter(i->i.getValue()
                    .equals(decHead.getTaxTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCutModeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }
        preDecHeadVo.setCusTradeCountry(decHead.getArrivalArea()); // 启运国
        //启运国名称
        if(StringUtils.isNotBlank(decHead.getArrivalArea())){
            List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                    .equals(decHead.getArrivalArea()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCusTradeCountryName(erpCountries.size()>0?erpCountries.get(0).getName():"");
        }

        preDecHeadVo.setTransMode(decHead.getTermsTypeCode()); // 成交方式
        //成交方式名称
        if(StringUtils.isNotBlank(decHead.getTermsTypeCode())){
            List<DictModelVO> dictModelVO1=tradingTypeList.stream().filter(i->i.getValue()
                    .equals(decHead.getTermsTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setTransModeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }
        preDecHeadVo.setFeeCurr(decHead.getShipCurrencyCode()); // 运费币制
        // 运费币制名称
        if(isNotBlank(decHead.getShipCurrencyCode())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                    .equals(decHead.getShipCurrencyCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setFeeCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }

        preDecHeadVo.setFeeMark(decHead.getShipFeeCode()); // 运费代码 setShipFee
        if(isNotBlank(decHead.getShipFeeCode())){
            if("1".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("率"); // 运费代码名称 setShipFee
            }else if("2".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("单价"); // 运费代码名称 setShipFee
            }else if("3".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("总价"); // 运费代码名称 setShipFee
            }
        }
        preDecHeadVo.setFeeRate(null!=decHead.getShipFee()?String.valueOf(decHead.getShipFee()):""); // 运费值
        preDecHeadVo.setInsurCurr(decHead.getInsuranceCurr()); // 保费币制
        // 保费币制名称
        if(isNotBlank(decHead.getInsuranceCurr())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                    .equals(decHead.getInsuranceCurr()))
                    .collect(Collectors.toList());
            preDecHeadVo.setInsurCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }
        preDecHeadVo.setInsurMark(decHead.getInsuranceCode()); // 保费代码 setInsurance
        if(isNotBlank(decHead.getInsuranceCode())){
            if("1".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("率"); // 保费代码名称 setShipFee
            }else if("2".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("单价"); // 保费代码名称 setShipFee
            }else if("3".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("总价"); // 保费代码名称 setShipFee
            }
        }
        preDecHeadVo.setInsurRate(null!=decHead.getInsurance()?String.valueOf(decHead.getInsurance()):""); // 保费值
        preDecHeadVo.setOtherCurr(decHead.getOtherCurr()); // 杂费币制
        // 杂费币制名称
        if(isNotBlank(decHead.getOtherCurr())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                    .equals(decHead.getOtherCurr()))
                    .collect(Collectors.toList());
            preDecHeadVo.setOtherCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }
        preDecHeadVo.setOtherMark(decHead.getExtrasCode()); // 杂费代码
        if(isNotBlank(decHead.getExtrasCode())){
            if("1".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("率"); // 杂费代码名称 setShipFee
            }else if("2".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("单价"); // 杂费代码名称 setShipFee
            }else if("3".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("总价"); // 杂费代码名称 setShipFee
            }
        }
        preDecHeadVo.setOtherRate(null!=decHead.getExtras()?String.valueOf(decHead.getExtras()):""); // 杂费值

        preDecHeadVo.setPackNo(null!=decHead.getPacks() ? String.valueOf(decHead.getPacks()) : null); // 件数
        preDecHeadVo.setWrapType(decHead.getPacksKinds()); // 包装种类
        //包装名称
        if(StringUtils.isNotBlank(decHead.getPacksKinds())){
            List<ErpPackagesTypes> erpPackagesTypes=erpPackagesTypesList.stream().filter(i->i.getCode()
                    .equals(decHead.getPacksKinds()))
                    .collect(Collectors.toList());
            preDecHeadVo.setWrapTypeName(erpPackagesTypes.size()>0?erpPackagesTypes.get(0).getName():"");
        }


        if(StringUtils.isNotBlank(decHead.getPacksKinds())){
            List<ErpPackagesTypes> erpPackagesTypes = erpPackagesTypesService.list(new LambdaQueryWrapper<ErpPackagesTypes>()
                    .eq(ErpPackagesTypes::getCode, decHead.getPacksKinds()));
            preDecHeadVo.setWrapTypeName(erpPackagesTypes.size()>0?erpPackagesTypes.get(0).getName():""); // 包装种类名称
        }
        preDecHeadVo.setGrossWt(null!=decHead.getGrossWeight() ? String.valueOf(decHead.getGrossWeight()) : null); // 毛重
        preDecHeadVo.setNetWt(null!=decHead.getNetWeight() ? String.valueOf(decHead.getNetWeight()) : null); // 净重
        preDecHeadVo.setDespPortCode(decHead.getDespPortCode()); // 启运港代码--进口
        //启运港代码名称
        if(StringUtils.isNotBlank(decHead.getDespPortCode())){
            List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                    .equals(decHead.getDespPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setDespPortCodeName(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
        }


        preDecHeadVo.setGoodsPlace(decHead.getGoodsPlace()); // 货物存放地点
        preDecHeadVo.setEntryType(decHead.getClearanceType()); // 报关单类型
        if(isNotBlank(decHead.getClearanceType())){
            List<DictModelVO> dictModelVOS=bgdlxList.stream().filter(j->j.getValue()
                    .equals(decHead.getClearanceType()))
                    .collect(Collectors.toList());
            preDecHeadVo.setEntryTypeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setNoteS(decHead.getMarkNumber());
        preDecHeadVo.setPromiseItems(isNotBlank(decHead.getPromiseItmes()) ?
                 decHead.getPromiseItmes().replaceAll("\\|","") : null); // 特殊关系/价格说明
        preDecHeadVo.setMarkNo(decHead.getMarkNo()); // 标记唛码
        //业务事项
        if(isNotBlank(decHead.getDecType())&&decHead.getDecType().contains("Z")){
            preDecHeadVo.setCusRemark("100000000000000");//自报自缴，待完善
        }
        preDecHeadVo.setOrgCode(decHead.getOrgCode()); // 检验检疫受理机关 商检信息
        // 检验检疫受理机关名称
        if(isNotBlank(decHead.getOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                    .equals(decHead.getOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setVsaOrgCode(decHead.getVsaOrgCode()); // 领证机关 商检信息
        // 领证机关名称
        if(isNotBlank(decHead.getVsaOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                    .equals(decHead.getVsaOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setVsaOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setInspOrgCode(decHead.getInspOrgCode()); // 口岸检验检疫机关 商检信息
        // 口岸检验检疫机关名称
        if(isNotBlank(decHead.getInspOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                    .equals(decHead.getInspOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setInspOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setDespDate(decHead.getDespDate()); // 启运日期 格式为：yyyyMMdd 商检信息
        preDecHeadVo.setPurpOrgCode(decHead.getPurpOrgCode()); // 目的地检验检疫机关 商检信息
        // 目的地检验检疫机关名称
        if(isNotBlank(decHead.getPurpOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                    .equals(decHead.getPurpOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setPurpOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }

        preDecHeadVo.setCiqEntyPortCode(decHead.getEntyPortCode()); // 入境口岸
        //入境口岸名称
        if(StringUtils.isNotBlank(decHead.getEntyPortCode())){
            List<ErpChinaPorts> erpChinaPorts=erpChinaPortsList.stream().filter(i->i.getChinaPortCode()
                    .equals(decHead.getEntyPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCiqEntyPortCodeName(erpChinaPorts.size()>0?erpChinaPorts.get(0).getName():"");
        }
        preDecHeadVo.setEntQualifTypeCodeSName("[]".equals(decHead.getCopLimitType())?null:decHead.getCopLimitType());//企业资质
        preDecHeadVo.setManualNo(decHead.getRecordNumber());//备案号
    }
    //表体数据处理
    private void setPreDecList(List<DecList> decList,List<JgVFlyBgDeclist> preDecListVo){
        //需要用到字典
        List<ErpUnits> erpUnitsList=erpUnitsService.list();//成交单位
        List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");//征免方式
        List<DictModelVO> yt = decListMapper.getDictItemByCode("YT");//用途
        List<DictModelVO> hwsx = decListMapper.getDictItemByCode("HWSX");//用途
        List<ErpCountries> erpCountriesList=erpCountriesService.list();//国家地区
        for(DecList decListObj:decList){
            JgVFlyBgDeclist jgVFlyBgDeclist=new JgVFlyBgDeclist();
            jgVFlyBgDeclist.setGNo(String.valueOf(decListObj.getItem())); // 项号
            jgVFlyBgDeclist.setCodeTs(decListObj.getHscode()); // 商品编码 税号
            jgVFlyBgDeclist.setGModel(decListObj.getHsmodel()); // 规格型号 申报要素
            jgVFlyBgDeclist.setGQty(String.valueOf(decListObj.getGoodsCount())); // 成交数量
            jgVFlyBgDeclist.setGUnit(decListObj.getUnitCode()); // 成交单位
            if(isNotBlank(decListObj.getUnitCode())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnitCode())).collect(Collectors.toList());
                jgVFlyBgDeclist.setGUnitName(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//成交单位名称
            }
            jgVFlyBgDeclist.setDeclPrice(String.valueOf(decListObj.getPrice())); // 单价
            jgVFlyBgDeclist.setDeclTotal(String.valueOf(decListObj.getTotal())); // 总价
            jgVFlyBgDeclist.setTradeCurr(decListObj.getCurrencyCode()); // 币制
            jgVFlyBgDeclist.setQty1(String.valueOf(decListObj.getCount1())); // 法定数量
            jgVFlyBgDeclist.setUnit1(decListObj.getUnit1()); // 法定单位
            if(isNotBlank(decListObj.getUnit1())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnit1())).collect(Collectors.toList());
                jgVFlyBgDeclist.setUnit1Name(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//法定单位名称
            }
            jgVFlyBgDeclist.setDestinationCountry(decListObj.getDestinationCountry()); // 最终目的国
            //最终目的国名称
            if(StringUtils.isNotBlank(decListObj.getDestinationCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                                .equals(decListObj.getDestinationCountry()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setDestinationCountryName(!erpCountries.isEmpty() ?erpCountries.get(0).getName():"");
            }

            jgVFlyBgDeclist.setQty2(String.valueOf(decListObj.getCount2())); // 法定数量2
            jgVFlyBgDeclist.setUnit2(decListObj.getUnit2()); // 法定单位2
            if(isNotBlank(decListObj.getUnit2())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnit2())).collect(Collectors.toList());
                jgVFlyBgDeclist.setUnit2Name(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//法定单位2名称
            }
            jgVFlyBgDeclist.setCusOriginCountry(decListObj.getDesCountry()); // 原产国
            //原产国名称
            if(StringUtils.isNotBlank(decListObj.getDesCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                                .equals(decListObj.getDesCountry()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setCusOriginCountryName(!erpCountries.isEmpty() ?erpCountries.get(0).getName():"");
            }
            jgVFlyBgDeclist.setDutyMode(decListObj.getFaxTypeCode()); // 征免方式
            if(isNotBlank(decListObj.getFaxTypeCode())){
                List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(j->j.getValue()
                        .equals(decListObj.getFaxTypeCode()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setDutyModeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
            }
            jgVFlyBgDeclist.setGoodsAttr(decListObj.getGoodsAttr()); // 货物属性代码
            //货物属性名称
            if(isNotBlank(decListObj.getGoodsAttr())){
                List<String> goodsAttrNameList=new ArrayList<>();
                for(String s:Arrays.asList(decListObj.getGoodsAttr().split(","))){
                    List<DictModelVO> dictModelVOS=hwsx.stream().filter(j->j.getValue()
                            .equals(s))
                            .collect(Collectors.toList());
                    goodsAttrNameList.add(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
                }
                jgVFlyBgDeclist.setGoodsAttrName(String.join(",",goodsAttrNameList));
            }
            jgVFlyBgDeclist.setGName(decListObj.getHsname()); // 商品名称 申报品名
            jgVFlyBgDeclist.setDistrictCode(decListObj.getDistrictCode()); // 境内目的地/境内货源地
            jgVFlyBgDeclist.setCiqName(decListObj.getCiqName());
            jgVFlyBgDeclist.setPurpose(decListObj.getPurpose()); // 用途代码
            if(isNotBlank(decListObj.getPurpose())){
                // 用途名称
                List<DictModelVO> dictModelVOS=yt.stream().filter(j->j.getValue()
                        .equals(decListObj.getPurpose()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setPurposeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
            }
            //许可证信息
            if(isNotBlank(decListObj.getGoodsLimitType())){
                String decRequCertList = decListObj.getGoodsLimitType()
                        .replaceAll("LicenceNo", "licenceNo")
                        .replaceAll("LicTypeCode", "licTypeCode")
                        .replaceAll("LicWrtofDetailNo", "licWrtofDetailNo")
                        .replaceAll("LicWrtofQty", "licWrtofQty")
                        .replaceAll("LicWrtofQtyUnit", "licWrtofQtyUnit")
                        .replaceAll("LicTypeName", "licTypeName");
                jgVFlyBgDeclist.setPreDecCiqGoodsLimit(decRequCertList);
            }
            preDecListVo.add(jgVFlyBgDeclist);
        }

    }
    //集装箱处理
    private void setPreDecContainerVo(List<DecContainer> decContainerList,List<JgVFlyBgDeccontainer> preDecContainerVoList){
        for(DecContainer decContainer:decContainerList){
            JgVFlyBgDeccontainer preDecContainerVo=new JgVFlyBgDeccontainer();
            preDecContainerVo.setContainerNo(decContainer.getContainerId());
            preDecContainerVo.setContainerMdCode(decContainer.getContainerMd());
            preDecContainerVo.setContainerWt(String.valueOf(decContainer.getGoodsContaWt()));
            preDecContainerVo.setLclFlag(decContainer.getLclFlag());
            preDecContainerVo.setGoodsNo(decContainer.getGoodsNo());
            preDecContainerVoList.add(preDecContainerVo);
        }

    }


    private Boolean sendRequest(String optUnitName,String brand){
        String appKey = "QqcO20V6nL63KuFu84L6";
        String secret = "PcV6HVGIK0Cjl72iuAGWNbOh0H5DBXIcK3EqdcX4";

        String timestamp = getTime();
        String nonce = getNonce();

        //业务参数, -- 要用LinkedHashMap
        Map<String, Object> jsonMap = new LinkedHashMap<String, Object>();
        jsonMap.put("name", optUnitName);
        jsonMap.put("recordName", brand);
        jsonMap.put("page", 1);
        jsonMap.put("maxResults", 10);

        System.out.println("=====jsonMap请求数据=====");
        String json = JSON.toJSONString(jsonMap);
        //业务参数必须进行编码
        try {
            json = URLEncoder.encode(json, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        System.out.println(json);

        //系统参数
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("accessKey", appKey);
        param.put("data", json);
        param.put("timestamp", timestamp);
        param.put("nonce",nonce);

        String sign = null;
        try {
            sign = buildSign(param, secret);
        } catch (IOException e) {
            e.printStackTrace();
        }
        param.put("reqSign", sign);

        System.out.println("=====param请求数据=====");
        String postJson = JSON.toJSONString(param);
        System.out.println(postJson);

        String resp = HttpUtil.post("https://api.jgsoft.com.cn:15555/open-api/brand/dPage",param); //测试post请求，参数会装载到body
        //处理返回数据
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if(jsonObject.getObject("successful",Boolean.class)){
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray records = data.getJSONArray("records");
            if(records.size()>0){
                return true;
            }else {
                return false;
            }
        }else {
            log.info("获取失败！");
            return false;
        }

    }
    /**
     * 构建签名
     *
     * @param paramsMap
     *            参数
     * @param secret
     *            密钥
     * @return
     * @throws IOException
     */
    public static String buildSign(Map<String, ?> paramsMap, String secret) throws IOException {
        Set<String> keySet = paramsMap.keySet();
        List<String> paramNames = new ArrayList<String>(keySet);

        Collections.sort(paramNames);

        StringBuilder paramNameValue = new StringBuilder();

        for (String paramName : paramNames) {
            paramNameValue.append(paramName).append(paramsMap.get(paramName));
        }

        String source = secret + paramNameValue.toString() + secret;

        return md5(source);
    }

    /**
     * 生成md5,全部大写
     *
     * @param message
     * @return
     */
    public static String md5(String message) {
        try {
            // 1 创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 2 将消息变成byte数组
            byte[] input = message.getBytes();

            // 3 计算后获得字节数组,这就是那128位了
            byte[] buff = md.digest(input);

            // 4 把数组每一字节（一个字节占八位）换成16进制连成md5字符串
            return byte2hex(buff);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 二进制转十六进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    public String getTime() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public String getNonce() {
        String nonce = RandomUtil.randomNumbers(6);
        if (nonce.startsWith("0")) {
            nonce = getNonce();
        }
        return nonce;
    }


    private void saveSysAnnouncement(String titile,String msgContent) {
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        String id = IdWorker.getIdStr(sysAnnouncement);
        sysAnnouncement.setId(id);
        sysAnnouncement.setTitile(titile);
        sysAnnouncement.setCreateTime(new Date());
        //消息类型为预警消息
        sysAnnouncement.setMsgCategory("4");
        sysAnnouncement.setMsgType(MSG_TYPE_UESR);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        sysAnnouncement.setMsgContent(msgContent);
        sysAnnouncement.setBusType("warning");
        List<String> idList = new ArrayList<>(16);
        idList.add(sysUser.getId());
        if (isEmpty(idList)) {
            log.info("[sendMessage]用户ID列表是空的，无法发送消息！");
            return;
        }
        sysAnnouncement.setUserIds(CollUtil.join(idList, ","));
//		sysAnnouncement.setOpenType("component");
//		sysAnnouncement.setOpenPage("/documentManage/DocumentManageList");
        sysAnnouncement.setCreateBy("system");
        sysAnnouncement.setSender("system");
        sysAnnouncement.setTenantId(TenantContext.getTenant());
        sysAnnouncement.setSendTime(new Date());
        sysAnnouncement.setDelFlag("0");
        //添加系统通告
        decHeadMapper.saveSysAnnouncement(sysAnnouncement);

        decHeadMapper.saveSysAnnouncementSend(String.valueOf(IdWorker.getId()),
                sysAnnouncement.getId(),sysUser.getId(),"system",new Date(),"4"
                );
    }

    /**
     * 根据报关单信息返回舱单信息
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    @GetMapping(value = "/getManifestInfo")
    public Result<?> getManifestInfo(@RequestParam("id") String id,
                                     @RequestParam("pc") String pc,
                                     @RequestParam("tt") String tt,
                                     @RequestParam("wb") String wb) {
        return decHeadService.getManifestInfo(id, pc, tt, wb,null);
    }

    /**
     * 根据船名航次提单号返回海运舱单的信息
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    @GetMapping(value = "/getShipManifestInfo")
    public Result<?> getShipManifestInfo(@RequestParam("id") String id,
                                         @RequestParam("iEFlag") String iEFlag,
                                         @RequestParam("trafName") String trafName,
                                         @RequestParam("cusVoyageNo") String cusVoyageNo,
                                         @RequestParam("billNo") String billNo) {
        return decHeadService.getShipManifestInfo(id, iEFlag, trafName, cusVoyageNo, billNo);
    }

    /**
     * 企业报关差错记录
     * 返回企业报关差错记录和详细错误信息
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    @GetMapping(value = "/getCusError")
    public Result<?> getCusError(@RequestParam("starDate") String starDate,
                                 @RequestParam("lastDate") String lastDate) {
        return decHeadService.getCusError(starDate, lastDate);
    }

    /**
     * 企业修撤单记录
     * 根据时间返回报关单修撤单的记录列表
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    @GetMapping(value = "/getDecModList")
    public Result<?> getDecModList(@RequestParam("starDate") String starDate,
                                 @RequestParam("lastDate") String lastDate) {
        return decHeadService.getDecModList(starDate, lastDate);
    }

    /**
     * 企业修撤单记录
     * 根据时间返回报关单修撤单的记录列表
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    @GetMapping(value = "/syncDecMod")
    public Result<?> syncDecMod(@RequestParam("starDate") String starDate,
                                @RequestParam("lastDate") String lastDate) {
        return decHeadService.syncDecMod(starDate, lastDate);
    }

    /**
     * 报关单税单信息
     * 返回指定报关单的税单信息（含税单货物信息）
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @GetMapping(value = "/getCDTax")
    public Result<?> getCDTax(@RequestParam("entryId") String entryId) {
        return decHeadService.getCDTax(entryId);
    }

    /**
     * 返回税单核对单PDF文件
     * 根据报关单税单号或报关单号来获取税单核对单PDF格式文件内容。
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/27 下午3:23
     */
    @GetMapping(value = "/getCDTaxPdf")
    public Result<?> getCDTaxPdf(@RequestParam("entryId") String entryId) {
        return decHeadService.getCDTaxPdf(entryId);
    }

    /**
     * 报关单文件退税联(PDF)
     * 根据报关单号来获取报关单退税联 PDF 格式文件内容。如果成功，直接返回 pdf 文件。否则返回 json 格式错误。
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:36
     */
    @GetMapping(value = "/getCDPdfRtx")
    public Result<?> getCDPdfRtx(@RequestParam("entryId") String entryId) {
        return decHeadService.getCDPdfRtx(entryId);
    }

    /**
     * 报关单放行通知(PDF)
     * 根据报关单统一编号或报关单号来获取放行通知 PDF 格式文件内容。建议放行后再调取。
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:55
     */
    @GetMapping(value = "/getCDReleaseNote")
    public Result<?> getCDReleaseNote(@RequestParam("cusCiqNo") String cusCiqNo) {
        return decHeadService.getCDReleaseNote(cusCiqNo);
    }

    /**
     * 报关单查验通知(PDF)
     * 根据报关单统一编号或报关单号来获取查验通知 PDF 格式文件内容。如果没有查验通知，返回错误信息
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午2:04
     */
    @GetMapping(value = "/getCDCheckNote")
    public Result<?> getCDCheckNote(@RequestParam("cusCiqNo") String cusCiqNo) {
        return decHeadService.getCDCheckNote(cusCiqNo);
    }

    /**
     * 获取进出口状态
     * 返回指定报关单或提单号的进出口状态。
     *
     * @param entryId
     * @param billNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/4 下午1:09
     */
    @GetMapping(value = "/getSwIEStatus")
    public Result<?> getSwIEStatus(@RequestParam("ieFlag") String ieFlag,
                                   @RequestParam(value = "entryId", required = false) String entryId,
                                   @RequestParam(value = "billNo", required = false) String billNo) {
        return decHeadService.getSwIEStatus(ieFlag, entryId, billNo);
    }

    /**
     * 获取需要同步舱单的报关单数据
     * @return
     */
    @GetMapping(value = "/getDecHeadByManifestInfo")
    public Result<?> getDecHeadByManifestInfo(String tenandId){

        return decHeadService.getDecHeadByManifestInfo(tenandId);
    }
    /**
     * 报关单生成核注单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/30 10:15
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "报关单生成核注单")
    @ApiOperation(value = "报关单生成核注单", notes = "报关单生成核注单")
    @PostMapping(value = "/handleCreateInvtByDec")
    public Result<?> handleCreateInvtByDec(@RequestParam("id") String id,@RequestParam("sysId") String sysId) {
        return decHeadService.handleCreateInvtByDec(id,sysId);
    }

    /**
     * 报关单列表
     * 根据最近操作时间，返回报关单列表。
     * 注意：最多只能查询 7 天的数据（使用 entryId 或 billNo 查询时，不受此限制）
     *
     * @param dclTrnRelFlag 报关单类型（空表示全部） 0 – 一般报关单 1 – 转关提前报关单 2 – 备案清单 3 – 转关提前备案清单 4 – 出口二次转关
     * @param etpsCategory 企业类别（空表示全部） A- 报关申报单位 B- 销售使用/生产销售单位 C - 报关收发货人 D – 报关录入单位
     * @param cusIEFlag 进出口标志（进口-I，出口-E， 全部-空串）
     * @param tableFlag 是否结关（未结关-0， 已结关-1， 全部-空串）
     * @param cnsnTradeCode 境内收发货人（18 位或 10 位）
     * @param entryId 报关单号或统一编号
     * @param billNo 提运单号
     * @param beginTime 起始时间(格式 yyyy-MM-dd)
     * @param endTime 结束时间(格式 yyyy-MM-dd)
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @GetMapping(value = "/GetCDQuery")
    public Result<?> GetCDQuery(@RequestParam(value = "dclTrnRelFlag", required = false) String dclTrnRelFlag,
                                @RequestParam(value = "etpsCategory", required = false) String etpsCategory,
                                @RequestParam(value = "cusIEFlag", defaultValue = "") String cusIEFlag,
                                @RequestParam(value = "tableFlag", defaultValue = "") String tableFlag,
                                @RequestParam(value = "cnsnTradeCode", required = false) String cnsnTradeCode,
                                @RequestParam(value = "entryId", required = false) String entryId,
                                @RequestParam(value = "billNo", required = false) String billNo,
                                @RequestParam(value = "beginTime", defaultValue = "") String beginTime,
                                @RequestParam(value = "endTime", defaultValue = "") String endTime,
                                @RequestParam("swid") String swid) {
        return decHeadService.GetCDQuery(dclTrnRelFlag, etpsCategory, cusIEFlag, tableFlag,
                cnsnTradeCode, entryId, billNo, beginTime, endTime, swid);
    }

    /**
     * 报关单全量数据(JSON)
     * 根据报关单统一编号或报关单号来获取报关单 Json 格式全部内容。
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    @GetMapping(value = "/GetCDDetails")
    public Result<?> GetCDDetails(@RequestParam(value = "cusCiqNo", required = false) String cusCiqNo,
                                  @RequestParam("swid") String swid) {
        return decHeadService.GetCDDetails(cusCiqNo, swid);
    }

    /**
     * 报关单全量数据(JSON)
     * 根据报关单统一编号或报关单号来获取报关单 Json 格式全部内容。
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    @GetMapping(value = "/GetCDDetailsByJsonFiles")
    public Result<?> GetCDDetailsByJsonFiles(@RequestParam("path") String path,
                                             @RequestParam("swid") String swid) {
        return decHeadService.GetCDDetailsByJsonFiles(path, swid);
    }

    /**
     * 报关单列表单独查询某一条数据
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    @GetMapping(value = "/syncDecClearance")
    public Result<?> syncDecClearance(@RequestParam("swid") String swid) {
        return decHeadService.syncDecClearance(swid);
    }

    @PostMapping(value = "/hideDecList")
    public Result<?> hideDecList(@RequestParam(value = "id") String id) {
        decListService.deleteDecList(id);
        return Result.OK("删除成功！");
    }
    @PostMapping(value = "/" +
            "")
    public Result<?> Ydt_batchExtractReportKey(@RequestParam(value = "goods") String goods) {
        decHeadService.Ydt_batchExtractReportKey(goods);
        return Result.OK("获取成功！");
    }
    @PostMapping(value = "/Ydt_batchQueryGoodsList")
    public Result<?> Ydt_batchQueryGoodsList(@RequestParam(value = "goodsHsCodes") String goodsHsCodes) {
        return Result.OK(decHeadService.Ydt_batchQueryGoodsList(goodsHsCodes));

    }
    @GetMapping("/listDecContainer")
    public Result<?> listDecContainer(DecContainer decContainer,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<DecContainer> page = new Page<DecContainer>(pageNo, pageSize);
        IPage<DecContainer> decContainerIPage =
                decContainerMapper.listDecContainerPage(page, TenantContext.getTenant(), decContainer.getContainerId());
        return Result.OK(decContainerIPage);
    }

    @GetMapping("/listDecHead")
    public Result<?> listDecHead(DecHead decHead,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<DecHead> page = new Page<DecHead>(pageNo, pageSize);
        LambdaQueryWrapper<DecHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(isNotBlank(decHead.getClearanceNo()),DecHead::getClearanceNo,decHead.getClearanceNo());
        queryWrapper.eq(DecHead::getTenantId,TenantContext.getTenant());
        queryWrapper.isNotNull(DecHead::getClearanceNo);
        queryWrapper.orderByDesc(DecHead::getCreateTime);
        Page<DecHead> decHeadPage = decHeadMapper.selectPage(page, queryWrapper);
        return Result.OK(decHeadPage);
    }

    /**
     * AI制单
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/14 15:58
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
//    @AutoLog(value = "AI制单")
    @PostMapping("/aiMaker")
    public Result<?> aiMaker(@RequestParam(value = "files[]", required = false) MultipartFile[] files,
                             @RequestParam(value = "ieFlag", defaultValue = "E") String ieFlag,
                             @RequestParam(value = "customerName", required = false) String customerName,
                             @RequestParam(value = "declarePlace", required = false) String declarePlace,
                             @RequestParam(value = "outPortCode", required = false) String outPortCode,
                             @RequestParam(value = "shipTypeCode", required = false) String shipTypeCode,
                             @RequestParam(value = "modelProvider", defaultValue = "doubao") String modelProvider,
                             @RequestParam(value = "priceReference", required = false) String priceReference,
                             @RequestParam(value = "decId", required = false) String decId) {
        return aiService.aiMaker(files, ieFlag, customerName, declarePlace, outPortCode, shipTypeCode, modelProvider, priceReference, decId);
    }

    /**
     * AI配置列表
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:14
     */
    @GetMapping(value = "/listAiSettings")
    public Result<?> listAiSettings(AiConfig aiConfig) {
        return aiConfigService.listAiSettings(aiConfig);
    }

    /**
     * 保存AI配置
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/18 23:59
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @PostMapping(value = "/saveAiSetting")
    public Result<?> saveAiSetting(@RequestBody AiConfig aiConfig) {
        return aiConfigService.saveAiSetting(aiConfig);
    }

    /**
     * 设置默认AI配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/30 14:19
     */
    @Idempotent(timeout = 1, message = "存在重复请求，已忽略")
    @PostMapping(value = "/setDefaultAiConfig")
    public Result<?> setDefaultAiConfig(@RequestParam("ids") String ids) {
        return aiConfigService.setDefaultAiConfig(ids);
    }

    /**
     * 根据ID查询AI配置
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:09
     */
    @GetMapping(value = "/getAiSettingById")
    public Result<?> getAiSettingById(@RequestParam("id") String id) {
        return aiConfigService.getAiSettingById(id);
    }

    /**
     * 删除Ai配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:21
     */
    @AutoLog(value = "删除Ai配置")
    @RequestMapping(value = "/deleteAiConfigBatch")
    public Result<?> deleteAiConfigBatch(@RequestParam("ids") String ids) {
        return aiConfigService.deleteAiConfigBatch(ids);
    }

    /**
     * 智能填写
     *
     * @param hscode
     * @param text
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/22 12:24
     */
    @AutoLog(value = "智能填写")
    @RequestMapping(value = "/smartFill")
    public Result<?> smartFill(@RequestParam("hscode") String hscode,
                               @RequestParam("text") String text) {
        return aiService.smartFill(hscode, text);
    }

    /**
     *获取当前租户的历史境外收发货人
     * @return
     */
    @GetMapping(value = "/listOverseasConsignorHistory")
    public Result<?> listOverseasConsignorHistory(@RequestParam("ieFlag") String ieFlag){
        return decHeadService.listOverseasConsignorHistory(ieFlag);
    }

    /**
     * AI制单导出发票
     *
     * @param id
     * @param request
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2025/6/20 11:50
     */
    @RequestMapping(value = "/exportInvoice")
    public void exportInvoice(@RequestParam("id") String id,
                                HttpServletRequest request, HttpServletResponse response) {
        aiService.exportInvoice(id, request, response);
    }
}
