package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * AI配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@Accessors(chain = true)
@TableName("ai_config")
public class AiConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 模板名称
     */
    @TableField("TPL_NAME")
    private String tplName;

    @TableField(exist = false)
    private String tplNameC;

    /**
     * 进出口标识
     */
    @TableField("IE_FLAG")
    private String ieFlag;

    /**
     * 运输方式 海运2航空5
     */
    @TableField("SHIP_TYPE_CODE")
    private String shipTypeCode;

    /**
     * 申报地海关
     */
    @TableField("DECLARE_PLACE")
    private String declarePlace;

    /**
     * 进出境关别
     */
    @TableField("OUT_PORT_CODE")
    private String outPortCode;

    /**
     * 收发货人
     */
    @TableField("OPT_UNIT_NAME")
    private String optUnitName;

    /**
     * 价格基准（1单价2总价）
     */
    @TableField("PRICE_REFERENCE")
    private String priceReference;

    /**
     * 是否默认
     */
    @TableField("IS_DEFAULT")
    private Boolean isDefault;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;
}
