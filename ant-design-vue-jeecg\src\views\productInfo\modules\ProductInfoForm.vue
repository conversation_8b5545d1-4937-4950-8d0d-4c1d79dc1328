<template>
	<div class="box productInfoWrapper">
		<a-spin :spinning="confirmLoading">
			<div v-show="showTime && !formDisabled" class="tipsText"
				style="width: 100%; padding-right: 10px; text-align: right;margin-top: -10px;">
				{{ getDateTimeToString() + ' 自动保存' }}
			</div>
			<j-form-container :disabled="formDisabled">
				<a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules">
					<a-collapse :bordered="false" default-active-key="1">
						<a-collapse-panel key="1">
							<div slot="header">
								<span>商品信息</span>
								<div style="float: right;">
									<a-tag v-if="model.isExamine == '1'" color="#87d068">
										已审核
									</a-tag>
									<a-tag v-else color="#f50">
										未审核
									</a-tag>
								</div>
							</div>


							<a-row>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol">
										<template #label>
											<span>商品类别</span>
											<a-tooltip slot="suffix" title="用户自定义的商品类别（非HS编码归类）">
												<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
											</a-tooltip>
										</template>

										<j-tree-select ref="treeSelect" v-model="model.productCategoryInfoId" :condition="condition"
											dict="product_category_info,product_category_name,id" hasChildField="has_child"
											pidField="parent_id" pidValue="0" placeholder="请选择商品类别"
											@change="onChangeProductCategoryInfoId" @search="onSearch(value)">
										</j-tree-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pn">
										<template #label>
											<span>物料号</span>
											<a-tooltip slot="suffix" title="您可以输入物料号，如果为空，则后台会自动生成物料号">
												<a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
											</a-tooltip>
										</template>
										<j-remarks-component v-model="model.pn" :max-length="128" :readOnly="formDisabled"
											placeholder="请输入物料号" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="中文品名" prop="chineseName">
										<j-remarks-component v-model="model.chineseName" :max-length="128" :readOnly="formDisabled"
											placeholder="请输入中文品名" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="英文品名" prop="englishName">
										<j-remarks-component v-model="model.englishName" :max-length="128" :readOnly="formDisabled"
											placeholder="请输入英文品名" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol">
										<template #label>
											<span>原产国(地区)</span>
											<a-tooltip slot="suffix" title="原产国指生产国，一国进口货物的出产或制造国家。默认为中国。">
												<a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
											</a-tooltip>
										</template>

<!--										<j-search-select-tag v-model="model.originCountry" :dict="dictCodeCustomerName"-->
<!--											:pageSize="50" />-->
										<j-search-select-tag
											v-model="model.originCountry"
											:dict="dictCodeCustomerName"
											placeholder="请选择付原产国(地区)"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="中文描述" prop="chineseDescribe">
										<j-remarks-component v-model="model.chineseDescribe" :max-length="512" :readOnly="formDisabled"
											placeholder="请输入中文描述" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="英文描述" prop="englishDescribe">
										<j-remarks-component v-model="model.englishDescribe" :max-length="512" :readOnly="formDisabled"
											placeholder="请输入英文描述" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol">
										<template #label>
											<span>商品标签</span>
										</template>
										<j-select-multiple v-model="model.productTagId" :options="jSelectMultipleOptions" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="条形码" prop="barCode">
										<j-remarks-component v-model="model.barCode" :max-length="64" :readOnly="formDisabled"
											placeholder="请输入条形码" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="商品备注" prop="productRemarks">
										<j-remarks-component v-model="model.productRemarks" :maxLength="256" :readOnly="formDisabled"
											placeholder="请输入备注" />
									</a-form-model-item>
								</a-col>
							</a-row>
							<a-row justify="start" type="flex">
								<a-col :span="24" class="productPicture">
									<a-form-model-item :labelCol="labelCol1" :wrapperCol="wrapperCol1" label="商品图片" prop="productPicture">
										<j-image-upload v-model="model.productPicture" :is-multiple="true"
											biz-path='productInfo'></j-image-upload>
									</a-form-model-item>
								</a-col>
							</a-row>
						</a-collapse-panel>
					</a-collapse>

					<a-collapse :bordered="false" default-active-key="2">
						<a-collapse-panel key="2">
							<div slot="header">
								<span>贸易信息</span>
								<div style="float: right;">
									<a-tag v-if="model.isCcc == '1'" color="orange">
										3C商品
									</a-tag>
									<a-tag v-if="model.isDualUseItem == '1'" color="purple">
										两用物项
									</a-tag>
									<a-tag v-if="model.isProhibit == '1'" color="red">
										禁止进出口
									</a-tag>

									<a-tag color="pink" v-if="isSFJ">
										涉法检 {{ model.iaqcategory }}
									</a-tag>
								</div>
							</div>
							<a-row>
								<a-form ref="form2" :form="form" :model="model" :rules="validatorRules">
									<a-row>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="customsCodeInfoId">
												<template #label>
													<span>海关编码</span>
													<a-tooltip slot="suffix"
														title="海关编码即HS编码，一套可使用的国际贸易商品分类体系。我国目前使用的HS编码，一共10位，其中前面8位称为主码，后两位称为附加码。">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<j-popup
													v-decorator="['customsCodeInfoId', { rules: [{ required: true, message: '请选择海关编码' }] }]"
													:trigger-change="true" code="erp_hscodes"
													org-fields="hscode,addtaxrate,backtaxrate,impcustomrate,legal_unit,second_unit,qtyunit,qtcunit,hscode,hsname,hsname,监管条件,检验检疫类别"
													dest-fields="customsCodeInfoId,addedTaxRate,taxRebateRate,impcustomrate,legalUnit,secondUnit,legalUnitCode,secondUnitCode,customsCodeInfoCode,tariffsName,hsname,monitorcondition,iaqcategory"
													@callback="popupCallback" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="税则品名" prop="tariffsName">
												<a-input v-model="model.tariffsName" v-decorator="['tariffsName']" disabled
													placeholder="根据海关编码自动带取" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申报品名" prop="hsname">
												<a-input v-model="model.hsname"
													v-decorator="['hsname', { rules: [{ required: true, message: '请输入申报品名' }] }]" placeholder="请输入申报品名" />
											</a-form-model-item>
										</a-col>
									</a-row>

									<a-row>
										<a-col :span="16">
											<a-form-model-item :labelCol="labelCol1" :wrapperCol="wrapperCol1"
												prop="customsDeclarationElements">
												<template #label>
													<span>申报要素</span>
													<a-tooltip slot="suffix" title="申报要素是海关针对每种海关商品编码(HS编码)，列出的出口商品申报条件。">
														<a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
													</a-tooltip>
												</template>
												<a-input v-model="model.customsDeclarationElements" v-decorator="['customsDeclarationElements', { rules: [{ required: true, message: '请输入申报要素' }] }]"
													:readOnly="formDisabled"
													placeholder="请输入申报要素">
													<a-icon slot="suffix" type="fullscreen" @click="showModal" style="pointer-events: all" />
												</a-input>
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="监管条件"
												prop="monitorcondition">
												<a-input v-model="model.monitorcondition" v-decorator="['monitorcondition']"
													disabled placeholder="根据海关编码自动带取" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="addedTaxRate">
												<template #label>
													<span>增值税率%</span>
													<a-tooltip slot="suffix" title="增值税税率就是增值税税额占货物或应税劳务销售额的比率，是计算货物或应税劳务增值税税额的尺度。">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<a-input v-decorator="[
			'addedTaxRate',
			{
				rules: [
					{
						pattern: /^(0\.([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{0,2}(\.\d{1,2})?)|0\.[0-9]{1,3}|0\.[0-9]{1,2}|0\.[0-9]{1}|[1-9][0-9]?(\.\d{1,2})?|99(\.0{1,2})?)$/,
						message: '请输入0.0001~99之间的数字',
					},
				],
				trigger: ['change', 'blur'],
			},
		]" :maxLength="17" placeholder="请输入增值税率" style="width: 100%" @change="handleValue" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="taxRebateRate">
												<template #label>
													<span>退税率%</span>
													<a-tooltip slot="suffix"
														title="出口退税率指出口商品应退税额与计算退税的价格比例。出口退税是将出口货物在国内生产、流通环节缴纳的增值税、消费税，在货物报关出口后退还给出口企业的一种税收管理制度，是一国政府对出口货物采取的一项免征或退还国内间接税的税收政策。">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<a-input v-decorator="[
			'taxRebateRate',
			{
				rules: [
					{
						pattern: /^(0\.([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{0,2}(\.\d{1,2})?)|0\.[0-9]{1,3}|0\.[0-9]{1,2}|0\.[0-9]{1}|[1-9][0-9]?(\.\d{1,2})?|99(\.0{1,2})?)$/,
						message: '请输入0.0001~99之间的数字',
					},
				],
				trigger: ['change', 'blur'],
			},
		]" :max-length="17" placeholder="请输入退税率" style="width: 100%" @change="handleValue" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="检验检疫类别"
												prop="iaqcategory">
												<a-input v-model="model.iaqcategory" v-decorator="['iaqcategory']" disabled
													placeholder="根据海关编码自动带取" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="最惠国税率%"
												prop="impcustomrate">
												<a-input v-decorator="[
			'impcustomrate',
			{
				rules: [
					{
						pattern: /^(0\.([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{0,2}(\.\d{1,2})?)|0\.[0-9]{1,3}|0\.[0-9]{1,2}|0\.[0-9]{1}|[1-9][0-9]?(\.\d{1,2})?|99(\.0{1,2})?)$/,
						message: '请输入0.0001~99之间的数字',
					},
				],
				trigger: ['change', 'blur'],
			},
		]" :max-length="17" placeholder="请输入最惠国税率" style="width: 100%" @change="handleValue" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="协定税率%"
												prop="salesGuidancePrice">
												<a-input v-decorator="[
			'salesGuidancePrice',
			{
				rules: [
					{
						pattern: /^(0\.([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{0,2}(\.\d{1,2})?)|0\.[0-9]{1,3}|0\.[0-9]{1,2}|0\.[0-9]{1}|[1-9][0-9]?(\.\d{1,2})?|99(\.0{1,2})?)$/,
						message: '请输入0.0001~99之间的数字',
					},
				],
				trigger: ['change', 'blur'],
			},
		]" :max-length="17" placeholder="请输入协定税率" @change="setleValue">
												</a-input>
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="productSpecificationModel">
												<template #label>
													<span>规格型号</span>
													<a-tooltip slot="suffix" title="即箱单以及发票中的StyleNo">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<a-input v-decorator="['productSpecificationModel']" :max-length="32" placeholder="请输入规格型号"
													@change="setleValue2">
												</a-input>
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item label="计量单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
												<j-search-select-tag v-model="model.qunit"
													dict="erp_units,name,item_key,item_key is not null" placeholder="请选择计量单位" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol">
												<template #label>
													<span>法定单位</span>
													<a-tooltip slot="suffix" title="海关商品编码第一法定单位，通关时使用。">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<a-input v-decorator="['legalUnit']" :maxLength="32" placeholder="请输入法定单位"
													@change="handleValue"></a-input>
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol">
												<template #label>
													<span>第二单位</span>
													<a-tooltip slot="suffix" title="海关商品编码第二法定单位，通关时使用。">
														<a-icon style="margin-top: 13px; margin-left: 2px" theme="twoTone" type="question-circle" />
													</a-tooltip>
												</template>
												<a-input v-decorator="['secondUnit']" :maxLength="32" placeholder="请输入第二单位"
													@change="handleValue"></a-input>
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item ref="netWeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="净重（kg）"
												prop="netWeight">
												<a-input-number v-model="model.netWeight" :max-length="11" placeholder="请输入净重（kg）"
													style="width: 100%" @change="weightChange" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="净重浮动比例">
												<a-input-number v-model="model.netWeightFluctuationRatio" :max-length="11"
													placeholder="净重浮动比例(百分比)" style="width: 100%" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="价格">
												<a-input-number v-model="model.price" :max-length="11" placeholder="请输入价格"
													style="width: 100%" />
											</a-form-model-item>
										</a-col>
										<a-col :span="8">
											<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="价格浮动比例">
												<a-input-number v-model="model.priceFluctuationRatio" :max-length="11"
													placeholder="价格浮动比例(百分比)" style="width: 100%" />
											</a-form-model-item>
										</a-col>
									</a-row>
								</a-form>
							</a-row>
						</a-collapse-panel>
					</a-collapse>

					<a-collapse v-show="isMyInfo" :bordered="false" class="collapse-box" default-active-key="3">
						<a-collapse-panel key="3" header="自定义信息">
							<a-row>
								<a-col v-for="(v, i) in list" :key="i" :span="8">
									<a-form-model :ref="'v' + i" :model="v" :rules="validatorRules">
										<a-form-model-item :label="v.customizeItemName" :labelCol="labelCol" :prop="v.isRequiredItems ? 'itemValue' : null"
											:required="v.isRequiredItems" :wrapperCol="wrapperCol">
											<j-remarks-component v-model="v.itemValue" :max-length="256" :readOnly="formDisabled"
												style="width: 100%" />
										</a-form-model-item>
									</a-form-model>
								</a-col>
							</a-row>
						</a-collapse-panel>
					</a-collapse>

					<!-- 上传附件区域 -->
					<a-collapse v-show="model.id && model.isExamine == '1'" :bordered="false" default-active-key="5">
						<a-collapse-panel key="5" header="上传附件区域">
							<a-row>
								<a-col :span="24" style="height:auto">
									<a-form-model-item label="上传附件" prop="attachmentsFileName">
										<j-upload v-model="otherName" :buttonVisible="true" :keyboard="false"
											:maskClosable="false" bizPath="exportOrder" style="margin-bottom: -20px;" @change="changeFile()" />

									</a-form-model-item>

								</a-col>

							</a-row>
						</a-collapse-panel>


					</a-collapse>

					<!--					归并关系-->
					<a-collapse default-active-key="6" :bordered="false" v-show="model.id">
						<a-collapse-panel key="6" header="归并关系">
							<a-row>
								<a-button class="editable-add-btn" size="small" style="margin-top: -8px" type="primary"
									@click="handleAdd">
									新增
								</a-button>
								<a-table ref="table2" size="small" :scroll="{ x: true }" bordered rowKey="id" :columns="columnsMerge"
									:dataSource="data" :loading="loadingMerge" class="j-table-force-nowrap">
									<template v-for="col in ['mergePn', 'model', 'code']" :slot="col" slot-scope="text, record, index">
										<div :key="col">
											<a-input v-if="record.editable" v-model="text" size="small" style="margin: -5px 0"
												@change="e => handleChange(e.target.value, record.key, col)" />
											<template v-else>
												{{ text }}
											</template>
										</div>
									</template>
									<template slot="operation" slot-scope="text, record, index">
										<div class="editable-row-operations">
											<span v-if="record.editable">
												<a @click="() => saveMerge(record)">保存</a>

											</span>
											<span v-else>
												<a :disabled="editingKey !== ''" style="margin-right: 10px"
													@click="() => editMerge(record)">编辑</a>
												<a-popconfirm :disabled="editingKey !== ''" title="确认删除吗?" @confirm="() => cancelMerge(record)">
													<a>删除</a>
												</a-popconfirm>
											</span>
										</div>
									</template>

								</a-table>
							</a-row>
						</a-collapse-panel>
					</a-collapse>

					<a-collapse default-active-key="4" :bordered="false" v-show="model.id">
						<a-collapse-panel key="4" header="历史变更记录">
							<a-row>
								<a-table ref="table" size="small" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
									:dataSource="dataSource" :loading="loading" :pagination="false" class="j-table-force-nowrap">
								</a-table>
							</a-row>
						</a-collapse-panel>

					</a-collapse>
					<!-- <a-collapse class="collapse-box" default-active-key="4" :bordered="false">
            <a-collapse-panel key="4" header="装箱信息">
              <a-row>
                <a-col :span="8">
                  <a-form-model-item
                    label="毛重（kg）"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    prop="grossWeight"
                    ref="grossWeight"
                  >
                    <a-input
                      v-model="model.grossWeight"
                      placeholder="请输入毛重（kg）"
                      style="width: 100%"
                      :max-length="11"
                      @change="weightChange"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="净重（kg）"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    prop="netWeight"
                    ref="netWeight"
                  >
                    <a-input
                      v-model="model.netWeight"
                      placeholder="请输入净重（kg）"
                      style="width: 100%"
                      :max-length="11"
                      @change="weightChange"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item label="体积（m3）" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="volume">
                    <a-input
                      v-model="model.volume"
                      placeholder="请输入体积（m3）"
                      style="width: 100%"
                      :max-length="11"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-collapse-panel>
          </a-collapse> -->
				</a-form-model>
			</j-form-container>
		</a-spin>

		<hsmodel-edit ref="hsmodelRef" :hscode="model.customsCodeInfoCode" :hsmodel="model.customsDeclarationElements" @change="handleValue"
			@keyFromPromise="hsmodelOK" />

<!--		<dec-hsmodel ref="decHsmodel" :show='show.decHsmodel' v-model="record.hsmodel"-->
<!--								 :txtrequestCertType.sync='record' @keyFromPromise="keyFromPromise"/>-->

	</div>
</template>

<script>
import { getAction, httpAction, postAction } from '@/api/manage'
import { generateTransactionNo } from '@/utils/util'
// import RemarksComponent from '@views/remarks/RemarksComponent.vue'
import JPopup from '@/components/jeecg/JPopup'
import Vue from 'vue'
import { TENANT_ID } from '@/store/mutation-types'
import { EmptyIcon } from '@/mixins/JeecgListMixin'
import JSelectMultiple from '@comp/jeecg/JSelectMultiple.vue'
import HsmodelEdit from '@/views/productInfo/components/HsmodelEdit.vue'
import DecHsmodel from '@views/Business/component/modal/dec-hsmodel'

export default {
	name: 'ProductInfoForm',
	components: { JPopup, JSelectMultiple, HsmodelEdit, DecHsmodel },
	mixins: [EmptyIcon],
	props: {
		//表单禁用
		disabled: {
			type: Boolean,
			default: false,
			required: false,
		}
	},
	data() {
		return {
			cacheData: [],
			loadingMerge: false,
			otherName: '',
			isSFJ: false,
			columns: [
				{
					title: '变更内容',
					align: 'center',
					sorter: false,
					width: 100,
					dataIndex: 'logContent',
				},
				{
					title: '更新人',
					align: 'center',
					sorter: false,
					width: 80,
					dataIndex: 'createBy',
				},
				{
					title: '更新时间',
					align: 'center',
					sorter: false,
					width: 100,
					dataIndex: 'createTime',
				},
			],
			columnsMerge: [
				{
					title: '料号',
					align: 'center',
					sorter: false,
					dataIndex: 'mergePn',
					scopedSlots: { customRender: 'mergePn' },
				},
				{
					title: '规格型号',
					align: 'center',
					sorter: false,
					dataIndex: 'model',
					scopedSlots: { customRender: 'model' },
				},
				{
					title: '代码',
					align: 'center',
					sorter: false,
					dataIndex: 'code',
					scopedSlots: { customRender: 'code' },
				},
				{
					title: '操作',
					dataIndex: 'operation',
					scopedSlots: { customRender: 'operation' },
				},
			],
			editingKey: '',
			dataSource: [],
			data: [],
			loading: false,
			jSelectMultipleOptions: [
				{ text: '字符串', value: 'String' },
				{ text: '整数型', value: 'Integer' },
				{ text: '浮点型', value: 'Double' },
				{ text: '布尔型', value: 'Boolean' }
			],
			timer: null,
			saveTime: null,
			form: this.$form.createForm(this),
			dictCodeCustomerName: '',
			selectValue: '',
			isMyInfo: false,
			model: {
				originCountry: '',
				pn: '',
				attachmentList: []
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 8 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 4 },
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 20 },
			},
			confirmLoading: false,
			validatorRules: {
				itemValue: [{ required: true, message: '请输入!' }],
				chineseName: [{ required: true, message: '请输入中文品名!' }],
				pn: [{ required: true, message: '请输入物料号!' }],
				customsCodeInfoId: [{ required: true, message: '请输入海关编码!' }],
				englishDescribe: [{ required: false }, { pattern: /^[^(\u4E00-\u9FA5)]+$/, message: '请输入正确的英文描述!' }],
				barCode: [{ required: false }, { pattern: /^[A-Z|a-z|0-9]+$/, message: '请输入正确的条形码!' }],
				salesGuidancePrice: [
					{ required: false },
					{
						pattern: /^[0-9]{0,14}(\.[0-9]{0,2})?$/,
						message: '仅支持14位整数及2位小数!',
					},
				],
				grossWeight: [
					{ required: false },
					{
						pattern: /^[0-9]{0,6}(\.[0-9]{0,4})?$/,
						message: '仅支持6位整数及4位小数!',
					},
					{
						validator: this.grossWeightValidator,
					},
				],
				netWeight: [
					{ required: false },
					{
						pattern: /^[0-9]{0,6}(\.[0-9]{0,4})?$/,
						message: '仅支持6位整数及4位小数!',
					},
					{
						validator: this.netWeightValidator,
					},
				],
				volume: [
					{ required: false },
					{
						pattern: /^[0-9]{0,6}(\.[0-9]{0,4})?$/,
						message: '仅支持6位整数及4位小数!',
					},
				],
			},
			url: {
				add: '/product/productInfo/add',
				edit: '/product/productInfo/edit',
				queryById: '/product/productInfo/queryById',
			},
			list: [],
			productInfo: {},
			deleteItemList: [],
			oofProductCategoryInfoId: '',
			oofProductId: '',
			condition: '',
			isSave: false, //是否是自动保存
			showTime: false, // 显示时间,
			addFlag: false, //是否是新增
			show: {
				decHsmodel: false
			},
			record: {
				// 商品编码 税号
				hscode: '',
				// 规格型号 申报要素
				hsmodel: ''
			}
		}
	},
	computed: {
		formDisabled() {
			return this.disabled
		},
	},
	watch: {
		// 监听model，随时赋值给record
		model() {
			this.record = {
				hscode: this.model.customsCodeInfoId,
				hsmodel: this.model.customsDeclarationElements
			}
		}
	},
	created() {
		// 查询当前租户id
		let tenantId = Vue.ls.get(TENANT_ID)
		if (!tenantId) {
			tenantId = 0
		}
		//备份model原始值
		this.modelDefault = JSON.parse(JSON.stringify(this.model))
		this.dictCodeCustomerName = 'erp_countries,name,code,isenabled=1'
		const conditionObj = {
			tenant_id: tenantId,
			del_flag: 0
		}
		this.condition = JSON.stringify(conditionObj);
		this.getTagsList()
	},
	mounted() {
		this.timer = setInterval(this.handleTreeSelectEmptyIcon, 100)
	},
	updated() { },
	destroyed() {
		clearInterval(this.timer)
		clearInterval(this.saveTime)
	},
	methods: {
		handleAdd() {
			console.log(this.data)
			const newData = {
				key: this.data ? this.data.length + 1 : 1,
				editable: true,
				mergePn: '',
				model: '',
				code: ''
			};
			this.data.unshift(newData)
		},
		handleChange(value, key, column) {
			const newData = [...this.data];
			const target = newData.find(item => key === item.key);
			if (target) {
				target[column] = value;
				this.data = newData;
			}
		},
		editMerge(record) {
			record.editable = true
			this.$forceUpdate()
			// const newData = [...this.data];
			// const target = newData.find(item => key === item.key);
			// console.log(target)
			// this.editingKey = key;
			// if (target) {
			// 	target.editable = true;
			// 	this.data = newData;
			// }
		},
		saveMerge(record) {
			if (!record.mergePn) {
				this.$message.error('请输入料号后进行保存')
				return
			}
			record.editable = false
			this.$forceUpdate()
			// const newData = [...this.data];
			// const newCacheData = [...this.cacheData];
			// const target = newData.find(item => key === item.key);
			// const targetCache = newCacheData.find(item => key === item.key);
			// if (target && targetCache) {
			// 	delete target.editable;
			// 	this.data = newData;
			// 	Object.assign(targetCache, target);
			// 	this.cacheData = newCacheData;
			// }
			// this.editingKey = '';
		},
		cancelMerge(record) {
			if (record.id) {
				this.data = this.data.filter(i => i.id != record.id)

			} else {
				this.data = this.data.filter(i => i.key != record.key)
			}
			this.$forceUpdate()
			//
			// const newData = [...this.data];
			// const target = newData.find(item => key === item.key);
			// this.editingKey = '';
			// if (target) {
			// 	Object.assign(target, this.cacheData.find(item => key === item.key));
			// 	delete target.editable;
			// 	this.data = newData;
			// }
		},

		changeFile() {
			let attachment = {
				attachmentsFileName: this.otherName,
				attachmentsFileType: 'product',
			}

			this.model.attachmentList.push(attachment)
		},
		/**
		 * 申报要素确认事件
		 */
		hsmodelOK(arg) {
			console.log(arg)
			this.$refs.hsmodelRef.handleCancel()
			this.model.customsDeclarationElements = arg
		},
		/**
		 * 光标落点 模态窗回调
		 * @param e
		 * @returns {number}
		 */
		keyFromPromise(e) {
			console.log(e)
			console.log(this.record)
			// if (this.record.hsmodel) {
			// 	this.model.customsDeclarationElements = this.record.hsmodel
			// }
			// 使用 this.form.setFieldsValue 更新表单值
			this.form.setFieldsValue({
				customsDeclarationElements: this.record.hsmodel
			});
		},
		showModal() {
			this.$refs.hsmodelRef.show()
		},
		// showModal(){
		// 	this.$refs.decHsmodel.title = '编辑申报要素信息'
		// 	this.$refs.decHsmodel.disabled = false
		// 	this.show.decHsmodel = !this.show.decHsmodel
		// },
		getTagsList() {
			getAction('/productCategoryInfo/productCategoryInfo/listTagsPageList', {
				pageNo: 1,
				pageSize: 9999999
			}).then((res) => {
				if (res.result && res.result.records) {
					let dataSource = []
					res.result.records.forEach(item => {
						dataSource.push({ text: item.productTagName, value: item.id })
					})
					this.jSelectMultipleOptions = dataSource
				}
			})
		},
		handleTimerDescroyed() {
			clearInterval(this.timer)
		},
		handleTreeSelectEmptyIcon() {
			let imageElement = document.getElementsByClassName('ant-empty-image')
			let descriptionElement = document.getElementsByClassName('ant-empty-description')
			if (imageElement.length > 0) {
				for (let i = 0; i < imageElement.length; i++) {
					imageElement[i].innerHTML = this.emptyIcon
				}
			}
			if (descriptionElement.length > 0) {
				for (let i = 0; i < descriptionElement.length; i++) {
					descriptionElement[i].innerHTML = this.emptyContent
				}
			}
		},
		onChangeProductCategoryInfoId() {
			if (this.model.productCategoryInfoId != undefined) {
				this.isMyInfo = true
				this.getCustomizeItemInfo()
			} else {
				this.isMyInfo = false
				this.list = []
			}
		},
		getId() {
			if (this.oofProductId == '') {
				this.getCustomizeItemInfo()
			}
			if (
				this.model.productCategoryInfoId != undefined &&
				this.oofProductId &&
				this.oofProductCategoryInfoId != this.model.productCategoryInfoId
			) {
				this.getCustomizeItemInfo()
			}
			if (this.oofProductCategoryInfoId != undefined) {
				this.isMyInfo = true
				this.getCustomizeItemInfoValue()
			}
		},
		setleValue(e) {
			this.model.salesGuidancePrice = e.target.value
		},
		setleValue2(e) {
			this.model.productSpecificationModel = e.target.value
		},
		handleValue(e) {
			if (this.model.customsCodeInfoId && e.target) {
				this.$nextTick(() => {
					setTimeout(() => {
						this.form.setFieldsValue({
							[e.target.id]: e.target.value,
						})
						this.model[e.target.id] = e.target.value
					})
				})
			} else if (this.model.customsCodeInfoId) {
				this.model.customsDeclarationElements = e
				this.form.setFieldsValue({
					customsDeclarationElements: e
				})
				//以上else if分支解释：
				//specificationModel使用JRemarksComponent组件，所以没有e.target.id，e就是他的值，因此专门用else if分支处理这个字段
			} else {
				this.$message.warning('您需要先选择海关编码！')
			}
		},
		popupCallback(row) {
			console.log(row)
			this.form.setFieldsValue(row)
			for (let key in row) {
				if (key !== 'customsDeclarationElements') {
					this.model[key] = row[key]
				}
			}
			this.isSFJ = this.containsCharacter(row.iaqcategory)
		},
		containsCharacter(str) {
			if (this.isEmpty(str)) {
				return false
			}
			const charactersToCheck = ['M', 'N', 'P', 'Q', 'R', 'S', 'L']
			for (const char of str) {
				if (charactersToCheck.includes(char)) {
					return true
				}
			}
			return false
		},
		//没有字段值
		getCustomizeItemInfo() {
			let params = Object.assign({}) //查询条件
			params.id = this.model.productCategoryInfoId
			getAction('/productCategoryInfo/productCategoryInfo/queryCustomizeItem', params)
				.then((res) => {
					if (res.success) {
						if (res.result[0] == null) {
							this.isMyInfo = false
						} else {
							this.list = res.result
							for (let i = 0; i < this.list.length; i++) {
								if (this.list[i].isRequiredItems == 1) {
									this.list[i].isRequiredItems = true
								} else {
									this.list[i].isRequiredItems = false
								}
							}
						}
					}
				})
				.finally(() => { })
		},
		//有字段值
		getCustomizeItemInfoValue() {
			let params = Object.assign({}) //查询条件
			params.id = this.oofProductCategoryInfoId
			params.productId = this.oofProductId
			getAction('/productCategoryInfo/productCategoryInfo/queryCustomizeItem', params)
				.then((res) => {
					if (res.success) {
						if (res.result[0] == null) {
							this.isMyInfo = false
						} else {
							this.list = res.result
							this.deleteItemList = res.result
							for (let i = 0; i < this.list.length; i++) {
								if (this.list[i].isRequiredItems == 1) {
									this.list[i].isRequiredItems = true
								} else {
									this.list[i].isRequiredItems = false
								}
							}
						}
					}
				})
				.finally(() => { })
		},
		//编辑时类别改变才会调用此方法
		addItem() {
			if (this.isMyInfo == false) {
				this.list = []
			}
			let params = Object.assign({}) //查询条件

			params.productCategoryInfoId = this.model.productCategoryInfoId
			params.id = this.oofProductId
			params.itemList = this.list
			params.deleteItemList = this.deleteItemList

			postAction('/product/productInfo/addItem', params)
				.then((res) => {
					if (res.success) {
						console.log('成功')
					}
				})
				.finally(() => { })
		},
		handleElements(data) {
			this.model.customsDeclarationElements = data
		},
		callback(key) {
			console.log(key)
		},
		//拷贝新增
		handleProductCopy(record) {
			this.model = Object.assign({}, record)
			this.form.setFieldsValue({
				customsCodeInfoId: record.customsCodeInfoId,
				addedTaxRate: record.addedTaxRate,
				taxRebateRate: record.taxRebateRate,
				customsDeclarationElements: record.customsDeclarationElements,
				legalUnit: record.legalUnit,
				secondUnit: record.secondUnit,
				salesGuidancePrice: record.salesGuidancePrice,
				impcustomrate: record.impcustomrate,
				productSpecificationModel: record.productSpecificationModel,
				customsCodeInfoCode: record.customsCodeInfoCode,
				pn: generateTransactionNo('WL')
			})
			this.isSFJ = this.containsCharacter(this.model.iaqcategory)
			this.oofProductCategoryInfoId = record.productCategoryInfoId
			this.oofProductId = record.id
			this.getId()
			this.model.pn = generateTransactionNo('WL')
			this.model.id = ''
			this.model.createBy = ''
			this.model.createTime = null
			this.model.updateBy = ''
			this.model.updateTime = null
		},
		add() {
			this.addFlag = true
			this.edit(this.modelDefault)
		},
		addFromOrder(record) {
			this.addFlag = true
			this.model = Object.assign({}, record)
			this.model.fromOrder = true
			this.form.setFieldsValue({
				customsCodeInfoId: record.customsCodeInfoId,
				addedTaxRate: record.addedTaxRate,
				taxRebateRate: record.taxRebateRate,
				customsDeclarationElements: record.customsDeclarationElements,
				legalUnit: record.legalUnit,
				secondUnit: record.secondUnit,
				salesGuidancePrice: record.salesGuidancePrice,
				impcustomrate: record.impcustomrate,
				productSpecificationModel: record.productSpecificationModel,
				customsCodeInfoCode: record.customsCodeInfoCode,
				tariffsName: record.tariffsName,
				hsname: record.hsname,
				iaqcategory: record.iaqcategory,
				monitorcondition: record.monitorcondition,
			})
			this.isSFJ = this.containsCharacter(this.model.iaqcategory)
			this.oofProductCategoryInfoId = record.productCategoryInfoId
			this.oofProductId = record.id
			this.getId()
			this.visible = true
			this.$nextTick(() => {
				this.productInfo = record
				this.model.customsDeclarationElements = record.customsDeclarationElements
			})
		},
		edit(record) {
			this.model = Object.assign({}, record)
			//加载归并关系
			if (record.productMergeList) {
				this.data = record.productMergeList
				for (let o of this.data) {
					o.editable = false
				}
				this.cacheData = record.productMergeList

			}
			if (this.model.attachmentList == undefined || this.model.attachmentList == null) {
				this.model.attachmentList = []
			}
			let attachments = this.model.attachmentList
			if (attachments != null && attachments.length > 0) {
				for (let i = 0; i < attachments.length; i++) {
					this.otherName = attachments[i].attachmentsFileName
				}
			}


			// this.model.attachmentList = []
			this.form.setFieldsValue({
				customsCodeInfoId: record.customsCodeInfoId,
				addedTaxRate: record.addedTaxRate,
				taxRebateRate: record.taxRebateRate,
				customsDeclarationElements: record.customsDeclarationElements,
				legalUnit: record.legalUnit,
				secondUnit: record.secondUnit,
				salesGuidancePrice: record.salesGuidancePrice,
				impcustomrate: record.impcustomrate,
				productSpecificationModel: record.productSpecificationModel,
				customsCodeInfoCode: record.customsCodeInfoCode,
				tariffsName: record.tariffsName,
				hsname: record.hsname,
				iaqcategory: record.iaqcategory,
				monitorcondition: record.monitorcondition,
				// pn: record.pn ? record.pn : generateTransactionNo('WL')
			})
			this.isSFJ = this.containsCharacter(this.model.iaqcategory)
			// this.model.pn = record.pn ? record.pn : generateTransactionNo('WL')
			this.oofProductCategoryInfoId = record.productCategoryInfoId
			this.oofProductId = record.id
			this.getId()
			this.visible = true
			this.$nextTick(() => {
				this.productInfo = record
				this.model.customsDeclarationElements = record.customsDeclarationElements
			})
			// if (!this.addFlag && !this.formDisabled) {
			// const duration = 1000 * 60 * 3 // 设置时长3min
			// this.saveTime = setInterval(this.autoSave,duration)
			// }
			//加载历史变更记录
			this.loading = true
			var param = {}
			param.sourceId = this.model.id
			param.pageNo = 1
			param.pageSize = 9999
			getAction("/operationLogInfo/operationLogInfo/list", param)
				.then(res => {
					if (res.success) {
						this.dataSource = res.result.records || res.result

					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		grossWeightValidator(rule, value, callback) {
			if (value == '' || value == undefined || value == null) {
				callback()
			} else {
				let netWeight = '0'
				if (this.model.netWeight) {
					netWeight = this.model.netWeight
				}
				if (parseInt(value) < parseInt(netWeight)) {
					callback(new Error('毛重不能小于净重！'))
				} else {
					callback()
				}
			}
		},
		netWeightValidator(rule, value, callback) {
			if (value == '' || value == undefined || value == null) {
				callback()
			} else {
				let grossWeight = '1000000'
				if (this.model.grossWeight) {
					grossWeight = this.model.grossWeight
				}
				if (parseInt(value) > parseInt(grossWeight)) {
					callback(new Error('净重不能大于毛重！'))
				} else {
					callback()
				}
			}
		},
		weightChange() {
			// this.$refs.grossWeight.validate()
			this.$refs.netWeight.validate()
		},
		getDateTimeToString() {
			let deate = ''
			const date_ = new Date()
			const year = date_.getFullYear()
			let month = date_.getMonth() + 1
			let day = date_.getDate()
			if (month < 10) month = '0' + month
			if (day < 10) day = '0' + day
			let hours = date_.getHours()
			let mins = date_.getMinutes()
			let secs = date_.getSeconds()
			const msecs = date_.getMilliseconds()
			if (hours < 10) hours = '0' + hours
			if (mins < 10) mins = '0' + mins
			if (secs < 10) secs = '0' + secs
			if (msecs < 10) secs = '0' + msecs
			deate = year + '-' + month + '-' + day + ' ' + hours + ':' + mins
			return deate
		},
		//自动保存
		autoSave() {
			this.isSave = true
			this.submitForm()
		},
		//HS编码归类
		codeClassification() { },
		submitForm(value) {
			const that = this
			let itemList = []
			this.form.validateFields((errors, values) => {
				this.$refs.form.validate((valid) => {
					if (valid) {
						let allValidate = []
						that.list.forEach((item, index) => {
							//有多个输入框需校验，待都校验成功后再进行别的操作，所以需要使用Promise
							allValidate.push(
								new Promise((rev, rej) => {
									//划重点：This.$refs['item'+index][0]
									that.$refs['v' + index][0].validate((valid) => {
										if (valid) {
											rev()
										} else {
											rej()
										}
									})
								})
							)
						})
						if (errors) return
						Promise.all(allValidate)
							.then(() => {
								that.confirmLoading = true
								let httpurl = ''
								let method = ''
								if (!this.model.id) {
									httpurl += this.url.add
									method = 'post'
								} else {
									httpurl += this.url.edit
									method = 'post'
								}

								itemList = that.list
								that.model.itemList = itemList
								//赋值合并关系
								that.model.productMergeList = that.data
								if (value && value.autoAudit && value.autoAudit == '1') {
									that.model.isExamine = '1'
								} else {
									that.model.isExamine = '0'
								}
								httpAction(httpurl, this.model, method)
									.then((res) => {
										if (res.success) {

											if (!this.isSave) {
												that.$message.success(res.message)
												that.$emit('ok')
											}
											if (this.isSave) {
												that.$message.success(this.getDateTimeToString() + ' 自动保存成功！')
												that.showTime = true

												//自动保存后把updateTime更新掉
												this.model.updateTime = res.result.updateTime
												for (let i = 0; i < that.list.length; i++) {
													for (let j = 0; j < res.result.proListAuto.length; j++) {
														if (that.list[i].goodsId == res.result.proListAuto[j].goodsId) {
															that.list[i].goodsUpdateTime = res.result.proListAuto[j].goodsUpdateTime
														}
													}
												}
												that.$emit('save')
											}

										} else {
											if (this.isSave) {
												that.$message.warning(that.getDateTimeToString() + ' 自动保存失败！')
											} else {
												that.$message.warning(res.message)
											}
										}
									})
									.finally(() => {
										that.confirmLoading = false
										that.isSave = false
									})
							})
							.catch(() => { })
					}
				})
				if (
					this.model.productCategoryInfoId != undefined &&
					this.oofProductCategoryInfoId != this.model.productCategoryInfoId &&
					this.oofProductId != ''
				) {
					this.addItem()
				}
			})
			// 触发表单验证
			clearInterval(this.timer)
		},
	},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';

.productPicture {
	margin-bottom: 50px;
}

/deep/ .ant-select-dropdown.ant-select-tree-dropdown.ant-select-dropdown--single.ant-select-dropdown-placement-bottomLeft {
	min-height: 200px;
}

/deep/ .ant-select-dropdown.ant-select-tree-dropdown.ant-select-dropdown--single.ant-select-dropdown-placement-topLeft {
	min-height: 200px;
}

/deep/ .ant-select-dropdown.ant-select-tree-dropdown.ant-select-dropdown--single.ant-select-dropdown-placement-bottomRight {
	min-height: 200px;
}

/deep/ .ant-select-dropdown.ant-select-tree-dropdown.ant-select-dropdown--single.ant-select-dropdown-placement-topRight {
	min-height: 200px;
}

.ant-col.ant-col-16 {
	height: 52px;
}
</style>
