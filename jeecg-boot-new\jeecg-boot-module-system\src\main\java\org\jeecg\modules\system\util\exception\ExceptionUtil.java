package org.jeecg.modules.system.util.exception;

import org.jeecg.common.api.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;

/**
 * 保存异常堆栈信息
 * -- 两种方式 Throwable和Exception
 *
 * <AUTHOR>
 * @date 2019/9/6 9:56
 * @since 1.0.0
 */
public class ExceptionUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExceptionUtil.class);

    /**
     * 异常堆栈信息保存到日志中
     *
     * @param ex
     */
    public static void getFullStackTrace(Exception ex) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        ex.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try {
            out.close();
        } catch (Exception e) {
        }
        ex.printStackTrace();
        logger.error(ret);
    }

    /**
     * 异常堆栈信息保存到日志中，并保存消息记录
     *
     * @param ex
     * @param msg
     */
    public static void getFullStackTrace(Exception ex, String msg) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        ex.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try {
            out.close();
        } catch (Exception e) {
        }
        ex.printStackTrace();
        logger.error("出现异常==>: " + msg + " ==> \n" + ret);
//        logger.error(ret);
    }

    /**
     * 参数是Throwable
     *
     * @param e
     * @return
     */
    public static void getFullStackTrace(Throwable e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw, true);
        try {
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
            logger.error(sw.toString());
        } finally {
            pw.close();
        }
    }

    /**
     * 转换msg消息
     *
     * @param msg
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/7/19 11:36
     */
    public static String ResExHandle(String msg) {
        // 如果有常规异常，返回
        for (GlobalErrorCodeEnum global : GlobalErrorCodeEnum.values()) {
            if (msg.toUpperCase().contains(global.toString().replaceAll("_", ""))) {
                return global.getMsg();
            }
        }
        // 不是常规异常的话，下方匹配...
        if (msg.contains("Data too long for column")) {
            String field = msg.replaceAll("[\\s\\S]+Data too long for column '(.+)' at[\\s\\S]+", "$1");
            return ("数据库异常，字段长度超过限制：" + field);
        } else if (msg.contains("Unknown column")) {
            String field = msg.replaceAll("[\\s\\S]+Unknown column '(.+)' in[\\s\\S]+", "$1");
            return ("数据库异常，未知的字段：" + field);
        } else if (msg.contains("doesn't have a default value")) {
            String field = msg.replaceAll("[\\s\\S]+Field '(.+)' doesn't have a default value[\\s\\S]+", "$1");
            return ("数据库异常，此字段必须有值：" + field);
        } else if (msg.contains("Data truncated for column")) {
            String field = msg.replaceAll("[\\s\\S]+Data truncated for column '(.+)' at[\\s\\S]+", "$1");
            return ("此字段插入的数据不合法：" + field);
        } else if (msg.contains("TooManyResultsException")) {
            return ("selectOne方法根据查询条件从数据库找到两条或多条相同的数据，请检查！");
        } else if (msg.contains("Duplicate entry")) {
            String field = msg.replaceAll("[\\s\\S]+Duplicate entry '(.+)' for key '(.+)';[\\s\\S]+", "$1,$2");
            if (field != null && !"".equals(field)) {
                if (field.split(",").length == 2) {
                    return ("唯一索引：存在重复值（" + field.split(",")[1] + ":" + field.split(",")[0] + "）");
                } else {
                    return ("唯一索引：存在重复值！");
                }
            }
        } else if (msg.contains("RuntimeException")) {
            return (msg.replaceAll("java.lang.RuntimeException: ", ""));
        } else if (msg.contains("get global lock fail")) {
            return ("Seata分布式事务异常：获取全局事务锁失败，二阶段决策提交失败！");
        } else if (msg.contains("Out of range value for column")) {
            String field = msg.replaceAll("[\\s\\S]+Out of range value for column '(.+)' at[\\s\\S]+", "$1");
            return ("数据库异常，此字段超出范围值：" + field);
        } else if (msg.contains("syntax error,except start with")) {
            return ("请求参数解析异常，请检查！");
        } else if (msg.contains("argument type mismatch")) {
            return ("参数类型不匹配！");
        } else if (msg.contains("YmException")) {
            return (msg);
        } else if ((msg.contains("org.mybatis.spring.MyBatisSystemException"))) {
            return ("与数据库交互出现异常，请联系管理员！");
        } else if ((msg.contains("Column count doesn't match value"))) {
            return ("与数据库交互出现异常，请联系管理员！");
        } else if (msg.contains("Incorrect datetime value")) {
            String field = msg.replaceAll("[\\s\\S]+Incorrect datetime value: '(.+)' for column '(.+)' at[\\s\\S]+", "$1,$2");
            if (!"".equals(field)) {
                if (field.split(",").length == 2) {
                    return ("保存时出现异常，日期格式错误：" + field.split(",")[1] + ":" + field.split(",")[0] + "）");
                } else {
                    return ("日期格式错误，保存数据库失败！");
                }
            }
        } else if (msg.contains("Unparseable date")) {
            String field = msg.replaceAll("[\\s\\S]+Unparseable date: (.+)[\\s\\S]+", "$1");
            return ("解析日期失败！date:" + field);
        }
        if (msg.length() > 500) {
            msg = "出现异常：" + msg.substring(0, 500);
        }
        return msg;
    }

    /**
     * 封装异常类
     *
     * @param e
     * @return
     */
    public static Result<?> resExHandle(Throwable e) {
        Result<?>  ymMsg = new Result<>();
        ymMsg.setSuccess(false);
        String eStr = e.toString();
        // 如果有常规异常，返回
        for (GlobalErrorCodeEnum global : GlobalErrorCodeEnum.values()) {
            if (eStr.toUpperCase().contains(global.toString().replaceAll("_", ""))) {
                ymMsg.setCode(global.getCode());
                ymMsg.setMessage(global.getMsg());
                if (!eStr.contains("java.lang.String cannot be cast to com.yorma.entity.YmMsg")) {
                    return ymMsg;
                }
            }
        }
        // 不是常规异常的话，下方匹配...
        if (eStr.contains("Data too long for column")) {
            String field = eStr.replaceAll("[\\s\\S]+Data too long for column '(.+)' at[\\s\\S]+", "$1");
            ymMsg.setCode(400);
            ymMsg.setMessage("数据库异常，字段长度超过限制：" + field);
        } else if (eStr.contains("Unknown column")) {
            String field = eStr.replaceAll("[\\s\\S]+Unknown column '(.+)' in[\\s\\S]+", "$1");
            ymMsg.setCode(400);
            ymMsg.setMessage("数据库异常，未知的字段：" + field);
        } else if (eStr.contains("doesn't have a default value")) {
            String field = eStr.replaceAll("[\\s\\S]+Field '(.+)' doesn't have a default value[\\s\\S]+", "$1");
            ymMsg.setCode(400);
            ymMsg.setMessage("数据库异常，此字段必须有值：" + field);
        } else if (eStr.contains("Data truncated for column")) {
            String field = eStr.replaceAll("[\\s\\S]+Data truncated for column '(.+)' at[\\s\\S]+", "$1");
            ymMsg.setCode(400);
            ymMsg.setMessage("此字段插入的数据不合法：" + field);
        } else if (eStr.contains("TooManyResultsException")) {
            ymMsg.setCode(400);
            ymMsg.setMessage("找到两条相同的数据，请联系管理员！");
        } else if (eStr.contains("Duplicate entry")) {
            String field = eStr.replaceAll("[\\s\\S]+Duplicate entry '(.+)' for key '(.+)';[\\s\\S]+", "$1,$2");
            if (field != null && !"".equals(field)) {
                if (field.split(",").length == 2) {
                    ymMsg.setMessage("唯一索引：存在重复值（" + field.split(",")[1] + ":" + field.split(",")[0] + "）");
                } else {
                    ymMsg.setMessage("唯一索引：存在重复值！");
                }
            }
            ymMsg.setCode(400);
        } else if (eStr.contains("RuntimeException")) {
            ymMsg.setMessage(eStr.replaceAll("java.lang.RuntimeException: ", ""));
            ymMsg.setCode(400);
        } else if (eStr.contains("get global lock fail")) {
            ymMsg.setCode(400);
            ymMsg.setMessage("Seata分布式事务异常：获取全局事务锁失败，二阶段决策提交失败！");
        } else if (eStr.contains("Out of range value for column")) {
            String field = eStr.replaceAll("[\\s\\S]+Out of range value for column '(.+)' at[\\s\\S]+", "$1");
            ymMsg.setCode(400);
            ymMsg.setMessage("数据库异常，此字段超出范围值：" + field);
        } else if (eStr.contains("syntax error,except start with")) {
            ymMsg.setCode(400);
            ymMsg.setMessage("请求参数解析异常，请检查！");
        } else if (eStr.contains("argument type mismatch")) {
            ymMsg.setCode(400);
            ymMsg.setMessage("参数类型不匹配！");
        } else if (eStr.contains("YmException")) {
            ymMsg.setCode(400);
            ymMsg.setMessage(e.getMessage());
        } else if (eStr.contains("java.lang.String cannot be cast to com.yorma.entity.YmMsg")) {
            ymMsg.setCode(400);
            ymMsg.setMessage("调用RPC远程服务失败，请联系服务提供方！");
        }
        // 都没有匹配上，则返回未知异常。
        if (isEmpty(ymMsg.getCode()) || isBlank(ymMsg.getMessage())) {
            ymMsg.setCode(GlobalErrorCodeEnum.UNKNOWN_EXCEPTION.getCode());
            ymMsg.setMessage(GlobalErrorCodeEnum.UNKNOWN_EXCEPTION.getMsg());
        }
        return ymMsg;
    }


}
