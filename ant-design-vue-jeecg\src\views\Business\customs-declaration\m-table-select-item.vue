<template>
    <fragment>
        <td :class="required ||required ==true?'tmpClass-required':tmpClass" :colspan='labelWidth' :title='label'
            class='single-td' v-if='!noLabel'>
            {{ label }} &nbsp;
        </td>
        <td :class='valClass' :colspan='valueWidth' :title='listQuery.name||placeholder'  class='single-td' style='text-align: left'>
            <!--             $event是实参，表示event对象-->
<!--            <a-form-model-item class='alone-input' :prop='iprop' :required='required' has-feedback>-->
                <a-input ref='input' :id="id"  class="ant-input-a" :style="styleSrt" type="text" :disabled='readonly'
                         v-model="listQuery.name" @blur="blur()" @focus="focus($event)" @keyup="getCreated($event)" autoselect
                         @keyup.enter="searchEnter($event)" v-focus="vfocusNum"  autocomplete="off" :placeholder="placeholder"
                         @keydown.down="selectDown()" @keydown.up.prevent="selectUp()"  @mouseover="tooltipHover()" @mouseout="tooltipLeave()">
<!--                    <a-tooltip slot="suffix" style="font-size:11px;" v-if="aIconType===0"-->
<!--                               class=".ant-input-affix-wrapper .ant-input-suffix ">-->
<!--                        <div    class="icons-list" >-->
<!--                            <a-icon   @mouseover="tooltipHover()" @click="clearInput()" style="color:#BFBFBF" type="close-circle"/>-->
<!--&lt;!&ndash;                            <a-icon v-if="aIconType ==1" type="down" style="color:#BFBFBF"/>&ndash;&gt;-->
<!--&lt;!&ndash;                            <a-icon v-if="aIconType ==2" type="up" style="color:#BFBFBF"/>&ndash;&gt;-->

<!--                        </div>-->
<!--                    </a-tooltip>-->
                </a-input>
<!--            </a-form-model-item>-->

            <!-- 这是一个小叉叉，点击它可清除输入框内容 -->
            <!--                <span class="search-reset" @click="clearInput()">&times;</span>-->
            <div ref='searchSelectDom' class="search-select" v-if="show ==true">
                <HappyScroll ref="nav" class=".happy-scroll-container .happy-scroll-content" color="#CDCDCD"
                             size="8" :hide-horizontal='true' resize
                             top>
                    <div class="con" style="width: 100%">

                        <!--样式1-->
                        <div v-if="show ==true&&styleType=='1'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <li
                                :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                {{dataObj.value+'-'+dataObj.title.replace(/[|]/g, "-")}}
                            </li>
                        </div>

                        <!--样式2 加备用代码 但是主代码不能有数字不然不会显示-->
                        <div v-if="show ==true&&styleType=='2'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <div class="search-select-option-div-one">
                                <li
                                    :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                    @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                    {{dataObj.title.replace(/[0-9]+/g," ").replace(/[|]/g, " ")}}
                                </li>
                            </div>
                            <div class="search-select-option-dev-two">
                                <li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    class="search-select-option-li search-select-list"
                                    @mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
                                </li>
                                <!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
                            </div>
                        </div>

                        <!--样式4 类似运输方式那种-->
                        <div v-if="show ==true&&styleType=='4'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <div class="search-select-option-div-one">
                                <li
                                    :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                    @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                    {{dataObj.value + ' ' + dataObj.title.replace(/[|]/g, " ")}}
                                </li>
                            </div>
                            <div class="search-select-option-dev-two">
                                <li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    class="search-select-option-li search-select-list"
                                    @mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
                                </li>
                                <!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
                            </div>
                        </div>
                        <!--样式5 有备用代码的统一都用这个-->
                        <div v-if="show ==true&&styleType=='5'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <div class="search-select-option-div-one">
                                <li
                                    :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                    @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                  {{dataObj.title.split("|")[0]+" "+dataObj.value}}
                                </li>
                            </div>
                            <div class="search-select-option-dev-two">
                                <li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                    @click="selectClick(dataObj.value)"
                                    class="search-select-option-li search-select-list"
                                    @mouseover="selectHover(index)">{{dataObj.title.split("|").length>1?dataObj.title.split("|")[1]:""}}
                                </li>
                                <!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
                            </div>
                        </div>
                        <!--样式3 手动传个 myData-->
                        <div v-if="show ==true&&styleType=='3'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <li
                                :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                {{dataObj.title}}
                            </li>
                            <!--                                </div>-->
                        </div>

                        <!--台账费用选择费用用~~~~~~~~~~~~~~~~~~~~~~~~~~-->
                        <div v-if="show ==true&&styleType=='6'" v-for="(dataObj,index) in myData"
                             class="search-select-option-sum">
                            <li
                                :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option search-select-list" @mouseover="selectHover(index)"
                                @click="selectClick(dataObj.value)" :key="dataObj.value" v-model="val">
                                {{dataObj.text}}
                            </li>
                        </div>

                        <li class="search-select-option search-select-list" v-if="myData.length==0">
                            {{styleType===6?"暂无历史数据":'暂无数据'}}
                        </li>
                    </div>
                </HappyScroll>
            </div>
        </td>
    </fragment>
</template>

<script>
    import {axios} from '@/utils/request'
    import {HappyScroll} from 'vue-happy-scroll'
    import 'vue-happy-scroll/docs/happy-scroll.css'
    import {Fragment} from 'vue-fragment'
    import {ajaxGetDictItems, getDictItemsFromCache,getCurrenciesList} from "@api/api";
    import {getAction} from "@api/manage";

    /**
     * 所有的字典表
     * @type {{customsDict: string, units: string, sysDict: string}}
     */
    const dictTableNames = {
        /**
         * 系统字典表
         */
        sysDict: 'sys_dict',
        /**
         * 海关参数
         */
        customsDict: 'CUSTOMS_DICT',
        /**
         * 计量单位表
         */
        units: 'UNITS',

      erpCustomsPorts:'erp_customs_ports'
    }

    const configMap = [
    ]

    export default {
        name: "xiala",
        components: {
            HappyScroll,
            Fragment,
        },

        data: function () {

            return {
                // 全部的
                allOptions: [],
                myData: [],
                //显示选中
                now: -1,
                //上下键滑动slideB滑动表slideK滑动快
                slideB: 0,
                slideK: 0,
                // styleType: '',
                show: false,
                dataSelectShow: false,
                //focusIndex副本
                focusIndexs: 0,
                //0删除1向下2向上
                aIconType :"",

                listQuery: {

                    page: 1,

                    limit: 20,

                    name: this.searchName

                },
                val: "",
            }

        },
        props: {
            searchUrl: {

                type: String

            },
            /*下拉框宽度*/
            selectWidth: {
                type: Number
            },
            searchName: {// 用户回显内容信息

                type: String

            },

            /**
             * 使用v-model, value双向绑定未处理
             */
            value: {
                type: String,
            },
            /**
             * 异步加载的下拉选项
             */
            dictKey: {
                type: String,
                required: "",
            },
            /**
             * 下拉的样式
             * 1，两种代码 -分隔
             * 2，分行的那种
             */
            styleType: {
                type: Number,
                required: true,
            },
            /**
             * 表名(字典表表名)
             */
            tableName: {
                type: String,
                default: dictTableNames.erpCustomsPorts
            },

            /*原来的*/
            /**
             * 组件id
             */
            id: {
                type: Number,
            },
            /**
             * 前端写死的部分下拉
             */
            options: {
                type: Array,
                default: () => {
                    return []
                }
            },
            blurV: {},
            /**
             * value所在td的class
             */
            valClass: {
                type: String,
            },
            tmpClass:{
                type: String,
            },

            iprop: {
                type: String,
            },
            required: {
                type: Boolean,
                default: false
            },
            vfocusNum: {
                type: Boolean,
            },
            focusIndex: {
                type: Number,
            },

            /**
             * label提示内容
             */
            label: {
                type: String,
            },
            /**
             * 提示内容
             */
            placeholder: {
                type: String,
                default: ''
            },
            /**
             * 选择框占用的单元格, 默认为1
             */
            valueWidth: {
                type: Number,
                default: 1,
            },

            /**
             * label占用的单元格, 默认为1
             */
            labelWidth: {
                type: Number,
                default: 1
            },
            /**
             * 为true则不显示label
             */
            noLabel: {
                type: Boolean,
            },

            /**
             * 禁用
             */
            readonly: {
                type: Boolean
            },
            styleSrt:{
                type: String,
            },
        },
        model: {
            prop: 'value',
            event: 'change',
            blur: "blurv",
        },
        created() {
            this.val = this.value
            //dictKey为空说明是穿list下拉
            if (this.dictKey == "" || this.dictKey == null) {
                this.allOptions = this.options
            }
            this.$nextTick(() => {
                //本地下拉不需要缓存数据
                if (!!this.dictKey) {
                    this.initSelect()
                }
                //提取显示中文
                this.extractName(1)
            })
        },
        methods: {

            async getCreated(event) {

                if (event.keyCode == 38 || event.keyCode == 40 || event.keyCode == 13) {  //向上向下回车
                    return;
                }
                if (event == "" || event == null|| this.listQuery.name=="") {
                    this.val = ''
                    //获取数据
                    this.search(this.listQuery.name)
                    // this.initSelect()
                    // //提取显示中文
                    // this.extractName(1)
                    //下拉样式调整  自定义宽度
                    this.downPullStyle()
                    return
                }

                // this.aIconType = 2
                //拼装查询url
                const surl = this.getSearchUrl()

                //手动传list
                if (this.options.length > 0) {
                    this.allOptions = this.options
                }
                let getLocalData = sessionStorage.getItem(this.dictKey);
              if (getLocalData != 'undefined' && getLocalData){
                let jsonData = JSON.parse(getLocalData);
                if (!!jsonData && jsonData.length > 0) {
                  this.allOptions = jsonData
                }
              }else if (this.allOptions.length == 0) {
                this.allOptions = getDictItemsFromCache(this.dictKey);
              }

                // //传海关代码
                // if (this.allOptions.length == 0) {
                //     const res = await axios.get(surl)
                //     this.allOptions = res.result
                //     let str_jsonData = JSON.stringify(this.allOptions);
                //     sessionStorage.setItem(this.dictKey, str_jsonData);
                // }

                //获取数据
                this.search(this.listQuery.name)
                //下拉样式调整  自定义宽度
                this.downPullStyle()
            },
            /**
             * 下拉样式调整  自定义宽度
             * **/
            downPullStyle(){
                //样式调整  自定义宽度
                this.$nextTick(async () => {
                    this.$refs.searchSelectDom.style.width = this.selectWidth + "%"
                    this.$refs.nav.initSize.width = "100%"
                    if(this.myData.length>0){
                        this.now = 0;
                    }
                })
            },
            //初始化下拉吧所有数据放到缓存
            async initSelect() {
                //拼装查询url
                const surl = this.getSearchUrl()
                let getLocalData = sessionStorage.getItem(this.dictKey);
              if (getLocalData != 'undefined' && getLocalData) {
                let jsonData = JSON.parse(getLocalData);
                this.allOptions = jsonData
              }else if(this.dictKey == 'trading_type'){
                this.allOptions = getDictItemsFromCache(this.dictKey);

              }else if (this.dictKey.indexOf(',')>-1){
								if (this.dictKey.includes('erp_currencies,name,code,currency,1=1')) {
									//根据字典Code, 初始化字典数组
									await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
										if (res.success) {
											if (res.result) {
												let list = []
												for (let dict of res.result) {
													let obj = {
														value: dict.value,
														text: dict.text,
														title: dict.text
													}
													list.push(obj)
												}
												this.allOptions = list

												let str_jsonData = JSON.stringify(this.allOptions)
												await sessionStorage.setItem(this.dictKey, str_jsonData)
											}
										}
									})
								} else {
									await getAction(`/sys/dict/loadDict/${this.dictKey}`, {
										keyword: '',
										pageSize: ''
									}).then(async (res) => {
										if (res.success) {
											let list = []
											for (let dict of res.result) {
												let obj = {
													value: dict.value,
													text: dict.text,
													title: dict.text
												}
												list.push(obj)
											}
											this.allOptions = list

											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									})
								}
              } else if (this.dictKey.includes('GBDQ-DEC')) {
								//根据字典Code, 初始化字典数组
								await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
									if (res.success) {
										if (res.result) {
											let list = res.result
											this.allOptions = list
											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									}
								})
							}
              else  if(this.dictKey.includes('BZDM')){
              	await getCurrenciesList().then(async (res)=>{
									let list=[]
									for (let dict of res.result.list) {
										let obj = {
											value: dict.currency,
											title: dict.name+'|'+dict.code
										}
										list.push(obj)
									}
									this.allOptions = list

									let str_jsonData = JSON.stringify(this.allOptions)
									await sessionStorage.setItem(this.dictKey, str_jsonData)
								})

							}
              else if (surl == ''){
                if (this.dictKey =='SFDZDM'){

                }
                //根据字典Code, 初始化字典数组
                let dictStr = ''
                if (this.dictKey) {
                  let arr = this.dictKey.split(',')
                  if (arr[0].indexOf('where') > 0) {
                    let tbInfo = arr[0].split('where')
                    dictStr = tbInfo[0].trim() + ',' + arr[1] + ',' + arr[2] + ',' + encodeURIComponent(tbInfo[1])
                  } else {
                    dictStr = this.dictKey
                  }
                  let list = [];
                  if (this.dictKey.indexOf(',') == -1) {
                    //优先从缓存中读取字典配置
                    if (getDictItemsFromCache(this.dictKey)) {
                      list = getDictItemsFromCache(this.dictKey)
                      this.allOptions = list;
                      return
                    }
                  }
									await ajaxGetDictItems(dictStr, null)
                      .then((res) => {
                        if (res.success) {
                          for(let dict of res.result){
                            let obj={
                              value: dict.value,
                              text: dict.text,
                              title:dict.text,
                            }
                            list.push(obj);
                          }
                          this.allOptions = list;
                        }
                      })
                }
              }else {
                const res = await axios.get(surl)
                this.allOptions = res.result

                let str_jsonData = JSON.stringify(this.allOptions);
                sessionStorage.setItem(this.dictKey, str_jsonData);
              }
              this.$forceUpdate()

            },
            getSearchUrl() {
                let url = ""
                if (this.dictKey =='GQDM') {
                  url = `/dictionary/erpCustomsPorts/listErpCustomsPorts`
                }
                return url
            },
            endStyle() {
                //解决样式最后一行不全
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByClassName('happy-scroll-container')//获取模块目的设置高度
                if (this.myData.length >= 50) {
                    // dd2[0].style.height = '220px'
                }
                //上下键的滚动
                // dd1[0].style.transform = "translateY(" + 0 + "px)"
            },
            /**
             * 海关参数库中的代码, 查询条件统一处理格式
             * @param config
             * @returns {string}
             */
            dictCodeCustomsDictGen(config) {
                // config为空
                if (!config || !config.dictCode) {
                    return `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,${this.dictKey},P_DICT_CODE`
                }
                return config.dictCode
            },
            // 对比字符串(忽略大小写)
            compareStr(a, b) {
                return a.toLowerCase() === b.toLowerCase()
            },
            // 检索
            search(value) {
                if (!value || !value.trim()) {
                    value = ''
                    this.listQuery.name = ''
                }
                this.initSearch(value)
                // this.solveDefaultValue(value)
                // this.initDomt()
            },
            // 初步检索
            initSearch(value) {
							console.log(value)
                if (value == "") {
									console.log("总谱这里。。。")
                    this.myData = this.allOptions.slice(0, 200)
                    //选中的元素一直显示
                    this.pitchShow()
                    this.show = true;
                    return
                }
                if (this.allOptions.length <= 200) {
                    this.myData = this.allOptions.filter(option => this.optionContain(option, value))
                    //选中的元素一直显示
                    this.pitchShow()
                    this.show = true;
                    return
                }
                let options = []
                for (let option of this.allOptions) {
                    if (this.optionContain(option, value)) {
                        options.push(option)
                        if (options.length === 50) {
                            break
                        }
                    }
                }
                if (options.length == 1) {
                    this.now = 0
                }
                this.myData = options
                //选中的元素一直显示
                this.pitchShow()
                this.show = true;

            },
            //选中的元素一直显示
            pitchShow() {
							console.log('又走这里1111')
                if (!!this.val) {
									console.log(this.val)
									console.log('又走这里22222')
                    let option = this.allOptions.filter(el => {
                        return el.value == this.val
                    })
                    //提取中文
                    if (option.length > 0) {
                        if (this.myData.length > 1) {
                            for (var i = 0; i < this.myData.length; i++) {
                                if (this.myData[i].value == this.val) {
                                    this.myData.splice(i, 1);
                                }
                            }
                            this.myData.unshift(option[0])
                        }
                    }
                }
            },

            /**
             * 判断 option 是否包含value(忽略option大小写)
             * @param option   this.options.item
             * @param value    子串
             * @returns {boolean} 为 true 代表包含
             */
            optionContain(option, value) {
                if(this.styleType===6){
                    return !!~option.text.toLowerCase().indexOf(value.toLowerCase()) || !!~option.value.toLowerCase().indexOf(value.toLowerCase())
                }else {
                    return !!~option.title.toLowerCase().indexOf(value.toLowerCase()) || !!~option.value.toLowerCase().indexOf(value.toLowerCase())
                }

            },
            //清除内容

            clearInput: function () {

                if (!this.readonly) {
                    this.show = false;
                    this.val = ''
                    this.listQuery.name = undefined;

                    this.myData = [];
                }


            },


            selectClick: function (index) {
                this.$emit("handleSelectClick", this.val);
                let option = this.myData.filter(el => {
                    return el.value == index
                })
                //提取代码
                let strVal =""
                //成交方式报关单类型
                if(option[0].byCode=="CJFS"||option[0].byCode=="BGDLX"){
                    strVal = option[0].title.split("|")[1]
                }else if(this.styleType===6){
                    //台账费用下拉
                    strVal = this.extractStr(option[0].text)
                } else {
                    //提取中文
                    strVal = this.extractStr(option[0].title)
                }
                strVal =  strVal.trim();
                // if(this.styleType===6&&strVal.indexOf("/")>-1){
                //         this.listQuery.name =strVal.split("/")[0]
                // }else {
                    this.listQuery.name =strVal
                // }
                this.$emit("handleSelectClickToName", this.listQuery.name);

                this.val = option[0].value;
                // this.aIconType = 1
                this.show = false;
                this.slideB = 0 //清空
                this.slideK = 0 //清空
                this.$nextTick(() => {
                    this.$emit("search", this.val);
                })
            },
            extractStr(str) {
                if(!!str){
                    let splitList = str.split("|")
                    // let strVal =splitList[1]
                    if(splitList.length==2){
                        let strVal =splitList[0]
                        let strVal1 =splitList[1]
                        if(escape(strVal).indexOf("%u")>0){
                            return strVal.replace(/(^\s*)|(\s*$)/g, "");
                        }
                        if(escape(strVal1).indexOf("%u")>0){
                            return strVal1.replace(/(^\s*)|(\s*$)/g, "");
                        }
											return strVal.replace(/(^\s*)|(\s*$)/g, "");
                    }else if(splitList.length==3){
                        let strVal2 =splitList[0]
                        let strVal3 =splitList[1]
                        let strVal4 =splitList[2]
                        if(escape(strVal2).indexOf("%u")>0){
                            return strVal2.replace(/(^\s*)|(\s*$)/g, "");
                        }
                        if(escape(strVal3).indexOf("%u")>0){
                            return strVal3.replace(/(^\s*)|(\s*$)/g, "");
                        }
                        if(escape(strVal4).indexOf("%u")>0){
                            return strVal4.replace(/(^\s*)|(\s*$)/g, "");
                        }
                    }else if(splitList.length==1){
                        return splitList[0].replace(/(^\s*)|(\s*$)/g, "");
                    }

                    // let reg = new RegExp('[\u4e00-\u9fa5]+$', 'g');
                    // let strVal = str.match(/[\u4e00-\u9fa5]/g).join("");
                    return strVal
                }
            },


            //光标移动到下拉当前下拉颜色更变
            selectHover: function (index) {
                this.now = index;

            },

            //向下

            selectDown: function () {
                if (this.now < this.myData.length) {
                    this.now++;
                }
                let dd = document.getElementsByClassName('happy-scroll-bar')
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByClassName('happy-scroll-container')//获取模块目的设置高度
                let dd3 = document.getElementsByTagName("li")
                let conHeight = dd1[0].clientHeight //模块总高度
                let liHeight = dd3[1].clientHeight //li模块高度

                if (this.now != 50 && this.now != 200 && this.myData.length > 7 && this.now > 3) {
                    this.slideB = this.slideB - 35
                    dd1[0].style.transform = "translateY(" + this.slideB + "px)"
                }

                this.slideK = this.slideK + 10

            },
            //选中
            tooltipHover(){
                this.aIconType = 0
            },
            tooltipLeave(){
                // if(this.show ==true){
                //     this.aIconType = 2
                // }else{
                //     this.aIconType = 1
                // }
                this.aIconType = ""

            },

            //向上
            selectUp: function () {
                if (this.now > 0) {
                    this.now--;
                }
                //
                let dd = document.getElementsByClassName('happy-scroll-bar')
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByTagName("li")
                let conHeight = dd1[0].clientHeight //模块总高度
                if (this.slideK > 30 && this.slideB != 0) {
                    this.slideB = this.slideB + 35
                }

                this.slideK = this.slideK + 10
                dd1[0].style.transform = "translateY(" + this.slideB + "px)"

                // dd[0].style.transform = "translateY("+this.slideK+"px)"
                this.$forceUpdate()
            },
            /**
             * 失焦下拉隐藏
             */
            blur(e) {
                setTimeout(() => {
                    this.show = false
                    this.extractName(0)
                    this.slideB = 0 //清空
                    this.slideK = 0 //清空
                }, 200)
            },
            blurv(e){
                this.$emit("blur", e);
            },
            focus(event) {
                this.focusIndexs = parseFloat(event.currentTarget.id)
                //获取焦点全选
                event.currentTarget.select();
                this.$emit('focusIndexDate', this.focusIndexs)
            },

            searchEnter(event) {
                if( this.show==true&&this.myData.length>0){
                    // let decmodel = this.myData[this.now]
                    //提取中文
                    let strVal
                    //成交方式报关单类型 提取的不是中文有可能是是数字或者其他单独处理
                    if(this.myData[this.now].byCode=="CJFS"||this.myData[this.now].byCode=="BGDLX"){
                        strVal = this.myData[this.now].title.split("|")[1]
                    }if(this.styleType===6){
                        strVal = this.extractStr(this.myData[this.now].text)
                    } else{
                        strVal = this.extractStr(this.myData[this.now].title)
                    }
                    strVal =  strVal.trim();
                    this.listQuery.name = strVal;
                    this.$emit("handleSelectClickToName", this.listQuery.name);
                    this.val = this.myData[this.now].value
                }
                //日期
                if(parseInt(event.currentTarget.id) ==57){
                    this.$emit('focusIndexDate', parseInt(event.currentTarget.id)+0.1)
                }else if(parseFloat(event.currentTarget.id) ==0.3){
                    this.$emit('focusIndexDate', 0)
                } else if(parseFloat(event.currentTarget.id) ==0){
                    this.$emit('focusIndexDate', parseInt(event.currentTarget.id)+0.1)
                }else{
                    this.focusIndexs = parseInt(event.currentTarget.id) + 1
                    this.$emit('focusIndexDate', this.focusIndexs)
                }
                // this.aIconType = 1
                this.show = false;
                this.slideB = 0 //清空
                this.slideK = 0 //清空
                this.$emit("search",this.val);
            },
            //0是失去焦点listQuery.name不作处理 1是表体新增的时候listQuery.name赋空 提取中文
            extractName(e) {
                if (!!this.val) {
                    let option = this.allOptions.filter(el => {
                        return el.value == this.val
                    })
                    //提取中文
                    if (option.length > 0) {
                        let strVal
                        //成交方式报关单类型
                        if(option[0].byCode=="CJFS"||option[0].byCode=="BGDLX"){
                            strVal = option[0].title.split("|")[1]
                        }else if(this.styleType===6){
                            strVal = this.extractStr(option[0].text)
                        }else {
                            strVal = this.extractStr(option[0].title)
                        }

                        if (!!strVal) {
                            strVal =  strVal.trim();
                            this.listQuery.name = strVal;
                        }
                    } else  {
                        // if(this.styleType===6){
                        //     this.listQuery.name=''
                        //     this.val=''
                        // }else {
                            this.listQuery.name = this.val;
                        // }
                    }
                }else if(e==1){
                    this.listQuery.name =""
                }
            },

        },


        watch: {
            value: {
                async handler(e) {
                    this.val = this.value
                    //本地下拉不需要缓存数据
                    if (!!this.dictKey) {
                        await  this.initSelect()
                    }
                    //提取显示中文
                    await  this.extractName(1)
                    // this.listQuery.name='d'
                },
            },
            val: {
                handler() {
                    this.$nextTick(() => {
                        this.$emit('change', this.val)
                    })

                },
            },
            options: {
                deep: true,
                handler(newVal,oldVal){
                    this.listQuery.name =""
                    this.myData =[]
                    this.myData = newVal
                    this.allOptions = newVal
                    this.extractName()
                }
            },
            myData: {
                deep: true,
                handler(newVal,oldVal){
                    if(newVal.length==0&&this.styleType!==6){
                        this.listQuery.name = ''
                        this.$message.warning("无匹配数据！")
                    }
                }
            }
        }

    }
</script>

<style scoped>
    .search-select {

        position: absolute;

        /*top: 45px;*/

        width: 36.5%;

        box-sizing: border-box;
        z-index: 999;
        height: 220px;
        max-height: 220px;
        /*float: left;*/
        background-color: #FFFFFF;
        border: 1px solid #d4d4d4;

    }

    .search-selectli {

        border: 1px solid #d4d4d4;

        border-top: none;

        border-bottom: none;

        background-color: #E6F7FF;

        width: 100%

    }

    .search-select-option {
        float: left;
        box-sizing: border-box;
        padding: 7px;
        width: 100%;
    }

    .search-select-option-sum {
        display: -webkit-flex;
        display: flex;
    }

    .search-select-option-dev-two {
        width: 30%;
        display: block;
        float: left;
    }

    .search-select-option-div-one {
        -webkit-flex: 1; /* Safari */
        -ms-flex: 1; /* IE 10 */
        flex: 1; /* Standard syntax */
        width: 70%;
    }

    .search-select-option-li {
        box-sizing: border-box;
        float: left;
        padding: 7px;
        width: 100%;
        height: 100%;
        color: #ccc;
    }

    .selectback {

        background-color: #E6F7FF !important;

        cursor: pointer

    }

    .selectbackSize {
        background-color: #F8F8F8 !important;
        font-weight: bold;
        cursor: pointer
    }

    input::-ms-clear {

        display: none

    }

    .search-reset {
        z-index: 999;
        width: 21px;
        height: 21px;

        position: relative;

        /*display: block;*/

        line-height: 21px;

        /*text-align: center;*/

        cursor: pointer;

        /*font-size: 20px;*/

        right: 20px;

        /*top: 12px*/

    }

    /*.search-reset {*/

    /*    width: 21px;*/

    /*    height: 21px;*/

    /*    position: relative;*/

    /*    display: block;*/

    /*    line-height: 21px;*/

    /*    text-align: center;*/

    /*    cursor: pointer;*/

    /*    font-size: 20px;*/

    /*    right: 110px;*/

    /*    top: 12px*/

    /*}*/

    .search-select-list {

        transition: all0 .5s

    }

    .itemfade-enter,
    .itemfade-leave-active {

        opacity: 0;

    }

    .itemfade-leave-active {

        position: absolute;

    }

    .selectback {

        background-color: #E6F7FF !important;

        cursor: pointer

    }

    .search-selectul {
        margin: 0;
        text-align: left;
    }

    .fade-enter-active, .fade-leave-active {

        transition: opacity .5s;

    }

    .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
    {

        opacity: 0;

    }

    >>> .happy-scroll-container .happy-scroll-content {
        /*display: inline;*/
        width: 100%;
    }


    .ant-input-a {
        padding: 1px 1px!important;
        width: 100%;
        font-size:11px;
        height:19px
    }
    .ant-input-affix-wrapper .ant-input-suffix {
        margin-right: 2px;
    }

    .alone-input >>> div.ant-col.ant-form-item-control-wrapper {
        width: 100%;
    }
    .tdWidth{
        width: 17%
    }
    .tdOneWidth{
        width: 8.02%
    }
    /*申报地海关*/
    .tdOneWidthClass{
        width:39.68%
    }
    .tdTwoClass{
        width: 8.9%
    }
    .tdDeclarePlaceClass{
        width: 11.1%
    }
    .tdDecStatus{
        width: 40.78%
    }
</style>