<template>
	<a-card :bordered='false' :bodyStyle="{padding:'0px'}">
		<!-- 查询区域 -->
		<div class='table-page-search-wrapper'>
			<a-form layout='inline' @keyup.enter.native='searchQuery'>
				<a-row :gutter='24'>
					<a-col :xl='6' :sm='24' :xxl='6' :md='12'>
						<a-form-item label='序号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
							<a-input placeholder='请输入序号' v-model='queryParam.gNo'></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl='6' :sm='24' :xxl='6' :md='12'>
						<a-form-item label='料号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
							<a-input placeholder='请输入料号' v-model='queryParam.copGno'></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl='6' :sm='24' :xxl='6' :md='12'>
						<a-form-item label='商品名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
							<a-input placeholder='请输入商品名称' v-model='queryParam.gName'></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl='6' :sm='24' :xxl='6' :md='12'>
							<span style='float: left;overflow: hidden;' class='table-page-search-submitButtons'>
								<a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
								<a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class='table-operator'>
			<a-button @click='handleAdd' type='primary' icon='plus'>新增</a-button>
			<a-button
				@click='batchDel'
				v-if='selectedRowKeys.length > 0'
				ghost
				type='primary'
				icon='delete'>批量删除
			</a-button>
		</div>

		<!-- table区域-begin -->
		<div>
			<a-table
				ref='table'
				size='small'
				:scroll='{ x: true }'
				bordered
				rowKey='id'
				:columns='columns'
				:dataSource='dataSource'
				:pagination='ipagination'
				:loading='loading'
				class='j-table-force-nowrap'
				:rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, fixed: true}'
				:rowClassName='getRowClassname'
				@change='handleTableChange'
				:customRow='rowEvent'
			>
				<!-- 可出口数量START -->
				<template slot="exportedQy" slot-scope="text, record">
        <span :class="{ textGreen: computedExportedQy(record) > 0 }">
          {{ computedExportedQy(record) }}
        </span>
				</template>
				<!-- 可出口数量END -->
				<!-- 备案数量START -->
				<template slot="qty" slot-scope="text, record">
        <span :class="{ textGreen: record.qty > 0 }">
          {{ record.qty }}
        </span>
					<!--					<a-icon  v-if="$store.getters.tenantType == 1" type="edit" theme='twoTone' @click="showBeianModal({ row })"/>-->
				</template>
				<!-- 备案数量END -->
				<!-- 已出口数量START -->
				<template slot="exportedQtySlots" slot-scope="text, record">
        <span :class="{ textGreen: record.exportedQty > 0 }">
          {{ record.exportedQty }}
        </span>
				</template>
				<!-- 已出口数量END -->
				<!-- 已进口数量START -->
				<template slot="calculateSlots" slot-scope="text, record">
					<template v-if="record.calculateNum || record.calculateNum === 0">
						<a-tag color="#87d068">
							{{ record.calculateNum }}
						</a-tag>
					</template>
					<a-button
						:size="$types.SMALL_SIZE"
						@click="calculateNumFun(record)"
						:loading="countLoadingFlag"
					>计算
					</a-button>
				</template>
				<!-- 已进口数量END -->
				<!-- 申报记录START -->
				<template slot="applyHistory" slot-scope="text, record">
					<a @click="getApplyHistory(record)">申报记录</a>
				</template>
				<!-- 申报记录END 已经通过其他方法计算-->
				<span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
          {{subStrForColumns(record.gModel, 15)}}
        </span>
				<span slot='action' slot-scope='text, record'>
          <a-dropdown>
            <a class='ant-dropdown-link' @click.stop=''> <a-icon type='setting' /></a>
            <a-menu slot='overlay'>
              <a-menu-item>
                <a @click='handleEdit(record)'>编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a @click='handleDetail(record)'>详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record)'>
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
			</a-table>
		</div>
		<!-- 成品新增编辑详情 -->
		<aexg-edit-modal ref='modalForm' @ok='modalFormOk' @close='forceRerender' />
	</a-card>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import {
	_postAction,
	deleteAction,
	getAction, httpAction
} from '@/api/manage'
import { subStrForColumns } from '@/utils/util'
import { ajaxGetDictItems } from '@/api/api'
import AexgEditModal from '@/views/AddTradeHandbook/modules/AexgEditModal.vue'

export default {
	name: 'AexgList',
	mixins: [JeecgListMixin, mixinDevice],
	components: {
		AexgEditModal
	},
	data() {
		return {
			countLoadingFlag: false, //计算按钮的loading状态
			mtpckEndprdMarkcd: 'E', // 申报表标识
			disableMixinCreated: true,
			emsHead: {},
			countries: [],
			queryParam: {
				type: '2', // 1料件 2成品 3损耗
				emsId: ''
			},
			selectDate: [],
			selectAppDate: [],
			selectWarehousingDate: [],
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl: { span: 5 },
				xl: { span: 9 }
			},
			wrapperCol: {
				xs: { span: 16 }
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '商品编码',
					align: 'center',
					dataIndex: 'codet'
				},
				{
					title: '规格型号',
					align: 'center',
					dataIndex: 'gModel',
					scopedSlots: { customRender: 'gModelSlots' }
				},
				{
					title: '理论可生产数量',
					align: 'center',
					dataIndex: 'calculateNum',
					scopedSlots: { customRender: 'calculateSlots' }
				},
				{
					title: '备案数量',
					align: 'center',
					dataIndex: 'qty',
					scopedSlots: { customRender: 'qty' }
				},
				{
					title: '已出口数量',
					align: 'center',
					dataIndex: 'exportedQty',
					scopedSlots: { customRender: 'exportedQtySlots' }
				},
				{
					title: '可出口数量',
					align: 'center',
					dataIndex: 'exportedQy',
					scopedSlots: { customRender: 'exportedQy' }
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				}
			],
			url: {
				list: '/business/ems/listEmsDetail',
				deleteBatch: '/business/ems/deleteDetailBatch',
				calculate: '/business/ems/getUsableAexg'
			}
		}
	},
	created() {
		this.initDictData('erp_countries,name,code')
	},
	methods: {
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length > 0) {
				if (dictCode.includes('erp_countries')) {
					this.countries = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						this.initDictData(dictCode)
					}
				})
			}
		},
		handleAdd: function() {
			if (!this.emsHead || !this.emsHead.id) {
				this.$message.warning('请先选择账册!')
				return
			}
			this.$refs.modalForm.add(this.emsHead)
			this.$refs.modalForm.title = '新增'
			this.$refs.modalForm.disableSubmit = false
		},
		initHead(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			if (!this.queryParam.emsId) {
				this.searchReset()
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
					},
					dblclick: () => {
						this.handleEdit(record)
					}
				}
			}
		},
		searchReset() {
			this.selectDate = []
			this.selectAppDate = []
			this.selectWarehousingDate = []
			this.queryParam = {
				type: '2', // 1料件 2成品 3损耗
				emsId: this.emsHead.id
			}
			this.loadData(1)
			this.onClearSelected()
		},
		clear() {
			this.queryParam = {
				type: '2', // 1料件 2成品 3损耗
				emsId: ''
			}
			this.emsHead = {}
			this.dataSource = []
			this.onClearSelected()
		},
		subStrForColumns,
		batchDel: function() {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function() {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids, type: '2' })
							.then(res => {
								if (res.success) {
									that.$message.success(res.message)
									that.forceRerender()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		handleDelete: function(record) {
			deleteAction(this.url.deleteBatch, {
				ids: record.id,
				type: '2'
			}).then((res) => {
				if (res.success) {
					this.$message.success(res.message)
					this.forceRerender()
				} else {
					this.$message.warning(res.message)
				}
			})
		},
		// 计算
		calculateNumFun(row) {
			this.countLoadingFlag = true
			let data = {
				emsId: this.emsHead.id,
				gNo: row.gNo
			}
			_postAction(this.url.calculate, data).then(res => {
				if (res.success) {
					row['calculateNum'] = res.result
				} else {
					this.$message.warning(res.message)
				}
			}).finally(() => {
				this.countLoadingFlag = false
			})
		},
		computedExportedQy(records) {
			return this.floatSub(records.qty, records.exportedQty)
		},
		// 增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '2') {
				return 'data-rule-invalid'
			}
		},
		forceRerender() {
			this.loadData(1)
			this.onClearSelected()
		},
		/**
		 ** 减法函数，用来得到精确的减法结果
		 ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
		 ** 调用：accSub(arg1,arg2)
		 ** 返回值：arg1加上arg2的精确结果
		 **/
		floatSub (arg1, arg2, n) {
			var r1, r2, m, n
			try { r1 = arg1.toString().split('.')[1].length } catch (e) { r1 = 0 }
			try { r2 = arg2.toString().split('.')[1].length } catch (e) { r2 = 0 }
			m = Math.pow(10, Math.max(r1, r2))
			// 动态控制精度长度
			if (!n) {
				n = (r1 >= r2) ? r1 : r2
			}
			return ((arg1 * m - arg2 * m) / m).toFixed(n)
		}
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
.textGreen {
	color: darkgreen;
	font-weight: bold;
}
</style>