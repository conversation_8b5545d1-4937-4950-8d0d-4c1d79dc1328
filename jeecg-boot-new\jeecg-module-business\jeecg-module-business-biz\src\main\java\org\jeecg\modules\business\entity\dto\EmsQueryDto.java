package org.jeecg.modules.business.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 账册查询实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@Accessors(chain = true)
public class EmsQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String type;
    /**
     * 手册流水号
     */
    private String emsId;
    /**
     * 手册编号
     */
    private String emsNo;
    /**
     * 序号（报关用）(备案号)
     */
    private String gNo;
    /**
     * 货号(归并后可以没有货号)
     */
    private String copGno;

    private String expiryDateStart;
    private String expiryDateEnd;
    /**
     *申报日期开始
     */
    private String appDateStart;
    /**
     *申报日期结束
     */
    private String appDateEnd;
    /**
     * 商品名称
     */
    private String gName;
    /**
     * 企业内部编号(统计需要(可累计的无意义))
     */
    private String internalNo;
    /**
     * 关联报关单号
     */
    private String clearanceNo;
    /**
     * 税号
     */
    private String codet;
    /**
     *入库日期开始
     */
    private String warehousingDateStart;

    /**
     *入库日期结束
     */
    private String warehousingDateEnd;

    private String stockQtyZero;

    /**
     * 序号集合（报关用）(备案号)
     */
    private List<String> gNoList;

    /**
     * 成品序号集合
     */
    private List<String> exgNoList;

    /**
     * 精确查找序号
     */
    private String accurateGNo;

    /**
     * 精确查找货号
     */
    private String accurateCopGno;

    /**
     * 精确查找手/账册号
     */
    private String accurateEmsNo;
    /**
     * 计量单位
     */
    private String unit;

    /**
     * 企业内部编号|货号
     */
    private List<String> internalNoAndPns;

    /**
     * 成品序号
     */
    private String exgNo;

    /**
     * 料件序号
     */
    private String imgNo;

    /**
     * 是否延期(太古物流账册用)
     *
     * 新：延期次数/状态
     */
    private String delay;
    /**
     * 原产国
     * @return
     */
    private String countryCode;

    /**
     * 排序字段,排序规则（ASC or DESC）
     */
    private List<String> orderByField;

    /**
     * 预警状态
     */
    private String warnStatus;
    private String tenantId;

    private String exgCopGno;
    private String exgGname;
    private String imgCopGno;
    private String imgGname;
    private String seqNo;

    /**
     * 是否来自报关单页面，需要数字转英文
     */
    private String isFromDec;
}
