<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="成品序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入成品序号" v-model="queryParam.exgNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="成品料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入成品料号" v-model="queryParam.exgCopGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入商品名称" v-model="queryParam.exgGname"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件序号" v-model="queryParam.imgNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="料件料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件料号" v-model="queryParam.imgCopGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="8" :md="12">
						<a-form-item label="料件商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件商品名称" v-model="queryParam.imgGname"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="24" :md="12">
							<span style="float: right;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div style="margin-top: -15px;">
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="id"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				class="j-table-force-nowrap"
				@change="handleTableChange"
			>
			</a-table>
		</div>
	</a-card>
</template>

<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import { getAction } from '@/api/manage'
import { ajaxGetDictItems } from '@/api/api'
const DICT_erp_units = 'erp_units,name,code'
export default {
	name: "ExportConsumption",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			disableMixinCreated: true,
			emsHead: {},
			queryParam: {
				type: '3', // 1料件 2成品 3损耗
				emsId: '999999999'
			},
			labelCol: {
				xs: {span: 5},
				// sm: { span: 7 },
				xxl: {span: 5},
				xl: {span: 9}
			},
			wrapperCol: {
				xs: {span: 16},
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '成品序号',
					align: 'center',
					dataIndex: 'exgNo'
				},
				{
					title: '成品料号',
					align: 'center',
					dataIndex: 'exgCopGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'exgGname'
				},
				{
					title: '成品计量单位',
					align: 'center',
					dataIndex: 'exgUnit',
					customRender: this.showQunitText
				},
				{
					title: '实际出口数量',
					align: 'center',
					dataIndex: 'exportQty',
				},
				{
					title: '料件序号',
					align: 'center',
					dataIndex: 'imgNo'
				},
				{
					title: '料件料号',
					align: 'center',
					dataIndex: 'imgCopGno'
				},
				{
					title: '料件商品名称',
					align: 'center',
					dataIndex: 'imgGname'
				},
				{
					title: '料件计量单位',
					align: 'center',
					dataIndex: 'imgUnit',
					customRender: this.showQunitText
				},
				{
					title: '实际进口数量',
					align: 'center',
					dataIndex: 'importQty',
				},
				{
					title: '净耗',
					align: 'center',
					dataIndex: 'decCm',
				},
				{
					title: '有形损耗率%',
					align: 'center',
					dataIndex: 'decDm',
				},
				{
					title: '无形损耗率%',
					align: 'center',
					dataIndex: 'intangibleLossRate',
				},
				{
					title: '保税料件比例%',
					align: 'center',
					dataIndex: 'proportionOfBondedMaterials',
				},
				{
					title: '出口耗用',
					align: 'center',
					dataIndex: 'ckhy',
				},
				{
					title: '工艺损耗',
					align: 'center',
					dataIndex: 'gysh',
				},
				{
					title: '保税料件耗用',
					align: 'center',
					dataIndex: 'bsljhy',
				},
				{
					title: '非保税料件耗用',
					align: 'center',
					dataIndex: 'fbsljhy',
				},
			],
			units: [],
			url: {
				list: '/business/ems/listEmsDetailByReport',
			}
		}
	},
	created() {
		this.initDictData(DICT_erp_units)
	},
	methods: {
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == DICT_erp_units) {
					this.units = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						this.initDictData(dictCode)
					}
				})
			}
		},
		showQunitText(text, record, index) {
			return this.getText(text, this.units)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text
		},
		init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						if (this.dataSource.length > 0) {
							this.dataSource.forEach(item => {
								item.dataSourceDec = []
								item.dataSourceInvt = []
							})
						}
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				type: '3', // 1料件 2成品 3损耗
				emsId: this.emsHead.id ? this.emsHead.id : '999999999',
				emsNo : this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			}
			this.loadData(1)
			this.onClearSelected()
		}
	}
}
</script>

<style scoped lang="less">

</style>