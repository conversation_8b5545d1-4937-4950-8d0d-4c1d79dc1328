<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DecHeadMapper">

    <insert id="insertBatchSomeColumn">
        insert into DEC_HEAD (ID, DCL_TRN_REL_FLAG,
                                    DEC_STATUS, PART_ID, ELEC_DEL_NO,
                                    CUSTOMS_CODE, SEQ_NO, CLEARANCE_NO,
                                    RECORD_NUMBER, IE_FLAG, OUT_DATE,
                                    APP_DATE, SHIP_TYPE_CODE, SHIP_NAME,
                                    VOYAGE, CONTRACT, BILL_CODE,
                                    DECLARE_PLACE, OUT_PORT_CODE, ENTY_PORT_CODE,
                                    OVERSEAS_CONSIGNEE_CODE, OVERSEAS_CONSIGNOR_CODE,
                                    OPT_UNIT_SOCIAL_CODE, OPT_UNIT_ID, TRADE_CIQ_CODE,
                                    OPT_UNIT_NAME, DELIVER_UNIT_SOCIAL_CODE, DELIVER_UNIT,
                                    OWNER_CIQ_CODE, DELIVER_UNIT_NAME, DECLARE_UNIT_SOCIAL_CODE,
                                    DECLARE_UNIT, DECL_CIQ_CODE, DECLARE_UNIT_NAME,
                                    TRADE_COUNTRY, ARRIVAL_AREA, DESP_PORT_CODE,
                                    DES_PORT, TERMS_TYPE_CODE, TRADE_TYPE_CODE,
                                    TAX_TYPE_CODE, SHIP_FEE_CODE, SHIP_FEE,
                                    SHIP_CURRENCY_CODE, INSURANCE_CODE, INSURANCE,
                                    INSURANCE_CURR, EXTRAS_CODE, EXTRAS,
                                    OTHER_CURR, PACKS, GROSS_WEIGHT,
                                    NET_WEIGHT, LICENCE_NUMBER, PACKS_KINDS,
                                    CONTAINER_NUM, CLEARANCE_TYPE, GOODS_PLACE,
                                    CONTRACT_ATT, MARK_NUMBER, SURETY_FLAG,
                                    CHK_SURETY, PROMISE_ITMES, BILL_TYPE,
                                    REL_ID, REL_MAN_NO, BON_NO,
                                    CUS_FIE, VOLUME, `TOTAL`,
                                    GOODS_COUNT, ORG_CODE, INSP_ORG_CODE,
                                    PURP_ORG_CODE, VSA_ORG_CODE, BL_NO,
                                    SPEC_DECL_FLAG, DESP_DATE, CMPL_DSCHRG_DT,
                                    CORRELATION_NO, CORRELATION_REASON_FLAG, ORIG_BOX_FLAG,
                                    NO_OTHER_PACK, DECLARANT, DECLARANT_NO,
                                    STATUS, TRANFER_TYPE, CLEARANCE_MODE,
                                    DEC_TYPE, EDI_ID,
                                    ETPS_INNER_INVT_NO, CREATE_PERSON, CREATE_TIME,
                                    CREATE_UNIT, `SEND`, TENANT_ID,
                                    DCL_TENANT_ID, IC_NUMBER,
                                    UPDATE_BY, UPDATE_DATE, INSP_MONITOR_COND,
                                    INPUT_ID, INPUT_ER_NAME,
                                    GOODS_TYPE,
                                    RELEASE_DATE, FINAL_DATE, ORDER_PROTOCOL_NO,
                                    PUSH_STATUS, INITIAL_REVIEW_STATUS,
                                    FIRST_TRIAL_BY, FIRST_TRIAL_DATE, FIRST_OPINION,
                                    REVIEW_BY, REVIEW_DATE, REVIEW_OPINION,
                                    INV_ID, IS_DOCKING, AUDITED,
                                    FLY_ID, SYNCHRONISM, SYNCHRONISM_DATE,
                                    OVERSEAS_CONSIGNEE_ENAME, OVERSEAS_CONSIGNOR_ENAME,
                                    PACK_TYPE, MARK_NO, COP_LIMIT_TYPE,
                                    DEC_USER_TYPE, REQUEST_CERT_TYPE,CURRENCY)
        values
        <foreach collection="addHeads" item="a" separator=",">
            (#{a.id}, #{a.dclTrnRelFlag},
            #{a.decStatus}, #{a.partId}, #{a.elecDelNo},
            #{a.customsCode}, #{a.seqNo}, #{a.clearanceNo},
            #{a.recordNumber}, #{a.ieFlag}, #{a.outDate},
            #{a.appDate}, #{a.shipTypeCode}, #{a.shipName},
            #{a.voyage}, #{a.contract}, #{a.billCode},
            #{a.declarePlace}, #{a.outPortCode}, #{a.entyPortCode},
            #{a.overseasConsigneeCode}, #{a.overseasConsignorCode},
            #{a.optUnitSocialCode}, #{a.optUnitId}, #{a.tradeCiqCode},
            #{a.optUnitName}, #{a.deliverUnitSocialCode}, #{a.deliverUnit},
            #{a.ownerCiqCode}, #{a.deliverUnitName}, #{a.declareUnitSocialCode},
            #{a.declareUnit}, #{a.declCiqCode}, #{a.declareUnitName},
            #{a.tradeCountry}, #{a.arrivalArea}, #{a.despPortCode},
            #{a.desPort}, #{a.termsTypeCode}, #{a.tradeTypeCode},
            #{a.taxTypeCode}, #{a.shipFeeCode}, #{a.shipFee},
            #{a.shipCurrencyCode}, #{a.insuranceCode}, #{a.insurance},
            #{a.insuranceCurr}, #{a.extrasCode}, #{a.extras},
            #{a.otherCurr}, #{a.packs}, #{a.grossWeight},
            #{a.netWeight}, #{a.licenceNumber}, #{a.packsKinds},
            #{a.containerNum}, #{a.clearanceType}, #{a.goodsPlace},
            #{a.contractAtt}, #{a.markNumber}, #{a.suretyFlag},
            #{a.chkSurety}, #{a.promiseItmes}, #{a.billType},
            #{a.relId}, #{a.relManNo}, #{a.bonNo},
            #{a.cusFie}, #{a.volume}, #{a.total},
            #{a.goodsCount}, #{a.orgCode}, #{a.inspOrgCode},
            #{a.purpOrgCode}, #{a.vsaOrgCode}, #{a.blNo},
            #{a.specDeclFlag}, #{a.despDate}, #{a.cmplDschrgDt},
            #{a.correlationNo}, #{a.correlationReasonFlag}, #{a.origBoxFlag},
            #{a.noOtherPack}, #{a.declarant}, #{a.declarantNo},
            #{a.status}, #{a.tranferType}, #{a.clearanceMode},
            #{a.decType}, #{a.ediId},
            #{a.etpsInnerInvtNo}, #{a.createPerson}, #{a.createTime},
            #{a.createUnit}, #{a.send}, #{a.tenantId},
            #{a.dclTenantId}, #{a.icNumber},
            #{a.updateBy}, #{a.updateDate}, #{a.inspMonitorCond},
            #{a.inputId}, #{a.inputErName},
            #{a.goodsType},
            #{a.releaseDate}, #{a.finalDate}, #{a.orderProtocolNo},
            #{a.pushStatus}, #{a.initialReviewStatus},
            #{a.firstTrialBy}, #{a.firstTrialDate}, #{a.firstOpinion},
            #{a.reviewBy}, #{a.reviewDate}, #{a.reviewOpinion},
            #{a.invId}, #{a.isDocking,jdbcType=CHAR}, #{a.audited},
            #{a.flyId}, #{a.synchronism}, #{a.synchronismDate},
            #{a.overseasConsigneeEname}, #{a.overseasConsignorEname},
            #{a.packType}, #{a.markNo}, #{a.copLimitType},
            #{a.decUserType}, #{a.requestCertType},#{a.currency})
        </foreach>
    </insert>

    <insert id="insertBatchLists">
        insert into DEC_LIST (ID, DEC_ID,
                              `ITEM`, RECORD_ITEM, HSCODE,
                              HSNAME, CIQ_CODE, CIQ_NAME,
                              HSMODEL, GOODS_COUNT, UNIT_CODE,
                              DES_COUNTRY, PRICE, `TOTAL`,
                              CURRENCY_CODE, FAX_TYPE_CODE, UNIT1,
                              COUNT1, UNIT2, COUNT2,
                              DESTINATION_COUNTRY, ORIG_PLACE_CODE, PURPOSE,
                              DISTRICT_CODE, DEST_CODE, GOODS_ATTR,
                              STUFF, GOODS_SPEC, GOODS_MODEL,
                              GOODS_BRAND, PROD_VALID_DT, PRODUCE_DATE,
                              PROD_BATCH_NO, MNUFCTR_REG_NO, MNUFCTR_REG_NAME,
                              PROD_QGP, ENG_MAN_ENT_CNM, NO_DANG_FLAG,
                              UNCODE, DANG_NAME, DANG_PACK_TYPE,
                              DANG_PACK_SPEC, MERGE_SEQUENCE, HSTYPE,
                              SUPV_MODECD, NET_WEIGHT, GROSS_WEIGHT,
                              CUSTOMS_CODE, EXG_VERSION, DG_FLAG,
                              RCEP_ORIG_PLACE_CODE, GOODS_LIMIT_TYPE,
                              GOODS_ID,DISTRICT_NAME, DEST_NAME)
        values
        <foreach collection="addLists" item="a" separator=",">
            (#{a.id}, #{a.decId},
            #{a.item}, #{a.recordItem}, #{a.hscode},
            #{a.hsname}, #{a.ciqCode}, #{a.ciqName},
            #{a.hsmodel}, #{a.goodsCount}, #{a.unitCode},
            #{a.desCountry}, #{a.price}, #{a.total},
            #{a.currencyCode}, #{a.faxTypeCode}, #{a.unit1},
            #{a.count1}, #{a.unit2}, #{a.count2},
            #{a.destinationCountry}, #{a.origPlaceCode}, #{a.purpose},
            #{a.districtCode}, #{a.destCode}, #{a.goodsAttr},
            #{a.stuff}, #{a.goodsSpec}, #{a.goodsModel},
            #{a.goodsBrand}, #{a.prodValidDt}, #{a.produceDate},
            #{a.prodBatchNo}, #{a.mnufctrRegNo}, #{a.mnufctrRegName},
            #{a.prodQgp}, #{a.engManEntCnm}, #{a.noDangFlag},
            #{a.uncode}, #{a.dangName}, #{a.dangPackType},
            #{a.dangPackSpec}, #{a.mergeSequence}, #{a.hstype},
            #{a.supvModecd}, #{a.netWeight}, #{a.grossWeight},
            #{a.customsCode}, #{a.exgVersion}, #{a.dgFlag},
            #{a.rcepOrigPlaceCode}, #{a.goodsLimitType},
            #{a.goodsId}, #{a.districtName}, #{a.destName})
        </foreach>
    </insert>

    <insert id="insertBatchContainers">
        insert into DEC_CONTAINER (ID, DEC_ID, CONTAINER_ID,
                                CONTAINER_MD, LCL_FLAG, GOODS_CONTA_WT,
                                CUSTOMS_CODE, GOODS_NO)
        values
        <foreach collection="addContainers" item="a" separator=",">
            (#{a.id}, #{a.decId}, #{a.containerId},
            #{a.containerMd}, #{a.lclFlag}, #{a.goodsContaWt},
            #{a.customsCode}, #{a.goodsNo})
        </foreach>
    </insert>

    <insert id="insertBatchLicenseDocuses">
        insert into DEC_LICENSE_DOCUS (ID, DEC_ID, DOCU_CODE,
        CERT_CODE, CUSTOMS_CODE, ORIGPLACE_CODE
        )
        values
        <foreach collection="addLicenseDocuss" item="a" separator=",">
            (#{a.id}, #{a.decId}, #{a.docuCode},
            #{a.certCode}, #{a.customsCode}, #{a.origplaceCode}
            )
        </foreach>
    </insert>

    <update id="updateBatchById">
        <foreach collection="updateHeads" item="decHead" index="index" open="" close="" separator=";">
            update DEC_HEAD
            <set>
                DCL_TRN_REL_FLAG = #{decHead.dclTrnRelFlag},
                DEC_STATUS = #{decHead.decStatus},
                PART_ID = #{decHead.partId},
                ELEC_DEL_NO = #{decHead.elecDelNo},
                CUSTOMS_CODE = #{decHead.customsCode},
                SEQ_NO = #{decHead.seqNo},
                CLEARANCE_NO = #{decHead.clearanceNo},
                RECORD_NUMBER = #{decHead.recordNumber},
                IE_FLAG = #{decHead.ieFlag},
                OUT_DATE = #{decHead.outDate},
                APP_DATE = #{decHead.appDate},
                SHIP_TYPE_CODE = #{decHead.shipTypeCode},
                SHIP_NAME = #{decHead.shipName},
                VOYAGE = #{decHead.voyage},
                CONTRACT = #{decHead.contract},
                BILL_CODE = #{decHead.billCode},
                DECLARE_PLACE = #{decHead.declarePlace},
                OUT_PORT_CODE = #{decHead.outPortCode},
                ENTY_PORT_CODE = #{decHead.entyPortCode},
                OVERSEAS_CONSIGNEE_CODE = #{decHead.overseasConsigneeCode},
                OVERSEAS_CONSIGNOR_CODE = #{decHead.overseasConsignorCode},
                OPT_UNIT_SOCIAL_CODE = #{decHead.optUnitSocialCode},
                OPT_UNIT_ID = #{decHead.optUnitId},
                TRADE_CIQ_CODE = #{decHead.tradeCiqCode},
                OPT_UNIT_NAME = #{decHead.optUnitName},
                DELIVER_UNIT_SOCIAL_CODE = #{decHead.deliverUnitSocialCode},
                DELIVER_UNIT = #{decHead.deliverUnit},
                OWNER_CIQ_CODE = #{decHead.ownerCiqCode},
                DELIVER_UNIT_NAME = #{decHead.deliverUnitName},
                DECLARE_UNIT_SOCIAL_CODE = #{decHead.declareUnitSocialCode},
                DECLARE_UNIT = #{decHead.declareUnit},
                DECL_CIQ_CODE = #{decHead.declCiqCode},
                DECLARE_UNIT_NAME = #{decHead.declareUnitName},
                TRADE_COUNTRY = #{decHead.tradeCountry},
                ARRIVAL_AREA = #{decHead.arrivalArea},
                DESP_PORT_CODE = #{decHead.despPortCode},
                DES_PORT = #{decHead.desPort},
                TERMS_TYPE_CODE = #{decHead.termsTypeCode},
                TRADE_TYPE_CODE = #{decHead.tradeTypeCode},
                TAX_TYPE_CODE = #{decHead.taxTypeCode},
                SHIP_FEE_CODE = #{decHead.shipFeeCode},
                SHIP_FEE = #{decHead.shipFee},
                SHIP_CURRENCY_CODE = #{decHead.shipCurrencyCode},
                INSURANCE_CODE = #{decHead.insuranceCode},
                INSURANCE = #{decHead.insurance},
                INSURANCE_CURR = #{decHead.insuranceCurr},
                EXTRAS_CODE = #{decHead.extrasCode},
                EXTRAS = #{decHead.extras},
                OTHER_CURR = #{decHead.otherCurr},
                PACKS = #{decHead.packs},
                GROSS_WEIGHT = #{decHead.grossWeight},
                NET_WEIGHT = #{decHead.netWeight},
                LICENCE_NUMBER = #{decHead.licenceNumber},
                PACKS_KINDS = #{decHead.packsKinds},
                CONTAINER_NUM = #{decHead.containerNum},
                CLEARANCE_TYPE = #{decHead.clearanceType},
                GOODS_PLACE = #{decHead.goodsPlace},
                CONTRACT_ATT = #{decHead.contractAtt},
                MARK_NUMBER = #{decHead.markNumber},
                SURETY_FLAG = #{decHead.suretyFlag},
                CHK_SURETY = #{decHead.chkSurety},
                PROMISE_ITMES = #{decHead.promiseItmes},
                BILL_TYPE = #{decHead.billType},
                REL_ID = #{decHead.relId},
                REL_MAN_NO = #{decHead.relManNo},
                BON_NO = #{decHead.bonNo},
                CUS_FIE = #{decHead.cusFie},
                VOLUME = #{decHead.volume},
                `TOTAL` = #{decHead.total},
                GOODS_COUNT = #{decHead.goodsCount},
                ORG_CODE = #{decHead.orgCode},
                INSP_ORG_CODE = #{decHead.inspOrgCode},
                PURP_ORG_CODE = #{decHead.purpOrgCode},
                VSA_ORG_CODE = #{decHead.vsaOrgCode},
                BL_NO = #{decHead.blNo},
                SPEC_DECL_FLAG = #{decHead.specDeclFlag},
                DESP_DATE = #{decHead.despDate},
                CMPL_DSCHRG_DT = #{decHead.cmplDschrgDt},
                CORRELATION_NO = #{decHead.correlationNo},
                CORRELATION_REASON_FLAG = #{decHead.correlationReasonFlag},
                ORIG_BOX_FLAG = #{decHead.origBoxFlag},
                NO_OTHER_PACK = #{decHead.noOtherPack},
                DECLARANT = #{decHead.declarant},
                DECLARANT_NO = #{decHead.declarantNo},
                STATUS = #{decHead.status},
                TRANFER_TYPE = #{decHead.tranferType},
                CLEARANCE_MODE = #{decHead.clearanceMode},
                DEC_TYPE = #{decHead.decType},
                EDI_ID = #{decHead.ediId},
                ETPS_INNER_INVT_NO = #{decHead.etpsInnerInvtNo},
                CREATE_PERSON = #{decHead.createPerson},
                CREATE_TIME = #{decHead.createTime},
                CREATE_UNIT = #{decHead.createUnit},
                SEND = #{decHead.send},
                TENANT_ID = #{decHead.tenantId},
                DCL_TENANT_ID = #{decHead.dclTenantId},
                IC_NUMBER = #{decHead.icNumber},
                UPDATE_BY = #{decHead.updateBy},
                UPDATE_DATE = #{decHead.updateDate},
                INSP_MONITOR_COND = #{decHead.inspMonitorCond},
                INPUT_ID = #{decHead.inputId},
                INPUT_ER_NAME = #{decHead.inputErName},
                GOODS_TYPE = #{decHead.goodsType},
                RELEASE_DATE = #{decHead.releaseDate},
                FINAL_DATE = #{decHead.finalDate},
                ORDER_PROTOCOL_NO = #{decHead.orderProtocolNo},
                PUSH_STATUS = #{decHead.pushStatus},
                INITIAL_REVIEW_STATUS = #{decHead.initialReviewStatus},
                FIRST_TRIAL_BY = #{decHead.firstTrialBy},
                FIRST_TRIAL_DATE = #{decHead.firstTrialDate},
                FIRST_OPINION = #{decHead.firstOpinion},
                REVIEW_BY = #{decHead.reviewBy},
                REVIEW_DATE = #{decHead.reviewDate},
                REVIEW_OPINION = #{decHead.reviewOpinion},
                INV_ID = #{decHead.invId},
                IS_DOCKING = #{decHead.isDocking,jdbcType=CHAR},
                AUDITED = #{decHead.audited},
                FLY_ID = #{decHead.flyId},
                SYNCHRONISM = #{decHead.synchronism},
                SYNCHRONISM_DATE = #{decHead.synchronismDate},
                OVERSEAS_CONSIGNEE_ENAME = #{decHead.overseasConsigneeEname},
                OVERSEAS_CONSIGNOR_ENAME = #{decHead.overseasConsignorEname},
                PACK_TYPE = #{decHead.packType},
                MARK_NO = #{decHead.markNo},
                COP_LIMIT_TYPE = #{decHead.copLimitType},
                DEC_USER_TYPE = #{decHead.decUserType},
                REQUEST_CERT_TYPE = #{decHead.requestCertType}
            </set>
            where id = #{decHead.id}
        </foreach>
    </update>
    <update id="updateApplyInvoiceSupvModecd">
        <foreach collection="supvModecdMap" item="value" index="key">
            UPDATE order_product_info SET MONITORCONDITION = #{value} WHERE ORDER_INFO_ID = #{applyNumber} AND SEQUENCE IN (${key});
        </foreach>
    </update>
    <select id="listBySeqNos" resultType="org.jeecg.modules.business.entity.DecHead">
        select * from dec_head where seq_no in
        <foreach collection="cusCiqNoList" item="seqNo" open="(" separator="," close=")">
            #{seqNo}
        </foreach>
    </select>
    <select id="exportDecStatisticsListTicketQty" resultType="org.jeecg.modules.business.vo.DecStatisticsVO">
        select count(id) statisticalContentValue,
               ${field} statisticalItemsValue
        from dec_head
        <where>
            AND ${field} != '' AND ${field} IS NOT NULL
            AND IE_FLAG=#{ieFlag}
            <if test="declarationDateStart != null and declarationDateStart != '' and
            declarationDateEnd != null and declarationDateEnd != ''">
                AND APP_DATE BETWEEN #{declarationDateStart} AND #{declarationDateEnd}
            </if>
        </where>
        group by ${field}
        order by statisticalContentValue desc
    </select>
    <select id="exportDecStatisticsListTradeVolume" resultType="org.jeecg.modules.business.vo.DecStatisticsVO">
        select SUM(TOTAL) statisticalContentValue,
        ${field} statisticalItemsValue,
        CURRENCY currency
        from dec_head
        <where>
            AND ${field} != '' AND ${field} IS NOT NULL
            AND IE_FLAG=#{ieFlag}
            <if test="declarationDateStart != null and declarationDateStart != '' and
            declarationDateEnd != null and declarationDateEnd != ''">
                AND APP_DATE BETWEEN #{declarationDateStart} AND #{declarationDateEnd}
            </if>
        </where>
        group by ${field},CURRENCY
        order by statisticalContentValue desc
    </select>
    <select id="exportDecStatisticsListTeu" resultType="org.jeecg.modules.business.vo.DecStatisticsVO">
        select dc.CONTAINER_MD statisticalContentValue,
        ${field} statisticalItemsValue
        from dec_head dh inner join dec_container dc on dh.id = dc.DEC_ID
        <where>
            AND ${field} != '' AND ${field} IS NOT NULL
            AND IE_FLAG=#{ieFlag}
            <if test="declarationDateStart != null and declarationDateStart != '' and
            declarationDateEnd != null and declarationDateEnd != ''">
                AND APP_DATE BETWEEN #{declarationDateStart} AND #{declarationDateEnd}
            </if>
        </where>
    </select>
    <select id="customExportDec" resultType="org.jeecg.modules.business.entity.excel.ExportDecExcel">
        select dh.*,
        (case dh.IE_FLAG when 'I' then OVERSEAS_CONSIGNOR_ENAME else OVERSEAS_CONSIGNEE_ENAME end) as overseasConsigneeEname,
               dl.*,dl.`TOTAL` goodsTotal,dl.GOODS_COUNT goodsCount,
        dc.CONTAINER_ID,dld.CERT_CODE bondInvtNo,dl.HSMODEL goodsHsmodel
        from
        dec_head dh left join dec_list dl on dh.ID = dl.DEC_ID
        left join dec_container dc on dc.DEC_ID=dh.ID AND dc.GOODS_NO in (dl.ITEM)
        left join dec_license_docus dld on dld.DEC_ID = dh.ID
        <where>
            and dh.TENANT_ID = #{decHead.tenantId}
            <if test="ids!=null  and ids.size()>0">
                and dh.ID in
                <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="decHead.optUnitName != null and decHead.optUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.OPT_UNIT_NAME LIKE concat('%',#{decHead.optUnitName},'%')
            </if>
            <if test="decHead.seqNo != null and decHead.seqNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.SEQ_NO LIKE concat('%',#{decHead.seqNo},'%')
            </if>
            <if test="decHead.clearanceNo != null and decHead.clearanceNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_NO LIKE concat('%',#{decHead.clearanceNo},'%')
            </if>
            <if test="decHead.billCode != null and decHead.billCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.BILL_CODE LIKE concat('%',#{decHead.billCode},'%')
            </if>
            <if test="decHead.ieFlag != null and decHead.ieFlag != '' and
            (ids == null or ids.size()==0)">
                AND dh.IE_FLAG =#{decHead.ieFlag}
            </if>
            <if test="decHead.tradeTypeCode != null and decHead.tradeTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.TRADE_TYPE_CODE =#{decHead.tradeTypeCode}
            </if>
            <if test="decHead.etpsInnerInvtNo != null and decHead.etpsInnerInvtNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.ETPS_INNER_INVT_NO LIKE concat('%',#{decHead.etpsInnerInvtNo},'%')
            </if>
            <if test="decHead.clearanceType != null and decHead.clearanceType != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_TYPE =#{decHead.clearanceType}
            </if>
            <if test="decHead.hasCd != null and decHead.hasCd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_CD =#{decHead.hasCd}
            </if>
            <if test="decHead.hasYd != null and decHead.hasYd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_YD =#{decHead.hasYd}
            </if>
            <if test="decHead.startAppDate != null and decHead.startAppDate != '' and
                    decHead.lastAppDate != null and decHead.lastAppDate != '' and
            (ids == null or ids.size()==0)">
                AND dh.APP_DATE BETWEEN #{decHead.startAppDate} AND #{decHead.lastAppDate}</if>
            <if test="decHead.shipTypeCode != null and decHead.shipTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.SHIP_TYPE_CODE =#{decHead.shipTypeCode}
            </if>
            <if test="decHead.createPerson != null and decHead.createPerson != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_PERSON LIKE concat('%',#{decHead.createPerson},'%')
            </if>
            <if test="decHead.declarePlace != null and decHead.declarePlace != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_PLACE =#{decHead.declarePlace}
            </if>
            <if test="decHead.startCreateTime != null and decHead.startCreateTime != '' and
                    decHead.lastCreateTime != null and decHead.lastCreateTime != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_TIME BETWEEN #{decHead.startCreateTime} AND #{decHead.lastCreateTime}</if>
            <if test="decHead.startUpdateTime != null and decHead.startUpdateTime != '' and
                    decHead.lastUpdateTime != null and decHead.lastUpdateTime != '' and
            (ids == null or ids.size()==0)">
                AND dh.UPDATE_DATE BETWEEN #{decHead.startUpdateTime} AND #{decHead.lastUpdateTime}</if>
            <if test="decHead.contract != null and decHead.contract != '' and
            (ids == null or ids.size()==0)">
                AND dh.CONTRACT LIKE concat('%',#{decHead.contract},'%')
            </if>
            <if test="decHead.licenceNumber != null and decHead.licenceNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.LICENCE_NUMBER LIKE concat('%',#{decHead.licenceNumber},'%')
            </if>
            <if test="decHead.decStatus != null and decHead.decStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.DEC_STATUS =#{decHead.decStatus}
            </if>
            <if test="decHead.pushStatus != null and decHead.pushStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.PUSH_STATUS =#{decHead.pushStatus}
            </if>
            <if test="decHead.outPortCode != null and decHead.outPortCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.OUT_PORT_CODE =#{decHead.outPortCode}
            </if>
            <if test="decHead.deliverUnitName != null and decHead.deliverUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DELIVER_UNIT_NAME LIKE concat('%',#{decHead.deliverUnitName},'%')
            </if>
            <if test="decHead.recordNumber != null and decHead.recordNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.RECORD_NUMBER LIKE concat('%',#{decHead.recordNumber},'%')
            </if>
            <if test="decHead.declareUnitName != null and decHead.declareUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_UNIT_NAME LIKE concat('%',#{decHead.declareUnitName},'%')
            </if>
            <if test="decHead.icNumber != null and decHead.icNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.IC_NUMBER LIKE concat('%',#{decHead.icNumber},'%')
            </if>
            <if test="decHead.hsname != null and decHead.hsname != '' and
            (ids == null or ids.size()==0)">
                AND dl.HSNAME LIKE concat('%',#{decHead.hsname},'%')
            </if>

        </where>
            order by dh.CREATE_TIME desc
    </select>
    <select id="customExportDecToDecList" resultType="org.jeecg.modules.business.entity.excel.ExportDecExcel">
        select dh.*,
        (case dh.IE_FLAG when 'I' then OVERSEAS_CONSIGNOR_ENAME else OVERSEAS_CONSIGNEE_ENAME end) as overseasConsigneeEname,
        dl.*,dl.`TOTAL` goodsTotal,dl.GOODS_COUNT goodsCount,dld.CERT_CODE bondInvtNo,dl.HSMODEL goodsHsmodel
        from
        dec_head dh left join dec_list dl on dh.ID = dl.DEC_ID
        left join dec_license_docus dld on dld.DEC_ID = dh.ID
        <where>
            and dh.TENANT_ID = #{decHead.tenantId}
            <if test="ids!=null  and ids.size()>0">
                and dh.ID in
                <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="decHead.optUnitName != null and decHead.optUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.OPT_UNIT_NAME LIKE concat('%',#{decHead.optUnitName},'%')
            </if>
            <if test="decHead.seqNo != null and decHead.seqNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.SEQ_NO LIKE concat('%',#{decHead.seqNo},'%')
            </if>
            <if test="decHead.clearanceNo != null and decHead.clearanceNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_NO LIKE concat('%',#{decHead.clearanceNo},'%')
            </if>
            <if test="decHead.billCode != null and decHead.billCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.BILL_CODE LIKE concat('%',#{decHead.billCode},'%')
            </if>
            <if test="decHead.ieFlag != null and decHead.ieFlag != '' and
            (ids == null or ids.size()==0)">
                AND dh.IE_FLAG =#{decHead.ieFlag}
            </if>
            <if test="decHead.tradeTypeCode != null and decHead.tradeTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.TRADE_TYPE_CODE =#{decHead.tradeTypeCode}
            </if>
            <if test="decHead.etpsInnerInvtNo != null and decHead.etpsInnerInvtNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.ETPS_INNER_INVT_NO LIKE concat('%',#{decHead.etpsInnerInvtNo},'%')
            </if>
            <if test="decHead.clearanceType != null and decHead.clearanceType != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_TYPE =#{decHead.clearanceType}
            </if>
            <if test="decHead.hasCd != null and decHead.hasCd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_CD =#{decHead.hasCd}
            </if>
            <if test="decHead.hasYd != null and decHead.hasYd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_YD =#{decHead.hasYd}
            </if>
            <if test="decHead.startAppDate != null and decHead.startAppDate != '' and
                    decHead.lastAppDate != null and decHead.lastAppDate != '' and
            (ids == null or ids.size()==0)">
                AND dh.APP_DATE BETWEEN #{decHead.startAppDate} AND #{decHead.lastAppDate}</if>
            <if test="decHead.shipTypeCode != null and decHead.shipTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.SHIP_TYPE_CODE =#{decHead.shipTypeCode}
            </if>
            <if test="decHead.createPerson != null and decHead.createPerson != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_PERSON LIKE concat('%',#{decHead.createPerson},'%')
            </if>
            <if test="decHead.declarePlace != null and decHead.declarePlace != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_PLACE =#{decHead.declarePlace}
            </if>
            <if test="decHead.startCreateTime != null and decHead.startCreateTime != '' and
                    decHead.lastCreateTime != null and decHead.lastCreateTime != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_TIME BETWEEN #{decHead.startCreateTime} AND #{decHead.lastCreateTime}</if>
            <if test="decHead.contract != null and decHead.contract != '' and
            (ids == null or ids.size()==0)">
                AND dh.CONTRACT LIKE concat('%',#{decHead.contract},'%')
            </if>
            <if test="decHead.licenceNumber != null and decHead.licenceNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.LICENCE_NUMBER LIKE concat('%',#{decHead.licenceNumber},'%')
            </if>
            <if test="decHead.decStatus != null and decHead.decStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.DEC_STATUS =#{decHead.decStatus}
            </if>
            <if test="decHead.pushStatus != null and decHead.pushStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.PUSH_STATUS =#{decHead.pushStatus}
            </if>
            <if test="decHead.outPortCode != null and decHead.outPortCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.OUT_PORT_CODE =#{decHead.outPortCode}
            </if>
            <if test="decHead.deliverUnitName != null and decHead.deliverUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DELIVER_UNIT_NAME LIKE concat('%',#{decHead.deliverUnitName},'%')
            </if>
            <if test="decHead.recordNumber != null and decHead.recordNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.RECORD_NUMBER LIKE concat('%',#{decHead.recordNumber},'%')
            </if>
            <if test="decHead.declareUnitName != null and decHead.declareUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_UNIT_NAME LIKE concat('%',#{decHead.declareUnitName},'%')
            </if>
            <if test="decHead.icNumber != null and decHead.icNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.IC_NUMBER LIKE concat('%',#{decHead.icNumber},'%')
            </if>
            <if test="decHead.startUpdateTime != null and decHead.startUpdateTime != '' and
               decHead.lastUpdateTime != null and decHead.lastUpdateTime != '' and(ids == null or ids.size()==0)">
                AND dh.UPDATE_DATE BETWEEN #{decHead.startUpdateTime} AND #{decHead.lastUpdateTime}</if>
            <if test="decHead.hsname != null and decHead.hsname != '' and
                    (ids == null or ids.size()==0)">
                AND dl.HSNAME LIKE concat('%',#{decHead.hsname},'%')</if>

        </where>
        order by dh.CREATE_TIME desc
    </select>
    <select id="customExportDecToDecHead" resultType="org.jeecg.modules.business.entity.excel.ExportDecExcel">
        select dh.*,
        (case dh.IE_FLAG when 'I' then OVERSEAS_CONSIGNOR_ENAME else OVERSEAS_CONSIGNEE_ENAME end) as overseasConsigneeEname,
        dld.CERT_CODE bondInvtNo
        from
        dec_head dh left join dec_license_docus dld on dld.DEC_ID = dh.ID

        <where>
            and dh.TENANT_ID = #{decHead.tenantId}
            <if test="ids!=null  and ids.size()>0">
                and dh.ID in
                <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="decHead.optUnitName != null and decHead.optUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.OPT_UNIT_NAME LIKE concat('%',#{decHead.optUnitName},'%')
            </if>
            <if test="decHead.seqNo != null and decHead.seqNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.SEQ_NO LIKE concat('%',#{decHead.seqNo},'%')
            </if>
            <if test="decHead.clearanceNo != null and decHead.clearanceNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_NO LIKE concat('%',#{decHead.clearanceNo},'%')
            </if>
            <if test="decHead.billCode != null and decHead.billCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.BILL_CODE LIKE concat('%',#{decHead.billCode},'%')
            </if>
            <if test="decHead.ieFlag != null and decHead.ieFlag != '' and
            (ids == null or ids.size()==0)">
                AND dh.IE_FLAG =#{decHead.ieFlag}
            </if>
            <if test="decHead.tradeTypeCode != null and decHead.tradeTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.TRADE_TYPE_CODE =#{decHead.tradeTypeCode}
            </if>
            <if test="decHead.etpsInnerInvtNo != null and decHead.etpsInnerInvtNo != '' and
            (ids == null or ids.size()==0)">
                AND dh.ETPS_INNER_INVT_NO LIKE concat('%',#{decHead.etpsInnerInvtNo},'%')
            </if>
            <if test="decHead.clearanceType != null and decHead.clearanceType != '' and
            (ids == null or ids.size()==0)">
                AND dh.CLEARANCE_TYPE =#{decHead.clearanceType}
            </if>
            <if test="decHead.hasCd != null and decHead.hasCd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_CD =#{decHead.hasCd}
            </if>
            <if test="decHead.hasYd != null and decHead.hasYd != '' and
            (ids == null or ids.size()==0)">
                AND dh.HAS_YD =#{decHead.hasYd}
            </if>
            <if test="decHead.startAppDate != null and decHead.startAppDate != '' and
                    decHead.lastAppDate != null and decHead.lastAppDate != '' and
            (ids == null or ids.size()==0)">
                AND dh.APP_DATE BETWEEN #{decHead.startAppDate} AND #{decHead.lastAppDate}</if>
            <if test="decHead.shipTypeCode != null and decHead.shipTypeCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.SHIP_TYPE_CODE =#{decHead.shipTypeCode}
            </if>
            <if test="decHead.createPerson != null and decHead.createPerson != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_PERSON LIKE concat('%',#{decHead.createPerson},'%')
            </if>
            <if test="decHead.declarePlace != null and decHead.declarePlace != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_PLACE =#{decHead.declarePlace}
            </if>
            <if test="decHead.startCreateTime != null and decHead.startCreateTime != '' and
                    decHead.lastCreateTime != null and decHead.lastCreateTime != '' and
            (ids == null or ids.size()==0)">
                AND dh.CREATE_TIME BETWEEN #{decHead.startCreateTime} AND #{decHead.lastCreateTime}</if>
            <if test="decHead.contract != null and decHead.contract != '' and
            (ids == null or ids.size()==0)">
                AND dh.CONTRACT LIKE concat('%',#{decHead.contract},'%')
            </if>
            <if test="decHead.licenceNumber != null and decHead.licenceNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.LICENCE_NUMBER LIKE concat('%',#{decHead.licenceNumber},'%')
            </if>
            <if test="decHead.decStatus != null and decHead.decStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.DEC_STATUS =#{decHead.decStatus}
            </if>
            <if test="decHead.pushStatus != null and decHead.pushStatus != '' and
            (ids == null or ids.size()==0)">
                AND dh.PUSH_STATUS =#{decHead.pushStatus}
            </if>
            <if test="decHead.outPortCode != null and decHead.outPortCode != '' and
            (ids == null or ids.size()==0)">
                AND dh.OUT_PORT_CODE =#{decHead.outPortCode}
            </if>
            <if test="decHead.deliverUnitName != null and decHead.deliverUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DELIVER_UNIT_NAME LIKE concat('%',#{decHead.deliverUnitName},'%')
            </if>
            <if test="decHead.recordNumber != null and decHead.recordNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.RECORD_NUMBER LIKE concat('%',#{decHead.recordNumber},'%')
            </if>
            <if test="decHead.declareUnitName != null and decHead.declareUnitName != '' and
            (ids == null or ids.size()==0)">
                AND dh.DECLARE_UNIT_NAME LIKE concat('%',#{decHead.declareUnitName},'%')
            </if>
            <if test="decHead.icNumber != null and decHead.icNumber != '' and
            (ids == null or ids.size()==0)">
                AND dh.IC_NUMBER LIKE concat('%',#{decHead.icNumber},'%')
            </if>
            <if test="decHead.startUpdateTime != null and decHead.startUpdateTime != '' and
               decHead.lastUpdateTime != null and decHead.lastUpdateTime != '' and(ids == null or ids.size()==0)">
                AND dh.UPDATE_DATE BETWEEN #{decHead.startUpdateTime} AND #{decHead.lastUpdateTime}</if>

        </where>
        order by dh.CREATE_TIME desc
    </select>


    <select id="getChartDec" resultType="org.jeecg.modules.business.vo.ChartDecStatisticsVO">
        select count(*) decCount,
               sum(CONTAINER_NUM) containerCount,
               sum(TOTAL) tradeVolume,
               DATE_FORMAT(APP_DATE, '%Y-%m') dateValue,
               CURRENCY currency
        from dec_head
        where IE_FLAG = #{ieFlag}
        and YEAR(APP_DATE) = #{year}
        and TENANT_ID = #{tenantId}
        GROUP BY  DATE_FORMAT(APP_DATE, '%Y-%m'),CURRENCY
    </select>
    <select id="getChartDecBonded" resultType="org.jeecg.modules.business.vo.ChartDecStatisticsVO">
        SELECT
            count(*) decCount,
            DATE_FORMAT( CREATE_TIME, '%Y-%m' ) dateValue
        FROM
            dec_head
        WHERE
            IE_FLAG = #{ieFlag}
                AND YEAR ( CREATE_TIME ) = #{year}
        GROUP BY
            DATE_FORMAT(
            CREATE_TIME,
            '%Y-%m')
    </select>
    <select id="getChartInvtBonded" resultType="org.jeecg.modules.business.vo.ChartDecStatisticsVO">
        SELECT
            count(*) invtCount,
            DATE_FORMAT( CREATE_DATE, '%Y-%m' ) dateValue
        FROM
            nems_invt_head
        WHERE
            IMPEXP_MARKCD = #{ieFlag}
                AND YEAR ( CREATE_DATE ) = #{year}
        GROUP BY
            DATE_FORMAT(
            CREATE_DATE,
            '%Y-%m')
    </select>
    <select id="getChartPassBonded" resultType="org.jeecg.modules.business.vo.ChartDecStatisticsVO">
        SELECT
            count(*) passCount,
            DATE_FORMAT( CREATE_DATE, '%Y-%m' ) dateValue
        FROM
            pass_port_head
        WHERE
            IO_TYPECD = #{ieFlag}
                AND YEAR ( CREATE_DATE ) = #{year}
        GROUP BY
            DATE_FORMAT(
            CREATE_DATE,
            '%Y-%m')
    </select>

    <insert id="saveSysAnnouncement">
        INSERT INTO sys_announcement (ID, TITILE, SENDER, MSG_CATEGORY, SEND_STATUS,SEND_TIME,
                                      DEL_FLAG,CREATE_BY,CREATE_TIME,TENANT_ID,MSG_CONTENT,USER_IDS)
        VALUES (#{id},#{titile},#{sender},#{msgCategory},'1',#{sendTime},#{delFlag},#{createBy},#{createTime}
               ,#{tenantId},#{msgContent},#{userIds})
    </insert>
    <insert id="saveSysAnnouncementSend">
        INSERT INTO sys_announcement_send (ID, ANNT_ID, USER_ID,CREATE_BY,CREATE_TIME,MSG_CATEGORY)
        VALUES (#{id},#{anntId},#{userIds},#{createBy},#{createTime},#{msgCategory})
    </insert>
    <select id="getDecHeadByManifestInfo" resultType="org.jeecg.modules.business.entity.DecHead">
        select * from dec_head
        <where>
            and (DEC_STATUS in
            <foreach collection="decStatus" item="decStatus" open="(" separator="," close=")">
                #{decStatus}
            </foreach>
                 OR DEC_STATUS IS NULL OR DEC_STATUS = "")

            <if test="tenandId != null and tenandId !=''">
              and TENANT_ID = #{tenandId}
            </if>
        and CREATE_TIME > DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        and (HAS_CD != 1 OR HAS_YD !=1)

        </where>


    </select>
    <select id="listDecHeadsByCond" resultType="org.jeecg.modules.business.entity.DecHead">
        SELECT
            *
        FROM
            `dec_head`
        WHERE
            SEQ_NO = #{seqNo}
          AND TENANT_ID = #{tenantId}
    </select>
    <select id="queryDecHeadPageList" resultType="org.jeecg.modules.business.entity.DecHead">
        SELECT DEC_HEAD.* FROM DEC_HEAD
        LEFT JOIN DEC_LIST ON DEC_HEAD.ID = DEC_LIST.DEC_ID
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
    </select>
    <select id="GetDecHeadById" resultType="org.jeecg.modules.business.entity.DecHead">
        SELECT
            *
        FROM
            `dec_head`
        WHERE
            ID = #{id}
    </select>
    <select id="getDecByContainerAndBillCode" resultType="org.jeecg.modules.business.entity.DecHead">
        SELECT
            a.*
        FROM
            dec_container b
                LEFT JOIN `dec_head` a ON a.ID = b.DEC_ID
        WHERE
            b.CONTAINER_ID = #{cXh}
          AND a.BILL_CODE = #{cTdh}
    </select>
    <select id="getDistrictCodeByDeliverUnitName" resultType="java.lang.String">
        SELECT
        b.DISTRICT_CODE
        FROM
        `dec_list` b
        LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        <where>
            a.DELIVER_UNIT_NAME = #{deliverUnitName}
            AND DISTRICT_CODE IS NOT NULL
            AND DISTRICT_CODE != ''
            AND a.DELIVER_UNIT_NAME IS NOT NULL
            AND a.DELIVER_UNIT_NAME != ''
        </where>
        ORDER BY
        a.CREATE_TIME DESC
        LIMIT 1
    </select>
    <select id="getDistrictCodeByOptUnitName" resultType="java.lang.String">
        SELECT
        b.DISTRICT_CODE
        FROM
        `dec_list` b
        LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        <where>
            a.OPT_UNIT_NAME = #{optUnitName}
            AND DISTRICT_CODE IS NOT NULL
            AND DISTRICT_CODE != ''
            AND a.OPT_UNIT_NAME IS NOT NULL
            AND a.OPT_UNIT_NAME != ''
        </where>
        ORDER BY
        a.CREATE_TIME DESC
        LIMIT 1
    </select>
    <select id="getHscodeByHistory" resultType="java.lang.String">
        SELECT
            b.HSCODE
        FROM
            `dec_list` b
                LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        WHERE
            b.HSNAME = #{hsname}
          AND b.HSNAME IS NOT NULL
          AND b.HSNAME != ''
          AND b.HSCODE IS NOT NULL
          AND b.HSCODE != ''
        ORDER BY
            a.CREATE_TIME DESC
            LIMIT 1
    </select>
    <select id="queryListForCheckHsmodel" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            b.*
        FROM
            dec_list b
                LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        WHERE
            ( a.OPT_UNIT_NAME = #{optUnitName} OR a.DELIVER_UNIT_NAME = #{deliverUnitName} )
          AND b.HSCODE = #{hscode} AND HSNAME = #{hsname}
          AND b.HSMODEL IS NOT NULL
          AND b.HSMODEL != ''
          AND a.DEC_STATUS >= 9
        <if test="hsmodelarrays != null and hsmodelarrays.size() > 0">
            AND (
            <foreach collection="hsmodelarrays" index="index" item="item" open="(" separator="AND" close=")">
                <if test="item != null and item != ''">
                    b.HSMODEL LIKE CONCAT('%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        ORDER BY
            b.ID ASC
	    LIMIT 1
    </select>
    <select id="queryListForCheckHsmodel_" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            b.*
        FROM
            dec_list b
        LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        WHERE
            a.DECLARE_UNIT_NAME = #{declareUnitName}
        AND b.HSCODE = #{hscode} AND HSNAME = #{hsname}
        AND b.HSMODEL IS NOT NULL
        AND b.HSMODEL != ''
        AND a.DEC_STATUS >= 9
        <if test="hsmodelarrays != null and hsmodelarrays.size() > 0">
            AND (
            <foreach collection="hsmodelarrays" index="index" item="item" open="(" separator="AND" close=")">
                <if test="item != null and item != ''">
                    b.HSMODEL LIKE CONCAT('%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        ORDER BY
            b.ID ASC
        LIMIT 1
    </select>
    <select id="queryListForDestCode" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            b.*
        FROM
            dec_list b
                LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        WHERE
            ( a.OPT_UNIT_NAME = #{optUnitName} OR a.DELIVER_UNIT_NAME = #{deliverUnitName} )
          AND b.HSCODE = #{hscode} AND b.HSNAME = #{hsname}
          AND b.DEST_CODE IS NOT NULL
          AND b.DEST_CODE != ''
          AND a.DEC_STATUS >= 9
        <if test="districtCode != null and districtCode !=''">
            AND b.DISTRICT_CODE = #{districtCode}
        </if>
        ORDER BY
            b.ID ASC
	    LIMIT 1
    </select>
    <select id="queryListForDistrictCode" resultType="org.jeecg.modules.business.entity.DecList">
        SELECT
            b.*
        FROM
            dec_list b
            LEFT JOIN dec_head a ON a.ID = b.DEC_ID
        WHERE
            ( a.OPT_UNIT_NAME = #{optUnitName} OR a.DELIVER_UNIT_NAME = #{deliverUnitName} )
            AND b.HSCODE = #{hscode} AND b.HSNAME = #{hsname}
            AND b.DISTRICT_NAME LIKE CONCAT('%',#{districtCode},'%')
        ORDER BY
            CASE
                WHEN a.DEC_STATUS >= 9 THEN
                    0 ELSE 1
                END ASC,
            CASE
                WHEN b.DEST_NAME IS NOT NULL
                    AND b.DEST_NAME != '' THEN
                    0 ELSE 1
                END ASC,
            b.ID ASC
        LIMIT 1
    </select>
</mapper>
