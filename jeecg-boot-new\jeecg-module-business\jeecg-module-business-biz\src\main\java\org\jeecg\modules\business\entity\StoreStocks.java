package org.jeecg.modules.business.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 仓库库存建账表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Data
@Accessors(chain = true)
@TableName("store_stocks")
public class StoreStocks implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 仓库编码
     */
    @Excel(name = "仓库", width = 18)
    @TableField("STORE_CODE")
    private String storeCode;

    /**
     * 货主
     */
    @Excel(name = "货主", width = 20)
    @TableField("CUSTOMER")
    private String customer;

    /**
     * 项号
     */
    @Excel(name = "项号", width = 13)
    @TableField("ITEM_NUMBER")
    private String itemNumber;

    /**
     * 批次号/备案号/备案项号
     */
    @Excel(name = "报关单号（批次号）", width = 22)
    @TableField("BATCH_NO")
    private String batchNo;
    /**
     * 清单编号 （多个逗号分割）
     */
    @Excel(name = "清单编号", width = 20)
    private String bondInvtNo;
    /**
     * 运单号（多个逗号分割）
     */
    private String billNo;

    /**
     * 商品编码
     */
    @Excel(name = "商品编码", width = 18)
    private String hscode;

    /**
     * 物料号
     */
    @Excel(name = "物料号", width = 15)
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 库区编码20240822
     */
    @Excel(name = "库区", width = 18)
    @TableField("AREA_CODE")
    private String areaCode;

    /**
     * 库区名称20240822
     */
    @TableField("AREA_NAME")
    private String areaName;

    /**
     * 关联储位编码
     */
    @Excel(name = "储位", width = 18)
    @TableField("SPACE_CODE")
    private String spaceCode;

    /**
     * 料件类型：1维修货物、2维修用保税料件、3旧件/坏件
     * 20240813
     */
    @Excel(name = "料件类型", width = 18)
    @TableField("DETAIL_TYPE")
    private String detailType;

    /**
     * 储位名称
     */
    @TableField("SPACE_NAME")
    private String spaceName;

    /**
     * 品名
     */
    @Excel(name = "品名", width = 20)
    @TableField("PN")
    private String pn;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号", width = 25)
    @TableField("MODEL")
    private String model;

    /**
     * 期初数量/库存数量
     */
    @Excel(name = "库存数量", width = 18)
    @TableField("BEGIN_QTY")
    private BigDecimal beginQty = BigDecimal.ZERO;

    /**
     * 匹配占用数量(物流账册用)
     */
    @Excel(name = "占用数量", width = 18)
    @TableField("OCCUPY_QTY")
    private BigDecimal occupyQty = BigDecimal.ZERO;

    /**
     * 预核扣数量(物流账册用)
     */
    @TableField("PREDISTRIBUTION")
    private BigDecimal predistribution = BigDecimal.ZERO;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位", width = 15)
    @TableField("QUNIT")
    private String qunit;

    /**
     * 期初金额
     */
    @TableField("BEGIN_AMOUNT")
    private BigDecimal beginAmount;

    /**
     * 期初单价
     */
    @TableField("BEGIN_DCLUPRCAMT")
    private BigDecimal beginDcluprcamt;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    @TableField("REMARK")
    private String remark;

    /**
     * 租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建日期
     */
    @Excel(name = "创建日期", exportFormat = DatePattern.NORM_DATETIME_PATTERN, width = 23)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 最后更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 最后更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    /**
     * 版本号
     */
    @Version
    @TableField("VERSION")
    private Integer version;



    /**
     * 变动加减数量
     */
    @TableField(exist = false)
    private BigDecimal changeQty;
    /**
     * 出入库类型 I入库 E出库
     */
    @TableField(exist = false)
    private String ieFlag;

    /**
     * 仓库名称
     */
    @TableField(exist = false)
    private String storeName;

    /**
     * 可用库存数量=库存数量-占用数量
     */
    @TableField(exist = false)
    private BigDecimal availableQty;

    // 20250616
    @TableField(exist = false)
    private BigDecimal weight;

    /**
     * 仓库名称
     */
    @TableField(exist = false)
    private String customerStr;

    @TableField(exist = false)
    private Map<String, Object> summary;
    /**
     *生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date manufactureDate;
    /**
     *有效到期日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;

}
