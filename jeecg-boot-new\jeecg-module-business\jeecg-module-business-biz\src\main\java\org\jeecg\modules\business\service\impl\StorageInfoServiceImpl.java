package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.oss.OssBootUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.InOrOutStorageDetailDTO;
import org.jeecg.modules.business.entity.dto.StoreToInvtDTO;
import org.jeecg.modules.business.entity.enums.StockTypeEnum;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.util.ExcelToPdfUtil;
import org.jeecg.modules.business.util.PrintUtil;
import org.jeecg.modules.business.util.excel.ExcelExportStylerBorderImpl;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.FileUtil;
import org.jeecgframework.poi.excel.export.styler.ExcelExportStylerColorImpl;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static org.jeecg.common.constant.CommonConstant.E;
import static org.jeecg.common.constant.CommonConstant.I;
import static org.jeecg.modules.business.util.Excel2Pdf.excel2pdf;

/**
 * <p>
 * 出入库单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
@Service
public class StorageInfoServiceImpl extends ServiceImpl<StorageInfoMapper, StorageInfo> implements IStorageInfoService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private IStorageDetailService storageDetailService;
    @Autowired
    private StorageDetailMapper storageDetailMapper;
    @Autowired
    private IStoreStocksService storeStocksService;
    @Autowired
    private StoreInfoMapper storeInfoMapper;
    @Autowired
    private PtsEmsHeadMapper emsHeadMapper;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private IStoreSpaceService storeSpaceService;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private ICommissionerService commissionerService;
    @Autowired
    private NemsInvtHeadMapper nemsInvtHeadMapper;
    @Autowired
    private NemsInvtListMapper nemsInvtListMapper;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private AppHeadTypeMapper appHeadTypeMapper;
    @Autowired
    private AppGoodsTypeMapper appGoodsTypeMapper;
    @Autowired
    private IStockHeadTypeService stockHeadTypeService;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private IAttachmentsInfoService attachmentsInfoService;
    @Autowired
    private StoreAreaMapper storeAreaMapper;
    @Autowired
    private StoreSpaceMapper storeSpaceMapper;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private StorageRepairOrderDetailMapper repairOrderDetailMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private CommissionerMapper commissionerMapper;

    @Value("${jeecg.path.upload}")
    private String upLoadPath;

    /**
     * @param pageNo
     * @param pageSize
     * @param storageInfo
     * @return
     */
    @Override
    public Result<?> queryPageList(Integer pageNo, Integer pageSize, StorageInfo storageInfo, HttpServletRequest req) {
//        Date appDate = storageInfo.getAppDate();
//        storageInfo.setAppDate(null);
//        String isReturn = storageInfo.getIsReturn();
//        storageInfo.setIsReturn(null);
//        QueryWrapper<StorageInfo> queryWrapper = QueryGenerator.initQueryWrapper(storageInfo, req.getParameterMap());
//        if (isNotBlank(isReturn)) {
//            queryWrapper.lambda().eq(StorageInfo::getIsReturn, isReturn);
//        } else {
//            queryWrapper.lambda().and(i -> i.eq(StorageInfo::getIsReturn, "0").or().isNull(StorageInfo::getIsReturn).or().eq(StorageInfo::getIsReturn, ""));
//        }
//        if (isNotEmpty(appDate)) {
//            queryWrapper.lambda().apply("date_format(APP_DATE,'%Y-%m-%d') = '" + DateUtil.format(appDate, "yyyy-MM-dd") + "'");
//        }
        /*
         * 企业数据隔离
         * 2024/8/21 下午3:46@ZHANGCHAO
         */
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
//            queryWrapper.lambda().eq(StorageInfo::getCustomer, loginUser.getUserTypeRel());
            storageInfo.setCustomer(loginUser.getUserTypeRel());
        }
        storageInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
        Page<StorageInfo> page = new Page<>(pageNo, pageSize);
        IPage<StorageInfo> pageList = baseMapper.queryPageList(page, storageInfo);
        if (isNotEmpty(pageList.getRecords())) {
            // 2024/6/11 上午9:23@ZHANGCHAO 追加/变更/完善：加单据类型！！
            List<String> orderNotes = pageList.getRecords().stream().map(StorageInfo::getOrderNo).collect(java.util.stream.Collectors.toList());
            Map<String, List<OrderInfo>> map = new HashMap<>(16);
            if (isNotEmpty(orderNotes)) {
                List<OrderInfo> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>()
                        .in(OrderInfo::getOrderProtocolNo, orderNotes));
                map = orderInfoList.stream().collect(Collectors.groupingBy(OrderInfo::getOrderProtocolNo));
            }

            Map<String, List<Commissioner>> commissionerMap = new HashMap<>();
            List<String> customerList = pageList.getRecords().stream().map(StorageInfo::getCustomer).filter(CharSequenceUtil::isNotBlank).distinct().collect(java.util.stream.Collectors.toList());
            if (isNotEmpty(customerList)) {
                List<Commissioner> commissionerList = commissionerService.listByIds(customerList);
                commissionerMap = commissionerList.stream().collect(Collectors.groupingBy(Commissioner::getId));
            }
            List<String> storageNos = pageList.getRecords().stream().map(StorageInfo::getStorageNo).distinct().collect(java.util.stream.Collectors.toList());
            List<StorageDetail> storageDetailAllList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                    .in(StorageDetail::getStorageNo, storageNos));
            Map<String, List<StorageDetail>> storageDetailMap = storageDetailAllList.stream().collect(java.util.stream.Collectors.groupingBy(StorageDetail::getStorageNo));
            for (StorageInfo storage : pageList.getRecords()) {
                if (isNotBlank(storage.getCustomer())) {
                    String tenantName = null;
                    try {
                        // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                        Result<Tenant> tenant = sysBaseApi.getTenantById(storage.getCustomer());
                        if (isNotEmpty(tenant.getResult())) {
                            tenantName = tenant.getResult().getName();
                        }
                    } catch (Exception e) {
                        log.error("获取租户名出现异常：" + e.getMessage());
                    }
                    Commissioner commissioner = isNotEmpty(commissionerMap) && isNotEmpty(commissionerMap.get(storage.getCustomer())) ? commissionerMap.get(storage.getCustomer()).get(0) : null;
                    if (isNotEmpty(commissioner)) {
                        storage.setCustomerStr(commissioner.getCommissionerFullName());
                    } else {
                        storage.setCustomerStr(tenantName);
                    }
                }
                List<StorageDetail> storageDetailList = storageDetailMap.get(storage.getStorageNo());
                // storageDetailList按批次号按数字排序
//                storageDetailList = storageDetailList.stream()
//                        .sorted(Comparator.comparing(storageDetail -> {
//                            try {
//                                // 将批次号转换为整数，如果转换失败可以捕获异常
//                                return Integer.parseInt(storageDetail.getItemNumber());
//                            } catch (NumberFormatException e) {
//                                // 处理无法转换为数字的情况，这里返回 0 或其他默认值
//                                return 0;
//                            }
//                        }))
//                        .collect(Collectors.toList());
                //20241122按照货物自然序号排序
                storageDetailList = storageDetailList.stream()
                        .sorted(Comparator.comparing(i->null==i.getGdsseqNo()?0:i.getGdsseqNo()))
                        .collect(Collectors.toList());

                // 2024/1/24 14:32@ZHANGCHAO 追加/变更/完善：换一种方式，因为batchNo可能为空的哟！！
                // 如果是出库，实际库存=库存-占用
                if ("E".equals(storage.getIeFlag())) {
                    if (isNotEmpty(storageDetailList)) {
                        for (StorageDetail storageDetail : storageDetailList) {
                            storageDetail.setCustomer(storage.getCustomer());
                            storageDetail.setIeFlag(storage.getIeFlag());
                            Result<StoreStocks> result = storeStocksService.getStockByDetail(storageDetail);
                            StoreStocks storeStocks = null;
                            if (result.isSuccess()) {
                                storeStocks = result.getResult();
                            }
                            if (isNotEmpty(storeStocks)) {
                                BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                                BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                                storageDetail.setStockQty(beginQty); // 实际库存数量
                                storageDetail.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
                                storageDetail.setOccupyQty(occupyQty); // 占用数量
                            }
                        }
                    }
                }
                storage.setStorageDetailList(storageDetailList);
                // 2024/3/6 14:42@ZHANGCHAO 追加/变更/完善：是否生成了出入库单（出入区）
                List<StockHeadType> stockHeads = stockHeadTypeService.list(new LambdaQueryWrapper<StockHeadType>()
                        .like(StockHeadType::getStorageInfoId, storage.getId()));
                if (isNotEmpty(stockHeads)) {
                    storage.setStockHeadTypeIds(stockHeads.stream().map(i -> i.getId().toString()).collect(Collectors.joining(",")));
                }
                // 2024/6/11 上午9:26@ZHANGCHAO 追加/变更/完善：加单据类型！！
                if (isNotEmpty(map) && map.containsKey(storage.getOrderNo())) {
                    List<OrderInfo> orderItems = map.get(storage.getOrderNo());
                    if (orderItems != null && !orderItems.isEmpty()) {
                        storage.setOrderType(orderItems.get(0).getOrderType());
                    } else {
                        storage.setOrderType(null);
                    }
                } else {
                    storage.setOrderType(null);
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 保存出入库单信息
     *
     * @param storageInfo 存储信息
     * @return 返回结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStorageInfo(StorageInfo storageInfo) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isBlank(storageInfo.getIeFlag())) {
            return Result.error("未知的出入库单类型！");
        }
        // 新增
        if (isBlank(storageInfo.getId())) {
            // 2024/9/14 15:04@ZHANGCHAO 追加/变更/完善：针对维修单同时生成两票出库单的情况！！
            if (isNotBlank(storageInfo.getRelRepairNos())) {
                List<StorageInfo> isExist = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                        .eq(StorageInfo::getRelRepairNos, storageInfo.getRelRepairNos())
                        .eq(StorageInfo::getIeFlag, "E"));
                if (isNotEmpty(isExist)) {
                    return Result.error("维修单[" + storageInfo.getRelRepairNos() + "]已生成过维修货物出库单，请检查！");
                }
            }
            String storageNo = "";
            if ("I".equals(storageInfo.getIeFlag())) {
                storageNo = serialNumberService.getSerialnumberByCustomerCode("BPW", 4);
            } else if ("E".equals(storageInfo.getIeFlag())) {
                storageNo = serialNumberService.getSerialnumberByCustomerCode("WS", 4);
            }
            storageInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
            storageInfo.setStorageNo(storageNo);
            storageInfo.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageInfo.setCreateDate(new Date());
            baseMapper.insert(storageInfo);
            // 编辑
        } else {
            storageInfo.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageInfo.setUpdateDate(new Date());
            baseMapper.updateById(storageInfo);
        }

        if (isNotEmpty(storageInfo.getStorageDetailList())) {
            List<StorageDetail> oldStorageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                    .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
            if (isNotEmpty(oldStorageDetailList)) {
                // 使用 Stream 进行过滤
                List<StorageDetail> dels = oldStorageDetailList.stream()
                        .filter(item -> storageInfo.getStorageDetailList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (StorageDetail storageDetail : dels) {
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setIeFlag(storageInfo.getIeFlag());
                        // 2024/1/22 9:22@ZHANGCHAO 追加/变更/完善：删除商品时解除占用！！
                        // 2024/1/27 22:53@ZHANGCHAO 追加/变更/完善：占用放到审核里！！
//                        if ("E".equals(storageInfo.getIeFlag())) {
//                            Result<?> deOccupyHandleResult = storeStocksService.deOccupyHandle(storageDetail);
//                            if (!deOccupyHandleResult.isSuccess()) {
//                                throw new RuntimeException(deOccupyHandleResult.getMessage());
//                            }
//                        }
                        storageDetailService.removeById(storageDetail.getId());
                    }
                }
            }
            for (StorageDetail storageDetail : storageInfo.getStorageDetailList()) {
                if (isEmpty(storageDetail.getId())) {
                    storageDetail.setStorageNo(storageInfo.getStorageNo());
                    storageDetail.setStoreCode(storageInfo.getStoreCode());
                    storageDetail.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageDetail.setCreateDate(new Date());
                    storageDetail.setTenantId(Long.valueOf(TenantContext.getTenant()));
                    // 2024/8/7 上午11:46@ZHANGCHAO 追加/变更/完善：入库单表体料件类型：1维修货物、2维修用保税料件、3旧件/坏件
                    if (isBlank(storageDetail.getDetailType())) {
                        if (isNotBlank(storageDetail.getPn())) {
                            if (storageDetail.getPn().contains("（待修复）")
                                    || storageDetail.getPn().contains("(待修复)")
                                    || storageDetail.getPn().contains("(待修复）")
                                    || storageDetail.getPn().contains("（待修复)")) {
                                storageDetail.setDetailType("1");
                            } else if (storageDetail.getPn().contains("(维修用料件)")
                                    || storageDetail.getPn().contains("（维修用料件）")
                                    || storageDetail.getPn().contains("(维修用料件）")
                                    || storageDetail.getPn().contains("（维修用料件)")) {
                                storageDetail.setDetailType("2");
                            } else if (storageDetail.getPn().contains("旧件") || storageDetail.getPn().contains("坏件")) {
                                storageDetail.setDetailType("3");
                            } else {
                                storageDetail.setDetailType(null);
                            }
                        }
                    }
                    storageDetailService.save(storageDetail);
                    // 2024/1/11 16:22@ZHANGCHAO 追加/变更/完善：进口核注单生成的入库单，回填核注单数据！！
                    if (storageInfo.isFromInvt()) {
                        NemsInvtList nemsInvtList = nemsInvtListMapper.selectById(storageDetail.getInvtListId());
                        if (isNotEmpty(nemsInvtList)) {
                            if (isNotBlank(nemsInvtList.getStorageNo())) {
                                throw new RuntimeException("核注单[" + nemsInvtList.getInvId() + "]已生成过入库单，无法重复生成！");
                            }
                        }
                        nemsInvtListMapper.update(null , new UpdateWrapper<NemsInvtList>().lambda()
                                .set(NemsInvtList::getStorageNo, storageDetail.getStorageNo())
                                .set(NemsInvtList::getStorageDetailId, storageDetail.getId())
                                .eq(NemsInvtList::getId, storageDetail.getInvtListId()));
                    }
                    // 2024/1/22 9:44@ZHANGCHAO 追加/变更/完善：出区新增时，加占用数量！！
                    // 2024/1/27 22:54@ZHANGCHAO 追加/变更/完善：占用放到审核里！！
//                    if ("E".equals(storageInfo.getIeFlag())) {
//                        storageDetail.setCustomer(storageInfo.getCustomer());
//                        storageDetail.setIeFlag(storageInfo.getIeFlag());
//                        Result<?> addOccupyHandleResult = storeStocksService.addOccupyHandle(storageDetail);
//                        if (addOccupyHandleResult.isSuccess()) {
//                            storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
//                                    .set(StorageDetail::getStocksFlowId, addOccupyHandleResult.getResult())
//                                    .eq(StorageDetail::getId, storageDetail.getId()));
//                        } else {
//                            throw new RuntimeException(addOccupyHandleResult.getMessage());
//                        }
//                    }
                    // 编辑时，需要解除占用，重新占用
                } else {
                    storageDetail.setStoreCode(storageInfo.getStoreCode());
                    storageDetail.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageDetail.setUpdateDate(new Date());
                    // 2024/8/7 上午11:46@ZHANGCHAO 追加/变更/完善：入库单表体料件类型：1维修货物、2维修用保税料件、3旧件/坏件
                    if (isBlank(storageDetail.getDetailType())) {
                        if (isNotBlank(storageDetail.getPn())) {
                            if (storageDetail.getPn().contains("（待修复）")
                                    || storageDetail.getPn().contains("(待修复)")
                                    || storageDetail.getPn().contains("(待修复）")
                                    || storageDetail.getPn().contains("（待修复)")) {
                                storageDetail.setDetailType("1");
                            } else if (storageDetail.getPn().contains("(维修用料件)")
                                    || storageDetail.getPn().contains("（维修用料件）")
                                    || storageDetail.getPn().contains("(维修用料件）")
                                    || storageDetail.getPn().contains("（维修用料件)")) {
                                storageDetail.setDetailType("2");
                            } else if (storageDetail.getPn().contains("旧件") || storageDetail.getPn().contains("坏件")) {
                                storageDetail.setDetailType("3");
                            } else {
                                storageDetail.setDetailType(null);
                            }
                        }
                    }
                    storageDetailService.updateById(storageDetail);
                    // 2024/1/22 16:04@ZHANGCHAO 追加/变更/完善：需要解除占用，重新占用！！
                    // 2024/1/27 22:54@ZHANGCHAO 追加/变更/完善：占用放到审核里！！
//                    if ("E".equals(storageInfo.getIeFlag())) {
//                        storageDetail.setCustomer(storageInfo.getCustomer());
//                        storageDetail.setIeFlag(storageInfo.getIeFlag());
//                        // 1.解除占用
//                        Result<?> deOccupyHandleResult = storeStocksService.deOccupyHandle(storageDetail);
//                        if (!deOccupyHandleResult.isSuccess()) {
//                            throw new RuntimeException(deOccupyHandleResult.getMessage());
//                        }
//                        // 2.重新占用
//                        Result<?> addOccupyHandleResult = storeStocksService.addOccupyHandle(storageDetail);
//                        if (addOccupyHandleResult.isSuccess()) {
//                            storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
//                                    .set(StorageDetail::getStocksFlowId, addOccupyHandleResult.getResult())
//                                    .eq(StorageDetail::getId, storageDetail.getId()));
//                        } else {
//                            throw new RuntimeException(addOccupyHandleResult.getMessage());
//                        }
//                    }
                }
            }
        }

        // 2024/6/11 上午10:54@ZHANGCHAO 追加/变更/完善：保存附件！！
        if (isNotEmpty(storageInfo.getAttachmentList())) {
            for (AttachmentsInfo attachmentsInfo : storageInfo.getAttachmentList()) {
                AttachmentsInfo atta = attachmentsInfoService.getOne(new LambdaQueryWrapper<AttachmentsInfo>()
                        .eq(AttachmentsInfo::getRelationId, storageInfo.getId())
                        .eq(AttachmentsInfo::getTenantId, storageInfo.getTenantId())
                        .eq(AttachmentsInfo::getAttachmentsFileType, attachmentsInfo.getAttachmentsFileType())
                        .eq(AttachmentsInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
                if (atta == null) {
//                    新建附件信息
                    attachmentsInfo.setId(null);
                    attachmentsInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
                    attachmentsInfo.setRelationId(storageInfo.getId());
                    attachmentsInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
                    attachmentsInfo.setUpdateTime(new Date());
                    attachmentsInfoService.save(attachmentsInfo);
                } else {
//                    编辑
                    attachmentsInfo.setId(atta.getId());
                    attachmentsInfo.setCreateBy(atta.getCreateBy());
                    attachmentsInfo.setCreateTime(atta.getCreateTime());
                    LambdaUpdateWrapper<AttachmentsInfo> updateWrapper = new UpdateWrapper().lambda();
                    updateWrapper.last(CommonUtils.SetUpdateSqlCondition(attachmentsInfo.getUpdateTime(), attachmentsInfo.getId()));
                    attachmentsInfoService.update(attachmentsInfo, updateWrapper);
                }
            }
        }
        StorageInfo resultStorageInfo= baseMapper.selectById(storageInfo.getId());
        resultStorageInfo.setStorageDetailList(storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .eq(StorageDetail::getStorageNo, resultStorageInfo.getStorageNo())));
        resultStorageInfo.setAttachmentList(attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, storageInfo.getId())));
        // 2024/8/13 下午3:06@ZHANGCHAO 追加/变更/完善：保税维修出库单自动生成拆下件/旧件的入库单！
//        if (isNotEmpty(storageInfo.getIsAutoInStorage()) && storageInfo.getIsAutoInStorage()) {
//            generateInStorageAuto(resultStorageInfo);
//        }
        return Result.ok(resultStorageInfo);
    }

    /**
     * 保税维修出库单自动生成拆下件/旧件的入库单！
     *
     * @param storageInfo
     * @return void
     * <AUTHOR>
     * @date 2024/8/13 下午3:07
     */
    private void generateInStorageAuto(StorageInfo storageInfo) {
        StorageInfo storageInfoIn = new StorageInfo();
        BeanUtil.copyProperties(storageInfo, storageInfoIn, CopyOptions.create().ignoreNullValue());
        storageInfoIn.setId(null);
        storageInfoIn.setIeFlag("I");
        storageInfoIn.setRelStorageNo(storageInfo.getStorageNo());
        String storageNo = serialNumberService.getSerialnumberByCustomerCode("BPW", 4);
        storageInfoIn.setStorageNo(storageNo);
        baseMapper.insert(storageInfoIn);
        if (isNotEmpty(storageInfo.getStorageDetailList())) {
            storageInfo.getStorageDetailList().forEach(storageDetail -> {
                // 生成维修用料件对应的拆下旧件的入库单
                if ("2".equals(storageDetail.getDetailType())) {
                    StorageDetail storageDetailIn = new StorageDetail();
                    BeanUtil.copyProperties(storageDetail, storageDetailIn, CopyOptions.create().ignoreNullValue());
                    storageDetailIn.setId(null);
                    storageDetailIn.setStorageNo(storageNo);
                    storageDetailIn.setDetailType("3"); // 拆下件旧件
                    storageDetailService.save(storageDetailIn);
                }
            });
        }
    }

    /**
     * 根据ID获取存储信息
     *
     * @param id 存储信息的ID
     * @return 返回一个Result对象，其中包含存储信息；如果ID无效，则返回null
     */
    @Override
    public Result<?> getStorageInfoById(String id) {
        StorageInfo storageInfo = baseMapper.selectById(id);
        if (isEmpty(storageInfo)) {
            return Result.error("未找到ID为" + id + "的出入库单信息，请刷新页面重试！");
        }
        if (isNotBlank(storageInfo.getCustomer())) {
            String tenantName = null;
            try {
                // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageInfo.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：" + e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageInfo.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageInfo.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageInfo.setCustomerStr(tenantName);
            }
        }
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
        if (isNotEmpty(storageDetailList)) {
            for (StorageDetail storageDetail : storageDetailList) {
                storageDetail.setCustomer(storageInfo.getCustomer());
                storageDetail.setIeFlag(storageInfo.getIeFlag());
                Result<?> result = storeStocksService.getStoreStocksBy4ModelCond(storageDetail);
                StoreStocks storeStocks = null;
                if (result.isSuccess()) {
                    storeStocks = (StoreStocks) result.getResult();
                }
                if (isNotEmpty(storeStocks)) {
                    storageDetail.setStockQty(storeStocks.getBeginQty());
                    // 如果是出库，实际库存=库存-占用
                    if ("E".equals(storageInfo.getIeFlag())) {
                        BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                        BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                        storageDetail.setStockQty(beginQty); // 实际库存数量
                        storageDetail.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
                        storageDetail.setOccupyQty(occupyQty); // 占用数量
                    }
                }
            }
//            List<String> copGnoList = storageDetailList.stream().map(StorageDetail::getCopGno).distinct().collect(java.util.stream.Collectors.toList());
//            List<String> storeCodes = storageDetailList.stream().map(StorageDetail::getStoreCode).distinct().collect(java.util.stream.Collectors.toList());
//            List<String> batchNoList = storageDetailList.stream().map(StorageDetail::getBatchNo).distinct().collect(java.util.stream.Collectors.toList());
//            LambdaQueryWrapper<StoreStocks> lambdaQueryWrapper = new LambdaQueryWrapper<StoreStocks>()
//                    .eq(StoreStocks::getCustomer, storageInfo.getCustomer())
//                    .in(StoreStocks::getCopGno, copGnoList)
//                    .in(StoreStocks::getStoreCode, storeCodes);
//            if (isNotEmpty(batchNoList)) {
//                lambdaQueryWrapper.in(StoreStocks::getBatchNo, batchNoList);
//            } else {
//                lambdaQueryWrapper.and(i -> i.isNull(StoreStocks::getBatchNo).or().eq(StoreStocks::getBatchNo, ""));
//            }
//            List<StoreStocks> storeStocksList = storeStocksService.list(lambdaQueryWrapper);
//            if (isNotEmpty(storeStocksList)) {
//                Map<String, List<StoreStocks>> storeStocksMap = storeStocksList.stream()
//                        .collect(java.util.stream.Collectors.groupingBy(i -> i.getCustomer() + i.getStoreCode() + i.getBatchNo() + i.getCopGno()));
//                for (StorageDetail storageDetail : storageDetailList) {
//                    List<StoreStocks> storeStocks = storeStocksMap.get(storageInfo.getCustomer() + storageDetail.getStoreCode() + storageDetail.getBatchNo() + storageDetail.getCopGno());
//                    if (isNotEmpty(storeStocks)) {
//                        storageDetail.setStockQty(storeStocks.stream().map(StoreStocks::getBeginQty).reduce(BigDecimal.ZERO, BigDecimal::add));
//                        // 如果是出库，实际库存=库存-占用
//                        if ("E".equals(storageInfo.getIeFlag())) {
//                            BigDecimal sumQty = storeStocks.stream().map(StoreStocks::getBeginQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            BigDecimal occupyQty = storeStocks.stream().map(StoreStocks::getOccupyQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            storageDetail.setStockQty(sumQty); // 实际库存数量
//                            storageDetail.setAvailableQty(sumQty.subtract(occupyQty)); // 可用库存数量
//                            storageDetail.setOccupyQty(occupyQty); // 占用数量
//                        }
//                    }
//                }
//            }
        }
        storageInfo.setStorageDetailList(storageDetailList);
        // 2024/6/11 上午10:51@ZHANGCHAO 追加/变更/完善：入库单附件！！
        List<AttachmentsInfo> attachmentList = attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, storageInfo.getId()));
        storageInfo.setAttachmentList(attachmentList);
        return Result.ok(storageInfo);
    }

    /**
     * 批量删除数据
     *
     * @param ids 要删除的数据的ID集合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (String id : ids.split(",")) {
            StorageInfo storageInfo = baseMapper.selectById(id);
            if (isEmpty(storageInfo)) {
                continue;
            }
            if ("1".equals(storageInfo.getStatus()) || "2".equals(storageInfo.getStatus())) {
                return Result.error("已审核或已出库的数据无法删除！");
            }
            List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                    .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
            if (isNotEmpty(storageDetailList)) {
                for (StorageDetail storageDetail : storageDetailList) {
                    // 2024/1/22 9:22@ZHANGCHAO 追加/变更/完善：删除商品时解除占用！！
                    if ("E".equals(storageInfo.getIeFlag())) {
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setIeFlag(storageInfo.getIeFlag());
                        try {
                            Result<?> deOccupyHandleResult = storeStocksService.deOccupyHandle(storageDetail);
                            if (deOccupyHandleResult.isSuccess()) {
                                storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
                                        .set(StorageDetail::getStocksFlowId, null)
                                        .eq(StorageDetail::getId, storageDetail.getId()));
                            } else {
                                throw new RuntimeException(deOccupyHandleResult.getMessage());
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                    // 2024/1/25 13:06@ZHANGCHAO 追加/变更/完善：清空关联核注单ID
                    nemsInvtListMapper.update(null, new UpdateWrapper<NemsInvtList>().lambda()
                            .set(NemsInvtList::getStorageDetailId, null)
                            .set(NemsInvtList::getStorageNo, null)
                            .eq(NemsInvtList::getStorageDetailId, storageDetail.getId()));
                    storageDetailService.removeById(storageDetail.getId());
                }
            }
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 提交库存
     *
     * @param ids - 库存ID列表
     * @return 返回提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> submitInventory(String ids, String ieFlag,String appDate) {
        List<StorageInfo> storageInfoList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(storageInfoList)) {
            return Result.error("提交失败，未找到相关出入库单数据！");
        }
        // 2024/2/16 23:42@ZHANGCHAO 追加/变更/完善：测试方要求提示信息要完善，有多少不符合的出入库单都要提示出来，那就性能方面不考虑了！！！
        List<String> storageNos = new ArrayList<>();
        List<String> storageNos1 = new ArrayList<>();
        List<String> storageNos2 = new ArrayList<>();
        boolean isBlankItemNumerAndBatchNo = false;
        Map<String, List<StorageDetail>> storageDetailListMap = new HashMap<>();
        Map<Long, List<NemsInvtList>> invtMap = new HashMap<>();
        Map<String, Boolean> isBSMap = new HashMap<>();
        for (StorageInfo storageInfo : storageInfoList) {
            StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                    .eq(StoreInfo::getStoreCode, storageInfo.getStoreCode())
                    .eq(StoreInfo::getTenantId, storageInfo.getTenantId()));
            // 2024/8/13 上午9:10@ZHANGCHAO 追加/变更/完善：专门针对保税维修业务！
            boolean isBSWX = "1".equals(storageInfo.getType());
            boolean isBS = false;
            if (isEmpty(storeInfo)) {
                storageNos.add(storageInfo.getStorageNo());
            } else {
                isBS = "1".equals(storeInfo.getPurpose());
            }
            isBSMap.put(storageInfo.getStorageNo(), isBS);
            List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                    .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
            storageDetailListMap.put(storageInfo.getStorageNo(), storageDetailList);
            if (isNotEmpty(storageDetailList)) {
                isBlankItemNumerAndBatchNo = storageDetailList.stream().anyMatch(i -> isBlank(i.getItemNumber()));
//                /*
//                 * 真正减库存是在提交出库操作，而保税业务需要关联的出口核注单已核扣才能提交出库，
//                 * 而这里不需要有核注单！保税维修料件出库不产生核注单，可直接依据提交出库核减库存。
//                 * 2024/8/9 上午10:06@ZHANGCHAO
//                 */
//                boolean isAllBswx = storageDetailList.stream().allMatch(i -> (isNotBlank(i.getPn()) && i.getPn().contains("维修用料件")) || "2".equals(i.getDetailType()));
//                boolean isAllBswx_ = storageDetailList.stream().anyMatch(i -> (isNotBlank(i.getPn()) && i.getPn().contains("维修用料件")) || "2".equals(i.getDetailType()));
//                if (!isAllBswx && isAllBswx_) {
//                    return Result.error("请检查商品，如果存在「维修用料件」商品说明为「保税维修」业务，则其余所有商品类型都应该为「维修用料件」！");
//                }

                if (isBS && !isBSWX) { // 排除保税维修业务2024-8-13
                    boolean isAllInvt = storageDetailList.stream().allMatch(i -> isNotBlank(i.getInvtListId()));
                    if (!isAllInvt) {
//                        storageNos1.add(storageInfo.getStorageNo());
                    }
                    List<String> invtIdList = storageDetailList.stream().map(StorageDetail::getInvtListId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
                    if (isNotEmpty(invtIdList)) {
                        List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.selectBatchIds(invtIdList);
                        if (isNotEmpty(nemsInvtListList)) {
                            invtMap = nemsInvtListList.stream().collect(Collectors.groupingBy(NemsInvtList::getId));
                            List<NemsInvtHead> nemsInvtHeadList = nemsInvtHeadMapper.selectBatchIds(nemsInvtListList.stream().map(NemsInvtList::getInvId).collect(Collectors.toList()));
                            if (isNotEmpty(nemsInvtHeadList)) {
                                boolean isAll = nemsInvtHeadList.stream().allMatch(i -> "2".equals(i.getVrfdedMarkcd()));
                                if (!isAll) {
//                                    storageNos2.add(storageInfo.getStorageNo());
                                } else {
                                    log.info("{}关联的核注单为全部已核扣！！", storageInfo.getStorageNo());
                                }
                            }
                        }
                    }
                }
            }
        }
        if (isNotEmpty(storageNos)) {
            return Result.error(("I".equals(ieFlag) ? "入库单[" : "出库单[") + CollUtil.join(storageNos, ",") + "]关联的仓库信息不存在！");
        }
        if (isNotEmpty(storageNos1)) {
            return Result.error(("I".equals(ieFlag) ? "入库单[" : "出库单[") + CollUtil.join(storageNos1, ",") + "]存在未关联核注单的明细，无法提交" + ("I".equals(ieFlag) ? "入库！" : "出库！"));
        }
        if (isNotEmpty(storageNos2)) {
            return Result.error(("I".equals(ieFlag) ? "入库单[" : "出库单[") + CollUtil.join(storageNos2, ",") + "]关联的核注清单必须全部为已核扣状态！");
        }
        if (isBlankItemNumerAndBatchNo) {
            return Result.error("存在项号为空的商品信息，请检查！");
        }

        for (StorageInfo storageInfo : storageInfoList) {
            List<StorageDetail> storageDetailList = isNotEmpty(storageDetailListMap) ? storageDetailListMap.get(storageInfo.getStorageNo()) : null;
            if (isNotEmpty(storageDetailList)) {
                // 前端组件改为vxe-table后，可以存在重复的明细，所以这里需要归并以提高性能！
//                Map<String, List<StorageDetail>> storageDetailMap = storageDetailList.stream().collect(Collectors.groupingBy(i -> storageInfo.getCustomer() + i.getStoreCode() + i.getCopGno() + i.getBatchNo()));
                for (StorageDetail storageDetail : storageDetailList) {
                    storageDetail.setIeFlag(ieFlag);
                    storageDetail.setBatchNo(isNotBlank(storageDetail.getBatchNo()) ? storageDetail.getBatchNo() : null);
                    if (isBSMap.get(storageInfo.getStorageNo()) && isNotEmpty(invtMap)) {
                        // 核注单生成的
                        List<NemsInvtList> list = invtMap.get(Long.valueOf(storageDetail.getInvtListId()));
                        String itemNumber = isNotEmpty(list) ? (isNotEmpty(list.get(0).getPutrecSeqno()) ? list.get(0).getPutrecSeqno().toString() : storageDetail.getItemNumber()) : storageDetail.getItemNumber();
                        // 2024/2/10 23:42@ZHANGCHAO 追加/变更/完善：针对核注单修改过备案序号的情况！回填！！
                        if (isNotBlank(itemNumber) && !itemNumber.equals(storageDetail.getBatchNo())) {
                            storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
                                    .set(StorageDetail::getItemNumber, itemNumber)
                                    .eq(StorageDetail::getId, storageDetail.getId()));
                        }
                        storageDetail.setItemNumber(itemNumber);
                    }
                    log.info("BatchNo：==> {}", storageDetail.getBatchNo());
                    storageDetail.setCustomer(storageInfo.getCustomer());
                    storageDetail.setIeFlag(storageInfo.getIeFlag());
                    storeStocksService.saveOrUpdateStocksAndFlow(storageDetail);
                }
            }
            /*
             * 针对已修复出库，提交出库时，已修复维修货物减库存，维修用料件减库存、取消占用。
             * 2024/8/28 09:05@ZHANGCHAO
             */
            if ("1".equals(storageInfo.getIsRepairedOut()) && isNotBlank(storageInfo.getRelRepairNos())) {
                List<StorageRepairOrderDetail> storageRepairOrderDetailList = repairOrderDetailMapper.selectList(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                        .in(StorageRepairOrderDetail::getRepairNo, Arrays.asList(storageInfo.getRelRepairNos().split(","))));
                if (isNotEmpty(storageRepairOrderDetailList)) {
                    for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrderDetailList) {
                        StorageDetail storageDetail = new StorageDetail();
                        BeanUtil.copyProperties(storageRepairOrderDetail, storageDetail, CopyOptions.create().ignoreNullValue());
                        storageDetail.setIeFlag(E);
                        storageDetail.setDetailType("2"); // 2维修用保税料件
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setStorageNo(storageRepairOrderDetail.getRepairNo() + "/" + storageInfo.getStorageNo());
                        storageDetail.setActualQty(storageRepairOrderDetail.getQty());
//                        storageDetail.setAreaCode(storageRepairOrderDetail.getAreaCode());
//                        storageDetail.setAreaName(storageRepairOrderDetail.getAreaName());
//                        storageDetail.setSpaceCode(storageRepairOrderDetail.getSpaceCode());
//                        storageDetail.setSpaceName(storageRepairOrderDetail.getSpaceName());
                        storageDetail.setRemark("针对已修复出库，提交出库时维修用料件减库存、取消占用");
                        // 核减库存/占用处理器
                        Result<?> reduceStockHandleResult = storeStocksService.reduceStockHandle(storageDetail);
                        if (reduceStockHandleResult.isSuccess()) {
                            repairOrderDetailMapper.update(null, new UpdateWrapper<StorageRepairOrderDetail>().lambda()
                                    .set(StorageRepairOrderDetail::getStocksFlowId, null)
                                    .eq(StorageRepairOrderDetail::getId, storageDetail.getId()));
                        } else {
                            throw new RuntimeException(reduceStockHandleResult.getMessage());
                        }
                    }
                }
            }
            baseMapper.update(null, new LambdaUpdateWrapper<StorageInfo>()
                    .set(StorageInfo::getStatus, "2") // 2 已入库/已出库
                    .set(StorageInfo::getAppDate, isNotBlank(appDate)?appDate:new Date())
                    .eq(StorageInfo::getId, storageInfo.getId()));
        }
        return Result.ok("提交成功！");
    }

    /**
     * 出入库单取消提交出入库
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/10 09:15
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleSubmitBack(String ids) {
        List<StorageInfo> storageInfoList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(storageInfoList)) {
            return Result.error("未找到相关出入库单数据！");
        }
        // 需已提交出入库的才能操作
        boolean isSubmit = storageInfoList.stream().allMatch(i -> "2".equals(i.getStatus()));
        if (!isSubmit) {
            return Result.error("请选择已提交出入库的出入库单！");
        }
        Set<String> distinctIeFlags = storageInfoList.stream()
                .map(StorageInfo::getIeFlag)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (distinctIeFlags.size() > 1 || (!distinctIeFlags.contains(I) && !distinctIeFlags.contains(E))) {
            return Result.error("进出口标识异常，请检查数据！");
        }
        List<String> storageNos = storageInfoList.stream().map(StorageInfo::getStorageNo).collect(Collectors.toList());
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .in(StorageDetail::getStorageNo, storageNos));
        Map<String, List<StorageDetail>> storageDetailMap = new HashMap<>();
        if (isNotEmpty(storageDetailList)) {
            storageDetailMap = storageDetailList.stream().collect(Collectors.groupingBy(StorageDetail::getStorageNo));
        }
        String ieFlag = distinctIeFlags.iterator().next();
        log.info("[取消出入库]获取到的进出口标识：==> {}", ieFlag);
        StringBuilder sb = new StringBuilder();
        Set<String> successIds = new HashSet<>(16);
        Set<String> errorIds = new HashSet<>(16);
        StorageInfoServiceImpl currentProxy = (StorageInfoServiceImpl) AopContext.currentProxy();
        for (StorageInfo storageInfo : storageInfoList) {
            try {
                Result<?> result = currentProxy.processStorageInfo(storageInfo, storageDetailMap, ieFlag);
                if (result.isSuccess()) {
                    log.info("[取消出入库]处理结果：{}", result.getMessage());
                    successIds.add(storageInfo.getStorageNo());
                } else {
                    throw new RuntimeException(result.getMessage());
                }
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("[取消出入库] 出入库单冲正库存出现异常，原因：{}", e.getMessage());
                errorIds.add(storageInfo.getStorageNo());
                sb.append("[").append(storageInfo.getStorageNo()).append("]库存冲正出现异常：").append(e.getMessage()).append(";|");
            }
        }
        String msg = "共" + ids.split(",").length + "票，成功数："
                + successIds.size() + (isNotEmpty(errorIds) ? ("，失败数："
                + errorIds.size() + "，原因：" + sb) : "");
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 为每个 storageInfo 处理独立事务
     *
     * @param storageInfo
     * @param storageDetailMap
     * @param ieFlag
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/10 16:08
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<?> processStorageInfo(StorageInfo storageInfo, Map<String, List<StorageDetail>> storageDetailMap, String ieFlag) {
        try {
            List<StorageDetail> storageDetails = storageDetailMap.get(storageInfo.getStorageNo());
            storageInfo.setStorageDetailList(storageDetails);
            if (isEmpty(storageDetails)) {
                throw new RuntimeException("未找到相关出入库单" + storageInfo.getStorageNo() + "明细数据！");
            }
            // 取消入库
            if (I.equals(ieFlag)) {
                // 1. 保税维修
                if ("1".equals(storageInfo.getType())) {
                    // 1. 保税维修旧件入库
                    // 库存数量 -
                    if (isNotBlank(storageInfo.getRelRepairNos())) {
                        log.info("[取消出入库]处理类型：保税维修旧件入库，继续...");
                        storageInfo.getStorageDetailList().forEach(storageDetail -> {
                            storageDetail.setIeFlag(I);
                            storageDetail.setCustomer(storageInfo.getCustomer());
                            storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_ADD); // GOODS_ADD和GOODS_INCR是同一个
                            // 冲正[记账和核增]处理器
                            Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                            if (addStockHandleResult.isSuccess()) {
                                log.info(addStockHandleResult.getMessage());
                            } else {
                                throw new RuntimeException(addStockHandleResult.getMessage());
                            }
                        });
                        // 2. 保税维修一般入库 - 核注单生成入库单
                        // 暂与旧件一致！！
                    } else {
                        log.info("[取消出入库]处理类型：保税维修一般入库，继续...");
                        // 库存数量 -
                        storageInfo.getStorageDetailList().forEach(storageDetail -> {
                            storageDetail.setIeFlag(I);
                            storageDetail.setCustomer(storageInfo.getCustomer());
                            storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_ADD); // GOODS_ADD和GOODS_INCR是同一个
                            // 冲正[记账和核增]处理器
                            Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                            if (addStockHandleResult.isSuccess()) {
                                log.info(addStockHandleResult.getMessage());
                            } else {
                                throw new RuntimeException(addStockHandleResult.getMessage());
                            }
                        });
                    }
                } else {
                    log.info("[取消出入库]处理类型：非保税维修入库单，继续...");
                    // 3. 非保税维修入库单
                    // 库存数量 -
                    storageInfo.getStorageDetailList().forEach(storageDetail -> {
                        storageDetail.setIeFlag(I);
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_ADD); // GOODS_ADD和GOODS_INCR是同一个
                        // 冲正[记账和核增]处理器
                        Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                        if (addStockHandleResult.isSuccess()) {
                            log.info(addStockHandleResult.getMessage());
                        } else {
                            throw new RuntimeException(addStockHandleResult.getMessage());
                        }
                    });
                }
                // 取消出库
            } else if (E.equals(ieFlag)) {
                // 1. 保税维修
                if ("1".equals(storageInfo.getType())) {
                    // 1. 保税维修 - 维修货物出库
                    // 维修货物（库存数量+、占用数量+）
                    if (isNotBlank(storageInfo.getRelRepairNos()) && "1".equals(storageInfo.getIsRepairedOut())) {
                        // 1. 维修货物出库
                        storageInfo.getStorageDetailList().forEach(storageDetail -> {
                            storageDetail.setIeFlag(E);
                            storageDetail.setCustomer(storageInfo.getCustomer());
                            storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_REDUCE);
                            // 冲正[记账和核增]处理器
                            Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                            if (addStockHandleResult.isSuccess()) {
                                log.info(addStockHandleResult.getMessage());
                            } else {
                                throw new RuntimeException(addStockHandleResult.getMessage());
                            }
                        });
                        // 2.维修用料件（库存数量+、占用数量+）
                        List<StorageRepairOrderDetail> storageRepairOrderDetailList = repairOrderDetailMapper.selectList(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                                .in(StorageRepairOrderDetail::getRepairNo, Arrays.asList(storageInfo.getRelRepairNos().split(","))));
                        if (isNotEmpty(storageRepairOrderDetailList)) {
                            for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrderDetailList) {
                                StorageDetail storageDetail = new StorageDetail();
                                BeanUtil.copyProperties(storageRepairOrderDetail, storageDetail, CopyOptions.create().ignoreNullValue());
                                storageDetail.setIeFlag(E);
                                storageDetail.setDetailType("2"); // 2维修用保税料件
                                storageDetail.setCustomer(storageInfo.getCustomer());
                                storageDetail.setStorageNo(storageRepairOrderDetail.getRepairNo());
                                storageDetail.setActualQty(storageRepairOrderDetail.getQty());
                                storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_REDUCE);
                                // 冲正[核减库存/占用]处理器
                                Result<?> reduceStockHandleResult = storeStocksService.rectify(storageDetail);
                                if (reduceStockHandleResult.isSuccess()) {
                                    log.info(reduceStockHandleResult.getMessage());
                                    // ???
                                    repairOrderDetailMapper.update(null, new UpdateWrapper<StorageRepairOrderDetail>().lambda()
                                            .set(StorageRepairOrderDetail::getStocksFlowId, null)
                                            .eq(StorageRepairOrderDetail::getId, storageDetail.getId()));
                                } else {
                                    throw new RuntimeException(reduceStockHandleResult.getMessage());
                                }
                            }
                        }
                    }
                    // 2. 保税维修 - 拆下件旧件出库
                    // 库存数量+、占用数量+
                    if (isBlank(storageInfo.getRelRepairNos()) && "1".equals(storageInfo.getIsOldOut())) {
                        storageInfo.getStorageDetailList().forEach(storageDetail -> {
                            storageDetail.setIeFlag(E);
                            storageDetail.setCustomer(storageInfo.getCustomer());
                            storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_REDUCE);
                            // 冲正[核减]处理器
                            Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                            if (addStockHandleResult.isSuccess()) {
                                log.info(addStockHandleResult.getMessage());
                            } else {
                                throw new RuntimeException(addStockHandleResult.getMessage());
                            }
                        });
                    }
                } else {
                    // 3. 一般出库单（核注单生成+手动新建）
                    // 库存数量+、占用数量+
                    storageInfo.getStorageDetailList().forEach(storageDetail -> {
                        storageDetail.setIeFlag(E);
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_REDUCE);
                        // 冲正[核减]处理器
                        Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                        if (addStockHandleResult.isSuccess()) {
                            log.info(addStockHandleResult.getMessage());
                        } else {
                            throw new RuntimeException(addStockHandleResult.getMessage());
                        }
                    });
                }
            }

        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("[取消出入库] 出入库单冲正库存出现异常，原因：{}", e.getMessage());
            throw new RuntimeException("[取消出入库] 出入库单冲正库存出现异常：" + e.getMessage());
        }
        // 都成功后，更新状态
        baseMapper.update(null, new LambdaUpdateWrapper<StorageInfo>()
                .set(StorageInfo::getStatus, "1") // 1 更新为已审核状态
                .eq(StorageInfo::getId, storageInfo.getId()));
        return Result.ok("操作成功！");
    }

    /**
     * 自动提交库存
     *
     * @param storageInfo
     * @return 返回提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> submitInventoryAuto(StorageInfo storageInfo, boolean isEnable) {
        List<StorageDetail> storageDetailList = storageInfo.getStorageDetailList();
        if (isEmpty(storageDetailList)) {
            return Result.error("【自动提交入库】此入库单[{" + storageInfo.getStorageNo() + "}]无商品明细，无法自动提交入库！");
        }
        for (StorageDetail storageDetail : storageDetailList) {
            storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
                    .set(StorageDetail::getItemNumber, storageDetail.getItemNumber())
                    .set(isNotBlank(storageDetail.getBatchNo()),StorageDetail::getBatchNo, storageDetail.getBatchNo())
                    .eq(StorageDetail::getId, storageDetail.getId()));
            storageDetail.setCustomer(storageInfo.getCustomer());
            storageDetail.setIeFlag(storageInfo.getIeFlag());
            if (isEnable) {
                storeStocksService.saveOrUpdateStocksAndFlow(storageDetail);
            }
        }
        if (isEnable) {
            baseMapper.update(null, new LambdaUpdateWrapper<StorageInfo>()
                    .set(StorageInfo::getStatus, "2") // 2 已入库/已出库
                    .set(StorageInfo::getAppDate, null==storageInfo.getAppDate()?
                            storageInfo.getCreateDate():storageInfo.getAppDate())
                    .set(StorageInfo::getRemark, DateUtil.now() + " 通过下行核注单[" + storageInfo.getInvtNo() + "]自动提交入库！")
                    .eq(StorageInfo::getId, storageInfo.getId()));
        }
        return Result.ok("提交成功！");
    }

    /**
     * 查询出入库单商品所在的出入库单是否已入库出库
     *
     * @param ids 要检查的头部标识符
     * @return 检查结果
     */
    @Override
    public Result<?> checkHeadStatus(String ids) {
        StoreToInvtDTO storeToInvtDTO = new StoreToInvtDTO();
        List<String> storageNos = new ArrayList<>();
        List<StorageInfo> storageInfos = new ArrayList<>();
        // 获取本企业的保税仓库
        List<StoreInfo> storeInfoList = storeInfoMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getPurpose, "1")
                .eq(StoreInfo::getTenantId, TenantContext.getTenant()));
        PtsEmsHead ptsHead = new PtsEmsHead();
        if (isNotEmpty(storeInfoList)) {
            // 获取物流账册
            List<PtsEmsHead> emsHeads = emsHeadMapper.selectList(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getOwnerCode, storeInfoList.get(0).getStoreCode())
                    .eq(PtsEmsHead::getEmsType, "L")
                    .eq(PtsEmsHead::getTenantId, TenantContext.getTenant()));
            ptsHead = isNotEmpty(emsHeads) ? emsHeads.get(0) : new PtsEmsHead();
        }
        List<PtsEmsAimg> ptsEmsAimgs = new ArrayList<>();
        List<StorageDetail> storageDetailList = storageDetailService.listByIds(Arrays.asList(ids.split(",")));
        if (isNotEmpty(storageDetailList)) {
            for (StorageDetail storageDetail : storageDetailList) {
                List<StorageInfo> storageInfoList = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                        .eq(StorageInfo::getStorageNo, storageDetail.getStorageNo()));
                boolean found = storageInfoList.stream().anyMatch(i -> i.getStatus().equals("2"));
                if (found) {
                    storageNos.add(storageDetail.getStorageNo());
                }
                storageInfos.addAll(storageInfoList);
                PtsEmsAimg ptsAimg = emsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                        .eq(PtsEmsAimg::getEmsId, ptsHead.getId())
                        .eq(PtsEmsAimg::getGNo, storageDetail.getBatchNo())
                        .eq(PtsEmsAimg::getCopGno, storageDetail.getCopGno()));
                if (isNotEmpty(ptsAimg)) {
                    ptsEmsAimgs.add(ptsAimg);
                }
            }
        }
        ptsHead.setEmsAimgList(ptsEmsAimgs);
        if (isNotEmpty(storageNos)) {
            return Result.error("表体所在的出库单[" + CollUtil.join(CollUtil.distinct(storageNos), ",") + "]已出库，请重新选择！");
        }
        storeToInvtDTO.setStorageDetailList(storageDetailList);
        // 去除重复项
        List<StorageInfo> distinctStorageInfos = storageInfos.stream()
                .collect(Collectors.toMap(StorageInfo::getStorageNo, s -> s, (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());
        storeToInvtDTO.setStorageInfoList(distinctStorageInfos);

        if (isNotEmpty(storeInfoList)) {
            storeToInvtDTO.setStoreInfo(storeInfoList.get(0));
            List<StoreSpace> storeSpaceList = storeSpaceService.list(new LambdaQueryWrapper<StoreSpace>()
                    .eq(StoreSpace::getStoreId, storeInfoList.get(0).getId()));
            storeToInvtDTO.setStoreSpaceList(storeSpaceList);

            storeToInvtDTO.setPtsEmsHead(ptsHead);
        }
        storeToInvtDTO.setEnterpriseInfo(enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant())));
        return Result.ok(storeToInvtDTO);
    }

    /**
     * 获取绑定仓库的详细列表
     *
     * @return 绑定仓库的详细列表结果
     */
    @Override
    public IPage<InOrOutStorageDetailDTO> bondedWarehouseInDetailList(Integer pageNo, Integer pageSize, InOrOutStorageDetailDTO inOrOutStorageDetailDTO) {
        Page<InOrOutStorageDetailDTO> page = new Page<>(pageNo, pageSize);
        inOrOutStorageDetailDTO.setTenantId(TenantContext.getTenant());
        IPage<InOrOutStorageDetailDTO> pageList = baseMapper.bondedWarehouseInDetailList(page, inOrOutStorageDetailDTO);
        return pageList;
    }

    /**
     * 创建库存中的页面用于存储存储信息
     *
     * @param page        存储信息的页码
     * @param storageInfo 存储信息
     * @return 创建的库存页面
     */
    @Override
    public IPage<StorageInfo> listForCreateInvt(Page<StorageInfo> page, StorageInfo storageInfo) {
        IPage<StorageInfo> pageList = baseMapper.listForCreateInvt(page, storageInfo);
        return pageList;
    }

    /**
     * 创建入库
     *
     * @param ids         ID集合
     * @param bizopEtpsno 生意操作方ID
     * @param rcvgdEtpsno 接收方ID
     * @return 返回结果
     */
    @Override
    public Result<?> getCreateInStorage(String ids, String bizopEtpsno, String rcvgdEtpsno, String type) {
        StorageInfo storageInfo = new StorageInfo();
        storageInfo.setType(type);
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getCustomsDeclarationCode, bizopEtpsno));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (isNotEmpty(enterpriseInfo)) {
            storageInfo.setCustomer(isNotEmpty(enterpriseInfo.getTenantId()) ? String.valueOf(enterpriseInfo.getTenantId()) : "");
            storageInfo.setCustomerStr(enterpriseInfo.getEnterpriseFullName());
        } else {
            Commissioner commissioner = commissionerService.getOne(new LambdaQueryWrapper<Commissioner>()
                    .eq(Commissioner::getDepartcd, bizopEtpsno));
            if (isNotEmpty(commissioner)) {
                storageInfo.setCustomer(commissioner.getId());
            }
        }
        storageInfo.setStoreCode(rcvgdEtpsno);
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getStoreCode, rcvgdEtpsno));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (isNotEmpty(storeInfo)) {
            storageInfo.setStoreName(storeInfo.getStoreName());
        } else {
            // 为了保税维修业务！！
            List<StoreInfo> storeInfoList = storeInfoMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                    .eq(StoreInfo::getPurpose, "1")
                    .eq(StoreInfo::getTenantId, TenantContext.getTenant()));
            if (isNotEmpty(storeInfoList)) {
                storageInfo.setStoreCode(storeInfoList.get(0).getStoreCode());
                storageInfo.setStoreName(storeInfoList.get(0).getStoreName());
            }
        }
        // 处理商品
        List<StorageDetail> storageDetailList = new ArrayList<>();
        List<NemsInvtHead> nemsInvtHeadList = nemsInvtHeadMapper.selectList(new LambdaQueryWrapper<NemsInvtHead>()
                .in(NemsInvtHead::getId, Arrays.asList(ids.split(","))));
        if (isNotEmpty(nemsInvtHeadList)) {
            nemsInvtHeadList.forEach(nemsInvtHead -> {
                List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.selectList(new LambdaQueryWrapper<NemsInvtList>()
                        .eq(NemsInvtList::getInvId, nemsInvtHead.getId()).orderByAsc(NemsInvtList::getGdsseqNo));
                //查询关联的报关单信息
                List<DecHead> decHeads = new ArrayList<>();
                if("1".equals(nemsInvtHead.getDclcusFlag())){
                    String clearanceNo="";
                    if ("1".equals(nemsInvtHead.getDclcusTypecd())) {//关联
                        clearanceNo = nemsInvtHead.getRltEntryNo();
                    } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) {//对应
                        clearanceNo = nemsInvtHead.getEntryNo();
                    }
                    if(isNotBlank(clearanceNo)){
                        decHeads = decHeadMapper.selectList(new LambdaQueryWrapper<DecHead>()
                                .eq(DecHead::getClearanceNo, clearanceNo));
                    }
                }
                if (isNotEmpty(nemsInvtListList)) {
                    for (NemsInvtList nemsInvtList : nemsInvtListList) {
                        StorageDetail storageDetail = new StorageDetail();
//                        if("I".equals(nemsInvtHead.getImpexpMarkcd())){
                            storageDetail.setBillNo(decHeads.size()>0?decHeads.get(0).getBillCode():null);//运单号
                            /**
                             *   核注单生成出入库单，增加净重字段，根据核注单表体带入，
                             *   净重逻辑：申报单位是千克的取申报数量，如果不是再看法一是不是千克，如果这两都不是直接赋法二数量。
                             */
                            if("035".equals(nemsInvtList.getDclUnitcd())){
                                storageDetail.setNetWeight(nemsInvtList.getDclQty());
                            }else if("035".equals(nemsInvtList.getLawfUnitcd())){
                                storageDetail.setNetWeight(nemsInvtList.getLawfQty());
                            }else {
                                storageDetail.setNetWeight(nemsInvtList.getSecdLawfQty());
                            }
//                        }
                        storageDetail.setBondInvtNo(nemsInvtHead.getBondInvtNo());//清单编号
                        storageDetail.setHscode(nemsInvtList.getHscode());//商品编码
                        storageDetail.setOriginCountry(nemsInvtList.getOriginCountry());//原产国
                        storageDetail.setSupvModecd(nemsInvtHead.getSupvModecd());//贸易方式
                        storageDetail.setShipName(decHeads.size()>0?decHeads.get(0).getShipName():null);//运输工具
                        storageDetail.setVoyage(decHeads.size()>0?decHeads.get(0).getVoyage():null);//运输工具编号
                        storageDetail.setStorageNo(storageInfo.getStorageNo());
                        storageDetail.setStoreCode(storageInfo.getStoreCode());
                        storageDetail.setCopGno(nemsInvtList.getGdsMtno()); // 物料号
                        storageDetail.setPn(nemsInvtList.getHsname()); // 中文品名
//                storageDetail.setModel(nemsInvtList.getHsmodel()); // 规格型号
                        storageDetail.setActualQty(nemsInvtList.getDclQty()); // 入库数量
                        storageDetail.setQunit(nemsInvtList.getDclUnitcd()); // 单位
                        storageDetail.setDcluprcamt(nemsInvtList.getDclUprcamt()); // 单价
                        storageDetail.setTotalPriceTax(nemsInvtList.getDclTotalamt()); // 总价
                        // 2024/8/22 下午3:27@ZHANGCHAO 追加/变更/完善：改为真正的备案序号！
                        storageDetail.setItemNumber(isNotEmpty(nemsInvtList.getPutrecSeqno()) ? String.valueOf(nemsInvtList.getPutrecSeqno()) : null);
                        // 2024/8/22 下午3:27@ZHANGCHAO 追加/变更/完善：批次号！带入报关单号=入库单的批次号
                        String clearanceNo = "";
                        if (isNotBlank(nemsInvtHead.getDclcusTypecd())) {
                            if ("1".equals(nemsInvtHead.getDclcusTypecd())){//关联
                                clearanceNo = nemsInvtHead.getRltEntryNo();
                            }else if ("2".equals(nemsInvtHead.getDclcusTypecd())){//对应
                                clearanceNo = nemsInvtHead.getEntryNo();
                            }
                        }
                        if("I".equals(nemsInvtHead.getImpexpMarkcd())){
                            storageDetail.setBatchNo(clearanceNo); // 带入报关单号=入库单的批次号
                        }
                        storageDetail.setInvtListId(String.valueOf(nemsInvtList.getId()));
                        // 2024/8/7 上午11:46@ZHANGCHAO 追加/变更/完善：入库单表体料件类型：1维修货物、2维修用保税料件、3旧件/坏件
                        if (isNotBlank(storageDetail.getPn())) {
                            if (storageDetail.getPn().contains("（待修复）")
                                    || storageDetail.getPn().contains("(待修复)")
                                    || storageDetail.getPn().contains("(待修复）")
                                    || storageDetail.getPn().contains("（待修复)")) {
                                storageDetail.setDetailType("1");
                            } else if (storageDetail.getPn().contains("(维修用料件)")
                                    || storageDetail.getPn().contains("（维修用料件）")
                                    || storageDetail.getPn().contains("(维修用料件）")
                                    || storageDetail.getPn().contains("（维修用料件)")) {
                                storageDetail.setDetailType("2");
                            } else if (storageDetail.getPn().contains("旧件") || storageDetail.getPn().contains("坏件")) {
                                storageDetail.setDetailType("3");
                            } else {
                                storageDetail.setDetailType(null);
                            }
                        }
                        // 2024/8/13 上午9:22@ZHANGCHAO 追加/变更/完善：去掉商品名称的关键字标识！
                        storageDetail.setPn(storageDetail.getPn().trim()
                                .replaceAll("（待修复）", "")
                                .replaceAll("\\(待修复\\)", "")
                                .replaceAll("\\(待修复）", "")
                                .replaceAll("（待修复\\)", "")
                                .replaceAll("\\(维修用料件\\)", "")
                                .replaceAll("（维修用料件）", "")
                                .replaceAll("\\(维修用料件）", "")
                                .replaceAll("（维修用料件\\)", "")
                                .replaceAll("旧件", "")
                                .replaceAll("坏件", ""));
                        // 2024/8/22 下午4:54@ZHANGCHAO 追加/变更/完善：默认库区和储位！！
                        String areaType = "";
                        if ("1".equals(storageDetail.getDetailType())) {
                            areaType = "2"; // 待维修库区
                        } else if ("2".equals(storageDetail.getDetailType())) {
                            areaType = "1"; // 维修用料件区
                        } else if ("3".equals(storageDetail.getDetailType())) {
                            areaType = "3"; // 拆下件区
                        }
                        List<StoreArea> saList = storeAreaMapper.selectList(new LambdaQueryWrapper<StoreArea>()
                                .eq(StoreArea::getType, areaType)
                                .eq(StoreArea::getStatus, "1")
                                .eq(StoreArea::getCustomer, storageInfo.getCustomer()));
                        if (isNotEmpty(saList)) {
                            storageDetail.setAreaCode(saList.get(0).getAreaName());
                            storageDetail.setAreaName(saList.get(0).getAreaCode());
                            List<StoreSpace> spList = storeSpaceMapper.selectList(new LambdaQueryWrapper<StoreSpace>()
                                    .eq(StoreSpace::getAreaCode, saList.get(0).getAreaCode()));
                            if (isNotEmpty(spList)) {
                                storageDetail.setSpaceCode(spList.get(0).getCabinCode());
                                storageDetail.setSpaceName(spList.get(0).getSpaceCode());
                            }
                        }
                        // 2024/11/22 13:19@ZHANGCHAO 追加/变更/完善：设置库存！！！
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setIeFlag(nemsInvtHead.getImpexpMarkcd());
                        Result<?> result = storeStocksService.getStoreStocksBy4ModelCond(storageDetail);
                        StoreStocks storeStocks = null;
                        if (result.isSuccess()) {
                            storeStocks = (StoreStocks) result.getResult();
                        }
                        if (isNotEmpty(storeStocks)) {
                            storageDetail.setStockQty(storeStocks.getBeginQty()); // 实际库存数量
                            storageDetail.setAvailableQty(storeStocks.getAvailableQty()); // 可用库存数量
                            storageDetail.setOccupyQty(storeStocks.getOccupyQty()); // 占用数量
                            /**
                             *入库报关单号（就是批次号/报关单号）、库区、储位：自动根据物料号、项号带库存筛选出符合条件的第一条即可
                             */
                            if ("E".equals(nemsInvtHead.getImpexpMarkcd())) {
                                storageDetail.setBatchNo(storeStocks.getBatchNo());
                                storageDetail.setAreaCode(storeStocks.getAreaCode());
                                storageDetail.setSpaceCode(storeStocks.getSpaceCode());
                            }
                        }
                        storageDetailList.add(storageDetail);
                    }
                }
            });
        }
//        List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.selectList(new LambdaQueryWrapper<NemsInvtList>()
//                .in(NemsInvtList::getInvId, Arrays.asList(ids.split(","))));
//        List<StorageDetail> storageDetailList = new ArrayList<>();
//        if (isNotEmpty(nemsInvtListList)) {
//            for (NemsInvtList nemsInvtList : nemsInvtListList) {
//                StorageDetail storageDetail = new StorageDetail();
//                storageDetail.setStorageNo(storageInfo.getStorageNo());
//                storageDetail.setStoreCode(rcvgdEtpsno);
//                storageDetail.setCopGno(nemsInvtList.getGdsMtno()); // 物料号
//                storageDetail.setPn(nemsInvtList.getHsname()); // 中文品名
////                storageDetail.setModel(nemsInvtList.getHsmodel()); // 规格型号
//                storageDetail.setActualQty(nemsInvtList.getDclQty()); // 入库数量
//                storageDetail.setQunit(nemsInvtList.getDclUnitcd()); // 单位
//                storageDetail.setDcluprcamt(nemsInvtList.getDclUprcamt()); // 单价
//                storageDetail.setTotalPriceTax(nemsInvtList.getDclTotalamt()); // 总价
//                // 2024/8/22 下午3:27@ZHANGCHAO 追加/变更/完善：改为真正的备案序号！
//                storageDetail.setItemNumber(isNotEmpty(nemsInvtList.getPutrecSeqno()) ? String.valueOf(nemsInvtList.getPutrecSeqno()) : null);
//                // 2024/8/22 下午3:27@ZHANGCHAO 追加/变更/完善：批次号！带入报关单号+运单号=入库单的批次号
//                if (isNotBlank(n)) {
//
//                }
//                storageDetail.setBatchNo(n);
//                storageDetail.setInvtListId(String.valueOf(nemsInvtList.getId()));
//                // 2024/8/7 上午11:46@ZHANGCHAO 追加/变更/完善：入库单表体料件类型：1维修货物、2维修用保税料件、3旧件/坏件
//                if (isNotBlank(storageDetail.getPn())) {
//                    if (storageDetail.getPn().contains("（待修复）") || storageDetail.getPn().contains("(待修复)")) {
//                        storageDetail.setDetailType("1");
//                    } else if (storageDetail.getPn().contains("(维修用料件)") || storageDetail.getPn().contains("（维修用料件）")) {
//                        storageDetail.setDetailType("2");
//                    } else if (storageDetail.getPn().contains("旧件") || storageDetail.getPn().contains("坏件")) {
//                        storageDetail.setDetailType("3");
//                    } else {
//                        storageDetail.setDetailType(null);
//                    }
//                }
//                // 2024/8/13 上午9:22@ZHANGCHAO 追加/变更/完善：去掉商品名称的关键字标识！
//                storageDetail.setPn(storageDetail.getPn().replaceAll("（待修复）", "").replaceAll("\\(待修复\\)", "")
//                        .replaceAll("\\(维修用料件\\)", "").replaceAll("（维修用料件）", "")
//                        .replaceAll("旧件", "").replaceAll("坏件", ""));
//                storageDetailList.add(storageDetail);
//            }
//        }
        storageInfo.setStorageDetailList(storageDetailList);
        return Result.ok(storageInfo);
    }

    /**
     * 出入库单审核
     *
     * @param ids
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/27 23:38
     */
    @Idempotent(timeout = 2, message = "存在重复请求，已忽略")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleAudits(String ids, String type) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 是否审核
        AtomicBoolean isAudits = new AtomicBoolean("1".equals(type));
        for (String id : ids.split(",")) {
            StorageInfo storageInfo = baseMapper.selectById(id);
            if (isEmpty(storageInfo)) {
                continue;
            }
            // 出库审核时处理占用
            if ("E".equals(storageInfo.getIeFlag())) {
                List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                        .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
                if (isNotEmpty(storageDetailList)) {
                    for (StorageDetail storageDetail : storageDetailList) {
                        storageDetail.setCustomer(storageInfo.getCustomer());
                        storageDetail.setIeFlag(storageInfo.getIeFlag());
                        // 处理审核时占用或解除占用
                        handleOccupyStatus(storageDetail, isAudits.get());
                    }
                }
            }
            baseMapper.update(null, new LambdaUpdateWrapper<StorageInfo>()
                    .set(StorageInfo::getStatus, "1".equals(type) ? "1" : "0") // 1
                    .set(StorageInfo::getAuditBy, "1".equals(type) ? loginUser.getRealname() : null)
                    .set(StorageInfo::getAuditDate, "1".equals(type) ? new Date() : null)
                    .eq(StorageInfo::getId, id));
        }
        return Result.ok("操作成功！");
    }

    /**
     * 根据物料号获取产品库
     *
     * @param copGno 物料号
     * @return 查询结果
     */
    @Override
    public Result<?> getStorageDetailByCopGno(String copGno) {
        ProductInfo productInfo = productInfoService.getOne(new LambdaQueryWrapper<ProductInfo>()
                .eq(ProductInfo::getPn, copGno).eq(ProductInfo::getIsExamine,"1")
        .ne(ProductInfo::getIsProhibit,"1"));
        return Result.ok(productInfo);
    }

    /**
     * 根据提供的ID字符串，通用库存头类型处理方法。
     *
     * @param ids 以字符串形式表示的ID集合，通常用于指定需要处理的库存头类型。
     * @return 返回一个Result对象，该对象可能包含处理结果的详细信息，具体取决于实现。
     */
    @Override
    public Result<?> generalStockHeadType(String ids) {
        List<StorageInfo> storageInfoList = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                .in(StorageInfo::getId, Arrays.asList(ids.split(","))));
        if (isEmpty(storageInfoList)) {
            return Result.error("未查询到出入库信息");
        }
        for (StorageInfo storageInfo : storageInfoList) {
            List<StockHeadType> stockHeads = stockHeadTypeService.list(new LambdaQueryWrapper<StockHeadType>()
                    .like(StockHeadType::getStorageInfoId, storageInfo.getId()));
            if (isNotEmpty(stockHeads)) {
                return Result.error("出库单[" + storageInfo.getStorageNo() + "]已生成出入库单(出区)，请重新选择！！");
            }
        }
        List<String> storageIds = storageInfoList.stream().map(StorageInfo::getId).collect(Collectors.toList());
        List<String> storageNos = storageInfoList.stream().map(StorageInfo::getStorageNo).collect(Collectors.toList());
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .in(StorageDetail::getStorageNo, storageNos));
        boolean isNo = storageDetailList.stream().anyMatch(i -> isBlank(i.getBatchNo()));
        if (isNo) {
            return Result.error("存在商品批次号（备案序号）为空的数据，请检查！");
        }
        StockHeadType stockHeadType = new StockHeadType();
        stockHeadType.setStorageInfoId(CollUtil.join(CollUtil.distinct(storageIds), ","));
        // 获取本企业的保税仓库
        List<StoreInfo> storeInfoList = storeInfoMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getPurpose, "1"));
        PtsEmsHead ptsHead;
        if (isNotEmpty(storeInfoList)) {
            // 获取物流账册
            List<PtsEmsHead> emsHeads = emsHeadMapper.selectList(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getOwnerCode, storeInfoList.get(0).getStoreCode())
                    .eq(PtsEmsHead::getEmsType, "L"));
            ptsHead = isNotEmpty(emsHeads) ? emsHeads.get(0) : null;
        } else {
            ptsHead = null;
        }
        if (isEmpty(ptsHead)) {
            return Result.error("未查询到账册信息，请检查！");
        }
        List<String> blankNos = new ArrayList<>();
        storageDetailList.forEach(storageDetail -> {
            PtsEmsAimg emsAimg = emsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                    .eq(PtsEmsAimg::getEmsId, ptsHead.getId())
                    .eq(PtsEmsAimg::getEmsNo, ptsHead.getEmsNo())
                    .eq(PtsEmsAimg::getGNo, storageDetail.getBatchNo()));
            if (isEmpty(emsAimg)) {
                blankNos.add(storageDetail.getBatchNo());
            }
        });
        if (isNotEmpty(blankNos)){
            return Result.error("未在账册料件找到的备案序号：[" + CollUtil.join(CollUtil.distinct(blankNos), "]"));
        }
        stockHeadType.setAreainOriactNo(ptsHead.getEmsNo());
        // 正常执行的业务申报表
        AppHeadType appHeadType = appHeadTypeMapper.selectOne(new LambdaQueryWrapper<AppHeadType>()
                .eq(AppHeadType::getDclTbStucd, "1"));
        if (isEmpty(appHeadType)) {
            throw new RuntimeException("未查询到正常执行状态的业务申报表信息！");
        }
        // 转换出入库单表头
        toStockHeadType(appHeadType, stockHeadType, storageInfoList);
        List<StockGoodsType> stockGoodsTypeList = new ArrayList<>();
        // 转换出入库单表体
        toStockGoodsTypes(appHeadType, stockGoodsTypeList, storageDetailList);
        stockHeadType.setStockGoodsTypeList(stockGoodsTypeList);
        return Result.ok(stockHeadType);
    }

    /**
     * 导出入库单Excel
     *
     * @param request
     * @param response
     * @param storageInfo1
     * @return void
     * <AUTHOR>
     * @date 2024/9/5 10:08
     */
    @Override
    public Result<?> exportInstore(HttpServletRequest request, HttpServletResponse response, StorageInfo storageInfo1) throws IOException {
        List<StorageInfo> storageInfoList = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                .in(StorageInfo::getId, Arrays.asList(storageInfo1.getIds().split(","))));
        // 此处导出默认只有一个！！
        if (storageInfoList.size() != 1) {
            return Result.error("未获取到入库单数据，请检查！");
        }
        StorageInfo storageInfo = storageInfoList.get(0);
        // 处理Excel
        Map<String, Object> map = new HashMap<>();
        map.put("storageNo", storageInfo.getStorageNo());
        StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getStoreCode, storageInfo.getStoreCode()));
        if (isNotEmpty(storeInfo)) {
            map.put("storeName", storeInfo.getStoreName());
        } else {
            map.put("storeName", "");
        }
        if (isNotBlank(storageInfo.getCustomer())) {
            String tenantName = null;
            try {
                // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageInfo.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：" + e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageInfo.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageInfo.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageInfo.setCustomerStr(tenantName);
            }
        }
        map.put("customerStr", storageInfo.getCustomerStr());
        map.put("auditDate", isNotEmpty(storageInfo.getAuditDate()) ? DateUtil.formatDateTime(storageInfo.getAuditDate()) : "");
        List<DictModel> dictModels = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo())
                .orderByAsc(StorageDetail::getItemNumber));
        if (isNotEmpty(storageDetailList)) {
            for (StorageDetail storageDetail : storageDetailList) {
                Map<String, Object> lm = new HashMap<>();
                lm.put("itemNumber", storageDetail.getItemNumber());
                lm.put("copGno", storageDetail.getCopGno());
                lm.put("pn", storageDetail.getPn());
                lm.put("actualQty", storageDetail.getActualQty().setScale(2, RoundingMode.HALF_UP));
                lm.put("qunit", isNotEmpty(dictMap) ? dictMap.get(storageDetail.getQunit()) : "");
                lm.put("batchNo", storageDetail.getBatchNo());
                listMap.add(lm);
            }
        }
        map.put("listMap", listMap);
        //获取模板文件路径
        Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream("/templates/xls/入库单导出模版.xlsx"));
        TemplateExportParams params = new TemplateExportParams();
        params.setTemplateWb(templateWorkbook);
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        // 保存到本地文件
        String suffix = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomString(6);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        byte[] data = bos.toByteArray();

        // 获取操作系统名称
        ResponseEntity<byte[]> responseEntity = null;
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            log.info("当前操作系统为：Windows");
            InputStream in = new ByteArrayInputStream(data);
            responseEntity = ExcelToPdfUtil.excel2pdf(in, "入库单详情_" + suffix, "");
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            log.info("当前操作系统为：Linux");
            String fileName = "入库单详情_" + suffix + ".xlsx";
            String xmlPath = upLoadPath + "/downloadPdf/" + fileName;
            File file1 = new File(xmlPath);
            // Create parent directories if they don't exist
            File parentDir1 = file1.getParentFile();
            if (!parentDir1.exists()) {
                parentDir1.mkdirs();  // Create the directory and any necessary but nonexistent parent directories
            }
            FileOutputStream fos;
            try {
                fos = new FileOutputStream(file1);
                fos.write(data, 0, data.length);
                fos.flush();
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            log.info("[入库单]生成文件成功：==> {}", xmlPath);
            responseEntity = ExcelToPdfUtil.excel2pdf_(xmlPath, "入库单详情_" + suffix, "");
            // 删除文件
            FileUtil.deleteFile(xmlPath);
        } else {
            log.info("无法识别的操作系统: {}", os);
        }
        if (isEmpty(responseEntity)) {
            return Result.error("组装数据失败，未获取到字体文件！");
        }
        byte[] bytes = responseEntity.getBody();
        String pdfPath = upLoadPath + "/downloadPdf/" + "入库单详情_" + suffix + ".pdf";
        File file = new File(pdfPath);

        // Create parent directories if they don't exist
        File parentDir = file.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();  // Create the directory and any necessary but nonexistent parent directories
        }

        FileOutputStream fos1;
        try {
            fos1 = new FileOutputStream(file);
            fos1.write(bytes, 0, bytes.length);
            fos1.flush();
            fos1.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("[入库单]pdf生成文件成功：==> {}", pdfPath);
        String file_url = null;
        try {
            file_url = OssBootUtil.upload(new FileInputStream(file), "pdf_temp/" + file.getName());
            log.info("[入库单]pdf上传文件成功：==> {}", file_url);
        } catch (Exception e) {
            log.error("上传文件发生异常：{}", String.valueOf(e));
        }
        // 删除文件
        FileUtil.deleteFile(pdfPath);
        return Result.OK(file_url);

//        response.setHeader("content-type", "application/octet-stream");
//        response.setContentType("application/octet-stream");
//        // 下载文件能正常显示中文
//        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
//
//        OutputStream fos = null;
//        FileOutputStream FileFos = null;
//        try {
//            //普通下载
//            fos = response.getOutputStream();
//            fos.write(bytes, 0, bytes.length);
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                if (FileFos != null) {
//                    FileFos.close();
//                }
//                if (fos != null) {
//                    fos.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
    }

    /**
     * 导出出库单列表Excel
     *
     * @param request
     * @param response
     * @param storageInfo1
     * @return void
     * <AUTHOR>
     * @date 2024/9/6 11:22
     */
    @Override
    public Result<?> exportOutstore(HttpServletRequest request, HttpServletResponse response, StorageInfo storageInfo1) throws IOException {
        List<StorageInfo> storageInfoList = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                .in(StorageInfo::getId, Arrays.asList(storageInfo1.getIds().split(","))));
        // 此处导出默认只有一个！！
        if (storageInfoList.size() != 1) {
            return Result.error("未获取到出库单数据，请检查！");
        }
        StorageInfo storageInfo = storageInfoList.get(0);
        // 处理Excel
        Map<String, Object> map = new HashMap<>();
        map.put("storageNo", storageInfo.getStorageNo());
        StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getStoreCode, storageInfo.getStoreCode()));
        if (isNotEmpty(storeInfo)) {
            map.put("storeName", storeInfo.getStoreName());
        } else {
            map.put("storeName", "");
        }
        if (isNotBlank(storageInfo.getCustomer())) {
            String tenantName = null;
            try {
                // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageInfo.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：" + e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageInfo.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageInfo.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageInfo.setCustomerStr(tenantName);
            }
        }
        map.put("customerStr", storageInfo.getCustomerStr());
        map.put("auditDate", isNotEmpty(storageInfo.getAuditDate()) ? DateUtil.formatDateTime(storageInfo.getAuditDate()) : "");
        List<DictModel> dictModels = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo())
                .orderByAsc(StorageDetail::getItemNumber));
        if (isNotEmpty(storageDetailList)) {
            for (StorageDetail storageDetail : storageDetailList) {
                Map<String, Object> lm = new HashMap<>();
                lm.put("itemNumber", storageDetail.getItemNumber());
                lm.put("copGno", storageDetail.getCopGno());
                lm.put("pn", storageDetail.getPn());
                lm.put("actualQty", storageDetail.getActualQty().setScale(2, RoundingMode.HALF_UP));
                lm.put("qunit", isNotEmpty(dictMap) ? dictMap.get(storageDetail.getQunit()) : "");
                lm.put("batchNo", storageDetail.getBatchNo());
                listMap.add(lm);
            }
        }
        map.put("listMap", listMap);
        //获取模板文件路径
        Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream("/templates/xls/出库单导出模版.xlsx"));
        TemplateExportParams params = new TemplateExportParams();
        params.setTemplateWb(templateWorkbook);
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        // 保存到本地文件
        String suffix = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomString(6);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        byte[] data = bos.toByteArray();

        // 获取操作系统名称
        ResponseEntity<byte[]> responseEntity = null;
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            log.info("当前操作系统为：Windows");
            InputStream in = new ByteArrayInputStream(data);
            responseEntity = ExcelToPdfUtil.excel2pdf(in, "出库单详情_" + suffix, "");
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            log.info("当前操作系统为：Linux");
            String fileName = "出库单详情_" + suffix + ".xlsx";
            String xmlPath = upLoadPath + "/downloadPdf/" + fileName;
            File file1 = new File(xmlPath);
            // Create parent directories if they don't exist
            File parentDir1 = file1.getParentFile();
            if (!parentDir1.exists()) {
                parentDir1.mkdirs();  // Create the directory and any necessary but nonexistent parent directories
            }
            FileOutputStream fos;
            try {
                fos = new FileOutputStream(file1);
                fos.write(data, 0, data.length);
                fos.flush();
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            log.info("[出库单]生成文件成功：==> {}", xmlPath);
            responseEntity = ExcelToPdfUtil.excel2pdf_(xmlPath, "出库单详情_" + suffix, "");
            // 删除文件
            FileUtil.deleteFile(xmlPath);
        } else {
            log.info("无法识别的操作系统: {}", os);
        }
        if (isEmpty(responseEntity)) {
            return Result.error("组装数据失败，未获取到字体文件！");
        }
        byte[] bytes = responseEntity.getBody();
        String pdfPath = upLoadPath + "/downloadPdf/" + "出库单详情_" + suffix + ".pdf";
        File file = new File(pdfPath);

        // Create parent directories if they don't exist
        File parentDir = file.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();  // Create the directory and any necessary but nonexistent parent directories
        }

        FileOutputStream fos1;
        try {
            fos1 = new FileOutputStream(file);
            fos1.write(bytes, 0, bytes.length);
            fos1.flush();
            fos1.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("[出库单]pdf生成文件成功：==> {}", pdfPath);
        String file_url = null;
        try {
            file_url = OssBootUtil.upload(new FileInputStream(file), "pdf_temp/" + file.getName());
            log.info("[出库单]pdf上传文件成功：==> {}", file_url);
        } catch (Exception e) {
            log.error("上传文件发生异常：{}", String.valueOf(e));
        }
        // 删除文件
        FileUtil.deleteFile(pdfPath);
        return Result.OK(file_url);
    }

    /**
     * 导出入库单列表Excel
     *
     * @param request
     * @param response
     * @param storageInfo
     * @return void
     * <AUTHOR>
     * @date 2024/9/6 11:22
     */
    @Override
    public void exportStorageInfoList(HttpServletRequest request, HttpServletResponse response, StorageInfo storageInfo) {
        List<StorageInfo> storageInfoList;
        if (isNotBlank(storageInfo.getIds())) {
            storageInfoList = this.listByIds(Arrays.asList(storageInfo.getIds().split(",")));
        } else {
            Result<?> result = this.queryPageList(1, 1000000, storageInfo, request);
            if (result.isSuccess()) {
                storageInfoList = ((IPage<StorageInfo>) result.getResult()).getRecords();
            } else {
                throw new RuntimeException(result.getMessage());
            }
        }
        List<DictModel> dictModels;
        if ("I".equals(storageInfo.getIeFlag())){
            dictModels = sysBaseApi.getDictItems("FKFS");
        } else {
            dictModels = sysBaseApi.getDictItems("SKFS");
        }
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<DictModel> dictModels1;
        if ("I".equals(storageInfo.getIeFlag())){
            dictModels1 = sysBaseApi.getDictItems("IN_STORAGE_STATUS");
        } else {
            dictModels1 = sysBaseApi.getDictItems("OUT_STORAGE_STATUS");
        }
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getValue(), dictModel.getText());
            });
        }
        if (isNotEmpty(storageInfoList)) {
            for (StorageInfo si : storageInfoList) {
                // 仓库
                if (isNotBlank(si.getStoreCode())) {
                    StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                            .eq(StoreInfo::getStoreCode, si.getStoreCode()));
                    si.setStoreCode(isNotEmpty(storeInfo) ? storeInfo.getStoreName() : "");
                }
                // 货主
                si.setCustomer(commonService.getCustomerNameById(si.getCustomer()));
                // 付款方式
                si.setPaymentMethod(isNotEmpty(dictMap) ? dictMap.get(si.getPaymentMethod()) : "");
                // 状态
                si.setStatus(isNotEmpty(dictMap1) ? dictMap1.get(si.getStatus()) : "");
            }
        }
        ExportParams params = new ExportParams();
        params.setSheetName("出入库单统计");
        params.setType(ExcelType.XSSF);
        params.setStyle(ExcelExportStylerBorderImpl.class);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> mapInv = new HashMap<>();
        mapInv.put("title", params);
        mapInv.put("entity", StorageInfo.class);
        mapInv.put("data", storageInfoList);
        list.add(mapInv);
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导出入库单详情Excel
     *
     * @param request
     * @param response
     * @param storageInfo1
     * @return void
     * <AUTHOR>
     * @date 2024/9/12 11:05
     */
    @Override
    public void exportInstoreXlsx(HttpServletRequest request, HttpServletResponse response, StorageInfo storageInfo1) throws IOException {
        List<StorageInfo> storageInfoList = baseMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                .in(StorageInfo::getId, Arrays.asList(storageInfo1.getIds().split(","))));
        // 此处导出默认只有一个！！
        if (storageInfoList.size() != 1) {
            throw new RuntimeException("未获取到导出数据，请检查！");
        }
        StorageInfo storageInfo = storageInfoList.get(0);
        // 处理Excel
        Map<String, Object> map = new HashMap<>();
        map.put("storageNo", storageInfo.getStorageNo());
        StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getStoreCode, storageInfo.getStoreCode()));
        if (isNotEmpty(storeInfo)) {
            map.put("storeName", storeInfo.getStoreName());
        } else {
            map.put("storeName", "");
        }
        if (isNotBlank(storageInfo.getCustomer())) {
            String tenantName = null;
            try {
                // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageInfo.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：" + e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageInfo.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageInfo.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageInfo.setCustomerStr(tenantName);
            }
        }
        map.put("customerStr", storageInfo.getCustomerStr());
        map.put("auditDate", isNotEmpty(storageInfo.getAuditDate()) ? DateUtil.formatDateTime(storageInfo.getAuditDate()) : "");
        List<DictModel> dictModels = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<StorageDetail> storageDetailList = storageDetailService.list(new LambdaQueryWrapper<StorageDetail>()
                .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo())
                .orderByAsc(StorageDetail::getItemNumber));
        if (isNotEmpty(storageDetailList)) {
            for (StorageDetail storageDetail : storageDetailList) {
                Map<String, Object> lm = new HashMap<>();
                lm.put("itemNumber", storageDetail.getItemNumber());
                lm.put("copGno", storageDetail.getCopGno());
                lm.put("pn", storageDetail.getPn());
                lm.put("actualQty", storageDetail.getActualQty().setScale(2, RoundingMode.HALF_UP));
                lm.put("qunit", isNotEmpty(dictMap) ? dictMap.get(storageDetail.getQunit()) : "");
                lm.put("batchNo", storageDetail.getBatchNo());
                listMap.add(lm);
            }
        }
        map.put("listMap", listMap);
        String pathTemplate = "/templates/xls/入库单导出模版.xlsx";
        if ("E".equals(storageInfo.getIeFlag())) {
            pathTemplate = "/templates/xls/出库单导出模版.xlsx";
        }
        //获取模板文件路径
        Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream(pathTemplate));
        TemplateExportParams params = new TemplateExportParams();
        params.setTemplateWb(templateWorkbook);
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 出入库单表头转出入库单（出入区）表头
     *
     * @param stockGoodsTypeList
     * @param storageDetailList
     * @return void
     * <AUTHOR>
     * @date 2024/3/6 13:04
     */
    private void toStockGoodsTypes(AppHeadType appHeadType, List<StockGoodsType> stockGoodsTypeList, List<StorageDetail> storageDetailList) {
        if (isEmpty(storageDetailList)) {
            return;
        }
        List<AppGoodsType> appGoodsTypeList = appGoodsTypeMapper.selectList(new LambdaQueryWrapper<AppGoodsType>()
                .eq(AppGoodsType::getHeadId, appHeadType.getId()));
        if (ObjectUtils.isEmpty(appGoodsTypeList)) {
            throw new RuntimeException("未查询到正常执行状态的业务申报表表体信息！");
        }
        Map<String, List<AppGoodsType>> appGoodsTypeMap = appGoodsTypeList.stream()
                .collect(Collectors.groupingBy(i -> isNotEmpty(i.getOriactGdsSeqno()) ? i.getOriactGdsSeqno().toString() : "-1"));
        List<String> noExistNos = new ArrayList<>();
        for (StorageDetail storageDetail : storageDetailList) {
            if (!appGoodsTypeMap.containsKey(storageDetail.getBatchNo())) {
                noExistNos.add(storageDetail.getCopGno() + " - " + storageDetail.getBatchNo());
            }
        }
        if (isNotEmpty(noExistNos)) {
            String errorMessage = String.format("以下商品[%s]在业务申报表中不存在，无法生成对应的出入库单！", CollUtil.join(CollUtil.distinct(noExistNos), "，"));
            throw new RuntimeException(errorMessage);
        }
        AtomicInteger i = new AtomicInteger(1);
        storageDetailList.forEach(storageDetail -> {
            AppGoodsType appGoodsType = isNotEmpty(appGoodsTypeMap.get(storageDetail.getBatchNo())) ? appGoodsTypeMap.get(storageDetail.getBatchNo()).get(0) : new AppGoodsType();
            StockGoodsType stockGoodsType = new StockGoodsType();
            BeanUtil.copyProperties(appGoodsType, stockGoodsType, CopyOptions.create().ignoreNullValue());
            stockGoodsType.setId(null);
            stockGoodsType.setStorageDetailId(isNotEmpty(storageDetail.getId()) ? storageDetail.getId().toString() : null); // 关联出入库单商品ID
            stockGoodsType.setSasStockSeqno(i.get()); // 商品序号
            stockGoodsType.setOriactGdsSeqno(storageDetail.getBatchNo()); // 底帐商品序号
            stockGoodsType.setGdsMtno(isNotBlank(appGoodsType.getGdsMtno())? appGoodsType.getGdsMtno() : storageDetail.getCopGno()); // 料号
            stockGoodsType.setGdsNm(isNotBlank(appGoodsType.getGdsNm()) ? appGoodsType.getGdsNm() : storageDetail.getPn()); // 商品名称
            stockGoodsType.setDclQty(storageDetail.getActualQty()); // 申报数量，取仓库商品实际出库数量
            stockGoodsType.setGdsSpcfModelDesc(appGoodsType.getGdsSpcfModelDesc());
            stockGoodsType.setDclCurrcd(appGoodsType.getDclCurrcd());
            stockGoodsType.setDclUnitcd(isNotBlank(appGoodsType.getDclUnitcd())? appGoodsType.getDclUnitcd() : storageDetail.getQunit());
            stockGoodsType.setLawfUnitcd(isNotBlank(appGoodsType.getLawfUnitcd())? appGoodsType.getLawfUnitcd() : storageDetail.getQunit());
            stockGoodsType.setSecdLawfUnitcd(isNotBlank(appGoodsType.getSecdLawfUnitcd())? appGoodsType.getSecdLawfUnitcd() : storageDetail.getQunit());
            stockGoodsType.setDclUprcAmt(isNotEmpty(appGoodsType.getDclUprcAmt())? appGoodsType.getDclUprcAmt() : storageDetail.getDcluprcamt());
            stockGoodsType.setDclTotalAmt(isNotEmpty(appGoodsType.getDclTotalAmt())? appGoodsType.getDclTotalAmt() : storageDetail.getTotalPriceTax());
            if (isEmpty(stockGoodsType.getDclTotalAmt())) {
                stockGoodsType.setDclTotalAmt(stockGoodsType.getDclQty().multiply(isNotEmpty(stockGoodsType.getDclUprcAmt()) ? stockGoodsType.getDclUprcAmt() : BigDecimal.ZERO));
            }
            i.getAndIncrement();
            stockGoodsTypeList.add(stockGoodsType);
        });
    }

    /**
     * 出入库单表头转出入库单（出入区）表头
     *
     * @param stockHeadType
     * @param storageInfoList
     * @return void
     * <AUTHOR>
     * @date 2024/3/6 11:38
     */
    private void toStockHeadType(AppHeadType appHeadType, StockHeadType stockHeadType, List<StorageInfo> storageInfoList) {
        stockHeadType.setStockTypecd(storageInfoList.get(0).getIeFlag());
        stockHeadType.setSasDclNo(appHeadType.getSasDclNo());
//        stockHeadType.setDecStatus("0");
        stockHeadType.setDclTypecd("1");
        stockHeadType.setMasterCuscd("4301");
        stockHeadType.setBusinessTypecd("A");
        stockHeadType.setDclcusFlag("2");
        stockHeadType.setDclEr(storageInfoList.get(0).getSalesman());
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, storageInfoList.get(0).getCustomer()));
        EnterpriseInfo enterpriseInfo1 = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        if (ObjectUtils.isEmpty(enterpriseInfo1)) {
            enterpriseInfo1 = new EnterpriseInfo();
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (isNotEmpty(enterpriseInfo)) {
            stockHeadType.setAreainEtpsno(enterpriseInfo.getCustomsDeclarationCode());
            stockHeadType.setAreainEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
            stockHeadType.setAreainEtpsNm(enterpriseInfo.getEnterpriseFullName());
        } else {
            Commissioner commissioner = commissionerService.getOne(new LambdaQueryWrapper<Commissioner>()
                    .eq(Commissioner::getDepartcd, storageInfoList.get(0).getCustomer()));
            if (isNotEmpty(commissioner)) {
                stockHeadType.setAreainEtpsno(commissioner.getDepartcd());
                stockHeadType.setAreainEtpsSccd(commissioner.getUnifiedSocialCreditCode());
                stockHeadType.setAreainEtpsNm(commissioner.getCommissionerFullName());
            }
        }
        // 2024/3/11 11:15@ZHANGCHAO 追加/变更/完善：申报企业 - 租户
        stockHeadType.setDclEtpsno(enterpriseInfo.getCustomsDeclarationCode());
        stockHeadType.setDclEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        stockHeadType.setDclEtpsNm(enterpriseInfo.getEnterpriseFullName());

        stockHeadType.setInputCode(enterpriseInfo.getCustomsDeclarationCode());
        stockHeadType.setInputSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        stockHeadType.setInputName(enterpriseInfo.getEnterpriseFullName());
        // TODO 还转换哪些字段？？
    }

    /**
     * 处理审核时占用或解除占用
     *
     * @param storageDetail
     * @param isAudits
     * @return void
     * <AUTHOR>
     * @date 2024/1/28 1:16
     */
    private void handleOccupyStatus(StorageDetail storageDetail, boolean isAudits) {
        try {
            if (isAudits) {
//                Result<?> addOccupyHandleResult = storeStocksService.addOccupyHandle(storageDetail);
                Result<?> addOccupyHandleResult = storeStocksService.addOccupyHandleOneToMany(storageDetail);
                if (addOccupyHandleResult.isSuccess()) {
                    storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
                            .set(StorageDetail::getStocksFlowId, addOccupyHandleResult.getResult())
                            .eq(StorageDetail::getId, storageDetail.getId()));
                } else {
                    throw new RuntimeException(addOccupyHandleResult.getMessage());
                }
            } else {
//                Result<?> deOccupyHandleResult = storeStocksService.deOccupyHandle(storageDetail);
                Result<?> deOccupyHandleResult = storeStocksService.deOccupyHandleOneToMany(storageDetail);
                if (deOccupyHandleResult.isSuccess()) {
                    storageDetailService.update(null, new UpdateWrapper<StorageDetail>().lambda()
                            .set(StorageDetail::getStocksFlowId, null)
                            .eq(StorageDetail::getId, storageDetail.getId()));
                } else {
                    throw new RuntimeException(deOccupyHandleResult.getMessage());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 根据租户id自动生成出入库单根据核注单
     *
     * @author: zhengliansong
     * @version: 1.0
     * @date: 2024/11/11 15:45
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> autoGenerateStorageByInvtJob(String tenantId, String startDate, String endDate) {
        int successCount;
        try {
            successCount = 0;
            if (isNotBlank(tenantId)) {
                List<NemsInvtHead> nemsInvtHeads = baseMapper.listInvtByGenerateStorage(tenantId, startDate, endDate);
                if (nemsInvtHeads.size() > 0) {
                    // 设置忽略租户插件
                    InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                    //本租户的仓库信息
                    List<StoreInfo> storeInfos = storeInfoMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                            .eq(StoreInfo::getPurpose, "1")
                            .eq(StoreInfo::getTenantId, tenantId));
                    //本租户储位信息
                    List<StoreSpace> storeSpaces = new ArrayList<>();
                    if (storeInfos.size() > 0) {
                        storeSpaces = storeSpaceMapper.selectList(new LambdaQueryWrapper<StoreSpace>()
                                .eq(StoreSpace::getStoreId, storeInfos.get(0).getId())
                                .eq(StoreSpace::getStatus, "0"));

                    }

                    //本租户的库区信息
                    List<StoreArea> storeAreas = storeAreaMapper.selectList(new LambdaQueryWrapper<StoreArea>()
                            .eq(StoreArea::getTenantId, tenantId)
                            .eq(StoreArea::getStatus, "1"));


                    //所有的表体数据
                    List<Long> headIds = nemsInvtHeads.stream().map(NemsInvtHead::getId)
                            .collect(Collectors.toList());
                    List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.selectList(new LambdaQueryWrapper<NemsInvtList>()
                            .in(NemsInvtList::getInvId, headIds));
                    //循环处理核注单表头
                    for (NemsInvtHead nemsInvtHead : nemsInvtHeads) {
                        StorageInfo storageInfo = new StorageInfo();
                        String storageNo = "";
                        if ("I".equals(nemsInvtHead.getImpexpMarkcd())) {
                            storageNo = serialNumberService.getSerialnumberByCustomerCode("BPW", 4);
                        } else if ("E".equals(nemsInvtHead.getImpexpMarkcd())) {
                            storageNo = serialNumberService.getSerialnumberByCustomerCode("WS", 4);
                        }
                        storageInfo.setStorageNo(storageNo);//出入库单号
                        storageInfo.setStoreCode(!storeInfos.isEmpty() ? storeInfos.get(0).getStoreCode() : null);//关联仓库编码
                        storageInfo.setAppDate(nemsInvtHead.getInvtDclTime());//业务日期
                        //  2. 出入库单的货主：自动取核注单关联的报关单的境内收发货人
                        String clearanceNo = "";
                        if ("1".equals(nemsInvtHead.getDclcusTypecd())) {//关联
                            clearanceNo = nemsInvtHead.getRltEntryNo();
                        } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) {//对应
                            clearanceNo = nemsInvtHead.getEntryNo();
                        }
                        List<DecHead> decHeads = new ArrayList<>();
                        if (isNotBlank(clearanceNo)) {
                            decHeads = decHeadMapper.selectList(new LambdaQueryWrapper<DecHead>()
                                    .eq(DecHead::getClearanceNo, clearanceNo));
                            if (decHeads.size() > 0 && isNotBlank(decHeads.get(0).getOptUnitName())) {
                                //如果平台不存在该委托方，则自动新增
                                List<Commissioner> commissioners
                                        = commissionerMapper.selectList(new LambdaQueryWrapper<Commissioner>()
                                        .eq(Commissioner::getCommissionerFullName, decHeads.get(0).getOptUnitName())
                                        .eq(Commissioner::getTenantId, tenantId));
                                //存在直接取
                                if (commissioners.size() > 0) {
                                    storageInfo.setCustomer(commissioners.get(0).getId());//货主id
                                } else {
                                    //创建委托方
                                    Commissioner commissioner = new Commissioner();
                                    commissionerMapper.insert(commissioner
                                            .setCommissionerFullName(decHeads.get(0).getOptUnitName())
                                            .setTenantId(Long.valueOf(tenantId))
                                            .setCreateBy("admin").setCreateTime(new Date()));
                                    storageInfo.setCustomer(commissioner.getId());//货主id
                                }
                            }

                        }
                        storageInfo.setIeFlag(nemsInvtHead.getImpexpMarkcd());//出入库类型 I入库E出库
                        storageInfo.setTenantId(Long.valueOf(tenantId));//租户id
                        storageInfo.setCreateBy("admin");
                        storageInfo.setCreateDate(new Date());
                        storageInfo.setBondInvtNo(nemsInvtHead.getBondInvtNo());//关联核注单清单编号
                        baseMapper.insert(storageInfo);
                        successCount++;
                        //处理表体信息====================
                        //该核注单的表体
                        List<NemsInvtList> nemsInvtListList1 = nemsInvtListList.stream().filter(i ->
                                        Objects.equals(i.getInvId(), nemsInvtHead.getId()))
                                .collect(Collectors.toList());
                        if (nemsInvtListList1.size() > 0) {
                            for (NemsInvtList nemsInvtList : nemsInvtListList1) {
                                StorageDetail storageDetail = new StorageDetail();
                                storageDetail.setStorageNo(storageInfo.getStorageNo());//出入库单号
                                storageDetail.setStoreCode(storageInfo.getStoreCode());//仓库编码
                                //库区信息
                                storageDetail.setAreaCode(!storeAreas.isEmpty() ? storeAreas.get(0).getAreaCode() : null);//库区编码
                                storageDetail.setAreaName(!storeAreas.isEmpty() ? storeAreas.get(0).getAreaName() : null);//库区编码
                                //储位信息
                                storageDetail.setSpaceCode(
                                        !storeSpaces.isEmpty() ? storeSpaces.get(0).getSpaceCode() : null);//关联储位编码
                                storageDetail.setSpaceName(
                                        !storeSpaces.isEmpty() ? storeSpaces.get(0).getSpaceName() : null);//关联储位名称
                                storageDetail.setItemNumber(String.valueOf(nemsInvtList.getPutrecSeqno()));//项号
                                if("I".equals(nemsInvtHead.getImpexpMarkcd())){
                                    storageDetail.setBatchNo(clearanceNo);//出入库单表体批次号：自动带报关单号
                                    //运单号
                                    storageDetail.setBillNo(!decHeads.isEmpty() ? decHeads.get(0).getBillCode() : null);
                                    storageDetail.setBondInvtNo(nemsInvtHead.getBondInvtNo());
                                }

                                storageDetail.setCopGno(nemsInvtList.getGdsMtno());//物料号

                                storageDetail.setModel(nemsInvtList.getHsmodel());//规格型号
                                storageDetail.setQunit(nemsInvtList.getDclUnitcd());//单位
                                storageDetail.setTenantId(Long.valueOf(tenantId));
                                storageDetail.setCreateBy("admin");
                                storageDetail.setCreateDate(new Date());
                                storageDetail.setInvtListId(String.valueOf(nemsInvtList.getId()));

                                storageDetailService.save(storageDetail);
                                //回填核注清单数据
                                nemsInvtListMapper.update(null , new UpdateWrapper<NemsInvtList>().lambda()
                                        .set(NemsInvtList::getStorageNo, storageDetail.getStorageNo())
                                        .set(NemsInvtList::getStorageDetailId, storageDetail.getId())
                                        .eq(NemsInvtList::getId, storageDetail.getInvtListId()));
                            }
                        }
                    }
                    // 关闭忽略策略
                    InterceptorIgnoreHelper.clearIgnoreStrategy();
                }

            }
        } catch (Exception e) {
            return Result.error("自动生成出入库单根据核注单出现异常：" + e.getMessage());
        }
        return Result.OK("自动生成出入库单根据核注单执行成功,共生成" + successCount+"票出入库单");
    }
}
