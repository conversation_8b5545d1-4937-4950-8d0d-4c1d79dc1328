package org.jeecg.modules.business.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * 调用外部接口工具类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/6/14 下午3:58
 */
@Slf4j
@Component
public class ApiUtil {

    private static String APP_KEY;
    private static String SECRET;

    public static void main(String[] args) throws IOException {
//        String url = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDQuery";
//        String url = "https://api.jgsoft.com.cn:15555/open-api/sm/SwSetSWUser";
//        String url = "https://api.jgsoft.com.cn:15555/open-api/sm/PtSetSWUser";
//        String url = "https://api.jgsoft.com.cn:15555/open-api/sm/GetEnterInfo";
//        String url = "https://api.jgsoft.com.cn:15555/open-api/sg/CqQuery";
        String url = "https://api.jgsoft.com.cn:15555/open-api/sg/DpImpQuery";
        // 业务参数, -- 要用LinkedHashMap
        Map<String, Object> jsonMap = new LinkedHashMap<>();
//        jsonMap.put("swid", "2100090030685");
//        jsonMap.put("systemId", "SC");
        jsonMap.put("FS", "");
        jsonMap.put("DH", "FQDOF00757");
//        jsonMap.put("JKHC", "526EI");
//        jsonMap.put("swid", "8930000026341"); // 操作员卡号
//        jsonMap.put("systemId", "SC");
//        jsonMap.put("tradeCode", "3701340A0C");
//        jsonMap.put("manualNo", "C430125A0006");
//        jsonMap.put("gdsSeqNo", "18");
//        jsonMap.put("password", "88888888");
//        jsonMap.put("logintype", "1");
//        jsonMap.put("seqNo", "202500000000690719");
//        jsonMap.put("beginTime", "2024-11-19");
//        jsonMap.put("endTime", "2024-11-20");
//        jsonMap.put("code", "青岛亚是加食品有限公司");
//        jsonMap.put("user", "MXzDX83RwTN5CzK1rwHi");
//        jsonMap.put("tdh", "140500554142");
//        jsonMap.put("nodeType", "2");
//        jsonMap.put("enShipName", "EVER ACME");
//        jsonMap.put("voy", "1342W");
//        jsonMap.put("nodeType", "2");
//                jsonMap.put("dclStartDate", "20250401");
//        jsonMap.put("dclEndDate", "20250420");
//        jsonMap.put("enShipName", "EVER ACME");
//        jsonMap.put("voy", "1342W");
//        jsonMap.put("nodeType", "2");
//        jsonMap.put("flag", "1");


        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = ApiUtil.sendOpenApi(url, JSON.toJSONString(jsonMap));
        log.info(result);
//        saveJsonToFile(result, "DEC", "SYNC_DEC_" + jsonMap.get("cusCiqNo"));
        JSONObject jsonObject = JSON.parseObject(result);
        System.out.println(jsonObject);

//        byte[] decodedBytes = Base64.getDecoder().decode(jsonObject.getString("data"));
//        String filePath = "C:\\Users\\<USER>\\Desktop\\xxxxxxxxxx.pdf";
//        // 将解码后的字节数据写入文件
//        try (FileOutputStream fos = new FileOutputStream(filePath)) {
//            fos.write(decodedBytes);
//            fos.flush();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 请求三方接口
     *
     * @param url
     * @param jsonString
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/18 下午4:08
     */
    public static String sendOpenApi(String url, String jsonString) {
        String appKey = "MXzDX83RwTN5CzK1rwHi";
        String secret = "0lJCpKpMUfHtW2oBCYk2Dj93pVdZSR5Yu4sW4JaI";
        APP_KEY = isBlank(APP_KEY) ? appKey : APP_KEY;
        SECRET = isBlank(SECRET) ? secret : SECRET;
        String timestamp = getTime();
        String nonce = "";
//        if("https://api.jgsoft.com.cn:15555/open-api/sm/GetManifestInfo".equals(url)){
//            nonce = getNonce_();
//        }else {
        nonce = getNonce_();
//        }

        // 业务参数必须进行编码
        try {
            jsonString = URLEncoder.encode(jsonString, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        log.info(jsonString);
        // 系统参数
        Map<String, Object> param = new HashMap<>();
        param.put("accessKey", APP_KEY);
        param.put("data", jsonString);
        param.put("timestamp", timestamp);
        param.put("nonce", nonce);
        String sign = null;
        try {
            sign = buildSign(param, SECRET);
        } catch (Exception e) {
            e.printStackTrace();
        }
        param.put("reqSign", sign);
        String postJson = JSON.toJSONString(param);
        log.info(postJson);
        String resp = HttpUtil.post(url, param); // post请求，参数会装载到body
        // 处理返回数据
//        JSONObject jsonObject = JSONObject.parseObject(resp);
        log.info(resp);
        return resp;
    }

    /**
     * 构建签名
     *
     * @param paramsMap 参数
     * @param secret    密钥
     * @return
     */
    public static String buildSign(Map<String, ?> paramsMap, String secret) {
        Set<String> keySet = paramsMap.keySet();
        List<String> paramNames = new ArrayList<String>(keySet);
        Collections.sort(paramNames);
        StringBuilder paramNameValue = new StringBuilder();
        for (String paramName : paramNames) {
            paramNameValue.append(paramName).append(paramsMap.get(paramName));
        }
        String source = secret + paramNameValue + secret;
        System.out.println("source: " + source);
        return md5(source);
    }

    /**
     * 生成md5,全部大写
     *
     * @param message
     * @return
     */
    public static String md5(String message) {
        try {
            // 1 创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 2 将消息变成byte数组
            byte[] input = message.getBytes();
            // 3 计算后获得字节数组,这就是那128位了
            byte[] buff = md.digest(input);
            // 4 把数组每一字节（一个字节占八位）换成16进制连成md5字符串
            return byte2hex(buff);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 二进制转十六进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    public static String getTime() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public static String getNonce() {
        String nonce = RandomUtil.randomNumbers(6);
        if (nonce.startsWith("0")) {
            nonce = getNonce();
        }
        return nonce;
    }

    public static String getNonce_() {
        String nonce = RandomUtil.randomNumbers(6) + System.currentTimeMillis();
        ;
        if (nonce.startsWith("0")) {
            nonce = getNonce_();
        }
        return nonce;
    }

    @Value("${api.auth.appKey}")
    public void setAppKey(String appKey) {
        ApiUtil.APP_KEY = appKey;
    }

    @Value("${api.auth.secret}")
    public void setSecret(String secret) {
        ApiUtil.SECRET = secret;
    }

    /**
     * 请求三方接口
     *
     * @param url
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/18 下午4:08
     */
    public static String requestPost(String url, String jsonString) {
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 系统参数
        Map<String, Object> param = new HashMap<>();
        param.put("timeStamp", timestamp);
        String sign = null;
        try {
            sign = buildSign(param, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/json");
        request.header("timestamp", timestamp);
        request.header("sign", sign);
        request.body(jsonString);
        int readTimeout = 180000;
        request.timeout(readTimeout);
        HttpResponse response = request.execute();
        String responseContent = response.body();
        log.info("[requestPost]服务调用结果：{}", responseContent);
        return responseContent;
    }
}
