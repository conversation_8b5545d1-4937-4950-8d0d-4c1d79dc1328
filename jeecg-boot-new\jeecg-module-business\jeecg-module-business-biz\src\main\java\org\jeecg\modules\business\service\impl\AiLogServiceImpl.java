package org.jeecg.modules.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.IpUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.business.entity.AiLog;
import org.jeecg.modules.business.mapper.AiLogMapper;
import org.jeecg.modules.business.service.IAiLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <p>
 * 系统日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class AiLogServiceImpl extends ServiceImpl<AiLogMapper, AiLog> implements IAiLogService {

    /**
     * 保存日志
     *
     * @param logDTO
     */
    @Override
    public void addLog(AiLog logDTO) {
        if(oConvertUtils.isEmpty(logDTO.getId())){
            logDTO.setId(String.valueOf(IdWorker.getId()));
        }
        try {
            //获取request
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            //设置IP地址
            logDTO.setIp(IpUtils.getIpAddr(request));
        } catch (Exception e) {
            logDTO.setIp("127.0.0.1");
        }
        //获取登录用户信息
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            logDTO.setUserid(user.getUsername());
            logDTO.setUsername(user.getRealname());
        } catch (Exception e) {
            //e.printStackTrace();
        }
        logDTO.setCreateTime(new Date());
        //保存日志（异常捕获处理，防止数据太大存储失败，导致业务失败）JT-238
        try {
            baseMapper.saveLog(logDTO);
        } catch (Exception e) {
            log.warn(" LogContent length : "+logDTO.getLogContent().length());
            log.warn(e.getMessage());
        }
    }
}
