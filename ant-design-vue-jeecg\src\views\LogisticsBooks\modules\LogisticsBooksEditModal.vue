<template>
	<j-modal
		:title="'物流账册 ' + title"
		:width="width"
		:visible="visible"
		@ok="handleSave"
		:okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
		@cancel="handleCancel"
		cancelText="关闭"
		selfCloseAction="closePop"
		:disableSubmit="disableSubmit"
	>
		<template slot="footer">
			<a-button type="default" @click="handleCancel">关闭</a-button>
			<a-button v-has="'ems:edit'" v-show="!disableSubmit" type="primary" @click="handleSave">保存</a-button>
		</template>

		<a-spin :spinning="confirmLoading">
			<a-collapse v-model="activeKeys" :bordered="false" style="margin-top: -10px">
				<!-- 基本信息 -->
				<a-collapse-panel key="1" header="基本信息" >
					<j-form-container :disabled="disableSubmit">
						<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
							<a-row>
								<a-col :span="8">
									<a-form-model-item label="预录入统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="seqNo">
										<a-input placeholder='请输入预录入统一编号' v-model="model.seqNo"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="物流账册号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="emsNo">
										<a-input placeholder='请输入物流账册号' v-model="model.emsNo"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="企业内部编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="copEmsNo">
										<a-input placeholder='请输入企业内部编号' v-model="model.copEmsNo"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="经营单位编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeCode">
										<a-input placeholder='请输入经营单位编码' v-model="model.tradeCode" :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="经营单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeSccd">
										<a-input v-model="model.tradeSccd" placeholder='经营单位社会信用代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="经营单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeName">
										<a-input v-model="model.tradeName" placeholder='请输入经营单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareCode">
										<a-input v-model="model.declareCode" placeholder='请输入申报单位编码' :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareSccd">
										<a-input v-model="model.declareSccd" placeholder='申报单位社会信用代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareName">
										<a-input v-model="model.declareName" placeholder='请输入申报单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareType">
										<a-select placeholder="请选择申报单位类型" allowClear showSearch v-model="model.declareType">
											<a-select-option value="1">企业</a-select-option>
											<a-select-option value="2">代理公司</a-select-option>
											<a-select-option value="3">报关行</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="主管海关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="masterCustoms">
										<j-dict-select-tag
											v-model="model.masterCustoms"
											type="node-limit"
											dictCode="erp_customs_ports,name,customs_port_code"
											placeholder="请选择主管海关"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="区域场所类别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="regionalSiteType">
										<j-dict-select-tag :allowClear="true"
																			 placeholder="请选择区域场所类别"
																			 v-model="model.regionalSiteType"
																			 type="node-limit"
																			 dictCode="QYCSLB" @change='regionalSiteTypeChange'/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="仓库代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ownerCode">
										<a-input v-model="model.ownerCode" placeholder='请输入仓库代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="仓库名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ownerName">
										<a-input v-model="model.ownerName" placeholder='请输入仓库名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8" v-if='false'>
									<a-form-model-item label="账册类型" ref="emsTypeRef" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="emsType">
										<a-cascader v-model="tempEmsType" :options="options" placeholder="请选择账册类型" @change="emsTypeChange"/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="仓库面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="area">
										<a-input-number v-model="model.area" placeholder="请输入仓库面积" :min='0' :precision="2" style='width: 100%;'/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="仓库容积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="volume">
										<a-input v-model="model.volume" placeholder='请输入仓库容积'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="仓库地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
										<j-remarks-component
											v-model="model.address"
											placeholder="请输入仓库地址"
											:readOnly="disableSubmit"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="associates">
										<a-input v-model="model.associates" placeholder='请输入联系人'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="telephone">
										<a-input v-model="model.telephone" placeholder='请输入联系电话'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="企业类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="businessType">
										<j-dict-select-tag :allowClear="true"
																			 placeholder="请选择企业类型"
																			 v-model="model.businessType"
																			 type="node-limit"
																			 dictCode="QYCSLB" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="账册有效期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endDate">
										<j-date is-node placeholder="请选择账册有效期" v-model="model.endDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="备案批准日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recordDate">
										<j-date is-node placeholder="请选择备案批准日期" v-model="model.recordDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="变更批准日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="changeDate">
										<j-date is-node placeholder="请选择变更批准日期" v-model="model.changeDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="记账模式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="keepingMode">
										<a-select v-model="model.keepingMode" placeholder="请选择记账模式">
											<a-select-option :value="2">不累计</a-select-option>
											<a-select-option :value="1">累计</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="录入单位编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inputCode">
										<a-input v-model="model.inputCode" placeholder='请输入录入单位编码' :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="录入单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inputCreditCode">
										<a-input v-model="model.inputCreditCode" placeholder='录入单位社会信用代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="录入单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inputName">
										<a-input v-model="model.inputName" placeholder='请输入录入单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="录入日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inputDate">
										<j-date is-node placeholder="请选择录入日期" v-model="model.inputDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareDate">
										<j-date is-node placeholder="请选择申报日期" v-model="model.declareDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="note">
										<j-remarks-component
											v-model="model.note"
											placeholder="请输入备注"
											:maxLength="500"
											:readOnly="disableSubmit"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8" v-if='false'>
									<a-form-model-item label="登记日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="byDate">
										{{ model.byDate }}
									</a-form-model-item>
								</a-col>
								<a-col :span="8" v-if='false'>
									<a-form-model-item label="登记人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="byName">
										{{ model.byName }}
									</a-form-model-item>
								</a-col>
							</a-row>
						</a-form-model>
					</j-form-container>
				</a-collapse-panel>
			</a-collapse>
		</a-spin>
	</j-modal>
</template>
<script>
import { getAction, httpAction } from '@/api/manage'
import { checkOwnerCode } from '@/api/api'
import store from '@/store'

export default {
	name: 'LogisticsBooksEditModal',
	data() {
		return {
			tempEmsType: [],
			options: [
				{
					value: 'TW',
					label: 'TW-账册',
					children: [
						{
							value: 'M',
							label: 'M贸易'
						},
						{
							value: 'W',
							label: 'W物流'
						}
					]
				},
				{
					value: 'L',
					label: 'L-账册'
				}
			],
			model: {
				status: '1',
				emsType: 'L', //账册类型
				useType: '',
				keepingMode: 1, //记账模式
				businessType: '',
				byDate: this.formatDate((new Date()).getTime(), 'yyyy-MM-dd hh:mm:ss'),//登记日期
				byName: store.getters.userInfo.username
			},
			activeKeys: ['1', '2'],
			validatorRules: {
				emsNo: [{ required: true, message: '请输入物流账册号!' }],
				tradeCode: [{ checkName: '经营单位编码',required: true,max:10,validator: this.checkNo }],
				tradeName: [{ required: true, message: '请填写经营单位名称!'}],
				declareType: [{ required: true, message: '请选择申报单位类型!'}],
				masterCustoms: [{ required: true, message: '请选择主管海关!'}],
				regionalSiteType: [{ required: true, message: '请选择区域场所类别!'}],
				ownerCode: [{ required: true, message: '请输入仓库代码!'}],
				ownerName: [{ required: true, message: '请填写仓库名称!' }],
				area: [{ required: true, message: '请输入仓库面积!' }],
				address: [{ required: true, message: '请填写仓库地址!' }],
				associates: [{ required: true, message: '请填写联系人!' }],
				telephone: [{ required: true, message: '请填写联系电话!' }],
				businessType: [{ required: true, message: '请选择企业类型!' }],
				endDate: [{ required: true, message: '请选择账册有效期!' }],
				keepingMode: [{ required: true, message: '请选择记账模式!' }],
				inputDate: [{ required: true, message: '请填写录入日期!' }],
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 8 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 14 },
			},
			title: '',
			width: 1180,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			url: {
				save: '/business/ems/saveEmsHead',
				getById: '/business/ems/getEmsHeadById',
			}
		}
	},
	created() {},
	methods: {
		add() {
			this.visible = true
		},
		edit(record) {
			this.initModel(record)
			this.visible = true
		},
		initModel(value) {
			this.confirmLoading = true
			let val = value
			if (val == undefined) {
				val = this.model
			}
			let params = {
				id: val.id,
			}
			if (val.id != null) {
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.model = record
							this.tempEmsType.push(this.model.emsType)
							this.tempEmsType.push(this.model.useType)
						} else {
							// 失败
							this.$message.warning(res.message || res)
							this.close()
						}
					})
					.finally(() => {
						this.confirmLoading = false
					})
			} else {
				// 新增
				this.confirmLoading = false
			}
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的物流账册数据：', this.model)
					httpAction(this.url.save, this.model, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								this.close()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		emsTypeChange (value) {
			if (value.length === 1) {
				this.model.emsType = value[0]
				this.model.useType = ''
			} else {
				this.model.emsType = value[0]
				this.model.useType = value[1]
			}
		},
		regionalSiteTypeChange(value) {
			this.model.businessType = value
		},
		close() {
			this.tempEmsType = []
			this.model = {
				status: '1',
				emsType: 'L', //账册类型
				useType: '',
				keepingMode: 1, //记账模式
				businessType: '',
				byDate: this.formatDate((new Date()).getTime(), 'yyyy-MM-dd hh:mm:ss'),//登记日期
				byName: store.getters.userInfo.username
			}
			this.$emit('close')
			this.visible = false
		},
		handleCancel () {
			this.close()
		},
		checkBind(rule, value, callback) {
			if (!value) {
				callback(new Error("请输入仓库代码!"))
			} else {
				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(new Error(`长度不能大于${rule.max}位!`))
				}
				let params = {
					ownerCode: value,
					id: this.model.id
				}
				checkOwnerCode(params).then((res) => {
					if (res.success) {
						callback()
					} else {
						callback(new Error("此仓库代码已被绑定!"))
					}
				})
			}
		},
		checkNo (rule, value, callback) {
			if (rule.required) {
				if (this.isEmpty(value)) {
					callback(new Error(`请输入${rule.checkName}!`))
				}
			}
			if (!this.isEmpty(value)) {
				let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
				if (rule.checkNum) {
					if (!reg.test(value)) {
						callback(new Error('请输入数字!'))
					}
				}
				if (value < 0) {
					callback(new Error('不能输入负数!'))
				}
				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(new Error(`长度不能大于${rule.max}位!`))
				}
				if ((value.toString()).indexOf('.') != -1) {
					callback(new Error('不能含有小数点!'))
				}
			}
			callback()
		},
		/**
		 * 时间格式化
		 * @param value
		 * @param fmt
		 * @returns {*}
		 */
		formatDate (value, fmt) {
			let regPos = /^\d+(\.\d+)?$/
			if (regPos.test(value)) {
				//如果是数字
				let getDate = new Date(value)
				let o = {
					'M+': getDate.getMonth() + 1,
					'd+': getDate.getDate(),
					'h+': getDate.getHours(),
					'm+': getDate.getMinutes(),
					's+': getDate.getSeconds(),
					'q+': Math.floor((getDate.getMonth() + 3) / 3),
					'S': getDate.getMilliseconds()
				}
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
				}
				for (let k in o) {
					if (new RegExp('(' + k + ')').test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
					}
				}
				return fmt
			} else {
				//TODO
				value = value.trim()
				return value.substr(0, fmt.length)
			}
		}
	}
}
</script>

<style scoped lang='less'>

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
</style>