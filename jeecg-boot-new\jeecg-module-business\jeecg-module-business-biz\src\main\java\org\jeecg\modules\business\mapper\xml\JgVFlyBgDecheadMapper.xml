<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.JgVFlyBgDecheadMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.JgVFlyBgDechead">
        SELECT
            *
        FROM
            JG_V_Fly_BG_DecHead
        WHERE
            ownerName = #{customerName}
          AND IsDelete = 0
          AND dDate IS NOT NULL
          AND CONVERT(DATETIME, dDate, 120) >= #{starDate}
          AND CONVERT(DATETIME, dDate, 120) &lt;= #{lastDate}
        ORDER BY
            dDate DESC
    </select>
    <select id="queryPageListCommon" resultType="org.jeecg.modules.business.entity.JgVFlyBgDechead">
        SELECT
            *
        FROM
            JG_V_Fly_BG_DecHead
        <where>
            IsDelete = 0
            <if test="ownerCode != null and ownerCode != ''">
                AND ownerCode = #{ownerCode}
            </if>
            <if test="ownerName != null and ownerName != ''">
                AND ownerName = #{ownerName}
            </if>
            <if test="isAll == null or isAll == ''">
                AND CAST(CreateDate AS DATE) >= CAST(#{startDate} AS DATE)
                AND CAST(CreateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
            </if>
        </where>
        ORDER BY
            CreateDate DESC
    </select>
    <select id="queryPageListSBCommon" resultType="org.jeecg.modules.business.entity.JgVFlyBgDechead">
        SELECT
            *
        FROM
            JG_V_Fly_BG_DecHead
        <where>
            IsDelete = 0
            <if test="agentCode != null and agentCode != ''">
                AND agentCode = #{agentCode}
            </if>
            <if test="agentName != null and agentName != ''">
                AND agentName = #{agentName}
            </if>
            <if test="isAll == null or isAll == ''">
                AND CAST(CreateDate AS DATE) >= CAST(#{startDate} AS DATE)
                AND CAST(CreateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
            </if>
        </where>
        ORDER BY
            CreateDate DESC
    </select>
</mapper>
