package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.EnterpriseInfo;
import org.jeecg.modules.system.mapper.EnterpriseInfoMapper;
import org.jeecg.modules.system.service.IEnterpriseInfoService;
import org.jeecg.modules.system.util.SsoRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date: 2022-02-18
 * @Version: V1.0
 */
@Service
@Slf4j
public class EnterpriseInfoServiceImpl extends ServiceImpl<EnterpriseInfoMapper, EnterpriseInfo> implements IEnterpriseInfoService {
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Value("${sso.api.query-balance}")
    private String queryBalanceUrl;

    @Override
    public List<EnterpriseInfo> getCollectionEnterpriseList(EnterpriseInfo enterpriseInfo) {
        List<EnterpriseInfo> list = enterpriseInfoMapper.getCollectionEnterpriseList(enterpriseInfo);
        if (list.size() == 0 && enterpriseInfo.getTenantId() != 0) {
            list.add(new EnterpriseInfo());
            String tenantName = enterpriseInfoMapper.getTenantNameById(enterpriseInfo.getTenantId());
            if (tenantName != null) {
                list.get(0).setEnterpriseFullName(tenantName);
            }
        }
        return list;
    }

    @Override
    public void updateTenantName(Long id, String name) {
        enterpriseInfoMapper.updateTenantName(id, name);
    }

    /**
     * 查询企业余额
     *
     * @param tenantId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/5/20 14:09
     */
    @Override
    public Result<?> queryBalanceByBiz(String tenantId) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = baseMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, tenantId)
                .last("LIMIT 1"));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        BigDecimal balance = null;
        if (isNotEmpty(enterpriseInfo)) {
            String creditCode = enterpriseInfo.getUnifiedSocialCreditCode();
            String completeUrl = queryBalanceUrl;
            if (isNotBlank(creditCode)) {
                // 动态构建URL
                completeUrl = queryBalanceUrl.replace("{creditCode}", creditCode);
            }
            log.info("[queryBalanceByBiz]查询余额URL：{}", completeUrl);
            String result = SsoRequestUtil.requestGet(completeUrl);
            try {
                JSONObject jsonObject = JSONObject.parseObject(result);
                balance = isNotBlank(jsonObject.getString("data")) ? new BigDecimal(jsonObject.getString("data")) : null;
            } catch (Exception e) {
                log.error("查询余额异常：{}", e.getMessage());
                return Result.error("未获取到企业[" + enterpriseInfo.getEnterpriseFullName() + "]的账户余额信息！");
            }
        }
        return Result.OK(balance);
    }
}
