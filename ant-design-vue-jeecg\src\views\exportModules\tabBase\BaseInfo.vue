<template>
  <j-form-container :disabled="formDisabled">
    <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
      <a-row>
				<a-col :span="8">
					<a-form-model-item label="委托方" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="buyer">
						<j-search-select-tag_ :disabled="buyerDisabled" @change="buyerChange"
																 v-model="model.buyer" :dict-options="dictOptions" :defaultValue="store.getters.tenantInfo.id" placeholder="请选择委托方"/>
					</a-form-model-item>
				</a-col>
        <a-col :span="8">
          <a-form-model-item label="贸易国(地区)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradingCountry">
            <j-search-select-tag
              v-model="model.tradingCountry"
              dict="erp_countries,name,code,isenabled=1"
              :async="true"
              :pageSize="50"
              @change="countryChange"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
					<a-form-model-item
						label="境内货源地"
						:labelCol="labelCol"
						:wrapperCol="wrapperCol"
						prop="domesticSourceOfGoods"
					>
						<j-search-select-tag
							v-model="model.domesticSourceOfGoods"
							dict="erp_districts,name,code,1=1"
							:async="true" :pageSize="50"
						/>
					</a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item
            label="境外客户"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="overseasPayerInfoId"
          >
            <j-search-select-tag_ v-model="model.overseasPayerInfoId" :dict="dictCodePayerName" @change="handleChangeOverseasPayerInfo"/>
          </a-form-model-item>
        </a-col>
<!--        <a-col :span="8">-->
<!--          <a-form-model-item-->
<!--            label="报关币制"-->
<!--            :labelCol="labelCol"-->
<!--            :wrapperCol="wrapperCol"-->
<!--            prop="customsDeclarationCurrency"-->
<!--          >-->
<!--            <j-search-select-tag-->
<!--              v-model="model.customsDeclarationCurrency"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
        <a-col :span="8">
          <!-- 合同总金额 -->
          <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalContractAmount">
            <template #label>
              <span>货值总金额</span>
              <a-tooltip slot="suffix" title="本订单内所有出口商品的货值总额，系统根据用户所选出口商品自动计算。">
                <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
              </a-tooltip>
            </template>
            <a-input-number v-model="model.totalContractAmount" :disabled="true" style='width: 100%;'/>
          </a-form-model-item>
        </a-col>
        <!-- </a-row>
        <a-row> -->
        <a-col :span="8">
          <a-form-model-item
            label="收汇方式"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <j-dict-select-tag
              type="list"
              v-model="model.exchangeCollectionType"
              dictCode="exchange_collection_type"
              placeholder="请选择收汇方式"
              :is9710or9810="is9710or9810"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item
            label="预计离港时间"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="estimatedPortTime"
          >
            <j-date placeholder="请选择预计离港时间" v-model="model.estimatedPortTime" style="width: 100%" />
          </a-form-model-item>
        </a-col>
				<a-col :span="8">
					<a-form-model-item
						label="实际离港时间"
						:labelCol="labelCol"
						:wrapperCol="wrapperCol"
					>
						<j-date placeholder="请选择实际离港时间" v-model="model.actualPortTime" style="width: 100%" />
					</a-form-model-item>
				</a-col>
<!--        <a-col :span="8">-->
<!--          <a-form-model-item label="提单收货人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="receiver">-->
<!--            <j-remarks-component-->
<!--              v-model="model.receiver"-->
<!--              placeholder="请输入提单收货人"-->
<!--              :maxLength="64"-->
<!--              :readOnly="formDisabled"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
				<a-col :span="8">
					<a-form-model-item label="发票号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="invoiceNo">
						<a-input v-model="model.invoiceNo" placeholder="请输入发票号" :maxLength="32"></a-input>
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<a-form-model-item label="外销合同号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="exportContractNo">
						<a-input v-model="model.exportContractNo" placeholder="请输入外销合同号" :maxLength="32" ></a-input>
					</a-form-model-item>
				</a-col>
<!--        <a-col :span="8">-->
<!--          <a-form-model-item label="出境关别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="exitClearance">-->
<!--            <j-search-select-tag-->
<!--              v-model="model.exitClearance"-->
<!--              dict="erp_customs_ports,name,customs_port_code, 1=1"-->
<!--              :async="true"-->
<!--              :pageSize="50"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8">-->
<!--          <a-form-model-item label="征免性质" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="taxTypeCode">-->
<!--            <j-dict-select-tag-->
<!--              v-model="model.taxTypeCode"-->
<!--              type="list"-->
<!--              dictCode="ZMXZ"-->
<!--              placeholder="请选择征免性质"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8">-->
<!--          <a-form-model-item label="许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licenceNumber">-->
<!--            <a-input v-model="model.licenceNumber" placeholder="请输入许可证号" :maxLength="32"></a-input>-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->


<!--        <a-col :span="8" v-show="freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shipFeeCode">-->
<!--            <j-dict-select-tag-->
<!--              v-model="model.shipFeeCode"-->
<!--              type="list"-->
<!--              dictCode="freight_amount_type"-->
<!--              placeholder="请选择运费种类"-->
<!--              @change="shipFeeCodeChange"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-dict-select-tag-->
<!--              v-model="model.shipFeeCode"-->
<!--              type="list"-->
<!--              dictCode="freight_amount_type"-->
<!--              placeholder="请选择运费种类"-->
<!--              @change="shipFeeCodeChange"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="freightAmount">-->
<!--            <a-input-number-->
<!--              v-model="model.freightAmount"-->
<!--              placeholder="请输入运费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--            <a-input-number-->
<!--              v-model="model.freightAmount"-->
<!--              placeholder="请输入运费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shipCurrencyCode">-->
<!--            <j-search-select-tag-->
<!--              :disabled="shipCurrencyCodeDisabled"-->
<!--              v-model="model.shipCurrencyCode"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!freightAmountShowFlag">-->
<!--          <a-form-model-item label="运费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-search-select-tag-->
<!--              :disabled="shipCurrencyCodeDisabled"-->
<!--              v-model="model.shipCurrencyCode"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insuranceCode">-->
<!--            <j-dict-select-tag-->
<!--              @change="insuranceCodeChange"-->
<!--              v-model="model.insuranceCode"-->
<!--              type="list"-->
<!--              dictCode="premium_amount_type"-->
<!--              placeholder="请选择保费种类"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-dict-select-tag-->
<!--              @change="insuranceCodeChange"-->
<!--              v-model="model.insuranceCode"-->
<!--              type="list"-->
<!--              dictCode="premium_amount_type"-->
<!--              placeholder="请选择保费种类"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="premiumAmount">-->
<!--            <a-input-number-->
<!--              v-model="model.premiumAmount"-->
<!--              placeholder="请输入保费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--            <a-input-number-->
<!--              v-model="model.premiumAmount"-->
<!--              placeholder="请输入保费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insuranceCurr">-->
<!--            <j-search-select-tag-->
<!--              :disabled="insuranceCurrDisabled"-->
<!--              v-model="model.insuranceCurr"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!premiumAmountShowFlag">-->
<!--          <a-form-model-item label="保费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-search-select-tag-->
<!--              :disabled="insuranceCurrDisabled"-->
<!--              v-model="model.insuranceCurr"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="extrasCode">-->
<!--            <j-dict-select-tag-->
<!--              @change="extrasCodeChange"-->
<!--              v-model="model.extrasCode"-->
<!--              type="list"-->
<!--              dictCode="freight_amount_type"-->
<!--              placeholder="请选择杂费种类"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费种类" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-dict-select-tag-->
<!--              @change="extrasCodeChange"-->
<!--              v-model="model.extrasCode"-->
<!--              type="list"-->
<!--              dictCode="freight_amount_type"-->
<!--              placeholder="请选择杂费种类"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="miscellaneousAmount">-->
<!--            <a-input-number-->
<!--              v-model="model.miscellaneousAmount"-->
<!--              placeholder="请输入杂费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <a-input-number-->
<!--              v-model="model.miscellaneousAmount"-->
<!--              placeholder="请输入杂费"-->
<!--              style="width: 100%"-->
<!--              :max="9999999999"-->
<!--              :min="0"-->
<!--              :step="1"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="otherCurr">-->
<!--            <j-search-select-tag-->
<!--              :disabled="otherCurrDisabled"-->
<!--              v-model="model.otherCurr"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
<!--        <a-col :span="8" v-show="!miscellaneousAmountShowFlag">-->
<!--          <a-form-model-item label="杂费币制" :labelCol="labelCol" :wrapperCol="wrapperCol" >-->
<!--            <j-search-select-tag-->
<!--              :disabled="otherCurrDisabled"-->
<!--              v-model="model.otherCurr"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
				<a-col :span="8">
					<a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remarks">
						<j-remarks-component
							v-model="model.remarks"
							placeholder="请输入备注"
							:maxLength="256"
							:readOnly="formDisabled"
						/>
					</a-form-model-item>
				</a-col>
      </a-row>
    </a-form-model>
  </j-form-container>
</template>

<script>
import { duplicateCheckTenant } from '@/api/api'
import Vue from 'vue'
import { TENANT_ID } from '@/store/mutation-types'
import { dateRandomNumber, number_format } from '@/utils/util'
import store from '@/store'
import { getAction } from '@/api/manage'

export default {
  name: 'BaseInfo',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    labelCol: Object,
    wrapperCol: Object,
    menuName: {
      type: String,
      default: 'unknown',
      required: false,
    },
  },
  data() {
    return {
			buyerDisabled:false,
      shipCurrencyCodeDisabled: false,
      otherCurrDisabled: false,
      insuranceCurrDisabled: false,
      miscellaneousAmountShowFlag: false,
      premiumAmountShowFlag: false,
      freightAmountShowFlag: false,
			dictOptions: [],
			model: {
				buyer: Vue.ls.get(TENANT_ID),
				totalContractAmount: 0
			},
      currencyVal: '',
      modelChangeTimes: 0, //model更新次数
      validatorRules: {
				buyer: [{ required: true, message: '请选择委托方!' }],
        tradingCountry: [{ required: true, message: '请输入贸易国(地区)!' }],
        finalContry: [{ required: true, message: '请输入最终目的国(地区)!' }],
        overseasPayerInfoId: [{ required: true, message: '请选择境外客户!' }],
				domesticSourceOfGoods: [{ required: true, message: '请选择境内货源地!' }],
        customsDeclarationCurrency: [
          { required: true, message: '请输入报关币制!' },
          {
            validator: (rule, value, callback) => {
              try {
                if (this.model.hasMatchingFlag && value != this.currencyVal) {
                  throw new Error('有外汇匹配不能修改币种')
                } else {
                  callback()
                }
              } catch (err) {
                callback(err)
                return
              }
            },
          },
        ],
        totalContractAmount: [
          { required: true, message: '请输入总金额!' },
          // { pattern: /^([1-9]\d{0,9}|[0]?)([.]?|(\.\d{1,2})?)$/, message: '请输入正确的金额!' },
        ],
        exchangeCollectionType: [{ required: true, message: '请输入收汇方式!' }],
				// estimatedPortTime: [{ required: true, message: '请输入预计出货日期!' }],
        receiver: [],
        invoiceNo: [
          { pattern: /^[\u0000-\u00ff]+$/, message: '请输入正确的发票号!' },
          // {
          //   validator: (rule, value, callback) =>
          //     this.validateDuplicateValueTenant('order_info', 'invoice_no', value, this.model.id, callback),
          // },
        ],
        exportContractNo: [
          { pattern: /^[\u0000-\u00ff]+$/, message: '请输入正确的外销合同号!' },
          // {
          //   validator: (rule, value, callback) =>
          //     this.validateDuplicateValueTenant('order_info', 'export_contract_no', value, this.model.id, callback),
          // },
        ],
      },
      dictCodePayerName: '',
      is9710or9810: false,
			dictCodeJiaCustomer: '',
			overseasPayerInfoAll: []
    }
  },
  created() {
    // 查询当前租户id
    let tenantId = Vue.ls.get(TENANT_ID)
    if (!tenantId) {
      tenantId = 0
    }
    this.dictCodePayerName =
      'overseas_payer_info,overseas_payer_name,id,tenant_id=' + tenantId + 'and is_effective_flag=1 and del_flag=0'
		this.dictCodeJiaCustomer =
			'commissioner,COMMISSIONER_FULL_NAME,id,tenant_id=' + tenantId + 'and del_flag=0'
		this.loadDictOptions()
  },
  computed: {
		store() {
			return store
		},
    formDisabled() {
      return this.disabled
    },
  },
  updated() {
    this.modelChangeTimes = this.modelChangeTimes + 1
  },
  methods: {
    shipFeeCodeChange(val){
      //选择率时币制禁止，去除必填
      if(1==val){
        this.shipCurrencyCodeDisabled=true
        this.validatorRules.shipCurrencyCode=[]

      }else {
        console.log(this.model)
        //cif的再加上必填币制
        if(this.model.tradingType==1||this.model.tradingType==2){
          this.validatorRules.shipCurrencyCode.push(
            { required: true, message: '请选择运费币制!'},
          )
        }
        this.shipCurrencyCodeDisabled=false
      }
    },
    insuranceCodeChange(val){
      //选择率时币制禁止，去除必填
      if(1==val){
        this.insuranceCurrDisabled=true
        this.validatorRules.insuranceCurr=[]
      }else {
        //cif的再加上必填币制
        if(this.model.tradingType==1){
          this.validatorRules.insuranceCurr.push(
            { required: true, message: '请选择保费币制!' },
          )
        }
        this.insuranceCurrDisabled=false
      }
    },
    extrasCodeChange(val){
      //选择率时币制禁止，去除必填
      if(1==val){
        this.otherCurrDisabled=true
        this.validatorRules.otherCurr=[]
      }else {
        if(this.model.tradingType==7){
          this.validatorRules.otherCurr.push(
            { required: true, message: '请选择杂费币制!' },
          )
        }
        this.otherCurrDisabled=false
      }
    },
		// 查询委托方数据
		async loadDictOptions() {
			this.dictOptions = []
			await getAction(`/sys/dict/getDictItems/${this.dictCodeJiaCustomer}`,{}).then(res=>{
				if (res.success) {
					this.dictOptions = res.result.map(item => ({value: item.value, text: item.text}))
				} else {
					console.error('getDictItems error: : ', res)
					this.dictOptions = []
				}
				if (store.getters.tenantInfo) {
					this.dictOptions.push({value: String(store.getters.tenantInfo.id), text: store.getters.tenantInfo.name})
					//委托方动态处理，如果当前登陆企业为报关行，则下拉选择，如果不是，则只读
					//
					// if(store.getters.tenantInfo.type=='1801046544255541249'){
					//20241223去除该逻辑
					// }else {
					// 	this.buyerDisabled=true
					// 	this.model.buyer=String(store.getters.tenantInfo.id)
					// 	this.model.buyerName=String(store.getters.tenantInfo.name)
					// 	this.model.receiver=this.model.buyerName
					// }
					this.model.buyer=this.model.buyer?this.model.buyer:String(store.getters.tenantInfo.id)
					this.model.buyerName=this.model.buyerName?this.model.buyerName:String(store.getters.tenantInfo.name)
					this.model.receiver=this.model.receiver?this.model.receiver:this.model.buyerName

				}
			})
			this.overseasPayerInfoAll = []
			await getAction(`/sys/dict/getDictItems/${this.dictCodePayerName}`,{}).then(res=>{
				if (res.success) {
					this.overseasPayerInfoAll = res.result.map(item => ({value: item.value, text: item.text}))
				} else {
					console.error('getDictItems error: : ', res)
					this.overseasPayerInfoAll = []
				}
			})
		},
		buyerChange(e){
			const o = this.dictOptions.find(i=>i.value==e)
			if(o){
				this.model.receiver=o.text
			}

		},
    initModel(value) {
      let model = value
      // if (model.invoiceNo == undefined) {
      //   this.$set(model, 'invoiceNo', 'IO' + dateRandomNumber())
      // }
      // if (model.exportContractNo == undefined) {
      //   this.$set(model, 'exportContractNo', 'CN' + dateRandomNumber())
      // }
      this.currencyVal = model.customsDeclarationCurrency
      this.model = model
      this.model.totalContractAmount = this.model.totalContractAmount?this.model.totalContractAmount.toFixed(2):0
      this.modelChangeTimes--
      this.initRules()
      this.is9710or9810 = this.model.supervisionMode == '9710' || this.model.supervisionMode == '9810'
    },
    initRules() {
      if (this.menuName === 'shipment' || this.menuName === 'customsClearance' || this.menuName === 'orderCompletion') {
        this.validatorRules.receiver.push({ required: true, message: '请输入提单收货人!' })
      }
    },
    async validateForm() {
      try {
        return await this.$refs.form.validate()
      } catch (error) {
        return false
      }
    },
    svModeChange(value) {
      if (value == '9710' || value == '9810') {
        this.is9710or9810 = true
        if (this.model.exchangeCollectionType != undefined && this.model.exchangeCollectionType != '10' && this.model.exchangeCollectionType != '20') {
          this.model.exchangeCollectionType = undefined
        }
      } else {
        this.is9710or9810 = false
      }
    },
    modelChange() {
      let flag = false
      if (this.modelChangeTimes > 0) {
        this.modelChangeTimes = 0
        flag = true
      }
      return flag
    },
    getModel() {
      return this.model
    },
    changeTotalAmount(val) {
      this.model.totalContractAmount = val
      this.$forceUpdate()
    },
		handleChangeOverseasPayerInfo(data) {
			// if (this.overseasPayerInfoAll.length > 0) {
			// 	let item = this.overseasPayerInfoAll.find(item => item.value == data)
			// 	this.model.receiver = item ? item.text : ''
			// }
		},
    countryChange() {
      if (this.model.finalContry == null) {
        this.model.finalContry = this.model.tradingCountry
        this.$refs.finalContry.validate()
      }
      this.$emit('countryChange', this.model.tradingCountry)
    }
  },
}
</script>