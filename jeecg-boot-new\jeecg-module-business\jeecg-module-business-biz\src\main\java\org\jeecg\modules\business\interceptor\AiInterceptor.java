package org.jeecg.modules.business.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static org.jeecg.modules.business.util.ApiUtil.md5;

/**
 * AI用拦截器
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/6/9 15:44
 */
@Slf4j
@Component
public class AiInterceptor implements HandlerInterceptor {

    public static String getTime() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    /**
     * 验证时间戳是否有效（5分钟内有效）
     *
     * @param timestampStr 格式为 yyyy-MM-dd HH:mm:ss 的时间字符串
     * @return 是否有效
     */
    public static boolean isTimestampValid(String timestampStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date timestamp = sdf.parse(timestampStr);
            Date currentTime = new Date();
            // 计算时间差（毫秒）
            long difference = Math.abs(currentTime.getTime() - timestamp.getTime());
            // 设置时间戳有效期为5分钟（300000毫秒）
            return difference <= 300000;
        } catch (Exception e) {
            // 时间格式不正确
            return false;
        }
    }

    /**
     * 验证签名是否有效
     *
     * @param sign 请求中的签名
     * @param timestamp 请求中的时间戳（格式为 yyyy-MM-dd HH:mm:ss）
     * @return 是否有效
     */
    public static boolean isSignValid(String sign, String timestamp) {
        if (sign == null || timestamp == null) {
            return false;
        }
        try {
            // 使用相同的密钥（与生成签名时使用的相同）
            String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
            // 使用相同的算法生成签名
            String expectedSign = md5(secretKey + timestamp).toUpperCase();
            // 比较生成的签名与请求中的签名是否一致
            return expectedSign.equals(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Interception point before the execution of a handler. Called after
     * HandlerMapping determined an appropriate handler object, but before
     * HandlerAdapter invokes the handler.
     * <p>DispatcherServlet processes a handler in an execution chain, consisting
     * of any number of interceptors, with the handler itself at the end.
     * With this method, each interceptor can decide to abort the execution chain,
     * typically sending an HTTP error or writing a custom response.
     * <p><strong>Note:</strong> special considerations apply for asynchronous
     * request processing. For more details see
     * {@link AsyncHandlerInterceptor}.
     * <p>The default implementation returns {@code true}.
     *
     * @param request  current HTTP request
     * @param response current HTTP response
     * @param handler  chosen handler to execute, for type and/or instance evaluation
     * @return {@code true} if the execution chain should proceed with the
     * next interceptor or the handler itself. Else, DispatcherServlet assumes
     * that this interceptor has already dealt with the response itself.
     * @throws Exception in case of errors
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info(">>>>>>> 进入AI接口拦截器...");
        try {
            // 从请求头中获取两个参数
            String sign = request.getHeader("sign");
            String timestamp = request.getHeader("timestamp");

            log.info("AI拦截器获取参数 - sign: {}, timestamp: {}", sign, timestamp);

            if (isBlank(sign) || isBlank(timestamp)) {
                log.error("sign 或 timestamp 字段缺失");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("sign 或 timestamp 字段缺失");
                return false;
            }

            // 验证时间戳
            if (!isTimestampValid(timestamp)) {
                log.error("时间戳已过期");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("时间戳已过期");
                return false;
            }

            // 验证签名
            if (!isSignValid(sign, timestamp)) {
                log.error("签名验证失败");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("签名验证失败");
                return false;
            }

            log.info("验证通过，请求继续...");
            return true;
        } catch (Exception e) {
            log.error("AI接口拦截器异常", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("AI接口拦截器异常: " + e.getMessage());
            return false;
        } finally {
            log.info(">>>>>>> AI接口拦截器结束...");
        }
    }
}
