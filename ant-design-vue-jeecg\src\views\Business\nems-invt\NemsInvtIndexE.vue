<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="经营单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input type="NO" placeholder="请输入经营单位名称" v-model="queryParam.bizopEtpsNm"></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input type="NO" placeholder="请输入统一编号" v-model="queryParam.seqNo"></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="清单编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input type="NO" placeholder="请输入清单编号" v-model="queryParam.bondInvtNo"></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<span style="float: left" class="table-page-search-submitButtons">
							<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
							<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							<a @click="handleToggleSearch" style="margin-left: 8px">
							{{ toggleSearchStatus ? '收起' : '展开' }}
							<a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
							</a>
						</span>
					</a-col>
					<template v-if="toggleSearchStatus">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="经营单位编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入经营单位编码" v-model="queryParam.bizopEtpsno"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="企业内部编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input type="NO" placeholder="请输入企业内部编号" v-model="queryParam.etpsInnerInvtNo"></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="手账册编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input type="NO" placeholder="请输入手账册编号" v-model="queryParam.putrecNo"></j-input>
							</a-form-item>
						</a-col>
						<!--            <a-col :xl="6" :sm="24" :xxl="6" :md="12">-->
						<!--              <a-form-item label="进出口" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
						<!--                <a-select placeholder="请选择进出口" v-model="queryParam.ieFlag">-->
						<!--                  <a-select-option value="I">进口</a-select-option>-->
						<!--                  <a-select-option value="E">出口</a-select-option>-->
						<!--                </a-select>-->
						<!--              </a-form-item>-->
						<!--            </a-col>-->
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="创建日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectCloseDate" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="onQueryTimeChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="是否生成出库单" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.hasSC">
									<a-select-option value="0">否</a-select-option>
									<a-select-option value="1">是</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="关联出库单编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入关联出库单编号" v-model="queryParam.storageNos"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="是否推送" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.isSend">
									<a-select-option value="0">否</a-select-option>
									<a-select-option value="1">是</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入创建人" v-model="queryParam.createPerson"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="数据状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.status">
									<a-select-option value="0">暂存</a-select-option>
									<a-select-option value="1">申报成功</a-select-option>
									<a-select-option value="4">成功发送海关</a-select-option>
									<a-select-option value="5">海关接收成功</a-select-option>
									<a-select-option value="6">海关接收失败</a-select-option>
									<a-select-option value="B">海关终审通过</a-select-option>
									<a-select-option value="C">海关退单</a-select-option>
									<a-select-option value="E">删除</a-select-option>
									<a-select-option value="N">待导入其他报文</a-select-option>
									<a-select-option value="P">预审批通过</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>

						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="清单类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.invtType">
									<a-select-option value="0">普通清单</a-select-option>
									<a-select-option value="1">集报清单</a-select-option>
									<a-select-option value="3">先入区后报关</a-select-option>
									<a-select-option value="4">简单加工</a-select-option>
									<a-select-option value="5">保税展示交易</a-select-option>
									<a-select-option value="6">区内流转</a-select-option>
									<a-select-option value="8">保税电商</a-select-option>
									<a-select-option value="9">一纳成品内销</a-select-option>
									<a-select-option value="7">区港联动</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="核扣标志" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.vrfdedMarkcd">
									<a-select-option value="0">未核扣</a-select-option>
									<a-select-option value="1">预核扣</a-select-option>
									<a-select-option value="2">已核扣</a-select-option>
									<a-select-option value="3">已核销</a-select-option>
									<a-select-option value="4">反核扣</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="清单申报日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectDclDate" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="selectDclDateChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="核注清单申报单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入核注清单申报单位" v-model="queryParam.dclEtpsNm"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="监管方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-dict-select-tag
									v-model="queryParam.supvModecd"
									type="list"
									dictCode="JGFS"
									placeholder="请选择监管方式"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="料件成品标志" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.mtpckEndprdMarkcd">
									<a-select-option value="I">料件</a-select-option>
									<a-select-option value="E">成品</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="备案序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入备案序号" v-model="queryParam.putrecSeqno"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入料号" v-model="queryParam.gdsMtno"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="初复审状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.initialReviewStatus">
									<a-select-option value="0">未审核</a-select-option>
									<a-select-option value="1">已初审/未复审</a-select-option>
									<a-select-option value="2">已复审</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>





					</template>
				</a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
<!--			新表格-->
			<query-vxe-grid
				class="xGrid-style"
				ref="xGrid"
				size="mini"
				height="500"
				:loading="loading"
				:gridOptions="gridOptions"
				:dataSource="dataSource"
				@cell-click="cellClick"
				@checkbox-change="checkboxChangeEvent"
				@checkbox-all="checkboxChangeEvent"
				@cell-dblclick="cellDblclick"
				@page-change="handlePageChange"
			>
				<template v-slot:toolbar_buttons>
					<!-- 操作按钮区域 -->
					<div class="table-operator">
						<a-dropdown class="xz" size="small">
							<a-menu slot="overlay" @click="handleAdd" size="small">
								<!--          <a-menu-item key="I" size="small">新增进口核注单</a-menu-item>-->
								<a-menu-item key="E" size="small">新增出口核注单</a-menu-item>
							</a-menu>
							<a-button v-has="'invt:edit'" size="small" icon="plus" type="primary">
								新增
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<a-button v-has="'invt:edit'" type="primary" icon="upload" size="small" @click="handleImportExcel">导入</a-button>
<!-- 添加列表导出下拉按钮 -->
						<a-dropdown class="list-export">
							<a-menu slot="overlay">
								<a-menu-item @click="handleSelectExport" key="selectExport">选择导出</a-menu-item>
								<a-menu-item @click="handleQueryExport" key="queryExport">查询导出</a-menu-item>
							</a-menu>
							<a-button v-has="'invt:export'" size="small" icon="export" type="primary">
								列表导出
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<!-- 添加详情导出按钮 -->
						<a-button v-has="'invt:export'" type="primary" icon="export" size="small" @click="handleDetailExport">详情导出</a-button>
						<a-button v-has="'invt:edit'" type="primary" icon="copy" size="small" @click="batchCopy">复制</a-button>
						<!--      <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>-->
						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item @click="handleFirstTrial" key="1">
									<a-icon type="key"/>
									初审
								</a-menu-item>
								<a-menu-item @click="handleReview" key="2">
									<a-icon type="key"/>
									复审
								</a-menu-item>
							</a-menu>
							<a-button v-has="'invt:edit'" size="small" icon="edit" type="primary">
								初复审
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<a-button v-has="'invt:ckdschzd'" size="small" icon="export" type="primary" @click="handleCreateByOutStorage">
							出库单生成核注单
						</a-button>
						<a-button v-has="'invt:hzdscckd'" size="small" icon="import" type="primary" @click="handleCreateOutStorage">
							核注单生成出库单
						</a-button>
						<!--			<a-button size="small" icon="import" type="primary" @click="handleCreateInStorage">-->
						<!--				核注单生成入库单-->
						<!--			</a-button>-->
						<a-button v-has="'invt:push'" type="primary" icon="select" size="small" @click="handlePush" :loading='pushLoading'>推送</a-button>
						<a-button v-has="'invt:edit'" type="primary" icon="issues-close" size="small" @click="handleManualDeduction">手动核扣</a-button>
						<a-button type="primary" icon="select" size="small" v-has="'invt:sync'" @click="handleSync" :loading='syncLoading'>手动同步</a-button>
						<!--			<a-dropdown class="xz" size="small">-->
						<!--				<a-menu slot="overlay" @click="handleChange" size="small">-->
						<!--					<a-menu-item key="1" size="small">二线进口清单生成二线入区报关单</a-menu-item>-->
						<!--					<a-menu-item key="2" size="small">二线进口清单生成二线入区核放单草稿</a-menu-item>-->
						<!--					<a-menu-item key="3" size="small">二线入区报关单生成一线出区报关单</a-menu-item>-->
						<!--					<a-menu-item key="4" size="small">一线进口清单生成一线进境备案清单</a-menu-item>-->
						<!--					<a-menu-item key="5" size="small">一线进口清单生成一线入区核放单草稿</a-menu-item>-->
						<!--					<a-menu-item key="6" size="small">一线进境备案清单生成二线出区报关单草单</a-menu-item>-->
						<!--					<a-menu-item key="7" size="small">二线入区核注清单申报生成一线出区核注清单</a-menu-item>-->
						<!--					<a-menu-item key="8" size="small">一线入区核注清单申报生成二线出区核注清单</a-menu-item>-->
						<!--				</a-menu>-->
						<!--				<a-button size="small" icon="edit" type="primary">-->
						<!--					单证转换-->
						<!--					<a-icon type="down"/>-->
						<!--				</a-button>-->
						<!--			</a-dropdown>-->
						<a-button
							v-has="'invt:edit'"
							@click="batchDel"
							v-if="selectedRowKeys.length > 0"
							ghost
							type="primary"
							icon="delete">批量删除
						</a-button>
					</div>
				</template>
				<template #action="{ row }">
					<a-dropdown>
						<a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
						<a-menu slot="overlay">
							<a-menu-item>
								<a @click="handleEdit(row)">编辑</a>
							</a-menu-item>
							<a-menu-item v-has="'invt:edit'">
								<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(row.id,row)">
									<a>删除</a>
								</a-popconfirm>
							</a-menu-item>
							<a-menu-item>
								<a @click="handleEditHis(row)">回执</a>
							</a-menu-item>
						</a-menu>
					</a-dropdown>

				</template>

			</query-vxe-grid>



<!--      <a-table ref="table" size="small" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"-->
<!--               :dataSource="dataSource" :pagination="ipagination" :loading="loading"-->
<!--               class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"-->
<!--               @change="handleTableChange" :customRow="rowEvent">-->
<!--				<span slot="firstOpinion" slot-scope="text, record" :title="record.firstOpinion">-->
<!--          {{subStrForColumns(record.firstOpinion, 15)}}-->
<!--        </span>-->
<!--				<span slot="reviewOpinion" slot-scope="text, record" :title="record.reviewOpinion">-->
<!--          {{subStrForColumns(record.reviewOpinion, 15)}}-->
<!--        </span>-->
<!--				<span slot="storageNos" slot-scope="text, record" :title="record.storageNos">-->
<!--          {{subStrForColumns(record.storageNos, 15)}}-->
<!--        </span>-->
<!--        <span slot="action" slot-scope="text, record">-->
<!--          <a-dropdown>-->
<!--            <a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>-->
<!--            <a-menu slot="overlay">-->
<!--              <a-menu-item>-->
<!--                <a @click="handleEdit(record)">编辑</a>-->
<!--              </a-menu-item>-->
<!--              <a-menu-item v-has="'invt:edit'">-->
<!--                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id,record)">-->
<!--                  <a>删除</a>-->
<!--                </a-popconfirm>-->
<!--              </a-menu-item>-->
<!--							<a-menu-item>-->
<!--								<a @click="handleEditHis(record)">回执</a>-->
<!--							</a-menu-item>-->
<!--            </a-menu>-->
<!--          </a-dropdown>-->
<!--        </span>-->
<!--      </a-table>-->
    </div>

		<!--初复审modal-->
		<j-modal
			:title="title"
			:width="600"
			:visible="visible"
			@ok="handleOk"
			@cancel="handleCancel"
			cancelText="关闭"
		>
			<template slot="footer">
				<a-button type="default" @click="handleCancel">取消</a-button>
				<a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
			</template>
			<a-spin :spinning="confirmLoading">
				<a-form-model ref="form" :model="model" :rules="validatorRules">
					<a-row>
						<a-card size="small" :bordered="false" title="">
							<a-col v-if="isFirst">
								<a-form-model-item
									label="初审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="firstOpinion"
								>
									<j-remarks-component
										v-model="model.firstOpinion"
										placeholder="请输入初审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
							<a-col v-if="!isFirst">
								<a-form-model-item
									label="复审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="reviewOpinion"
								>
									<j-remarks-component
										v-model="model.reviewOpinion"
										placeholder="请输入复审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
						</a-card>
					</a-row>
				</a-form-model>
			</a-spin>
		</j-modal>

		<!-- 手动核扣模态框 -->
		<a-modal
			title="手动核扣"
			:visible="vrfdedMarkcdModalVisible"
			@ok="handleManualDeductionOk"
			@cancel="handleManualDeductionCancel"
			:confirmLoading='modalLoading'
			cancelText="关闭"
		>
			<a-form-model ref="formModal" :model="modelModal" :rules="validatorRulesModal">
				<a-form-model-item label="核扣日期" :labelCol="labelCol1" :wrapperCol="wrapperCol1" prop="warehousingDate">
					<j-date is-node placeholder="请选择核扣日期" v-model="modelModal.warehousingDate" style="width: 100%"/>
				</a-form-model-item>
			</a-form-model>
		</a-modal>

		<!-- 出库单生成核注单 -->
		<nems-invt-from-bonded-warehouse-out-store-modal ref='fromBondedWarehouseModal' />

		<out-store-modal ref="outStoreModalRef" @close="searchReset"/>

		<!-- 核注单生成入库单 -->
		<in-store-modal ref='inStoreModalRef' @close="searchReset"/>

		<upload-modal ref="importModal" title="核注单导入" im-sign="E"
									:importUrl="url.importExcelUrl" @closeUploadModal="closeUploadModal" />
		<edi-status-list ref="ediStatusList"></edi-status-list>
		<!-- 导出核注单表头选择列框 -->
		 <exportCustomFile
		 	ref="exportCustomFileRef"
            :visible.sync="exportCustomFileVisible"
            :defColumns="columnsVO"
            title="自定义导出"
            :type="nems"
            :ids="selectedRowKeys"
            :queryParam='queryParam'
            :url="url.customUrl"
            name="核注清单列表"
        ></exportCustomFile>
	</a-card>
</template>
<script>
    import { JeecgListMixin } from '@/mixins/JeecgListMixin'
    import { mixinDevice } from '@/utils/mixin'
    import { subStrForColumns } from '@/utils/util'
    import { filterDictTextByCache } from '@/components/dict/JDictSelectUtil'
		import { _postAction, deleteAction, getAction, httpAction } from '@/api/manage'
		import NemsInvtFromBondedWarehouseOutStoreModal
			from '@/views/Business/nems-invt/modules/NemsInvtFromBondedWarehouseOutStoreModal.vue'
		import InStoreModal from '@/views/Business/nems-invt/modules/InStoreModal.vue'
		import OutStoreModal from '@/views/Business/nems-invt/modules/OutStoreModal.vue'
		import UploadModal from "@/views/contractManage/component/UploadModal.vue";
		import { ajaxGetDictItems } from '@/api/api'
		import EdiStatusList from '@/views/Business/component/modal/EdiStatusList.vue'
		import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid.vue'
		import { MyXGridMixin } from '@/mixins/MyXGridMixin'
		import exportCustomFile from '@views/Business/component/exportCustomFile'
    export default {
      name: 'NemsInvtIndex',
      mixins: [MyXGridMixin, mixinDevice],
			components: {
				QueryVxeGrid,
				EdiStatusList,
				OutStoreModal,
				UploadModal,
				NemsInvtFromBondedWarehouseOutStoreModal,
				InStoreModal,
				exportCustomFile
			},
      data() {
        return {
			 customUrl: '/dcl/invt/exportInvtHeadByFieldsBatch',
			columnsVO: [],
					dataSource:[],
					/* 分页参数 */
					ipagination: {
						total: 0,
						currentPage: 1,
						pageSize: 15,
						pageSizes: [15, 30, 50, 100, 200],
						perfect: true
					},
					selectDclDate:'',
					queryParam: {
						ieFlag: 'E'
					},
					pushLoading: false,
					syncLoading: false,
					vrfdedMarkcdModalVisible: false,
					model: { firstOpinion: '', reviewOpinion: '' },
					modelModal: { warehousingDate: '' },
					confirmLoading: false,
					modalLoading: false,
					visible: false,
					isFirst: false,
					title: '初审意见',
					validatorRules: {
						firstOpinion: [{ required: true, message: '请输入审核意见!' }],
						reviewOpinion: [{ required: true, message: '请输入复审意见!' }]
					},
					validatorRulesModal: {
						warehousingDate: [{ required: true, message: '请选择核扣日期!' }]
					},
          selectCloseDate: [],
          labelCol: {
            xs: { span: 9 },
            // sm: { span: 9 },
            xl:{ span: 12 }
          },
          wrapperCol: {
            xs: { span: 16 },
            // sm: { span: 14 },
          },
          labelCol1: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol1: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
          columns: [
						{
							title: '经营单位名称',
							align: 'center',
							dataIndex: 'bizopEtpsNm',
						},
						{
							title: '清单编号',
							align: 'center',
							dataIndex: 'bondInvtNo',
						},
						{
							title: '清单类型',
							align: 'center',
							dataIndex: 'invtType',
							customRender: (text) => {
								let re = "";
								if (text == 0) {
									re = "普通清单"
								} else if (text == 3) {
									re = "先入区后报关";
								} else if (text == 4) {
									re = "简单加工";
								} else if (text == 5) {
									re = "保税展示交易";
								} else if (text == 6) {
									re = "区内流转";
								} else if (text == 8) {
									re = "保税电商";
								} else if (text == 9) {
									re = "一纳成品内销";
								} else if (text == 1) {
									re = "集报清单";
								}else if (text == 7) {
									re = "区港联动";
								}

								return re;
							}
						},
						{
							title: '企业内部编号',
							align: 'center',
							dataIndex: 'etpsInnerInvtNo',
						},
						{
							title: '预录入统一编号',
							align: 'center',
							dataIndex: 'seqNo'
						},
						{
							title: '手账册编号',
							align: 'center',
							dataIndex: 'putrecNo',
						},
						{
							title: '进出口',
							align: 'center',
							dataIndex: 'impexpMarkcd',
							customRender:function (text) {
								if(text=='I'){
									return "进口"
								}else if(text=='E'){
									return "出口"
								}else{
									return text
								}
							}
						},
						{
							title: '核扣标志',
							align: 'center',
							dataIndex: 'vrfdedMarkcd',
							customRender: function (text) {
								if (text == '0') {
									return "未核扣"
								} else if (text == '1'){
									return "预核扣"
								} else if (text == '2'){
									return "已核扣"
								} else if (text == '3'){
									return "已核销"
								} else if (text == '4'){
									return "反核扣"
								} else {
									return text
								}
							}
						},
						{
							title: '报关标志',
							align: 'center',
							dataIndex: 'dclcusFlag',
							customRender:function (text) {
								if(text=='1'){
									return "报关"
								}else if(text=='2'){
									return "非报关"
								}else{
									return text
								}
							}
						},
						{
							title: '报关类型',
							align: 'center',
							dataIndex: 'dclcusTypecd',
							customRender:function (text) {
								if(text=='1'){
									return "关联报关"
								}else if(text=='2'){
									return "对应报关"
								}else{
									return text
								}
							}
						},
						{
							title: '申报类型',
							align: 'center',
							dataIndex: 'dclcusTypecd',
							customRender:function (text) {
								if(text=='1'){
									return "备案申请"
								}else if(text=='2'){
									return "变更申请"
								}else if(text=='3'){
									return "删除申请"
								}else{
									return text
								}
							}
						},

						{
							title: '进出口口岸',
							align: 'center',
							dataIndex: 'impexpPortcd',
							customRender: this.impexpPortcdFormat
						},
						{
							title: '申报日期',
							align: 'center',
							dataIndex: 'invtDclTime'
						},
						{
							title: '数据状态',
							align: 'center',
							dataIndex: 'status',
							customRender:function (text) {
								if(text=='0'){
									return "暂存"
								}else if(text=='1'){
									return "申报成功"
								}else if(text=='4'){
									return "成功发送海关"
								}else if(text=='5'){
									return "海关接收成功"
								}else if(text=='6'){
									return "海关接收失败"
								}else if(text=='B'){
									return "海关终审通过"
								}else if(text=='C'){
									return "海关退单"
								}else if(text=='E'){
									return "删除"
								}else if(text=='N'){
									return "待导入其他报文"
								}else if(text=='P'){
									return "预审批通过"
								}else{
									return text
								}
							}
						},

						{
							title: '加工单位名称',
							align: 'center',
							dataIndex: 'rcvgdEtpsNm',
						},

						{
							title: '监管方式',
							align: 'center',
							dataIndex: 'supvModecd',
							customRender: this.supvModecdFormat
						},


						{
							title: '已核扣日期',
							align: 'center',
							dataIndex: 'warehousingDate'
						},

						{
							title: '创建人',
							align: 'center',
							dataIndex: 'createPerson'
						},
						{
							title: '创建日期',
							align: 'center',
							dataIndex: 'createDate'
						},
						{
							title: '是否生成出库单',
							dataIndex: 'hasSC',
							align: 'center',
							customRender: (text, record, index) => {
								if (record.impexpMarkcd === 'E' && text === '1') {
									return '是'
								} else if (record.impexpMarkcd === 'I') {
									return '-'
								} else {
									return '否'
								}
							}
						},
						{
							title: '关联出库单编号',
							dataIndex: 'storageNos',
							align: 'center',
							scopedSlots: { customRender: 'storageNos' }
						},
						{
							title: '是否推送',
							dataIndex: 'send',
							align: 'center',
							customRender: (text, record, index) => {
								if (record.send === true) {
									return '是'
								} else {
									return '否'
								}
							}
						},
						{
							dataIndex: 'initialReviewStatus',
							title: '初复审状态',
							align: 'center',
							customRender:  (text, record, index)=> {
								if (text=='0'){
									return '未审核'
								}else if(text=='1'){
									return "已初审/未复审";
								}else if(text=='2'){
									return '已复审'
								}else {
									return '未审核'
								}
							},
						},
						{
							dataIndex: 'firstTrialBy',
							align: 'center',
							title: '初审人'
						},
						{
							dataIndex: 'firstTrialDate',
							align: 'center',
							title: '初审时间'
						},
						{
							dataIndex: 'firstOpinion',
							align: 'center',
							title: '初审意见',
							scopedSlots: { customRender: 'firstOpinion' }
						},
						{
							dataIndex: 'reviewBy',
							align: 'center',
							title: '复审人'
						},
						{
							dataIndex: 'reviewDate',
							align: 'center',
							width: 200,
							title: '复审时间'
						},
						{
							dataIndex: 'reviewOpinion',
							align: 'center',
							width: 200,
							title: '复审意见',
							scopedSlots: { customRender: 'reviewOpinion' }
						},




            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              fixed: 'right',
              width: 20,
              scopedSlots: { customRender: 'action' },
            },
          ],
					url: {
						list: '/dcl/invt/list',
						deleteBatch: '/dcl/invt/delBatch',
						handleManualDeduction: '/dcl/invt/handleManualDeduction',
						handlePush: '/dcl/invt/handlePush',
						importExcelUrl: '/dcl/invt/importExcel',
						handleSync: '/dcl/invt/handleSync',
					},
					jckkaList:[],
					jgfsList:[],
					exportCustomFileVisible:false
        }
      },
			computed: {
				gridOptions() {
					const gridOptions = {
						id: 'Table',
						pagerConfig: {
							currentPage:this.ipagination.currentPage,
							pageSize:this.ipagination.pageSize,
							pageSizes: [15, 30, 50, 100, 200],
							total:this.ipagination.total
						},
						toolbarConfig: {
							perfect: true,
							refresh: {
								query: () => this.loadData(1)
							},
							zoom: true,
							custom: true,
							slots: {
								buttons: 'toolbar_buttons'
							}
						},
						columns: [
							{
								type: 'checkbox',
								field: 'checkbox',
								align: 'center',
								width: 50,
								fixed: 'left',
							},
							{
								title: '经营单位名称',
								align: 'center',
								width: 200,
								field: 'bizopEtpsNm',
							},
							{
								title: '清单编号',
								align: 'center',
								width: 160,
								field: 'bondInvtNo',
							},
							{
								title: '清单类型',
								align: 'center',
								field: 'invtType',
								width: 110,
								formatter:function ({ cellValue, row, column }) {
									let re = "";
									if (cellValue == 0) {
										re = "普通清单"
									} else if (cellValue == 3) {
										re = "先入区后报关";
									} else if (cellValue == 4) {
										re = "简单加工";
									} else if (cellValue == 5) {
										re = "保税展示交易";
									} else if (cellValue == 6) {
										re = "区内流转";
									} else if (cellValue == 8) {
										re = "保税电商";
									} else if (cellValue == 9) {
										re = "一纳成品内销";
									} else if (cellValue == 1) {
										re = "集报清单";
									}else if (cellValue == 7) {
										re = "区港联动";
									}

									return re;
								}
							},
							{
								title: '企业内部编号',
								align: 'center',
								field: 'etpsInnerInvtNo',
								width: 160,
							},
							{
								title: '预录入统一编号',
								align: 'center',
								field: 'seqNo',
								width: 160,
							},
							{
								title: '手账册编号',
								align: 'center',
								field: 'putrecNo',
								width: 120,
							},
							{
								title: '进出口',
								align: 'center',
								field: 'impexpMarkcd',
								width: 80,
								formatter:function ({ cellValue, row, column }) {
									if(cellValue=='I'){
										return "进口"
									}else if(cellValue=='E'){
										return "出口"
									}else{
										return cellValue
									}
								}
							},
							{
								title: '核扣标志',
								align: 'center',
								field: 'vrfdedMarkcd',
								width: 90,
								formatter:function ({ cellValue, row, column }) {
									if (cellValue == '0') {
										return "未核扣"
									} else if (cellValue == '1'){
										return "预核扣"
									} else if (cellValue == '2'){
										return "已核扣"
									} else if (cellValue == '3'){
										return "已核销"
									} else if (cellValue == '4'){
										return "反核扣"
									} else {
										return cellValue
									}
								}
							},
							{
								title: '报关标志',
								align: 'center',
								field: 'dclcusFlag',
								width: 80,
								formatter:function ({ cellValue, row, column }) {
									if(cellValue=='1'){
										return "报关"
									}else if(cellValue=='2'){
										return "非报关"
									}else{
										return cellValue
									}
								}
							},
							{
								title: '报关类型',
								align: 'center',
								field: 'dclcusTypecd',
								width: 80,
								formatter:function ({ cellValue, row, column }) {
									if(cellValue=='1'){
										return "关联报关"
									}else if(cellValue=='2'){
										return "对应报关"
									}else{
										return cellValue
									}
								}
							},
							{
								title: '申报类型',
								align: 'center',
								field: 'dclTypecd',
								width: 80,
								formatter:function ({ cellValue, row, column }) {
									if(cellValue=='1'){
										return "备案申请"
									}else if(cellValue=='2'){
										return "变更申请"
									}else if(cellValue=='3'){
										return "删除申请"
									}else{
										return cellValue
									}
								}
							},

							{
								title: '进出口口岸',
								align: 'center',
								field: 'impexpPortcd',
								width: 100,
								formatter: this.impexpPortcdFormat
							},
							{
								title: '申报日期',
								align: 'center',
								width: 100,
								field: 'invtDclTime'
							},
							{
								title: '数据状态',
								align: 'center',
								field: 'status',
								width: 100,
								formatter:function ({ cellValue, row, column }) {
									if(cellValue=='0'){
										return "暂存"
									}else if(cellValue=='1'){
										return "申报成功"
									}else if(cellValue=='4'){
										return "成功发送海关"
									}else if(cellValue=='5'){
										return "海关接收成功"
									}else if(cellValue=='6'){
										return "海关接收失败"
									}else if(cellValue=='B'){
										return "海关终审通过"
									}else if(cellValue=='C'){
										return "海关退单"
									}else if(cellValue=='E'){
										return "删除"
									}else if(cellValue=='N'){
										return "待导入其他报文"
									}else if(cellValue=='P'){
										return "预审批通过"
									}else{
										return cellValue
									}
								}
							},
							{
								title: '加工单位名称',
								align: 'center',
								field: 'rcvgdEtpsNm',
								width: 200,
							},

							{
								title: '监管方式',
								align: 'center',
								field: 'supvModecd',
								width: 120,
								formatter: this.supvModecdFormat
							},

							{
								title: '已核扣日期',
								align: 'center',
								width: 120,
								field: 'warehousingDate'
							},

							{
								title: '创建人',
								align: 'center',
								width: 80,
								field: 'createPerson'
							},
							{
								title: '创建日期',
								align: 'center',
								width: 140,
								field: 'createDate'
							},
							{
								title: '是否生成出库单',
								field: 'hasSC',
								align: 'center',
								width: 120,
								formatter:function ({ cellValue, row, column }) {
									if (row.impexpMarkcd === 'E' && cellValue === '1') {
										return '是'
									} else if (row.impexpMarkcd === 'I') {
										return '-'
									} else {
										return '否'
									}
								}
							},
							{
								title: '关联出入库单编号',
								field: 'storageNos',
								align: 'center',
								width: 140,
							},
							{
								title: '是否推送',
								field: 'send',
								align: 'center',
								width: 80,
								formatter:function ({ cellValue, row, column }) {
									if (row.send === true) {
										return '是'
									} else {
										return '否'
									}
								}
							},
							{
								field: 'initialReviewStatus',
								title: '初复审状态',
								align: 'center',
								width: 140,
								formatter:function ({ cellValue, row, column }) {
									if (cellValue=='0'){
										return '未审核'
									}else if(cellValue=='1'){
										return "已初审/未复审";
									}else if(cellValue=='2'){
										return '已复审'
									}else {
										return '未审核'
									}
								},
							},
							{
								field: 'firstTrialBy',
								align: 'center',
								title: '初审人',
								width: 140,
							},
							{
								field: 'firstTrialDate',
								align: 'center',
								title: '初审时间',
								width: 140,
							},
							{
								field: 'firstOpinion',
								align: 'center',
								title: '初审意见',
								width: 140,
							},
							{
								field: 'reviewBy',
								align: 'center',
								title: '复审人',
								width: 140,
							},
							{
								field: 'reviewDate',
								align: 'center',
								width: 200,
								title: '复审时间'
							},
							{
								field: 'reviewOpinion',
								align: 'center',
								width: 200,
								title: '复审意见',
							},

							{
								title: '操作',
								field: 'action',
								align: 'center',
								fixed: 'right',
								width: 80,
								slots: {
									default: 'action'
								}
							},

						]



					}
					return gridOptions
				},
				routeTab() {
					return {
						title: '核注清单',
						tips: `核注清单`
					}
				}
			},
      created() {
				this.initDictData('erp_customs_ports,name,customs_port_code')
				this.initDictData2("JGFS")
				this.loadData(1)
				this.getAllFields()
      },
      methods: {
		 /**
             * 核注单所有自定义字段
             */
            getAllFields() {
                getAction('/dcl/invt/listInvtFields', {
                    type: '2'
                }).then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    this.columnsVO = res.result
                })
            },
        handleSelectExport() {
            if (this.selectedRowKeys.length <= 0) {
                this.$message.warning('请选择一条记录！')
                return
            }
            // 列表导出
            this.exportCustomFileVisible = true
			this.$refs.exportCustomFileRef.exportType = 'S'
        },
        handleQueryExport() {
            let queryType = false
            for (let key in this.queryParam) {
                if (!this.isEmpty(this.queryParam[key])&&'ieFlag'!=key) {
                    queryType = true
                    break
                }
            }
            
            if (!queryType) {
                this.$message.warning('请增加导出条件！')
                return
            }
            
            this.exportCustomFileVisible = true
			this.$refs.exportCustomFileRef.exportType = 'Q'
        },
		handleDetailExport(){

		},
				onClearSelected() {
					this.selectedRowKeys = []
					this.selectionRows = []
				},
				reCalculatePage(count) {
					//总数量-count
					let total = this.ipagination.total - count
					//获取删除后的分页数
					let currentIndex = Math.ceil(total / this.ipagination.pageSize)
					//删除后的分页数<所在当前页
					if (currentIndex < this.ipagination.current) {
						this.ipagination.current = currentIndex
					}
					console.log('currentIndex', currentIndex)
				},
				cellDblclick({ row }){
					this.$router.push({
						path: '/Business/NemsInvt/nemsInvtDetailsEdit',
						query: {
							id: row.id
						}
					})
				},
				impexpPortcdFormat({ cellValue, row, column }){
					const o=this.jckkaList.find(i=>i.value==cellValue)
					return o?o.text:cellValue
				},
				supvModecdFormat({ cellValue, row, column }){
					const o=this.jgfsList.find(i=>i.value==cellValue)
					return o?o.text:cellValue
				},
				initDictData(dictCode) {
					//根据字典Code, 初始化字典数组
					ajaxGetDictItems(dictCode, null).then((res) => {
						if (res.success) {
							this.jckkaList = res.result
						}
					})

				},
				initDictData2(dictCode) {
					//根据字典Code, 初始化字典数组
					ajaxGetDictItems(dictCode, null).then((res) => {
						if (res.success) {
							this.jgfsList = res.result
						}
					})

				},
				/**
				 * 手动同步保税维修的核注单
				 */
				handleSync() {
					this.syncLoading = true
					getAction(this.url.handleSync)
						.then((res) => {
							if (res.success) {
								this.$message.success('同步成功，请重新查询数据')
								this.$tabs.refresh()
							}
						}).finally(() => {
						this.syncLoading = false
					})
				},
				batchDel: function () {
					if (!this.url.deleteBatch) {
						this.$message.error('请设置url.deleteBatch属性!')
						return
					}
					if (this.selectedRowKeys.length <= 0) {
						this.$message.warning('请选择一条记录！')
						return
					} else {
						for(let a of this.selectionRows){
							/* 	VRFDED_MARKCD 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
      核注单0或空能删除，
      2不能再保存修改 */
							if(!a.vrfdedMarkcd||a.vrfdedMarkcd=='0'){

							}else {
								this.$message.error('包含无法删除的核注单，请重新选择进行删除')
								retrun
							}
						}

						var ids = ''
						for (var a = 0; a < this.selectedRowKeys.length; a++) {
							ids += this.selectedRowKeys[a] + ','
						}
						var that = this
						this.$confirm({
							title: '确认删除',
							content: '是否删除选中数据?',
							onOk: function () {
								that.loading = true
								deleteAction(that.url.deleteBatch, { ids: ids })
									.then(res => {
										if (res.success) {
											//重新计算分页问题
											that.reCalculatePage(that.selectedRowKeys.length)
											that.$message.success(res.message)
											that.loadData()
											that.onClearSelected()
										} else {
											that.$message.warning(res.message)
										}
									})
									.finally(() => {
										that.loading = false
									})
							}
						})
					}
				},

				//导入
				handleImportExcel() {
					this.$refs.importModal.visible = true
				},
				/**
				 * 复制核注单
				 */
				batchCopy() {
					if (this.selectionRows.length !== 1) {
						this.$message.warning('请选择一条记录！')
						return
					}
					this.$router.push({
						path: '/Business/NemsInvt/nemsInvtDetailsEdit',
						query: {
							id: this.selectionRows[0].id,
							copyStatus: 1
						}
					})
				},
				handleManualDeductionOk() {
					let idArr = []
					this.selectionRows.forEach(item => {
						idArr.push(item.id)
					})
					// 触发表单验证
					this.$refs.formModal.validate(valid => {
						if (valid) {
							this.modalLoading = true
							_postAction(this.url.handleManualDeduction, {
								ids: idArr.join(','),
								warehousingDate: this.modelModal.warehousingDate,
								type: '1'
							}).then((res) => {
									if (res.success) {
										this.$message.success('核扣成功！')
										this.handleManualDeductionCancel()
										this.searchQuery()
									} else {
										this.$message.warning(res.message || res)
									}
								})
								.finally(() => {
									this.modalLoading = false
								})
						} else {
							this.$message.warning('请检查必填项！')
							return false
						}
					})
				},
				handleManualDeductionCancel() {
					this.modelModal = {
						warehousingDate: ''
					}
					this.vrfdedMarkcdModalVisible = false
				},
				/**
				 * 推送核注单
				 */
				handlePush() {
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择至少一条记录！')
						return
					}
					let statusType = false
					let statusList = []
					let pushList = []
					for (let i = 0; i < this.selectionRows.length; i++) {
						if (this.selectionRows[i].status == 'E') {
							statusList.push(this.selectionRows[i].id)
							statusType = true
						}
						if (this.selectionRows[i].send === true) {
							pushList.push(this.selectionRows[i].id)
						}
					}
					if (statusType) {
						this.$message.warning('所选数据[' + statusList.join(',') + ']已删单，不允许推送报文！')
						return
					}
					let msg = `确定要推送报文吗?`
					if (pushList.length > 0) {
						// this.$message.warning('所选数据[' + pushList.join(',') + ']已推送，不允许重复推送！')
						// return
						msg = `存在已推送数据，确定要重复推送吗?`
					}
					var that = this
					this.$confirm({
						title: '操作确认',
						content: msg,
						onOk: async function() {
							that.pushLoading = true
							_postAction(that.url.handlePush, {
								ids: that.selectedRowKeys.join(','),
								passageway: 'DY'
							}).then((res) => {
								if (res.success) {
									that.$message.success(res.result)
									that.$tabs.refresh()
								} else {
									that.$message.warning(res.message)
								}
							}).finally((that.pushLoading = false))
						}
					})
				},
				/**
				 * 手动核扣
				 */
				handleManualDeduction() {
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择至少一条记录！')
						return
					}
					let arr = []
					for (let i = 0; i < this.selectionRows.length; i++) {
						if (this.selectionRows[i].vrfdedMarkcd == '2') {
							arr.push(this.selectionRows[i].id)
						}
					}
					if (arr.length > 0) {
						this.$message.warning(`核注单[${arr.join(',')}]已核扣，无法再次核扣！`)
						return
					}
					this.vrfdedMarkcdModalVisible = true
				},
				/**
				 * 根据核注单生成入库单
				 */
				handleCreateInStorage() {
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择至少一条记录！')
						return
					}
					let isAllI = false
					let hasSC = []
					this.selectionRows.forEach(item => {
						if (item.impexpMarkcd != 'I') {
							isAllI = true
						}
						if (item.hasSC == '1') {
							hasSC.push(item.id)
						}
					})
					if (isAllI) {
						this.$message.warning('请选择进口核注单！')
						return
					}
					if (hasSC.length > 0) {
						this.$message.warning(`核注单[${hasSC.join(',')}]已生成过出库单，无法重复生成！`)
						return
					}
					if (this.selectionRows.length > 1) {
						const areJYEqual = this.areAllElementsEqual(this.selectionRows, 'bizopEtpsno')
						if (!areJYEqual) {
							this.$message.warning('请选择相同「经营单位编码」的核注单!')
							return
						}
						const areJGEqual = this.areAllElementsEqual(this.selectionRows, 'rcvgdEtpsno')
						if (!areJGEqual) {
							this.$message.warning('请选择相同「加工单位编码」的核注单!')
							return
						}
					}
					var that = this
					this.$confirm({
						title: '操作确认',
						content: `确定要根据选择的${this.selectedRowKeys.length}条核注单生成出库单吗?`,
						onOk: async function() {
							that.$refs.inStoreModalRef.add({
								ids: that.selectedRowKeys.join(','),
								bizopEtpsno: that.selectionRows[0].bizopEtpsno,
								rcvgdEtpsno: that.selectionRows[0].rcvgdEtpsno
							})
							that.$refs.inStoreModalRef.title = '新增'
							that.$refs.inStoreModalRef.disableSubmit = false
						}
					})
				},
				areAllElementsEqual(arr, propertyName) {
					return arr.every((element) => element[propertyName] === arr[0][propertyName])
				},
				handleCreateByOutStorage() {
					this.$refs.fromBondedWarehouseModal.show()
				},
        handleFirstTrial() {
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择一条记录！')
						return
					}
					this.model = {firstOpinion: '', reviewOpinion: ''}
					this.title = '初审意见'
					this.isFirst = true
					this.visible = true
				},
        handleReview() {
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择一条记录！')
						return
					}
					this.model = {firstOpinion: '', reviewOpinion: ''}
					this.title = '复审意见'
					this.isFirst = false
					this.visible = true
				},
				handleEdit(record) {
					this.$router.push({
						path: '/Business/NemsInvt/nemsInvtDetailsEdit',
						query: {
							id: record.id
						}
					})
				},
        handleAdd(arg) {
          this.$router.push({
            path: '/Business/NemsInvt/nemsInvtDetailsEdit',
            query: {
              impexpMarkcd: arg.key,
              newStatus: true
            }
          })
        },
				handleChange(key) {
					console.log(key)
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择一条记录！')
						return
					}
					this.$router.push({
						path: '/Business/NemsInvt/nemsInvtDetailsEdit',
						query: {
							id: this.selectionRows[0].id
						}
					})
				},
        /**
         * 点击表格行触发
         * @param {Object} record - 行数据
         * @param {Number} index - 索引值
         * @return Function
         */
        rowEvent: function(record, index) {
          return {
            on: {
							click: () => {
								let keys = []
								this.selectionRows = []
								keys.push(record.id)
								this.selectedRowKeys = keys
								this.selectionRows.push(record)
							},
              dblclick: () => {
                console.log('双击了我')
                this.$router.push({
                  path: '/Business/NemsInvt/nemsInvtDetailsEdit',
                  query: {
                    id: record.id
                  }
                })
              }
            }
          }
        },
				handleDelete: function (id,record) {
					/* 	VRFDED_MARKCD 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
	核注单0或空能删除，
	2不能再保存修改 */
					if(!record.vrfdedMarkcd||record.vrfdedMarkcd=='0'){

					}else {
						this.$message.error('该核注单无法删除')
						retrun
					}
					var that = this
					deleteAction(that.url.deleteBatch, { ids: id }).then(res => {
						if (res.success) {
							//重新计算分页问题
							that.reCalculatePage(1)
							that.$message.success(res.message)
							that.loadData()
						} else {
							that.$message.warning(res.message)
						}
					})
				},
				handleOk() {
					let idArr = []
					let arr = []
					let arr1 = []
					this.selectionRows.forEach(item => {
						if (item.initialReviewStatus!='0' && item.initialReviewStatus!='') {
							arr.push(item)
						}
						if (item.status && item.status!='1') {
							arr1.push(item)
						}
						idArr.push(item.id)
					})
					if (this.isFirst) {
						if (arr.length > 0) {
							this.$message.error('只有未审核状态的才能进行初审，请重新选择!')
							return
						}
					}
					if (arr1.length > 0) {
						this.$message.error('已申报的核注单，不允许再进行初复审，请重新选择!')
						return
					}
					// 触发表单验证
					this.$refs.form.validate(valid => {
						if (valid) {
							this.confirmLoading = true
							_postAction('/dcl/invt/handleInitialReview', {
								ids: idArr.join(','),
								initialReviewStatus: this.isFirst ? '1' : '2',
								opinion: this.isFirst ? this.model.firstOpinion : this.model.reviewOpinion
							}).then(res => {
								if (res.success) {
									this.$message.success(res.message)
									this.searchQuery()
								} else {
									this.$message.warning(res.message)
								}
								this.confirmLoading = false
								this.visible = false
							})
						}
					})
				},
        subStrForColumns,
        onQueryTimeChange(value, dateString) {
          console.log(dateString[0], dateString[1])
          this.queryParam.starCreateDate = dateString[0].toString()
          this.queryParam.lastCreateDate = dateString[1].toString()
        },
				selectDclDateChange(value, dateString){
					this.queryParam.starDclDate = dateString[0].toString()
					this.queryParam.lastDclDate = dateString[1].toString()
				},
				handleCancel() {
					this.visible = false
				},
				closeUploadModal() {
					this.$refs.importModal.visible = false
					this.$refs.importModal.fileList = []
					this.loadData()
					this.onClearSelected()
				},
        searchReset() {
          this.queryParam = {ieFlag: 'E'}
					this.selectCloseDate = []
					this.selectDclDate = []
          this.loadData(1)
          this.onClearSelected()
        },
				handleCreateOutStorage(){
					if (this.selectionRows.length <= 0) {
						this.$message.warning('请选择至少一条记录！')
						return
					}
					let isAllE = false
					let hasSC = []
					this.selectionRows.forEach(item => {
						if (item.impexpMarkcd != 'E') {
							isAllE = true
						}
						if (item.hasSC == '1') {
							hasSC.push(item.id)
						}
					})
					if (isAllE) {
						this.$message.warning('请选择出口核注单！')
						return
					}
					if (hasSC.length > 0) {
						this.$message.warning(`核注单[${hasSC.join(',')}]已生成过出库单，无法重复生成！`)
						return
					}
					if (this.selectionRows.length > 1) {
						const areJYEqual = this.areAllElementsEqual(this.selectionRows, 'bizopEtpsno')
						if (!areJYEqual) {
							this.$message.warning('请选择相同「经营单位编码」的核注单!')
							return
						}
						const areJGEqual = this.areAllElementsEqual(this.selectionRows, 'rcvgdEtpsno')
						if (!areJGEqual) {
							this.$message.warning('请选择相同「加工单位编码」的核注单!')
							return
						}
					}
					var that = this
					this.$confirm({
						title: '操作确认',
						content: `确定要根据选择的${this.selectedRowKeys.length}条核注单生成出库单吗?`,
						onOk: async function() {
							that.$refs.outStoreModalRef.add({
								ids: that.selectedRowKeys.join(','),
								bizopEtpsno: that.selectionRows[0].bizopEtpsno,
								rcvgdEtpsno: that.selectionRows[0].rcvgdEtpsno
							})
							that.$refs.outStoreModalRef.title = '出库单新增'
							that.$refs.outStoreModalRef.disableSubmit = false
						}
					})




				},
				handleEditHis(record){
					this.$refs.ediStatusList.visible= true;
					this.$nextTick(()=>{
						this.$refs.ediStatusList.getEdis(record.etpsInnerInvtNo,record.seqNo);
					})
				},
      }
    }
</script>
<style  lang="less" scoped>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-card-body{
	padding-top: 4px;

}
    /deep/ .ant-tabs-bar {
        margin: 0px 0px 4px 0px !important;
    }
    /deep/ .ant-tabs .ant-tabs-large-bar .ant-tabs-tab {
        padding: 12px;
    }
    /deep/.ant-card-head {
        min-height: 44px !important;
        margin-bottom: -1px;
        padding: 0 24px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 13px;
        background: transparent;
        border-bottom: 0px solid #e8e8e8 !important;
        zoom: 1;
    }
    /deep/.ant-badge {
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        color: rgba(0, 0, 0, 0.65);
        font-size: 13px;
        font-variant: tabular-nums;
        line-height: 1.5;
        list-style: none;
        -webkit-font-feature-settings: 'tnum';
        font-feature-settings: 'tnum';
        position: relative;
        display: inline-block;
        color: unset;
        line-height: 1;
    }

    /deep/ .ant-badge-count {
        min-width: 20px;
        height: 16px !important;
        padding: 0 6px;
        color: #fff;
        font-weight: normal;
        font-size: 12px;
        line-height: 16px !important;
        border-radius: 10px;
        -webkit-box-shadow: 0 0 0 1px #fff;
        box-shadow: 0 0 0 1px #fff;
    }
</style>
