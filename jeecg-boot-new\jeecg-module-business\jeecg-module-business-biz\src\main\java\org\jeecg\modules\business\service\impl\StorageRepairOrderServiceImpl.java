package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.enums.StockTypeEnum;
import org.jeecg.modules.business.entity.paramVo.RepairOrderStocksVO;
import org.jeecg.modules.business.entity.paramVo.RepairOrderVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.E;
import static org.jeecg.common.constant.CommonConstant.I;

/**
 * <p>
 * 维修单信息表（表头） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Slf4j
@Service
public class StorageRepairOrderServiceImpl extends ServiceImpl<StorageRepairOrderMapper, StorageRepairOrder> implements IStorageRepairOrderService {
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private IStorageRepairOrderDetailService storageRepairOrderDetailService;
    @Autowired
    private ICommissionerService commissionerService;
    @Autowired
    private IStoreStocksService storeStocksService;
    @Autowired
    private StoreInfoMapper storeInfoMapper;
    @Autowired
    private StorageInfoMapper storageInfoMapper;
    @Autowired
    private StorageDetailMapper storageDetailMapper;
    @Autowired
    private StoreSpaceMapper storeSpaceMapper;
    @Autowired
    private StorageRepairOrderDetailMapper repairOrderDetailMapper;
    @Autowired
    private PtsEmsHeadMapper emsHeadMapper;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private IAttachmentsInfoService attachmentsInfoService;
    @Autowired
    private IStorageTransferService storageTransferService;
    @Autowired
    private StorageTransferMapper storageTransferMapper;
    @Autowired
    private StorageTransferDetailMapper storageTransferDetailMapper;
    @Autowired
    private IStorageInfoService storageInfoService;

    /**
     * 保存维修单信息
     *
     * @param storageRepairOrder
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/12 上午9:25
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveRepairOrder(StorageRepairOrder storageRepairOrder) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(storageRepairOrder.getId())) {
            String repairNo = serialNumberService.getSerialnumberByCustomerCode("WXD", 4);
            storageRepairOrder.setRepairNo(repairNo);
            storageRepairOrder.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageRepairOrder.setCreateDate(new Date());
            baseMapper.insert(storageRepairOrder);
            // 编辑
        } else {
            storageRepairOrder.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageRepairOrder.setUpdateDate(new Date());
            baseMapper.updateById(storageRepairOrder);
        }
        if (isNotEmpty(storageRepairOrder.getStorageRepairOrderDetailList())) {
            List<StorageRepairOrderDetail> oldRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                    .eq(StorageRepairOrderDetail::getRepairNo, storageRepairOrder.getRepairNo()));
            if (isNotEmpty(oldRepairOrderDetailList)) {
                // 使用 Stream 进行过滤
                List<StorageRepairOrderDetail> dels = oldRepairOrderDetailList.stream()
                        .filter(item -> storageRepairOrder.getStorageRepairOrderDetailList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    storageRepairOrderDetailService.removeBatchByIds(dels.stream().map(StorageRepairOrderDetail::getId).collect(Collectors.toList()));
                }
            }
            for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrder.getStorageRepairOrderDetailList()) {
                if (isBlank(storageRepairOrderDetail.getId())) {
                    storageRepairOrderDetail.setRepairNo(storageRepairOrder.getRepairNo());
                    storageRepairOrderDetail.setHeadId(storageRepairOrder.getId());
                    storageRepairOrderDetail.setStoreCode(storageRepairOrder.getStoreCode());
                    storageRepairOrderDetail.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageRepairOrderDetail.setCreateDate(new Date());
                    storageRepairOrderDetailService.save(storageRepairOrderDetail);
                } else {
                    storageRepairOrderDetail.setStoreCode(storageRepairOrder.getStoreCode());
                    storageRepairOrderDetail.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageRepairOrderDetail.setUpdateDate(new Date());
                    storageRepairOrderDetailService.updateById(storageRepairOrderDetail);
                }
            }
        } else {
            storageRepairOrderDetailService.remove(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                    .eq(StorageRepairOrderDetail::getRepairNo, storageRepairOrder.getRepairNo()));
        }
        // 2024/8/23 上午10:54@ZHANGCHAO 追加/变更/完善：保存附件！！
        if (isNotEmpty(storageRepairOrder.getAttachmentList())) {
            for (AttachmentsInfo attachmentsInfo : storageRepairOrder.getAttachmentList()) {
                AttachmentsInfo atta = attachmentsInfoService.getOne(new LambdaQueryWrapper<AttachmentsInfo>()
                        .eq(AttachmentsInfo::getRelationId, storageRepairOrder.getId())
                        .eq(AttachmentsInfo::getTenantId, storageRepairOrder.getTenantId())
                        .eq(AttachmentsInfo::getAttachmentsFileType, attachmentsInfo.getAttachmentsFileType())
                        .eq(AttachmentsInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
                if (atta == null) {
//                    新建附件信息
                    attachmentsInfo.setId(null);
                    attachmentsInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
                    attachmentsInfo.setRelationId(storageRepairOrder.getId());
                    attachmentsInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
                    attachmentsInfo.setUpdateTime(new Date());
                    attachmentsInfoService.save(attachmentsInfo);
                } else {
//                    编辑
                    attachmentsInfo.setId(atta.getId());
                    attachmentsInfo.setCreateBy(atta.getCreateBy());
                    attachmentsInfo.setCreateTime(atta.getCreateTime());
                    LambdaUpdateWrapper<AttachmentsInfo> updateWrapper = new UpdateWrapper().lambda();
                    updateWrapper.last(CommonUtils.SetUpdateSqlCondition(attachmentsInfo.getUpdateTime(), attachmentsInfo.getId()));
                    attachmentsInfoService.update(attachmentsInfo, updateWrapper);
                }
            }
        }
        StorageRepairOrder resultRepairOrder= baseMapper.selectById(storageRepairOrder.getId());
        resultRepairOrder.setStorageRepairOrderDetailList(storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                .eq(StorageRepairOrderDetail::getRepairNo, resultRepairOrder.getRepairNo())));
        resultRepairOrder.setAttachmentList(attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, resultRepairOrder.getId())));
        return Result.ok(resultRepairOrder);
    }

    /**
     * 根据ID查询出维修单信息
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/12 上午11:21
     */
    @Override
    public Result<?> getRepairOrderById(String id) {
        StorageRepairOrder storageRepairOrder = baseMapper.selectById(id);
        if (isEmpty(storageRepairOrder)) {
            return Result.error("未找到ID为" + id + "的维修单信息，请刷新页面重试！");
        }
        if (isNotBlank(storageRepairOrder.getCustomer())) {
            String tenantName = null;
            try {
                // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageRepairOrder.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：{}", e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageRepairOrder.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageRepairOrder.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageRepairOrder.setCustomerStr(tenantName);
            }
        }
        List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                .eq(StorageRepairOrderDetail::getRepairNo, storageRepairOrder.getRepairNo()));
        if (isNotEmpty(storageRepairOrderDetailList)) {
            storageRepairOrderDetailList.forEach(item -> {
                item.setCustomer(storageRepairOrder.getCustomer());
                Result<StoreStocks> result = storeStocksService.getStockByRepairDetail(item);
                StoreStocks storeStocks = null;
                if (result.isSuccess()) {
                    storeStocks = result.getResult();
                }
                if (isNotEmpty(storeStocks)) {
                    BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                    BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                    item.setStockQty(beginQty); // 实际库存数量
                    item.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
                }
            });
        }
        storageRepairOrder.setStorageRepairOrderDetailList(storageRepairOrderDetailList);
        // 2024/6/11 上午10:51@ZHANGCHAO 追加/变更/完善：入库单附件！！
        List<AttachmentsInfo> attachmentList = attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, storageRepairOrder.getId()));
        storageRepairOrder.setAttachmentList(attachmentList);
        return Result.ok(storageRepairOrder);
    }

    /**
     * 删除维修单
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/12 下午1:54
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        for (String id : ids.split(",")) {
            StorageRepairOrder storageRepairOrder = baseMapper.selectById(id);
            if (isEmpty(storageRepairOrder)) {
                continue;
            }
            if ("1".equals(storageRepairOrder.getAudits())) {
                return Result.error("已审核的数据无法删除！");
            }
            // 2024/12/11 12:55@ZHANGCHAO 追加/变更/完善：处理调拨单
            List<StorageTransfer> storageTransfers = storageTransferMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                    .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo()));
            if (isNotEmpty(storageTransfers)) {
                boolean hasCompleted = storageTransfers.stream().anyMatch(transfer -> "1".equals(transfer.getStatus()));
                if (hasCompleted) {
                    return Result.error("存在已确认的调拨单，请先处理调拨单！");
                }
            }
            List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                    .eq(StorageRepairOrderDetail::getRepairNo, storageRepairOrder.getRepairNo()));
            if (isNotEmpty(storageRepairOrderDetailList)) {
                for (StorageRepairOrderDetail recordOrderDetail : storageRepairOrderDetailList) {
                    storageRepairOrderDetailService.removeById(recordOrderDetail.getId());
                }
            }
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 维修单状态变更
     *
     * @param ids
     * @param status
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/12 下午2:47
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleChangeStatus(String ids, String status, String closeReason, String sn, String billNo, String batchNoNew) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<StorageRepairOrder> storageRepairOrderList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isNotEmpty(storageRepairOrderList)) {
            boolean allAudited = storageRepairOrderList.stream().allMatch(order -> "1".equals(order.getAudits()));
            if (!allAudited) {
                return Result.error("只能操作已审核的数据，请重新选择！");
            }
            for (StorageRepairOrder storageRepairOrder : storageRepairOrderList) {
                try {
                    if (isNotBlank(storageRepairOrder.getCustomer())) {
                        String tenantName = null;
                        try {
                            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                            Result<Tenant> tenant = sysBaseApi.getTenantById(storageRepairOrder.getCustomer());
                            if (isNotEmpty(tenant.getResult())) {
                                tenantName = tenant.getResult().getName();
                            }
                        } catch (Exception e) {
                            log.error("获取租户名出现异常：{}", e.getMessage());
                        }
                        Commissioner commissioner = commissionerService.getById(storageRepairOrder.getCustomer());
                        if (isNotEmpty(commissioner)) {
                            storageRepairOrder.setCustomerStr(commissioner.getCommissionerFullName());
                        } else {
                            storageRepairOrder.setCustomerStr(tenantName);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取客户信息出现异常：{}", e.getMessage());
                }
                /*
                 * 1 == 开始检验
                 * ①维修单开始检验，进入维修中状态。自动产生一个调拨单（从待维修库区到维修货物区的一个调拨单），
                 * 调拨单储运人员确认后，自动移库，待维修进入维修货物库区。
                 * （注：库区变化，相应库存也会变化----待维修库区减库存，维修货物库区加库存或新增）。
                 * 2024/8/27 10:24@ZHANGCHAO
                 */
                if ("1".equals(status)) {
                    // 查询货主待维修库区的第一个储位
                    StoreSpace storeSpace_DWX = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "2"); // 2 = 待维修库区
                    if (isEmpty(storeSpace_DWX)) {
                        throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无待维修库区或没有相应储位，请先配置！");
                    }
                    // 查询货主维修货物库区的第一个储位
                    StoreSpace storeSpace_WXHW = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "4"); // 4 = 维修货物库区
                    if (isEmpty(storeSpace_WXHW)) {
                        throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无维修货物库区或没有相应储位，请先配置！");
                    }
                    StorageTransfer storageTransfer = new StorageTransfer();
                    storageTransfer.setStep("1"); // 1开始检验进入维修中
                    storageTransfer.setRelRepairNo(storageRepairOrder.getRepairNo());
                    storageTransfer.setStoreCode(storageRepairOrder.getStoreCode());
                    storageTransfer.setCustomer(storageRepairOrder.getCustomer());
                    storageTransfer.setAreaCodeBefore(storageRepairOrder.getAreaName()); // 调拨前库区编码
                    storageTransfer.setAreaNameBefore(storageRepairOrder.getAreaCode()); // 调拨前库区名称
                    storageTransfer.setAreaCodeAfter(storeSpace_WXHW.getAreaCode()); // 调拨后库区编码 - 维修货物区的第一个储位默认
                    storageTransfer.setAreaNameAfter(storeSpace_WXHW.getAreaName()); // 调拨后库区名称 - 维修货物区的第一个储位默认
                    storageTransfer.setAppDate(new Date());
                    storageTransfer.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "「开始检验」操作自动生成");
                    storageTransfer.setCreateBy(loginUser.getUsername());
                    storageTransfer.setCreateDate(new Date());
                    List<StorageTransferDetail> storageTransferDetailList = new ArrayList<>();
                    StorageTransferDetail storageTransferDetail = new StorageTransferDetail();
                    storageTransferDetail.setSpaceCodeBefore(storageRepairOrder.getSpaceCode()); // 调拨前储位编码
                    storageTransferDetail.setSpaceNameBefore(storageRepairOrder.getSpaceName()); // 调拨前储位名称
                    storageTransferDetail.setSpaceCodeAfter(storeSpace_WXHW.getCabinCode()); // 调拨后储位编码
                    storageTransferDetail.setSpaceNameAfter(storeSpace_WXHW.getSpaceCode()); // 调拨后储位名称
                    storageTransferDetail.setItemNumber(storageRepairOrder.getItemNumber());
                    storageTransferDetail.setBatchNo(storageRepairOrder.getBatchNo());
                    storageTransferDetail.setCopGno(storageRepairOrder.getCopGno());
                    storageTransferDetail.setPn(storageRepairOrder.getPn());
                    storageTransferDetail.setSn(storageRepairOrder.getSn());
                    storageTransferDetail.setBillNo(storageRepairOrder.getBillNo());
                    storageTransferDetail.setActualQty(BigDecimal.ONE); // 默认数量为1--维修货物在维修单表头中就是一个！！
                    storageTransferDetail.setDetailType("1"); // 默认整机
                    storageTransferDetail.setQunit(storageRepairOrder.getQunit());
                    storageTransferDetail.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "表头维修货物「开始检验」操作自动生成");
                    storageTransferDetailList.add(storageTransferDetail);
                    storageTransfer.setStorageTransferDetailList(storageTransferDetailList);
                    Result<?> saveTransferResult = storageTransferService.saveStorageTransfer(storageTransfer);
                    if (!saveTransferResult.isSuccess()) {
                        throw new RuntimeException(saveTransferResult.getMessage());
                    } else {
                        log.info("维修单{}「开始检验」操作自动生成调拨单成功！：{}", storageRepairOrder.getRepairNo(), saveTransferResult.getResult());
                    }
                    /*
                     * ②关闭：录入原因。维修单状态关闭。自动产生一个调拨单（从维修货物库区到待维修库区的一个调拨单），
                     * 调拨单储运人员确认后，自动移库，维修货物进入待维修库区。（库区变化，相应库存也会变化）
                     * 2024/8/27 14:26@ZHANGCHAO
                     */
                } else if ("3".equals(status)) {
                    /*
                     * ②关闭：录入原因。维修单状态关闭。
      a.如果上一步（开始检验）产生的调拨单还未确认，则直接删除此调拨单。
      b.如果上一步（开始检验）产生的调拨单已确认（即已经完成调拨），则自动产生一个再调回去的调拨单（从维修货物库区到待维修库区的一个调拨单），调拨单储运人员确认后，自动移库。（库区变化，相应库存也会变化）
                     * 2024/9/12 09:00@ZHANGCHAO
                     */
                    List<StorageTransfer> storageTransferList = storageTransferMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                            .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo())
                            .eq(StorageTransfer::getStep, "1"));
                    if (isNotEmpty(storageTransferList) && !"1".equals(storageTransferList.get(0).getStatus())) {
                        storageTransferService.removeBatchByIds(storageTransferList.stream().map(StorageTransfer::getId).collect(Collectors.toList()));
                    } else {
                        // 查询货主维修货物库区的第一个储位
                        StoreSpace storeSpace_WXHW = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "4"); // 4 = 维修货物库区
                        if (isEmpty(storeSpace_WXHW)) {
                            throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无维修货物库区或没有相应储位，请先配置！");
                        }
                        // 查询货主待维修区的第一个储位
                        StoreSpace storeSpace_DWX = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "2"); // 2 = 待维修库区
                        if (isEmpty(storeSpace_DWX)) {
                            throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无待维修库区或没有相应储位，请先配置！");
                        }
                        StorageTransfer storageTransfer = new StorageTransfer();
                        storageTransfer.setStep("2"); // 2检验后关闭不维修
                        storageTransfer.setRelRepairNo(storageRepairOrder.getRepairNo());
                        storageTransfer.setStoreCode(storageRepairOrder.getStoreCode());
                        storageTransfer.setCustomer(storageRepairOrder.getCustomer());
                        storageTransfer.setAreaCodeBefore(storeSpace_WXHW.getAreaCode()); // 调拨前库区编码 - 维修货物区的第一个储位默认
                        storageTransfer.setAreaNameBefore(storeSpace_WXHW.getAreaName()); // 调拨前库区名称 - 维修货物区的第一个储位默认
                        storageTransfer.setAreaCodeAfter(storeSpace_DWX.getAreaCode()); // 调拨后库区编码 - 待维修库区的第一个储位默认
                        storageTransfer.setAreaNameAfter(storeSpace_DWX.getAreaName()); // 调拨后库区名称 - 待维修库区的第一个储位默认
                        storageTransfer.setAppDate(new Date());
                        storageTransfer.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "「检验后关闭不维修」操作自动生成");
                        storageTransfer.setCreateBy(loginUser.getUsername());
                        storageTransfer.setCreateDate(new Date());
                        List<StorageTransferDetail> storageTransferDetailList = new ArrayList<>();
                        StorageTransferDetail storageTransferDetail = new StorageTransferDetail();
                        storageTransferDetail.setSpaceCodeBefore(storeSpace_WXHW.getCabinCode()); // 调拨前储位编码 - 维修货物区的第一个储位默认
                        storageTransferDetail.setSpaceNameBefore(storeSpace_WXHW.getSpaceCode()); // 调拨前储位名称 - 维修货物区的第一个储位默认
                        storageTransferDetail.setSpaceCodeAfter(storeSpace_DWX.getCabinCode()); // 调拨后储位编码 - 待维修库区的第一个储位默认
                        storageTransferDetail.setSpaceNameAfter(storeSpace_DWX.getSpaceCode()); // 调拨后储位名称 - 待维修库区的第一个储位默认
                        storageTransferDetail.setItemNumber(storageRepairOrder.getItemNumber());
                        storageTransferDetail.setBatchNo(storageRepairOrder.getBatchNo());
                        storageTransferDetail.setCopGno(storageRepairOrder.getCopGno());
                        storageTransferDetail.setPn(storageRepairOrder.getPn());
                        storageTransferDetail.setSn(storageRepairOrder.getSn());
                        storageTransferDetail.setBillNo(storageRepairOrder.getBillNo());
                        storageTransferDetail.setActualQty(BigDecimal.ONE); // 默认数量为1--维修货物在维修单表头中就是一个！！
                        storageTransferDetail.setDetailType("1"); // 默认整机
                        storageTransferDetail.setQunit(storageRepairOrder.getQunit());
                        storageTransferDetail.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "表头维修货物「检验后关闭不维修」操作自动生成");
                        storageTransferDetailList.add(storageTransferDetail);
                        storageTransfer.setStorageTransferDetailList(storageTransferDetailList);
                        Result<?> saveTransferResult = storageTransferService.saveStorageTransfer(storageTransfer);
                        if (!saveTransferResult.isSuccess()) {
                            throw new RuntimeException(saveTransferResult.getMessage());
                        } else {
                            log.info("维修单{}「检验后关闭不维修」操作自动生成调拨单成功！：{}", storageRepairOrder.getRepairNo(), saveTransferResult.getResult());
                        }
                    }
                    /*
                     * ①维修单状态变更为已修复，维修用料件占用---维修用料件根据维修单所有维修用料件来挂钩计算。
                     * 自动产生一个调拨单（从待维修区到已修复区的一个调拨单），调拨单储运人员确认后，自动移库。
                     * （库区变化，相应库存也会变化）
                     * ②自动拆下件入库，拆下件进入库区-拆下件区。（拆下件库区库存变化）
                     * 2024/8/27 14:50@ZHANGCHAO
                     */
                } else if ("2".equals(status)) {
                    // 2024/9/23 22:48@ZHANGCHAO 追加/变更/完善：查第一步的调拨单，获取调拨前的库区和储位！！
                    List<StorageTransfer> storageTransferList = storageTransferMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                            .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo())
                            .eq(StorageTransfer::getStep, "1"));
                    StorageTransferDetail std_one = null;
                    if (isNotEmpty(storageTransferList)) {
                        List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailMapper.selectList(new LambdaQueryWrapper<StorageTransferDetail>()
                                .eq(StorageTransferDetail::getTransferNo, storageTransferList.get(0).getTransferNo()));
                        if (isNotEmpty(storageTransferDetailList)) {
                            std_one = storageTransferDetailList.get(0);
                        }
                    }
                    StorageTransfer st_one = isNotEmpty(storageTransferList) ? storageTransferList.get(0) : null;
                    // 查询货主维修货物区的第一个储位
                    StoreSpace storeSpace_WXHW = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "4"); // 4 = 维修货物库区
                    if (isEmpty(storeSpace_WXHW)) {
                        throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无维修货物库区或没有相应储位，请先配置！");
                    }
                    // 查询货主已修复库区的第一个储位
                    StoreSpace storeSpace_YXF = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "5"); // 5 = 已修复区
                    if (isEmpty(storeSpace_YXF)) {
                        throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无已修复库区或没有相应储位，请先配置！");
                    }
                    StorageTransfer storageTransfer = new StorageTransfer();
                    storageTransfer.setStep("3"); // 3维修完成
                    storageTransfer.setRelRepairNo(storageRepairOrder.getRepairNo());
                    storageTransfer.setStoreCode(storageRepairOrder.getStoreCode());
                    storageTransfer.setCustomer(storageRepairOrder.getCustomer());
                    storageTransfer.setAreaCodeBefore(isNotEmpty(st_one) ? st_one.getAreaCodeAfter() : storeSpace_WXHW.getAreaCode()); // 调拨前库区编码 - 维修货物区的第一个储位默认
                    storageTransfer.setAreaNameBefore(isNotEmpty(st_one) ? st_one.getAreaNameAfter() : storeSpace_WXHW.getAreaName()); // 调拨前库区名称 - 维修货物区的第一个储位默认
                    storageTransfer.setAreaCodeAfter(storeSpace_YXF.getAreaCode()); // 调拨后库区编码 - 已修复库区的第一个储位默认
                    storageTransfer.setAreaNameAfter(storeSpace_YXF.getAreaName()); // 调拨后库区名称 - 已修复库区的第一个储位默认
                    storageTransfer.setAppDate(new Date());
                    storageTransfer.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "「维修完成」操作自动生成");
                    storageTransfer.setCreateBy(loginUser.getUsername());
                    storageTransfer.setCreateDate(new Date());
                    List<StorageTransferDetail> storageTransferDetailList = new ArrayList<>();
                    StorageTransferDetail storageTransferDetail = new StorageTransferDetail();
                    storageTransferDetail.setSpaceCodeBefore(isNotEmpty(std_one) ? std_one.getSpaceCodeAfter() : storeSpace_WXHW.getCabinCode()); // 调拨前储位编码 - 维修货物区的第一个储位默认
                    storageTransferDetail.setSpaceNameBefore(isNotEmpty(std_one) ? std_one.getSpaceNameAfter() : storeSpace_WXHW.getSpaceCode()); // 调拨前储位名称 - 维修货物区的第一个储位默认
                    storageTransferDetail.setSpaceCodeAfter(storeSpace_YXF.getCabinCode()); // 调拨后储位编码 - 已修复库区的第一个储位默认
                    storageTransferDetail.setSpaceNameAfter(storeSpace_YXF.getSpaceCode()); // 调拨后储位名称 - 已修复库区的第一个储位默认
                    storageTransferDetail.setItemNumber(storageRepairOrder.getItemNumber());
                    storageTransferDetail.setBatchNo(storageRepairOrder.getBatchNo());
                    storageTransferDetail.setBatchNoNew(batchNoNew); // 2024/9/14 11:46@ZHANGCHAO 追加/变更/完善：维修完成时可能会修改批次号！！
                    storageTransferDetail.setCopGno(storageRepairOrder.getCopGno());
                    storageTransferDetail.setPn(storageRepairOrder.getPn());
                    storageTransferDetail.setSn(storageRepairOrder.getSn());
                    storageTransferDetail.setBillNo(storageRepairOrder.getBillNo());
                    storageTransferDetail.setActualQty(BigDecimal.ONE); // 默认数量为1--维修货物在维修单表头中就是一个！！
                    storageTransferDetail.setDetailType("1"); // 默认整机
                    storageTransferDetail.setQunit(storageRepairOrder.getQunit());
                    storageTransferDetail.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "表头维修货物「维修完成」操作自动生成");
                    storageTransferDetailList.add(storageTransferDetail);
                    storageTransfer.setStorageTransferDetailList(storageTransferDetailList);
                    Result<?> saveTransferResult = storageTransferService.saveStorageTransfer(storageTransfer);
                    if (!saveTransferResult.isSuccess()) {
                        throw new RuntimeException(saveTransferResult.getMessage());
                    } else {
                        log.info("维修单{}「维修完成」操作自动生成调拨单成功！：{}", storageRepairOrder.getRepairNo(), saveTransferResult.getResult());
                    }
                    // 2. 维修用料件占用!!
                    List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                            .eq(StorageRepairOrderDetail::getRepairNo, storageRepairOrder.getRepairNo()));
//                    // 查询货主拆下件区的第一个储位
//                    StoreSpace storeSpace_JJ = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "3"); // 3 = 拆下件区
//                    if (isEmpty(storeSpace_JJ)) {
//                        throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无拆下件库区或没有相应储位，请先配置！");
//                    }
                    if (isNotEmpty(storageRepairOrderDetailList)) {
                        // 查询货主拆下件区的第一个储位
                        StoreSpace storeSpace_JJ = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "3"); // 3 = 拆下件区
                        if (isEmpty(storeSpace_JJ)) {
                            throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无拆下件库区或没有相应储位，请先配置！");
                        }
                        for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrderDetailList) {
                            StorageDetail storageDetail = new StorageDetail();
                            BeanUtil.copyProperties(storageRepairOrderDetail, storageDetail, CopyOptions.create().ignoreNullValue());
                            storageDetail.setCustomer(storageRepairOrder.getCustomer());
                            storageDetail.setStoreCode(storageRepairOrder.getStoreCode());
                            storageDetail.setIeFlag("E");
                            storageDetail.setStorageNo(storageRepairOrder.getRepairNo());
                            storageDetail.setActualQty(storageRepairOrderDetail.getQty());
                            storageDetail.setDetailType("2"); // 2维修用保税料件
                            Result<?> addOccupyHandleResult = storeStocksService.addOccupyHandle(storageDetail);
                            if (addOccupyHandleResult.isSuccess()) {
                                storageRepairOrderDetailService.update(null, new UpdateWrapper<StorageRepairOrderDetail>().lambda()
                                        .set(StorageRepairOrderDetail::getStocksFlowId, addOccupyHandleResult.getResult())
                                        .eq(StorageRepairOrderDetail::getId, storageDetail.getId()));
                            } else {
                                throw new RuntimeException(addOccupyHandleResult.getMessage());
                            }
                        }
                        // 3. ②自动拆下件入库，拆下件进入库区-拆下件区。（拆下件库区库存变化）
                        StorageInfo storageInfo = new StorageInfo();
                        storageInfo.setRelRepairNos(storageRepairOrder.getRepairNo());
                        storageInfo.setType("1");
                        storageInfo.setIeFlag("I");
                        storageInfo.setStoreCode(storageRepairOrder.getStoreCode());
                        storageInfo.setCustomer(storageRepairOrder.getCustomer());
                        storageInfo.setRemark(DateUtil.now() + "由维修单" + storageRepairOrder.getRepairNo() + "自动生成拆下件/旧件入库单");
                        List<StorageDetail> storageDetailList = new ArrayList<>();
                        for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrderDetailList) {
                            StorageDetail storageDetail = new StorageDetail();
                            BeanUtil.copyProperties(storageRepairOrderDetail, storageDetail, CopyOptions.create().ignoreNullValue());
                            storageDetail.setId(null);
                            // 2024/9/3 16:00@ZHANGCHAO 追加/变更/完善：生成旧件入库时，品名去掉”新“字
                            if (storageRepairOrderDetail.getPn().trim().endsWith("新")) {
                                String pn = storageRepairOrderDetail.getPn();
                                int index = pn.lastIndexOf("新");
                                if (index != -1) {
                                    pn = pn.substring(0, index) + pn.substring(index + "新".length());
                                }
                                storageDetail.setPn(pn);
                            }
                            storageDetail.setActualQty(storageRepairOrderDetail.getQty());
                            storageDetail.setDetailType("3"); // 3旧件/坏件/拆下件
                            storageDetail.setRepairListId(storageRepairOrderDetail.getId());
                            storageDetail.setAreaCode(storeSpace_JJ.getAreaName());
                            storageDetail.setAreaName(storeSpace_JJ.getAreaCode());
                            storageDetail.setSpaceCode(storeSpace_JJ.getCabinCode());
                            storageDetail.setSpaceName(storeSpace_JJ.getSpaceCode());
                            storageDetailList.add(storageDetail);
                        }
                        storageInfo.setStorageDetailList(storageDetailList);
                        Result<?> saveStorageInfoResult = storageInfoService.saveStorageInfo(storageInfo);
                        if (!saveStorageInfoResult.isSuccess()) {
                            throw new RuntimeException(saveStorageInfoResult.getMessage());
                        } else {
                            log.info("维修单{}自动生成旧件/拆下件入库单成功！：{}", storageRepairOrder.getRepairNo(), saveStorageInfoResult.getResult());
                        }
                    } else {
                        log.info("此维修单无表体数据：{}", storageRepairOrder.getRepairNo());
                    }
                } else {
                    throw new RuntimeException("未知操作！");
                }
            }
        }
        baseMapper.update(null, new LambdaUpdateWrapper<StorageRepairOrder>()
                .set(StorageRepairOrder::getStatus, status)
                .set(isNotBlank(closeReason), StorageRepairOrder::getCloseReason, closeReason)
                .set(isNotBlank(sn), StorageRepairOrder::getSn, sn)
                .set(isNotBlank(billNo), StorageRepairOrder::getBillNo, billNo)
                .set(isNotBlank(batchNoNew), StorageRepairOrder::getBatchNoNew, batchNoNew)
                .in(StorageRepairOrder::getId, Arrays.asList(ids.split(","))));
        return Result.ok("操作成功！");
    }

    /**
     * 工单回退
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/11 08:38
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleSubmitBack(String ids) {
        List<StorageRepairOrder> storageRepairOrderList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(storageRepairOrderList)) {
            return Result.error("未获取到维修工单数据！");
        }
        // 需已修复的才能操作
        boolean isSubmit = storageRepairOrderList.stream().allMatch(i -> "2".equals(i.getStatus()));
        if (!isSubmit) {
            return Result.error("请选择已修复的维修工单！");
        }
        List<String> repairNos = storageRepairOrderList.stream().map(StorageRepairOrder::getRepairNo).collect(Collectors.toList());
        List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
                .in(StorageRepairOrderDetail::getRepairNo, repairNos));
        Map<String, List<StorageRepairOrderDetail>> storageRepairOrderDetailMap = new HashMap<>();
        if (isNotEmpty(storageRepairOrderDetailList)) {
            storageRepairOrderDetailMap = storageRepairOrderDetailList.stream().collect(Collectors.groupingBy(StorageRepairOrderDetail::getRepairNo));
        }
        StringBuilder sb = new StringBuilder();
        Set<String> successIds = new HashSet<>(16);
        Set<String> errorIds = new HashSet<>(16);
        StorageRepairOrderServiceImpl currentProxy = (StorageRepairOrderServiceImpl) AopContext.currentProxy();
        for (StorageRepairOrder storageRepairOrder : storageRepairOrderList) {
            try {
                Result<?> result = currentProxy.processRepairOrderOne(storageRepairOrder, storageRepairOrderDetailMap);
                if (result.isSuccess()) {
                    log.info("[工单回退]处理结果：{}", result.getMessage());
                    successIds.add(storageRepairOrder.getRepairNo());
                } else {
                    throw new RuntimeException(result.getMessage());
                }
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("[工单回退] 工单回退出现异常，原因：{}", e.getMessage());
                errorIds.add(storageRepairOrder.getRepairNo());
                sb.append("[").append(storageRepairOrder.getRepairNo()).append("]工单回退出现异常：").append(e.getMessage()).append(";|");
            }
        }
        String msg = "共" + ids.split(",").length + "票，成功数："
                + successIds.size() + (isNotEmpty(errorIds) ? ("，失败数："
                + errorIds.size() + "，原因：" + sb) : "");
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 处理单个的工单回退
     *
     * @param storageRepairOrder
     * @param storageRepairOrderDetailMap
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/11 09:10
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<?> processRepairOrderOne(StorageRepairOrder storageRepairOrder, Map<String, List<StorageRepairOrderDetail>> storageRepairOrderDetailMap) {
        try {
            // 1.需要验证是否已经生成出库单。已经生成的不允许回退。
            List<StorageInfo> storageInfoListE = storageInfoService.list(new LambdaQueryWrapper<StorageInfo>()
                    .like(StorageInfo::getRelRepairNos, storageRepairOrder.getRepairNo())
                    .eq(StorageInfo::getIeFlag, "E"));
            if (isNotEmpty(storageInfoListE)) {
                throw new RuntimeException("该工单已出库，无法回退，请先处理出库单！");
            }
            List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailMap.get(storageRepairOrder.getRepairNo());
            // 2.是否有表体，是否使用了维修料件，有则处理自动拆下件入库单取消入库（库存数量-），维修用料件取消占用（占用数量-）
            if (isNotEmpty(storageRepairOrderDetailList)) {
                log.info("[工单回退]工单回退，【{}】存在维修料件，去处理旧件取消入库和维修料件取消占用！", storageRepairOrder.getRepairNo());
                ////////////////////////处理旧件入库回滚START//////////////////////////
                // 1.取旧件入库单
                StorageInfo storageInfo = storageInfoService.getOne(new LambdaQueryWrapper<StorageInfo>()
                        .like(StorageInfo::getRelRepairNos, storageRepairOrder.getRepairNo())
//                        .eq(StorageInfo::getStatus, "2") // 已入库的
                        .eq(StorageInfo::getType, "1")
                        .eq(StorageInfo::getIeFlag, I));
                // 2.存在旧件入库单
                if (isNotEmpty(storageInfo)) {
                    // 已入库，则处理库存，再删
                    if ("2".equals(storageInfo.getStatus())) {
                        List<StorageDetail> storageDetailList = storageDetailMapper.selectList(new LambdaQueryWrapper<StorageDetail>()
                                .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
                        if (isNotEmpty(storageDetailList)) {
                            storageDetailList.forEach(storageDetail -> {
                                storageDetail.setIeFlag(I);
                                storageDetail.setCustomer(storageInfo.getCustomer());
                                storageDetail.setStockTypeEnum(StockTypeEnum.GOODS_ADD); // GOODS_ADD和GOODS_INCR是同一个
                                // 冲正[记账和核增]处理器
                                Result<?> addStockHandleResult = storeStocksService.rectify(storageDetail);
                                if (addStockHandleResult.isSuccess()) {
                                    log.info(addStockHandleResult.getMessage());
                                } else {
                                    throw new RuntimeException(addStockHandleResult.getMessage());
                                }
                            });
                        }
                    }
                    // 直接删
                    storageInfoService.removeById(storageInfo.getId());
                    storageDetailMapper.delete(new LambdaQueryWrapper<StorageDetail>()
                            .eq(StorageDetail::getStorageNo, storageInfo.getStorageNo()));
                } else {
                    log.info("旧件入库单不存在，或者还没有提交入库，不需要处理！");
                }
                ////////////////////////处理旧件入库回滚END//////////////////////////
                ////////////////////////处理维修用料件占用回滚START//////////////////////////
                for (StorageRepairOrderDetail storageRepairOrderDetail : storageRepairOrderDetailList) {
                    StorageDetail storageDetail = new StorageDetail();
                    BeanUtil.copyProperties(storageRepairOrderDetail, storageDetail, CopyOptions.create().ignoreNullValue());
                    storageDetail.setCustomer(storageRepairOrder.getCustomer());
                    storageDetail.setStoreCode(storageRepairOrder.getStoreCode());
                    storageDetail.setIeFlag(E);
                    storageDetail.setStorageNo(storageRepairOrder.getRepairNo());
                    storageDetail.setActualQty(storageRepairOrderDetail.getQty());
                    storageDetail.setDetailType("2"); // 2维修用保税料件
                    Result<?> addOccupyHandleResult = storeStocksService.deOccupyHandle(storageDetail);
                    if (addOccupyHandleResult.isSuccess()) {
                        storageRepairOrderDetailService.update(null, new UpdateWrapper<StorageRepairOrderDetail>().lambda()
                                .set(StorageRepairOrderDetail::getStocksFlowId, null)
                                .eq(StorageRepairOrderDetail::getId, storageDetail.getId()));
                    } else {
                        throw new RuntimeException(addOccupyHandleResult.getMessage());
                    }
                }
                ////////////////////////处理维修用料件占用回滚END//////////////////////////
            } else {
                log.info("[工单回退]工单回退，此工单【{}】无使用维修料件，无需处理旧件取消入库和维修料件取消占用！", storageRepairOrder.getRepairNo());
            }
            // 3.调拨单处理
            // 调拨单step3：3已修复库区 -> 2维修货物库区（3库存数量-  2库存数量+）
            StorageTransfer storageTransfer = storageTransferMapper.selectOne(new LambdaQueryWrapper<StorageTransfer>()
                    .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo())
                    .eq(StorageTransfer::getStep, "3"));
            if (isNotEmpty(storageTransfer)) {
                // 已确认，则处理库存，再删
                if ("1".equals(storageTransfer.getStatus())) {
                    List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailMapper.selectList(new LambdaQueryWrapper<StorageTransferDetail>()
                            .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
                    if (isNotEmpty(storageTransferDetailList)) {
                        for (StorageTransferDetail storageTransferDetail : storageTransferDetailList) {
                            log.info("[工单回退]调拨单step3[{}]商品：维修货物：[{}-{}-{}-{}] => 调拨前库区储位：[{}-{}] => 调拨后库区储位：[{}-{}]",
                                    storageTransfer.getTransferNo(), storageTransferDetail.getCopGno(),
                                    storageTransferDetail.getItemNumber(), storageTransferDetail.getBatchNo(),
                                    storageTransferDetail.getPn(), storageTransferDetail.getAreaCodeAfter(),
                                    storageTransferDetail.getSpaceCodeAfter(), storageTransferDetail.getAreaCodeBefore(),
                                    storageTransferDetail.getSpaceCodeBefore());

                            // 处理库存的减少与增加
                            handleStockChangeRectify(storageTransfer, storageTransferDetail, true); // ---
                            handleStockChangeRectify(storageTransfer, storageTransferDetail, false); // +++
                        }
                    }
                }
                storageTransferMapper.deleteById(storageTransfer.getId());
                storageTransferDetailMapper.delete(new LambdaQueryWrapper<StorageTransferDetail>()
                        .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
            }
            // 调拨单step1：2维修货物库区 -> 1待维修库区（2库存数量-  1库存数量+）
            StorageTransfer storageTransfer1 = storageTransferMapper.selectOne(new LambdaQueryWrapper<StorageTransfer>()
                    .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo())
                    .eq(StorageTransfer::getStep, "1"));
            if (isNotEmpty(storageTransfer1)) {
                // 已确认，则处理库存，再删
                if ("1".equals(storageTransfer1.getStatus())) {
                    List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailMapper.selectList(new LambdaQueryWrapper<StorageTransferDetail>()
                            .eq(StorageTransferDetail::getTransferNo, storageTransfer1.getTransferNo()));
                    if (isNotEmpty(storageTransferDetailList)) {
                        for (StorageTransferDetail storageTransferDetail : storageTransferDetailList) {
                            log.info("[工单回退]调拨单step1[{}]商品：维修货物：[{}-{}-{}-{}] => 调拨前库区储位：[{}-{}] => 调拨后库区储位：[{}-{}]",
                                    storageTransfer1.getTransferNo(), storageTransferDetail.getCopGno(),
                                    storageTransferDetail.getItemNumber(), storageTransferDetail.getBatchNo(),
                                    storageTransferDetail.getPn(), storageTransferDetail.getAreaCodeAfter(),
                                    storageTransferDetail.getSpaceCodeAfter(), storageTransferDetail.getAreaCodeBefore(),
                                    storageTransferDetail.getSpaceCodeBefore());

                            // 处理库存的减少与增加
                            handleStockChangeRectify(storageTransfer1, storageTransferDetail, true); // ---
                            handleStockChangeRectify(storageTransfer1, storageTransferDetail, false); // +++
                        }
                    }
                }
                storageTransferMapper.deleteById(storageTransfer1.getId());
                storageTransferDetailMapper.delete(new LambdaQueryWrapper<StorageTransferDetail>()
                        .eq(StorageTransferDetail::getTransferNo, storageTransfer1.getTransferNo()));
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("[工单回退]工单回退出现异常，原因：{}", e.getMessage());
            throw new RuntimeException("[工单回退]工单回退出现异常：" + e.getMessage());
        }
        // 都成功后，更新状态
        baseMapper.update(null, new LambdaUpdateWrapper<StorageRepairOrder>()
                .set(StorageRepairOrder::getStatus, "0") // 0 提交待检验
                .eq(StorageRepairOrder::getId, storageRepairOrder.getId()));
        return Result.ok("操作成功！");
    }

    /**
     * 处理库存增减
     *
     * @param storageTransfer
     * @param storageTransferDetail
     * @param isReduce
     * @return void
     * <AUTHOR>
     * @date 2024/8/27 09:36
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleStockChangeRectify(StorageTransfer storageTransfer, StorageTransferDetail storageTransferDetail, boolean isReduce) {
        StorageDetail storageDetail = new StorageDetail();
        BeanUtil.copyProperties(storageTransferDetail, storageDetail, CopyOptions.create().ignoreNullValue());
        // 2024/9/14 11:50@ZHANGCHAO 追加/变更/完善：针对维修完成的调拨单，批次号可能会修改！！
        if (isNotBlank(storageTransferDetail.getBatchNoNew())) {
            storageDetail.setBatchNo(isReduce ? storageTransferDetail.getBatchNo() : storageTransferDetail.getBatchNoNew());
        }
        storageDetail.setIeFlag(isReduce ? E : I);
        storageDetail.setStockTypeEnum(isReduce ? StockTypeEnum.GOODS_REDUCE : StockTypeEnum.GOODS_ADD);
        storageDetail.setCustomer(storageTransfer.getCustomer());
        storageDetail.setSpaceName(isReduce ? storageTransferDetail.getSpaceNameBefore() : storageTransferDetail.getSpaceNameAfter());
        storageDetail.setSpaceCode(isReduce ? storageTransferDetail.getSpaceCodeBefore() : storageTransferDetail.getSpaceCodeAfter());
        storageDetail.setAreaCode(isReduce ? storageTransferDetail.getAreaNameBefore() : storageTransferDetail.getAreaNameAfter());
        storageDetail.setAreaName(isReduce ? storageTransferDetail.getAreaCodeBefore() : storageTransferDetail.getAreaCodeAfter());
        storageDetail.setStorageNo(storageTransferDetail.getTransferNo());

        Result<?> stockHandleResult = storeStocksService.rectify(storageDetail); // 冲正
        if (!stockHandleResult.isSuccess()) {
            throw new RuntimeException(stockHandleResult.getMessage());
        }
    }

//    /**
//     * 处理库存增减
//     *
//     * @param storageTransfer
//     * @param storageTransferDetail
//     * @param isReduce
//     * @return void
//     * <AUTHOR>
//     * @date 2024/8/27 09:36
//     */
//    private void handleStockChangeRectify(StorageTransfer storageTransfer, StorageTransferDetail storageTransferDetail, boolean isReduce) {
//        StorageDetail storageDetail = new StorageDetail();
//        BeanUtil.copyProperties(storageTransferDetail, storageDetail, CopyOptions.create().ignoreNullValue());
//        // 2024/9/14 11:50@ZHANGCHAO 追加/变更/完善：针对维修完成的调拨单，批次号可能会修改！！
//        if (isNotBlank(storageTransferDetail.getBatchNoNew())) {
//            storageDetail.setBatchNo(isReduce ? storageTransferDetail.getBatchNoNew() : storageTransferDetail.getBatchNo());
//        }
//        storageDetail.setIeFlag(isReduce ? E : I);
//        storageDetail.setCustomer(storageTransfer.getCustomer());
//        storageDetail.setSpaceName(isReduce ? storageTransferDetail.getSpaceNameAfter() : storageTransferDetail.getSpaceNameBefore());
//        storageDetail.setSpaceCode(isReduce ? storageTransferDetail.getSpaceCodeAfter() : storageTransferDetail.getSpaceCodeBefore());
//        storageDetail.setAreaCode(isReduce ? storageTransferDetail.getAreaNameAfter() : storageTransferDetail.getAreaNameBefore());
//        storageDetail.setAreaName(isReduce ? storageTransferDetail.getAreaCodeAfter() : storageTransferDetail.getAreaCodeBefore());
//        storageDetail.setStorageNo(storageTransferDetail.getTransferNo());
//        storageDetail.setRemark("[工单回退]操作");
//
//        Result<?> stockHandleResult = isReduce ? storeStocksService.reduceStockHandle(storageDetail) : storeStocksService.addStockHandle(storageDetail);
//        if (!stockHandleResult.isSuccess()) {
//            throw new RuntimeException(stockHandleResult.getMessage());
//        }
//    }

    /**
     * 维修单生成出库单查询转换数据
     *
     * @param ids
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/13 上午10:39
     */
    @Override
    public Result<?> handleRepairCreateOutStorage(String ids, String type) {

        List<StorageRepairOrder> storageRepairOrderList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(storageRepairOrderList)) {
            return Result.error("未获取到维修单数据！");
        }

        storageRepairOrderList.forEach(storageRepairOrder -> {
            List<StorageTransfer> storageTransferList = storageTransferMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                    .eq(StorageTransfer::getRelRepairNo, storageRepairOrder.getRepairNo()));
            if (isEmpty(storageTransferList)) {
                throw new RuntimeException("维修单["+storageRepairOrder.getRepairNo()+"]不存在调拨单，无法生成出库单！");
            }
            boolean isAllConfirm = storageTransferList.stream().allMatch(storageTransfer -> "1".equals(storageTransfer.getStatus()));
            if (!isAllConfirm) {
                throw new RuntimeException("维修单["+storageRepairOrder.getRepairNo()+"]的调拨单未确认，无法生成出库单！");
            }
        });

        StorageInfo storageInfo = new StorageInfo();
        storageInfo.setIsRepairedOut("1"); // 是否已修复出库！是的！！
        storageInfo.setType(type);
        storageInfo.setCustomer(storageRepairOrderList.get(0).getCustomer());
        storageInfo.setStoreCode(storageRepairOrderList.get(0).getStoreCode());
        storageInfo.setRelRepairNos(storageRepairOrderList.stream().map(StorageRepairOrder::getRepairNo).collect(Collectors.joining(",")));
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        StoreInfo storeInfo = storeInfoMapper.selectOne(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getStoreCode, storageRepairOrderList.get(0).getStoreCode()));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (isNotEmpty(storeInfo)) {
            storageInfo.setStoreName(storeInfo.getStoreName());
        }
        // 2024/8/27 16:50@ZHANGCHAO 追加/变更/完善：商品是维修货物即整机！！！！！
        List<StorageDetail> storageDetailList = new ArrayList<>();
        for (StorageRepairOrder storageRepairOrder : storageRepairOrderList) {
            StorageDetail storageDetail = new StorageDetail();
            storageDetail.setStorageNo(storageInfo.getStorageNo());
            storageDetail.setStoreCode(storageInfo.getStoreCode());
            storageDetail.setItemNumber(storageRepairOrder.getItemNumber());
            // Fix 20240920 新批次号换了以后，导致库存查询有问题!!
            storageDetail.setBatchNo(isNotBlank(storageRepairOrder.getBatchNoNew()) ? storageRepairOrder.getBatchNoNew() : storageRepairOrder.getBatchNo());
            storageDetail.setCopGno(storageRepairOrder.getCopGno()); // 物料号
            storageDetail.setPn(storageRepairOrder.getPn()); // 中文品名
            storageDetail.setActualQty(BigDecimal.ONE); // 默认1
            storageDetail.setRepairListId(storageRepairOrder.getId());
            storageDetail.setQunit(storageRepairOrder.getQunit()); // 单位
            storageDetail.setDetailType("1");
            storageDetail.setSn(storageRepairOrder.getSn());
            storageDetail.setBillNo(storageRepairOrder.getBillNo());
//            storageDetail.setAreaCode(storageRepairOrder.getAreaCode());
//            storageDetail.setAreaName(storageRepairOrder.getAreaName());
//            storageDetail.setSpaceCode(storageRepairOrder.getSpaceCode());
//            storageDetail.setSpaceName(storageRepairOrder.getSpaceName());
            storageDetail.setCustomer(storageInfo.getCustomer());
            // 查询维修单对应的已修复的调拨单的数据
            StorageTransferDetail storageTransferDetail = storageTransferMapper.getTransferDetailByRepair(storageRepairOrder.getRepairNo());
            if (isEmpty(storageTransferDetail)) {
                throw new RuntimeException("维修单["+storageRepairOrder.getRepairNo()+"]不存在已修复的调拨单，无法生成出库单！");
            }
            storageDetail.setAreaCode(storageTransferDetail.getAreaCodeAfter());
            storageDetail.setAreaName(storageTransferDetail.getAreaNameAfter());
            storageDetail.setSpaceCode(storageTransferDetail.getSpaceCodeAfter());
            storageDetail.setSpaceName(storageTransferDetail.getSpaceNameAfter());
            Result<StoreStocks> result = storeStocksService.getStockByDetail(storageDetail);
            StoreStocks storeStocks = null;
            if (result.isSuccess()) {
                storeStocks = result.getResult();
            }
            if (isNotEmpty(storeStocks)) {
                BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                storageDetail.setStockQty(beginQty); // 实际库存数量
                storageDetail.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
            }
            storageDetailList.add(storageDetail);
        }
//        // 处理商品
//        List<StorageRepairOrderDetail> storageRepairOrderDetailList = storageRepairOrderDetailService.list(new LambdaQueryWrapper<StorageRepairOrderDetail>()
//                .in(StorageRepairOrderDetail::getHeadId, Arrays.asList(ids.split(","))));
//        List<StorageDetail> storageDetailList = new ArrayList<>();
//        if (isNotEmpty(storageRepairOrderDetailList)) {
//            for (StorageRepairOrderDetail repairOrderDetail : storageRepairOrderDetailList) {
//                StorageDetail storageDetail = new StorageDetail();
//                storageDetail.setStorageNo(storageInfo.getStorageNo());
//                storageDetail.setStoreCode(storageInfo.getStoreCode());
//                storageDetail.setCopGno(repairOrderDetail.getCopGno()); // 物料号
//                storageDetail.setPn(repairOrderDetail.getPn()); // 中文品名
//                storageDetail.setModel(repairOrderDetail.getModel()); // 规格型号
//                storageDetail.setActualQty(repairOrderDetail.getQty()); // 出库数量
//                storageDetail.setQunit(repairOrderDetail.getQunit()); // 单位
//                storageDetail.setBatchNo(repairOrderDetail.getBatchNo());
//                storageDetail.setDetailType(repairOrderDetail.getDetailType());
//                storageDetail.setRepairListId(repairOrderDetail.getId());
//                repairOrderDetail.setCustomer(storageInfo.getCustomer());
//                Result<StoreStocks> result = storeStocksService.getStockByRepairDetail(repairOrderDetail);
//                StoreStocks storeStocks = null;
//                if (result.isSuccess()) {
//                    storeStocks = result.getResult();
//                }
//                if (isNotEmpty(storeStocks)) {
//                    BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
//                    BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
//                    storageDetail.setStockQty(beginQty); // 实际库存数量
//                    storageDetail.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
//                }
//                storageDetailList.add(storageDetail);
//            }
//        }
        storageInfo.setStorageDetailList(storageDetailList);
        return Result.ok(storageInfo);
    }

    /**
     * 维修单审核
     *
     * @param ids
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/12 上午11:02
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleAudits(String ids, String type) {
        // 是否审核
//        AtomicBoolean isAudits = new AtomicBoolean("1".equals(type));
        for (String id : ids.split(",")) {
            StorageRepairOrder storageRepairOrder = baseMapper.selectById(id);
            if (isEmpty(storageRepairOrder)) {
                continue;
            }
            baseMapper.update(null, new LambdaUpdateWrapper<StorageRepairOrder>()
                    .set(StorageRepairOrder::getAudits, "1".equals(type) ? "1" : "0") // 1
                    .eq(StorageRepairOrder::getId, id));
        }
        return Result.ok("操作成功！");
    }

    /**
     * 获取保税维修数据
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/13 下午2:41
     */
    @Override
    public Result<?> getBondedMaintenance(String tenantId) {
        /*****************20240828重构********************/
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<PtsEmsHead> ptsEmsHeadList = emsHeadMapper.selectList(new LambdaQueryWrapper<PtsEmsHead>()
                .in(PtsEmsHead::getEmsType, Arrays.asList("1", "2", "3", "4"))
                .eq(PtsEmsHead::getTenantId, tenantId));
        if (isEmpty(ptsEmsHeadList)) {
            return Result.ok("未查询到账册数据！");
        }
        List<RepairOrderVO> repairOrderVOList = new ArrayList<>();
        for (PtsEmsHead emsHead : ptsEmsHeadList) {
            RepairOrderVO repairOrderVO = new RepairOrderVO();
            repairOrderVO.setAccountBookNo(emsHead.getEmsNo()); // 账册编号
            repairOrderVO.setEtpsName(emsHead.getTradeName()); // 企业名称
            repairOrderVO.setEtpsCode(emsHead.getTradeCode()); // 企业海关编码
            repairOrderVO.setEtpsSccd(emsHead.getTradeSccd()); // 企业社会信用代码
            repairOrderVO.setRegion("济南章锦综合保税区"); // 所属区域?
            repairOrderVO.setGoodsSource("1"); // 维修货物来源?
            repairOrderVO.setBusinessModel("1"); // 业务模式?
            // 表体
            List<RepairOrderStocksVO> repairOrderStocksVOList = new ArrayList<>();
            List<PtsEmsAimg> ptsEmsAimgList = emsAimgMapper.selectList(new LambdaQueryWrapper<PtsEmsAimg>()
                    .eq(PtsEmsAimg::getEmsId, emsHead.getId()));
            if (isEmpty(ptsEmsAimgList)) continue;
            ptsEmsAimgList.forEach(emsAimg -> {
                if (isNotBlank(emsAimg.getGName())) {
                    if (emsAimg.getGName().contains("（待修复）")
                            || emsAimg.getGName().contains("(待修复)")
                            || emsAimg.getGName().contains("(待修复）")
                            || emsAimg.getGName().contains("（待修复)")) {
                        emsAimg.setDetailType("1");
                    } else if (emsAimg.getGName().contains("(维修用料件)")
                            || emsAimg.getGName().contains("（维修用料件）")
                            || emsAimg.getGName().contains("(维修用料件）")
                            || emsAimg.getGName().contains("（维修用料件)")) {
                        emsAimg.setDetailType("2");
                    }
                    emsAimg.setGName(emsAimg.getGName().trim()
                            .replaceAll("（待修复）", "")
                            .replaceAll("\\(待修复\\)", "")
                            .replaceAll("\\(待修复）", "")
                            .replaceAll("（待修复\\)", "")
                            .replaceAll("\\(维修用料件\\)", "")
                            .replaceAll("（维修用料件）", "")
                            .replaceAll("\\(维修用料件）", "")
                            .replaceAll("（维修用料件\\)", ""));
                }
            });
            log.info("{}", ptsEmsAimgList);
            // 2025/3/14 18:59@ZHANGCHAO 追加/变更/完善：分开统计！！
            // 这是货物
            List<PtsEmsAimg> hwList = ptsEmsAimgList.stream().filter(i -> "1".equals(i.getDetailType())).collect(Collectors.toList());
            // 这是料件
            List<PtsEmsAimg> ljList = ptsEmsAimgList.stream().filter(i -> "2".equals(i.getDetailType())).collect(Collectors.toList());

            /*
             * 先统计货物！！
             * 2025/3/14 19:02@ZHANGCHAO
             */
            if (isNotEmpty(hwList)) {
                Map<String, List<PtsEmsAimg>> emsAimgMergeMap = hwList.stream().collect(Collectors.groupingBy(i -> i.getCopGno() + "|" + i.getDetailType()));
                emsAimgMergeMap.forEach((k, v) -> {
                    RepairOrderStocksVO repairOrderStocksVO = new RepairOrderStocksVO();
                    repairOrderStocksVO.setAccountBookNo(repairOrderVO.getAccountBookNo()); // 账册编号
                    repairOrderStocksVO.setMaterialNo(v.get(0).getCopGno()); // 商品料号
                    repairOrderStocksVO.setMaterialName(v.get(0).getGName()); // 商品名称
                    repairOrderStocksVO.setMaterialType(v.get(0).getDetailType()); // 料件类型
                    repairOrderStocksVO.setUnit(v.get(0).getUnit()); // 计量单位
                    /*
                     * 入库数量
                     * 1.对于维修货物和保税料件，汇总入库（区）单对应商品数量。
                     * 2.对于旧件、坏件，汇总坏件入库单对应商品数量。
                     * 2024/8/28 上午9:12@ZHANGCHAO
                     */
                    List<StorageDetail> storageDetailList = storageInfoMapper.getStorageDetailByCond("I", v.get(0).getCopGno(), v.get(0).getDetailType(), tenantId);
                    if (isNotEmpty(storageDetailList)) {
                        repairOrderStocksVO.setInStockQuantity(storageDetailList.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    repairOrderStocksVO.setInStockQuantity(isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO);
                    /*
                     * 领用数量
                     * 1.对于维修货物，汇总维修单对应商品数量。
                     * 2.对于维修用保税料件，汇总料件领用单对应商品数量。
                     * 3.对于旧件、坏件，显示为灰色（无内容）。
                     * 2024/8/14 上午9:16@ZHANGCHAO
                     */
                    List<StorageRepairOrderDetail> storageRepairOrderDetailList = repairOrderDetailMapper.getRepairOrderDetailByCond_(k, tenantId);
                    if (isNotEmpty(storageRepairOrderDetailList)) {
                        repairOrderStocksVO.setUseQuantity(storageRepairOrderDetailList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setUseQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 维修完成数量
                     * 1.对于维修货物，汇总维修单状态为完成维修的对应商品数量。
                     * 2.对于维修用保税料件，汇总状态为完成维修的维修单对应的领用单的料件数量。
                     * 3.对于旧件、坏件，显示为灰色（无内容）。
                     * 2024/8/14 上午9:40@ZHANGCHAO
                     */
                    // 2025/3/19 09:58@ZHANGCHAO 追加/变更/完善：整机直接统计维修单表头的数据，一个维修单=一个整机！！
                    List<StorageRepairOrder> repairCompletedList = baseMapper.getRepairByCond(k, "2", tenantId);
                    if (isNotEmpty(repairCompletedList)) {
                        repairOrderStocksVO.setRepairCompletedQuantity(BigDecimal.valueOf(repairCompletedList.size()));
                    } else {
                        repairOrderStocksVO.setRepairCompletedQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 出库数量
                     * 汇总商品出库（区）单数量。
                     * 2024/8/14 上午10:32@ZHANGCHAO
                     */
                    List<StorageDetail> storageDetailOutList = storageDetailMapper.getStorageDetailOutByCond_(k, tenantId);
                    if (isNotEmpty(storageDetailOutList)) {
                        repairOrderStocksVO.setOutStockQuantity(storageDetailOutList.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setOutStockQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 库存数量
                     * 库存数量=入库数量-出库数量。
                     * 2024/8/14 上午10:45@ZHANGCHAO
                     */
                    repairOrderStocksVO.setStockQuantity((isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO)
                            .subtract((isNotEmpty(repairOrderStocksVO.getOutStockQuantity()) ? repairOrderStocksVO.getOutStockQuantity() : BigDecimal.ZERO)));
                    if (repairOrderStocksVO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                        repairOrderStocksVO.setStockQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 待维修数量
                     * 2024/8/14 上午10:47@ZHANGCHAO
                     */
                    // 2025/3/19 09:58@ZHANGCHAO 追加/变更/完善：整机直接统计维修单表头的数据，一个维修单=一个整机！！
                    List<StorageRepairOrder> repairWaitingList = baseMapper.getRepairByCond(k, "0", tenantId);
                    if (isNotEmpty(repairWaitingList)) {
                        repairOrderStocksVO.setWaitingRepairQuantity(BigDecimal.valueOf(repairWaitingList.size()));
                    } else {
                        repairOrderStocksVO.setWaitingRepairQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 维修中数量
                     * 2024/8/14 上午10:49@ZHANGCHAO
                     */
                    // 2025/3/19 09:58@ZHANGCHAO 追加/变更/完善：整机直接统计维修单表头的数据，一个维修单=一个整机！！
                    List<StorageRepairOrder> repairRepairingList = baseMapper.getRepairByCond(k, "1", tenantId);
                    if (isNotEmpty(repairRepairingList)) {
                        repairOrderStocksVO.setRepairingQuantity(BigDecimal.valueOf(repairRepairingList.size()));
                    } else {
                        repairOrderStocksVO.setRepairingQuantity(BigDecimal.ZERO);
                    }
                    repairOrderStocksVOList.add(repairOrderStocksVO);
                });
            }
            /*
             * 再统计料件！！
             * 2025/3/17 10:19@ZHANGCHAO
             */
            if (isNotEmpty(ljList)) {
                Map<String, List<PtsEmsAimg>> emsAimgMergeMap = ljList.stream().collect(Collectors.groupingBy(i -> i.getCopGno() + "|" + i.getDetailType()));
                emsAimgMergeMap.forEach((k, v) -> {
                    RepairOrderStocksVO repairOrderStocksVO = new RepairOrderStocksVO();
                    repairOrderStocksVO.setAccountBookNo(repairOrderVO.getAccountBookNo()); // 账册编号
                    repairOrderStocksVO.setMaterialNo(v.get(0).getCopGno()); // 商品料号
                    repairOrderStocksVO.setMaterialName(v.get(0).getGName()); // 商品名称
                    repairOrderStocksVO.setMaterialType(v.get(0).getDetailType()); // 料件类型
                    repairOrderStocksVO.setUnit(v.get(0).getUnit()); // 计量单位
                    /*
                     * 入库数量
                     * 1.对于维修货物和保税料件，汇总入库（区）单对应商品数量。
                     * 2.对于旧件、坏件，汇总坏件入库单对应商品数量。
                     * 2024/8/28 上午9:12@ZHANGCHAO
                     */
                    List<StorageDetail> storageDetailList = storageInfoMapper.getStorageDetailByCond("I", v.get(0).getCopGno(), v.get(0).getDetailType(), tenantId);
                    if (isNotEmpty(storageDetailList)) {
                        repairOrderStocksVO.setInStockQuantity(storageDetailList.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    repairOrderStocksVO.setInStockQuantity(isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO);
                    /*
                     * 领用数量
                     * 1.对于维修货物，汇总维修单对应商品数量。
                     * 2.对于维修用保税料件，汇总料件领用单对应商品数量。
                     * 3.对于旧件、坏件，显示为灰色（无内容）。
                     * 2024/8/14 上午9:16@ZHANGCHAO
                     */
                    List<StorageRepairOrderDetail> storageRepairOrderDetailList = repairOrderDetailMapper.getRepairOrderDetailByCond_(k, tenantId);
                    if (isNotEmpty(storageRepairOrderDetailList)) {
                        repairOrderStocksVO.setUseQuantity(storageRepairOrderDetailList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setUseQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 维修完成数量
                     * 1.对于维修货物，汇总维修单状态为完成维修的对应商品数量。
                     * 2.对于维修用保税料件，汇总状态为完成维修的维修单对应的领用单的料件数量。
                     * 3.对于旧件、坏件，显示为灰色（无内容）。
                     * 2024/8/14 上午9:40@ZHANGCHAO
                     */
                    List<StorageRepairOrderDetail> repairCompletedList = repairOrderDetailMapper.getRepairOrderByCond_(k, "2", tenantId);
                    if (isNotEmpty(repairCompletedList)) {
                        repairOrderStocksVO.setRepairCompletedQuantity(repairCompletedList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setRepairCompletedQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 出库数量
                     * 汇总商品出库（区）单数量。
                     * 2024/8/14 上午10:32@ZHANGCHAO
                     */
                    // 2025/3/24 16:53@ZHANGCHAO 追加/变更/完善：料件的出库数量：应该是统计维修单表体里的料件！
                    List<StorageInfo> storageInfoList = storageInfoMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                            .eq(StorageInfo::getIeFlag, "E")
                            .eq(StorageInfo::getStatus, "2") // 已出库！！
                            .eq(StorageInfo::getIsRepairedOut, "1")
                            .isNotNull(StorageInfo::getRelRepairNos)
                            .ne(StorageInfo::getRelRepairNos, ""));
                    List<String> relRepairNosList = new ArrayList<>();
                    if (isNotEmpty(storageInfoList)) {
                        storageInfoList.forEach(i -> {
                            relRepairNosList.addAll(Arrays.asList(i.getRelRepairNos().split(",")));
                        });
                    }
                    List<StorageRepairOrderDetail> storageRepairOrderDetails = repairOrderDetailMapper.getRepairOrderDetailByCond__(relRepairNosList, k, tenantId);
                    if (isNotEmpty(storageRepairOrderDetails)) {
                        repairOrderStocksVO.setOutStockQuantity(storageRepairOrderDetails.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setOutStockQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 库存数量
                     * 库存数量=入库数量-出库数量。
                     * 2024/8/14 上午10:45@ZHANGCHAO
                     */
                    // 2025/3/18 10:41@ZHANGCHAO 追加/变更/完善：维修的料件库存数量，应该是入库数量-维修完成数量！！
//                    repairOrderStocksVO.setStockQuantity((isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO)
//                            .subtract((isNotEmpty(repairOrderStocksVO.getOutStockQuantity()) ? repairOrderStocksVO.getOutStockQuantity() : BigDecimal.ZERO)));
                    repairOrderStocksVO.setStockQuantity((isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO)
                            .subtract((isNotEmpty(repairOrderStocksVO.getRepairCompletedQuantity()) ? repairOrderStocksVO.getRepairCompletedQuantity() : BigDecimal.ZERO)));
                    if (repairOrderStocksVO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                        repairOrderStocksVO.setStockQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 待维修数量
                     * 2024/8/14 上午10:47@ZHANGCHAO
                     */
                    List<StorageRepairOrderDetail> repairWaitingList = repairOrderDetailMapper.getRepairOrderByCond_(k, "0", tenantId);
                    if (isNotEmpty(repairWaitingList)) {
                        repairOrderStocksVO.setWaitingRepairQuantity(repairWaitingList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setWaitingRepairQuantity(BigDecimal.ZERO);
                    }
                    /*
                     * 维修中数量
                     * 2024/8/14 上午10:49@ZHANGCHAO
                     */
                    List<StorageRepairOrderDetail> repairRepairingList = repairOrderDetailMapper.getRepairOrderByCond_(k, "1", tenantId);
                    if (isNotEmpty(repairRepairingList)) {
                        repairOrderStocksVO.setRepairingQuantity(repairRepairingList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setRepairingQuantity(BigDecimal.ZERO);
                    }
                    repairOrderStocksVOList.add(repairOrderStocksVO);
                });
            }

            /*
             * 别忘了，还有拆下件的统计！！
             * 2024/8/28 15:00@ZHANGCHAO
             */
            List<StorageDetail> storageDetailOldGoodsList = storageDetailMapper.getOldPartsByCond(tenantId);
            if (isNotEmpty(storageDetailOldGoodsList)) {
                Map<String, List<StorageDetail>> storageDetailMergeMap = storageDetailOldGoodsList.stream().collect(Collectors.groupingBy(StorageDetail::getCopGno));
                storageDetailMergeMap.forEach((k, v) -> {
                    RepairOrderStocksVO repairOrderStocksVO = new RepairOrderStocksVO();
                    repairOrderStocksVO.setAccountBookNo(repairOrderVO.getAccountBookNo()); // 账册编号
                    repairOrderStocksVO.setMaterialNo(v.get(0).getCopGno()); // 商品料号
                    repairOrderStocksVO.setMaterialName(v.get(0).getPn()); // 商品名称
                    repairOrderStocksVO.setMaterialType("3"); // 料件类型
                    repairOrderStocksVO.setUnit(v.get(0).getQunit()); // 计量单位
                    // 入库数量
                    List<StorageDetail> inStoreOldParts = v.stream().filter(i -> "I".equals(i.getIeFlag())).collect(Collectors.toList());
                    if (isNotEmpty(inStoreOldParts)) {
                        repairOrderStocksVO.setInStockQuantity(inStoreOldParts.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setInStockQuantity(BigDecimal.ZERO);
                    }
                    // 领用数量
                    repairOrderStocksVO.setUseQuantity(BigDecimal.ZERO);
                    // 维修完成数量
                    repairOrderStocksVO.setRepairCompletedQuantity(BigDecimal.ZERO);
                    // 出库数量
                    List<StorageDetail> outStoreOldParts = v.stream().filter(i -> "E".equals(i.getIeFlag())).collect(Collectors.toList());
                    if (isNotEmpty(outStoreOldParts)) {
                        repairOrderStocksVO.setOutStockQuantity(outStoreOldParts.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        repairOrderStocksVO.setOutStockQuantity(BigDecimal.ZERO);
                    }
                    // 库存数量
                    repairOrderStocksVO.setStockQuantity((isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO)
                            .subtract((isNotEmpty(repairOrderStocksVO.getOutStockQuantity()) ? repairOrderStocksVO.getOutStockQuantity() : BigDecimal.ZERO)));
                    if (repairOrderStocksVO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                        repairOrderStocksVO.setStockQuantity(BigDecimal.ZERO);
                    }
                    // 待维修数量
                    repairOrderStocksVO.setWaitingRepairQuantity(BigDecimal.ZERO);
                    // 维修中数量
                    repairOrderStocksVO.setRepairingQuantity(BigDecimal.ZERO);
                    repairOrderStocksVOList.add(repairOrderStocksVO);
                });
            }
            repairOrderVO.setRepairOrderStocksVOList(repairOrderStocksVOList);
            repairOrderVOList.add(repairOrderVO);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK(repairOrderVOList);
        /*****************20240828重构********************/
//        List<StorageInfo> storageInfoList = storageInfoMapper.getStorageInfoByDateRange();
//        if (isEmpty(storageInfoList)) {
//            return Result.ok("暂无数据！");
//        }
//        // 设置忽略租户插件
//        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
//        List<StorageDetail> storageDetailList = storageDetailMapper.selectList(new LambdaQueryWrapper<StorageDetail>()
//                .in(StorageDetail::getStorageNo, storageInfoList.stream().map(StorageInfo::getStorageNo).collect(Collectors.toList())));
//        if (isEmpty(storageDetailList)) {
//            return Result.ok("暂无数据！");
//        }
//        RepairOrderVO repairOrderVO = new RepairOrderVO();
//        String emsNo = storageDetailMapper.getEmsNo(storageDetailList.stream().map(StorageDetail::getInvtListId).collect(Collectors.toList()));
//        String accountBookNo = isNotBlank(emsNo) ? emsNo : "H430622A0004"; // 没有可以默认这个
////        PtsEmsHead emsHead = ptsEmsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
////                .eq(PtsEmsHead::getEmsNo, accountBookNo));
////        repairOrderVO.setAccountBookNo(accountBookNo); // 账册编号
//        repairOrderVO.setAccountBookNo("H430622A0004"); // 账册编号
////        if (isNotEmpty(emsHead)) {
////            repairOrderVO.setEtpsName(emsHead.getTradeName()); // 企业名称
////            repairOrderVO.setEtpsCode(emsHead.getTradeCode()); // 企业海关编码
////            repairOrderVO.setEtpsSccd(emsHead.getTradeSccd()); // 企业社会信用代码
////        } else {
//            repairOrderVO.setEtpsName("济南迅吉安保税物流有限公司"); // 企业名称
//            repairOrderVO.setEtpsCode("370166A005"); // 企业海关编码
//            repairOrderVO.setEtpsSccd("91370100MA3NYD2310"); // 企业社会信用代码
////        }
//        repairOrderVO.setRegion("济南章锦综合保税区"); // 所属区域?
//        repairOrderVO.setGoodsSource("1"); // 维修货物来源?
//        repairOrderVO.setBusinessModel("1"); // 业务模式?
//        // 处理商品库存
//        // 根据物料号、商品名称、料件类型归并出入库单商品表体
//        Map<String, List<StorageDetail>> storageDetailMergeMap = storageDetailList.stream().collect(Collectors.groupingBy(i -> i.getCopGno() + "|" + i.getPn() + "|" + i.getDetailType()));
//        List<RepairOrderStocksVO> repairOrderStocksVOList = new ArrayList<>();
//        storageDetailMergeMap.forEach((k, v) -> {
//            RepairOrderStocksVO repairOrderStocksVO = new RepairOrderStocksVO();
//            repairOrderStocksVO.setAccountBookNo(repairOrderVO.getAccountBookNo()); // 账册编号
//            repairOrderStocksVO.setMaterialNo(v.get(0).getCopGno()); // 商品料号
//            repairOrderStocksVO.setMaterialName(v.get(0).getPn()); // 商品名称
//            repairOrderStocksVO.setMaterialType(v.get(0).getDetailType()); // 料件类型
//            repairOrderStocksVO.setUnit(v.get(0).getQunit()); // 计量单位
//            /*
//             * 入库数量
//             * 1.对于维修货物和保税料件，汇总入库（区）单对应商品数量。
//             * 2.对于旧件、坏件，汇总坏件入库单对应商品数量。
//             * 2024/8/14 上午9:12@ZHANGCHAO
//             */
////            if ("3".equals(repairOrderStocksVO.getMaterialType())) { // 针对自动生成的旧件入库，暂无法标识是旧件还是维修用料件，会混在一起，其实入库单的数据就是它库存的数量。
////                List<StorageDetail> storageDetailOldGoodsList = storageDetailMapper.getStorageDetailOldGoodsByCond(k);
////                if (isNotEmpty(storageDetailOldGoodsList)) {
////                    repairOrderStocksVO.setInStockQuantity(storageDetailOldGoodsList.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
////                }
////            } else {
//                repairOrderStocksVO.setInStockQuantity(v.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//                repairOrderStocksVO.setInStockQuantity(isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO);
////            }
//            /*
//             * 领用数量
//             * 1.对于维修货物，汇总维修单对应商品数量。
//             * 2.对于维修用保税料件，汇总料件领用单对应商品数量。
//             * 3.对于旧件、坏件，显示为灰色（无内容）。
//             * 2024/8/14 上午9:16@ZHANGCHAO
//             */
//            // 获取对应的维修单商品
//            List<StorageRepairOrderDetail> storageRepairOrderDetailList = repairOrderDetailMapper.getRepairOrderDetailByCond(k);
//            if (isNotEmpty(storageRepairOrderDetailList)) {
//                repairOrderStocksVO.setUseQuantity(storageRepairOrderDetailList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//            } else {
//                repairOrderStocksVO.setUseQuantity(BigDecimal.ZERO);
//            }
//            /*
//             * 维修完成数量
//             * 1.对于维修货物，汇总维修单状态为完成维修的对应商品数量。
//             * 2.对于维修用保税料件，汇总状态为完成维修的维修单对应的领用单的料件数量。
//             * 3.对于旧件、坏件，显示为灰色（无内容）。
//             * 2024/8/14 上午9:40@ZHANGCHAO
//             */
//            List<StorageRepairOrderDetail> repairCompletedList = repairOrderDetailMapper.getRepairOrderByCond(k, "2");
//            if (isNotEmpty(repairCompletedList)) {
//                repairOrderStocksVO.setRepairCompletedQuantity(repairCompletedList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//            } else {
//                repairOrderStocksVO.setRepairCompletedQuantity(BigDecimal.ZERO);
//            }
//            /*
//             * 出库数量
//             * 汇总商品出库（区）单数量。
//             * 2024/8/14 上午10:32@ZHANGCHAO
//             */
//            List<StorageDetail> storageDetailOutList = storageDetailMapper.getStorageDetailOutByCond(k);
//            if (isNotEmpty(storageDetailOutList)) {
//                repairOrderStocksVO.setOutStockQuantity(storageDetailOutList.stream().map(StorageDetail::getActualQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//            } else {
//                repairOrderStocksVO.setOutStockQuantity(BigDecimal.ZERO);
//            }
//            /*
//             * 库存数量
//             * 库存数量=入库数量-出库数量。
//             * 2024/8/14 上午10:45@ZHANGCHAO
//             */
//            repairOrderStocksVO.setStockQuantity((isNotEmpty(repairOrderStocksVO.getInStockQuantity()) ? repairOrderStocksVO.getInStockQuantity() : BigDecimal.ZERO)
//                    .subtract((isNotEmpty(repairOrderStocksVO.getOutStockQuantity()) ? repairOrderStocksVO.getOutStockQuantity() : BigDecimal.ZERO)));
//            if (repairOrderStocksVO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
//                repairOrderStocksVO.setStockQuantity(BigDecimal.ZERO);
//            }
//            /*
//             * 待维修数量
//             * 2024/8/14 上午10:47@ZHANGCHAO
//             */
//            List<StorageRepairOrderDetail> repairWaitingList = repairOrderDetailMapper.getRepairOrderByCond(k, "0");
//            if (isNotEmpty(repairWaitingList)) {
//                repairOrderStocksVO.setWaitingRepairQuantity(repairWaitingList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//            } else {
//                repairOrderStocksVO.setWaitingRepairQuantity(BigDecimal.ZERO);
//            }
//            /*
//             * 维修中数量
//             * 2024/8/14 上午10:49@ZHANGCHAO
//             */
//            List<StorageRepairOrderDetail> repairRepairingList = repairOrderDetailMapper.getRepairOrderByCond(k, "1");
//            if (isNotEmpty(repairRepairingList)) {
//                repairOrderStocksVO.setRepairingQuantity(repairRepairingList.stream().map(StorageRepairOrderDetail::getQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
//            } else {
//                repairOrderStocksVO.setRepairingQuantity(BigDecimal.ZERO);
//            }
//            repairOrderStocksVOList.add(repairOrderStocksVO);
//        });
//
//        repairOrderVO.setRepairOrderStocksVOList(repairOrderStocksVOList);
//        // 关闭忽略策略
//        InterceptorIgnoreHelper.clearIgnoreStrategy();
//        return Result.ok(repairOrderVO);
    }

    /**
     * @param page
     * @param storageRepairOrder
     * @return
     */
    @Override
    public Result<?> listGoods(Page<StoreStocks> page, StorageRepairOrder storageRepairOrder) {
        page = new Page<>(1, Integer.MAX_VALUE);
        // 查询货主待维修区的第一个储位
        StoreSpace storeSpace_DWX = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "2"); // 2 = 待维修区
        storageRepairOrder.setAreaCode(isNotEmpty(storeSpace_DWX) ? storeSpace_DWX.getAreaCode() : null);
        IPage<StoreStocks> pageList = baseMapper.listGoods(page, storageRepairOrder, "1");
        List<StoreStocks> storeStocksList = new ArrayList<>();
        if (isNotEmpty(pageList.getRecords())) {
            log.info("查询出的数量listGoods:{}", pageList.getRecords().size());
            pageList.getRecords().forEach(k -> k.setAvailableQty((isNotEmpty(k.getBeginQty()) ? k.getBeginQty() : BigDecimal.ZERO)
                    .subtract(isNotEmpty(k.getOccupyQty()) ? k.getOccupyQty() : BigDecimal.ZERO)));
            for (StoreStocks storeStocks : page.getRecords()) {
                // 2025/3/24 15:10@ZHANGCHAO 追加/变更/完善：防止维修单多做！！！
                // 1.获取入库总数
                BigDecimal allCount = baseMapper.getInStorageCount(storeStocks);
                // 2.获取维修单总数，维修单总数 < 入库总数
                List<StorageRepairOrder> repairOrderList = baseMapper.selectList(new LambdaUpdateWrapper<StorageRepairOrder>()
                        .eq(StorageRepairOrder::getCopGno, storeStocks.getCopGno())
                        .eq(StorageRepairOrder::getBatchNo, storeStocks.getBatchNo()));
                int repairOrderCount = repairOrderList != null ? repairOrderList.size() : 0;
                if (repairOrderCount < allCount.intValue()) {
                    storeStocksList.add(storeStocks);
                } else if (repairOrderCount == allCount.intValue()) {
                    log.info("待维修货物：【{} - {}】入库数量：{}，维修单数量：{}；已经全部维修完啦！不要再出啦！！！！！", storeStocks.getCopGno(), storeStocks.getBatchNo(), allCount, repairOrderCount);
                } else {
                    log.info("待维修货物：【{} - {}】入库数量：{}，维修单数量：{}；不符合条件！已经出超啦！！！！！", storeStocks.getCopGno(), storeStocks.getBatchNo(), allCount, repairOrderCount);
                }
            }
        }
        log.info("符合条件的数量storeStocksList:{}", storeStocksList.size());
        pageList.setRecords(storeStocksList);
        return Result.ok(pageList);
    }

    /**
     * @param page
     * @param storageRepairOrder
     * @return
     */
    @Override
    public Result<?> listGoods_(Page<StoreStocks> page, StorageRepairOrder storageRepairOrder) {
        storageRepairOrder.setAreaCode(storageRepairOrder.getAreaCode());
        IPage<StoreStocks> pageList = storeStocksService.page(page, new LambdaQueryWrapper<StoreStocks>()
                .eq(StoreStocks::getCustomer, storageRepairOrder.getCustomer())
                .eq(StoreStocks::getStoreCode, storageRepairOrder.getStoreCode())
//                .eq(StoreStocks::getDetailType, "1")
                .gt(StoreStocks::getBeginQty, BigDecimal.ZERO)
                .like(isNotBlank(storageRepairOrder.getCopGno()), StoreStocks::getCopGno, storageRepairOrder.getCopGno())
                .like(isNotBlank(storageRepairOrder.getPn()), StoreStocks::getPn, storageRepairOrder.getPn())
                .like(isNotBlank(storageRepairOrder.getBatchNo()), StoreStocks::getBatchNo, storageRepairOrder.getBatchNo())
                .eq(StoreStocks::getAreaName, storageRepairOrder.getAreaCode()));
        if (isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(k -> k.setAvailableQty((isNotEmpty(k.getBeginQty()) ? k.getBeginQty() : BigDecimal.ZERO)
                    .subtract(isNotEmpty(k.getOccupyQty()) ? k.getOccupyQty() : BigDecimal.ZERO)));
        }
        return Result.ok(pageList);
    }

    /**
     * @param page
     * @param storageRepairOrder
     * @return
     */
    @Override
    public Result<?> listGoodsDetail(Page<StoreStocks> page, StorageRepairOrder storageRepairOrder) {
        IPage<StoreStocks> pageList = baseMapper.listGoods(page, storageRepairOrder, "2");
        if (isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(k -> k.setAvailableQty((isNotEmpty(k.getBeginQty()) ? k.getBeginQty() : BigDecimal.ZERO)
                    .subtract(isNotEmpty(k.getOccupyQty()) ? k.getOccupyQty() : BigDecimal.ZERO)));
        }
        return Result.ok(pageList);
    }

    /**
     * @param page
     * @param storageRepairOrder
     * @return
     */
    @Override
    public Result<?> listOldParts(Page<StoreStocks> page, StorageRepairOrder storageRepairOrder) {
        // 查询货主拆下件区的第一个储位
        StoreSpace storeSpace_JJ = storeSpaceMapper.getSpaceByCond(storageRepairOrder.getCustomer(), "3"); // 3 = 拆下件区
//        if (isEmpty(storeSpace_JJ)) {
//            throw new RuntimeException("货主[" + storageRepairOrder.getCustomerStr() + "]无拆下件库区或没有相应储位，请先配置！");
//        }
        storageRepairOrder.setAreaCode(isNotEmpty(storeSpace_JJ) ? storeSpace_JJ.getAreaCode() : null);
        IPage<StoreStocks> pageList = baseMapper.listOldParts(page, storageRepairOrder);
        if (isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(k -> k.setAvailableQty((isNotEmpty(k.getBeginQty()) ? k.getBeginQty() : BigDecimal.ZERO)
                    .subtract(isNotEmpty(k.getOccupyQty()) ? k.getOccupyQty() : BigDecimal.ZERO)));
        }
        return Result.ok(pageList);
    }

    /**
     * 检查维修单关联的调拨单是否都已确认
     *
     * @param repairNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/3 16:41
     */
    @Override
    public Result<?> checkRepairAllConfirm(String repairNo) {
        List<StorageTransfer> storageTransferList = storageTransferMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                .eq(StorageTransfer::getRelRepairNo, repairNo));
        if (isEmpty(storageTransferList)) {
            return Result.error("维修单[" + repairNo + "]未查询到关联的调拨单数据！");
        }
        boolean isAllConfirm = storageTransferList.stream().allMatch(storageTransfer -> "1".equals(storageTransfer.getStatus()));
        if (!isAllConfirm) {
            return Result.error("维修单[" + repairNo + "]关联的调拨单状态还未确认，无法继续操作！");
        }
        return Result.ok();
    }

    /**
     * 导出库存统计
     *
     * @return void
     * <AUTHOR>
     * @date 2024/9/9 16:40
     */
    @Override
    public void exportStatistics(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Result<?> result = getBondedMaintenance(TenantContext.getTenant());
        List<RepairOrderVO> repairOrderVOList;
        if (result.isSuccess()) {
            repairOrderVOList = (List<RepairOrderVO>) result.getResult();
        } else {
            throw new RuntimeException(result.getMessage());
        }
        RepairOrderVO repairOrderVO = repairOrderVOList.get(0);
        // 处理Excel
        Map<String, Object> map = new HashMap<>();
        map.put("accountBookNo", repairOrderVO.getAccountBookNo());
        map.put("etpsCode", repairOrderVO.getEtpsCode());
        map.put("etpsName", repairOrderVO.getEtpsName());
        map.put("etpsSccd", repairOrderVO.getEtpsSccd());
        map.put("region", repairOrderVO.getRegion());
        map.put("businessModel", "1".equals(repairOrderVO.getBusinessModel()) ? "集团自产产品维修" : "协议维修");
        if (isNotBlank(repairOrderVO.getGoodsSource())) {
            if ("1".equals(repairOrderVO.getGoodsSource())) {
                map.put("goodsSource", "境外");
            } else if ("2".equals(repairOrderVO.getGoodsSource())) {
                map.put("goodsSource", "境内");
            } else if ("3".equals(repairOrderVO.getGoodsSource())) {
                map.put("goodsSource", "区内");
            } else {
                map.put("goodsSource", "");
            }
        }
        List<Map<String, Object>> listMap = new ArrayList<>();
        if (isNotEmpty(repairOrderVO.getRepairOrderStocksVOList())) {
            List<DictModel> dictModels = sysBaseApi.getDictItems("STORE_DETAIL_TYPE");
            Map<String, String> dictMap = new HashMap<>();
            if (isNotEmpty(dictModels)) {
                dictModels.forEach(dictModel -> {
                    dictMap.put(dictModel.getValue(), dictModel.getText());
                });
            }
            List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_units,name,code");
            Map<String, String> dictMap1 = new HashMap<>();
            if (isNotEmpty(dictModels1)) {
                dictModels1.forEach(dictModel -> {
                    dictMap1.put(dictModel.getValue(), dictModel.getText());
                });
            }
            repairOrderVO.getRepairOrderStocksVOList().forEach(k -> {
                Map<String, Object> lm = new HashMap<>();
                lm.put("accountBookNo", k.getAccountBookNo());
                lm.put("materialName", k.getMaterialName());
                lm.put("materialNo", k.getMaterialNo());
                lm.put("materialType", isNotEmpty(dictMap) ? dictMap.get(k.getMaterialType()) : "");
                lm.put("inStockQuantity", k.getInStockQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("outStockQuantity", k.getOutStockQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("repairCompletedQuantity", k.getRepairCompletedQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("repairingQuantity", k.getRepairingQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("stockQuantity", k.getStockQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("useQuantity", k.getUseQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("waitingRepairQuantity", k.getWaitingRepairQuantity().setScale(2, RoundingMode.HALF_UP));
                lm.put("unit", isNotEmpty(dictMap1) ? dictMap1.get(k.getUnit()) : "");
                listMap.add(lm);
            });
        }
        map.put("listMap", listMap);
        //获取模板文件路径
        Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream("/templates/xls/库存统计导出模版.xlsx"));
        TemplateExportParams params = new TemplateExportParams();
        params.setTemplateWb(templateWorkbook);
        Workbook workbook = ExcelExportUtil.exportExcel(params, map);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
