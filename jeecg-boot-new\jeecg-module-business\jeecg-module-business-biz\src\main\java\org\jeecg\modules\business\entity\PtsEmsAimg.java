package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 手账册归并后料件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@Accessors(chain = true)
@TableName("pts_ems_aimg")
public class PtsEmsAimg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 手册流水
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("EMS_ID")
    private Long emsId;

    /**
     * 手册编号
     */
    @TableField("EMS_NO")
    private String emsNo;
    /**
     * 统一编号
     */
    @TableField("SEQ_NO")
    private String seqNo;

    /**
     * 序号(报关用)(备案号)
     */
    @JsonProperty("gNo")
    @TableField("G_NO")
    private Integer gNo;

    /**
     * 货号(归并后可以没有货号)
     */
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 商品编码(税号)
     */
    @TableField("CODET")
    private String codet;

    /**
     * 商品名称
     */
    @JsonProperty("gName")
    @TableField("G_NAME")
    private String gName;

    /**
     * 规格型号(申报要素)
     */
    @JsonProperty("gModel")
    @TableField("G_MODEL")
    private String gModel;

    /**
     * 计量单位
     */
    @Dict(dictTable = "erp_units", dicText = "name", dicCode = "code")
    @TableField("UNIT")
    private String unit;

    /**
     * 法定计量单位
     */
    @Dict(dictTable = "erp_units", dicText = "name", dicCode = "code")
    @TableField("UNIT1")
    private String unit1;

    /**
     * 法定第二计量单位
     */
    @TableField("UNIT2")
    private String unit2;

    /**
     * 产销国(原产国)
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    @TableField("COUNTRY_CODE")
    private String countryCode;

    /**
     * 自定义净重(系统用,物流账册取值)
     */
    @TableField("NET_WEIGHT")
    private BigDecimal netWeight;

    /**
     * 自定义所属企业(系统用,物流账册取值)(TENANT_ID)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("CUSTOMER_ID")
    private Long customerId;

    /**
     * 申报单价
     */
    @TableField("DEC_PRICE")
    private BigDecimal decPrice;

    /**
     * 币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    @TableField("CURR")
    private String curr;

    /**
     * 申报单价（人民币）
     */
    @TableField("DEC_PRICE_RMB")
    private String decPriceRmb;

    /**
     * 备案数量(加贸手册用)
     */
    @TableField("QTY")
    private BigDecimal qty;

    /**
     * 已进口数量(加贸手/账册用)
     */
    @TableField("IMPORTED_QTY")
    private BigDecimal importedQty;

    /**
     * 已进口数量(加贸手/账册用)
     */
    @TableField("EXP_QTY")
    private BigDecimal expQty;

    /**
     * 理论库存数量(都用)
     */
    @TableField("STOCK_QTY")
    private BigDecimal stockQty;

    /**
     * 匹配占用数量(物流账册用)
     */
    @TableField("OCCUPY_QTY")
    private BigDecimal occupyQty;

    /**
     * 预核扣数量(物流账册用)
     */
    @TableField("PREDISTRIBUTION")
    private BigDecimal predistribution;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 处理标志
     */
    @TableField("MODIFY_MARK")
    private String modifyMark;

    /**
     * 申报总价
     */
    @TableField("APPR_AMT")
    private BigDecimal apprAmt;

    /**
     * 征免方式
     */
    @Dict(dicCode = "ZJMSFS")
    @TableField("DUTY_MODE")
    private String dutyMode;

    /**
     * 记账清单编号(海关要求回填值(可累计的无意义))
     */
    @TableField("BOND_INVT_NO")
    private String bondInvtNo;

    /**
     * 记账清单序号(海关要求回填值(可累计的无意义))
     */
    @TableField("BOND_INVT_ITEM")
    private String bondInvtItem;

    /**
     * 企业内部编号(统计需要(可累计的无意义))
     */
    @TableField("INTERNAL_NO")
    private String internalNo;

    /**
     * 版本号(乐观锁)
     */
    @Version
    @TableField("VERSION")
    private Integer version;

    /**
     * 入库申报单号(太古账册用,报关的是报关单号；非报关单的是非报关；(可累计的无意义))
     */
    @TableField("CLEARANCE_NO")
    private String clearanceNo;

    /**
     * 入库申报日期(太古账册用,报关的是报关申报日期；非报关的是清单申报日期；(可累计的无意义))
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("IAPP_DATE")
    private Date iappDate;

    /**
     * 是否弃用(0:否，1:是)
     */
    @TableField("ABANDONING")
    private Boolean abandoning;

    /**
     * 延期次数/状态
     */
    @TableField("DELAY")
    private String delay;

    /**
     * 到期日(二年期到期日)
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("DUE_DATE")
    private Date dueDate;

    /**
     * 延期日(一年期延期日)
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("DELAY_DATE")
    private Date delayDate;

    /**
     * 到期日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("EXPIRY_DATE")
    private Date expiryDate;

    /**
     * 入库日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("WAREHOUSING_DATE")
    private Date warehousingDate;

    /**
     * 法定第一数量
     */
    @TableField("COUNT1")
    private BigDecimal count1;

    /**
     * 法定第二数量
     */
    @TableField("COUNT2")
    private BigDecimal count2;

    /**
     * 单项法一数量（法定数量/申报数量）
     */
    @TableField("SING_COUNT1")
    private BigDecimal singCount1;

    /**
     * 单项法二数量（第二法定数量/申报数量）
     */
    @TableField("SING_COUNT2")
    private BigDecimal singCount2;

    /**
     * 单项净重（净重/申报数量）
     */
    @TableField("SING_NET_WT")
    private BigDecimal singNetWt;

    /**
     * 是否推送（0-未推送，1-已推送）
     */
    @TableField("PUSH_STATUS")
    private String pushStatus;

    /**
     * 是否推送（0-未推送，1-已推送）
     */
    @TableField("STOCK_GOODS_TYPE_ID")
    private String stockGoodsTypeId;

    /**
     * 重点商品标识
     */
    private String keyProductIdentification;
    /**
     * 商品属性
     */
    private String attributes;
    /**
     * 修改标志
     */
    private String modifyFlag;

    /**
     * 海关执行标志
     */
    private String customsEnforcementMark;


    /**
     * 是否待延期
     */
    @TableField(exist = false)
    private String isDyq;
    /**
     * 是否已过期
     */
    @TableField(exist = false)
    private String isYgq;

    /**
     * 是否已过期
     */
    @TableField(exist = false)
    private String detailType;
    @TableField(exist = false)
    private String type;
    @TableField(exist = false)
    private String zjjksl;
    @TableField(exist = false)
    private String scyl;
    @TableField(exist = false)
    private String ylbl;
    @TableField(exist = false)
    private String sjgjzjksl;
    @TableField(exist = false)
    private String yljzjksl;
    @TableField(exist = false)
    private String ljthjksl;
    @TableField(exist = false)
    private String ljthcksl;
    @TableField(exist = false)
    private String nxzssl;
    @TableField(exist = false)
    private String zfcsl;
    @TableField(exist = false)
    private String ylckjzsl;
    @TableField(exist = false)
    private String xhsl;
    @TableField(exist = false)
    private String sjjksl;

    /**
     * 出口成品耗料合计
     */
    @TableField(exist = false)
    private BigDecimal ckcphlhj;
    /**
     * 保税料件耗用合计
     */
    @TableField(exist = false)
    private BigDecimal bsljhyhj;
    /**
     * 非保税料件耗用合计
     */
    @TableField(exist = false)
    private BigDecimal fbsljhyhj;
    /**
     * 工艺耗损合计
     */
    @TableField(exist = false)
    private BigDecimal gyhshj;
    /**
     * 理论剩余料件 = 实际进口数量 - 出口成品耗料合计
     */
    @TableField(exist = false)
    private BigDecimal llsylj;
    /**
     * 保税边角料应补数量
     */
    @TableField(exist = false)
    private BigDecimal bsbjlybsl;
    /**
     * 边角料已补数量
     */
    @TableField(exist = false)
    private BigDecimal bjlybsl;
    /**
     * 边角料复出数量
     */
    @TableField(exist = false)
    private BigDecimal bjlfcsl;
    /**
     * 进口金额汇总
     */
    @TableField(exist = false)
    private BigDecimal jkjehj;
    /**
     * 实际进口金额汇总
     */
    @TableField(exist = false)
    private BigDecimal sjjkjehj;

    /**
     * 加权平均单价
     */
    @TableField(exist = false)
    private BigDecimal jqpjdj;

    /**
     * 已出口成品折料金额合计
     */
    @TableField(exist = false)
    private BigDecimal yckcpzljehj;

    /**
     * 注意：计算金额时候的直接进口数量用
     */
    @TableField(exist = false)
    private BigDecimal zjjkslQty;

    @TableField(exist = false)
    private BigDecimal tgzddj;
    @TableField(exist = false)
    private BigDecimal tgzgdj;
    /**
     * 来源标识 (-境外重点料件 2-境外普通料件 3-国内采购料件 4-专账成品转入料件 表头重点标识为1时必填，为空时不允许填写)
     */
    @Dict(dicCode = "LYBS")
    private String col1;

    /**
     * 申报总价
     */
    private BigDecimal dclTotalAmt;


    /**
     * 消耗量
     */
    @TableField(exist = false)
    private BigDecimal consumption;

    /**
     * 差额量
     */
    @TableField(exist = false)
    private BigDecimal difference;

    /**
     * 差额率
     */
    @TableField(exist = false)
    private BigDecimal differenceRate;
}
