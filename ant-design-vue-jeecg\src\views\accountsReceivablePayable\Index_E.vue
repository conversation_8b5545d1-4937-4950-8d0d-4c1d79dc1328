<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="登记类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select placeholder="请选择登记类型" v-model="queryParam.registrationType">
								<a-select-option value="1">根据业务</a-select-option>
								<a-select-option value="2">根据报关单</a-select-option>
								<a-select-option value="3">根据集装箱</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select placeholder="请选择状态" v-model="queryParam.status">
								<a-select-option value="0">已登记</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="箱号/业务单号/报关单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input placeholder="请输入箱号/业务单号/报关单号" v-model="queryParam.documentNumber"></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
					</a-col>
					<template v-if="toggleSearchStatus">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="费用名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input placeholder="请输入费用名称" v-model="queryParam.feeName"></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="createTimeDate" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="createTimeDateChange" />
							</a-form-item>
						</a-col>
					</template>
				</a-row>
			</a-form>
		</div>


		<!-- table区域-begin -->
		<div>
			<!--			新表格-->
			<query-vxe-grid
				class="xGrid-style"
				ref="xGrid"
				size="mini"
				height="500"
				:loading="loading"
				:gridOptions="gridOptions"
				:dataSource="dataSource"
				@cell-click="cellClick"
				@checkbox-change="checkboxChangeEvent"
				@checkbox-all="checkboxChangeEvent"
				@cell-dblclick="cellDblclick"
				@page-change="handlePageChange"
			>
				<template v-slot:toolbar_buttons>
					<!-- 操作按钮区域 -->
					<div class="table-operator">
<!--						<a-button @click="handleCostRegistration" type="primary" icon="dollar" v-has="'Business:CostRegistration'">费用登记</a-button>-->
						<a-dropdown >
							<a-menu slot="overlay" >
								<a-menu-item key="1" @click="handleCostRegistration('1')">
									根据集装箱登记
								</a-menu-item>
								<a-menu-item key="2" @click="handleCostRegistration('2')">
									根据报关单登记
								</a-menu-item>
								<a-menu-item key="3" @click="handleCostRegistration('3')">
									根据业务登记
								</a-menu-item>
							</a-menu>
							<a-button v-has="'Business:CostRegistration'" type="primary" size="small" icon="dollar">
								费用登记
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>

						<a-button @click="batchDeleteByIds" v-if="selectedRowKeys.length > 0" ghost type="primary" icon="delete" v-has="'Business:CostRegistration'">批量删除
						</a-button>
					</div>
				</template>
				<template #action="{ row }">
					<a @click="handleEdit(row)">编辑</a> |
					<a @click="handleDeleteRow(row.id)">删除</a>
				</template>
			</query-vxe-grid>
			<AccountsReceivablePayableModal ref="AccountsReceivablePayableModal" @loadData="loadData">

			</AccountsReceivablePayableModal>

			<CostRegistrationModal ref="CostRegistrationModal" :visible="visible"
														 @update:visible = 'updateVisible'
														 @loadData="loadData">
			</CostRegistrationModal>
			<CostRegistrationByDecModal	ref="CostRegistrationByDecModal"
															 @loadData="loadData" :visible="visibleByDec"
															 @update:visible = 'updateVisibleByDec'>
			</CostRegistrationByDecModal>
			<CostRegistrationByBusinessModal ref="CostRegistrationByBusinessModal"
																			 @loadData="loadData" :visible="visibleByBusiness"
																			 @update:visible = 'updateVisibleByBusiness'>
			</CostRegistrationByBusinessModal>
		</div>

	</a-card>
</template>
<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import Vue from 'vue'
import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid.vue'
import { MyXGridMixin } from '@/mixins/MyXGridMixin'
import AccountsReceivablePayableModal from './modules/AccountsReceivablePayableModal'
import CostRegistrationModal from './modules/CostRegistrationModal'
import CostRegistrationByDecModal from '@/views/accountsReceivablePayable/modules/CostRegistrationByDecModal.vue'
import CostRegistrationByBusinessModal
	from '@/views/accountsReceivablePayable/modules/CostRegistrationByBusinessModal.vue'



export default {
	name: 'accountsReceivablePayableIndex',
	mixins: [MyXGridMixin, mixinDevice],
	components: {
		CostRegistrationByBusinessModal,
		CostRegistrationByDecModal,
		QueryVxeGrid,AccountsReceivablePayableModal,CostRegistrationModal
	},
	data() {
		return {
			visible:false,
			visibleByDec:false,
			visibleByBusiness:false,
			ieFlag:'',
			createTimeDate:'',
			// 高级查询条件
			queryParam: {},
			dataSource:[],
			/* 分页参数 */
			ipagination: {
				total: 0,
				currentPage: 1,
				pageSize: 15,
				pageSizes: [15, 30, 50, 100, 200],
				perfect: true
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			url: {
				list: '/business/accountsReceivablePayable/list',
				delete: '/business/accountsReceivablePayable/delete',
				deleteBatch: '/business/accountsReceivablePayable/deleteBatch',

			},
		}
	},
	computed: {
		gridOptions() {
			const gridOptions = {
				id: 'Table',
				pagerConfig: {
					currentPage:this.ipagination.currentPage,
					pageSize:this.ipagination.pageSize,
					pageSizes: [15, 30, 50, 100, 200],
					total:this.ipagination.total
				},
				toolbarConfig: {
					perfect: true,
					refresh: {
						query: () => this.loadData(1)
					},
					zoom: true,
					custom: true,
					slots: {
						buttons: 'toolbar_buttons'
					}
				},
				columns: [
					{
						type: 'checkbox',
						field: 'checkbox',
						align: 'center',
						width: 50,
						fixed: 'left',
					},
					{
						title: '登记类型',
						align: 'center',
						sorter: false,
						width: 100,
						field: 'registrationType',
						formatter:function ({ cellValue, row, column }) {
							if(cellValue=='1'){
								return "根据业务";
							}else if(cellValue=='2'){
								return "根据报关单";
							}else if(cellValue=='3'){
								return "根据集装箱";
							}else{
								return cellValue;
							}
						}
					},
					{
						title: '状态',
						align: 'center',
						sorter: false,
						width: 80,
						field: 'status',
						formatter:function ({ cellValue, row, column }) {
							if(cellValue=='0'){
								return "已登记";
							}else{
								return cellValue;
							}
						}
					},
					{
						title: '集装箱号/业务单号/报关单号',
						align: 'center',
						sorter: false,
						width: 180,
						field: 'documentNumber',
					},
							{
						title: '结算单位',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'settlementName',
					},
						{
						title: '收支方向',
						align: 'center',
						sorter: false,
						width: 80,
						field: 'type',
						formatter:function ({ cellValue, row, column }) {
							if(cellValue=='1'){
								return "应收";
							}else if(cellValue=='2'){
								return "应付";
								}else {
								return cellValue;
							}
						}
					},
					{
						title: '费用名称',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'feeName',
					},
					{
						title: '费用金额',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'feeAmount',
					},
					{
						title: '币制',
						align: 'center',
						sorter: false,
						width: 80,
						field: 'currency',
					},
					{
						title: '创建人',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'creator',
					},
					{
						title: '创建时间',
						align: 'center',
						sorter: false,
						width: 160,
						field: 'createTime',
					},
					{
						title: '操作',
						field: 'action',
						align: 'center',
						fixed: 'right',
						width: 80,
						slots: {
							default: 'action'
						}
					},
				]
			}
			return gridOptions
		}
	},
	created() {

		// let map = new Map()
		// if (this.$route.path.indexOf('?') > -1) {
		// 	map = this.handleRouterUrl(this.$route.path)
			this.ieFlag = 'E'
			this.queryParam = {ieFlag:this.ieFlag}
			this.loadData(1)
		// }


	},
	methods: {
		updateVisible(flag){
			this.visible = flag
			this.loadData(1)
		},
		updateVisibleByDec(flag){
			this.visibleByDec = flag
			this.loadData(1)
		},
		updateVisibleByBusiness(flag){
			this.visibleByBusiness = flag
			this.loadData(1)
		},
		createTimeDateChange(value, dateString){
			this.queryParam.startCreateTimeDate = dateString[0].toString()+' 00:00:00'
			this.queryParam.lastCreateTimeDate = dateString[1].toString()+' 23:59:59'
		},
		async handleCostRegistration(type){
			if('1'==type){
				//集装箱
				this.visible = true
				await this.$refs.CostRegistrationModal.loadData(this.$refs.CostRegistrationModal.url.listDecContainer,1)
				this.$refs.CostRegistrationModal.selectedContainerKeys = []
				this.$refs.CostRegistrationModal.selectionContainers = []
				this.$refs.CostRegistrationModal.registeredFees = []
				this.$refs.CostRegistrationModal.ieFlag = 'E'

			}else if('2'==type){
				//报关单
				this.visibleByDec = true
				await this.$refs.CostRegistrationByDecModal.loadData(this.$refs.CostRegistrationByDecModal.url.listDecHead,1)
				this.$refs.CostRegistrationByDecModal.selectedContainerKeys = []
				this.$refs.CostRegistrationByDecModal.selectionContainers = []
				this.$refs.CostRegistrationByDecModal.registeredFees = []
				this.$refs.CostRegistrationByDecModal.ieFlag = 'E'

			}else if('3'==type){
				//业务
				this.visibleByBusiness = true
				await this.$refs.CostRegistrationByBusinessModal.loadData(
					this.$refs.CostRegistrationByBusinessModal.url.listOrderInfo,1)
				this.$refs.CostRegistrationByBusinessModal.selectedContainerKeys = []
				this.$refs.CostRegistrationByBusinessModal.selectionContainers = []
				this.$refs.CostRegistrationByBusinessModal.registeredFees = []
				this.$refs.CostRegistrationByBusinessModal.ieFlag = 'E'
			}

			// await this.$refs.AccountsReceivablePayableModal.showModal()
			// this.$refs.AccountsReceivablePayableModal.title='费用登记-新增'
			// //设置默认值
			// this.$refs.AccountsReceivablePayableModal.model={}
			// this.$refs.AccountsReceivablePayableModal.model.registrationType=3
			// this.$refs.AccountsReceivablePayableModal.model.ieFlag=this.ieFlag
			// this.$refs.AccountsReceivablePayableModal.$refs.attachments.otherName = ''
		},

		searchReset() {
			this.queryParam={ieFlag:this.ieFlag}
			this.createTimeDate = ''
			this.onClearSelected()
			this.loadData(1)
		},
		onClearSelected() {
			this.selectedRowKeys = []
			this.selectionRows = []
		},

		async cellDblclick({ row }){
			await this.$refs.AccountsReceivablePayableModal.showModal()
			this.$refs.AccountsReceivablePayableModal.title='费用登记-编辑'
			this.$refs.AccountsReceivablePayableModal.model=row
			await this.$refs.AccountsReceivablePayableModal.$refs.attachments.initModel(row)
		},
		async handleEdit(row){
			await this.$refs.AccountsReceivablePayableModal.showModal()
			this.$refs.AccountsReceivablePayableModal.title='费用登记-编辑'
			this.$refs.AccountsReceivablePayableModal.model=row
			await this.$refs.AccountsReceivablePayableModal.$refs.attachments.initModel(row)
		},
		//处理路由参数，首次跳转获取不到query参数
		handleRouterUrl(url) {
			let arr1 = url.split('?')
			if (arr1[1]) {
				let arr2 = arr1[1].split('&')
				let map = new Map()
				for (let s of arr2) {
					map.set(s.split('=')[0], s.split('=')[1])
				}
				return map
			}
		},
	}






}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-card-body{
	padding-top: 4px;

}
</style>