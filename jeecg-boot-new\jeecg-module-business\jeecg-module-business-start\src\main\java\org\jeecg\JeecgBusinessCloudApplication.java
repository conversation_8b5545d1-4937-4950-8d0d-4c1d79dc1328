package org.jeecg;

import cn.hutool.cron.CronUtil;
import icu.develop.apiwrap.WrapStore;
import icu.develop.apiwrap.annotation.EnableApiWrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;

@EnableApiWrap
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
@EnableFeignClients
//@ImportResource("classpath:config/receipt-config.xml")
public class JeecgBusinessCloudApplication implements CommandLineRunner {

    @Autowired
    private WrapStore wrapStore;

    public static void main(String[] args) {
        SpringApplication.run(JeecgBusinessCloudApplication.class, args);
    }

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void run(String... args) throws Exception {
        // API接口签名验签工具，文档地址：https://github.com/develop-mount/api-wrap
        wrapStore.putSecret("SecretKey@2024!#JHsa", "SecretKey@2024!#JHsa");

        //支持秒级别定时任务
        CronUtil.setMatchSecond(true);
        //启动定时任务
        CronUtil.start();
    }
}
