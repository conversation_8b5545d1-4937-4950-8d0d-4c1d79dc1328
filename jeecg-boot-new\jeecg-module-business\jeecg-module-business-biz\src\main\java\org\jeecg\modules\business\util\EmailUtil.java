package org.jeecg.modules.business.util;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * EmailUtil
 * <pre>
 *   发送邮件工具类
 * </pre>
 *
 * <AUTHOR>  2023/9/21 17:05
 * @version 1.0
 */
public class EmailUtil {
    public static boolean sendMail(String to, String title, String content) {
        Properties props = new Properties();
        props.setProperty("mail.host", "smtp.qq.com");
        props.setProperty("mail.transport.protocol", "SMTP");
        props.setProperty("mail.smtp.auth", "true");

        Authenticator authenticator = new Authenticator() {
            @Override
            public PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication("1105101678", "sgadtoyldqzngggg"); // 邮件账号和授权码，注意不是密码。
            }
        };

        Session session = Session.getInstance(props, authenticator);

        MimeMessage mess = new MimeMessage(session);
        try {
            mess.setFrom(new InternetAddress("<EMAIL>"));
            mess.setRecipients(Message.RecipientType.TO, to);
            //邮件标题
            mess.setSubject(title);
            //邮件内容
            mess.setContent(content, "text/html;charset=utf-8");

            Transport.send(mess);
        } catch (MessagingException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
