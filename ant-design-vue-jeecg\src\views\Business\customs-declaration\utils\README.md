# 报关单数据校验工具

这是一个用于报关单数据校验的工具库，集中管理所有校验规则，为报关单详情页面提供数据验证功能。

## 功能特性

1. 集中管理所有报关单数据校验规则
2. 支持自定义校验规则和错误消息
3. 提供友好的错误提示界面
4. 易于扩展和维护

## 使用方法

### 基本引入和使用

```javascript
import { validateAndShowResult } from '@/views/Business/customs-declaration/utils/decValidator'

// 在组件中使用
methods: {
  validateRecord() {
    // record 是报关单数据对象
    // 第二个参数是显示错误消息的回调函数
    validateAndShowResult(this.record, (errorHtml) => {
      this.$info({
        title: '关小宝',
        content: h => h('div', {
          domProps: { innerHTML: errorHtml }
        }),
        // ...其他配置
      })
    })
  }
}
```

### 集成到保存流程

建议在保存报关单数据前进行校验：

```javascript
async saveData() {
  // 先进行数据校验
  const isValid = validateAndShowResult(this.record, (errorHtml) => {
    // 显示错误信息
    this.$info({
      title: '关小宝',
      content: h => h('div', {
        domProps: { innerHTML: errorHtml }
      }),
      // ...其他配置
    })
  })

  // 如果校验不通过，中断保存
  if (!isValid) {
    return
  }

  // 继续执行保存逻辑
  // ...
}
```

## 添加新的校验规则

如需添加新的校验规则，请修改 `decValidator.js` 文件中的 `validateDecRecord` 函数：

```javascript
// 在 validateDecRecord 函数中添加新的校验规则
if (!record.someField || record.someField.trim() === '') {
	errorMsgs.push('【某字段】不能为空')
}

// 对于复杂的校验规则，可以添加专门的校验函数
if (!isValidSomeField(record.someField)) {
	errorMsgs.push('【某字段】格式不正确')
}

// 校验表体数据的某个属性
record.decLists.forEach((item, index) => {
	if (!isValidItemField(item.someField)) {
		errorMsgs.push(`第${index + 1}项商品【某字段】不符合要求`)
	}
})
```

## 注意事项

1. 校验规则应考虑兼容性，避免对不存在的属性进行操作
2. 错误消息应清晰明确，指明问题所在
3. 校验逻辑应尽量集中在本工具中，而不是分散在各个组件中
