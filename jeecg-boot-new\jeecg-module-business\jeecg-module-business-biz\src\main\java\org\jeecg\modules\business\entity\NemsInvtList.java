package org.jeecg.modules.business.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 核注单表体
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
@TableName("nems_invt_list")
public class NemsInvtList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表体流水
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 核注单流水
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long invId;

    /**
     * 委托流水号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyNumber;

    /**
     * 清单企业内部编号
     */
    private String etpsInnerInvtNo;

    /**
     * 中心统一编号 (返填 - 系统暂存时自动生成并返填）
     */
    private String seqNo;

    /**
     * 商品序号
     */
    private Integer gdsseqNo;

    /**
     * 备案序号(对应底账序号）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer putrecSeqno;

    /**
     * 自动备案序号20241106
     */
    private String autoNo;

    /**
     * 商品料号 （企业可录入，也可根据企业录入的备案序号从备案数据中获取并返填）
     */
    private String gdsMtno;

    /**
     * 商品编码 （系统自动返填。参数值如下：0-未修改1-修改2-删除3-增加）
     */
    private String hscode;

    /**
     * 商品名称 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    private String hsname;

    /**
     * 商品规格型号 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    private String hsmodel;

    /**
     * 申报计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    private String dclUnitcd;

    /**
     * 法定计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    private String lawfUnitcd;

    /**
     * 法定第二计量 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    private String secdlawfUnitcd;

    /**
     * 最终目的国
     */
    private String natcd;

    /**
     * 企业申报单价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal dclUprcamt;

    /**
     * 企业申报总价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal dclTotalamt;

    /**
     * 美元统计总金额
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal usdstatTotalamt;

    /**
     * 币制
     */
    private String dclCurrcd;

    /**
     * 法定数量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal lawfQty;

    /**
     * 第二法定数量 （当法定第二计量单位为空时，该项为非必填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal secdLawfQty;

    /**
     * 重量比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal wtsfVal;

    /**
     * 第一比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal fstsfVal;

    /**
     * 第二比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal secdsfVal;

    /**
     * 申报数量* （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
     */
    private BigDecimal dclQty;

    /**
     * 毛重
     */
    private BigDecimal grossWt;

    /**
     * 净重
     */
    private BigDecimal netWt;

    /**
     * 用途代码*
     */
    private String useCd;

    /**
     * 征免方式
     */
    private String lvyrlfModecd;

    /**
     * 单耗版本号 （成品可填。手册不填，账册由开关控制是否必填。需看单耗该字段如何定义）（E02-04）
     */
    private String ucnsVerno;

    /**
     * 报关单商品序号  （企业可录入，如果企业不录入，系统自动返填）（E02-05）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer entryGdsSeqno;

    /**
     * 归类标志
     */
    private String clymarkcd;

    /**
     * 流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer applyTbSeqnoA;

    /**
     * 申请表序号
     */
    private String applyTbSeqnoB;

    /**
     * 入库时间 （返填）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date addTime;

    /**
     * 实际过卡数量 （(SAS项目新增) 卡口抬杆后，系统根据核放单数量累计申报表商品过卡数量）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal actlPassQty;

    /**
     * 核放单已用数量 （(SAS项目新增) 已生成核放单的商品数量，用于控制核放单商品数量超量）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal passPortusedQty;

    /**
     * 备注
     */
    private String rmk;

    /**
     * 法捡类型
     */
    private String hstype;

    private String goodsId;

    /**
     * 原进口企业内部编号
     */
    private String importBillNo;

    private String ciqCode;

    private String ciqName;

    /**
     * 飞机注册号
     */
    private String aircraftRegistrationNumber;

    private String customHscode;

    private String customDname;

    private String customHsname;

    /**
     * 成交方式
     */
    private String transMode;

    /**
     * 贸易国(不显示)
     */
    private String tradeCountry;

    /**
     * 订单号
     */
    private String po;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 原产国
     */
    private String originCountry;

    /**
     * 通用字段(用于存储各种暂存值)
     */
    private String universal;

    /**
     * 剩余数量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal balanceQty;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer version;

    /**
     * 监管条件
     */
    private String supvModecd;

    /**
     * 英文描述
     */
    private String enMemo;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer packs;

    /**
     * 清单编号 （返填 - 海关审批通过后系统自动返填）
     */
    private String bondInvtNo;

    /**
     * 旧业务编号
     */
    private String applyId;

    /**
     * 出入库单表体流水号
     */
    private String stockGoodsId;
    /**
     * 出入库单编号
     */
    @TableField("SAS_STOCK_NO")
    private String sasStockNo;

    /**
     * 出入库单号
     */
//    @TableField("STORAGE_NO")
    private String storageNo;
    private Long storageDetailId;
    private String emsFlowId;

    /**
     * 修改标记代码
     */
    @TableField("MODF_MARKCD")
    private String modfMarkcd;

    /**
     * 修改标记名称
     */
    @TableField("MODF_MARKCD_NAME")
    private String modfMarkcdName;

    /**
     * 来源标识 1-境外重点料件 2-境外普通料件 3-国内采购料件 4-专账成品转入料件
     */
    private String param1;
}
