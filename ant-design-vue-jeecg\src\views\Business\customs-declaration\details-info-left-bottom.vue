<template>
    <fragment>
        <!--表体列表padding-top: 10px-->
        <tr>
            <td colspan='100' style='border-top: none;border-bottom: none'>
                <div style='text-align: left;padding-top: 10px; position: relative;'>
                    <!--顶部按钮-->
                    <div class='ant-alert ant-alert-info' >
                                <span>
                                   <button-item :readonly='disableEdit' icon='import' text='导入'/>
                                    <button-item :readonly='disableEdit' @click='handleAdd(imSignShowList)'
                                                 icon='file-add' text='新增'/>
                                    <button-item :readonly='disableEdit' icon='save'
                                                text='保存' @click='goodsSave'/>
                                    <button-item :readonly='disableEdit' @click='handleDelete'
                                                 icon='delete' text='删除'/>
                                    <button-item :readonly='disableEdit' @click='handleCopy'
                                                 icon='copy' text='复制'/>
                                    <button-item :readonly='disableEdit'  text='归类先例' @click='classifyPrecedent'/>
                                   <button-item :readonly='disableEdit' icon='arrow-up' text='上移'
                                                @click='handleMoveUp'/>
                                   <button-item :readonly='disableEdit' icon='arrow-down' text='下移'
                                                @click='handleMoveDown'/>
																	<button-item :readonly='disableEdit' icon='icon-charu'
																							 text='插入'
																							 @click='handleInsert'/>

<!--																	 <button-item :readonly='disableEdit' v-if="decHeadId" :loading="checkForGoodsLoading"-->
<!--																								@click='handleCheckForGoods'-->
<!--																								icon='check' text='价格检查(商品库检查)'/>-->
<!--																	<button-item :readonly='disableEdit' v-if="decHeadId" :loading="checkForHistoryLoading"-->
<!--																							 @click='handleCheckForHistory'-->
<!--																							 icon='check' text='价格检查(申报历史检查)'/>-->
<!--																		<button-item :readonly='disableEdit' v-if="decHeadId&&value.ieFlag=='E'"-->
<!--																								 :loading="checkForIntellectualPropertyLoading"-->
<!--																								 @click='handleCheckForIntellectualProperty'-->
<!--																								 icon='check' text='海关知识产权备案检验'/>-->

			<a-dropdown class="xz" size="small">
        <a-menu slot="overlay" size="small" :readonly='disableEdit'>
          <a-menu-item key="1" @click="handleCheckForGoods" size="small">价格检查(商品库检查)</a-menu-item>
					<a-menu-item key="2" @click="handleCheckForHistory" size="small">价格检查(申报历史检查)</a-menu-item>
					<a-menu-item key="3" @click="handleCheckNetWeightForGoods" size="small">净重检查(商品库检查)</a-menu-item>
					<a-menu-item key="4" @click="handleCheckNetWeightForHistory" size="small">净重检查(申报历史检查)</a-menu-item>
					<a-menu-item key="5" v-if="decHeadId&&value.ieFlag=='E'"
											 @click="handleCheckForIntellectualProperty"  size="small">海关知识产权备案检验</a-menu-item>
        </a-menu>
					<button-item :readonly='disableEdit' v-if="decHeadId"
							icon='check' text='数据检验'/>
      </a-dropdown>


                                <button-item :readonly='disableEdit' text='重新归类'
                                                            @click='handleHsmodelEdit'/>
                                            <button-item :readonly='disableEdit' text='归类查看'
                                                                        @click='handleHsmodelDetail'/>
                                            <button-item  text='删除享惠'
                                                @click='()=>{}'/>
																	<button-item  text='批量修改'
																								@click='handleBatchEdit'/>
                                            <button-item  text='进入批量编辑模式'
                                            @click='handleBulkEdit'/>

<!--                                    <button-item :readonly='disableEdit' @click='searchingGoodspnClick(decRecord)'-->
<!--                                                 icon='zoom-in' text='检索商品库'/>-->

<!--                                    <button-item :readonly='disableEdit' @click='handleInsert'-->
<!--                                                 icon='icon-charu'-->
<!--                                                 text='插入'/>-->
<!--                                    <button-item :readonly='disableEdit' @click="batchUpdateClick" text='批量修改'/>-->

                                </span>




                    </div>
                    <vxe-toolbar ref="toolbarRef" class="toolbar" custom ></vxe-toolbar>

<!--									<div v-for="item in dataSource " :key="item.id">{{item.id}}}</div>-->
                    <vxe-grid
                        resizable
                        :custom-config="{ storage: true }"
                        id="details-info-left-bottom-table"
                        :transfer="true"
                        border="full"
                        ref="xTable2"
                        height="280"
                        size="mini"
                        class="sortable-row-demo"
                        :show-overflow=true
                        :data="dataSource"
                        :columns="columns"
                        :highlight-current-row="true"
                        @header-cell-click="defineTableEvent"
                        @cell-click="cellClickEvent"
                        @cell-dblclick="cellDBLClickEvent"
                        @cell-selected="cellSelected"
                        @checkbox-all="this.handleVxeCheckboxAll"
                        @checkbox-change="this.handleVxeCheckboxChange"
                        @checkbox-range-change="checkboxRangeChange"
                        :checkbox-config="{ trigger: 'col', highlight: true, range: true }"
                        row-id="item"
                    >
                        <vxe-table-column width="60">
                            <template v-slot>
                              <span class="drag-btn">
                                <i class="vxe-icon--menu"></i>
                              </span>
                            </template>
                            <template v-slot:header>
                                <vxe-tooltip v-model="showHelpTip1" content="按住后可以上下拖动排序！" enterable>
                                    <i class="vxe-icon--question" @click="showHelpTip1 = !showHelpTip1"></i>
                                </vxe-tooltip>
                            </template>
                        </vxe-table-column>
                    </vxe-grid>

									<!-- 统计区域 -->
									<div class="statistics-container" style="margin-top: 10px; padding: 2px; background-color: #f5f5f5; border: 1px solid #d9d9d9; font-size: 12px;">
										<span style="margin-right: 20px;">
												<span style="color: #000; font-weight: bold;">总商品条数: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalItems }}</span>
										</span>
										<span style="margin-right: 20px;">
												<span style="color: #000; font-weight: bold;">成交数量: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalCount }}</span>
										</span>
										<span style="margin-right: 20px;">
												<span style="color: #000; font-weight: bold;">总价: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalAmount }}</span>
										</span>
										<span style="margin-right: 20px;">
												<span style="color: #000; font-weight: bold;">总件数: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalPackages }}</span>
										</span>
										<span style="margin-right: 20px;">
												<span style="color: #000; font-weight: bold;">总毛重: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalGrossWeight }}</span>
										</span>
										<span>
												<span style="color: #000; font-weight: bold;">总净重: </span>
												<span style="color: #1890ff; font-weight: bold;">{{ statisticsData.totalNetWeight }}</span>
										</span>
									</div>

                </div>
            </td>
        </tr>
        <!--表体-->
        <tr>
            <td colspan='100'>
                <table class='my-table'>
                    <!--表体表单-->
                    <fragment>
                        <tr>
                            <input-item label='项号' readonly
                                        v-model='record.item'/>
                            <input-item  :id="62" v-model='record.recordItem' :readonly='!record.id || recordItemType'
                             :vfocusNum='focusIndex===62'
                                        label='备案序号' @focusIndexDate="focusIndexMethod" />

                            <input-item :id="65" :readonly='!record.id'  :styleSrt="styleSrt" maxLength = 10
                                        label='商品编号' :focusIndex='focusIndex' :vfocusNum='focusIndex===65'
                                        v-model='record.hscode' @focusIndexDate="focusIndexMethod"/> <!--@change='hscodeChange'-->

                            <input-item :readonly='disableEdit || !record.id' label='监管类别名称' readonly
                                        v-model='record.ciqName' :value-width='3' />

													<td colspan='1' style="width: 2%">
														<a-button :id="66" :disabled='disableEdit || !record.id' title="监管类别"
																			v-focus='focusIndex===66' class="buttonStyle"
																			@click="show.decCiqName = !show.decCiqName" block  size='small'
																			type='primary' style='flex: 1;border-radius: 50%'>
															<a-icon type="ellipsis" style="font-size: 18px;margin-left: -8px" />
														</a-button>
													</td>

                        </tr>
                        <tr>
                            <input-item :id="644" :readonly='disableEdit || !record.id' :value-width='3'
																				:styleSrt="styleSrt"
                                        label='商品名称' :focusIndex='focusIndex' :vfocusNum='focusIndex===64'
                                        v-model='record.hsname' @focusIndexDate="focusIndexMethod"/>

                            <input-item :readonly='true' :value-width='6'
                                        @change='hsmodelChange' label='规格型号'
                                        v-model='record.hsmodel' @dbclick="showClick"/>

                          	<a-button ref="refButton" :disabled='disableEdit || !record.id' title="规格型号"
                                      @click="showClick"
                                      block ghost class="buttonStyle" @focus="focusButton"
                                      size='small' type='primary' style='flex: 1;display: none' v-focus='focusIndex===67'>
                                ...
                            </a-button>
                        </tr>
                        <tr>
                            <input-item :id="68" :readonly='disableEdit || !record.id' type="number"
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===68'
                                        :styleSrt="styleSrt" @change='returnBackFnCount'
                                        label='成交数量' @keyup.enter='totalEnter' @blur="unitButtonBlur(1)"
                                        v-model='record.goodsCount' @focusIndexDate="focusIndexMethod"/>


                            <decSelect-Item :id="70" :focusIndex='focusIndex' :vfocusNum='focusIndex===70'
                                            @focusIndexDate="focusIndexMethod" @search="unitCodeSearch"
                                            :readonly='disableEdit || !record.id' label='成交计量单位'
																						:styleSrt="styleSrt"
                                            dict-key='erp_units,name,code,1=1'
                                            :selectWidth="9.2" :styleType="1"
                                            v-model='record.unitCode'></decSelect-Item>
                            <input-item :id="71" :readonly='disableEdit || !record.id' type="number"
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===71'
                                        @focusIndexDate="focusIndexMethod" @blur="unitButtonBlur(2)"
                                        label='单价' @keyup.enter='totalButton'
																				:styleSrt="styleSrt"
                                        v-model='record.price'/>
                            <input-item :id="72" :readonly='disableEdit || !record.id' type="number"
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===72'
                                        @focusIndexDate="focusIndexMethod" @blur="unitButtonBlur(3)"
                                        label='总价' @keyup.enter='unitButton'
																				:styleSrt="styleSrt"
                                        v-model='record.total'/>


                            <decSelect-Item :id="73" :focusIndex='focusIndex' :vfocusNum='focusIndex===73'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='2' label='币制'
                                            :dict-key='dictKeys.BZDM'
                                            :selectWidth="25" :styleSrt="styleSrt"
																						:styleType="5"
                                            v-model='record.currencyCode'></decSelect-Item>
                        </tr>
                        <tr>
                            <input-item :id="74" :readonly='disableEdit || !record.id' type="number"
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===74'
                                        @focusIndexDate="focusIndexMethod"
																				:styleSrt="styleSrt"
                                        label='法定第一数量' v-model='record.count1'/>

                            <decSelect-Item :id="75" :focusIndex='focusIndex' :vfocusNum='focusIndex===75'
                                            @focusIndexDate="focusIndexMethod"
                                            label='法定第一计量单位' dict-key='erp_units,name,code,1=1'
                                            :selectWidth="9.2" :styleType="1" v-model='record.unit1'
                                            :readonly='true' @search='lawfUnitcdDlur'></decSelect-Item><!--readonly-->
                            <input-item :id="76" :readonly='disableEdit || !record.id'
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===76'
                                        @focusIndexDate="focusIndexMethod"
                                        label='加工成品单耗版本号' v-model='record.exgVersion'/>
                            <input-item :id="77" :readonly='disableEdit || !record.id' label='货号'
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===77'
                                        @focusIndexDate="focusIndexMethod"
                                        v-model='record.tmp002'/>

                            <decSelect-Item :id="78" :focusIndex='focusIndex' :vfocusNum='focusIndex===78'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='2' label='最终目的国(地区)'
                                            :dict-key='dictKeys.GBDQDEC'
                                            :selectWidth="25" :styleType="2"
                                            v-model='record.destinationCountry'></decSelect-Item>
                        </tr>
                        <tr>
                            <input-item :id="79"
																				:readonly='count2Readonly?true:(disableEdit || !record.id)&&count2Readonly'
                                        type="number"
                                        :focusIndex='focusIndex' :vfocusNum='focusIndex===79'
                                        @focusIndexDate="focusIndexMethod"
                                        label='法定第二数量' v-model='record.count2'/>

                            <decSelect-Item :id="80" :focusIndex='focusIndex' :vfocusNum='focusIndex===80'
                                            @focusIndexDate="focusIndexMethod"
                                            label='法定第二计量单位' dict-key='erp_units,name,code,1=1'
                                            :selectWidth="9.2" :styleType="1" v-model='record.unit2'
                                            @search='lawfUnitcdDlur'
																						:readonly='true'
																						></decSelect-Item><!--readonly-->

                            <decSelect-Item :id="81" :focusIndex='focusIndex' :vfocusNum='focusIndex===81'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width="imSignShowList?2:4"
                                            label='原产国(地区)'
																						:styleSrt="styleSrt"
                                            :dict-key='dictKeys.GBDQDEC'
                                            :selectWidth="26.6" :styleType="2"
                                            v-model='record.desCountry'></decSelect-Item>
                            <td>
                                <a-button :disabled='disableEdit || !record.id' :value-width="1"  class="buttonStyle w-100"
                                          size='small'  title="协定享惠"
                                          type='primary' @click="concertedReciprocal" >
                                    协定享惠
                                </a-button>
                            </td>

                            <decSelect-Item :id="82" :focusIndex='focusIndex' :vfocusNum='focusIndex===82'
                                            @focusIndexDate="focusIndexMethod" v-if="imSignShowList"
                                            :readonly='disableEdit || !record.id' :value-width='2' label='原产地区'
                                            :dict-key='dictKeys.GBDQDEC'
                                            :selectWidth="18" :styleType="2"
                                            v-model='record.origPlaceCode'></decSelect-Item>
                        </tr>
                        <tr>
                            <td colspan='2'>
                                <a-icon @click='listBodyOther = !listBodyOther'
                                        type='right-circle'
                                        v-show='!listBodyOther'/>
                                <a-icon @click='listBodyOther = !listBodyOther'
                                        type='down-circle'
                                        v-show='listBodyOther'/>
                            </td>
                            <decSelect-Item :id="83" :focusIndex='focusIndex' :vfocusNum='focusIndex===83'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='2'
                                            :label='imSignShowList?`境内目的地`:`境内货源地`' :dict-key='dictKeys.GNDQ'
																						:styleSrt="styleSrt"
                                            :selectWidth="35.8" :styleType="1"
                                            v-model='record.districtCode'></decSelect-Item>

                            <decSelect-Item :id="84" :focusIndex='focusIndex' :vfocusNum='focusIndex===84'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='2' no-label
                                            :dict-key='dictKeys.XZQH'
																						:styleSrt="styleSrt"
                                            :selectWidth="17.7" :styleType="1"
                                            v-model='record.destCode'></decSelect-Item>

                            <decSelect-Item :id="85" :focusIndex='focusIndex' :vfocusNum='focusIndex===85'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='3' label='征免方式'
                                            :dict-key='dictKeys.ZJMSFS'
                                            :selectWidth="26.9" :styleType="1"
																						:styleSrt="styleSrt"
                                            v-model='record.faxTypeCode'></decSelect-Item>

                        </tr>
                    </fragment>
                    <!--表体表单折叠部分-->
                    <fragment>
                        <tr v-show='listBodyOther'>

                            <input-item  :readonly='disableEdit || !record.id' :value-width='value.ieFlag=="I"?7:8' readonly

                                        label='检验检疫货物规格' v-model='record.txtGoodsSpecStr'/>
                            <td colspan='1'>
                                <a-button :id="86" :disabled='disableEdit || !record.id' title="检验检疫货物规格"
                                          class="buttonStyle " size='small' @click="decGoodsSpecClick"
                                          type='primary' v-focus='focusIndex===86'>
                                    ...
                                </a-button>
                            </td>
                            <td v-if="value.ieFlag=='I'">
                                <a-button :disabled='disableEdit || !record.id' class="buttonStyle w-100"
                                        size='small'
                                        title="产品资质" type='primary' @click="show.decGoodsLimittype = !show.decGoodsLimittype">
                                    产品资质
                                </a-button>
                            </td>


                        </tr>
                        <tr v-show='listBodyOther'>
                                <input-item  v-model='record.produceDate'
                                    :value-width='8' label='生产日期'/>
                                <td v-if="value.ieFlag=='I'">
                                    <a-button  :disabled='disableEdit || !record.id' class="buttonStyle w-100"
                                          size='small'  title="危险货物信息"
                                          type='primary' @click="show.decDangInfo = !show.decDangInfo">
                                    危险货物信息
                                    </a-button>
                                </td>
                                <td v-if="value.ieFlag=='E'">
                                    <a-button :disabled='disableEdit || !record.id' class="buttonStyle w-100"
                                            size='small'
                                            title="产品资质" type='primary' @click="show.decGoodsLimittype = !show.decGoodsLimittype">
                                        产品资质
                                    </a-button>
                                </td>
                            </tr>
                        <tr v-show='listBodyOther'>
                            <input-item :readonly='disableEdit || !record.id' :value-width='3'

                                        label='货物属性' v-model='record.goodsAttrStr'/>
                            <td>
                                <a-button :id="87" :disabled='disableEdit || !record.id' title="货物属性"
                                          size='small'  @click="show.goodsAttrType = !show.goodsAttrType"
                                          type='primary' v-focus='focusIndex===87' class="buttonStyle">
                                    ...
                                </a-button>
                            </td>

                            <decSelect-Item :id="88" :focusIndex='focusIndex' :vfocusNum='focusIndex===88'
                                            @focusIndexDate="focusIndexMethod"
                                            :readonly='disableEdit || !record.id' :value-width='3' label='用途'
                                            :dict-key='dictKeys.YT'
                                            :selectWidth="26.9" :styleType="1"
                                            v-model='record.purpose'></decSelect-Item>
                            <td v-if="value.ieFlag=='I'">
                                <a-button :disabled='disableEdit || !record.id' class="buttonStyle w-100"
                                          size='small'  title="商品单据"
                                          type='primary' @click="show.decgoodsdocumentsType = !show.decgoodsdocumentsType">
                                          商品单据
                                </a-button>
                            </td>
                            <td v-if="value.ieFlag=='E'">
                                <a-button  :disabled='disableEdit || !record.id' class="buttonStyle w-100"
                                        size='small'  title="危险货物信息"
                                        type='primary' @click="show.decDangInfo = !show.decDangInfo">
                                危险货物信息
                                </a-button>
                            </td>
                        </tr>
                    </fragment>
                </table>
                <!--产品资质-->
                <dec-goods-limittype ref='decGoodsLimittype' :show='show.decGoodsLimittype':imSignShowList="imSignShowList"
                                     v-model='record'></dec-goods-limittype>
                <!--危险货物信息-->
                <Dec-Dang-Info :show='show.decDangInfo' v-model='record'></Dec-Dang-Info>
                <!--检验检疫货物规格-->
                <Dec-Goods-Spec :show='show.decGoodsSpec' v-model='record'
                                @keyFromPromise="keyFromPromise" @handleDecGoodsSpec = "handleDecGoodsSpec"></Dec-Goods-Spec>
                <!--检验检疫名称-->
                <Dec-Ciq-Name ref='decCiqName' :show='show.decCiqName' :txtrequestCertType.sync='record'
                              v-model='record.hscode' @keyFromPromise="keyFromPromise"></Dec-Ciq-Name>
                <!--税则信息-->
           			<Dec-Teriff ref='decTeriff' :show='show.decTeriff' :txtrequestCertType.sync='record'
                            v-model='record.hscode' @keyFromPromise="keyFromPromise"></Dec-Teriff>
                <!--规格型号编辑-->
                <Dec-Hsmodel ref='decHsmodel' :show='show.decHsmodel' :showPage.sync='show'
                             :txtrequestCertType.sync='record' :hintDialog.sync='decHsmodels'
                             :imSignShowList="imSignShowList"
                             v-model='record.hsmodel' @keyFromPromise="keyFromPromise"></Dec-Hsmodel>
<!--							<Dec-Hsmodel1 ref='decHsmodel' :show='show.decHsmodel' :showPage.sync='show'-->
<!--													 :txtrequestCertType.sync='record' :hintDialog.sync='decHsmodels'-->
<!--													 :imSignShowList="imSignShowList"-->
<!--													 @keyFromPromise="keyFromPromise1"></Dec-Hsmodel1>-->
                <!--货物属性-->
                <Dec-Goods-Attr ref='decGoodsAttrs' :show='show.goodsAttrType'
                                v-model='record.goodsAttr' @goodsAttrStr="goodsAttrStr" @keyFromPromise="keyFromPromise"></Dec-Goods-Attr>

                <concerted-Reciprocale :show='concertedReciprocalType' :dataSource="dataSource" v-model='record'  :txtRecord='record.decEcoRelation' @txtRecordTxtChange="txtRecordTxtChange"></concerted-Reciprocale>
                <!--归类先例-->
                <classifyPrecedent ref="classifyPrecedentList" :goodsPnGridOptions="classifyGridOptions"
                                  @classifyPromise="classifyPromise"></classifyPrecedent>
							<!--选择历史商品模态框-->
							<DecHsnameChoose ref="DecHsnameChoose" @decHsnameChooseOk="decHsnameChooseOk"
															 @decHsnameChooseNo="decHsnameChooseNo"
															 :optUnitSocialCode="optUnitSocialCode"
															 :optUnitId="optUnitId"
															 :optUnitName="optUnitName"></DecHsnameChoose>

							<!--批量修改规格型号-->
							<DecListBulkEdit ref="DecListBulkEdit" :IE_FLAG="IE_FLAG" @Bulk-event="savemore"></DecListBulkEdit>
							<!--批量修改商品模态框-->
							<DecProductInformation ref="DecProductInformation"
							:IE_FLAG="IE_FLAG" :declareUnitSocialCode="declareUnitSocialCode"
							:hscode="decListqueryParam.hscode" :hsname="decListqueryParam.hsname" @decBuckHsnameChooseOk="decBuckHsnameChooseOk"></DecProductInformation>
                <!--批量修改规格型号编辑-->
                <Dec-Hsmodel ref='bulkEditDecHsmodel' v-model="bulkEditHsmodel" :hintDialog.sync='decBulkHsmodels' :imSignShowList="imSignShowList"
								:show='selectedCell.hsmodel' :showPage.sync='selectedCell' :txtrequestCertType.sync='record' @keyFromPromise="bulkkeyFromPromise"
								></Dec-Hsmodel>

                <!--总价单价数量联动modal-->
                <a-modal v-model="show.unitTotal" title="报关单修改单价/总价?" width='20%'>
                    <a-button ref="totalButtonRef" class='totalButton' @keyup.right="keyupLeft" @click="unitButton(1)">
                        修改单价
                    </a-button>
                    <a-button ref="totalButton1Ref" class='totalButton1' @keyup.left="keyupright"
                              @click="totalButton(1)">
                        修改总价
                    </a-button>

                    <template slot="footer">
                        <a-button @click="handleCancel">取消</a-button>
                    </template>
                </a-modal>
                <!--批量修改-->
                <a-modal v-model="show.batchUpdate" :maskClosable= "maskClosable" title="批量修改报关单表体信息" width='40%' v-enterToNext>
                    <a-card :bordered="false" :bodyStyle="{padding:'0px'}">
                        <table class='inlineTable'>
                            <tr class='inlineTd'>

<!--                                <decSelect-Item-->
<!--                                    styleSrt="height:25px;font-size: 13px;"-->
<!--                                    :readonly='disableEdit || !record.id' :value-width='3' label='币制'-->
<!--                                    :dict-key='dictKeys.BZDM'-->
<!--                                    :selectWidth="45" :styleType="2"-->
<!--                                    v-model='batchRecord.currencyCode'></decSelect-Item>-->

															<select-item
																dict-key="erp_currencies,name,code,currency,1=1"
																:readonly="disableEdit || !record.id"
																iprop="dclCurrcd"
																label="币制"
																v-model="batchRecord.currencyCode"
															/>

                            </tr>
                            <tr class='inlineTd'>
                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3' label='原产国(地区)'
                                                :dict-key='dictKeys.GBDQDEC'
                                                :selectWidth="45" :styleType="2"
                                                v-model='batchRecord.desCountry'></decSelect-Item>
                            </tr>
                            <tr class='inlineTd'>
                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3'
                                                :label='imSignShowList?`境内目的地`:`境内货源地`' :dict-key='dictKeys.GNDQ'
                                                :selectWidth="45" :styleType="1"
                                                v-model='batchRecord.districtCode'></decSelect-Item>
                            </tr>
                            <tr class='inlineTd'>
<!--                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"-->
<!--                                                :readonly='disableEdit || !record.id' :value-width='3' label='目的地代码'-->
<!--                                                :dict-key='dictKeys.XZQH'-->
<!--                                                :selectWidth="45" :styleType="1"-->
<!--                                                v-model='batchRecord.destCode'></decSelect-Item>-->
                            </tr>
                            <tr class='inlineTd'>
                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3' label='征免方式'
                                                :dict-key='dictKeys.ZJMSFS'
                                                :selectWidth="45" :styleType="1"
                                                v-model='batchRecord.faxTypeCode'></decSelect-Item>
                            </tr>
                            <tr class='inlineTd'>
                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3' label='最终目的国(地区)'
                                                :dict-key='dictKeys.GBDQDEC'
                                                :selectWidth="45" :styleType="2"
																								:styleSrt="styleSrt"
                                                v-model='batchRecord.destinationCountry'></decSelect-Item>
                            </tr>
<!--                            <tr class='inlineTd'>
                                <td :colspan='1' :title='规格型号'
                                    class='single-td' >
                                    规格型号
                                </td>
                                <td :colspan='3'>
                                    <decSelect-Item styleSrt="height:25px;font-size: 13px;width:100%"
                                                    :readonly='disableEdit || !record.id' :value-width='5'
                                                    :hsModelPPLX="hsModelPPLX"
                                                    dictKey='PPLX'
                                                    :selectWidth="45" :styleType="2"
                                                    v-model='batchRecord.destinationPPLX'></decSelect-Item>
                                    <decSelect-Item styleSrt="height:25px;font-size: 13px;width:100%"
                                                    :readonly='disableEdit || !record.id' :value-width='3'
                                                    :hsModelPPLX="hsModelPPLX"
                                                    dictKey='XHQK'
                                                    :selectWidth="45" :styleType="2"
                                                    v-model='batchRecord.destinationXHQK'></decSelect-Item>
                                </td>
                            </tr>-->
                            <tr class='inlineTd'>
                                <input-item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3' label='货物属性'
                                                :selectWidth="45" :styleType="2"
                                                v-model='batchRecord.goodsAttr'></input-item>
                                <td>
                                    <a-button :id="87" :disabled='disableEdit || !record.id' title="货物属性"
                                              @click="show.goodsAttrType1 = !show.goodsAttrType1" block ghost size='small'
                                              type='primary' v-focus='focusIndex===87' class="buttonStyle">
                                        ...
                                    </a-button>
                                </td>
                                <Dec-Goods-Attr ref='decGoodsAttrs' :show='show.goodsAttrType1' :txtrequestCertType.sync='batchRecord'
                                                v-model='batchRecord.goodsAttr' @goodsAttrStr="goodsAttrStr"
                                                @keyFromPromise="keyFromPromise"></Dec-Goods-Attr>
                            </tr>
                            <tr class='inlineTd'>
                                <decSelect-Item styleSrt="height:25px;font-size: 13px;"
                                                :readonly='disableEdit || !record.id' :value-width='3' label='用途'
                                                :dict-key='dictKeys.YT'
                                                :selectWidth="45" :styleType="2"
                                                v-model='batchRecord.purpose'></decSelect-Item>
                            </tr>
                        </table>
                    </a-card>
                    <template slot="footer">
                        <a-button type="primary" @click="handleCancel">取消</a-button>
                        <a-button type="primary" @click="batchUpdateCancel">确认</a-button>
                    </template>
                </a-modal>


								<!-- 批量修改商品excel编辑 -->
								<a-modal :centered="true"
											:visible="show.bulkEdit"
											:maskClosable= "maskClosable"
											@cancel="show.bulkEdit=false"
											@ok="acceptClick"
											cancel-text=''
											title="批量修改"
											width="1400px"
											>
								<a-card :bodyStyle="{padding:'0px'}" :bordered="false">
									<div>
											<div class="single" >
											<a-button-group>
												<a-button size="smal" @click="historicalselection">从历史商品选择</a-button>
												<a-button size="smal" @click="clearChineseName">清除非中文品名</a-button>
												<a-button size="smal" @click="deleterow">删除选中行</a-button>
											</a-button-group>
											<a-input v-model="insertrow" class="single bntinsert">
												<a-button slot="addonBefore" class="insert" size="smal" @click="addrow">插入</a-button>
												<span  slot="addonAfter">行</span>
											</a-input>
											</div>
											<div class="single" >
												<div class="single mg10"> 当前共<span class="titleFont" >{{rowNumber_sum}}</span>项商品</div>
												<div class="single mg10"> 成交数量:<span class="titleFont" >{{gQty_sum}}</span></div>
												<div class="single mg10"> 总价:<span class="titleFont" >{{declTotal_sum}}</span></div>
												<div class="single mg10"> 净重:<span class="titleFont" >{{NetWeight_sum}}</span></div>
												<div class="single mg10"> 法一数量:<span class="titleFont">{{qty1_sum}}</span></div>
												<div class="single mg10"> 法二数量:<span class="titleFont">{{qty2_sum}}</span></div>
										</div>
											<div id="spreadsheetapp" ref="spreadsheet" />
										</div>
								</a-card>
							</a-modal>
                <!-- 商品单据 -->
                 <dec-goods-documents ref="decgoodsdocuments" :goods="record" :show='show.decgoodsdocumentsType'></dec-goods-documents>
							<!-- 表体批量修改 -->
							<dec-list-batch-edit ref="decListBatchEditRef" @batchUpdate="handleBatchUpdate" />
            </td>
        </tr>
    </fragment>
</template>
<script>
		import jexcel from 'jexcel'
		import 'jexcel/dist/jexcel.css'
    import InputItem from "@views/declaration/component/m-dec-table-input-item"
    import SelectItem from "@views/declaration/component/m-table-select-item"
    import DateItem from "@views/declaration/component/m-table-date-item"
    import ButtonItem from "@views/declaration/component/m_table_top_button_item"
    import {Fragment} from 'vue-fragment'
    import {singleOperator} from '@views/Business/mixin/single-operator'
    import DecGoodsLimittype from '@views/Business/component/modal/dec-goods-limittype'
    import DecDangInfo from '@views/Business/component/modal/dec-Dang-Info'
    import DecGoodsSpec from '@views/Business/component/modal/dec-goods-spec'
    import DecCiqName from '@views/Business/component/modal/dec-ciq-name'
    import DecTeriff from '@views/Business/component/modal/dec-teriff'
    import DecHsmodel from '@views/Business/component/modal/dec-hsmodel'
    import DecHsmodel1 from '@views/Business/component/modal/dec-hsmodel1'
    import DecGoodsAttr from '@views/Business/component/modal/dec-goods-attr'
    import DecGoodsDocuments from '@views/Business/component/modal/dec-goods-documents'
    import Vue from 'vue';
    import decSelectItem from './m-table-select-item'
    import concertedReciprocale from '@views/Business/component/modal/concerted-reciprocale'
    import classifyPrecedent from '@views/Business/component/modal/classify-precedent'
    import mTabledecSelectItem from '@views/declaration/component/m-table-select-item-modal'
		import DecHsnameChoose from '@views/Business/component/modal/DecHsnameChoose'
		import DecListBulkEdit from '@views/Business/component/modal/DecListBulkEdit'
		import DecProductInformation from '@views/Business/component/modal/DecProductInformation'
		import {ajaxGetDictItems} from "@/api/api";
		import DecListBatchEdit from "@/views/Business/customs-declaration/components/DecListBatchEdit.vue";
		import {number_format} from "@/utils/util";

    export default {
        mixins: [singleOperator],
        name: "details-info-left-bottom",
        components: {
					units: [],
            ButtonItem,
            SelectItem,
            InputItem,
            DateItem,
            DecGoodsLimittype,
            DecDangInfo,
            DecGoodsSpec,
            DecCiqName,
            DecTeriff,
            DecHsmodel,
            DecHsmodel1,
            DecGoodsAttr,
            Vue,
            decSelectItem,
            Fragment,
            concertedReciprocale,
            classifyPrecedent,
            mTabledecSelectItem,
						DecHsnameChoose,
						DecProductInformation,
						DecListBulkEdit,
                        DecGoodsDocuments,
					DecListBatchEdit
        },
        data() {
            return {
							styleSrt: 'background:#FAFFBD',
							spreadsheet:null,
							BrandType:[{ id: "0", name: "无品牌",  synonym: ["0-无品牌"] },
								{ id: "1", name: "境内自主品牌", synonym: ["1-境内自主品牌"] },
								{ id: "2", name: "境内收购品牌", synonym: ["2-境内收购品牌"] },
								{ id: "3", name: "境外品牌(贴牌生产)",synonym: ["3-境外品牌(贴牌生产)"] },
								{ id: "4", name: "境外品牌(其它)",synonym: ["4-境外品牌(其它)"] }],
							BenefitSituation : [{ id: "0", name: "不享受优惠", synonym: ["0-不享受优惠"] },
								{ id: "1", name: "享受优惠", synonym: ["1-享受优惠"] },
								{ id: "2", name: "不能确定", synonym: ["2-不能确定"] },
								{ id: "3", name: "不适用于进口", synonym: ["3-不适用于进口"] }],
							checkForIntellectualPropertyLoading:false,
							checkForHistoryLoading:false,
							checkForGoodsLoading:false,
                url:{
                    importExcelUrl:"/dcl/tool/importDecListUpdateFile",
                    templateUpUrl:'/dcl/报关单表体导入更新模板.xlsx',
                },
								unitList:[],
								currencieList:[],
								GBDQDECList:[],
								GZJMSFSList:[],
								erpunitList:[],
								districtList:[],
								YTList:[],
								XZQHList:[],
								gQty_sum:'0',//成交数量
								rowNumber_sum:0,//多少商品
								declTotal_sum:'0',//总价
								NetWeight_sum:'0',//净重
								qty1_sum:'0',//法一数量
								qty2_sum:'0',//法二数量
								GoodList:[],
								insertrow:'',
								stopChange:false,
								setValueFlg:false,
								decListqueryParam:{},
								selectedCell:{},
								decBulkHsmodels:{
									decHsmodelDialog: ''
								},
								bulkEditHsmodel:'',
                hsModelPPLX: "hsModelPPLX",
                dictOptions: {

                    takeSign: [
                        {text: '使用单位', value: "11"},
                        {text: '创建人2员', value: "00"},
                        {text: '创建人员', value: "66"},
                        {
                            value: '22',
                            text: 'aa',
                        },


												{
                            value: '33',
                            text: 'ab',
                        },
                    ],
                    takeSign1: [
                        {text: 'zz', value: "11"},
                        {text: 'xx', value: "22"},
                        {text: 'cc', value: "33"},
                    ],
                },
                dataSourcedd: [
                    {
                        value: '11',
                        text: 'aa',
                    },
                    {
                        value: '22',
                        text: 'ab',
                    },
                    {
                        value: '33',
                        text: 'cc',
                    },
                    {
                        value: '44',
                        text: 'dd',
                    },
                ],
                dataSourceType: [],
                focusIndex: 0, //用来存放下一个应该聚焦的index值
                inputs: [{
                    val: 1
                }, {
                    val: 2
                }, {
                    val: 3
                }, {
                    val: 4
                }],
                dictKeys: {

                    // 运输方式
                    YSFS: 'YSFS2',
                    // 关区代码
                    GQDM: 'GQDM',
                    // 国别地区
                    GBDQ: 'GBDQ',
                    // GBDQDEC: 'erp_countries,name,code,isenabled=1',
                    GBDQDEC: 'GBDQ-DEC',
                    // 监管方式
                    JGFS: 'JGFS2',
                    //征免方式
                    ZJMSFS: 'ZJMSFS',
                    //征免性质
                    ZMXZ: 'ZMXZ',
                    // 港口
                    GKDM: 'GKDM',
                    GKDMDEC: 'GKDM-DEC',
                    // 成交方式
                    CJFS: 'CJFS',
                    // 价格类型
                    JGLX: 'JGLX',
                    // 币制代码
                    // BZDM: 'erp_currencies,name,currency,1=1 order by currency_order desc',
                    BZDM: 'BZDM',
                    // 包装种类
                    BZZL: 'BZZL',
                    // 国内口岸
                    GNKA: 'GNKA',
                    // 检疫机关
                    JYJG: 'JYJG',
                    // 报关单类型
                    BGDLX: 'BGDLX',
                    // 集装箱规格
                    JZXGG: 'JZXGG',
                    //
                    GNDQ: 'erp_districts,name,code,del_Flag=0',
									  XZQH:'XZQH',
                    //用途
                    YT: 'YT'
                },
                decRecord: {},
                batchRecord: {
                    faxTypeCode: '',
                    destCode: '',
                    districtCode: '',
                    desCountry: '',
                    currencyCode: '',
                },
                record: {
                    // 报关明细流水号
                    id: '',
                    // 委托单号
                    applyNumber: '',
                    // 报关流水号 与报关表头关联
                    decId: '',
                    // 项号
                    item: '',
                    // 备案序号
                    recordItem: '',
                    // 商品编码 税号
                    hscode: '',
                    // 商品名称 申报品名
                    hsname: '',
                    // 检验检疫编码
                    ciqCode: '',
                    // 检验检疫名称
                    ciqName: '',
                    // 规格型号 申报要素
                    hsmodel: '',
                    // 成交数量
                    goodsCount: '',
                    // 成交单位
                    unitCode: '',
                    // 原产国
                    desCountry: '',
                    // 单价
                    price: '',
                    // 总价
                    total: '',
                    // 币制
                    currencyCode: '',
                    // 征免方式
                    faxTypeCode: '',
                    // 法定单位
                    unit1: '',
                    // 法定数量
                    count1: '',
                    // 第二法定单位
                    unit2: '',
                    // 第二法定数量
                    count2: '',
                    // 最终目的国
                    destinationCountry: '',
                    // 原产地区代码
                    origPlaceCode: '',
                    // 用途代码
                    purpose: '',
                    // 境内目的地/境内货源地
                    districtCode: '',
                    districtName: '',
                    // 目的地代码 境内目的地/境内货源地辅助字段
                    destCode: '',
                    destCodeName: '',
                    // 货物属性代码
                    goodsAttr: '',
                    // 货物属性代码显示
                    goodsAttrStr: '',
                    //检验检疫货物规格
                    txtGoodsSpecStr: '',
                    // 成份/原料/组份 检验检疫货物规格
                    stuff: '',
                    // 货物规格 检验检疫货物规格
                    goodsSpec: '',
                    // 货物型号 检验检疫货物规格
                    goodsModel: '',
                    // 货物品牌 检验检疫货物规格
                    goodsBrand: '',
                    // 产品有效期 格式：yyyyMMdd 检验检疫货物规格
                    prodValidDt: '',
                    // 生产日期 格式：yyyy-MM-dd,多个日期用英文半角分号分隔 检验检疫货物规格
                    produceDate: '',
                    // 生产批号 检验检疫货物规格
                    prodBatchNo: '',
                    // 生产单位注册号 检验检疫货物规格
                    mnufctrRegNo: '',
                    // 生产单位名称 检验检疫货物规格
                    mnufctrRegName: '',
                    // 产品保质期 检验检疫货物规格
                    prodQgp: '',
                    // 境外生产企业名称 检验检疫货物规格
                    engManEntCnm: '',
                    // 非危险化学品 危险货物信息
                    noDangFlag: '',
                    // UN编码 危险货物信息
                    uncode: '',
                    // 危险货物名称 危险货物信息
                    dangName: '',
                    // 危包类别 危险货物信息
                    dangPackType: '',
                    // 危包规格 危险货物信息
                    dangPackSpec: '',
                    //产品资质
                    goodsLimitType: '',
                    //表体状态字段
                    opt: '',
                    //表体监管要求
                    hstype: '',
                },
                // 显示标题折叠项
                listBodyOther: false,
                // selectedRowKeys
                selectedRowKeys: [],
                selectedRows: [],
                // 列表
                dataSource: [],
                // 列表列指示
                columns: [
                    {type: 'checkbox', width: 50},
                    // {title: '项号', field: 'item', type: 'seq', width: 50},
                    {title: '项号', field: 'item', width: 50},
                    {
                        title: '备案序号',
                        width: '80px',
                        field: 'recordItem',
                    },
                    {
                        title: '商品编码',
                        width: '95px',
                        field: 'hscode',
                    },
                    {
                        title: '监管类别名称',
                        width: '170px',
                        field: 'ciqName',
                    },
                    {
                        title: '商品名称',
                        width: '110px',
                        field: 'hsname',
                    },
                    {
                        title: '规格',
                        width: '170px',
                        field: 'hsmodel',
                    },
                    {
                        title: '成交数量',
                        width: '80px',
                        field: 'goodsCount',
                    },
                    {
                        title: '成交单位',
                        width: '80px',
                        field: 'unitCode',
                        formatter: this.formatUnitCode
                    },
                    {
                        title: '单价',
                        width: '80px',
                        field: 'price',
											formatter: function ({ cellValue, row, column }) {
												return Number(cellValue).toFixed(4)
											}
                    },
                    {
                        title: '总价',
                        width: '90px',
                        field: 'total',
											formatter: function ({ cellValue, row, column }) {
												return Number(cellValue).toFixed(4)
											}
                    },
                    {
                        title: '币制',
                        width: '80px',
                        field: 'currencyCode',
                        formatter: this.formatCurrencyCode
                    },
                    {
                        title: '原产国(地区)',
                        width: '100px',
                        field: 'desCountry',
                        formatter: this.formatDesCountry
                    },
                    {
                        title: '最终目的国(地区)',
                        width: '130px',
                        field: 'destinationCountry',
                        formatter: this.formatDestinationCountryName
                    },
                    {
                        title: '征免方式',
                        width: '100px',
                        field: 'faxTypeCode',
                        formatter: this.formatterlvyrlfModecd
                    },
                    {
                        title: '监管要求',
                        width: '100px',
                        field: 'supvModecd',
                        // field: 'hstype',
                    },
                ],
                //索检商品库
                goodsPnGridOptions: {
                    columns: [
                        {field: 'partNumber', title: '料号',},
                        {field: 'hscode', title: '申报税号',},
                        {field: 'hsname', title: '申报品名',},
                        {field: 'hsmodel', title: '申报要素',},
                        {field: 'ciqName', title: '检验检疫名称',},
                        {field: 'ciqCode', title: '检验检疫编码',},
                    ],
                    // pagerConfig: null,
                    toolbarConfig: null,
                    pagerConfig: {
                        pageSize: 15,
                        pageSizes: [15, 50, 100, 200]
                    }
                },
                //归类先例
                classifyGridOptions: {
                    columns: [
                        {field: 'hsname', title: '商品名称',},
                        // {field: '33', title: '警告',},
                        {field: 'hscode', title: '商品编码',},
                        {field: 'hsmodel', title: '规格型号',},
                        {field: 'ciqName', title: '商检名称',},
                        {field: 'ciqCode', title: '商检代码',},
                        {field: 'clearanceNo', title: '报关单号',},
                        {field: 'optUnitName', title: '境内收发货人名称',},
                    ],
                    // pagerConfig: null,
                    toolbarConfig: null,
                    pagerConfig: {
                        pageSize: 15,
                        pageSizes: [15, 50, 100, 200]
                    }
                },
                //征免方式
                lvyrlfModecdList: [],
                //区分进出口显示隐藏项为 true是表示进口报关单, 否则为出口报关单
                imSignShow: false,
                show: {
                    decGoodsLimittype: false,
                    decDangInfo: false,
                    decGoodsSpec: false,
                    decCiqName: false,
                    decTeriff: false,
                    decHsmodel: false,
                    decHsmodelPage: false,
                    goodsAttrType: false,
                    goodsAttrType1: false,
                    unitTotal: false,
                    batchUpdate: false,
                    bulkEdit: false,
										productInformation:false,

                decgoodsdocumentsType:false,
                },
                count2Readonly: true,
                //规格型号的弹窗
                decHsmodels: {
                    decHsmodelDialog: '',
                },
                //备案序号是否可编辑
                recordItemType: true,
                showHelpTip1: false,
                maskClosable:false,
                concertedReciprocalType:false,//享惠

            }
        },
        props: {
            /**
             * 禁用编辑功能
             */
            disableEdit: {
                type: Boolean
            },
            decLists: {
                type: Array,
                default: () => []
            },
            value: {
                type: Object,
            },
            // v-model
            imSignShowList: {
                type: Boolean,
            },
            //企业货代判断字段
            txtApplyType: {
                type: Boolean
            },
            focusIndexs: {
                type: Number
            },
            decStatus:{
                type:String
            },
            decHeadId:{
                type:String
            },
					optUnitSocialCode:{
						type:String
					},
					declareUnitSocialCode:{
						type:String
					},
					IE_FLAG:{
						type:String
					},
					optUnitId:{
						type:String
					},
					optUnitName:{
						type:String
					},
        },
        model: {
            prop: 'value',
            event: 'change'
        },
        watch: {
            dataSource: {
                handler(val) {
                    this.$emit('update:decLists', val)
                },
                deep: true,
            },
            decLists: {
                handler(val) {
                    this.dataSource = val
                },
                deep: true
            },
            value: {
                handler(val) {
									if(val.recordNumber){
										this.recordItemType = false
									}else {
										this.recordItemType = true
									}
                    this.decRecord = val

                    if(val.imSign==1){
                        this.record.destinationCountry='CHN'
                    }

                },
                deep: true
            },
            focusIndexs: {
                handler(val, oldVal) {
                    if (val == 64 && (oldVal == 85 || oldVal == 88)) {//val==64&&oldVal==85 说明是保存老数据光标从征免方式或者用途跳转来的目的新增新的数据方便录入数据
                        this.handleAdd()
                        setTimeout(() => {
                            this.focusIndex = val
                        }, 200)
                    } else {
                        this.focusIndex = val
                    }

                },
                deep: true,
            },
            focusIndex: {
                handler(val) {
                    this.$emit("focusIndexsUpadte", val)
                }
            },
            // 'record.unit1'(newValue, oldValue) {
            //     this.transitionUnit()
            // },
            // 'record.unit2'(newValue, oldValue) {
            //     this.transitionUnit()
            // },
            // 'record.unitCode'(newValue, oldValue) {
            //     this.transitionUnit()
            // },
            imSignShowList: {
                handler(val) {

                    if(val==true){
                        this.record.destinationCountry=142
                    }

                }
            },
        },
        created() {
					this.initDictData('erp_units,name,code')
        	//如果新增报关单并且报关制单地区为烟台默认表体的境内货源地37069-烟台其他
					if (this.$route.query.decNew&&this.$route.query.district==1) {
						this.record.districtCode = '37069'
					}

            this.dataSource = this.decLists
            if (this.dataSource.length > 0) {
                this.record = this.dataSource[0]
            }
            this.$nextTick(() => {
                this.handleAdd()
            })

        },
			computed: {
				statisticsData() {
					if (!this.dataSource || this.dataSource.length === 0) {
						return {
							totalItems: 0,
							totalCount: 0,
							totalAmount: 0,
							totalPackages: 0,
							totalGrossWeight: this.value && this.value.grossWeight ? this.value.grossWeight : 0,
							totalNetWeight: this.value && this.value.netWeight ? this.value.netWeight : 0
						}
					}

					const calculatedGrossWeight = this.dataSource.reduce((sum, item) => sum + (parseFloat(item.grossWeight) || 0), 0);
					const totalGrossWeight = calculatedGrossWeight > 0 ?
						calculatedGrossWeight.toFixed(4) :
						(this.value && this.value.grossWeight ? this.value.grossWeight : 0);

					const calculatedNetWeight = this.dataSource.reduce((sum, item) => sum + (parseFloat(item.netWeight) || 0), 0);
					const totalNetWeight = calculatedNetWeight > 0 ?
						calculatedNetWeight.toFixed(4) :
						(this.value && this.value.netWeight ? this.value.netWeight : 0);

					return {
						totalItems: this.dataSource.length,
						totalCount: this.dataSource.reduce((sum, item) => sum + (parseFloat(item.goodsCount) || 0), 0).toFixed(4),
						totalAmount: this.dataSource.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0).toFixed(4),
						totalPackages: this.value && this.value.packs ? this.value.packs : 0, // 从表头获取总件数
						totalGrossWeight: totalGrossWeight,
						totalNetWeight: totalNetWeight
					}
				}
			},
        methods:{
					initDictData(dictCode) {
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length > 0) {
							if (dictCode == 'erp_units,name,code') {
								this.units = dictOptions
							}
						} else {
							//根据字典Code, 初始化字典数组
							ajaxGetDictItems(dictCode, null).then(res => {
								if (res.success) {
									sessionStorage.setItem(dictCode,JSON.stringify(res.result))
									// this.initDictData(dictCode)
									let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
									if (dictOptions != null && dictOptions.length > 0) {
										if (dictCode == 'erp_units,name,code') {
											this.units = dictOptions
										}
									}
								}
							})
						}
					},
					handleBatchUpdate(data) {
						console.log('批量修改的数据是：', data)
						// 获取选中的行
						const selectedRows = this.$refs.xTable2.getCheckboxRecords() || [];
						if (selectedRows.length > 0) {
							selectedRows.forEach(selectedRow => {
								this.dataSource.forEach(row => {
									if (row.id === selectedRow.id || row.item === selectedRow.item) {
										if (data.pplx !== undefined && data.pplx !== null && data.pplx !== '') {
											if (!row.hsmodel) {
												row.hsmodel = data.pplx + '|';
											} else if (/^\d/.test(row.hsmodel)) {
												row.hsmodel = data.pplx + row.hsmodel.substring(1);
											} else if (row.hsmodel.startsWith('|')) {
												row.hsmodel = data.pplx + row.hsmodel;
											}
										}
										Object.keys(data).forEach(key => {
											if (key === 'pplx') return;
											if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
												row[key] = data[key];
											}
										});
									}
								});
							});
							this.$message.success(`成功修改 ${selectedRows.length} 条数据`);
						} else {
							this.dataSource.forEach(row => {
								if (data.pplx !== undefined && data.pplx !== null && data.pplx !== '') {
									if (!row.hsmodel) {
										row.hsmodel = data.pplx + '|';
									} else if (/^\d/.test(row.hsmodel)) {
										row.hsmodel = data.pplx + row.hsmodel.substring(1);
									} else if (row.hsmodel.startsWith('|')) {
										row.hsmodel = data.pplx + row.hsmodel;
									}
								}
								Object.keys(data).forEach(key => {
									if (key === 'pplx') return;
									if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
										row[key] = data[key];
									}
								});
							});
							this.$message.success(`成功修改所有数据（共 ${this.dataSource.length} 条）`);
						}
					},
					showQunitText({ cellValue, row, column }) {
						return this.getText(cellValue, this.units)
					},
					getText(value, arr) {
						var text
						if (value == null) {
							text = null
						} else {
							for (let i = 0; i < arr.length; i++) {
								if (value == arr[i].value) {
									text = arr[i].text
									break
								}
							}
						}
						return text
					},
          handleDecGoodsSpec(e){
            this.keyFromPromise(e)
          },
          goodsSave(){
            if (this.record['hscode'] == '' || this.record['hscode'] == null || this.record['hscode'] === undefined) {
                this.$message.warning("商品编号为空不允许保存！")
                return ;
            }else if (this.record['hsname'] == '' || this.record['hsname'] == null || this.record['hsname'] === undefined) {
                this.$message.warning("商品名称为空不允许保存！")
                return ;
            }
            let find = !!this.dataSource && this.dataSource.find(p => p.item === this.record.item)
            if(!find){//新增
                this.record.opt = 'I'
                this.record.item = this.dataSource.length+1
                this.dataSource.push(this.record)
            }else{//修改
                this.record.opt = 'U'
            }


          },

        },
				mounted() {
                    // 将表格和工具栏进行关联
                    const $table = this.$refs.xTable2
                    const $toolbar = this.$refs.toolbarRef
                    if ($table && $toolbar) {
                    $table.connect($toolbar)
                    }

					// 添加事件监听
					document.addEventListener('keydown', this.handleKeydown);

				},
    }
</script>

<style scoped>

    @import '~@assets/less/common.less';
    .toolbar{
        position: absolute;
        height: 19px;
        top: 10px;
        right: 0;
        background-color: rgba(0, 0, 0, 0);
    }
    .toolbar >>> button{
        height: 24px;
        border-radius: 4px !important;
    }

    table.my-table {
        width: 100%;
        table-layout: fixed;
    }

    .totalButton {
        margin-left: 12%
    }

    .totalButton1 {
        margin-left: 30px
    }

    .ant-form-item-control {
        line-height: 0;
    }

    .buttonStyle {
        height: 19px
    }
    .w-100{
        width: 100%;
    }

    .inlineTable {
        width: 100%;
        margin-top: 10px;
        table-layout: fixed;
        border: solid #BDBDBD;
        border-width: 1px 1px 1px 1px;

    }

    .inlineTd {
        border: solid #BDBDBD;
        border-width: 1px 1px 1px 1px;
        font-size: 13px;
    }

    .inlineTd >>> ant-input-a {
        border: solid #BDBDBD;
        border-width: 1px 1px 1px 1px;
        font-size: 13px
    }

    >>> .inlineTable > tr > td + td {
        border-left: 1px solid rgba(38, 38, 38, 0.3);
        font-size: 13px;
        height: 25px;
    }
		>>> .ant-alert{
			padding: 0px 0px 3px 20px;
			margin-top: -4px;
			margin-bottom: 6px;

		}

		.single{
			display: inline-block;
		}
		.bntinsert{
			width:180px;
			margin-left: 10px;
		}
		.insert{
			border:none;
		}

		.mg10{
			margin-left: 10px;
		}
		.titleFont{
			color: #c50b0b;
			font-size:17px;
		}



</style>
