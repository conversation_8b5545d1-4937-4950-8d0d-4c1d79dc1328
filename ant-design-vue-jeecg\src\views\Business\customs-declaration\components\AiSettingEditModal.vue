<template>
	<a-modal :disableSubmit="disableSubmit" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" :title="title"
		:visible="visible" :width="width" cancelText="关闭" selfCloseAction="closePop" @cancel="handleCancel"
		@ok="handleSave">
		<j-form-container :disabled="disableSubmit">
			<a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules">
				<a-col :span="24">
					<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="模版名称" prop="tplName">
						<a-input v-model="model.tplName" placeholder="请输入模版名称"></a-input>
					</a-form-model-item>
				</a-col>
				<a-row :gutter="24" justify="center" type="flex">
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出口标识" prop="ieFlag">
							<a-select v-model="model.ieFlag" allowClear defaultValue="E" placeholder="请选择" showSearch>
								<a-select-option value="I">进口</a-select-option>
								<a-select-option value="E">出口</a-select-option>
							</a-select>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申报地海关" prop="declarePlace">
							<j-dict-select-tag v-model="model.declarePlace"
								dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
								placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出境关别" prop="outPortCode">
							<j-dict-select-tag v-model="model.outPortCode"
								dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
								placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="运输方式" prop="shipTypeCode">
							<j-dict-select-tag v-model="model.shipTypeCode" dictCode="trans_type" placeholder="请选择，支持搜索"
								type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="收发货人" prop="optUnitName">
							<a-input v-model="model.optUnitName" placeholder="请输入收发货人"></a-input>
						</a-form-model-item>
					</a-col>
					<!--					<a-col :span="24">-->
					<!--						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="价格基准" prop="priceReference">-->
					<!--							<a-select v-model="model.priceReference" allowClear defaultValue="1" placeholder="请选择" showSearch>-->
					<!--								<a-select-option value="1">单价</a-select-option>-->
					<!--								<a-select-option value="2">总价</a-select-option>-->
					<!--							</a-select>-->
					<!--						</a-form-model-item>-->
					<!--					</a-col>-->
				</a-row>
			</a-form-model>
		</j-form-container>
	</a-modal>
</template>
<script>
import { getAction, httpAction } from "@/api/manage";

export default {
	name: "AiSettingEditModal",
	data() {
		return {
			title: "AI配置",
			width: 500,
			visible: false,
			disableSubmit: false,
			model: {
				priceReference: '1',
			},
			validatorRules: {
				tplName: [{ required: true, validator: this.validateTplName, trigger: 'change' }],
				ieFlag: [{ required: true, message: '请选择进出口标识' }],
				// cabinCode: [{ required: true, message: '请输入储位代码!' }],
				// areaCode: [{required: true, validator: validateAreaCode, trigger: 'change'}]
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 6 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			url: {
				save: '/DecHead/dec-head/saveAiSetting',
				getById: '/DecHead/dec-head/getAiSettingById',
				list: '/DecHead/dec-head/listAiSettings'
			}
		}
	},
	methods: {
		validateTplName(rule, value, callback) {
			if (!value || value.trim() === '') {
				callback(new Error('请输入模版名称'))
				return
			}
			this.$nextTick(async () => {
				const res = await getAction(this.url.list, {
					tplNameC: this.model.tplName
				})
				if (res && res.result && res.result.length > 0) {
					callback(new Error('模版名称已存在'))
				}
				callback()
			})
		},
		add() {
			this.visible = true
		},
		handleSave() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(async valid => {
				if (valid) {
					console.log('最终保存的AI配置数据：', this.model)
					that.confirmLoading = true
					httpAction(this.url.save, this.model, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								this.close()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				}
			})
		},
		edit(record) {
			this.visible = true
			this.initModel(record)
		},
		initModel(value) {
			this.confirmLoading = true
			let val = value
			if (val == undefined) {
				val = this.model
			}
			let params = {
				id: val.id,
			}
			if (val.id != null) {
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.model = record
						} else {
							this.$message.warning(res.message || res)
							this.close()
						}
					})
					.finally(() => {
						this.confirmLoading = false
					})
			}
		},
		close() {
			this.$emit('ok')
			this.model = {
				priceReference: '1',
			}
			this.visible = false
		},
		handleCancel() {
			this.close()
		},
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>