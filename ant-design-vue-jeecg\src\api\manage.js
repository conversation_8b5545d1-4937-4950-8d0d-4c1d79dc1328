import Vue from 'vue'
import {axios} from '@/utils/request'
import signMd5Utils from '@/utils/encryption/signMd5Utils'

const api = {
  user: '/mock/api/user',
  role: '/mock/api/role',
  service: '/mock/api/service',
  permission: '/mock/api/permission',
  permissionNoPager: '/mock/api/permission/no-pager'
}

export default api

//post
export function postAction(url,parameter) {
  let sign = signMd5Utils.getSign(url, parameter);
  //将签名和时间戳，添加在请求接口 Header
  let signHeader = {"X-Sign": sign,"X-TIMESTAMP": signMd5Utils.getDateTimeToString()};

  return axios({
    url: url,
    method:'post' ,
    data: parameter,
    headers: signHeader
  })
}
//get
export function getFile(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter,
    responseType: "arraybuffer"
  })
}

export function _postAction(url,parameter) {
  let sign = signMd5Utils.getSign(url, parameter);
  //将签名和时间戳，添加在请求接口 Header
  let signHeader = {"X-Sign": sign,"X-TIMESTAMP": signMd5Utils.getDateTimeToString()};

  return axios({
    url: url,
    method: 'post',
    params: parameter,
    headers: signHeader
  })
}

//post method= {post | put}
export function httpAction(url,parameter,method) {
  let sign = signMd5Utils.getSign(url, parameter);
  //将签名和时间戳，添加在请求接口 Header
  let signHeader = {"X-Sign": sign,"X-TIMESTAMP": signMd5Utils.getDateTimeToString()};

  return axios({
    url: url,
    method:method ,
    data: parameter,
    headers: signHeader
  })
}

//put
export function putAction(url,parameter) {
  return axios({
    url: url,
    method:'put',
    data: parameter
  })
}

//get
export function getAction(url,parameter) {
  let sign = signMd5Utils.getSign(url, parameter);
  //将签名和时间戳，添加在请求接口 Header
  let signHeader = {"X-Sign": sign,"X-TIMESTAMP": signMd5Utils.getDateTimeToString()};

  return axios({
    url: url,
    method: 'get',
    params: parameter,
    headers: signHeader
  })
}

//deleteAction
export function deleteAction(url,parameter) {
  return axios({
    url: url,
    method: 'delete',
    params: parameter
  })
}

export function getUserList(parameter) {
  return axios({
    url: api.user,
    method: 'get',
    params: parameter
  })
}

export function getRoleList(parameter) {
  return axios({
    url: api.role,
    method: 'get',
    params: parameter
  })
}

export function getServiceList(parameter) {
  return axios({
    url: api.service,
    method: 'get',
    params: parameter
  })
}

export function getPermissions(parameter) {
  return axios({
    url: api.permissionNoPager,
    method: 'get',
    params: parameter
  })
}

// id == 0 add     post
// id != 0 update  put
export function saveService(parameter) {
  return axios({
    url: api.service,
    method: parameter.id == 0 ? 'post' : 'post',
    data: parameter
  })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export function downFile(url,parameter){
  return axios({
    url: url,
    params: parameter,
    method:'get' ,
    responseType: 'blob'
  })
}

/**
 * 在浏览器中打开PDF文件
 * @param url 文件路径
 * @param parameter 请求参数
 * @returns {Promise<any>}
 */
export function openPdfInBrowser(url, parameter) {
	return axios({
		url: url,
		method: 'get',
		params: parameter,
		responseType: 'blob'
	}).then(response => {
		// 创建Blob对象
		const blob = new Blob([response], { type: 'application/pdf' })

		// 创建URL
		const fileURL = URL.createObjectURL(blob)

		// 尝试在用户点击事件处理函数的上下文中打开窗口
		const newWindow = window.open(fileURL, '_blank')

		// 如果打开失败（被拦截），则提供备选方案
		if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
			// 创建一个临时链接并模拟点击
			const link = document.createElement('a')
			link.href = fileURL
			link.target = '_blank'
			link.style.display = 'none'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)

			// 提示用户检查弹出窗口拦截器
			console.warn('PDF可能被弹出窗口拦截器阻止。请检查浏览器设置。')
		}

		return {
			success: true,
			result: true
		}
	}).catch(error => {
		console.error('打开PDF失败:', error)
		return {
			success: false,
			message: error.message || '打开PDF失败'
		}
	})
}

/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export function downloadFile(url, fileName, parameter) {
  return downFile(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      Vue.prototype['$message'].warning('文件下载失败')
      return
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data]), fileName)
    } else {
      let url = window.URL.createObjectURL(new Blob([data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(url) //释放掉blob对象
    }
  })
}

/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export function downloadFileBase64(url, fileName, parameter) {
	return getAction(url, parameter).then((res) => {
		if (!res || !res.result) {
			// Vue.prototype['$message'].warning('无文件数据！')
			return res
		}
		const base64String = res.result;
		const decodedString = atob(base64String);
		const buffer = new ArrayBuffer(decodedString.length);
		const view = new Uint8Array(buffer);
		for (let i = 0; i< decodedString.length; i++) {
			view[i] = decodedString.charCodeAt(i);
		}
		const blob = new Blob([buffer], { type: 'application/pdf' });
		const url = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.style.display = 'none';
		link.href = url;
		link.setAttribute('download', fileName);
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link); // 下载完成移除元素
		window.URL.revokeObjectURL(url); // 释放掉blob对象
	})
}

/**
 * 文件上传 用于富文本上传图片
 * @param url
 * @param parameter
 * @returns {*}
 */
export function uploadAction(url,parameter){
  return axios({
    url: url,
    data: parameter,
    method:'post' ,
    headers: {
      'Content-Type': 'multipart/form-data',  // 文件上传
    },
  })
}

/**
 * 获取文件服务访问路径
 * @param avatar
 * @param subStr
 * @returns {*}
 */
export function getFileAccessHttpUrl(avatar,subStr) {
  if(!subStr) subStr = 'http'
  try {
    if(avatar && avatar.startsWith(subStr)){
      return avatar;
    }else{
      if(avatar &&　avatar.length>0 && avatar.indexOf('[')==-1){
        return window._CONFIG['staticDomainURL'] + "/" + avatar;
      }
    }
  }catch(err){
   return;
  }
}

/**
 * 获取文件服务访问路径
 * @param avatar
 * @param subStr
 * @returns {*}
 */
export function getFileAccessHttpUrl_(avatar,subStr) {
	try {
    let url = getFileAccessHttpUrl(avatar,subStr)
		if (url && url.indexOf('trade-service-platform')!==-1) {
			return window._CONFIG['staticDomainURL'] + url.split('trade-service-platform')[1]
		} else if (url && url.indexOf('aliyuncs.com') !== -1) {
			return window._CONFIG['staticDomainURL'] + url.split('aliyuncs.com')[1]
		}
		return url
  }catch(err){
   console.log(err)
  }
}

/**
 * 获取文件服务访问路径
 * @param avatar
 * @param subStr
 * @returns {*}
 */
export async function getMinioExpiresUrl(avatar, subStr) {
	try {
		let url = getFileAccessHttpUrl(avatar, subStr)
		if (url && url.indexOf('trade-service-platform') !== -1) {
			const res = await getAction(window._CONFIG['staticMinioURL'] + url.split('trade-service-platform')[1])
			return res.result
			// return window._CONFIG['staticDomainURL'] + url.split('trade-service-platform')[1]
		} else if (url && url.indexOf('aliyuncs.com') !== -1) {
			const res = await getAction(window._CONFIG['staticMinioURL'] + url.split('aliyuncs.com')[1]);
			return res.result;
		}
		return url
	} catch (err) {
		console.log(err)
	}
}
