<template>
	<j-modal
		:title="'加贸手册单损耗 ' + title"
		:width="width"
		:visible="visible"
		@ok="handleSave"
		:okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
		@cancel="handleCancel"
		cancelText="关闭"
		selfCloseAction="closePop"
		:disableSubmit="disableSubmit"
	>
		<template slot="footer">
			<span class="tipsText" style="float: left">手册编号：{{model.emsNo}}</span>
			<a-button type="default" @click="handleCancel">关闭</a-button>
			<a-button v-show="!disableSubmit" type="primary" @click="handleSave">保存</a-button>
		</template>

		<a-spin :spinning="confirmLoading">
			<a-collapse v-model="activeKeys" :bordered="false" style="margin-top: -10px">
				<!-- 基本信息 -->
				<a-collapse-panel key="1" header="基本信息" >
					<j-form-container :disabled="disableSubmit">
						<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
							<a-row :gutter="24" type="flex" justify="start">
								<a-col :span="8">
									<a-form-model-item label="成品序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="exgNo">
										<a-input-number placeholder='请输入成品序号' v-model="model.exgNo" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="imgNo">
										<a-input-number placeholder='请输入料件序号' v-model="model.imgNo" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="净耗" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="decCm">
										<a-input-number placeholder='请输入净耗' v-model="model.decCm" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="有形损耗率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="decDm">
										<a-input-number placeholder='请输入有形损耗率' v-model="model.decDm" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="无形损耗率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="intangibleLossRate">
										<a-input-number placeholder='请输入无形损耗率' v-model="model.intangibleLossRate" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="单耗版本号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ucnsverno">
										<a-input placeholder='请输入单耗版本号' v-model="model.ucnsverno"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="单耗申报状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unitConsumptionStatus">
										<a-select v-model="model.unitConsumptionStatus" allowClear showSearch placeholder="请选择单耗申报状态">
													<!-- <a-select-option value="2">已申报</a-select-option> -->
											<a-select-option value="1">未申报</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="保税料件比例%" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="proportionOfBondedMaterials">
										<a-input placeholder='请输入保税料件比例' v-model="model.proportionOfBondedMaterials"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="修改标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="modifyFlag">
										<a-select v-model="model.modifyFlag" allowClear showSearch placeholder="请选择修改标志">
											<a-select-option value="1">修改</a-select-option>
											<!-- <a-select-option value="2">报核前</a-select-option> -->
											<a-select-option value="3">增加</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="企业执行标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseExecutionFlag">
											<a-select v-model="model.enterpriseExecutionFlag" allowClear showSearch placeholder="请选择企业执行标志">
											<a-select-option value="1">运行</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
							</a-row>
						</a-form-model>
					</j-form-container>
				</a-collapse-panel>
			</a-collapse>
		</a-spin>

	</j-modal>
</template>
<script>
import { getAction, httpAction } from '@/api/manage'

export default {
	name: 'CmEditModal',
	data() {
		return {
			model: {
				emsId: '',
				emsNo: ''
			},
			emsHead: {},
			activeKeys: ['1', '2'],
			labelCol: {
				xs: { span: 24 },
				sm: { span: 7 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 17 },
			},
			title: '',
			width: 980,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			validatorRules: {
				exgNo: [{ checkName: '成品序号', max: 10, required: true, validator: this.checkNo }],
				imgNo: [{ checkName: '料件序号', max: 10, required: true, validator: this.checkNo }],
				decDm: [
					{
						required: true,
						max: 18,
						validateParam: [
							{
								pattern: /^((\d|[123456789]\d)(\.\d+)?|100)$/,
								message: '请填写0-100内数字!'
							},
							{
								pattern: /^(([^0][0-9]+|0).([0-9]{1,5})$)|^(([^0][0-9]+|0)$)|^(([1-9]+).([0-9]{1,5})$)|^(([1-9]+)$)/,
								message: '最多五位小数!'
							}
						],
						validator: this.checkNo,
						checkName: '有形损耗率'
					}
				],
				decCm: [
					{
						checkName: '净耗',
						max: 18,
						validateParam: [
							{
								pattern: /^[0-9]+([.]{1}[0-9]+){0,1}$/,
								message: '请填写正小数!'
							},
							{
								pattern: /^(([^0][0-9]+|0).([0-9]{1,5})$)|^(([^0][0-9]+|0)$)|^(([1-9]+).([0-9]{1,5})$)|^(([1-9]+)$)/,
								message: '最多五位小数!'
							}
						],
						required: true,
						validator: this.checkNo
					}
				],
				ucnsverno: [{ checkName: '单耗版本号', max: 8, required: true, validator: this.checkNo }]
			},
			url: {
				save: '/business/ems/saveEmsCm',
				getById: '/business/ems/getEmsCmById',
			}
		}
	},
	methods : {
		add(record) {
			this.emsHead = Object.assign({}, record)
			this.model.emsNo = this.emsHead.emsNo
			this.model.emsId = this.emsHead.id
			this.visible = true
		},
		edit(record) {
			this.initModel(record)
			this.visible = true
		},
		initModel(value) {
			this.confirmLoading = true
			let val = value
			if (val == undefined) {
				val = this.model
			}
			let params = {
				id: val.id,
			}
			if (val.id != null) {
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.model = record
						} else {
							// 失败
							this.$message.warning(res.message || res)
							this.close()
						}
					})
					.finally(() => {
						this.confirmLoading = false
					})
			} else {
				// 新增
				this.confirmLoading = false
			}
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的加贸手册单损耗数据：', this.model)
					httpAction(this.url.save, this.model, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								this.close()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		close() {
			this.$emit('close')
			this.model = {}
			this.emsHead = {}
			this.visible = false
		},
		handleCancel () {
			this.close()
		},
		/**
		 ** 小数验证
		 ** rule入参
      {
        required: true,
        max: 18,
        validateParam: [
          {
            pattern: this.$regex.POSITIVE_DECIMAL,
            message: "请填写正确的申报单价!"
          },
          {
            pattern: this.$regex.POSITIVE_DECIMAL_5,
            message: "最多五位小数!"
          }
        ],
        validator: this.checkNo,
        checkName: '申报单价'
      }
		 ** 返回值：callback
		 **/
		checkNo (rule, value, callback) {
			if (rule.required && this.isEmpty(value)) {
				if (rule.inputType === 'select') {
					callback(`请选择${rule.checkName}!`)
				} else {
					callback(`请填写${rule.checkName}!`)
				}
			}
			if (!this.isEmpty(value)) {
				if (rule.validateParam) {
					rule.validateParam.forEach(element => {
						if (element.pattern && !element.pattern.test(value)) {
							callback(element.message)
						}
					})
				}

				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(`长度不能大于${rule.max}位!`)
				}
				if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
					callback(`长度不能大于${rule.max}位!`)
				} else {
					callback()
				}
			} else {
				callback()
			}
		}
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
</style>