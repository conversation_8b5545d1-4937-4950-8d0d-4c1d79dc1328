package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dec_list")
public class DecList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报关明细流水号
     */
    private String id;

    /**
     * 委托单号
     */
    private String applyNumber;

    /**
     * 报关流水号 与报关表头关联
     */
    private String decId;

    /**
     * 项号
     */
    private Integer item;
    /**
     * 关联产品id
     */
    private String productInfoId;

    /**
     * 备案序号
     */
    private Integer recordItem;

    /**
     * 商品编码 税号
     */
    private String hscode;

    /**
     * 商品名称 申报品名
     */
    private String hsname;

    /**
     * 检验检疫编码
     */
    private String ciqCode;

    /**
     * 检验检疫名称
     */
    private String ciqName;

    /**
     * 规格型号 申报要素
     */
    private String hsmodel;

    /**
     * 成交数量
     */
    private BigDecimal goodsCount;

    /**
     * 成交单位
     */
    @Dict(dictTable = "erp_units", dicText = "name", dicCode = "code")
    private String unitCode;

    /**
     * 原产国
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    private String desCountry;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal total;

    /**
     * 币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    private String currencyCode;

    /**
     * 征免方式
     */
    @Dict(dicCode = "ZJMSFS")
    private String faxTypeCode;

    /**
     * 法定单位
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    private String unit1;

    /**
     * 法定数量
     */
    private BigDecimal count1;

    /**
     * 第二法定单位
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    private String unit2;

    /**
     * 第二法定数量
     */
    private BigDecimal count2;

    /**
     * 最终目的国
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    private String destinationCountry;

    /**
     * 原产地区代码
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    private String origPlaceCode;

    /**
     * 用途代码
     */
    @Dict(dicCode = "YT")
    private String purpose;

    /**
     * 境内目的地/境内货源地
     */
    @Dict(dictTable = "erp_districts", dicText = "name", dicCode = "code")
    private String districtCode;

    /**
     * 境内目的地/境内货源地名称
     */
    private String districtName;

    /**
     * 目的地代码 境内目的地/境内货源地辅助字段
     */
    @Dict(dictTable = "erp_districts", dicText = "name", dicCode = "code")
    private String destCode;

    /**
     * 目的地代码 境内目的地/境内货源地辅助字段名称
     */
    private String destName;

    /**
     * 货物属性代码
     */
    @Dict(dicCode = "HWSX")
    private String goodsAttr;

    /**
     * 成份/原料/组份 检验检疫货物规格
     */
    private String stuff;

    /**
     * 货物规格 检验检疫货物规格
     */
    private String goodsSpec;

    /**
     * 货物型号 检验检疫货物规格
     */
    private String goodsModel;

    /**
     * 货物品牌 检验检疫货物规格
     */
    private String goodsBrand;

    /**
     * 产品有效期 格式：yyyyMMdd 检验检疫货物规格
     */
    private String prodValidDt;

    /**
     * 生产日期 格式：yyyy-MM-dd,多个日期用英文半角分号分隔 检验检疫货物规格
     */
    private String produceDate;

    /**
     * 生产批号 检验检疫货物规格
     */
    private String prodBatchNo;

    /**
     * 生产单位注册号 检验检疫货物规格
     */
    private String mnufctrRegNo;

    /**
     * 生产单位名称 检验检疫货物规格
     */
    private String mnufctrRegName;

    /**
     * 产品保质期 检验检疫货物规格
     */
    private String prodQgp;

    /**
     * 境外生产企业名称 检验检疫货物规格
     */
    private String engManEntCnm;

    /**
     * 非危险化学品 危险货物信息
     */
    private String noDangFlag;

    /**
     * UN编码 危险货物信息
     */
    private String uncode;

    /**
     * 危险货物名称 危险货物信息
     */
    private String dangName;

    /**
     * 危包类别 危险货物信息
     */
    private String dangPackType;

    /**
     * 危包规格 危险货物信息
     */
    private String dangPackSpec;

    /**
     * 许可证信息	[{	"LicenceNo":"3",	"LicTypeCode":"2",	"LicWrtofDetailNo":"4",	"LicWrtofQty":"5",	"LicWrtofQtyUnit":"6"	},{	"LicenceNo":"3",	"LicTypeCode":"2",	"LicWrtofDetailNo":"4",	"LicWrtofQty":"5",	"LicWrtofQtyUnit":"6"	}]
     */
    private String goodsLimitType;

    /**
     * 合并序号 系统用
     */
    private Integer mergeSequence;

    /**
     * 包含件号ID 系统用
     */
    private String goodsId;

    /**
     * 法捡类型 系统用
     */
    private String hstype;

    /**
     * 监管条件 系统用
     */
    private String supvModecd;

    /**
     * 净重 系统用
     */
    private BigDecimal netWeight;

    /**
     * 毛重 系统用
     */
    private BigDecimal grossWeight;

    /**
     * 客户端编号
     */
    private String customsCode;

    /**
     * 加工成品单耗版本号
     */
    private String exgVersion;

    /**
     * 危险品标志
     */
    private Boolean dgFlag;

    /**
     * 优惠贸易协定项下原产地
     */
    private String rcepOrigPlaceCode;

    @TableField(exist = false)
    private String opt;

    /**
     * 货物属性代码名称
     */
    @TableField(exist = false)
    private String goodsAttrStr;

    /** 原产地证明项号关联关系 */
    @TableField(exist = false)
    private DecEcoRelation decEcoRelation;

    /**
     * 历史税则的申报规范
     */
    private String historyDeclarationSpecification;
    /**
     * 历史税则的商品名
     */
    private String historyHsname;
    /**
     * 历史税则的监管条件
     */
    private String historyRegulatoryConditions;
    /**
     * 历史税则的检疫条件
     */
    private String historyQuarantineConditions;

    /**
     * 表头境内收发货人
     */
    @TableField(exist = false)
    private String optUnitName;
    /**
     * 备注
     */
    @TableField(exist = false)
    private String markNumber;
    /**
     * 进出口标识
     *
     */
    @TableField(exist = false)
    private String ieFlag;
    /**
     * 是否隐藏历史数据
     */
    private String ishide;
    /**
     * 消费使用单位名称
     */
    @TableField(exist = false)
    private String deliverUnitName;
    @TableField(exist = false)
    private String seqNo;

}
