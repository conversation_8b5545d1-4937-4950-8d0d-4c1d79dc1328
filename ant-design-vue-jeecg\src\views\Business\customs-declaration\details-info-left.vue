<template>
    <a-form-model :model='record' ref='list_header'>
        <table class='my-table' style='width: 98%'>
            <!--表头-->
            <tr v-show="false" >
              <input-item   v-model='record.id' :label-width="10" :value-width="45" label='报关单流水号' readonly/>

              <input-item  v-model='record.orderProtocolNo' :label-width="8" :value-width="37" label='业务编号' readonly/>

            </tr>
          <tr>
                <decSelect-Item :id="0" :focusIndex='focusIndex' :vfocusNum='focusIndex===0' :label-width="10"
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :value-width="45"
                                :readonly='disableEdit' label='申报地海关'
                                dict-key='GQDM'
                                :selectWidth="36.5" :styleType="1" v-model='record.declarePlace'></decSelect-Item>

                <decSelect-Item label='申报状态' :options="dictOptions.decStatus" :selectWidth="36.5"
                                :label-width="8"
                                :styleType="3" readonly v-model='record.decStatus' :value-width="37"
                ></decSelect-Item>

            </tr>
            <colgroup v-for="n in 100" style="width:1%"></colgroup>
            <tr>
                <input-item :id="0.1" :focusIndex='focusIndex' :vfocusNum='focusIndex===0.1' :valClass="tdDecStatus"
														readonly
                            :value-width="45"
                            @focusIndexDate="focusIndexMethod" label='统一编号' v-model='record.seqNo' :label-width="10"/>

                <input-item label='预录入编号' readonly v-model='record.clearanceNo' :label-width="8" :value-width="37"/>
            </tr>
            <tr>
                <input-item label='海关编号' readonly v-model='record.clearanceNo' :label-width="10" :value-width="45"/>

                <decSelect-Item :id="1" :focusIndex='focusIndex' :vfocusNum='focusIndex===1' :label-width="8"
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :value-width="37"
                                :readonly='disableEdit' :label='imSignShow? `进境关别` :`出境关别`'
                                :dict-key='dictKeys.GQDM' :selectWidth="36.5" :styleType="1"
                                v-model='record.outPortCode'></decSelect-Item>

            </tr>
            <tr>

                <!-- @change="recordNumberChange" -->
                <input-item :id="2" :readonly='disableEdit' label='备案号' :focusIndex='focusIndex'
                            :vfocusNum='focusIndex===2' :label-width="10" :value-width="45"
                            @change="()=>{}" @focusIndexDate="focusIndexMethod"
                            v-model='record.recordNumber'/><!--:label-width="imSignShow?9:10" :value-width="imSignShow?18:45" -->
                <input-item :id="3" :readonly='disableEdit' label='合同协议号' :focusIndex='focusIndex'
                            :label-width="8" :value-width="35" :vfocusNum='focusIndex===3'
                            @focusIndexDate="focusIndexMethod" v-model='record.contract'/>
                            <td colspan="2">
                                <a-checkbox title="合同协议号转大写" @change="contractOnChange">
                                </a-checkbox>
                            </td>
            </tr>
            <tr>
                <input-item :id="3.1" ref="outDateRef" :readonly='!imSignShow'
                            :label='imSignShow ? `进口日期` : `出口日期`' :label-width="10" :value-width="45"
                            :styleSrt="imSignShow ? styleSrt:''" :vfocusNum='focusIndex===3.1' :focusIndex='focusIndex'
                            @focusIndexDate="focusIndexMethod" v-model='record.outDate'/><!--:value-width="!imSignShow?45:18"-->
                <!--                    <date-item  ref="outDateRef"  :label='imSignShow ? `进口日期` : `出口日期`'@focusIndexDate="focusIndexMethod" v-model='record.outDate'/>-->
<!--                <date-item v-if="imSignShow" ref="despDateRef" :readonly='disableEdit' label='启运日期' v-model='record.despDate'
                           @focusIndexDate="focusIndexMethod" @change="changeDespDate" :value-width="18"
                           :label-width="9"/>-->
                <date-item label='申报日期' readonly v-model='record.appDate' @change="changeDate" :label-width="8"
                           :value-width="37"/>
            </tr>
            <!--境内收货人-->
            <tr>

                <input-item :id="4" :label='imSignShow?`境内收货人`:`境内发货人`' :focusIndex='focusIndex'
                            :vfocusNum='focusIndex===4' :styleSrt="styleSrt" :label-width="10" :value-width="27"
                            @focusIndexDate="focusIndexMethod" :readonly='disableEdit'
                            placeholder='18位社会信用代码'
                            v-model='record.optUnitSocialCode'/>

                <input-item :id="5" :readonly='disableEdit' no-label placeholder='10位海关代码'
                            :focusIndex='focusIndex' :value-width="13"
                            :vfocusNum='focusIndex===5' :styleSrt="styleSrt"
                            @focusIndexDate="focusIndexMethod"
                            v-model='record.optUnitId'/>
                <input-item :id="6" :readonly='disableEdit' no-label placeholder='10位检验检疫编码'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===6' :value-width="13"
                            @focusIndexDate="focusIndexMethod" v-model='record.tradeCiqCode'/>
                <input-item :id="7" :readonly='disableEdit' no-label placeholder='企业名称(中文)'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===7' :styleSrt="styleSrt"
                            @focusIndexDate="focusIndexMethod" v-model='record.optUnitName' :value-width="37"/>

            </tr>
            <!--境外发货人-->
            <tr v-if='imSignShow'>
                <input-item :id="8" :readonly='disableEdit' label='境外发货人' placeholder='境外发货人代码'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===8' @focusIndexDate="focusIndexMethod"
                            v-model='record.overseasConsignorCode' :label-width="10" :value-width="53"/>
                <input-item :id="9" v-model='record.overseasConsignorEname' :focusIndex='focusIndex' :readonly='disableEdit'
                            :styleSrt="styleSrt" :value-width="37" :vfocusNum='focusIndex===9'
                            no-label placeholder='企业名称' @focusIndexDate="focusIndexMethod"/>
<!--							0617优化境内外收发货人获取历史申报数据-->
<!--							<decSelect-Item :id="9" :focusIndex='focusIndex' :options='overseasConsignorEnameHistoryList'-->
<!--															:vfocusNum='focusIndex===9' no-label :value-width="37"-->
<!--															@focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"-->
<!--															:readonly='disableEdit' placeholder="企业名称"-->
<!--															:selectWidth="34" :styleType="6"-->
<!--															v-model='record.overseasConsignorEname'></decSelect-Item>-->

            </tr>
            <!--境外收货人-->
            <tr v-else>
                <input-item :id="8" :readonly='disableEdit' label='境外收货人' placeholder='境外收货人代码'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===8' @focusIndexDate="focusIndexMethod"
                            v-model='record.overseasConsigneeCode' :label-width="10" :value-width="53"/>
                <input-item :id="9" v-model='record.overseasConsigneeEname' :focusIndex='focusIndex' :readonly='disableEdit'
                            :styleSrt="styleSrt" :value-width="37" :vfocusNum='focusIndex===9'
                            no-label placeholder='企业名称' @focusIndexDate="focusIndexMethod"/>
							<!--							0617优化境内外收发货人获取历史申报数据-->
<!--							<decSelect-Item :id="9" :focusIndex='focusIndex' :options='overseasConsignorEnameHistoryList'-->
<!--															:vfocusNum='focusIndex===9' no-label :value-width="37"-->
<!--															@focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"-->
<!--															:readonly='disableEdit' placeholder="企业名称"-->
<!--															:selectWidth="34" :styleType="6"-->
<!--															v-model='record.overseasConsigneeEname'></decSelect-Item>-->
            </tr>
            <tr>
                <input-item :id="10" :readonly='disableEdit' :label='imSignShow?`消费使用单位`:`生产销售单位`'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===10' @focusIndexDate="focusIndexMethod"
                            placeholder='18位社会信用代码' :styleSrt="styleSrt" :label-width="10" :value-width="27"
                            v-model='record.deliverUnitSocialCode'/>
                <input-item :id="11" :readonly='disableEdit' no-label placeholder='10位海关代码' :styleSrt="styleSrt"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===11' @focusIndexDate="focusIndexMethod"
                            v-model='record.deliverUnit' :value-width="13"/>
                <input-item :id="12" :readonly='disableEdit' no-label placeholder='10位检验检疫编码'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===12' @focusIndexDate="focusIndexMethod"
                            v-model='record.ownerCiqCode' :value-width="13"/>
                <input-item :id="13" :readonly='disableEdit' no-label placeholder='企业名称'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===13' @focusIndexDate="focusIndexMethod"
                            v-model='record.deliverUnitName ' :value-width="37" :styleSrt="styleSrt"/>
            </tr>
            <tr>
                <input-item :id="14" :readonly='disableEdit' label='申报单位' placeholder='18位社会信用代码'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===14' @focusIndexDate="focusIndexMethod"
                            v-model='record.declareUnitSocialCode' :styleSrt="styleSrt" :label-width="10"
                            :value-width="27"/>
                <input-item :id="15" :readonly='disableEdit' no-label placeholder='10位海关代码' :value-width="13"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===15' @focusIndexDate="focusIndexMethod"
                            v-model='record.declareUnit' :styleSrt="styleSrt"/>
                <input-item :id="16" :readonly='disableEdit' no-label placeholder='10位检验检疫编码' :value-width="13"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===16' @focusIndexDate="focusIndexMethod"
                            v-model='record.declCiqCode'/>
                <input-item :id="17" :readonly='disableEdit' no-label placeholder='企业名称' :value-width="37"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===17' @focusIndexDate="focusIndexMethod"
                            v-model='record.declareUnitName' :styleSrt="styleSrt"/>
            </tr>

            <tr>
                <decSelect-Item :id="18" :focusIndex='focusIndex' :options='dictOptions.ysfs'
                                :vfocusNum='focusIndex===18' :label-width="10" :value-width="18"
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
                                :readonly='disableEdit' label='运输方式'
                                :selectWidth="20" :styleType="4"
                                v-model='record.shipTypeCode'></decSelect-Item>
                <input-item :id="19" :readonly='disableEdit' label='运输工具名称'
                            :focusIndex='focusIndex' :label-width="9"
                            :vfocusNum='focusIndex===19' :value-width="18"
                            @focusIndexDate="focusIndexMethod" v-model='record.shipName'/>
                <input-item :id="20" :focusIndex='focusIndex' :vfocusNum='focusIndex===20'
                            @focusIndexDate="focusIndexMethod" :label-width="8" :value-width="37"
                            :readonly='disableEdit' label='航次号' v-model='record.voyage'/>
            </tr>

            <tr>
                <input-item :id="21" :focusIndex='focusIndex' :vfocusNum='focusIndex===21'
                            :readonly='disableEdit' :label-width="10" :value-width="40"
                            @focusIndexDate="focusIndexMethod" label='提运单号' val-class='mini-item'
                            v-model='record.billCode'/>
                <td colspan="2">
                    <a-checkbox title="提运单号转大写" @change="billCodeOnChange">
                    </a-checkbox>
                </td>
                <td colspan="3">
                    <a-button :disabled='disableEdit' title="调用舱单数据" @click="getManifestInfo" size='small'
                              type='primary' class="buttonStyle">...
                    </a-button>
                </td>

                <decSelect-Item :id="22" :focusIndex='focusIndex' :vfocusNum='focusIndex===22'
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
                                :readonly='disableEdit' :dict-key='dictKeys.JGFS'
                                val-class='mini-item' label='监管方式'
                                :selectWidth="20" :styleType="4" :label-width="8" :value-width="17"
                                v-model='record.tradeTypeCode'></decSelect-Item>


                <decSelect-Item :id="23" :focusIndex='focusIndex' :vfocusNum='focusIndex===23'
                                @focusIndexDate="focusIndexMethod"
                                :readonly='disableEdit' val-class='mini-item'
                                :dict-key='dictKeys.ZMXZ' label='征免性质'
                                :label-width="7" :selectWidth="20" :styleType="1" :value-width="13"
                                v-model='record.taxTypeCode'></decSelect-Item>
            </tr>
            <tr>
                <input-item :id="24" :valClass="tdClass" :readonly='disableEdit' label='许可证号'
                            v-model='record.licenceNumber' :label-width="10" :value-width="18"
                            @focusIndexDate="focusIndexMethod" @change="licenceNumberChange"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===24'/>

                <decSelect-Item :id="25" :focusIndex='focusIndex' :vfocusNum='focusIndex===25'
                                @focusIndexDate="focusIndexMethod" :tmpClass="tdOneClass"
                                :readonly='disableEdit' :label='imSignShow?`启运国(地区)`:`运抵国(地区)`'
																:dict-key='dictKeys.GBDQDEC' :styleSrt="styleSrtSelect" :label-width="9"
                                :selectWidth="25" :styleType="2" prop='arrivalArea' :value-width="18"
                                v-model='record.arrivalArea'></decSelect-Item>


                <decSelect-Item :id="26" :focusIndex='focusIndex' :vfocusNum='focusIndex===26'
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
                                :readonly='disableEdit' :label='imSignShow?`经停港`:`指运港`'
                                :dict-key='dictKeys.GKDM' :value-width="17" :label-width="8"
                                :selectWidth="25" :styleType="1" v-model='record.desPort'></decSelect-Item>

                <decSelect-Item :id="27" :focusIndex='focusIndex' :vfocusNum='focusIndex===27'
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
                                :readonly='disableEdit' label='成交方式' :dict-key='dictKeys.CJFS'
                                :selectWidth="12.2" :styleType="1" :label-width="7" :value-width="13"
                                v-model='record.termsTypeCode'></decSelect-Item>
            </tr>

            <tr>

                <decSelect-Item :id="28" :focusIndex='focusIndex' :vfocusNum='focusIndex===28'
                                @focusIndexDate="focusIndexMethod" :label-width="10" :value-width="5"
                                :readonly='shipFeeReadonly' val-class='mini-item'
                                :dict-key='dictKeys.JGLX' label='运费'
                                :styleSrt="shipFeeReadonly==false?styleSrtSelect:''"
                                :selectWidth="12.2" :styleType="1"
                                v-model='record.shipFeeCode'></decSelect-Item>
                <!--type="number"-->
                <input-item :id="29" :readonly='shipFeeReadonly' no-label
                            v-model='record.shipFee' :value-width="8"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===29'
                            @focusUpadte="focusUpadte"
                            val-class='mini-item' @focusIndexDate="focusIndexMethod"/>
                <decSelect-Item :id="30" :focusIndex='focusIndex' :vfocusNum='focusIndex===30'
                                @focusIndexDate="focusIndexMethod" :value-width="5"
                                :readonly='shipFeeReadonly||shipCurrencyType' no-label :dict-key='dictKeys.BZDM'
                                :selectWidth="18.2" :styleType="5"
                                v-model='record.shipCurrencyCode'></decSelect-Item>


                <decSelect-Item :id="31" :focusIndex='focusIndex' :vfocusNum='focusIndex===31'
                                @focusIndexDate="focusIndexMethod" :label-width="9" :value-width="5"
                                :readonly='insuranceReadonly' val-class='mini-item'
                                :dict-key='dictKeys.JGLXBF' label='保险费'
                                :selectWidth="12.2" :styleType="1"
                                v-model='record.insuranceCode'></decSelect-Item>
                <input-item :id="32" :readonly='insuranceReadonly' no-label :value-width="8"
                            v-model='record.insurance' @focusIndexDate="focusIndexMethod"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===32'
                            val-class='mini-item'/>

                <decSelect-Item :id="33" :focusIndex='focusIndex' :vfocusNum='focusIndex===33'
                                @focusIndexDate="focusIndexMethod" :value-width="5"
                                :readonly='insuranceReadonly||insuranceType' no-label :dict-key='dictKeys.BZDM'
                                :selectWidth="18.2" :styleType="5"
                                v-model='record.insuranceCurr'></decSelect-Item>

                <decSelect-Item :id="34" :focusIndex='focusIndex' :vfocusNum='focusIndex===34'
                                @focusIndexDate="focusIndexMethod" :label-width="8" :value-width="4"
                                :readonly='disableEdit' :dict-key='dictKeys.JGLX'
                                :selectWidth="12.2" :styleType="1" label='杂费'
                                v-model='record.extrasCode'></decSelect-Item>
                <input-item :id="35" :readonly='disableEdit' no-label :value-width="8"
                            :focusIndex='focusIndex' @focusIndexDate="focusIndexMethod"
                            :vfocusNum='focusIndex===35' v-model='record.extras'/>

                <decSelect-Item :id="36" :focusIndex='focusIndex' :vfocusNum='focusIndex===36'
                                @focusIndexDate="focusIndexMethod" :value-width="5"
                                :readonly='extrasType' no-label :dict-key='dictKeys.BZDM'
                                :selectWidth="18.2" :styleType="5"
                                v-model='record.otherCurr'></decSelect-Item>

                <input-item :id="37" :readonly='disableEdit' type="number" label='件数'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===37' :label-width="7" :value-width="13"
                            @change='packsBackFn' v-model='record.packs' :styleSrt="styleSrt"
                            @focusIndexDate="focusIndexMethod"/>
            </tr>
            <tr>

                <decSelect-Item :id="38" :focusIndex='focusIndex' :vfocusNum='focusIndex===38'
                                @focusIndexDate="focusIndexMethod" :label-width="10"
                                :readonly='disableEdit' :dict-key='dictKeys.BZZL' label='包装种类'
                                val-class='mini-item' :styleSrt="styleSrtSelect" :value-width="27"
                                :selectWidth="25" :styleType="5" v-model='record.packsKinds'></decSelect-Item>
                <td colspan="18">
                    <a-button :disabled='disableEdit' block class="buttonStyle" size='small'
                              title="其他包装" type='primary' @click='show.packType = !show.packType'>其他包装
                    </a-button>
                </td>
                <input-item :id="39" :readonly='disableEdit' type="number" label='毛重(KG)'
                            v-model='record.grossWeight' :styleSrt="styleSrt" :value-width="17" :label-width="8"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===39'
                            @focusIndexDate="focusIndexMethod"/>
                <input-item :id="40" :readonly='disableEdit' type="number" label='净重(KG)' v-model='record.netWeight'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===40' :styleSrt="styleSrt"
                            @focusIndexDate="focusIndexMethod" :label-width="7" :value-width="13"/>
            </tr>

            <tr>

                <decSelect-Item :id="41" :focusIndex='focusIndex' :vfocusNum='focusIndex===41'
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :label-width="10"
                                :value-width="18"
                                :readonly='disableEdit' label='贸易国别(地区)' :dict-key='dictKeys.GBDQDEC'
                                :selectWidth="25" :styleType="2" v-model='record.tradeCountry'></decSelect-Item>
                <input-item label='集装箱数' readonly v-model='record.containerNum' :label-width="9" :value-width="18"
                            @focusUpadte="focusUpadte"/>
                <input-item label='随附单证' readonly v-model='record.contractAtt' :label-width="8" :value-width="37"/>
            </tr>
            <tr v-if="!imSignShow">
							<input-item :id="43" :readonly='disableEdit' label='货物存放地点'
													:focusIndex='focusIndex' :vfocusNum='focusIndex===43' :styleSrt="imSignShow ?styleSrt:''"
													:label-width="imSignShow ?10 :10"
													v-model='record.goodsPlace' @focusIndexDate="focusIndexMethod"
													:value-width="imSignShow ? 43 :45"/>


                <decSelect-Item :id="42" :focusIndex='focusIndex' :vfocusNum='focusIndex===42' :label-width="8"
                                :value-width="38"
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
                                :readonly='disableEdit' :label='imSignShow?`入境口岸`:`离境口岸`' :dict-key='dictKeys.GNKA'
                                :selectWidth="24.5" :styleType="1" v-model='record.entyPortCode'></decSelect-Item>



                <!--                <input-item :id="43" v-if='!imSignShow' :readonly='disableEdit' :label-width="9" :value-width='63'-->
                <!--                            label='货物存放地点'-->
                <!--                            :focusIndex='focusIndex' :vfocusNum='focusIndex===43' :styleSrt="styleSrt"-->
                <!--                            v-model='record.goodsPlace' @focusIndexDate="focusIndexMethod"/>-->

                <decSelect-Item :id="44" v-if='imSignShow' :focusIndex='focusIndex' :vfocusNum='focusIndex===44'
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :label-width="7"
                                :value-width="13"
                                :readonly='disableEdit' label='启运港' :dict-key='dictKeys.GKDM' prop='despPortCode'
                                :selectWidth="25" :styleType="1" v-model='record.despPortCode'></decSelect-Item>


            </tr>
					<tr v-if="imSignShow">
						<decSelect-Item :id="42" :focusIndex='focusIndex' :vfocusNum='focusIndex===42' :label-width="10"
														:value-width="18"
														@focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
														:readonly='disableEdit' :label='imSignShow?`入境口岸`:`离境口岸`' :dict-key='dictKeys.GNKA'
														:selectWidth="24.5" :styleType="1" v-model='record.entyPortCode'></decSelect-Item>
						<input-item :id="43" :readonly='disableEdit' label='货物存放地点'
												:focusIndex='focusIndex' :vfocusNum='focusIndex===43' :styleSrt="imSignShow ?styleSrt:''"
												:label-width="imSignShow ?9 :9"
												v-model='record.goodsPlace' @focusIndexDate="focusIndexMethod"
												:value-width="imSignShow ? 43 :45"/>

						<decSelect-Item :id="44" v-if='imSignShow' :focusIndex='focusIndex' :vfocusNum='focusIndex===44'
														@focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :label-width="7"
														:value-width="13"
														:readonly='disableEdit' label='启运港' :dict-key='dictKeys.GKDM' prop='despPortCode'
														:selectWidth="25" :styleType="1" v-model='record.despPortCode'></decSelect-Item>

					</tr>
            <tr>
							<decSelect-Item v-if="$route.query.dclTrnRelFlag=='2'||record.dclTrnRelFlag=='2'" :id="45" :focusIndex='focusIndex'
															:vfocusNum='focusIndex===45' :label-width="10"
															@focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect"
															:value-width="18"
															:readonly='disableEdit' label='清单类型' :options='dictOptions.bgdqdlx'
															:selectWidth="14.2" :styleType="4" v-model='record.listType'></decSelect-Item>

                <decSelect-Item  v-else :id="45" :focusIndex='focusIndex' :vfocusNum='focusIndex===45' :label-width="10"
                                @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :value-width="18"
                                :readonly='disableEdit' label='报关单类型' :dict-key='dictKeys.BGDLX'
                                :selectWidth="12.2" :styleType="1" v-model='record.clearanceType'></decSelect-Item>

                <input-item :id="46" :readonly='disableEdit' label='备注' v-model='record.markNumber'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===46' :label-width="9" :value-width="47"
                            @focusIndexDate="focusIndexMethod"/>
                <td colspan="3">
                    <a-button :disabled='disableEdit' title="备注文本域" @click="markNumberButton" size='small'
                              type='primary' class="buttonStyle">...
                    </a-button>
                </td>
                <td colspan="13">
                    <a-button :id="47" :disabled='disableEdit' title="价格说明"
                              @click='show.promiseItems = !show.promiseItems'
                              block v-focus='focusIndex===47'
                              size='small' type='primary' class="buttonStyle">价格说明
                    </a-button>
                </td>
            </tr>
            <tr>
                <td colspan='28'>
                    <a-icon @click='listHeaderOther = !listHeaderOther'
                            type='right-circle'
                            v-show='!listHeaderOther'/>
                    <a-icon @click='listHeaderOther = !listHeaderOther'
                            type='down-circle'
                            v-show='listHeaderOther'/>
                </td>
                <input-item :id="53" :readonly='disableEdit' label='标记唛码' v-model='record.markNo'
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===53' :styleSrt="styleSrt"
                            :label-width="9" :value-width="46" @focusIndexDate="focusIndexMethod"/>
                <td colspan="2">
                    <a-button :disabled='disableEdit' class="buttonStyle" size='small' title="标记唛码文本域"
                            type='primary' @click="markNoButton">...
                    </a-button>

                </td>
                <td  colspan="2">
                    <a-button :disabled='disableEdit' class="buttonStyle" icon="cloud-upload" size='small' type="primary"  @click="bjhmfileInfo=!bjhmfileInfo" />
                </td>
                <td colspan="13">
                    <a-button :id="54" :disabled='disableEdit' title="业务事项" @click='show.decType = !show.decType'
                              block
                              size='small' v-focus='focusIndex===54'
                              type='primary' class="buttonStyle">业务事项
                    </a-button>
                </td>
            </tr>
            <!--            </fragment>-->
            <!--表头折叠部分-->
            <fragment>
                <tr v-show='listHeaderOther'>

                    <!-- <decSelect-Item  :id="55" :focusIndex='focusIndex' :vfocusNum='focusIndex===55'
                                    @focusIndexDate="focusIndexMethod" :styleSrt="styleSrtSelect" :label-width="10"
                                    :value-width="45"
                                    :readonly='disableEdit' label='检验检疫受理机关' :dict-key='dictKeys.JYJG'
                                    :selectWidth="36.5" :styleType="1" v-model='record.orgCode'></decSelect-Item> -->
                    <decSelect-Item :id="59" v-model='record.purpOrgCode' :dict-key='dictKeys.JYJG'
                    :focusIndex='focusIndex' :label-width="10" :readonly='disableEdit'
                    :selectWidth="36.5"
                    :styleSrt="styleSrtSelect" :styleType="1" :value-width="imSignShow?18:45"
                    :vfocusNum='focusIndex===59' label='目的地海关' @focusIndexDate="focusIndexMethod"></decSelect-Item>
                    <date-item v-if="imSignShow" ref="despDateRef" v-model='record.despDate' :label-width="8" :readonly='disableEdit'
                               :value-width="imSignShow?17:17" label='启运日期' @change="changeDespDate"
                               @focusIndexDate="focusIndexMethod"/>
                    <!--todo: 此处为模态框-->
                    <input-item label='企业资质' readonly v-model='record.txtEntQualifNo' :label-width="imSignShow?8:10"
                                :value-width="16"/>
                    <input-item v-model='record.txtEntQualifTypeCode' :value-width="14" no-label readonly/>
                    <td colspan="9">
                        <a-button-group size='small' style='display: flex' class="buttonStyle">
                            <a-button :disabled='disableEdit' title="向左" @click='copLimitSubtract' icon='left'
                                      style='flex: 1'
                                      type='primary' class="buttonStyle"></a-button>
                            <a-button :disabled='disableEdit' title="向右" @click='copLimitPlus' icon='right'
                                      style='flex: 1'
                                      type='primary' class="buttonStyle"></a-button>
                            <a-button :disabled='disableEdit' title="企业资质编辑"
                                      @click='show.copLimitType = !show.copLimitType' icon='dash' style='flex: 1'
                                      type='primary' class="buttonStyle"></a-button>
                        </a-button-group>
                    </td>
                </tr>
                <tr v-show='listHeaderOther'>


                    <input-item v-if="imSignShow" :id="58" :readonly='disableEdit' label='B/L号' @focusIndexDate="focusIndexMethod"
                                :label-width="!imSignShow?7:10" :value-width="!imSignShow?13:18"
                                :focusIndex='focusIndex' :vfocusNum='focusIndex===58' v-model='record.blNo'/>
                                <input-item :id="60" :readonly='disableEdit' label='关联号码及理由'
                                @focusIndexDate="focusIndexMethod" :label-width="imSignShow?9:10" :value-width="imSignShow?26:18"
                                :focusIndex='focusIndex' :vfocusNum='focusIndex===60' v-model='record.correlationNo'/><!--:value-width="26"-->


                    <decSelect-Item :id="61" :focusIndex='focusIndex' :vfocusNum='focusIndex===61'
                                    @focusIndexDate="focusIndexMethod" :value-width="24"
                                    :readonly='disableEdit' no-label :dict-key='dictKeys.GLLY'
                                    :selectWidth="25" :styleType="1"
                                    v-model='record.correlationReasonFlag'></decSelect-Item>
                    <td colspan="13">
                        <a-button :disabled='disableEdit' title="使用人" @click="show.decUserType = !show.decUserType"
                                  block size='small' type='primary' class="buttonStyle">使用人
                        </a-button>
                    </td>
                </tr>
                <tr v-show='listHeaderOther'>
                    <decSelect-Item v-if="imSignShow" :id="62" :options='dictOptions.origBoxFlag' :selectWidth="12"
                                    @focusIndexDate="focusIndexMethod" :focusIndex='focusIndex'
                                    :vfocusNum='focusIndex===62' label='原箱运输'
                                    :styleType="3" :label-width="10" :value-width="18"
                                    v-model='record.origBoxFlag'></decSelect-Item>

                    <input-item v-model='record.specDeclFlagStr' readonly
                                val-class='mini-item' label='特殊业务标识' :label-width="imSignShow?9:10" :value-width="imSignShow?16:43"/><!--:label-width="9" :value-width="16"-->
                    <td colspan="2">
                        <a-button :id="63" :disabled='disableEdit' title="特殊业务标识"
                                  v-focus='focusIndex===63'
                                  @click="show.specDeclFlagType = !show.specDeclFlagType" block
                                  size='small' type='primary' class="buttonStyle">...
                        </a-button>
                    </td>
                    <input-item label='所需单证' readonly v-model='record.txtrequestCertType'
                                :value-width="24" :label-width="imSignShow?8:10"/><!--:label-width="8"-->
                    <td colspan="13">
                        <a-button :disabled='disableEdit' title="检验检疫签证申报要素"
                                  @click="show.decCertType = !show.decCertType" block size='small'
                                  type='primary' class="buttonStyle">
                            检验检疫签证申报要素
                        </a-button>
                    </td>
                </tr>
            </fragment>
            <!--表体-->
            <details-info-left-bottom ref="derailsInfoLeftBotton"
                                      :focusIndexs.sync="focusIndex"
                                      :txt-apply-type='txtApplyType'
                                      :decLists.sync='record.decLists'
                                      :disable-edit='disableEdit'
                                      :imSignShowList='imSignShowList'
                                      :decStatus="record.decStatus"
                                      :decHeadId="record.id"
																			:optUnitSocialCode="record.optUnitSocialCode"
																			:declareUnitSocialCode="record.declareUnitSocialCode"
																			:IE_FLAG="record.ieFlag"
																			:optUnitId="record.optUnitId"
																			:optUnitName="record.optUnitName"
                                      @focusIndexsUpadte='focusIndexsListUpadte'
                                      v-model='record'/>

        </table>
        <!--编辑其它包装信息-->
        <pack-type :show='show.packType' v-model='record.packType'/>
        <!--其pack它事项确认-->
        <promise-items :show='show.promiseItems' v-model='record.promiseItmes' @keyFromPromise="keyFromPromise"/>
        <!--业务事项-->
        <dec-type :chk-surety='record.chkSurety' :show='show.decType'
                  :txtDecTypeRecord.sync='record' @keyFromPromise="keyFromPromise"
                  @chkSurety='chkSurety=>record.chkSurety = chkSurety'
                  v-model='record.decType'/>
        <!--企业资质-->
        <cop-limit-type :show='show.copLimitType' :imSign='imSignId' :copLimitTypeList='copLimitTypeList'
                        :record='record'
                        v-model='record.copLimitType'></cop-limit-type>
        <!--使用人-->
        <dec-user-type :show='show.decUserType' v-model='record.decUserType'></dec-user-type>
        <!--检验检疫签证申报要素-->
        <dec-cert-type :show='show.decCertType'
                       v-model='record.requestCertType' @txtrequestCertType="txtrequestCertType"></dec-cert-type>
        <!--特俗业务标识-->
        <spec-decl-flag :show='show.specDeclFlagType' v-model='record.specDeclFlag'
                        @keyFromPromise="keyFromPromise" @resp="specDeclFlagResp"></spec-decl-flag>
				<!--舱单数据展示-->
				<manifest-modal ref="manifestRef" :decHead="record" @callback="manifestInfoCallback"/>
        <template>

        </template>
        <template>
            <a-modal
                title="备注"
                :visible="markNumberTest"
                @ok="markNumberTestOk"
                @cancel="markNumberTest=!markNumberTest"
            >
                <a-textarea  placeholder="备注" v-model='markNumberStr' :rows="4" />
            </a-modal>
        </template>
        <a-modal
                title="标记唛码"
                :visible="markNoTest"
                @ok="markNoTestOk"
                @cancel="markNoTest=!markNoTest"
            >
                <a-textarea  v-model='mmarkNoStr' :rows="4" placeholder="标记唛码" />
            </a-modal>
            <a-modal
             :visible="bjhmfileInfo"
              title="编辑标记及号码附件信息"
               @cancel="bjhmfileInfo=!bjhmfileInfo"
                @ok="bjhmfileInfo=!bjhmfileInfo"
            >
            <a-upload
                :headers="headers"
                :multiple="false"
                action=""
                name="file"
                @change="()=>{}"
            >
                <a-button> <a-icon type="upload" /> 点击上传 </a-button>
            </a-upload>

            </a-modal>
    </a-form-model>

</template>

<script>
    import InputItem from '@views/declaration/component/m-dec-table-input-item'
    import DateItem from '@views/declaration/component/m-table-date-item'
    import ButtonItem from '@views/declaration/component/m_table_top_button_item'
    import { Fragment } from 'vue-fragment'
    import { singleOperator } from '@views/Business/mixin/single-operator'
    import DetailsInfoLeftBottom from '@views/Business/customs-declaration/details-info-left-bottom'
    import PackType from '@views/Business/component/modal/pack-type'
    import PromiseItems from '@views/Business/component/modal/promise-items'
    import DecType from '@views/Business/component/modal/dec-type'
    import CopLimitType from '@views/Business/component/modal/cop-limit-type'
    import DecUserType from '@views/Business/component/modal/dec-user-type'
    import DecCertType from '@views/Business/component/modal/dec-cert-type'
    import SpecDeclFlag from '@views/Business/component/modal/spec-decl-flag'
    import { getAction, postAction } from '@/api/manage'
    import decSelectItem from './m-table-select-item.vue'
		import ManifestModal from "@/views/Business/component/ManifestModal.vue";
        import store from '@/store'

    export default {
        name: 'details-info-left',
        mixins: [singleOperator],
        components: {
            DecType,
            PromiseItems,
            DetailsInfoLeftBottom,
            ButtonItem,
            InputItem,
            DateItem,
            PackType,
            CopLimitType,
            DecUserType,
            DecCertType,
            SpecDeclFlag,
            decSelectItem,
            Fragment,
					ManifestModal
        },
        data() {
            return {
							overseasConsignorEnameHistoryList:[],
                headers:{},
                bjhmfileInfo:false,
                basicOptions:[
                    {title: '否', value: '0'},
                    {title: '是', value: '1'},
                    {title: '', value: '9'}
                ],
                styleSrt: 'font-size: 11px;background:#FAFFBD',
                styleSrtSelect: 'background:#FAFFBD',
                /**
                 *1.进口报关单 成交方式是CIF 运费保费不能填
                                       C&F 运费不能填
                                       C&I  保费不能填
                 2.出口报关单 成交方式是C&F 保费不能填
                                        FOB  运费 保费不能填
                                        C&I  运费不能填
                 * */
                shipFeeReadonly: false,//运费
                insuranceReadonly: false,//保费

                shipCurrencyType: false,//运费币值
                insuranceType: false,//保险费币值
                extrasType: false,//杂费币值

                /**
                 * 为true是表示进口报关单, 否则为出口报关单
                 */
                isIn: false,
                // 展示相关项
                show: {
                    packType: false,
                    promiseItems: false,
                    decType: false,
                    copLimitType: false,
                    decUserType: false,
                    decCertType: false,
                    specDeclFlagType: false
                },
                //区分进出口显示隐藏项为 true是表示进口报关单, 否则为出口报关单
                imSignShow: false,
                imSignShowList: true,
                // 字典库dictKey
                dictKeys: {

                    // 运输方式
                    YSFS: 'erp_transport_types,name,code,1=1 order by code',
                    // 关区代码
                    GQDM: 'erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code ',
                    // 国别地区
                    // GBDQDEC: 'erp_countries,name,code,isenabled=1',
                    GBDQDEC: 'GBDQ-DEC',
                    // 监管方式
                    JGFS: 'JGFS',
                    //征免方式
                    ZJMSFS: 'ZJMSFS',
                    //征免性质
                    ZMXZ: 'ZMXZ',
                    // 港口
                    // GKDM:'GKDM',
                    GKDM:'erp_cityports,cnname,cityport_code,isenabled=1',
                    // 成交方式
                    CJFS: 'trading_type',
                    // 价格类型
                    JGLX: 'freight_amount_type',
                    JGLXBF: 'premium_amount_type',
                    // 币制代码
                    // BZDM: 'erp_currencies,name,currency,1=1 order by currency_order desc',
                    BZDM: 'BZDM',
                    // 包装种类
                    BZZL: 'erp_packages_types,name,code,isenabled=1',
                    // 国内口岸
                    // GNKA: 'erp_cityports,cnname,cityport_code,isenabled=1',
                    GNKA: 'erp_china_ports,name,china_port_code,1=1',
                    // 检疫机关
                    JYJG: 'JYJG',
                    // 报关单类型
                    BGDLX: 'BGDLX',
									// 清单类型
									BGDQDLX:'BGDQDLX',
                    // 关联理由
                    GLLY: 'GLLY'

                },
                record: {
                    //  id
                    id: '',
                    // 委托单号
                    applyNumber: '',
                    // 报关单分类 （0：一般报关单 1：转关提前报关单 2：备案清单 3：转关提前备案清单 4：出口二次转关）
                    dclTrnRelFlag: '',
                    // 申报状态
                    decStatus: '',
                    // 分票序号
                    partId: '',
                    // 电子委托号 暂时未使用
                    elecDelNo: '',
                    // 客户端编号 原CUSTOMS_CODE
                    clientSeqNo: '',
                    // 统一编号
                    seqNo: '',
                    // 报关单号
                    clearanceNo: '',
                    // 备案号
                    recordNumber: '',
                    // 进出口标识
                    ieFlag: '',
                    // 进出日期
                    outDate: '',
                    // 申报日期
                    appDate: '',
                    // 运输方式 海运2航空5
                    shipTypeCode: '',
                    // 运输工具名称
                    shipName: '',
                    // 航次
                    voyage: '',
                    // 合同协议号
                    contract: '',
                    // 提运单号
                    billCode: '',
                    // 申报地海关
                    declarePlace: '',
                    // 进出境关别
                    outPortCode: '',
                    // 入境口岸/离境口岸
                    entyPortCode: '',
                    // 境外收货人代码
                    overseasConsigneeCode: '',
                    // 境外收货人名称（外文）
                    overseasConsigneeEname: '',
                    // 境外发货人代码
                    overseasConsignorCode: '',
                    // 境外发货人名称（外文）
                    overseasConsignorEname: '',
                    // 境内收发货人社会统一信用代码
                    optUnitSocialCode: '',
                    // 境内收发货人海关代码
                    optUnitId: '',
                    // 境内收发货人检验检疫编码
                    tradeCiqCode: '',
                    // 境内收发货人名称
                    optUnitName: '',
                    // 消费使用单位社会统一信用代码
                    deliverUnitSocialCode: '',
                    // 消费使用单位海关代码
                    deliverUnit: '',
                    // 消费使用单位检验检疫编码
                    ownerCiqCode: '',
                    // 消费使用单位名称
                    deliverUnitName: '',
                    // 申报单位社会统一信用代码
                    declareUnitSocialCode: '',
                    // 申报单位海关代码
                    declareUnit: '',
                    // 申报单位检验检疫编码
                    declCiqCode: '',
                    // 申报单位名称
                    declareUnitName: '',
                    // 贸易国
                    tradeCountry: '',
                    // 启运国
                    arrivalArea: '',
                    // 启运港代码
                    despPortCode: '',
                    // 经停港/指运港
                    desPort: '',
                    // 成交方式
                    termsTypeCode: '',
                    // 监管方式
                    tradeTypeCode: '',
                    // 征免性质
                    taxTypeCode: '',
                    // 运费代码
                    shipFeeCode: '',
                    // 运费值
                    shipFee: '',
                    // 运费币制
                    shipCurrencyCode: '',
                    // 保费代码
                    insuranceCode: '',
                    // 保费值
                    insurance: '',
                    // 保费币制
                    insuranceCurr: '',
                    // 杂费代码
                    extrasCode: '',
                    // 杂费值
                    extras: '',
                    // 杂费币制
                    otherCurr: '',
                    // 件数
                    packs: '',
                    // 毛重
                    grossWeight: '',
                    // 净重
                    netWeight: '',
                    // 许可证号
                    licenceNumber: '',
                    // 包装种类
                    packsKinds: '',
                    // 其他包装
                    packType: '',
                    // 集装箱数
                    containerNum: '',
                    // 报关单类型
                    clearanceType: '',
                    // 货物存放地点
                    goodsPlace: '',
                    // 随附单证
                    contractAtt: '',
                    // 备注
                    markNumber: '',
                    // 标记唛码
                    markNo: '',
                    // 是否担保验放 ？？？？？
                    chkSurety: '',
                    // 特殊关系
                    promiseItmes: '',
                    // 关联报关单号
                    relId: '',
                    // 关联备案号
                    relManNo: '',
                    // 保税监管场地
                    bonNo: '',
                    // 场地代码
                    cusFie: '',
                    // 体积 ？？？？？
                    volume: '',
                    // 总金额 ？？？？？
                    total: '',
                    // 总数量 ？？？？？
                    goodsCount: '',
                    // 检验检疫受理机关 商检信息
                    orgCode: '',
                    // 口岸检验检疫机关 商检信息
                    inspOrgCode: '',
                    // 目的地检验检疫机关 商检信息
                    purpOrgCode: '',
                    // 领证机关 商检信息
                    vsaOrgCode: '',
                    // B/LNO 商检信息
                    blNo: '',
                    // 特殊业务标识 商检信息
                    specDeclFlag: '',
                    // 启运日期 格式为：yyyyMMdd 商检信息
                    despDate: '',
                    // 卸毕日期 格式为：yyyyMMdd ？？？？？？
                    cmplDschrgDt: '',
                    // 关联号码 商检信息
                    correlationNo: '',
                    // 关联理由 商检信息
                    correlationReasonFlag: '',
                    // 原集装箱标识 商检信息
                    origBoxFlag: '',
                    // 企业资质信息 商检信息[{"EntQualifNo":"123","EntQualifTypeCode":"456"}]
                    copLimitType: '',
                    txtEntQualifNo: '',//临时字段
                    txtEntQualifTypeCode: '',//临时字段
                    // 使用人信息表 商检信息[{"UseOrgPersonCode":"123","UseOrgPersonTel":"456"}]
                    decUserType: '',
                    // 检验检疫签证申报要素 商检信息[{"AppCertCode":"123","ApplOri","456","ApplCopyQuan":"789"}]
                    requestCertType: '',
                    txtrequestCertType: '',//临时字段
                    // 报关员
                    declarant: '',
                    // 报关人员证号
                    declarantNo: '',
                    // 报关单状态 保存；发送
                    status: '',
                    // 转关类型 报文用
                    tranferType: '',
                    // 通关模式 报文用
                    clearanceMode: '',
                    // 申报单类型 报文用属地报关SD；备案清单：ML。LY：两单一审备案清单。CL:汇总征税报关单。SS:”属地申报，属地验放”
                    decType: '',
                    // 报关标志 1：普通报关 3：北方转关提前 5：南方转关提前 6：普通报关，运输工具名称以‘◎’开头，南方H2000直转 报文用
                    ediId: '',
                    // 企业内部清单编号 （由企业自行编写） 与核注单关联
                    etpsInnerInvtNo: '',
                    opt: '',
                    decLists: [],
                    profileType: '',
                    tmp6:''//已实施预防性消毒
                },
                // 显示表头折叠项
                listHeaderOther: false,
                // selectedRowKeys
                selectedRowKeys: [],
                leftBottom: [],
                // 本地下拉框
                dictOptions: {
                    origBoxFlag: [
                        { title: '0-否', value: '0' },
                        { title: '1-是', value: '1' }
                    ],
                    decStatus: [
                        { title: '保存', value: '1' },
                        { title: '结关', value: '10' },
                        { title: '查验通知', value: '11' },
                        { title: '已申报', value: '2' },
                        { title: '海关入库成功', value: '4' },
                        { title: '退单', value: '6' },
                        { title: '审结', value: '7' },
                        { title: '删单', value: '8' },
                        { title: '放行', value: '9' },
                        { title: '公自用物品核准通过', value: 'S' },
                        { title: '公自用物品退单', value: 'T' },
                        { title: '公自用物品待核准', value: 'U' },
                        // { title: '查验通知', value: 'C' }
                    ],
									ysfs:[],
									bgdqdlx:[]
                },
                focusIndex: 0, //用来存放下一个应该聚焦的index值
                copLimitTypeList: [],//企业资质临时集合
                copLimitIndex: 0,
                decCertTypeList: [],//所需单证集合
                tdOne: 'tdOneWidthClass',
                tdDeclarePlaceClass: 'tdDeclarePlaceClass',
                tdTwoClass: 'tdTwoClass',
                tdDecStatus: 'tdDecStatus',

                tdClass: 'tdWidth',

                tdOneClass: 'tdOneWidth',
                tdVoyageClass: 'tdVoyageClass',
                downCustomsDataLoading: false,
                downDecStatusTypes:false,
                markNumberTest:false,
                markNoTest:false,
                markNumberStr:'',
                mmarkNoStr:'',

            }
        },

        props: {
            disableEdit: {
                type: Boolean
            },
            // 表体
            decLists: {
                type: Array,
                default: () => []
            },
            // v-model
            value: {
                type: Object
            },
            txtApplyType: {
                type: Boolean
            },
            focusIndexs: {
                type: Number
            },
            downDecStatusType: {
                type: Boolean
            },
            // imSignShowList:{
            //     type: Boolean
            // }
            imSignId: {
                type: String
            },
        },

        created() {
            this.loadYSFS()
					this.loadBGDQDLX()
            this.record = this.value
            if (this.record.imSign == 1) {
                this.imSignShow = true
                this.imSignShowList = true
            } else {
                this.imSignShow = false
                this.imSignShowList = false
            }

            let tenantInfo = store.getters.tenantInfo
            this.headers['X-Access-Token'] = store.getters.token
            this.headers['Rest-Flag'] = 1
            this.headers['Tenant-Id'] = tenantInfo.id
					this.listOverseasConsignorHistory()
        },
        methods: {
					async listOverseasConsignorHistory(){
						let res = await getAction('/DecHead/dec-head/listOverseasConsignorHistory',{
							ieFlag:this.imSignShow?'I':'E'})
						if(res.success&&res.result){
							for(let o of res.result){
								let obj = {value:o,text:o}
								this.overseasConsignorEnameHistoryList.push(obj)
							}
						}
					},
            specDeclFlagResp(val){
                this.record.specDeclFlagStr=val
            },
					manifestInfoCallback(decHead){
						this.record = decHead
					},
					/**
					 * 获取舱单信息
					 */
					async getManifestInfo() {

						if (!this.record.shipTypeCode) {
							this.$message.warning('请先选择运输方式!')
							return false
						}
						if (!this.record.outPortCode) {
							let msg = this.imSignShow ? `进境关别` :`出境关别`
							this.$message.warning('请先选择' + msg + '!')
							return false
						}
						if (!this.record.billCode) {
							this.$message.warning('请输入提运单号!')
							return false
						}
						this.$message.info('正在调用舱单数据，请稍等···')
						this.$refs.manifestRef.dataSourceHead=[]
						this.$refs.manifestRef.dataSourceBox=[]
						//20241211调用海运舱单数据前，先调用普通清单数据，回填数据
						if(this.record.shipTypeCode == '2'){
						let res = await getAction('/DecHead/dec-head/getManifestInfo', {
							id: this.record.id,
							pc: this.record.outPortCode,
							tt: this.record.shipTypeCode,
							wb: this.record.billCode
						})
						if (res && res.result&&res.result.length>0) {
							let dataSourceHead = []
							//组装表格数据
							dataSourceHead=[{title:'进出境关别',bxt:this.record.outPortCode,ypcd:res.result[0].customsOfficeID,yd:res.result[0].cargoArrivalState},
								{title:'件数',bxt:this.record.packs,ypcd:res.result[0].quantity,yd:res.result[0].cargoArrivalState},
								{title:'毛重',bxt:this.record.grossWeight,ypcd:res.result[0].grossMassMeasure,yd:res.result[0].cargoArrivalState},
								{title:'运输工具名称',bxt:this.record.shipName,ypcd:res.result[0].transportName,yd:res.result[0].cargoArrivalState},
								{title:'航次号',bxt:this.record.voyage,ypcd:res.result[0].journeyID,yd:res.result[0].cargoArrivalState}]
							this.$refs.manifestRef.dataSourceHead = dataSourceHead
							this.record.shipName = res.result[0].transportName
							this.record.voyage = res.result[0].journeyID

						}
						}

						if (this.record.shipTypeCode != '2' && this.record.shipTypeCode != '5') {
							this.$message.warning('查询舱单只支持海运和空运!')
							return false
						}
						// if (this.record.shipTypeCode == '2' && !this.record.shipName) {
						// 	this.$message.warning('请输入运输工具名称!')
						// 	return false
						// }
						// if (this.record.shipTypeCode == '2' && !this.record.voyage) {
						// 	this.$message.warning('请输入航次号!')
						// 	return false
						// }
						this.$refs.manifestRef.open(this.record)
					},
        	loadBGDQDLX(){
						//先从缓存获取，获取不到请求
						let getLocalData = sessionStorage.getItem(this.dictKeys.BGDQDLX);
						if(getLocalData){
							let jsonData = JSON.parse(getLocalData);
							this.dictOptions.bgdqdlx = jsonData
						}else {
							getAction(`/sys/dict/getDictItems/${this.dictKeys.BGDQDLX}`,
								).then(async (res) => {
								if (res.success) {
									let list = [];
									for(let dict of res.result){
										let obj={
											value: dict.value,
											text: dict.text,
											title:dict.text,
										}
										list.push(obj);
									}
									this.dictOptions.bgdqdlx = list;
									let str_jsonData = JSON.stringify(this.dictOptions.bgdqdlx);
									sessionStorage.setItem(this.dictKeys.BGDQDLX, str_jsonData);
								}
							})
						}
					},
        	loadYSFS(){
        		//先从缓存获取，获取不到请求
						let getLocalData = sessionStorage.getItem(this.dictKeys.YSFS);
						if(getLocalData){
							let jsonData = JSON.parse(getLocalData);
							this.dictOptions.ysfs = jsonData
						}else {
						 getAction(`/sys/dict/loadDict/${this.dictKeys.YSFS}`,
							 { keyword: '', pageSize: '' }).then(async (res) => {
							if (res.success) {
								let list = [];
								for(let dict of res.result){
									let obj={
										value: dict.value,
										text: dict.text,
										title:dict.text,
									}
									list.push(obj);
								}
								this.dictOptions.ysfs = list;
								let str_jsonData = JSON.stringify(this.dictOptions.ysfs);
								 sessionStorage.setItem(this.dictKeys.YSFS, str_jsonData);
							}
						})
						}
					},
            //判断是否为空
            isEmpty(obj) {
                if (typeof obj == 'undefined' || obj == null || obj == '') {
                    return true
                } else {
                    return false
                }
            },
            changeRightBottom() {
                this.$refs.derailsInfoLeftBotton.listEmpty()
            },
            termsTypeCodeChange() {
                if (this.record.ieFlag == 'I') {
                    if (this.record.termsTypeCode == '1') {
                        this.shipFeeReadonly = true
                        this.insuranceReadonly = true

                        this.record.shipFeeCode = ''
                        this.record.shipFee = ''
                        this.record.shipCurrencyCode = ''

                        this.record.insuranceCode = ''
                        this.record.insurance = ''
                        this.record.insuranceCurr = ''

                    } else if (this.record.termsTypeCode == '2') {
                        this.shipFeeReadonly = true
                        this.insuranceReadonly = false

                        this.record.shipFeeCode = ''
                        this.record.shipFee = ''
                        this.record.shipCurrencyCode = ''

                    } else if (this.record.termsTypeCode == '4') {
                        this.shipFeeReadonly = false
                        this.insuranceReadonly = true

                        this.record.insuranceCode = ''
                        this.record.insurance = ''
                        this.record.insuranceCurr = ''
                    } else {
                        this.shipFeeReadonly = false
                        this.insuranceReadonly = false
                    }

                } else if (this.record.ieFlag == 'E') {
                    if (this.record.termsTypeCode == '2') {
                        this.shipFeeReadonly = false
                        this.insuranceReadonly = true

                        this.record.insuranceCode = ''
                        this.record.insurance = ''
                        this.record.insuranceCurr = ''
                    } else if (this.record.termsTypeCode == '3') {
                        this.shipFeeReadonly = true
                        this.insuranceReadonly = true

                        this.record.shipFeeCode = ''
                        this.record.shipFee = ''
                        this.record.shipCurrencyCode = ''

                        this.record.insuranceCode = ''
                        this.record.insurance = ''
                        this.record.insuranceCurr = ''
                    } else if (this.record.termsTypeCode == '4') {
                        this.shipFeeReadonly = true
                        this.insuranceReadonly = false

                        this.record.shipFeeCode = ''
                        this.record.shipFee = ''
                        this.record.shipCurrencyCode = ''
                    } else {
                        this.shipFeeReadonly = false
                        this.insuranceReadonly = false
                    }
                }
            },
            async focusIndexMethod(data) {
                console.log(data)
                let focusType = true
                if (data === 44 && !this.imSignShow) {//出口没有起运港
                    data = data + 1
                    this.focusIndex = data
                } else if (data === 3.1) {
                    // this.$refs.outDateRef.inputFocus()
                    this.focusIndex = data
                } else if (data === 28) {//成交方式回车 界面输入限制：

                    this.termsTypeCodeChange()
                    this.focusIndex = data
                } else if (data === 54) {//标记买吗跳转到表体
                    return this.focusIndex = 64//跳到表体
                } else if (data === 57.1) {
                    this.$refs.despDateRef.inputFocus()
                    this.$refs.despDateRef.click()
                } else {
										this.eventLogic(data)
                    this.focusIndex = data
                }


            },
					async eventLogic( data) {
						console.log('-----------')
						console.log(data)
						console.log('-----------')
				      if(data === 5&&this.record.optUnitSocialCode){
							//境内发货人 输入18位社会信用代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode: this.record.optUnitSocialCode,
									departcd:'',
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.optUnitId = res.result.departcd //海关十位
										this.record.tradeCiqCode = res.result.ciqCode //检验检疫代码
										this.record.optUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 6&&this.record.optUnitId){
							//境内发货人 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode:'',
									departcd: this.record.optUnitId,
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.optUnitSocialCode = res.result.socialCode //18位
										this.record.tradeCiqCode = res.result.ciqCode //检验检疫代码
										this.record.optUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 7&&this.record.tradeCiqCode){
							//境内发货人 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode:'',
									departcd: '',
									tradeCiqCode: this.record.tradeCiqCode,
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.optUnitSocialCode = res.result.socialCode //18位
										// this.record.tradeCiqCode = res.result.ciqCode //检验检疫代码
										this.record.optUnitName = res.result.departName //企业名称
										this.record.optUnitId = res.result.departcd //海关十位
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 8&&this.record.optUnitName){
							//境内发货人 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode:'',
									departcd: '',
									tradeCiqCode: '',
									customerName: this.record.optUnitName
								}).then((res) => {
									if (res.success) {
										this.record.optUnitSocialCode = res.result.socialCode //18位
										this.record.tradeCiqCode = res.result.ciqCode //检验检疫代码
										// this.record.optUnitName = res.result.departName //企业名称
										this.record.optUnitId = res.result.departcd //海关十位
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 11&&this.record.deliverUnitSocialCode){
							//生产销售单位 输入18位社会信用代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode: this.record.deliverUnitSocialCode,
									departcd:'',
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.deliverUnit = res.result.departcd //海关十位
										this.record.ownerCiqCode = res.result.ciqCode //检验检疫代码
										this.record.deliverUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 12&&this.record.deliverUnit){
							//生产销售单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: this.record.deliverUnit,
									socialCode:'',
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.deliverUnitSocialCode = res.result.socialCode //18位
										this.record.ownerCiqCode = res.result.ciqCode //检验检疫代码
										this.record.deliverUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})

						}else if(data === 13&&this.record.ownerCiqCode){
							//生产销售单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: '',
									socialCode:'',
									tradeCiqCode: this.record.ownerCiqCode,
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.deliverUnitSocialCode = res.result.socialCode //18位
										// this.record.ownerCiqCode = res.result.ciqCode //检验检疫代码
										this.record.deliverUnit = res.result.departcd //海关十位
										this.record.deliverUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})

						}else if(data === 14&&this.record.deliverUnitName){
							//生产销售单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: '',
									socialCode:'',
									tradeCiqCode: '',
									customerName: this.record.deliverUnitName
								}).then((res) => {
									if (res.success) {
										this.record.deliverUnitSocialCode = res.result.socialCode //18位
										this.record.ownerCiqCode = res.result.ciqCode //检验检疫代码
										// this.record.deliverUnitName = res.result.departName //企业名称
										this.record.deliverUnit = res.result.departcd //海关十位
									}else{
                                        this.$message.warning(res.message)
                                    }
								})

						}else if(data === 15&&this.record.declareUnitSocialCode){
							//申报单位 输入18位社会信用代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									socialCode: this.record.declareUnitSocialCode,
									departcd:'',
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.declareUnit = res.result.departcd //海关十位
										this.record.declCiqCode = res.result.ciqCode //检验检疫代码
										this.record.declareUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})

						}else if(data === 16&&this.record.declareUnit){
							//申报单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: this.record.declareUnit,
									socialCode:'',
									tradeCiqCode: '',
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.declareUnitSocialCode = res.result.socialCode  //18位
										this.record.declCiqCode = res.result.ciqCode //检验检疫代码
										this.record.declareUnitName = res.result.departName //企业名称
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 17&&this.record.declCiqCode){
							//申报单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: '',
									socialCode:'',
									tradeCiqCode: this.record.declCiqCode,
									customerName: ''
								}).then((res) => {
									if (res.success) {
										this.record.declareUnitSocialCode = res.result.socialCode  //18位
										// this.record.declCiqCode = res.result.ciqCode //检验检疫代码
										this.record.declareUnitName = res.result.departName //企业名称
										this.record.declareUnit = res.result.departcd //海关十位
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}else if(data === 18&&this.record.declareUnitName){
							//申报单位 输入10位海关代码回车带出全部信息
								getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
									departcd: '',
									socialCode:'',
									tradeCiqCode: '',
									customerName: this.record.declareUnitName
								}).then((res) => {
									if (res.success) {
										this.record.declareUnitSocialCode = res.result.socialCode  //18位
										this.record.declCiqCode = res.result.ciqCode //检验检疫代码
										// this.record.declareUnitName = res.result.departName //企业名称
										this.record.declareUnit = res.result.departcd //海关十位
									}else{
                                        this.$message.warning(res.message)
                                    }
								})
						}
						this.focusIndex = data
					},
            //下拉点击获取当前的id
            focusUpadte(e) {
                this.$emit('focusUpadte', e)
            },
            keyFromPromise(e) {
                if (e == 'chkSurety' && !this.listHeaderOther) {//业务事项
                    return this.focusIndex = 64//跳到表体
                } else if (e == 'specDeclFlagType') {
                    return this.focusIndex = 64 //跳到表体
                } else if (e == 'promiseItems') {//其他事項原来是跳转到集装箱号现在是跳转到标记唛码
                    return this.focusIndex = 53 //标记唛码
                } else {
                    return this.focusIndex = this.focusIndex + 1
                }

            },
            /**
             * 许可证号输入
             */
            licenceNumberChange() {
                if (this.record.licenceNumber.length == 2) {
                    this.record.licenceNumber = this.record.licenceNumber + '-'
                } else if (this.record.licenceNumber.length == 5) {
                    this.record.licenceNumber = this.record.licenceNumber + '-'
                }

            },

            txtrequestCertType(e){
                this.record.txtrequestCertType = e
            },
            billCodeOnChange(e){
                if(e.target.checked){//大写
                    let sd = this.record.billCode.toUpperCase()
                    this.record.billCode =sd

                }else{//小写
                    let sd = this.record.billCode.toLowerCase()
                    this.record.billCode =sd
                }

            },
            contractOnChange(e){
                if(e.target.checked){//大写
                    let sd = this.record.contract.toUpperCase()
                    this.record.contract =sd

                }else{//小写
                    let sd = this.record.contract.toLowerCase()
                    this.record.contract =sd
                }

            },
            markNumberButton(){
                this.markNumberStr = this.record.markNumber
                this.markNumberTest=!this.markNumberTest
            },
            markNumberTestOk(){
                this.record.markNumber = this.markNumberStr
                this.markNumberTest=!this.markNumberTest
            },
            markNoButton(){
                console.log(this.record.markNo)
                this.mmarkNoStr = this.record.markNo
                this.markNoTest=!this.markNoTest
            },
            markNoTestOk(){
                this.record.markNo = this.mmarkNoStr
                this.markNoTest=!this.markNoTest
            },
            /**
             * 清空统一编号
             */
            cleanSeqNo(){
                let that = this;
                if(this.record.decStatus !='' && this.record.decStatus != null && this.record.decStatus !='1'){
                    this.$message.warning("申报以后不允许再清空");
                    return;
                }
                this.$confirm({
                    title: "清空统一编号",
                    content: "是否将统一编号清空？",
                    onOk: function (){
                        postAction("/dcl/dec/cleanSeqNoById",{id:that.record.id}).then(res=>{
                            if (res.success){
                                that.record.seqNo = "",
                                that.$message.success("清除成功")
                            }else{
                                that.$message.warning(res.message)
                            }
                        })
                    }
                })
            }
        },
        model: {
            prop: 'value',
            event: 'change'
        },

        watch: {
            record: {
                handler(val) {
                    if (this.record.ieFlag == 'I') {
                        this.imSignShow = true
                        this.imSignShowList = true
                    } else if (this.record.ieFlag == 'E') {
                        this.imSignShow = false
                        this.imSignShowList = false
                    }
                    //拼接最后第六位
                    // if(){
                    //
                    // }
                    this.$emit('change', val)
                },
                deep: true
            },
            //运费
            'record.shipFeeCode'(newValue, oldValue) {
                if (newValue == 1) {
                    this.record.shipCurrencyCode = ''
                    this.shipCurrencyType = true
                } else {
                    this.shipCurrencyType = false
                }

            },
            //保险费
            'record.insuranceCode'(newValue, oldValue) {
                if (newValue == 1) {
                    this.record.insuranceCurr = ''
                    this.insuranceType = true
                } else {
                    this.insuranceType = false
                }

            },

            //杂费
            'record.extrasCode'(newValue, oldValue) {
                if (newValue == 1) {
                    this.record.otherCurr = ''
                    this.extrasType = true
                } else {
                    this.extrasType = false
                }

            },

            'record.termsTypeCode'(newValue, oldValue) {
                this.termsTypeCodeChange()
            },
            value: {
                handler(val) {

                    this.record = val
                    //所需单证获取海关合规库数据
                    /*if (this.decCertTypeList.length === 0) {
                        getAction('/sys/dict/getDictItems', {
                            dictCode: 'CUSTOMS_DICT,ITEM_NAME,ITEM_CODE,JYQZYS,P_DICT_CODE',
                            splicing: 'ITEM_CODE,ITEM_NAME'
                        }).then((res) => {
                            if (res.success) {
                                for (let i = 0; i < res.list.length; i++) {
                                    let decCertType = {
                                        AppCertCode: '',
                                        AppCertName: '',
                                        ApplOri: '',
                                        ApplCopyQuan: ''
                                    }
                                    decCertType.AppCertCode = res.list[i].value
                                    if (!!res.list[i].text) {
                                        decCertType.AppCertName = res.list[i].text.split('|')[1]
                                        decCertType.ApplOri = 1
                                        decCertType.ApplCopyQuan = 2
                                        this.decCertTypeList.push(decCertType)
                                    }
                                }
                            }
                        })
                    }*/

                    //企业资质赋值
                    if (!this.isEmpty(this.record.copLimitType)) {
                        let jsonList1
                        if (typeof (this.record.copLimitType) == 'string') {
                            jsonList1 = JSON.parse(this.record.copLimitType)
                        } else {
                            jsonList1 = this.record.copLimitType
                        }
                        if (jsonList1.length > 0) {
                            //临时字段赋值
                            this.copLimitTypeList = jsonList1
                            this.record.txtEntQualifNo = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeCode
                            this.record.txtEntQualifTypeCode = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeName
                        }
                    }
                },
                deep: true
            },
            focusIndexs: {
                handler(val, oldVal) {
                    this.focusIndex = val
                },
                deep: true
            },
            focusIndex: {
                handler(val) {
                    this.$emit('focusIndexsUpadte', val)
                }
            },
            downDecStatusType: {
                handler(val, oldVal) {
                    this.downDecStatusTypes = val
                },
                deep: true
            },
            downDecStatusTypes: {
                handler(val) {

                    this.$emit('downDecStatusType', val)
                }
            },
            imSignId: {
                handler(val, oldVal) {
                },
                deep: true
            }
        }
    }
</script>

<style scoped>

    @import '~@assets/less/common.less';

    table.my-table {
        width: 100%;
        table-layout: fixed;
    }


    >>> .ant-form-item-control {
        line-height: 0;
    }

    .buttonStyle {
        height: 19px
    }

    .inlineTable {
        width: 100%;
        table-layout: fixed;
        border-bottom: 0px;
    }


    >>> .inlineTable > tr > td {
        border-left: 1px solid rgba(38, 38, 38, 0.3);
        border-top: 1px solid rgba(38, 38, 38, 0.3);

        box-sizing: border-box;
    }
</style>
