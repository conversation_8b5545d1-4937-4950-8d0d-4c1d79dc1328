package org.jeecg.modules.business.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.RecContainerCargoNote;
import org.jeecg.modules.business.entity.RecShipPlanI;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 进口船舶计划推送 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface IRecShipPlanIService extends IService<RecShipPlanI> {

    /**
     * 进口船舶计划列表查询
     *
     * @param recShipPlanI
     * @param pageNo
     * @param pageSize
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/2 16:32
     */
    Result<?> queryPageList(Integer pageNo, Integer pageSize, RecShip<PERSON>lan<PERSON> recShipPlanI, HttpServletRequest req);

    /**
     * 运抵回执列表查询
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/4 17:08
     */
    Result<?> listContainerCargoNodeByDecId(String decId, HttpServletRequest req);

    /**
     * 获取船信息数据
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/7/24 17:08
     */
    Result<?> getShipInfoByDecId(String decId, HttpServletRequest req);
}
