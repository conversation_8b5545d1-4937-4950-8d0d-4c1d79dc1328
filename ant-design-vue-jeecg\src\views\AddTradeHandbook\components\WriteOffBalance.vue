<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件序号" v-model="queryParam.gNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件料号" v-model="queryParam.copGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入商品名称" v-model="queryParam.gName"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div style="margin-top: -15px;">
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="gNo"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				class="j-table-force-nowrap"
				@change="handleTableChange"
			>
				<span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
          {{subStrForColumns(record.gModel, 25)}}
        </span>
			</a-table>
		</div>
	</a-card>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import {subStrForColumns} from "@/utils/util";
import { getAction } from '@/api/manage'
const DICT_erp_units = 'erp_units,name,code'
export default {
	name: "WriteOffBalance",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			units: [],
			disableMixinCreated: true,
			emsHead: {},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '料件序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '料件料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '商品编码',
					align: 'center',
					dataIndex: 'codet'
				},
				{
					title: '规格型号',
					align: 'center',
					dataIndex: 'gModel',
					scopedSlots: { customRender: 'gModelSlots' }
				},
				{
					title: '计量单位',
					align: 'center',
					dataIndex: 'unit'
				},
				{
					title: '直接进口数量(A)',
					align: 'center',
					dataIndex: 'zjjksl'
				},
				{
					title: '深加工结转进口数量(B)',
					align: 'center',
					dataIndex: 'sjgjzjksl'
				},
				{
					title: '余料结转进口数量(C)',
					align: 'center',
					dataIndex: 'yljzjksl'
				},
				{
					title: '料件退换进口数量(D)',
					align: 'center',
					dataIndex: 'ljthjksl'
				},
				{
					title: '料件退换出口数量(E)',
					align: 'center',
					dataIndex: 'ljthcksl'
				},
				{
					title: '内销征税数量(F)',
					align: 'center',
					dataIndex: 'nxzssl'
				},
				{
					title: '转复出数量(G)',
					align: 'center',
					dataIndex: 'zfcsl'
				},
				{
					title: '余料结转出口数量(H)',
					align: 'center',
					dataIndex: 'ylckjzsl'
				},
				{
					title: '销毁数量(I)',
					align: 'center',
					dataIndex: 'xhsl'
				},
				{
					title: '实际进口数量(J=A+B+C+D-E-F-G-H-I)',
					align: 'center',
					dataIndex: 'sjjksl'
				},
				{
					title: '出口成品耗料合计',
					align: 'center',
					dataIndex: 'ckcphlhj'
				},
				{
					title: '保税料件耗用合计(K)',
					align: 'center',
					dataIndex: 'bsljhyhj'
				},
				{
					title: '非保税料件耗用合计',
					align: 'center',
					dataIndex: 'fbsljhyhj'
				},
				{
					title: '工艺耗损合计',
					align: 'center',
					dataIndex: 'gyhshj'
				},
				{
					title: '理论剩余料件(L=J-K)',
					align: 'center',
					dataIndex: 'llsylj'
				},
				{
					title: '保税边角料应补数量',
					align: 'center',
					dataIndex: 'bsbjlybsl'
				},
				{
					title: '边角料已补数量',
					align: 'center',
					dataIndex: 'bjlybsl'
				},
				{
					title: '边角料复出数量',
					align: 'center',
					dataIndex: 'bjlfcsl'
				},
			],
			url: {
							list: '/business/ems/listEmsDetailWriteOffBalance',
						}
		}
	},
	created() {
		this.initDictData(DICT_erp_units)
	},
	methods: {
		subStrForColumns,
				// 加载字典值
				initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == DICT_erp_units) {
					this.units = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						this.initDictData(dictCode)
					}
				})
			}
		},
		showQunitText(text, record, index) {
			return this.getText(text, this.units)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text
		},
		init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						if (this.dataSource.length > 0) {
							this.dataSource.forEach(item => {
								item.dataSourceDec = []
								item.dataSourceInvt = []
							})
						}
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				emsId: this.emsHead.id ? this.emsHead.id : '999999999',
				emsNo : this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			}
			this.loadData(1)
			this.onClearSelected()
		}
	},
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.table-page-search-wrapper {
	margin-top: -16px;
	margin-bottom: 16px;
}
.textGreen {
	color: darkgreen;
	font-weight: bold;
}
/deep/  .ant-table-expand-icon-th {
	background-color: #eaebed !important;
	border: 1px solid #d7dbe4 !important;
}
</style>