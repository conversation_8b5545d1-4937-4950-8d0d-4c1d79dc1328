<template>
	<img :class="iconClass" :src="iconSrc" :style="iconStyle" />
</template>

<script>
export default {
	name: 'JGptIcon',
	props: {
		size: {
			type: [Number, String],
			default: 16
		},
		color: {
			type: String,
			default: null
		},
		style: {
			type: Object,
			default: () => { }
		},
		className: {
			type: String,
			default: ''
		},
		type: {
			type: String,
			default: 'default' // 'default'或'popup'
		}
	},
	computed: {
		iconSrc() {
			return require('@/assets/icons/ai-robot.svg')
		},
		iconStyle() {
			const size = typeof this.size === 'number' ? `${this.size}px` : this.size
			return {
				width: size,
				height: size,
				...this.style
			}
		},
		iconClass() {
			return `j-gpt-icon ${this.className}`
		}
	}
}
</script>

<style scoped>
.j-gpt-icon {
	display: inline-block;
	vertical-align: middle;
}
</style>