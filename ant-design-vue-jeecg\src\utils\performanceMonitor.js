// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadStart: 0,
      domContentLoaded: 0,
      loadComplete: 0,
      firstPaint: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0
    }
    
    this.resourceMetrics = []
    this.userTimings = new Map()
    this.isMonitoring = false
    
    this.init()
  }

  init() {
    if (typeof window === 'undefined') return
    
    this.isMonitoring = true
    this.startBasicMetrics()
    this.startWebVitals()
    this.startResourceMonitoring()
    
    console.log('🚀 性能监控已启动')
  }

  // 基础性能指标
  startBasicMetrics() {
    if (!window.performance) return

    // 页面加载时间
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0]
      if (navigation) {
        this.metrics.loadStart = navigation.loadEventStart
        this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
        this.metrics.loadComplete = navigation.loadEventEnd - navigation.loadEventStart
        
        this.logMetric('页面加载完成', {
          'DOM解析时间': `${this.metrics.domContentLoaded.toFixed(2)}ms`,
          '完整加载时间': `${this.metrics.loadComplete.toFixed(2)}ms`
        })
      }
    })

    // DOMContentLoaded
    document.addEventListener('DOMContentLoaded', () => {
      this.mark('dom-content-loaded')
    })
  }

  // Web Vitals 监控
  startWebVitals() {
    // First Paint & First Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-paint') {
              this.metrics.firstPaint = entry.startTime
            } else if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime
              this.logMetric('首次内容绘制', `${entry.startTime.toFixed(2)}ms`)
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.metrics.largestContentfulPaint = lastEntry.startTime
          this.logMetric('最大内容绘制', `${lastEntry.startTime.toFixed(2)}ms`)
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              this.metrics.cumulativeLayoutShift += entry.value
            }
          }
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })

      } catch (error) {
        console.warn('Web Vitals监控初始化失败:', error)
      }
    }
  }

  // 资源加载监控
  startResourceMonitoring() {
    if (!window.performance) return

    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.initiatorType) {
          const resource = {
            name: entry.name,
            type: entry.initiatorType,
            size: entry.transferSize || 0,
            duration: entry.duration,
            startTime: entry.startTime
          }
          
          this.resourceMetrics.push(resource)
          
          // 记录大文件加载
          if (resource.size > 500000) { // 500KB以上
            this.logMetric('大文件加载', {
              '文件': resource.name.split('/').pop(),
              '大小': `${(resource.size / 1024).toFixed(2)}KB`,
              '耗时': `${resource.duration.toFixed(2)}ms`
            })
          }
        }
      }
    })

    resourceObserver.observe({ entryTypes: ['resource'] })
  }

  // 自定义计时
  mark(name) {
    if (!window.performance) return
    
    const timestamp = performance.now()
    performance.mark(name)
    this.userTimings.set(name, timestamp)
    
    console.log(`⏱️ 标记: ${name} - ${timestamp.toFixed(2)}ms`)
  }

  // 测量两个标记之间的时间
  measure(name, startMark, endMark) {
    if (!window.performance) return
    
    try {
      performance.measure(name, startMark, endMark)
      const measure = performance.getEntriesByName(name, 'measure')[0]
      
      this.logMetric(`测量: ${name}`, `${measure.duration.toFixed(2)}ms`)
      return measure.duration
    } catch (error) {
      console.warn(`测量失败: ${name}`, error)
      return 0
    }
  }

  // 获取性能报告
  getPerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: { ...this.metrics },
      resources: {
        total: this.resourceMetrics.length,
        totalSize: this.resourceMetrics.reduce((sum, r) => sum + r.size, 0),
        byType: this.groupResourcesByType(),
        slowest: this.getSlowestResources(5)
      },
      userTimings: Object.fromEntries(this.userTimings),
      recommendations: this.getRecommendations()
    }

    return report
  }

  // 按类型分组资源
  groupResourcesByType() {
    const groups = {}
    this.resourceMetrics.forEach(resource => {
      if (!groups[resource.type]) {
        groups[resource.type] = { count: 0, totalSize: 0, totalDuration: 0 }
      }
      groups[resource.type].count++
      groups[resource.type].totalSize += resource.size
      groups[resource.type].totalDuration += resource.duration
    })
    return groups
  }

  // 获取最慢的资源
  getSlowestResources(count = 5) {
    return this.resourceMetrics
      .sort((a, b) => b.duration - a.duration)
      .slice(0, count)
      .map(r => ({
        name: r.name.split('/').pop(),
        type: r.type,
        duration: r.duration.toFixed(2),
        size: (r.size / 1024).toFixed(2)
      }))
  }

  // 获取优化建议
  getRecommendations() {
    const recommendations = []
    
    // 检查FCP
    if (this.metrics.firstContentfulPaint > 2500) {
      recommendations.push('首次内容绘制时间过长，建议优化关键资源加载')
    }
    
    // 检查LCP
    if (this.metrics.largestContentfulPaint > 4000) {
      recommendations.push('最大内容绘制时间过长，建议优化图片和字体加载')
    }
    
    // 检查CLS
    if (this.metrics.cumulativeLayoutShift > 0.25) {
      recommendations.push('累积布局偏移过大，建议为图片和广告预留空间')
    }
    
    // 检查资源数量
    if (this.resourceMetrics.length > 100) {
      recommendations.push('资源请求数量过多，建议合并小文件或使用HTTP/2')
    }
    
    // 检查大文件
    const largeFiles = this.resourceMetrics.filter(r => r.size > 1000000) // 1MB
    if (largeFiles.length > 0) {
      recommendations.push(`发现${largeFiles.length}个大文件，建议压缩或分割`)
    }
    
    return recommendations
  }

  // 日志输出
  logMetric(name, value) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${name}:`, value)
    }
  }

  // 输出性能报告到控制台
  logReport() {
    const report = this.getPerformanceReport()
    console.group('🎯 性能监控报告')
    console.log('核心指标:', report.metrics)
    console.log('资源统计:', report.resources)
    console.log('优化建议:', report.recommendations)
    console.groupEnd()
    
    return report
  }

  // 停止监控
  stop() {
    this.isMonitoring = false
    console.log('⏹️ 性能监控已停止')
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 导出
export default performanceMonitor
export { PerformanceMonitor }
