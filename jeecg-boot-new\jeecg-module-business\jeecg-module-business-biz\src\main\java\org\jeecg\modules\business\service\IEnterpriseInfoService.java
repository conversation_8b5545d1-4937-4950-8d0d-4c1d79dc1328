package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.CustomerEnterprise;
import org.jeecg.modules.business.entity.EnterpriseInfo;

import java.util.List;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-18
 * @Version: V1.0
 */
public interface IEnterpriseInfoService extends IService<EnterpriseInfo> {
    List<EnterpriseInfo> getCollectionEnterpriseList(EnterpriseInfo enterpriseInfo) ;

    /**
     * 将公司全称同步到系统企业管理表
     * @param id
     * @param name
     */
    void updateTenantName(Long id, String name);

    /**
     * 海关备案企业信息查询
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/19 下午1:15
     */
    Result<?> customsRecordEnterpriseInformationInquiry();

    Result<?> customsRecordEnterpriseInformationInquiry_();

    Result<?> customsRecordEnterpriseInformationInquiry__();

    Result<?> updateFileTempl(String templ, String type);

    Result<?> removeFileTempl(String templ, String type);

    /**
     * 获取树毛的企业备案信息
     *
     * @param socialCode
     * @param customerName
     * @return org.jeecg.modules.business.entity.CustomerEnterprise
     * <AUTHOR>
     * @date 2024/11/25 11:23
     */
    Result<?> getCustomerEnterpriseBySm(String socialCode, String departcd, String tradeCiqCode, String customerName);
    CustomerEnterprise getCustomerEnterpriseByCond(String socialCode, String departcd, String tradeCiqCode, String customerName);

    /**
     * 查询企业余额
     *
     * @param tenantId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/5/20 14:09
     */
    Result<?> queryBalanceByBiz(String tenantId);
}
