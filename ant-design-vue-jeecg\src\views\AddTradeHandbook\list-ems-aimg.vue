<template>
	<div>
		<a-spin :spinning="confirmLoading">
		<!--表单-->
		<a-form-model :model="record" ref="emsAimgForm" :rules="rules" v-enterToNext>
			<table class="my-table">
				<tr>
					<input-item :readonly="true" label="序号" iprop="gNo" v-model="record.gNo" />
					<input-item :readonly="addType" required label="料号" iprop="copGno" v-model="record.copGno" />
					<input-item :readonly="addType" required label="商品编码" iprop="codet" v-model="record.codet" 
					@pressEnter="handleCodetEnter()"/>
				</tr>
				<tr>
					<input-item :readonly="addType" required label="商品名称" iprop="gName" v-model="record.gName" />
					<input-item :readonly="addType" required label="规格型号" iprop="gModel" v-model="record.gModel" />
					<select-item  ref="selectItem"
												dict-key="erp_units,name,code,1=1 order by code asc"
												label="申报计量单位"
												required
												iprop="unit"
												v-model="record.unit"
					/>
				</tr>
				<tr>
					<select-item  ref="selectItem" :readonly="true"
												dict-key="erp_units,name,code,1=1 order by code asc"
												label="法定计量单位"
												required
												iprop="unit1"
												v-model="record.unit1"
					/>
					<select-item  ref="selectItem" :readonly="true"
												dict-key="erp_units,name,code,1=1 order by code asc"
												label="法定第二计量单位"
												iprop="unit2"
												v-model="record.unit2"
					/>
					<input-num-item
						:readonly="addType"
						stype="number"
						label="申报单价"
						iprop="decPrice"
						required
						v-model="record.decPrice"
					/>
				</tr>
				<tr>
					<select-item  ref="selectItem"
												dict-key="erp_currencies,name,code,currency,1=1"
												label="币制"
												required
												iprop="curr"
												v-model="record.curr"
					/>
					<input-num-item
						:readonly="addType"
						stype="number"
						label="申报数量"
						iprop="qty"
						required
						v-model="record.qty"
					/>
					<input-num-item
						:readonly="addType"
						stype="number"
						label="申报总价"
						iprop="apprAmt"
						required
						v-model="record.apprAmt"
					/>
				</tr>
				<tr>
					<select-item  ref="selectItem" :readonly="emsHead.col1 != '1'"
												dict-key="LYBS"
												label="来源标识"
												iprop="col1"
												v-model="record.col1"
					/>
					<select-item  ref="selectItem"
												dict-key="erp_countries,name,code,isenabled=0"
												label="原产国(地区)"
												required
												iprop="countryCode"
												v-model="record.countryCode"
					/>
					<select-item  ref="selectItem"
												dict-key="ZJMSFS"
												label="征免方式"
												required
												iprop="dutyMode"
												v-model="record.dutyMode"
					/>
				</tr>
				<tr>
					<select-item  ref="selectItem"
												dict-key="EMS_SPSX"
												label="商品属性"
												iprop="attributes"
												v-model="record.attributes"
					/>
					<select-item  ref="selectItem"
												:options="[{value: '1',text: '修改'},{value: '3',text: '增加'}]"
												:value-width="1"
												label="修改标志"
												iprop="modifyFlag"
												v-model="record.modifyFlag"
					/>
					<select-item  ref="selectItem" :readonly="true"
												:options="[{value: '1',text: '正常执行'}]"
												:value-width="1"
												label="海关执行标志"
												iprop="customsEnforcementMark"
												v-model="record.customsEnforcementMark"
					/>
				</tr>
				<tr>
					<input-item :readonly="addType" label="备注" @pressEnter="handleNoteEnter()"
					iprop="note" v-model="record.note" placeholder="按回车键保存"/>

				</tr>

			</table>
		</a-form-model>
			<!--列表-->
			<div style="font-size: 14px;color: #64A5EB;font-weight: bold">
				<a-icon type="appstore" theme="twoTone" /><span style="margin-left: 4px">料件列表</span>
			</div>
			<!-- 操作按钮区域 -->
			<div class="table-operator">
				<a-row>
					<a-col :span="9">
						<a-button size="small" @click="handleAdd" type="primary" icon="plus">新增</a-button>
						<a-button
							size="small"
							@click="batchDel"
							v-if="selectedRowKeys.length > 0"
							ghost
							type="primary"
							icon="delete">批量删除
						</a-button>
					</a-col>
					<a-col :span="15">
						<!-- 查询区域 -->
						<div class="table-page-search-wrapper">
							<a-form layout="inline" @keyup.enter.native="searchQuery">
								<a-row :gutter="24">
									<a-col :xl="4" :sm="24" :xxl="4" :md="12" style="height: 34px">
										<a-form-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
											<a-input placeholder="序号" v-model="queryParam.gNo"></a-input>
										</a-form-item>
									</a-col>
									<a-col :xl="4" :sm="24" :xxl="6" :md="12" style="height: 34px">
										<a-form-item label="料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
											<a-input placeholder="料号" v-model="queryParam.copGno"></a-input>
										</a-form-item>
									</a-col>
									<a-col :xl="5" :sm="24" :xxl="6" :md="12" style="height: 34px">
										<a-form-item label="商品编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
											<a-input placeholder="商品编码" v-model="queryParam.codet"></a-input>
										</a-form-item>
									</a-col>
									<a-col :xl="6" :sm="24" :xxl="6" :md="12" style="height: 34px">
										<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
											<a-input placeholder="商品名称" v-model="queryParam.gName"></a-input>
										</a-form-item>
									</a-col>
									<a-col :xl="5" :sm="24" :xxl="6" :md="12" style="height: 34px;margin-top: -2px">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
									</a-col>
								</a-row>
							</a-form>
						</div>

					</a-col>

				</a-row>

			</div>

			<!-- table区域-begin -->
			<div>
				<a-table
					ref="table"
					size="small"
					:scroll="{ x: true }"
					bordered
					rowKey="id"
					:columns="columns"
					:dataSource="dataSource"
					:pagination="ipagination"
					:loading="loading"
					class="j-table-force-nowrap"
					:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, fixed: true}"
					:rowClassName="getRowClassname"
					@change="handleTableChange"
					:customRow="rowEvent"
				>
					<!-- 备案数量START -->
					<template slot="qty" slot-scope="text, record">
        <span :class="{ textGreen: record.qty > 0 }">
          {{ record.qty }}
        </span>
						<!--					<a-icon  v-if="$store.getters.tenantType == 1" type="edit" theme='twoTone' @click="showBeianModal({ row })"/>-->
					</template>
					<!-- 备案数量END -->
					<!-- 理论库存START -->
					<template slot="stockQty" slot-scope="text, record">
					<span :class="{ textGreen: record.stockQty > 0 }">
          {{ record.stockQty }}
        </span>
					</template>
					<!-- 理论库存END -->
					<!-- 已进口数量START -->
					<template slot="importedQty" slot-scope="text, record">
					<span :class="{ textGreen: record.importedQty > 0 }">
						{{ record.importedQty }}
					</span>
					</template>
					<!-- 已进口数量END -->
					<!-- 可进口数量START -->
					<template slot="importedQy" slot-scope="text, record">
					<span :class="{ textGreen: computedImportedQy(record) > 0 }">
						{{ computedImportedQy(record) }}
					</span>
					</template>
					<!-- 可进口数量END -->
					<span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
          {{subStrForColumns(record.gModel, 15)}}
        </span>
					<!-- 申报记录START -->
					<template slot="applyHistory" slot-scope="text, record">
						<a @click="getApplyHistory(record)">申报记录</a>
					</template>
				</a-table>
			</div>
			          <!--税则信息-->
			 <Dec-Teriff ref='decTeriff' :show='decTeriffShow' :txtrequestCertType.sync='record'
                        v-model='record.codet' @keyFromPromise="keyFromPromise" ></Dec-Teriff>

		</a-spin>
	</div>


</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InputNumItem from '@views/declaration/component/m-table-input-num-item'
import InputItem from '@views/declaration/component/m-table-input-item'
import SelectItem from '@views/declaration/component/m-table-select-item'
import DateItem from '@views/declaration/component/m-table-date-item'
import { getAction, postAction, deleteAction, _postAction, httpAction } from '@/api/manage'
import DecTeriff from '@views/Business/component/modal/dec-teriff'
import { ajaxGetDictItems, duplicateCheckByGrouping } from '@/api/api'
import { nextTick } from 'vuedraggable'
import lodash from 'lodash'
import { Mul } from '@/utils/util'
import { subStrForColumns } from '@/utils/util'
export default {
	mixins: [JeecgListMixin],
	name: 'list-ems-aimg',
	components: {
		SelectItem,
		InputItem,
		DateItem,
		InputNumItem,
		DecTeriff
	},
	props: {
		emsHead: {
			type: Object,
		}
	},
	data() {
		return {
			decTeriffShow: false, //税则信息
			queryParam:{
				type: '1',
			},
			disableMixinCreated:true,
			confirmLoading:false,
			record:{},
			rules:{
				copGno: [{ checkName: '料号', required: true, max: 50, validator: this.checkNo }],
				codet: [{ required: true, message: '请填写商品编码!'}],
				gName: [{ required: true, message: '请填写商品名称!'}],
				gModel: [{ required: true, message: '请填写规格型号!'}],
				qty: [{ required: true, message: '请填写申报数量!'}],
				unit: [{ required: true, message: '请选择申报计量单位!' }],
				unit1: [{ required: true, message: '请选择法定计量单位!' }],
				decPrice : [{ required: true, message: '请填写申报单价!'}],
				curr : [{ required: true, message: '请选择币制!' }],
				apprAmt : [{ required: true, message: '请填写申报总价!'}],
				countryCode : [{ required: true, message: '请选择原产国(地区)!'}],
				dutyMode : [{ required: true, message: '请选择征免方式!' }]
			},
			columns: [
				{
					title: '序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '商品编码',
					align: 'center',
					dataIndex: 'codet'
				},
				{
					title: '规格型号',
					align: 'center',
					dataIndex: 'gModel',
					scopedSlots: { customRender: 'gModelSlots' }
				},
				{
					title: '申报计量单位',
					align: 'center',
					dataIndex: 'unit_dictText'
				},
				{
					title: '法定计量单位',
					align: 'center',
					dataIndex: 'unit1_dictText'
				},
				{
					title: '申报单价',
					align: 'center',
					dataIndex: 'decPrice'
				},
				{
					title: '币制',
					align: 'center',
					dataIndex: 'curr_dictText'
				},
				{
					title: '理论库存',
					align: 'center',
					dataIndex: 'stockQty',
					scopedSlots: { customRender: 'stockQty' }
				},
				{
					title: '申报数量',
					align: 'center',
					dataIndex: 'qty',
					scopedSlots: { customRender: 'qty' }
				},
				{
					title: '申报总价',
					align: 'center',
					dataIndex: 'apprAmt',
				},
				{
					title: '已进口数量',
					align: 'center',
					dataIndex: 'importedQty',
					scopedSlots: { customRender: 'importedQty' }
				},
				{
					title: '可进口数量',
					align: 'center',
					dataIndex: 'importedQy',
					scopedSlots: { customRender: 'importedQy' }
				},
				{
					title: '原产国(地区)',
					align: 'center',
					dataIndex: 'countryCode_dictText'
				},
				{
					title: '征免方式',
					align: 'center',
					dataIndex: 'dutyMode_dictText'
				},
				{
					title: '修改标志',
					align: 'center',
					dataIndex: 'modifyFlag',
					customRender: function (text) {
						if (text == '1') {
							return "修改";
						} else if(text == '3'){
							return "增加";
						} else {
							return '';
						}
					}
				},
				{
					title: '海关执行标志',
					align: 'center',
					dataIndex: 'customsEnforcementMark',
					customRender: function (text) {
						if (text == '1') {
							return "正常执行";
						} else {
							return '';
						}
					}
				},
				{
					title: '来源标识',
					align: 'center',
					dataIndex: 'col1_text'
				},
				{
					title: '申报记录',
					align: 'center',
					dataIndex: 'applyHistory',
					fixed: 'right',
					width: 80,
					scopedSlots: { customRender: 'applyHistory' }
				},
			],
			addType: false,
			url: {
				list: '/business/ems/listEmsDetail',
				save: '/business/ems/saveEmsAimg',
				getById: '/business/ems/getEmsAimgById',
				deleteBatch: '/business/ems/deleteDetailBatch',
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},

		}


		},
	created() {



	},
	mounted() {

	},
	methods: {
		keyFromPromise(){
			this.$forceUpdate()
		},
		handleCodetEnter(){
			if (this.record.codet.length <= 3) {
                    this.$message.error("商品编号不可小于4位")
                }else{
					this.decTeriffShow = ! this.decTeriffShow
				}
		},
		subStrForColumns,
		computedImportedQy(records) {
			return this.floatSub(records.qty, records.importedQty)
		},
		handleAdd(){
			this.queryParam.emsId = this.emsHead.id
			this.initData()
		},
		//初始化
		async initData() {
			this.record = {}
			this.record.emsNo = this.emsHead.emsNo
			this.record.emsId = this.emsHead.id
			this.record.gNo = this.ipagination.total + 1
			//修改标志为 增加
			this.record.modifyFlag = '3'
			//海关执行标志为 正常执行
			this.record.customsEnforcementMark = '1'
			this.$forceUpdate()
		},
		init(){
			this.queryParam.emsId = this.emsHead.id
			if (this.queryParam.emsId) {
				this.loadData(1)
			}
			this.initData()
		},
		handleNoteEnter(){
			this.saveForm()
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			if(!this.record.emsId){
				this.$message.warning('请先保存表头信息。')
				return
			}
			const that = this
			// 触发表单验证
			this.$refs.emsAimgForm.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的加贸手册料件数据：', this.record)
					httpAction(this.url.save, this.record, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								that.init()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		checkNo (rule, value, callback) {
			if (rule.required) {
				if (this.isEmpty(value)) {
					callback(new Error(`请输入${rule.checkName}!`))
				}
			}
			if (!this.isEmpty(value)) {
				let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
				if (rule.checkNum) {
					if (!reg.test(value)) {
						callback(new Error('请输入数字!'))
					}
				}
				if (value < 0) {
					callback(new Error('不能输入负数!'))
				}
				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(new Error(`长度不能大于${rule.max}位!`))
				}
				if ((value.toString()).indexOf('.') != -1) {
					callback(new Error('不能含有小数点!'))
				}
			}
			callback()
		},
		// 增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '2') {
				return 'data-rule-invalid'
			}
		},
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
						this.record = record
						this.$forceUpdate()
					},
				}
			}
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			if (!this.queryParam.emsId) {
				this.searchReset()
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						this.$forceUpdate()
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						this.record.gNo = this.ipagination.total + 1
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				type: '1', // 1料件 2成品 3损耗
				emsId: this.emsHead.id
			}
			this.loadData(1)
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids, type: '1' })
							.then(res => {
								if (res.success) {
									that.$message.success(res.message)
									that.loadData(1)
									that.record = {}
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		/**
		 ** 减法函数，用来得到精确的减法结果
		 ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
		 ** 调用：accSub(arg1,arg2)
		 ** 返回值：arg1加上arg2的精确结果
		 **/
		floatSub (arg1, arg2, n) {
			arg1 = !arg1 ? 0 : arg1
			arg2 = !arg2 ? 0 : arg2
			var r1, r2, m, n
			try { r1 = arg1.toString().split('.')[1].length } catch (e) { r1 = 0 }
			try { r2 = arg2.toString().split('.')[1].length } catch (e) { r2 = 0 }
			m = Math.pow(10, Math.max(r1, r2))
			// 动态控制精度长度
			if (!n) {
				n = (r1 >= r2) ? r1 : r2
			}
			return ((arg1 * m - arg2 * m) / m).toFixed(n)
		}
	}





}


</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';

</style>