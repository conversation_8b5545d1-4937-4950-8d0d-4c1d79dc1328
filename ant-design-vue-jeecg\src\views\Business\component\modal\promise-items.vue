<template>
    <!--其它事项确认-->
    <a-modal :centered='true'
             :visible='showModal'
             :maskClosable= "maskClosable"
             @cancel='showModal = !showModal'
             @ok='handleOk'
             cancel-text=''
             title='价格说明'
             width='520px'
             v-enterToNext
    >

        <a-card :bordered="false" :bodyStyle="{padding:'0px'}">
            <!-- 查询区域 -->
            <div class="table-page-search-wrapper">
                <a-form :form='record' layout="inline" >
                    <a-row :gutter="24">
                        <a-col :xl="24" :lg="24" :md="24" :sm="24">
                            <a-form-item label="特殊关系确认" style="margin-left: 30%">
                                <decSelect-Item
                                    ref = "refTmp1"
                                    :inputRef="inputTmp1"
                                    :options="options"
                                    :selectWidth="100"
                                    :styleType="3"
                                    v-model="record.tmp1"
                                ></decSelect-Item>
                            </a-form-item>
                        </a-col>
                        <a-col :xl="24" :lg="24" :md="24" :sm="24">
                            <a-form-item label="价格影响确认" style="margin-left: 30%">
                                <decSelect-Item
                                    :options="options"
                                    :selectWidth="100"
                                    :styleType="3"
                                    v-model="record.tmp2"
                                ></decSelect-Item>
                            </a-form-item>
                        </a-col>
                        <a-col :xl="24" :lg="24" :md="24" :sm="24" class="tmp3Class">
                            <a-form-item label="与货物有关的特许权使用费支付确认">
                                <decSelect-Item
                                    :options="options"
                                    :selectWidth="100"
                                    :styleType="3"
                                    v-model="record.tmp3"
                                ></decSelect-Item>
                            </a-form-item>
                        </a-col>
                        <a-col :xl="24" :lg="24" :md="24" :sm="24">
                            <a-form-item label="公式定价确认" style="margin-left: 30%">
                                <decSelect-Item
                                    :options="options"
                                    :selectWidth="100"
                                    :styleType="3"
                                    v-model="record.tmp4"
                                ></decSelect-Item>
                            </a-form-item>
                        </a-col>
                        <a-col :xl="24" :lg="24" :md="24" :sm="24" >
                            <a-form-item label="暂定价格确认"style="margin-left: 30%" >
                                <decSelect-Item
                                    :options="options"
                                    :selectWidth="100"
                                    :styleType="3"
                                    v-model="record.tmp5"
                                ></decSelect-Item>
                            </a-form-item>
                        </a-col>

                    </a-row>
                </a-form>
            </div>


        </a-card>
    </a-modal>
</template>

<script>

    import MSelect from "@views/declaration/component/m-dict-select"
    import decSelectItem from "@views/Business/component/m-table-select-item-fuben";

    export default {
        name: "promise-items",
        components: {MSelect, decSelectItem},
        data() {
            return {
                // value副本
                val: '',
                // show副本
                showModal: false,
                inputTmp1:"inputTmp1",
                options: [
                    {title: '否', value: '0'},
                    {title: '是', value: '1'},
                    // {title: '空', value: '9'},
                ],
                record: {
                    // 特殊关系确认
                    tmp1: '',
                    // 价格影响确认
                    tmp2: '',
                    // 与货物有关的特许权使用费支付确认
                    tmp3: '',
                    // 与货物有关的特许权使用费支付确认
                    tmp4: '',
                    // 与货物有关的特许权使用费支付确认
                    tmp5: ''
                },
                maskClosable:false,
            }
        },
        created() {

            if (!!this.value) {
                let split = this.value.split('|')
                if(split.length>3){

                }
                this.record = {tmp1: split[0] || '', tmp2: split[1] || '', tmp3: split[2] || ''}
            }

        },
        props: {
            'value': {
                type: String,
                require: true
            },
            'show': {
                type: Boolean,
            }
        },
        model: {
            prop: 'value',
            event: 'change'
        },
        watch: {
            value: {
                handler(val) {
                    if (!!val) {

                        let split = val.split('|')
                        if(split.length>3){
                            this.record = {tmp1: split[0] || '', tmp2: split[1] || '', tmp3: split[2] || '', tmp4: split[3] || '', tmp5: split[4] || ''}
                        }else{
                            this.record = {tmp1: split[0] || '', tmp2: split[1] || '', tmp3: split[2] || ''}
                        }

                        this.$forceUpdate()
                    }

                }
            },
            val: {
                handler() {
                    this.$emit('change', this.val)
                },
            },
            show: {
                handler(val) {
                    this.showModal = !this.showModal
                    this.$nextTick(()=>{
                        setTimeout(() => {
                            //默认初始化的光标
                            this.$refs.refTmp1.$refs.inputTmp1.focus()
                        }, 200)
                    });
                }
            },
        },
        methods: {
            /**
             * 取消修改
             */
            handleCancel() {
                this.showModal = !this.showModal
                setTimeout(() => {
                    this.$emit('keyFromPromise', "promiseItems")
                }, 20)
            },
            /**
             * 确认修改
             */
            handleOk() {

                if(!!this.record.tmp1||!!this.record.tmp2||!!this.record.tmp3||!!this.record.tmp4||
                    !!this.record.tmp5){
                    this.val = `${this.record.tmp1 || 9}|${this.record.tmp2 || 9}|${this.record.tmp3 || 9}|${this.record.tmp4 || 9}|${this.record.tmp5 || 9}`
                    this.showModal = !this.showModal
                }else{
                    this.val = ''
                    this.showModal = !this.showModal
                }

                //加上延迟是因为回车作用在按钮上事件并没有销毁程序会错乱
                setTimeout(() => {
                    this.$emit('keyFromPromise', "promiseItems")
                }, 200)
            },
        }
    }
</script>
<style scoped>

    @import '~@assets/less/common.less';

    .tmp3Class >>> .ant-input{
        margin-left: 6px;
    }
</style>