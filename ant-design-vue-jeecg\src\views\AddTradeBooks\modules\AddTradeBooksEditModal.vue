<template>
	<j-modal
		:title="'加贸账册 ' + title"
		:width="width"
		:visible="visible"
		@ok="handleSave"
		:okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
		@cancel="handleCancel"
		cancelText="关闭"
		selfCloseAction="closePop"
		:disableSubmit="disableSubmit"
	>
		<template slot="footer">
			<a-button type="default" @click="handleCancel">关闭</a-button>
			<a-button v-show="!disableSubmit" type="primary" @click="handleSave">保存</a-button>
		</template>

		<a-spin :spinning="confirmLoading">
			<a-collapse v-model="activeKeys" :bordered="false" style="margin-top: -10px">
				<!-- 基本信息 -->
				<a-collapse-panel key="1" header="基本信息" >
					<j-form-container :disabled="disableSubmit">
						<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
							<a-row :gutter="24" type="flex" justify="start">
								<a-col :span="8">
									<a-form-model-item label="账册编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="emsNo">
										<a-input placeholder='请输入账册编号' v-model="model.emsNo"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="账册类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="emsType">
										<a-select v-model="model.emsType" :options="options" placeholder="请选择账册类型"/>
									</a-form-model-item>
								</a-col>
<!--								<a-col :span="8">-->
<!--									<a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">-->
<!--										<a-select placeholder="请选择状态" allowClear showSearch v-model="model.status">-->
<!--											<a-select-option value="1">正在使用</a-select-option>-->
<!--											<a-select-option value="2">暂停使用</a-select-option>-->
<!--											<a-select-option value="3">已核销</a-select-option>-->
<!--										</a-select>-->
<!--									</a-form-model-item>-->
<!--								</a-col>-->
								<a-col :span="8">
									<a-form-model-item label="经营单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeCode">
										<a-input placeholder='请输入经营单位' v-model="model.tradeCode" :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="经营单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeName">
										<a-input v-model="model.tradeName" placeholder='请输入经营单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="经营单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeSccd">
										<a-input v-model="model.tradeSccd" placeholder='经营单位社会信用代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="加工单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ownerCode">
										<a-input v-model="model.ownerCode" placeholder='请输入加工单位'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="加工单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ownerName">
										<a-input v-model="model.ownerName" placeholder='请输入加工单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="加工单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ownerSccd">
										<a-tooltip :title='model.ownerSccd' :visible='false'>
											<a-input v-model="model.ownerSccd" placeholder='加工单位社会信用代码'></a-input>
										</a-tooltip>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareCode">
										<a-input v-model="model.declareCode" placeholder='请输入申报单位' :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareName">
										<a-input v-model="model.declareName" placeholder='请输入申报单位名称'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单位社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareSccd">
										<a-input v-model="model.declareSccd" placeholder='申报单位社会信用代码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="主管海关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="masterCustoms">
										<j-dict-select-tag
											v-model="model.masterCustoms"
											type="node-limit"
											dictCode="erp_customs_ports,name,customs_port_code"
											placeholder="请选择主管海关"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="企业内部编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="copEmsNo">
										<a-input placeholder='请输入企业内部编号' v-model="model.copEmsNo"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="展期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="modifyMark">
										<a-select v-model="model.modifyMark" allowClear showSearch placeholder="请选择展期">
											<a-select-option value="0">否</a-select-option>
											<a-select-option value="1">是</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="录入日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inputDate">
										<j-date is-node placeholder="请选择录入日期" v-model="model.inputDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="有效日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endDate">
										<j-date is-node placeholder="请选择有效日期" v-model="model.endDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="declareDate">
										<j-date is-node placeholder="请选择申报日期" v-model="model.declareDate" style="width: 100%" />
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="进口货物项数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="imgItems">
										<a-input-number v-model="model.imgItems" placeholder="请输入进口货物项数" :min='0' :maxLength="32" style='width: 100%;'/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="出口货物项数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="exgItems">
										<a-input-number v-model="model.exgItems" placeholder="请输入出口货物项数" :min='0' :maxLength="32" style='width: 100%;'/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="item">
										<a-input-number v-model="model.item" placeholder="请输入序号" :min='0' :maxLength="32" style='width: 100%;'/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="note">
										<j-remarks-component
											v-model="model.note"
											placeholder="请输入备注"
											:maxLength="500"
											:readOnly="disableSubmit"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="登记日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="byDate">
										{{ model.byDate }}
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="登记人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="byName">
										{{ model.byName }}
									</a-form-model-item>
								</a-col>
							</a-row>
						</a-form-model>
					</j-form-container>
				</a-collapse-panel>
			</a-collapse>
		</a-spin>
	</j-modal>
</template>
<script>
import { getAction, httpAction } from '@/api/manage'
import store from '@/store'

export default {
	name: 'AddTradeBooksEditModal',
	data() {
		return {
			options: [
				{
					value: '1',
					label: 'E账册'
				},
				{
					value: '2',
					label: 'H账册'
				},
				{
					value: '3',
					label: '耗料'
				},
				{
					value: '4',
					label: '工单'
				},
				{
					value: '5',
					label: '企业为单元'
				}
			],
			model: {
				status: '1',
				emsType: '', //账册类型
				byDate: this.formatDate((new Date()).getTime(), 'yyyy-MM-dd hh:mm:ss'),//登记日期
				byName: store.getters.userInfo.username
			},
			activeKeys: ['1', '2'],
			validatorRules: {
				emsNo: [{ required: true, message: '请输入账册编号!' }],
				tradeCode: [{ checkName: '经营单位',required: true,max:10,validator: this.checkNo }],
				tradeName: [{ required: true, message: '请填写经营单位名称!'}],
				tradeSccd: [{ checkName: '经营单位社会信用代码',required: true,max:30,validator: this.checkNo }],
				ownerCode: [{ checkName: '加工单位',required: true, validator: this.checkNo }],
				ownerName: [{ required: true, message: '请填写加工单位名称!' }],
				ownerSccd: [{ checkName: '加工单位社会信用代码',required: true,max:30,validator: this.checkNo }],
				status: [{ required: true, message: '请填写状态!' }],
				emsType: [{ required: true, message: '请填写账册类型!' }],
				endDate: [{ required: true, message: '请填写有效日期!' }],
				inputDate: [{ required: true, message: '请填写录入日期!' }],
				declareDate: [{ required: true, message: '请填写申报日期!' }],
				item: [{ type: 'number', message: '序号只能是数字!' }]
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 10 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 14 },
			},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 3 },
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 20 },
			},
			title: '',
			width: 1150,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			url: {
				save: '/business/ems/saveEmsHead',
				getById: '/business/ems/getEmsHeadById',
			}
		}
	},
	created() {},
	methods: {
		add() {
			this.visible = true
		},
		edit(record) {
			this.initModel(record)
			this.visible = true
		},
		initModel(value) {
			this.confirmLoading = true
			let val = value
			if (val == undefined) {
				val = this.model
			}
			let params = {
				id: val.id,
			}
			if (val.id != null) {
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.model = record
						} else {
							// 失败
							this.$message.warning(res.message || res)
							this.close()
						}
					})
					.finally(() => {
						this.confirmLoading = false
					})
			} else {
				// 新增
				this.confirmLoading = false
			}
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的加贸账册数据：', this.model)
					httpAction(this.url.save, this.model, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								this.close()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		close() {
			this.model = {
				status: '1',
				emsType: '', //账册类型
				byDate: this.formatDate((new Date()).getTime(), 'yyyy-MM-dd hh:mm:ss'),//登记日期
				byName: store.getters.userInfo.username
			}
			this.$emit('close')
			this.visible = false
		},
		handleCancel () {
			this.close()
		},
		checkNo (rule, value, callback) {
			if (rule.required) {
				if (this.isEmpty(value)) {
					callback(new Error(`请输入${rule.checkName}!`))
				}
			}
			if (!this.isEmpty(value)) {
				let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
				if (rule.checkNum) {
					if (!reg.test(value)) {
						callback(new Error('请输入数字!'))
					}
				}
				if (value < 0) {
					callback(new Error('不能输入负数!'))
				}
				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(new Error(`长度不能大于${rule.max}位!`))
				}
				if ((value.toString()).indexOf('.') != -1) {
					callback(new Error('不能含有小数点!'))
				}
			}
			callback()
		},
		/**
		 * 时间格式化
		 * @param value
		 * @param fmt
		 * @returns {*}
		 */
		formatDate (value, fmt) {
			let regPos = /^\d+(\.\d+)?$/
			if (regPos.test(value)) {
				//如果是数字
				let getDate = new Date(value)
				let o = {
					'M+': getDate.getMonth() + 1,
					'd+': getDate.getDate(),
					'h+': getDate.getHours(),
					'm+': getDate.getMinutes(),
					's+': getDate.getSeconds(),
					'q+': Math.floor((getDate.getMonth() + 3) / 3),
					'S': getDate.getMilliseconds()
				}
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
				}
				for (let k in o) {
					if (new RegExp('(' + k + ')').test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
					}
				}
				return fmt
			} else {
				//TODO
				value = value.trim()
				return value.substr(0, fmt.length)
			}
		}
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
</style>