package org.jeecg.modules.business.service.impl.receipt.handle.add.trade;

import com.alibaba.fastjson.JSON;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.EdiReceipt;
import org.jeecg.modules.business.entity.EdiStatusHistory;
import org.jeecg.modules.business.entity.PtsEmsHead;
import org.jeecg.modules.business.entity.receipt.DTO.HandleResultDTO;
import org.jeecg.modules.business.entity.receipt.DTO.Resp;
import org.jeecg.modules.business.entity.receipt.vo.trade.EmlPutrecBsc;
import org.jeecg.modules.business.entity.receipt.vo.trade.PackageReceiptBusiness;
import org.jeecg.modules.business.service.impl.receipt.service.IHandleMessageTypeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.jeecg.modules.business.util.exception.ExceptionUtil.ResExHandle;

/**
 * Eml211HandleMessageTypeServiceImpl
 * <pre>
 *   加工贸易手册EML211的回执处理
 * </pre>
 *
 * <AUTHOR>  2025/6/26 13:49
 * @version 1.0
 */
@Service("eml211HandleMessageTypeServiceImpl")
public class Eml211HandleMessageTypeServiceImpl extends AbstractHandleMessageTypeService
        implements IHandleMessageTypeService {
    @Override
    public HandleResultDTO handleMessageType(PackageReceiptBusiness packageReceipt, String fileName,
                                             Integer sourceType) {
        // 存储异常信息
        List<Resp> respList = new ArrayList<>(16);
        int index = 0;
        List typeList = Arrays.asList(packageReceipt.getManageResult().split(":"));
        String type = (String) typeList.get(typeList.size() - 1);
        StringBuffer result = new StringBuffer();
        Integer mark = 1;
        switch (type) {
            //通过 申报成功
            case "1":
                PtsEmsHead ptsEmsHead = makePtsEmsHead(packageReceipt.getEmlPutrecBsc());
                ptsEmsHead.setStatus(type);
                ptsEmsHead.setCopEmsNo(packageReceipt.getEtpsPreentNo());//企业内编号
//                ptsEmsHead.setEmsNo(packageReceipt.getBusinessId());
                Result<?> resultM = ptsEmsHeadService.updatePtsEmsByReceipt(ptsEmsHead, type);
                Resp resp = new Resp();
                if (resultM.isSuccess()){
                    resp.setMsg(++index + ".手册申报成功：").setStatus("Ok");
                    respList.add(resp);

                } else {
                    mark = 0;
                    resp.setMsg(++index + ".手册申报成功：").setStatus("Failed").setFailureStr(ResExHandle(resultM.getMessage()));
                    respList.add(resp);
                }
                result.append(resultM.getMessage());
                break;
                //2 转人工
            case "2":
                PtsEmsHead ptsEmsHead2 = makePtsEmsHead(packageReceipt.getEmlPutrecBsc());
                ptsEmsHead2.setStatus(type);
                ptsEmsHead2.setCopEmsNo(packageReceipt.getEtpsPreentNo());//企业内编号
//                ptsEmsHead.setEmsNo(packageReceipt.getBusinessId());
                Result<?> resultM2 = ptsEmsHeadService.updatePtsEmsByReceipt(ptsEmsHead2, type);
                Resp resp2 = new Resp();
                if (resultM2.isSuccess()){
                    resp2.setMsg(++index + ".手册转人工：").setStatus("Ok");
                    respList.add(resp2);

                } else {
                    mark = 0;
                    resp2.setMsg(++index + ".手册转人工：").setStatus("Failed").setFailureStr(ResExHandle(resultM2.getMessage()));
                    respList.add(resp2);
                }
                result.append(resultM2.getMessage());
                break;
                //3 退单
            case "3":
                PtsEmsHead ptsEmsHead3 = makePtsEmsHead(packageReceipt.getEmlPutrecBsc());
                ptsEmsHead3.setStatus(type);
                ptsEmsHead3.setCopEmsNo(packageReceipt.getEtpsPreentNo());//企业内编号
//                ptsEmsHead.setEmsNo(packageReceipt.getBusinessId());
                Result<?> resultM3 = ptsEmsHeadService.updatePtsEmsByReceipt(ptsEmsHead3, type);
                Resp resp3 = new Resp();
                if (resultM3.isSuccess()){
                    resp3.setMsg(++index + ".手册退单：").setStatus("Ok");
                    respList.add(resp3);

                } else {
                    mark = 0;
                    resp3.setMsg(++index + ".手册退单：").setStatus("Failed").setFailureStr(ResExHandle(resultM3.getMessage()));
                    respList.add(resp3);
                }
                result.append(resultM3.getMessage());
                break;
                //Y-入库成功
            case "Y":
                PtsEmsHead ptsEmsHeadY = makePtsEmsHead(packageReceipt.getEmlPutrecBsc());
                ptsEmsHeadY.setStatus(type);
                ptsEmsHeadY.setCopEmsNo(packageReceipt.getEtpsPreentNo());//企业内编号
//                ptsEmsHead.setEmsNo(packageReceipt.getBusinessId());
                Result<?> resultMY = ptsEmsHeadService.updatePtsEmsByReceipt(ptsEmsHeadY, type);
                Resp respY = new Resp();
                if (resultMY.isSuccess()){
                    respY.setMsg(++index + ".手册入库成功：").setStatus("Ok");
                    respList.add(respY);
                } else {
                    mark = 0;
                    respY.setMsg(++index + ".手册入库成功：").setStatus("Failed").setFailureStr(ResExHandle(resultMY.getMessage()));
                    respList.add(respY);
                }
                result.append(resultMY.getMessage());
                break;
                //Z-入库失败
            case "Z":
                PtsEmsHead ptsEmsHeadZ = makePtsEmsHead(packageReceipt.getEmlPutrecBsc());
                ptsEmsHeadZ.setStatus(type);
                ptsEmsHeadZ.setCopEmsNo(packageReceipt.getEtpsPreentNo());//企业内编号
//                ptsEmsHead.setEmsNo(packageReceipt.getBusinessId());
                Result<?> resultMZ = ptsEmsHeadService.updatePtsEmsByReceipt(ptsEmsHeadZ, type);
                Resp respZ = new Resp();
                if (resultMZ.isSuccess()){
                    respZ.setMsg(++index + ".手册入库失败：").setStatus("Ok");
                    respList.add(respZ);
                } else {
                    mark = 0;
                    respZ.setMsg(++index + ".手册入库失败：").setStatus("Failed").setFailureStr(ResExHandle(resultMZ.getMessage()));
                    respList.add(respZ);
                }
                result.append(resultMZ.getMessage());
                break;
            default:
                result.append("[未处理的报文标记]: EML211 -> " + type + ", 文件:" + fileName);
        }
        logger.info("[加贸手册回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", fileName, sourceType,
                packageReceipt.getSeqNo(), result.toString());
        //处理回执日志
        saveEdiReceiptAndEdiStatusHistory(packageReceipt, fileName, respList, type);
        return new HandleResultDTO(mark, result.toString(), respList);
    }

    /**
     * 更新手册的部分字段
     * @return
     */
    private PtsEmsHead makePtsEmsHead(EmlPutrecBsc emlPutrecBsc){
        PtsEmsHead ptsEmsHead = new PtsEmsHead();
        ptsEmsHead.setEmsNo(emlPutrecBsc.getEmlNo());//手册号
        return ptsEmsHead;
    }
    /**
     * 保存EdiReceipt和EdiStatusHistory的 通用方法
     */
    private void saveEdiReceiptAndEdiStatusHistory(PackageReceiptBusiness packageReceipt, String fileName,List<Resp> respList,String type){
        EdiReceipt ediReceipt = new EdiReceipt();
        ediReceipt.setSeqNo(packageReceipt.getSeqNo());
        ediReceipt.setFileName(fileName);
        ediReceipt.setReceiverTime(new Date());
        ediReceipt.setReceiptType(packageReceipt.getReceiptType());
        ediReceipt.setSourceType(1);
        ediReceipt.setReceiptJson(JSON.toJSONString(packageReceipt));
        ediReceipt.setDealStatus("1");
        ediReceipt.setDealResult(JSON.toJSONString(respList));
        ediReceipt.setCreateTime(new Date());
//        ediReceipt.setRelatedId("");
        ediReceiptService.save(ediReceipt);
        EdiStatusHistory ediStatusHistory = new EdiStatusHistory();
        ediStatusHistory.setBusinessId(packageReceipt.getBusinessId());
        ediStatusHistory.setCreateTime(new Date());
        ediStatusHistory.setSeqNo(packageReceipt.getSeqNo());
        ediStatusHistory.setType(packageReceipt.getReceiptType());
        ediStatusHistory.setCode(type);
        ediStatusHistory.setNote(packageReceipt.getManageResult());
        ediStatusHistory.setNoticeDate(packageReceipt.getSendTime());
        ediStatusHistory.setReceiverTime(new Date());
//        ediStatusHistory.setRelatedId("");
        ediStatusHistoryService.save(ediStatusHistory);
    }
}
