package org.jeecg.modules.system.util.exception;

/**
 * 自定义全局异常枚举
 *
 * <AUTHOR>
 * @date 2019/10/18 13:15
 * @since 1.0.0
 */
public enum GlobalErrorCodeEnum {

    /** 未知异常 */
    UNKNOWN_EXCEPTION(1001,"未知异常，请联系管理员"),
    /** 请求参数不存在 */
    MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION(1002,"请求参数不存在或拼写错误！"),
    /** 类转换异常 */
    CLASS_CAST_EXCEPTION(1003,"类型强制转换异常"),
    /** 算术条件异常 */
    ARITHMETIC_EXCEPTION(1004,"算术条件异常"),
    /** 空指针异常 */
    NULL_POINTER_EXCEPTION(1005,"空指针异常"),
    /** 字符串转换为数字异常 */
    NUMBER_FORMAT_EXCEPTION(1006,"字符串转换为数字异常"),
    /** 数组下标越界异常 */
    ARRAY_INDEX_OUT_OF_BOUNDS_EXCEPTION(1007,"数组下标越界异常"),
    /** 方法未找到异常 */
    NO_SUCH_METHOD_EXCEPTION(1008,"方法未找到异常"),
    /** 未找到类定义错误 */
    NO_CLASS_DEF_FOUND_ERROR(1009,"未找到类定义错误"),
    /** 未找到类定义错误 */
    CLASS_NOT_FOUND_EXCEPTION(1010,"找不到类异常"),
    /** 索引越界异常 */
    INDEX_OUT_OF_BOUNDS_EXCEPTION(1011,"索引越界异常"),
    /** 数据库异常 */
    DB_ERROR(1012,"数据库异常")
    ;

    private Integer code;
    private String msg;

    GlobalErrorCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
