<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PtsEmsCmMapper">

    <select id="listEmsDetail" resultType="org.jeecg.modules.business.entity.PtsEmsCm">
        SELECT
            *
        FROM
            `pts_ems_cm`
        <where>
            <if test="emsQueryDto.emsId!= null and emsQueryDto.emsId!= ''">
                AND EMS_ID = #{emsQueryDto.emsId}
            </if>
            <if test="emsQueryDto.emsNo!= null and emsQueryDto.emsNo!= ''">
                AND EMS_NO = #{emsQueryDto.emsNo}
            </if>
            <if test="emsQueryDto.exgNo!= null and emsQueryDto.exgNo!= ''">
                AND EXG_NO = #{emsQueryDto.exgNo}
            </if>
            <if test="emsQueryDto.imgNo!= null and emsQueryDto.imgNo!= ''">
                AND IMG_NO = #{emsQueryDto.imgNo}
            </if>
            <if test="emsQueryDto.exgNoList != null and emsQueryDto.exgNoList.size() > 0">
                AND EXG_NO IN
                <foreach collection="emsQueryDto.exgNoList" item="exgNo" open="(" separator="," close=")">
                    #{exgNo}
                </foreach>
            </if>
        </where>
        ORDER BY G_NO DESC
    </select>
    <select id="listEmsCmByReport" resultType="org.jeecg.modules.business.entity.PtsEmsCm">
        SELECT
            aexg.G_NO exgNo,
            aexg.COP_GNO exgCopGno,
            aexg.G_NAME exgGname,
            aexg.UNIT exgUnit,
            aimg.G_NO imgNo,
            aimg.COP_GNO imgCopGno,
            aimg.G_NAME imgGname,
            aimg.UNIT imgUnit,
            pec.*
        FROM
            pts_ems_cm pec
                LEFT JOIN pts_ems_aexg aexg ON aexg.G_NO = pec.EXG_NO
                AND aexg.EMS_NO = #{emsQueryDto.emsNo}
                LEFT JOIN pts_ems_aimg aimg ON aimg.G_NO = pec.IMG_NO
                AND aimg.EMS_NO = #{emsQueryDto.emsNo}
        WHERE
            pec.EMS_NO = #{emsQueryDto.emsNo}
        <if test="emsQueryDto.exgNo != null and emsQueryDto.exgNo != ''">
            AND aexg.G_NO = #{emsQueryDto.exgNo}
        </if>
        <if test="emsQueryDto.exgCopGno!= null and emsQueryDto.exgCopGno!= ''">
            AND aexg.COP_GNO = #{emsQueryDto.exgCopGno}
        </if>
        <if test="emsQueryDto.imgNo != null and emsQueryDto.imgNo != ''">
            AND aimg.G_NO = #{emsQueryDto.imgNo}
        </if>
        <if test="emsQueryDto.imgCopGno != null and emsQueryDto.imgCopGno != ''">
            AND aimg.COP_GNO = #{emsQueryDto.imgCopGno}
        </if>
        <if test="emsQueryDto.exgGname != null and emsQueryDto.exgGname != ''">
            AND aexg.G_NAME LIKE CONCAT('%', #{emsQueryDto.exgGname}, '%')
        </if>
        <if test="emsQueryDto.imgGname != null and emsQueryDto.imgGname != ''">
            AND aimg.G_NAME LIKE CONCAT('%', #{emsQueryDto.imgGname}, '%')
        </if>

    </select>

</mapper>
