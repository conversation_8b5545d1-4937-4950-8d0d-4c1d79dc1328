<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.NemsInvtHeadMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            a.*
        FROM
            `nems_invt_head` a
        LEFT JOIN nems_invt_list b ON a.ID = b.INV_ID
        <where>
            a.TENANT_ID = #{nemsInvtHeadDTO.tenantId}
            <if test="nemsInvtHeadDTO.bizopEtpsNm != null and nemsInvtHeadDTO.bizopEtpsNm !=''">
                AND a.BIZOP_ETPS_NM LIKE CONCAT('%', #{nemsInvtHeadDTO.bizopEtpsNm}, '%')
            </if>
            <if test="nemsInvtHeadDTO.bizopEtpsno != null and nemsInvtHeadDTO.bizopEtpsno !=''">
                AND a.BIZOP_ETPSNO LIKE CONCAT('%', #{nemsInvtHeadDTO.bizopEtpsno}, '%')
            </if>
            <if test="nemsInvtHeadDTO.seqNo != null and nemsInvtHeadDTO.seqNo !=''">
                AND a.SEQ_NO LIKE CONCAT('%', #{nemsInvtHeadDTO.seqNo}, '%')
            </if>
            <if test="nemsInvtHeadDTO.bondInvtNo != null and nemsInvtHeadDTO.bondInvtNo !=''">
                AND a.BOND_INVT_NO LIKE CONCAT('%', #{nemsInvtHeadDTO.bondInvtNo}, '%')
            </if>
            <if test="nemsInvtHeadDTO.etpsInnerInvtNo != null and nemsInvtHeadDTO.etpsInnerInvtNo !=''">
                AND a.ETPS_INNER_INVT_NO LIKE CONCAT('%', #{nemsInvtHeadDTO.etpsInnerInvtNo}, '%')
            </if>
            <if test="nemsInvtHeadDTO.putrecNo != null and nemsInvtHeadDTO.putrecNo !=''">
                AND a.PUTREC_NO LIKE CONCAT('%', #{nemsInvtHeadDTO.putrecNo}, '%')
            </if>
            <if test="nemsInvtHeadDTO.ieFlag != null and nemsInvtHeadDTO.ieFlag !=''">
                AND a.IMPEXP_MARKCD = #{nemsInvtHeadDTO.ieFlag}
            </if>
            <if test="nemsInvtHeadDTO.starCreateDate != null and nemsInvtHeadDTO.starCreateDate !=''
             and nemsInvtHeadDTO.lastCreateDate != null and nemsInvtHeadDTO.lastCreateDate !=''">
                AND
                DATE_FORMAT(a.CREATE_DATE, '%Y-%m-%d') between DATE_FORMAT(#{nemsInvtHeadDTO.starCreateDate}, '%Y-%m-%d')
                AND DATE_FORMAT(#{nemsInvtHeadDTO.lastCreateDate}, '%Y-%m-%d')
            </if>
            <if test="nemsInvtHeadDTO.starDclDate != null and nemsInvtHeadDTO.starDclDate !=''
             and nemsInvtHeadDTO.lastDclDate != null and nemsInvtHeadDTO.lastDclDate !=''">
                AND
                DATE_FORMAT(a.INVT_DCL_TIME, '%Y-%m-%d') between DATE_FORMAT(#{nemsInvtHeadDTO.starDclDate}, '%Y-%m-%d')
                AND DATE_FORMAT(#{nemsInvtHeadDTO.lastDclDate}, '%Y-%m-%d')
            </if>

            <if test="nemsInvtHeadDTO.hasSC != null and nemsInvtHeadDTO.hasSC != '' and nemsInvtHeadDTO.hasSC == 0">

                AND (
                b.STORAGE_NO = ''
                OR b.STORAGE_NO IS NULL)
            </if>
            <if test="nemsInvtHeadDTO.hasSC != null and nemsInvtHeadDTO.hasSC != '' and nemsInvtHeadDTO.hasSC == 1">

                AND b.STORAGE_NO != ''
                AND b.STORAGE_NO IS NOT NULL
            </if>
            <if test="nemsInvtHeadDTO.storageNos != null and nemsInvtHeadDTO.storageNos != ''">
                AND b.STORAGE_NO LIKE CONCAT('%', #{nemsInvtHeadDTO.storageNos}, '%')
            </if>
            <if test="nemsInvtHeadDTO.createPerson != null and nemsInvtHeadDTO.createPerson != ''">
                AND a.CREATE_PERSON = #{nemsInvtHeadDTO.createPerson}
            </if>
            <if test="nemsInvtHeadDTO.isSend != null and nemsInvtHeadDTO.isSend != '' and nemsInvtHeadDTO.isSend == 0">
                AND a.SEND = 0
            </if>
            <if test="nemsInvtHeadDTO.isSend != null and nemsInvtHeadDTO.isSend != '' and nemsInvtHeadDTO.isSend == 1">
                AND a.SEND = 1
            </if>
            <if test="nemsInvtHeadDTO.status != null and nemsInvtHeadDTO.status != ''">
                AND a.STATUS = #{nemsInvtHeadDTO.status}
            </if>
              <if test="nemsInvtHeadDTO.bizopEtpsno != null and nemsInvtHeadDTO.bizopEtpsno != ''">
            AND a.BIZOP_ETPSNO LIKE CONCAT('%', #{nemsInvtHeadDTO.bizopEtpsno}, '%')
            </if>
            <if test="nemsInvtHeadDTO.invtType != null and nemsInvtHeadDTO.invtType != ''">
                AND a.INVT_TYPE = #{nemsInvtHeadDTO.invtType}
            </if>
            <if test="nemsInvtHeadDTO.vrfdedMarkcd != null and nemsInvtHeadDTO.vrfdedMarkcd != ''">
                AND a.VRFDED_MARKCD = #{nemsInvtHeadDTO.vrfdedMarkcd}
            </if>
            <if test="nemsInvtHeadDTO.dclEtpsNm != null and nemsInvtHeadDTO.dclEtpsNm != ''">
                AND a.DCL_ETPS_NM LIKE CONCAT('%', #{nemsInvtHeadDTO.dclEtpsNm}, '%')
            </if>
            <if test="nemsInvtHeadDTO.supvModecd != null and nemsInvtHeadDTO.supvModecd != ''">
                AND a.SUPV_MODECD = #{nemsInvtHeadDTO.supvModecd}
            </if>
            <if test="nemsInvtHeadDTO.mtpckEndprdMarkcd != null and nemsInvtHeadDTO.mtpckEndprdMarkcd != ''">
                AND a.MTPCK_ENDPRD_MARKCD = #{nemsInvtHeadDTO.mtpckEndprdMarkcd}
            </if>
            <if test="nemsInvtHeadDTO.putrecSeqno != null and nemsInvtHeadDTO.putrecSeqno != ''">
                AND b.PUTREC_SEQNO = #{nemsInvtHeadDTO.putrecSeqno}
            </if>
            <if test="nemsInvtHeadDTO.gdsMtno != null and nemsInvtHeadDTO.gdsMtno != ''">
                AND b.GDS_MTNO LIKE CONCAT('%', #{nemsInvtHeadDTO.gdsMtno}, '%')
            </if>
            <if test="nemsInvtHeadDTO.initialReviewStatus != null and nemsInvtHeadDTO.initialReviewStatus != ''">
                AND a.INITIAL_REVIEW_STATUS = #{nemsInvtHeadDTO.initialReviewStatus}
            </if>
            <if test="nemsInvtHeadDTO.sysId != null and nemsInvtHeadDTO.sysId != ''">
                AND a.SYS_ID = #{nemsInvtHeadDTO.sysId}
            </if>
        </where>
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>
    <select id="listBySeqNos" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            a.*
        FROM
            `nems_invt_head` a
        WHERE
            a.SEQ_NO IN
            <foreach collection="seqNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="listDeclarationRecord" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            ANY_VALUE ( NEMS_INVT_HEAD.ID ) id,
            NEMS_INVT_HEAD.PUTREC_NO,
            ANY_VALUE ( NEMS_INVT_LIST.ID ) vid,
            NEMS_INVT_LIST.PUTREC_SEQNO putrecSeqno,
            NEMS_INVT_HEAD.MTPCK_ENDPRD_MARKCD,
            NEMS_INVT_HEAD.BOND_INVT_NO,
            NEMS_INVT_HEAD.ETPS_INNER_INVT_NO,
            VRFDED_MARKCD,
            INVT_DCL_TIME,
            IMPEXP_MARKCD,
            SUM( DCL_QTY ) AS DCL_QTY,
            ANY_VALUE ( DEC_HEAD.CLEARANCE_NO ) dclEtpsno,
            ANY_VALUE ( DEC_HEAD.APP_DATE ) declarationDate,
            NEMS_INVT_HEAD.CREATE_DATE createDate,
            NEMS_INVT_HEAD.CREATE_PERSON createPerson
        FROM NEMS_INVT_HEAD
        LEFT JOIN NEMS_INVT_LIST ON NEMS_INVT_HEAD.ID = NEMS_INVT_LIST.INV_ID
        LEFT JOIN DEC_HEAD ON DEC_HEAD.INV_ID=NEMS_INVT_HEAD.ID
        WHERE NEMS_INVT_HEAD.PUTREC_NO = #{putrecNo} AND NEMS_INVT_LIST.PUTREC_SEQNO = #{putrecSeqno} AND
            NEMS_INVT_HEAD.MTPCK_ENDPRD_MARKCD = #{mtpckEndprdMarkcd}
        AND (NEMS_INVT_HEAD.STATUS IS NULL OR NEMS_INVT_HEAD.STATUS &lt;&gt;'E')
        AND (NEMS_INVT_HEAD.DCL_TYPECD IS NULL OR NEMS_INVT_HEAD.DCL_TYPECD != '3')
        <if test="vrfdedMarkcd != null and vrfdedMarkcd != ''">
            AND NEMS_INVT_HEAD.VRFDED_MARKCD = #{vrfdedMarkcd}
        </if>
        GROUP BY NEMS_INVT_HEAD.ID
    </select>

    <select id="listDecByEmsNo" resultType="org.jeecg.modules.business.entity.DecHead">
        SELECT
            *
        FROM
            `dec_head`
        WHERE
            CLEARANCE_NO IN (
                SELECT
                    a.ENTRY_NO
                FROM
                    `nems_invt_head` a
                        LEFT JOIN nems_invt_list b ON a.ID = b.INV_ID
                WHERE
                    a.PUTREC_NO = #{emsNo}
                  AND b.PUTREC_SEQNO = #{putrecSeqno}
                  AND a.IMPEXP_MARKCD = #{impexpMarkcd}
            )
        ORDER BY
            CREATE_TIME DESC
    </select>
    <select id="listInvtByEmsNo" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            a.*
        FROM
            `nems_invt_head` a
                LEFT JOIN nems_invt_list b ON a.ID = b.INV_ID
        WHERE
            a.PUTREC_NO = #{emsNo}
          AND b.PUTREC_SEQNO = #{putrecSeqno}
          AND a.IMPEXP_MARKCD = #{impexpMarkcd}
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>
    <select id="queryDict" resultType="java.lang.String">
        SELECT
            CONCAT_WS( '_', a.ITEM_TEXT, a.ITEM_VALUE )
        FROM
            `sys_dict_item` a
                LEFT JOIN sys_dict b ON a.DICT_ID = b.ID
        WHERE
            b.DICT_CODE = #{dictCode}
    </select>
    <select id="queryDict1" resultType="java.lang.String">
        SELECT
            CONCAT_WS( '_', ${dicText}, ${dicCode})
        FROM
            ${dicTable}
        <where>
            <if test="dicTable == 'erp_countries'">
                AND ISENABLED = 1
            </if>
        </where>
    </select>

    <insert id="insertBatchSomeColumn">
        insert into nems_invt_head (ID, APPLY_NUMBER, BOND_INVT_NO,
                                          PART_ID, SEQ_NO, CHG_TMS_CNT,
                                          PUTREC_NO, ETPS_INNER_INVT_NO, BIZOP_ETPS_SCCD,
                                          BIZOP_ETPSNO, BIZOP_ETPS_NM, RCVGD_ETPSNO,
                                          RVSNGD_ETPS_SCCD, RCVGD_ETPS_NM, DCL_ETPS_SCCD,
                                          DCL_ETPSNO, DCL_ETPS_NM, INVT_DCL_TIME,
                                          ENTRY_DCL_TIME, ENTRY_NO, RLTINVT_NO,
                                          RLTPUTREC_NO, RLT_ENTRY_NO, RLT_ENTRY_BIZOP_ETPS_SCCD,
                                          RLT_ENTRY_BIZOP_ETPSNO, RLT_ENTRY_BIZOP_ETPS_NM,
                                          RLT_ENTRY_RVSNGD_ETPS_SCCD, RLT_ENTRY_RCVGD_ETPSNO,
                                          RLT_ENTRY_RCVGD_ETPS_NM, RLT_ENTRY_DCL_ETPS_SCCD,
                                          RLT_ENTRY_DCL_ETPSNO, RLT_ENTRY_DCL_ETPS_NM, IMPEXP_PORTCD,
                                          DCLPLC_CUSCD, IMPEXP_MARKCD, MTPCK_ENDPRD_MARKCD,
                                          SUPV_MODECD, TRSP_MODECD, DCLCUS_FLAG,
                                          DCLCUS_TYPECD, VRFDED_MARKCD, INVT_IOCHKPT_STUCD,
                                          PREVD_TIME, FORMAL_VRFDED_TIME, APPLY_NO,
                                          LIST_TYPE, INPUT_CODE, INPUT_CREDIT_CODE,
                                          INPUT_NAME, IC_CARD_NO, INPUT_TIME,
                                          LIST_STAT, CORR_ENTRY_DCL_ETPS_SCCD, CORR_ENTRY_DCL_ETPSNO,
                                          CORR_ENTRY_DCL_ETPS_NM, DEC_TYPE, ADD_TIME,
                                          STSHIP_TRSARV_NATCD, INVT_TYPE, ENTRY_STUCD,
                                          PASSPORT_USED_TYPE_CD, RMK, CREATE_PERSON,
                                          CREATE_DATE, STATUS, AUDITED,
                                          HAWB, CHECKED, CHECK_DATE,
                                          DECLARATION_DATE, GOODS_TYPE, BALANCE_QTY,
                                          SYS_ID, MOULD_ID, GEN_DEC_FLAG,
                                          NON_BUSINESS, SEND, DCL_TYPECD,
                                          EMS_FLOWS_ID, APPLY_ID, TENANT_ID,
                                          DCL_TENANT_ID, CHARGED_WEIGHT, UPDATE_BY,
                                          UPDATE_DATE, IMPORT_SEQ_NO, RBAC_IDS,
                                          INPUT_ID,
                                          STOCK_HEAD_ID, CREATE_PASS_PORT, WAREHOUSING_DATE,
                                          AIRCRAFT_REGISTRATION_NUMBER, AIRCRAFT_TYPE,
                                          INITIAL_REVIEW_STATUS, FIRST_TRIAL_BY, FIRST_TRIAL_DATE,
                                          REVIEW_BY, REVIEW_DATE, FLY_ID,
                                          IS_RECEIPT, RECEIPT_DATE, FIRST_OPINION,
                                          REVIEW_OPINION)
        values
        <foreach collection="addHeads" item="a" separator=",">
            (#{a.id,jdbcType=BIGINT}, #{a.applyNumber,jdbcType=BIGINT}, #{a.bondInvtNo,jdbcType=VARCHAR},
            #{a.partId,jdbcType=VARCHAR}, #{a.seqNo,jdbcType=VARCHAR}, #{a.chgTmsCnt,jdbcType=INTEGER},
            #{a.putrecNo,jdbcType=VARCHAR}, #{a.etpsInnerInvtNo,jdbcType=VARCHAR}, #{a.bizopEtpsSccd,jdbcType=VARCHAR},
            #{a.bizopEtpsno,jdbcType=VARCHAR}, #{a.bizopEtpsNm,jdbcType=VARCHAR}, #{a.rcvgdEtpsno,jdbcType=VARCHAR},
            #{a.rvsngdEtpsSccd,jdbcType=VARCHAR}, #{a.rcvgdEtpsNm,jdbcType=VARCHAR}, #{a.dclEtpsSccd,jdbcType=VARCHAR},
            #{a.dclEtpsno,jdbcType=VARCHAR}, #{a.dclEtpsNm,jdbcType=VARCHAR}, #{a.invtDclTime,jdbcType=TIMESTAMP},
            #{a.entryDclTime,jdbcType=TIMESTAMP}, #{a.entryNo,jdbcType=VARCHAR}, #{a.rltinvtNo,jdbcType=VARCHAR},
            #{a.rltputrecNo,jdbcType=VARCHAR}, #{a.rltEntryNo,jdbcType=VARCHAR}, #{a.rltEntryBizopEtpsSccd,jdbcType=VARCHAR},
            #{a.rltEntryBizopEtpsno,jdbcType=VARCHAR}, #{a.rltEntryBizopEtpsNm,jdbcType=VARCHAR},
            #{a.rltEntryRvsngdEtpsSccd,jdbcType=VARCHAR}, #{a.rltEntryRcvgdEtpsno,jdbcType=VARCHAR},
            #{a.rltEntryRcvgdEtpsNm,jdbcType=VARCHAR}, #{a.rltEntryDclEtpsSccd,jdbcType=VARCHAR},
            #{a.rltEntryDclEtpsno,jdbcType=VARCHAR}, #{a.rltEntryDclEtpsNm,jdbcType=VARCHAR}, #{a.impexpPortcd,jdbcType=VARCHAR},
            #{a.dclplcCuscd,jdbcType=VARCHAR}, #{a.impexpMarkcd,jdbcType=VARCHAR}, #{a.mtpckEndprdMarkcd,jdbcType=VARCHAR},
            #{a.supvModecd,jdbcType=VARCHAR}, #{a.trspModecd,jdbcType=VARCHAR}, #{a.dclcusFlag,jdbcType=VARCHAR},
            #{a.dclcusTypecd,jdbcType=VARCHAR}, #{a.vrfdedMarkcd,jdbcType=VARCHAR}, #{a.invtIochkptStucd,jdbcType=VARCHAR},
            #{a.prevdTime,jdbcType=TIMESTAMP}, #{a.formalVrfdedTime,jdbcType=TIMESTAMP}, #{a.applyNo,jdbcType=VARCHAR},
            #{a.listType,jdbcType=VARCHAR}, #{a.inputCode,jdbcType=VARCHAR}, #{a.inputCreditCode,jdbcType=VARCHAR},
            #{a.inputName,jdbcType=VARCHAR}, #{a.icCardNo,jdbcType=VARCHAR}, #{a.inputTime,jdbcType=TIMESTAMP},
            #{a.listStat,jdbcType=VARCHAR}, #{a.corrEntryDclEtpsSccd,jdbcType=VARCHAR}, #{a.corrEntryDclEtpsno,jdbcType=VARCHAR},
            #{a.corrEntryDclEtpsNm,jdbcType=VARCHAR}, #{a.decType,jdbcType=VARCHAR}, #{a.addTime,jdbcType=TIMESTAMP},
            #{a.stshipTrsarvNatcd,jdbcType=VARCHAR}, #{a.invtType,jdbcType=VARCHAR}, #{a.entryStucd,jdbcType=VARCHAR},
            #{a.passportUsedTypeCd,jdbcType=VARCHAR}, #{a.rmk,jdbcType=VARCHAR}, #{a.createPerson,jdbcType=VARCHAR},
            #{a.createDate,jdbcType=TIMESTAMP}, #{a.status,jdbcType=VARCHAR}, #{a.audited,jdbcType=BIT},
            #{a.hawb,jdbcType=VARCHAR}, #{a.checked,jdbcType=BIT}, #{a.checkDate,jdbcType=TIMESTAMP},
            #{a.declarationDate,jdbcType=TIMESTAMP}, #{a.goodsType,jdbcType=VARCHAR}, #{a.balanceQty,jdbcType=DECIMAL},
            #{a.sysId,jdbcType=VARCHAR}, #{a.mouldId,jdbcType=VARCHAR}, #{a.genDecFlag,jdbcType=VARCHAR},
            #{a.nonBusiness,jdbcType=BIT}, #{a.send,jdbcType=BIT}, #{a.dclTypecd,jdbcType=VARCHAR},
            #{a.emsFlowsId,jdbcType=BIGINT}, #{a.applyId,jdbcType=VARCHAR}, #{a.tenantId,jdbcType=BIGINT},
            #{a.dclTenantId,jdbcType=BIGINT}, #{a.chargedWeight,jdbcType=DECIMAL}, #{a.updateBy,jdbcType=VARCHAR},
            #{a.updateDate,jdbcType=TIMESTAMP}, #{a.importSeqNo,jdbcType=VARCHAR}, #{a.rbacIds,jdbcType=VARCHAR},
            #{a.inputId,jdbcType=BIGINT},
            #{a.stockHeadId,jdbcType=VARCHAR}, #{a.createPassPort,jdbcType=BIT}, #{a.warehousingDate,jdbcType=TIMESTAMP},
            #{a.aircraftRegistrationNumber,jdbcType=VARCHAR}, #{a.aircraftType,jdbcType=VARCHAR},
            #{a.initialReviewStatus,jdbcType=VARCHAR}, #{a.firstTrialBy,jdbcType=VARCHAR}, #{a.firstTrialDate,jdbcType=TIMESTAMP},
            #{a.reviewBy,jdbcType=VARCHAR}, #{a.reviewDate,jdbcType=TIMESTAMP}, #{a.flyId,jdbcType=VARCHAR},
            #{a.isReceipt,jdbcType=BIT}, #{a.receiptDate,jdbcType=TIMESTAMP}, #{a.firstOpinion,jdbcType=VARCHAR},
            #{a.reviewOpinion,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="insertBatchLists">
        insert into nems_invt_list (ID, INV_ID, APPLY_NUMBER,
                ETPS_INNER_INVT_NO, SEQ_NO, GDSSEQ_NO,
                PUTREC_SEQNO, GDS_MTNO, HSCODE,
                HSNAME, HSMODEL, DCL_UNITCD,
                LAWF_UNITCD, SECDLAWF_UNITCD, NATCD,
                DCL_UPRCAMT, DCL_TOTALAMT, USDSTAT_TOTALAMT,
                DCL_CURRCD, LAWF_QTY, SECD_LAWF_QTY,
                WTSF_VAL, FSTSF_VAL, SECDSF_VAL,
                DCL_QTY, GROSS_WT, NET_WT,
                USE_CD, LVYRLF_MODECD, UCNS_VERNO,
                ENTRY_GDS_SEQNO, CLYMARKCD, APPLY_TB_SEQNO_A,
                APPLY_TB_SEQNO_B, ADD_TIME, ACTL_PASS_QTY,
                PASS_PORTUSED_QTY, RMK, HSTYPE,
                GOODS_ID, IMPORT_BILL_NO, CIQ_CODE,
                CIQ_NAME, AIRCRAFT_REGISTRATION_NUMBER, CUSTOM_HSCODE,
                CUSTOM_DNAME, CUSTOM_HSNAME, TRANS_MODE,
                TRADE_COUNTRY, PO, SUPPLIER,
                ORIGIN_COUNTRY, UNIVERSAL, BALANCE_QTY,
                VERSION, SUPV_MODECD, EN_MEMO,
                PACKS, BOND_INVT_NO, APPLY_ID,
                STOCK_GOODS_ID, AUTO_NO,MODF_MARKCD,MODF_MARKCD_NAME)
        values
        <foreach collection="addLists" item="a" separator=",">
            (#{a.id,jdbcType=BIGINT}, #{a.invId,jdbcType=BIGINT}, #{a.applyNumber,jdbcType=BIGINT},
            #{a.etpsInnerInvtNo,jdbcType=VARCHAR}, #{a.seqNo,jdbcType=VARCHAR}, #{a.gdsseqNo,jdbcType=INTEGER},
            #{a.putrecSeqno,jdbcType=INTEGER}, #{a.gdsMtno,jdbcType=VARCHAR}, #{a.hscode,jdbcType=VARCHAR},
            #{a.hsname,jdbcType=VARCHAR}, #{a.hsmodel,jdbcType=VARCHAR}, #{a.dclUnitcd,jdbcType=VARCHAR},
            #{a.lawfUnitcd,jdbcType=VARCHAR}, #{a.secdlawfUnitcd,jdbcType=VARCHAR}, #{a.natcd,jdbcType=VARCHAR},
            #{a.dclUprcamt,jdbcType=DECIMAL}, #{a.dclTotalamt,jdbcType=DECIMAL}, #{a.usdstatTotalamt,jdbcType=DECIMAL},
            #{a.dclCurrcd,jdbcType=VARCHAR}, #{a.lawfQty,jdbcType=DECIMAL}, #{a.secdLawfQty,jdbcType=DECIMAL},
            #{a.wtsfVal,jdbcType=DECIMAL}, #{a.fstsfVal,jdbcType=DECIMAL}, #{a.secdsfVal,jdbcType=DECIMAL},
            #{a.dclQty,jdbcType=DECIMAL}, #{a.grossWt,jdbcType=DECIMAL}, #{a.netWt,jdbcType=DECIMAL},
            #{a.useCd,jdbcType=VARCHAR}, #{a.lvyrlfModecd,jdbcType=VARCHAR}, #{a.ucnsVerno,jdbcType=VARCHAR},
            #{a.entryGdsSeqno,jdbcType=INTEGER}, #{a.clymarkcd,jdbcType=VARCHAR}, #{a.applyTbSeqnoA,jdbcType=INTEGER},
            #{a.applyTbSeqnoB,jdbcType=VARCHAR}, #{a.addTime,jdbcType=TIMESTAMP}, #{a.actlPassQty,jdbcType=DECIMAL},
            #{a.passPortusedQty,jdbcType=DECIMAL}, #{a.rmk,jdbcType=VARCHAR}, #{a.hstype,jdbcType=VARCHAR},
            #{a.goodsId,jdbcType=VARCHAR}, #{a.importBillNo,jdbcType=VARCHAR}, #{a.ciqCode,jdbcType=VARCHAR},
            #{a.ciqName,jdbcType=VARCHAR}, #{a.aircraftRegistrationNumber,jdbcType=VARCHAR}, #{a.customHscode,jdbcType=VARCHAR},
            #{a.customDname,jdbcType=VARCHAR}, #{a.customHsname,jdbcType=VARCHAR}, #{a.transMode,jdbcType=VARCHAR},
            #{a.tradeCountry,jdbcType=VARCHAR}, #{a.po,jdbcType=VARCHAR}, #{a.supplier,jdbcType=VARCHAR},
            #{a.originCountry,jdbcType=VARCHAR}, #{a.universal,jdbcType=VARCHAR}, #{a.balanceQty,jdbcType=DECIMAL},
            #{a.version,jdbcType=INTEGER}, #{a.supvModecd,jdbcType=VARCHAR}, #{a.enMemo,jdbcType=VARCHAR},
            #{a.packs,jdbcType=INTEGER}, #{a.bondInvtNo,jdbcType=VARCHAR}, #{a.applyId,jdbcType=VARCHAR},
            #{a.stockGoodsId,jdbcType=BIGINT}, #{a.autoNo,jdbcType=BIGINT},
            #{a.modfMarkcd,jdbcType=BIGINT}, #{a.modfMarkcdName,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="listMonthlyReport" resultType="org.jeecg.modules.business.entity.dto.ReportManagerDTO">
        SELECT
        ANY_VALUE(COUNT(*)) dclItemNum,

        ANY_VALUE(SUM((case when nems_invt_list.DCL_UNITCD = '035' then nems_invt_list.DCL_QTY
        when nems_invt_list.LAWF_UNITCD = '035' then nems_invt_list.LAWF_QTY
        else nems_invt_list.SECD_LAWF_QTY end))) as netWeightSum,
        ANY_VALUE(SUM( nems_invt_list.DCL_TOTALAMT )) monetaryTotal,
        ANY_VALUE(COUNT( DISTINCT nems_invt_head.ID )) dclNum,
        ANY_VALUE(DCL_CURRCD) dclCurrcd,
        ANY_VALUE(nems_invt_head.ID) headId,
        ANY_VALUE(date_format(nems_invt_head.INVT_DCL_TIME, '%Y-%m' )) invtDclTime
        FROM
        nems_invt_head
        LEFT JOIN nems_invt_list ON nems_invt_head.ID = nems_invt_list.INV_ID
        <where>
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &gt;= #{startInvtDclMonth}
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &lt;= #{endInvtDclMonth}
        </where>
        GROUP BY date_format(nems_invt_head.INVT_DCL_TIME, '%Y-%m' ),DCL_CURRCD
    </select>
    <select id="listMonthlyListEntitySize"  resultType="java.lang.Long">
        SELECT
        COUNT(DISTINCT nems_invt_head.ID) dclNum
        FROM
        nems_invt_head
        LEFT JOIN nems_invt_list ON nems_invt_head.ID = nems_invt_list.INV_ID
        <where>
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &gt;= #{startInvtDclMonth}
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &lt;= #{endInvtDclMonth}
        </where>
    </select>
    <select id="listMonthlyListEntity"  resultType="org.jeecg.modules.business.entity.dto.MonthlyListEntity">
        SELECT
        nems_invt_head.SUPV_MODECD,
        nems_invt_head.INVT_DCL_TIME,
        nems_invt_head.PUTREC_NO,
        nems_invt_head.IMPEXP_MARKCD,
        nems_invt_head.ETPS_INNER_INVT_NO,
        nems_invt_list.PUTREC_SEQNO,
        nems_invt_head.BOND_INVT_NO,
        nems_invt_list.GDS_MTNO,
        nems_invt_list.HSMODEL,
        nems_invt_head.DCLCUS_FLAG,
        nems_invt_head.DCLCUS_TYPECD,
        nems_invt_head.RLT_ENTRY_NO,
        nems_invt_head.ENTRY_NO,
        nems_invt_head.RLT_ENTRY_NO,
        nems_invt_head.ENTRY_DCL_TIME,
        nems_invt_list.DCL_QTY,
        nems_invt_list.DCL_UNITCD,
        nems_invt_list.IMPORT_BILL_NO,
        nems_invt_head.DECLARATION_DATE,
        nems_invt_head.DCLCUS_FLAG,
        nems_invt_list.DCL_UPRCAMT,
        nems_invt_list.DCL_TOTALAMT,
        nems_invt_list.DCL_CURRCD,
        (case when nems_invt_list.DCL_UNITCD = '035' then nems_invt_list.DCL_QTY
                when nems_invt_list.LAWF_UNITCD = '035' then nems_invt_list.LAWF_QTY
            else nems_invt_list.SECD_LAWF_QTY end) as netWeight
        FROM
        nems_invt_head
        LEFT JOIN nems_invt_list ON nems_invt_head.ID = nems_invt_list.INV_ID
        <where>
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &gt;= #{startInvtDclMonth}
            AND
            date_format(nems_invt_head.INVT_DCL_TIME,'%Y-%m' ) &lt;= #{endInvtDclMonth}
            AND
            nems_invt_head.IMPEXP_MARKCD = #{flag}

        </where>
    </select>

    <select id="listByWriteOffBalance" resultType="org.jeecg.modules.business.entity.paramVo.NemsListByWriteOffBalanceVO">
        SELECT
        ANY_VALUE ( nh.PUTREC_NO ) emsNo,
            ANY_VALUE ( nl.PUTREC_SEQNO ) gno,
            ANY_VALUE ( nl.GDS_MTNO ) copGno,
            ANY_VALUE ( psa.G_NAME ) gname,
            ANY_VALUE ( nl.HSCODE ) codet,
            ANY_VALUE ( nl.DCL_UNITCD ) unit,
            ANY_VALUE (
                    SUM( nl.DCL_QTY )) importQty,
            ANY_VALUE (
                    SUM(
                            CASE

                                WHEN nh.SUPV_MODECD = '0844'
                                    OR nh.SUPV_MODECD = '0845'
                                    OR nh.SUPV_MODECD = '0644'
                                    OR nh.SUPV_MODECD = '0245'
                                    OR nh.SUPV_MODECD = '0446' THEN
                                    nl.DCL_QTY ELSE 0
                                END
                    )) AS marketQty,
            ANY_VALUE (
                    SUM( CASE WHEN nh.SUPV_MODECD = '0657' OR nh.SUPV_MODECD = '0258' THEN nl.DCL_QTY ELSE 0 END )) AS rollOutQty
        FROM
            nems_invt_list nl
                INNER JOIN nems_invt_head nh ON nh.ID = nl.INV_ID
        left join pts_ems_aimg psa on psa.G_NO = nl.PUTREC_SEQNO
        left join pts_ems_head psh on psh.id = psa.EMS_ID and nh.PUTREC_NO = psh.EMS_NO
        WHERE
            nh.IMPEXP_MARKCD = 'I'
          AND nh.TENANT_ID = #{tenantId}
          AND psh.TENANT_ID = #{tenantId}
          AND nl.PUTREC_SEQNO IS NOT NULL
        <if test="startDate != null and startDate !='' and lastDate != null and lastDate !=''">
            AND DATE_FORMAT(nh.INVT_DCL_TIME, '%Y-%m-%d') between DATE_FORMAT(#{startDate}, '%Y-%m-%d')
                AND DATE_FORMAT(#{lastDate}, '%Y-%m-%d')
        </if>
        AND nh.SUPV_MODECD NOT IN ('0300','0466','0700','4400','4500','4561','4600','5361')

        GROUP BY
            nl.PUTREC_SEQNO,nh.PUTREC_NO
        ORDER BY
            nl.PUTREC_SEQNO

    </select>

    <select id="listAimgByTenantId" resultType="org.jeecg.modules.business.entity.PtsEmsAimg">
        select pts_ems_aimg.G_NO,
               pts_ems_aimg.COP_GNO
        from pts_ems_aimg inner join pts_ems_head on pts_ems_head.id = pts_ems_aimg.EMS_ID
        where pts_ems_head.tenant_id = #{tenantId}
    </select>
    <select id="listAexgByTenantId" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        select pts_ems_aexg.G_NO,
               pts_ems_aexg.COP_GNO
        from pts_ems_aexg inner join pts_ems_head on pts_ems_head.id = pts_ems_aexg.EMS_ID
        where pts_ems_head.tenant_id = #{tenantId}
    </select>

    <select id="listNemsInvtHead" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            *
        FROM
            `nems_invt_head`
        WHERE
            SEQ_NO = #{seqNo}
          AND TENANT_ID = #{tenantId}
    </select>
    <select id="selectUnClosed" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            *
        FROM
            `nems_invt_head`
        WHERE
            TENANT_ID = #{tenantId}
          AND VRFDED_MARKCD IN ( '0', '1' )
        <if test="startTime != null and startTime != ''">
            AND date_format(CREATE_DATE, '%Y-%m-%d') &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND date_format(CREATE_DATE, '%Y-%m-%d') &lt;= #{endTime}
        </if>
        ORDER BY
            CREATE_DATE DESC
    </select>

</mapper>
