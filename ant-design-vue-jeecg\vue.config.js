const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')

function resolve(dir) {
	return path.join(__dirname, dir)
}

// vue.config.js
module.exports = {
	runtimeCompiler: true,
	/*
    Vue-cli3:
    Crashed when using Webpack `import()` #2463
    https://github.com/vuejs/vue-cli/issues/2463
   */
	// 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
	productionSourceMap: false,
	// 多入口配置
	// pages: {
	//   index: {
	//     entry: 'src/main.js',
	//     template: 'public/index.html',
	//     n'p'm 'index.html',
	//   }
	// },
	//打包app时放开该配置
	//publicPath:'./',
	configureWebpack: config => {
		//生产环境取消 console.log
		if (process.env.NODE_ENV === 'production') {
			config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
		}

		// 优化代码分割策略
		config.optimization = {
			...config.optimization,
			splitChunks: {
				chunks: 'all',
				cacheGroups: {
					// 基础框架库
					vendor: {
						name: 'chunk-vendors',
						test: /[\\/]node_modules[\\/]/,
						priority: 10,
						chunks: 'initial'
					},
					// UI组件库
					antd: {
						name: 'chunk-antd',
						test: /[\\/]node_modules[\\/](ant-design-vue|@ant-design)[\\/]/,
						priority: 20,
						chunks: 'all'
					},
					// 图表库
					charts: {
						name: 'chunk-charts',
						test: /[\\/]node_modules[\\/](echarts|@antv)[\\/]/,
						priority: 20,
						chunks: 'all'
					},
					// 编辑器库
					editors: {
						name: 'chunk-editors',
						test: /[\\/]node_modules[\\/](tinymce|codemirror|@tinymce)[\\/]/,
						priority: 20,
						chunks: 'all'
					},
					// 公共代码
					common: {
						name: 'chunk-common',
						minChunks: 2,
						priority: 5,
						chunks: 'all',
						reuseExistingChunk: true
					}
				}
			}
		}
	},

	chainWebpack: config => {
		config.resolve.alias
			.set('@$', resolve('src'))
			.set('@api', resolve('src/api'))
			.set('@assets', resolve('src/assets'))
			.set('@comp', resolve('src/components'))
			.set('@views', resolve('src/views'))

		// 禁用prefetch和preload，减少首屏网络请求
		config.plugins.delete('prefetch')
		config.plugins.delete('preload')

		//生产环境，开启js\css压缩
		if (process.env.NODE_ENV === 'production') {
			config.plugin('compressionPlugin').use(
				new CompressionPlugin({
					test: /\.(js|css|less)$/, // 匹配文件名
					threshold: 10240, // 对超过10k的数据压缩
					deleteOriginalAssets: false // 不删除源文件
				})
			)
		}

		// 配置 webpack 识别 markdown 为普通的文件
		// config.module
		//   .rule('markdown')
		//   .test(/\.md$/)
		//   .use()
		//   .loader('file-loader')
		//   .end()

		config.module
			.rule('md')
			.test(/\.md$/)
			.use('html-loader')
			.loader('html-loader')
			.end()

		// 编译vxe-table包里的es6代码，解决IE11兼容问题
		config.module
			.rule('vxe')
			.test(/\.js$/)
			.include.add(resolve('node_modules/vxe-table'))
			.add(resolve('node_modules/vxe-table-plugin-antd'))
			.end()
			.use()
			.loader('babel-loader')
			.end()
	},

	css: {
		loaderOptions: {
			less: {
				modifyVars: {
					/* less 变量覆盖，用于自定义 ant design 主题 */
					'primary-color': '#1A4DF2',
					'link-color': '#fff',
					'border-radius-base': '4px',
					'menu-dark-bg': '#1A3176',
					'menu-dark-submenu-bg': '#1A3176',
					'layout-sider-background': '#1A3176'
				},
				javascriptEnabled: true
			}
		}
	},

	devServer: {
		// port: 3000,
		port: 80,
		open: true,
		headers: { 'Access-Control-Allow-Origin': '*' },
		proxy: {
			/* '/api': {
         target: 'https://mock.ihx.me/mock/5baf3052f7da7e07e04a5116/antd-pro', //mock API接口系统
         ws: false,
         changeOrigin: true,
         pathRewrite: {
           '/jeecg-boot': ''  //默认所有请求都加了jeecg-boot前缀，需要去掉
         }
       },*/
			'/jeecg-boot': {
				target: 'http://localhost:8080', //请求本地 需要jeecg-boot后台项目
				// target: 'http://************:8080', //请求本地 需要jeecg-boot后台项目
				ws: false,
				changeOrigin: true
			}
		}
	},

	lintOnSave: undefined
}

// module.exports = {
//   module: {
//     rules: [
//       {
//         test: /\.s(c|a)ss$/,
//         use: [
//           'vue-style-loader',
//           'css-loader',
//           {
//             loader: 'sass-loader',
//             // Requires sass-loader@^7.0.0
//             options: {
//               implementation: require('sass'),
//               indentedSyntax: true // optional
//             },
//             // Requires >= sass-loader@^8.0.0
//             options: {
//               implementation: require('sass'),
//               sassOptions: {
//                 indentedSyntax: true // optional
//               },
//             },
//           },
//         ],
//       },
//     ],
//   }
// }
