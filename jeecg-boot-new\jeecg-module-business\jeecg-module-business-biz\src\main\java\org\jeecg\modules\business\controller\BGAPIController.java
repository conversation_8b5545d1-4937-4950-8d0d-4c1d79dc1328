package org.jeecg.modules.business.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.EnterpriseInfo;
import org.jeecg.modules.business.mapper.EnterpriseInfoMapper;
import org.jeecg.modules.business.util.BGAPIUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @create 2024/12/31 14:47
 */
@RestController
@RequestMapping("/business/bgapi")
public class BGAPIController {
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    /**
     * OCR识别报关单
     * @param links
     * @return
     */
    @GetMapping("/Ydt_OcrRecognition")
    public Result<?> Ydt_OcrRecognition(@RequestParam(required = false) String links){
        Object data= BGAPIUtil.Ydt_OcrRecognition(links);
        return  Result.OK(data);
    }

    /**
     * 通过料号/序号获取手/账册备案信息
     * @param tradeCode
     * @param manualNo
     * @param gdsSeqNo
     * @return
     */
    @PostMapping("/GetManualItem")
    public Result<String> GetManualItem(@RequestParam("tradeCode") String tradeCode,
                                   @RequestParam("manualNo") String manualNo,
                                   @RequestParam("gdsSeqNo") String gdsSeqNo){
        //获取当前租户登陆的企业
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        String result=BGAPIUtil.GetManualItem("SC",tradeCode,manualNo,"",gdsSeqNo,"1",
                enterpriseInfo.getSwid());
        JSONObject jsonObject = JSON.parseObject(result);
        if (!jsonObject.getBoolean("ok")){
            return Result.error(jsonObject.getString("errors"));
        }
        return Result.ok(jsonObject.getString("data"));
    }
}
