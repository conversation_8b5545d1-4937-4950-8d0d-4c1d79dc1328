<template>
	<a-modal
		:title="title"
		:width="width"
		:visible="visible"
		@cancel="handleCancel"
		cancelText="关闭"
		:maskClosable="false"
	>
		<template slot="footer">
			<a-button type="primary" @click="handleCancel" icon="bulb">我知道了</a-button>
		</template>

		<!-- table区域-begin -->
		<div>
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="id"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				class="j-table-force-nowrap"
				@change="handleTableChange"
			>
				<span slot="remark" slot-scope="text, record" :title="record.remark">
          {{subStrForColumns(record.remark, 15)}}
        </span>
			</a-table>
		</div>

	</a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import { subStrForColumns } from '@/utils/util'
import { ajaxGetDictItems } from '@/api/api'
import store from '@/store'
import { TENANT_INFO } from '@/store/mutation-types'

export default {
	name: 'InventoryFlowModal',
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			disableMixinCreated: true,
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			title: '',
			width: 980,
			visible: false,
			// 表头
			columns: [
				{
					title: '业务单号',
					align: 'center',
					dataIndex: 'storageNo'
				},
				{
					title: '出入库',
					align: 'center',
					dataIndex: 'ieFlag',
					customRender:function (text) {
						if(text=='I'){
							return "入库";
						}else if(text=='E'){
							return "出库";
						}else{
							return text;
						}
					}
				},
				{
					title: '库区',
					align: 'center',
					dataIndex: 'areaCode'
				},
				{
					title: '储位',
					align: 'center',
					dataIndex: 'spaceCode'
				},
				{
					title: '清单编号',
					align: 'center',
					dataIndex: 'bondInvtNo'
				},
				{
					title: '操作类型',
					align: 'center',
					dataIndex: 'optType',
					customRender: this.showOptTypeText
				},
				{
					title: '库存变动数量',
					align: 'center',
					dataIndex: 'stockVar',
					customRender: (value, row, index) => {
						if(row.ieFlag == 'I'){
							return (
								<div>
									<span style={'vertical-align:middle;color:green;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						} else {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						}
					}
				},
				{
					title: '占用变动数量',
					align: 'center',
					dataIndex: 'occupyVar',
					customRender: (value, row, index) => {
						if(row.ieFlag == 'I'){
							return (
								<div>
									<span style={'vertical-align:middle;color:blue;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						} else {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						}
					}
				},
				{
					title: '变动日期',
					align: 'center',
					dataIndex: 'execDate',
				},
				// {
				// 	title: '备注',
				// 	align: 'center',
				// 	width: 250,
				// 	dataIndex: 'remark',
				// 	scopedSlots: { customRender: 'remark' }
				// },
			],
			url: {
				list: '/business/storeStocks/listGoodsFlow',
			},
			optTypeList: []
		}
	},
	created() {
		this.initDictData('BSCKANDSTORE_TYPE')
	},
	methods: {
		async open(record) {
			this.queryParam = Object.assign({}, record)
			this.loadData(1)
			this.visible = true
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						let dataSource = res.result.records || res.result
						
						// 根据企业信息过滤数据，屏蔽optType为2的数据
						const tenantInfo = store.getters.tenantInfo
						if (tenantInfo && tenantInfo.name && tenantInfo.name.includes('青岛双龙汇国际贸易有限公司')) {
							dataSource = dataSource.filter(item => item.optType !== 2)
						}
						
						this.dataSource = dataSource
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == 'BSCKANDSTORE_TYPE') {
					this.optTypeList = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length>0) {
							if (dictCode == 'BSCKANDSTORE_TYPE') {
								this.optTypeList = dictOptions
							}
						}
					}
				})
			}
		},
		showOptTypeText(text, record, index) {
			return this.getText(text, this.optTypeList)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text
		},
		subStrForColumns,
		close() {
			this.dataSource = []
			this.visible = false
		},
		handleCancel () {
			this.close()
		}
	}
}
</script>

<style scoped lang='less'>

</style>