<template>
  <fragment>
    <td
      :colspan="labelWidth"
      :title="label"
      :class="required || required == true ? 'tmpClass-required' : 'simple-td'"
      v-if="!noLabel"
    >
      {{ label }} &nbsp;
    </td>
    <td
      :colspan="valueWidth"
      :title="valueString || placeholder"
      style="text-align: left"
    >
      <a-form-model-item
        class="alone-input"
        :prop="iprop"
        @keyup.enter.native="keyFrom($event)"
        :required="required"
      >
        <a-date-picker
          ref="datePicker"
          @openChange="openChange"
          @keyup.enter.native="keyFrom($event)"
          :align="alignRight ? align : empty"
          :disabled="readonly"
          :keyFocusTo="keyFocusTo"
          :type="type"
					:format="dateFormat"
          :placeholder="placeholder"
          @change="onChange"
          size="small"
          v-model="val"
          :default-value="defaultValue"
        />
      </a-form-model-item>
    </td>
  </fragment>
</template>

<script>
import { Fragment } from "vue-fragment";
import moment from "moment";

export default {
  components: {
    Fragment,
  },
  name: "m-table-date-item",
  data() {
    return {
      align: {
        points: ["tr", "tr"],
        overflow: {
          adjustX: true,
          adjustY: true,
        },
      },
      empty: {},
      valueString: "",
      /**
       * v-model
       */
      val: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        if (!!val) {
          this.val = moment(val).format(this.dateFormat)
        }
      },
    },
  },
  props: {
    label: {
      type: String,
    },
    type: {
      type: String,
    },
    value: {
      required: true,
    },
    valueWidth: {
      default: 1,
    },
    iprop: {
      type: String,
    },
    required: {
      type: Boolean,
      default: false,
    },
    /**
     * label占用的单元格, 默认为1
     */
    labelWidth: {
      type: Number,
      default: 1,
    },
    /**
     * 提示内容
     */
    placeholder: {
      type: String,
      required: false,
    },
    /**
     * 右对齐, 最右边的日期选择器会显示不全, 可以用此方式解决
     */
    alignRight: {
      type: Boolean,
      required: false,
      default: false,
    },
    /**
     * 为true则不显示label
     */
    noLabel: {
      type: Boolean,
    },
    /**
     * 禁用
     */
    readonly: {
      type: Boolean,
    },
    id: {
      type: String,
    },
    defaultValue: {
      type: String,
    },
    // enter获取焦点的ref值
    keyFocusTo: {
      type: String,
    },
		dateFormat:{
			type: String,
			default: 'YYYY-MM-DD',
			required: false
		},
  },
  model: {
    prop: "value",
    event: "change",
  },
  methods: {
    openChange(status) {
      if (!status) {
        this.$refs.datePicker.focus();
      }
    },
    onChange(a, b) {
			this.$emit('change', b);
      // this.valueString = b;
      // if (!this.isEmpty(a)) {
      //   this.$emit("change", a.startOf("day").valueOf());
      //   return;
      // }
      // this.$emit("change", "");
    },
    // 定位焦点
    inputFocus() {
      this.$refs.datePicker.focus();
    },
    keyFrom(e) {
      if (this.label == "启运时间") {
        this.$emit("focusIndexDate", 58);
      }
    },
  },
};
</script>

<style scoped>
td div {
  width: 100%;
}

.alone-input >>> div.ant-col.ant-form-item-control-wrapper {
  width: 100%;
}

.not-alone-input >>> div.ant-col.ant-form-item-control-wrapper {
  max-width: 200px;
}

.not-alone-input {
  display: flex;
  justify-content: flex-end;
}

.not-alone-input >>> div.ant-col.ant-form-item-control-wrapper {
  flex: 2;
  max-width: 100%;
}

.not-alone-input >>> .ant-col.ant-form-item-label {
  /*flex: 1;*/
}

>>> .ant-calendar-picker {
  width: 100%;
}

.tmpClass-required::before {
  content: "*";
  color: rgb(253, 0, 0);
}

.alone-input >>> div.ant-col.ant-form-item-control-wrapper {
  width: 100%;
}
.alone-input >>> input.ant-calendar-picker-input.ant-input.ant-input-sm {
  font-size: 11px;
}

/*	.not-alone-input {
        display: flex;
    }

    .not-alone-input >>> .ant-form-item-control-wrapper {
        flex: 1;
    }*/
</style>
