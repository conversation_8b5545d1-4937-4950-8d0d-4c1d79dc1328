<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.EnterpriseInfoMapper">
    <insert id="insertBrokerInfo">
        INSERT INTO customs_broker_info (
            id,
            tenant_id,
            create_by,
            create_time,
            customs_broker_name,
            del_flag,
            departcd,
            unified_social_credit_code,
            remarks
        )
        VALUES (
                   #{id},
                   #{tenantId},
                   #{createBy},
                   #{createTime},
                   #{customsBrokerName},
                   #{delFlag},
                   #{departcd},
                   #{unifiedSocialCreditCode},
                   #{remarks}
               )
    </insert>
    <!--  -->
    <select id="getCollectionEnterpriseList" parameterType="org.jeecg.modules.system.entity.EnterpriseInfo"
            resultType="org.jeecg.modules.system.entity.EnterpriseInfo">

        SELECT
        e.id,
        e.enterprise_full_name,
        e.english_name,
        e.registered_address,
        e.registered_address_detail,
        e.unified_social_credit_code,
        e.enterprise_legal_person,
        e.cotact_ch_name,
        e.cotact_en_name,
        e.customs_declaration_code,
        e.sditds_userloginname,
        e.sditds_userpassowrd,
        e.private_key_certificate,
        e.message_transmission_number,
        e.web_site,
        e.english_adress,
        e.compay_telphone,
        e.compay_fax,
        e.suppliers_email,
        e.compay_picture,
        e.company_stamp,
        e.contract_stamp,
        e.document_stamp,
        e.purchasing_stamp,
        e.financial_stamp,
        e.del_flag,
        e.remarks,
        e.update_time
        FROM
        enterprise_info e

        <where>
            e.del_flag = 0
            and e.tenant_id = #{tenantId,jdbcType=INTEGER}
            <!--            and b.info_code = #{infoCode,jdbcType=VARCHAR}-->

        </where>
    </select>

    <select id="getTenantNameById" parameterType="int" resultType="String">
        SELECT `name` AS tenantName FROM sys_tenant WHERE id = #{tenantId,jdbcType=INTEGER}
    </select>
    <select id="getBrokerInfo" resultType="org.jeecg.common.system.vo.CustomsBrokerInfo">
        SELECT
            *
        FROM
            `customs_broker_info`
        WHERE
            CUSTOMS_BROKER_NAME = #{enterpriseName}
    </select>

    <update id="updateTenantName">
        UPDATE
        sys_tenant
        SET
        `name` = #{name,jdbcType=VARCHAR}
        <where>
            id = #{id,jdbcType=INTEGER}
        </where>
    </update>
</mapper>