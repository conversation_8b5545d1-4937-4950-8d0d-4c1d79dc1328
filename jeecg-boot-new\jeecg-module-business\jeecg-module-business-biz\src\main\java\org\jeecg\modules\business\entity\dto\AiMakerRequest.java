package org.jeecg.modules.business.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI制单请求参数对象
 *
 * <AUTHOR>
 * @date 2025/4/14 16:30
 */
@Data
@ApiModel(value = "AiMakerRequest", description = "AI制单请求参数")
public class AiMakerRequest {

    @ApiModelProperty(value = "上传文件数组", required = false)
    private MultipartFile[] files;

    @ApiModelProperty(value = "进出口标识", required = true, example = "E")
    private String ieFlag = "E";

    @ApiModelProperty(value = "客户名称", required = false)
    private String customerName;

    @ApiModelProperty(value = "申报地点", required = false)
    private String declarePlace;

    @ApiModelProperty(value = "出境关别代码", required = false)
    private String outPortCode;

    @ApiModelProperty(value = "运输方式代码", required = false)
    private String shipTypeCode;

    @ApiModelProperty(value = "模型提供商", required = true, example = "doubao")
    private String modelProvider = "doubao";

    /**
     * 默认构造函数
     */
    public AiMakerRequest() {
    }

    /**
     * 全参构造函数
     *
     * @param files 上传文件数组
     * @param ieFlag 进出口标识
     * @param customerName 客户名称
     * @param declarePlace 申报地点
     * @param outPortCode 出境关别代码
     * @param shipTypeCode 运输方式代码
     * @param modelProvider 模型提供商
     */
    public AiMakerRequest(MultipartFile[] files, String ieFlag, String customerName, 
                         String declarePlace, String outPortCode, String shipTypeCode, 
                         String modelProvider) {
        this.files = files;
        this.ieFlag = ieFlag != null ? ieFlag : "E";
        this.customerName = customerName;
        this.declarePlace = declarePlace;
        this.outPortCode = outPortCode;
        this.shipTypeCode = shipTypeCode;
        this.modelProvider = modelProvider != null ? modelProvider : "doubao";
    }
}
