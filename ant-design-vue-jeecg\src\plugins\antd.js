// Ant Design Vue 按需导入配置
// 只导入首屏必需的组件，其他组件按需加载

import Vue from 'vue'
import Viser from 'viser-vue'

// 首屏必需的基础组件
import {
	ConfigProvider,
	Layout,
	Input,
	InputNumber,
	Button,
	Switch,
	Radio,
	Checkbox,
	Select,
	Card,
	Form,
	FormModel,
	Row,
	Col,
	Modal,
	Table,
	Tabs,
	Icon,
	Menu,
	Dropdown,
	DatePicker,
	TimePicker,
	Popover,
	Tooltip,
	Alert,
	Tag,
	Divider,
	Spin,
	BackTop,
	Drawer,
	Badge,
	List,
	Avatar,
	Breadcrumb,
	Steps,
	Upload,
	Progress,
	Skeleton,
	Popconfirm,
	PageHeader,
	Result,
	Statistic,
	Descriptions,
	Empty,
	Tree,
	TreeSelect,
	Carousel,
	Pagination,
	Cascader,
	Slider,
	Transfer,
	Rate,
	Collapse,
	message,
	notification
} from 'ant-design-vue'

// 注册首屏必需组件
Vue.use(ConfigProvider)
Vue.use(Layout)
Vue.use(Input)
Vue.use(InputNumber)
Vue.use(Button)
Vue.use(Switch)
Vue.use(Radio)
Vue.use(Checkbox)
Vue.use(Select)
Vue.use(Card)
Vue.use(Form)
Vue.use(FormModel)
Vue.use(Row)
Vue.use(Col)
Vue.use(Modal)
Vue.use(Table)
Vue.use(Tabs)
Vue.use(Icon)
Vue.use(Menu)
Vue.use(Dropdown)
Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(Popover)
Vue.use(Tooltip)
Vue.use(Alert)
Vue.use(Tag)
Vue.use(Divider)
Vue.use(Spin)
Vue.use(BackTop)
Vue.use(Drawer)
Vue.use(Badge)
Vue.use(List)
Vue.use(Avatar)
Vue.use(Breadcrumb)
Vue.use(Steps)
Vue.use(Upload)
Vue.use(Progress)
Vue.use(Skeleton)
Vue.use(Popconfirm)
Vue.use(PageHeader)
Vue.use(Result)
Vue.use(Statistic)
Vue.use(Descriptions)
Vue.use(Empty)
Vue.use(Tree)
Vue.use(TreeSelect)
Vue.use(Carousel)
Vue.use(Pagination)
Vue.use(Cascader)
Vue.use(Slider)
Vue.use(Transfer)
Vue.use(Rate)
Vue.use(Collapse)

// 注册Viser图表组件
Vue.use(Viser)

// 全局方法
Vue.prototype.$message = message
Vue.prototype.$notification = notification
Vue.prototype.$info = Modal.info
Vue.prototype.$success = Modal.success
Vue.prototype.$error = Modal.error
Vue.prototype.$warning = Modal.warning
Vue.prototype.$confirm = Modal.confirm

// 所有必需组件已直接导入，无需按需加载
