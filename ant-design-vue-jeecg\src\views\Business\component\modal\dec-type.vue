<template>
    <!--业务事项-->
    <a-modal :visible='showModal'
             :maskClosable= "maskClosable"
             @cancel='showModal = !showModal'
             @ok='handleOk'
             cancel-text=''
             title='业务事项'
             width='60%'>
        <a-checkbox-group style='width: 100%' v-model='checkedValues'>
            <a-col :key='item.value' :span='6' v-for='item in options'>
                <a-checkbox :disabled='item.disabled' :key='item.value'
                            :value='item.value'>
                    {{ item.text }}
                </a-checkbox>
            </a-col>
        </a-checkbox-group>
    </a-modal>
</template>

<script>

export default {
    name   : "dec-type",
    data() {
        return {
            // value副本
            val          : '',
            decTypeval          : '',
            // show副本
            showModal    : false,

            options      : [
                {text: '税单无纸化', value: '0'},
                {text: '水运中转', value: '2'},
                {text: '担保验放', value: 'chkSurety'},// 担保验放为单独字段, 故此命名
                {text: '跨境电商海外仓', value: '4'},// 值不确定, 以后可能启用
                {text: '组合港', value: '5'},
            ],
            // 已选中的
            checkedValues: [],
            decTypeRecord:[],
            maskClosable:false,
        }
    },
    props  : {
        'value'  : {
            type   : String,
            require: true
        },
        'show'   : {
            type: Boolean,
        },
        chkSurety: {
            type: Boolean,
        },
        txtDecTypeRecord: {
            type   : Object,
        },
    },
    model  : {
        prop : 'value',
        event: 'change'
    },
    watch  : {
        value    : {
            handler(val) {
                if(!!val){

                    this.val           = val
                    this.decTypeval = this.val.replace(/[^a-zA-Z_]/g,'')

                }

            }
        },
        val      : {
            handler() {
                this.$emit('change', this.val)
            },
        },
        show     : {
            handler(val) {
                this.showModal = !this.showModal
            }
        },

        txtDecTypeRecord    : {
            handler(val) {
                this.checkedValues=[]

                this.decTypeRecord = val
                if(this.decTypeRecord.dclTrnRelFlag=='2'&&this.decTypeval=='SM'){//备案清单
                    this.checkedValues.push('2') //水运中转
                }else if(this.decTypeRecord.dclTrnRelFlag=='0'||typeof (this.decTypeRecord.dclTrnRelFlag)=="undefined"||this.decTypeRecord.dclTrnRelFlag==''){//一般报关单
                    if(this.decTypeval=='SZ'){
                        this.checkedValues.push('2')//水运中转
                    }else if(this.decTypeval=='ZB'){
                        this.checkedValues.push('1')//自主报税
                    }else if(this.decTypeval=='Z'){
                        this.checkedValues.push('3')//自报自缴
                    }else if(this.decTypeval=='ZW'){
                        this.checkedValues.push('3') //自报自缴 税单无纸化
                        this.checkedValues.push('0')
                    }else if(this.decTypeval=='ZC'){
                        this.checkedValues.push('3')//自报自缴，汇总征税  汇总征税 暂时没有
                    }else if(this.decTypeval=='SW'){
                        this.checkedValues.push('0')//税单无纸化
                    }else if(this.decTypeval=='CL'){
                      //汇总征税 暂时没有
                    }
                }
                for (let i = 0; i <  this.checkedValues.length; i++) {
                    for(let j = i+1; j <  this.checkedValues.length; j++){
                        if( this.checkedValues[i]== this.checkedValues[j]){
                            this.checkedValues.splice(j,1);　　　　　　　　　　
                            j--;
                        }
                    }
                }

                if (this.chkSurety && !~this.val.indexOf('chkSurety')) {
                    // 重复push没问题, 点击确认的时候会过滤掉
                    this.checkedValues.push('chkSurety')
                } else {
                    this.checkedValues = this.checkedValues.filter(item => item !== 'chkSurety')
                }
                // this.checkedValues = val.split(',')
            }
        },
        decTypeRecord :{
            handler(val) {

                this.$emit('txtDecTypeRecord', val)
            }
        },
        chkSurety:{
            handler(val) {

            }
        }
    },
    created() {

        this.val           = this.value
        //初始化数据
        this.checkedValues = !!this.value && this.value.split(',') || []

        if(this.txtDecTypeRecord.imSign==1){//进口报关单
            this.options=[
                {text: '税单无纸化', value: '0'},
                {text: '担保验放', value: 'chkSurety'},// 担保验放为单独字段, 故此命名
                {text: '跨境电商海外仓', value: '4'},// 值不确定, 以后可能启用
							{text: '组合港', value: '5'},
            ]
        }

    },
    methods: {
        /**
         * 确认修改
         */
        handleOk() {
            this.chkSurety

            if(!!this.checkedValues){

                if(this.decTypeRecord.dclTrnRelFlag=='2'){//备案清单
                    if(this.checkedValues.length ==1&&this.checkedValues[0] == '2'){//水运中转
                        this.decTypeval ='SM'
                    }else{
                        this.$message.warning('所选不合法')
                        return
                    }
                }else  if(this.decTypeRecord.dclTrnRelFlag=='0'||typeof (this.decTypeRecord.dclTrnRelFlag)=="undefined"){
                    console.log(this.checkedValues.indexOf('2')>-1)
                    if((this.checkedValues.indexOf('chkSurety')>-1?this.checkedValues.length ==2:this.checkedValues.length ==1)&&(this.checkedValues.indexOf('2')>-1)){//水运中转
                        this.decTypeval ='SZ'
                    // }else if( this.checkedValues.length ==1&&this.checkedValues[0] == '1'){//自主报税
                    }else if( (this.checkedValues.indexOf('chkSurety')>-1?this.checkedValues.length ==2:this.checkedValues.length ==1)&&(this.checkedValues.indexOf('1')>-1)){//自主报税
                        this.decTypeval ='ZB'
                    // }else if( this.checkedValues.length ==1&&this.checkedValues[0] == '3'){//自报自缴
                    }else if( (this.checkedValues.indexOf('chkSurety')>-1?this.checkedValues.length ==2:this.checkedValues.length ==1)&&(this.checkedValues.indexOf('3')>-1)){//自报自缴
                        this.decTypeval ='Z '
                    // }else if( this.checkedValues.length ==2&&this.checkedValues.indexOf('3')>-1&&this.checkedValues.indexOf('0')>-1){//自报自缴，税单无纸化
                    }else if( (this.checkedValues.indexOf('chkSurety')>-1?this.checkedValues.length ==3:this.checkedValues.length ==2)&&this.checkedValues.indexOf('3')>-1&&this.checkedValues.indexOf('0')>-1){//自报自缴，税单无纸化
                        this.decTypeval ='ZW'
                    // }else if( this.checkedValues.length ==1&&this.checkedValues[0] == '0'){//税单无纸化
                    }else if( (this.checkedValues.indexOf('chkSurety')>-1?this.checkedValues.length ==2:this.checkedValues.length ==1)&&(this.checkedValues.indexOf('0')>-1)){//税单无纸化
                        this.decTypeval ='SW'
                    }else if(this.checkedValues.indexOf('chkSurety')>-1&&this.checkedValues.length==1){

                    }else if(this.checkedValues.length==0){
                        this.val =  this.val.replace(/[^0-9_]/g,'')
                        this.decTypeval =''
                    }else{
                        this.$message.warning('所选不合法')
                        return
                    }
                }

                if( !!this.val){
                    this.val =  this.val.replace(/[^0-9_]/g,'')
                }
                if(this.checkedValues.indexOf('chkSurety')>-1&&this.checkedValues.length==1){
                    this.val ='  '+this.val
                }else{
                    this.val = this.decTypeval + this.val
                }

            }

            this.showModal = !this.showModal
            // 更新chkSurety
            const chkSurety = this.checkedValues.find(item => item === 'chkSurety')
            this.$emit('chkSurety', !!chkSurety)
            setTimeout(() => {
                this.$emit('keyFromPromise', "chkSurety")
            }, 20)
        },
    }
}
</script>
