package org.jeecg.modules.business.config;

import org.jeecg.modules.business.interceptor.AiInterceptor;
import org.jeecg.modules.business.interceptor.ReceiveReceInterceptor;
import org.jeecg.modules.business.interceptor.ThirdPartyAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 三方登录用拦截器
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/12/1 01:30
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private ThirdPartyAuthInterceptor thirdPartyAuthInterceptor;
    @Autowired
    private ReceiveReceInterceptor receiveReceInterceptor;
    @Autowired
    private AiInterceptor aiInterceptor;

    /**
     * Add Spring MVC lifecycle interceptors for pre- and post-processing of
     * controller method invocations and resource handler requests.
     * Interceptors can be registered to apply to all requests or be limited
     * to a subset of URL patterns.
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(thirdPartyAuthInterceptor)
                .addPathPatterns("/open-api/thirdParty/**")
                .addPathPatterns("/open-api/order/v1/importOrders")
                .excludePathPatterns("/open-api/thirdParty/v1/register") // 排除注册接口
                .excludePathPatterns("/open-api/thirdParty/v1/tokenLogin");

        // 税则等用拦截器
        registry.addInterceptor(receiveReceInterceptor)
                .addPathPatterns("/open-api/hscode/v1/basicTariffs")
                .addPathPatterns("/open-api/receive/v1/shipExportPlan")
                .addPathPatterns("/open-api/receive/v1/shipExportPlanI")
                .addPathPatterns("/open-api/receive/v1/containerCargoNode");
//                .addPathPatterns("/open-api/hscode/v1/basicTariffs");
        // AI接口拦截器
        registry.addInterceptor(aiInterceptor)
                .addPathPatterns("/open-api/ai/**");
    }
}
