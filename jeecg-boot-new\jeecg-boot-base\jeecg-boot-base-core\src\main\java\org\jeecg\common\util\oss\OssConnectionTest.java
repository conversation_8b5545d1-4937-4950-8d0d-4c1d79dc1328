package org.jeecg.common.util.oss;

import lombok.extern.slf4j.Slf4j;

/**
 * OSS连接测试工具类
 * 用于测试和验证OSS连接池修复效果
 * 
 * @author: ZHANGCHAO
 * @date: 2025/6/24
 */
@Slf4j
public class OssConnectionTest {
    
    /**
     * 测试OSS连接状态
     */
    public static void testConnection() {
        log.info("开始测试OSS连接状态...");
        
        try {
            // 检查连接状态
            boolean isConnected = OssBootUtil.isConnected();
            log.info("OSS连接状态: {}", isConnected ? "正常" : "异常");
            
            // 获取客户端信息
            if (OssBootUtil.getOssClient() != null) {
                log.info("OSS客户端实例: {}", OssBootUtil.getOssClient().getClass().getSimpleName());
                log.info("OSS配置信息 - Endpoint: {}, Bucket: {}", 
                    OssBootUtil.getEndPoint(), OssBootUtil.getBucketName());
            } else {
                log.warn("OSS客户端实例为null");
            }
            
        } catch (Exception e) {
            log.error("OSS连接测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 模拟多次调用测试连接池稳定性
     */
    public static void testConnectionPool() {
        log.info("开始测试OSS连接池稳定性...");
        
        for (int i = 1; i <= 10; i++) {
            try {
                log.info("第{}次连接测试", i);
                
                // 模拟获取连接
                boolean isConnected = OssBootUtil.isConnected();
                log.info("连接状态: {}", isConnected ? "正常" : "异常");
                
                // 短暂等待
                Thread.sleep(100);
                
            } catch (Exception e) {
                log.error("第{}次连接测试失败: {}", i, e.getMessage());
            }
        }
        
        log.info("连接池稳定性测试完成");
    }
    
    /**
     * 测试签名URL生成（之前导致连接池关闭的方法）
     */
    public static void testSignedUrl() {
        log.info("开始测试签名URL生成...");
        
        try {
            // 测试生成签名URL
            String testObjectName = "test/sample.jpg";
            long expirationSeconds = 3600; // 1小时
            
            String signedUrl = OssBootUtil.generatePresignedUrl(testObjectName, expirationSeconds);
            
            if (signedUrl != null) {
                log.info("签名URL生成成功: {}", signedUrl.substring(0, Math.min(100, signedUrl.length())) + "...");
            } else {
                log.warn("签名URL生成失败");
            }
            
            // 检查生成签名URL后连接是否还正常
            boolean isConnectedAfter = OssBootUtil.isConnected();
            log.info("生成签名URL后连接状态: {}", isConnectedAfter ? "正常" : "异常");
            
        } catch (Exception e) {
            log.error("签名URL测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 综合测试方法
     */
    public static void runAllTests() {
        log.info("=== 开始OSS连接池修复验证测试 ===");
        
        // 1. 基础连接测试
        testConnection();
        
        // 2. 连接池稳定性测试
        testConnectionPool();
        
        // 3. 签名URL测试
        testSignedUrl();
        
        log.info("=== OSS连接池修复验证测试完成 ===");
    }
}
