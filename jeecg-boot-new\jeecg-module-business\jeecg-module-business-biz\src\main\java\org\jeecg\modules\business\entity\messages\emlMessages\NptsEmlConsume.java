package org.jeecg.modules.business.entity.messages.emlMessages;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * NptsEmlConsume
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/6/24 10:31
 * @version 1.0
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class NptsEmlConsume {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "EndprdSeqno")
    private Integer endprdSeqno;

    @XmlElement(name = "EndprdGdsMtno")
    private String endprdGdsMtno;

    @XmlElement(name = "MtpckSeqno")
    private Integer mtpckSeqno;

    @XmlElement(name = "MtpckGdsMtno")
    private String mtpckGdsMtno;

    @XmlElement(name = "UcnsVerno")
    private String ucnsVerno;

    @XmlElement(name = "UcnsQty")
    private String ucnsQty;

    @XmlElement(name = "NetUseupQty")
    private BigDecimal netUseupQty;

    @XmlElement(name = "TgblLossRate")
    private BigDecimal tgblLossRate;

    @XmlElement(name = "IntgbLossRate")
    private BigDecimal intgbLossRate;

    @XmlElement(name = "UcnsDclStucd")
    private String ucnsDclStucd;

    @XmlElement(name = "ModfMarkcd")
    private String modfMarkcd;

    @XmlElement(name = "BondMtpckPrpr")
    private BigDecimal bondMtpckPrpr;

    @XmlElement(name = "EtpsExeMarkcd")
    private String etpsExeMarkcd;

    @XmlElement(name = "Rmk")
    private String rmk;

    @XmlElement(name = "GseqNo")
    private Integer gseqNo;

    @XmlElement(name = "EndprdGdecd")
    private String endprdGdecd;

    @XmlElement(name = "EndprdGdsNm")
    private String endprdGdsNm;

    @XmlElement(name = "MtpckGdecd")
    private String mtpckGdecd;

    @XmlElement(name = "MtpckGdsNm")
    private String mtpckGdsNm;

    // Getters and Setters
    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public Integer getEndprdSeqno() {
        return endprdSeqno;
    }

    public void setEndprdSeqno(Integer endprdSeqno) {
        this.endprdSeqno = endprdSeqno;
    }

    public String getEndprdGdsMtno() {
        return endprdGdsMtno;
    }

    public void setEndprdGdsMtno(String endprdGdsMtno) {
        this.endprdGdsMtno = endprdGdsMtno;
    }

    public Integer getMtpckSeqno() {
        return mtpckSeqno;
    }

    public void setMtpckSeqno(Integer mtpckSeqno) {
        this.mtpckSeqno = mtpckSeqno;
    }

    public String getMtpckGdsMtno() {
        return mtpckGdsMtno;
    }

    public void setMtpckGdsMtno(String mtpckGdsMtno) {
        this.mtpckGdsMtno = mtpckGdsMtno;
    }

    public String getUcnsVerno() {
        return ucnsVerno;
    }

    public void setUcnsVerno(String ucnsVerno) {
        this.ucnsVerno = ucnsVerno;
    }

    public String getUcnsQty() {
        return ucnsQty;
    }

    public void setUcnsQty(String ucnsQty) {
        this.ucnsQty = ucnsQty;
    }

    public BigDecimal getNetUseupQty() {
        return netUseupQty;
    }

    public void setNetUseupQty(BigDecimal netUseupQty) {
        this.netUseupQty = netUseupQty;
    }

    public BigDecimal getTgblLossRate() {
        return tgblLossRate;
    }

    public void setTgblLossRate(BigDecimal tgblLossRate) {
        this.tgblLossRate = tgblLossRate;
    }

    public BigDecimal getIntgbLossRate() {
        return intgbLossRate;
    }

    public void setIntgbLossRate(BigDecimal intgbLossRate) {
        this.intgbLossRate = intgbLossRate;
    }

    public String getUcnsDclStucd() {
        return ucnsDclStucd;
    }

    public void setUcnsDclStucd(String ucnsDclStucd) {
        this.ucnsDclStucd = ucnsDclStucd;
    }

    public String getModfMarkcd() {
        return modfMarkcd;
    }

    public void setModfMarkcd(String modfMarkcd) {
        this.modfMarkcd = modfMarkcd;
    }

    public BigDecimal getBondMtpckPrpr() {
        return bondMtpckPrpr;
    }

    public void setBondMtpckPrpr(BigDecimal bondMtpckPrpr) {
        this.bondMtpckPrpr = bondMtpckPrpr;
    }

    public String getEtpsExeMarkcd() {
        return etpsExeMarkcd;
    }

    public void setEtpsExeMarkcd(String etpsExeMarkcd) {
        this.etpsExeMarkcd = etpsExeMarkcd;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public Integer getGseqNo() {
        return gseqNo;
    }

    public void setGseqNo(Integer gseqNo) {
        this.gseqNo = gseqNo;
    }

    public String getEndprdGdecd() {
        return endprdGdecd;
    }

    public void setEndprdGdecd(String endprdGdecd) {
        this.endprdGdecd = endprdGdecd;
    }

    public String getEndprdGdsNm() {
        return endprdGdsNm;
    }

    public void setEndprdGdsNm(String endprdGdsNm) {
        this.endprdGdsNm = endprdGdsNm;
    }

    public String getMtpckGdecd() {
        return mtpckGdecd;
    }

    public void setMtpckGdecd(String mtpckGdecd) {
        this.mtpckGdecd = mtpckGdecd;
    }

    public String getMtpckGdsNm() {
        return mtpckGdsNm;
    }

    public void setMtpckGdsNm(String mtpckGdsNm) {
        this.mtpckGdsNm = mtpckGdsNm;
    }
}
