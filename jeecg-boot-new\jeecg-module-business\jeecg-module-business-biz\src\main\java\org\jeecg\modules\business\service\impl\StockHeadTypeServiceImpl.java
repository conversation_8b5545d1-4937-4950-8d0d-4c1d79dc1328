package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.dto.StockHeadTypeToInvtDTO;
import org.jeecg.modules.business.entity.paramVo.StockParamVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.util.message.FTPUtil;
import org.jeecg.modules.business.util.message.MessageFileUtil;
import org.jeecg.modules.business.util.message.SFTPUtil;
import org.jeecg.modules.business.util.message.StockTypeMessageUtil;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isEmpty;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.common.constant.CommonConstant.SFTP;

/**
 * <p>
 * 出入库单表头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Slf4j
@Service
public class StockHeadTypeServiceImpl extends ServiceImpl<StockHeadTypeMapper, StockHeadType> implements IStockHeadTypeService {
    @Autowired
    private StockGoodsTypeMapper stockGoodsTypeMapper;
    @Autowired
    private IEmsStocksFlowService emsStocksFlowService;
    @Autowired
    private IStockGoodsTypeService stockGoodsTypeService;
    @Autowired
    private StockTypeImportMapper stockTypeImportMapper;
    @Autowired
    private StoreInfoMapper storeInfoMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private IPassPortHeadService passPortHeadService;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private IAppHeadTypeService appHeadTypeIService;
    @Autowired
    private AppHeadTypeMapper appHeadTypeMapper;
    @Autowired
    private AppGoodsTypeMapper appGoodsTypeMapper;
    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendSasPath}") // /ImpPath/Sas/OutBox
//    private String remoteSendSasPath;
@Autowired
private FtpProperties ftpProperties;

    /**
     * 查询分页列表
     *
     * @param page          分页对象
     * @param stockHeadType 货物清单类型
     * @param request       请求对象
     * @return 分页列表
     */
    @Override
    public IPage<StockHeadType> queryPageList(Page<StockHeadType> page, StockHeadType stockHeadType, HttpServletRequest request) {
//        IPage<StockHeadType> stockHeadTypeIPage = baseMapper.queryPageList(page, stockHeadType);
        QueryWrapper<StockHeadType> queryWrapper = setQueryWrapper(stockHeadType);
        IPage<StockHeadType> stockHeadTypeIPage = baseMapper.queryPageList(page, queryWrapper);
        return stockHeadTypeIPage;
    }

    /**
     * 根据 id 查询(申报表、出入库单、核注单) 并反填统一编号
     *
     * @param id
     * @param seqNo
     * @param type
     * @return
     * @apiNote <pre>
     *   功能简介
     * </pre>
     * <AUTHOR> 2022/9/14 10:00
     * @version 1.0
     */
    @Override
    public Result<String> queryAndSetSeqNo(String id, String seqNo, String type) {
        if (isBlank(id)){
            return Result.error("[queryAndSetSeqNo]流水号为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if ("SAS002".equals(type)){
            StockHeadType stockHeadType = this.getById(id);
            if (stockHeadType != null){
                boolean result = this.update(new UpdateWrapper<StockHeadType>().lambda()
                        .set(StockHeadType::getSeqNo,seqNo).set(StockHeadType::getDecStatus,"0").eq(StockHeadType::getId,id));
                return result ? Result.ok(String.format("更新出入库单统一编号成功，seqNo:%s,流水号:%s",seqNo,id))
                        : Result.error(String.format("更新出入库单统一编号失败，seqNo:%s,流水号:%s",seqNo,id));
            }
        }else if ("SAS001".equals(type)){
            boolean result = appHeadTypeIService.update(new UpdateWrapper<AppHeadType>().lambda()
                    .set(isNotBlank(seqNo),AppHeadType::getSeqNo,seqNo).set(AppHeadType::getAppHeadStatus,"0").eq(AppHeadType::getId,id));
            return result ? Result.ok(String.format("更新业务申报表统一编号/状态成功，seqNo:%s,流水号:%s",seqNo,id))
                    : Result.error(String.format("更新业务申报表统一编号/状态失败，seqNo:%s,流水号:%s",seqNo,id));
        } else if ("SAS003".equals(type)) {
            NemsInvtHead invtHead = nemsInvtHeadService.getById(id);
            if (invtHead != null){
                nemsInvtHeadService.update(new UpdateWrapper<NemsInvtHead>().lambda().set(NemsInvtHead::getSeqNo,seqNo).eq(NemsInvtHead::getId,id));
                return Result.ok(String.format("更新核注单统一编号成功，seqNo:%s,流水号:%s",seqNo,id));
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.error("[queryAndSetSeqNo]未找到对应单据");
    }

    /**
     * 根据id查询货物清单类型
     *
     * @param id 货物清单类型id
     * @return 货物清单类型
     */
    @Override
    public Result<?> getStock(String id) {
        StockHeadType stockHeadType = baseMapper.selectById(id);
        LambdaQueryWrapper lambdaQueryWrapper = new QueryWrapper<StockGoodsType>().lambda().eq(StockGoodsType::getStockId, stockHeadType.getId());
        List<StockGoodsType> list = stockGoodsTypeMapper.selectList(lambdaQueryWrapper);
        stockHeadType.setStockGoodsTypeList(list);
        return Result.ok(stockHeadType);
    }

    /**
     * 保存出入库单
     *
     * @param stockHeadType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStockHeadType(StockHeadType stockHeadType) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isEmpty(stockHeadType.getStockGoodsTypeList())) {
            return Result.error("保存失败！出入库单商品数据不能为空！");
        }
        // 正常执行的业务申报表
        AppHeadType appHeadType = appHeadTypeMapper.selectOne(new LambdaQueryWrapper<AppHeadType>()
                .eq(AppHeadType::getDclTbStucd, "1"));
        if (isEmpty(appHeadType)) {
            return Result.error("未查询到正常执行状态的业务申报表信息！");
        }
        List<AppGoodsType> appGoodsTypeList = appGoodsTypeMapper.selectList(new LambdaQueryWrapper<AppGoodsType>()
                .eq(AppGoodsType::getHeadId, appHeadType.getId()));
        if (isEmpty(appGoodsTypeList)) {
            return Result.error("未查询到正常执行状态的业务申报表表体信息！");
        }
        Map<String, List<AppGoodsType>> appGoodsTypeMap = appGoodsTypeList.stream()
                .collect(Collectors.groupingBy(i -> isNotEmpty(i.getOriactGdsSeqno()) ? i.getOriactGdsSeqno().toString() : "-1"));
        for (StockGoodsType stockGoodsType : stockHeadType.getStockGoodsTypeList()) {
            if (!appGoodsTypeMap.containsKey(stockGoodsType.getOriactGdsSeqno())) {
                return Result.error("商品序号[" + stockGoodsType.getSasStockSeqno() + "]的表体在业务申报表中不存在！");
            } else {
                AppGoodsType appGoodsType = appGoodsTypeMap.get(stockGoodsType.getOriactGdsSeqno()).get(0);
                stockGoodsType.setSasDclSeqno(appGoodsType.getSasDclSeqno());
            }
        }

        String type = "";
        if (I.equals(stockHeadType.getStockTypecd())) {
            // 一线入可以没有关联出区编号
            if (isBlank(stockHeadType.getRltSasStockNo())) {
                type = "1I";
            } else {
                type = "2I"; // 回仓单，二线
            }
        }
        if ("2I".equals(type)) {
            StockHeadType stockHeadType_E = baseMapper.selectOne(new LambdaQueryWrapper<StockHeadType>()
                    .eq(StockHeadType::getSasStockNo, stockHeadType.getRltSasStockNo()));
            if (isEmpty(stockHeadType_E)) {
                return Result.error("保存失败！系统中不存在编号为[" + stockHeadType.getRltSasStockNo() + "]的出区出入库单！");
            }
            stockHeadType.setRltWarehousingId(stockHeadType_E.getId());
            List<StockGoodsType> stockGoodsTypeList_E = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                    .eq(StockGoodsType::getStockId, stockHeadType_E.getId()));
            if (isNotEmpty(stockGoodsTypeList_E)) {
                Map<String, List<StockGoodsType>> map = stockGoodsTypeList_E.stream().collect(Collectors.groupingBy(i -> i.getSasDclSeqno() + i.getOriactGdsSeqno()));
                if (isNotEmpty(stockHeadType.getStockGoodsTypeList())) {
                    for (StockGoodsType stockGoodsType : stockHeadType.getStockGoodsTypeList()) {
                        if (isEmpty(map.get(stockGoodsType.getSasDclSeqno() + stockGoodsType.getOriactGdsSeqno()))) {
                            return Result.error("保存失败！表体商品[" + stockGoodsType.getSasDclSeqno() + "-" + stockGoodsType.getOriactGdsSeqno() + "]在关联的出区出入库单中不存在！");
                        } else {
                            BigDecimal dclQtyI = isNotEmpty(stockGoodsType.getDclQty()) ? stockGoodsType.getDclQty() : BigDecimal.ZERO;
                            BigDecimal dclQtyE = isNotEmpty(map.get(stockGoodsType.getSasDclSeqno() + stockGoodsType.getOriactGdsSeqno()).get(0).getDclQty()) ? map.get(stockGoodsType.getSasDclSeqno() + stockGoodsType.getOriactGdsSeqno()).get(0).getDclQty() : BigDecimal.ZERO;
                            if (dclQtyI.compareTo(dclQtyE) > 0) {
                                return Result.error("保存失败！表体商品[" + stockGoodsType.getSasDclSeqno() + "-" + stockGoodsType.getOriactGdsSeqno() + "]的申报数量必须小于或等于其关联的出区出入库单中对应的申报数量！");
                            }
                        }
                    }
                } else {
                    return Result.error("保存失败！出入库单商品数据不能为空！");
                }
            }
        }
        if (isEmpty(stockHeadType.getId())) {
            String sasStockNo = serialNumberService.getSerialnumberByCustomerCode("X4301E", 4);
            stockHeadType.setSasStockNo(sasStockNo);
            stockHeadType.setCreatePerson(sysUser.getUsername());
            stockHeadType.setCreateDate(new Date());
            stockHeadType.setCreatePassPort(false);
            baseMapper.insert(stockHeadType);
        } else {
            if ("B".equals(stockHeadType.getDecStatus())){
                return Result.error("海关终审通过后不允许修改");
            }
            stockHeadType.setUpdateBy(sysUser.getUsername());
            stockHeadType.setUpdateDate(new Date());
            baseMapper.updateById(stockHeadType);
        }
        StockHeadTypeServiceImpl currentProxy = (StockHeadTypeServiceImpl) AopContext.currentProxy(); // 获取代理对象
        if (isNotEmpty(stockHeadType.getStockGoodsTypeList())) {
            List<StockGoodsType> oldStockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                    .eq(StockGoodsType::getStockId, stockHeadType.getId()));
            if (isNotEmpty(oldStockGoodsTypeList)) {
                // 2024/3/13 9:56@ZHANGCHAO 追加/变更/完善：出入库单发送报文后，不可再修改申报数量！！
                if ("1".equals(stockHeadType.getSend())) {
                    for (StockGoodsType stockGoods : stockHeadType.getStockGoodsTypeList()) {
                        if (isEmpty(stockGoods.getId())) {
                            continue;
                        }
                        for (StockGoodsType oldGoods : oldStockGoodsTypeList) {
                            if (stockGoods.getId().equals(oldGoods.getId())) {
                                BigDecimal oldQty = isNotEmpty(oldGoods.getDclQty()) ? oldGoods.getDclQty() : BigDecimal.ZERO;
                                BigDecimal newQty = isNotEmpty(stockGoods.getDclQty()) ? stockGoods.getDclQty() : BigDecimal.ZERO;
                                if (oldQty.compareTo(newQty) != 0) {
                                    throw new RuntimeException("出入库单已发送报文，无法再修改申报数量！商品序号[" + stockGoods.getSasStockSeqno() + "]原申报数量[" + oldGoods.getDclQty() + "]");
                                }
                            }
                        }
                    }
                }
                // 使用 Stream 进行过滤
                List<StockGoodsType> dels = oldStockGoodsTypeList.stream()
                        .filter(item -> stockHeadType.getStockGoodsTypeList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (StockGoodsType stockGoodsType : dels) {
                        // 解除占用
                        Result<?> result = currentProxy.handleOccupy(stockHeadType, stockGoodsType, false);
                        if (!result.isSuccess()) {
                            throw new RuntimeException(result.getMessage());
                        }
                        stockGoodsTypeService.removeById(stockGoodsType.getId());
                    }
                }
            }
            for (StockGoodsType stockGoodsType : stockHeadType.getStockGoodsTypeList()) {
                if (isEmpty(stockGoodsType.getId())) {
                    stockGoodsType.setStockId(stockHeadType.getId());
                    stockGoodsType.setSasStockNo(stockHeadType.getSasStockNo());
                    stockGoodsTypeService.save(stockGoodsType);
                } else {
                    stockGoodsTypeService.updateById(stockGoodsType);
                }
            }
            // 针对全部删除的情况
        } else {
            List<StockGoodsType> oldStockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                    .eq(StockGoodsType::getStockId, stockHeadType.getId()));
            if (isNotEmpty(oldStockGoodsTypeList)) {
                for (StockGoodsType stockGoodsType : oldStockGoodsTypeList) {
                    // 解除占用
                    Result<?> result = currentProxy.handleOccupy(stockHeadType, stockGoodsType, false);
                    if (!result.isSuccess()) {
                        throw new RuntimeException(result.getMessage());
                    }
                }
            }
        }
        // 产品项目没有出入库清单模块，不需要
//        //处理出入库清单状态
//        if ("I".equals(stockHeadType.getStockTypecd())){
//            //用于回退出入库清单中的
//            Map<String,BigDecimal> mrNoAndMcQtyMap = new HashMap<>();
//            List<StockGoodsType> goodsTypeList = iStockGoodsTypeService.list(new QueryWrapper<StockGoodsType>().lambda()
//                    .eq(StockGoodsType::getStockId,stockHeadType.getId()));
//            if (goodsTypeList != null && !goodsTypeList.isEmpty()){
//                goodsTypeList.forEach(v->{
//                    BigDecimal mcQty =v.getDclQty();
//                    if (mrNoAndMcQtyMap.containsKey(v.getMrNo())){
//                        mcQty = mrNoAndMcQtyMap.get(v.getMrNo()).add(v.getDclQty());
//                    }
//                    mrNoAndMcQtyMap.put(v.getMrNo(),mcQty);
//                });
//                mrNoAndMcQtyMap.forEach((k,v)->{
//                    stockTypeImportService.update(new UpdateWrapper<StockTypeImport>().lambda()
//                            .set(StockTypeImport::getHadWarehouseReceipt,"0")
//                            .set(StockTypeImport::getSurplusMcQty,v)
//                            .eq(StockTypeImport::getMrNo,k));
//                });
//            }
//        }else {
//            List<StockGoodsType> goodsTypeList = iStockGoodsTypeService.list(new QueryWrapper<StockGoodsType>().lambda()
//                    .eq(StockGoodsType::getStockId,stockHeadType.getId()));
//            if (goodsTypeList != null && !goodsTypeList.isEmpty()){
//                List<String> mrNos = goodsTypeList.stream().map(StockGoodsType::getMrNo).collect(Collectors.toList());
//                stockTypeImportMapper.update(null,new UpdateWrapper<StockTypeImport>().lambda()
//                        .set(StockTypeImport::getGenerateStockType,"0").in(StockTypeImport::getMrNo,mrNos));
//            }
//        }
//        if(stockHeadType.getStockGoodsTypeList() != null && !stockHeadType.getStockGoodsTypeList().isEmpty()){
//            /*
//            获取原出库单和入库单，用于计算原始的占用信息
//             */
//            //原出库单
//            StockHeadType oldHeadType = new StockHeadType();
//            //原入库单Map
//            Map<String, List<StockGoodsType>> oldEnterStockGoodsMap = new HashMap<>();
//
//            //出库单
//            StockHeadType stockHeadType_E = new StockHeadType();
//            //处理回仓入库单
//            Map<String, List<StockGoodsType>> enterStockGoodsMap = new HashMap<>();
//            // todo 简化！！！！！！
//            if ("I".equals(stockHeadType.getStockTypecd()) && isNotEmpty(stockHeadType.getRltWarehousingId())){//修改数据为入库单时
////                List<String> mrNos = stockHeadType.getStockGoodsTypeList().stream().map(StockGoodsType::getMrNo).collect(Collectors.toList());
////                stockTypeImportMapper.update(null,new UpdateWrapper<StockTypeImport>().lambda()
////                        .set(StockTypeImport::getHadWarehouseReceipt,"1")
////                        .set(StockTypeImport::getSurplusMcQty,0)
////                        .in(StockTypeImport::getMrNo,mrNos));
//                stockHeadType.getStockGoodsTypeList().forEach(v -> {
//                    String key = stockHeadType.getRltWarehousingId().toString() + v.getRltGdsSeqno().toString();
//                    if(enterStockGoodsMap.containsKey(key)){
//                        enterStockGoodsMap.get(key).add(v);
//                    }else {
//                        List<StockGoodsType> goodsTypeList = new ArrayList<>();
//                        goodsTypeList.add(v);
//                        enterStockGoodsMap.put(key, goodsTypeList);
//                    }
//                });
//                stockHeadType_E = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda().eq(StockHeadType::getId,stockHeadType.getRltWarehousingId()));
//                List<StockGoodsType> stockGoodsTypeList_E = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
//                        .eq(StockGoodsType::getStockId,stockHeadType.getRltWarehousingId()));
//                stockHeadType_E.setStockGoodsTypeList(stockGoodsTypeList_E);
//
//                //获取原出库单信息，用于占用处理
//                BeanUtils.copyProperties(stockHeadType_E,oldHeadType);
//                oldHeadType.setStockGoodsTypeList(new ArrayList<>());
//                for(StockGoodsType v: stockGoodsTypeList_E){
//                    StockGoodsType goods = new StockGoodsType();
//                    BeanUtils.copyProperties(v,goods);
//                    oldHeadType.getStockGoodsTypeList().add(goods);
//                }
//
//                //获取原入库单信息
//                List<String> rltWarehousingIds = new ArrayList<>();
//                rltWarehousingIds.add(stockHeadType.getRltWarehousingId().toString());
//                List<StockGoodsType> enterStockHeads = stockGoodsTypeMapper.listStockGoodsByRltWarehousingId(rltWarehousingIds);
//                enterStockHeads.forEach(v -> {
//                    String key = v.getRltWarehousingId().toString() + v.getRltGdsSeqno().toString();
//                    if (!v.getStockId().toString().equals(stockHeadType.getId().toString())){
//                        if(enterStockGoodsMap.containsKey(key)){
//                            enterStockGoodsMap.get(key).add(v);
//                        }else {
//                            List<StockGoodsType> goodsTypeList = new ArrayList<>();
//                            goodsTypeList.add(v);
//                            enterStockGoodsMap.put(key, goodsTypeList);
//                        }
//                    }
//                    if(oldEnterStockGoodsMap.containsKey(key)){
//                        oldEnterStockGoodsMap.get(key).add(v);
//                    }else {
//                        List<StockGoodsType> goodsTypeList = new ArrayList<>();
//                        goodsTypeList.add(v);
//                        oldEnterStockGoodsMap.put(key, goodsTypeList);
//                    }
//                });
//
//            }else {//修改数据为出库单时
////                List<String> mrNos = stockHeadType.getStockGoodsTypeList().stream().map(StockGoodsType::getMrNo).collect(Collectors.toList());
////                stockTypeImportMapper.update(null,new UpdateWrapper<StockTypeImport>().lambda()
////                        .set(StockTypeImport::getGenerateStockType,"1")
////                        .in(StockTypeImport::getMrNo,mrNos));
//                BeanUtils.copyProperties(stockHeadType,stockHeadType_E);
//                stockHeadType_E.setStockGoodsTypeList(new ArrayList<>());
//                for(StockGoodsType v: stockHeadType.getStockGoodsTypeList()){
//                    StockGoodsType goods = new StockGoodsType();
//                    BeanUtils.copyProperties(v,goods);
//                    stockHeadType_E.getStockGoodsTypeList().add(goods);
//                }
//
//                List<String> rltWarehousingIds = new ArrayList<>();
//                rltWarehousingIds.add(stockHeadType.getId().toString());
//                List<StockGoodsType> enterStockHeads = stockGoodsTypeMapper.listStockGoodsByRltWarehousingId(rltWarehousingIds);
//                enterStockHeads.forEach(v -> {
//                    String key = v.getRltWarehousingId().toString() + v.getRltGdsSeqno().toString();
//                    if(enterStockGoodsMap.containsKey(key)){
//                        enterStockGoodsMap.get(key).add(v);
//                    }else {
//                        List<StockGoodsType> goodsTypeList = new ArrayList<>();
//                        goodsTypeList.add(v);
//                        enterStockGoodsMap.put(key, goodsTypeList);
//                    }
//                });
//
//                //获取原出库单信息，用于占用处理
//                oldHeadType = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda().eq(StockHeadType::getId,stockHeadType.getId()));
//                List<StockGoodsType> stockGoodsTypeList_E = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
//                        .eq(StockGoodsType::getStockId,stockHeadType.getId()));
//                oldHeadType.setStockGoodsTypeList(stockGoodsTypeList_E);
//                //获取原入库单信息
//                enterStockHeads.forEach(v -> {
//                    String key = v.getRltWarehousingId().toString() + v.getRltGdsSeqno().toString();
//                    if(oldEnterStockGoodsMap.containsKey(key)){
//                        oldEnterStockGoodsMap.get(key).add(v);
//                    }else {
//                        List<StockGoodsType> goodsTypeList = new ArrayList<>();
//                        goodsTypeList.add(v);
//                        oldEnterStockGoodsMap.put(key, goodsTypeList);
//                    }
//                });
//            }
//
//            //更新数据
//            saveOrUpdate(stockHeadType);
//            if (null != stockHeadType.getStockGoodsTypeList()) {
//                stockHeadType.getStockGoodsTypeList().forEach(v -> {
//                    v.setId(IdWorker.getId());
//                    v.setStockId((stockHeadType.getId()));
//                });
//                LambdaQueryWrapper<StockGoodsType> queryWrapper = new LambdaQueryWrapper();
//                queryWrapper.eq(StockGoodsType::getStockId, stockHeadType.getId());
//                // 批量更新
//                stockGoodsTypeService.remove(queryWrapper);
//                stockGoodsTypeService.saveBatch(stockHeadType.getStockGoodsTypeList());
//            }
//
//            //处理回仓数量
//            handleReturnWarehouse(stockHeadType_E, enterStockGoodsMap);
//            //获取原始占用数量
//            handleReturnWarehouse(oldHeadType, oldEnterStockGoodsMap);
//            //判断数量是否充足
//            for(StockGoodsType stockGoodsType : stockHeadType_E.getStockGoodsTypeList()){
//                if (stockGoodsType.getDclQty().compareTo(BigDecimal.ZERO)<0){
//                    throw new RuntimeException("回仓数量大于出库数量，保存失败");
//                }
//            }
//            //原始占用数量集合
//            Map<String, BigDecimal> oldExitDclQtyMap = new HashMap<>();
//            oldHeadType.getStockGoodsTypeList().forEach(v-> {
////                String key = new StringBuilder(String.valueOf(v.getStockId())).append(v.getSasStockSeqno()).toString();
//                String key = v.getId().toString();
//                oldExitDclQtyMap.put(key,v.getDclQty());
//            });
//            /*
//            判断修改后的数量是否充足
//             */
//            List<String> gnos = new ArrayList<>();
//            stockHeadType_E.getStockGoodsTypeList().forEach(v->{
//                if (v.getDclQty().compareTo(BigDecimal.ZERO)>0){
//                    if (isNotBlank(v.getOriactGdsSeqno())) {
//                        gnos.add(v.getOriactGdsSeqno().toString());
//                    }
//                }
//            });
//            if (!gnos.isEmpty()){
//                EmsQueryDto emsQueryDto = new EmsQueryDto();
//                emsQueryDto.setEmsNo(stockHeadType_E.getAreainOriactNo());
//                emsQueryDto.setGNoList(gnos);
//                List<PtsEmsAimg> emsAimgList = emsAimgService.searchAimg(emsQueryDto);
//                if (isNotEmpty(emsAimgList)) {
//                    Map<Integer, PtsEmsAimg> emsAimgMap = emsAimgList.stream().collect(Collectors.toMap(PtsEmsAimg::getGNo, a -> a, (k1, k2) -> k1));
//
//                    List<Integer> qtyInsufficients = new ArrayList<>();
//                    List<Integer> nonExistents = new ArrayList<>();
//                    stockHeadType_E.getStockGoodsTypeList().forEach(v->{
//                        if (v.getDclQty().compareTo(BigDecimal.ZERO)<=0){
//                            return;
//                        }
//                        if (emsAimgMap.containsKey(v.getOriactGdsSeqno())){
////                            String key = new StringBuilder(String.valueOf(v.getStockId())).append(v.getSasStockSeqno()).toString();
//                            BigDecimal oldExitDclQty = oldExitDclQtyMap.get(v.getId().toString());
//                            BigDecimal availableQty = emsAimgMap.get(v.getOriactGdsSeqno()).getStockQty()
//                                    .subtract(emsAimgMap.get(v.getOriactGdsSeqno()).getOccupyQty() != null ? emsAimgMap.get(v.getOriactGdsSeqno()).getOccupyQty() : BigDecimal.ZERO)
//                                    .add(oldExitDclQty != null ? oldExitDclQty : BigDecimal.ZERO);
//                            if (availableQty.compareTo(v.getDclQty())<0){
//                                qtyInsufficients.add(isNotBlank(v.getOriactGdsSeqno()) ? Integer.valueOf(v.getOriactGdsSeqno()) : null);
//                            }
//                        }else {
//                            nonExistents.add(isNotBlank(v.getOriactGdsSeqno()) ? Integer.valueOf(v.getOriactGdsSeqno()) : null);
//                        }
//                    });
//                    String resultError= "";
//                    if (qtyInsufficients.size()>0){
//                        resultError = new StringBuilder("数量不足的备案序号为：").append(JSONObject.toJSONString(qtyInsufficients)).toString();
//                    }
//                    if (nonExistents.size()>0){
//                        resultError = new StringBuilder(ObjectUtil.isEmpty(resultError) ? "未在账册料件找到的备案序号：" : resultError+",未在账册料件找到的备案序号：").append(JSONObject.toJSONString(nonExistents)).toString();
//                    }
//                    if (isNotEmpty(resultError)){
//                        throw new RuntimeException(resultError);
//                    }
//                }
//
//                Result<StockHeadType> stockHeadTypeYmMsg = warehouseOutOccupyAimg(stockHeadType_E,stockHeadType_E.getStockGoodsTypeList());
//                if (!stockHeadTypeYmMsg.isSuccess()){
//                    return stockHeadTypeYmMsg;
//                }
////                stockHeadType_E.setEmsFlowsId(stockHeadTypeYmMsg.getData().getEmsFlowsId());//返回ems记录ID
////                baseMapper.updateById(stockHeadType_E);
//            } else {
//                this.rectifyQtyForStockTypeId(stockHeadType_E.getId().toString());
//            }
//        } else {
//            this.rectifyQtyForStockTypeId(stockHeadType.getId().toString());
//        }
        StockHeadType resultStockHeadType = baseMapper.selectById(stockHeadType.getId());
        resultStockHeadType.setStockGoodsTypeList(stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                .eq(StockGoodsType::getStockId, stockHeadType.getId())));
        return Result.ok(resultStockHeadType);
    }

    /**
     * 根据股票类型生成护照。
     *
     * @param ids 股票类型的ID，用字符串表示。这个参数指定了需要生成护照的股票类型。
     * @return 返回一个Result对象，该对象包含了操作的结果。具体的返回值类型依赖于实际的业务逻辑。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> generatePassPortByStockType(String ids) {
        List<StockHeadType> stockHeadTypes = baseMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
                .in(StockHeadType::getId,Arrays.asList(ids.split(",")))
                .eq(StockHeadType::getCreatePassPort,"0"));
        if (stockHeadTypes == null || stockHeadTypes.isEmpty()){
            return Result.error("未找到对应出入库单,请核实数据");
        }
        List<PassPortHead> passPortHeads = new ArrayList<>();
        stockHeadTypes.forEach(v->{
            PassPortHead passPortHead = new PassPortHead();
            createPassPortHeadByStockHead(passPortHead,v);
            passPortHeads.add(passPortHead);
        });
        boolean result = passPortHeadService.saveBatch(passPortHeads);
        if (result){
            baseMapper.update(null,new UpdateWrapper<StockHeadType>().lambda()
                    .set(StockHeadType::getCreatePassPort,"1")
                    .in(StockHeadType::getId,Arrays.asList(ids.split(","))));
        }
        return result ? Result.ok("生成成功") : Result.error("生成失败，请联系管理员");
    }

    /**
     * 批量发送消息。
     *
     * @param ids 以字符串形式表示的消息ID列表，多个ID之间通常使用逗号分隔。
     * @return 返回一个Result对象，其中包含了操作的结果信息，例如成功与否、错误码等。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> sendMessageBatch(String ids) {
        // 2024/3/8 18:02@ZHANGCHAO 追加/变更/完善：发送之前先处理占用！！
        List<StockHeadType> stockHeadTypes = baseMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
                .in(StockHeadType::getId,Arrays.asList(ids.split(","))));
        if (stockHeadTypes == null || stockHeadTypes.isEmpty()){
            return Result.error("未找到对应出入库单,请核实数据");
        }
        for (StockHeadType stockHeadType : stockHeadTypes) {
            if ("1".equals(stockHeadType.getDclcusFlag())) {
                return Result.error("存在已经集中申报过的出入库单，请重新选择！");
            }
        }
        StockHeadTypeServiceImpl currentProxy = (StockHeadTypeServiceImpl) AopContext.currentProxy(); // 获取代理对象
        // 只有出区类型的才走占用逻辑
        List<StockHeadType> stockHeadTypesE = stockHeadTypes.stream()
                .filter(i -> E.equals(i.getStockTypecd())).collect(Collectors.toList());
        if (isNotEmpty(stockHeadTypesE)) {
            for (StockHeadType stockHeadType : stockHeadTypesE) {
                // 2024/3/13 9:20@ZHANGCHAO 追加/变更/完善：只有第一次发送时，才走占用逻辑！！
                if ("1".equals(stockHeadType.getSend())) {
                    continue;
                }
                List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                        .eq(StockGoodsType::getStockId, stockHeadType.getId()));
                Result<?> result = currentProxy.handleOccupyBatch(stockHeadType, stockGoodsTypeList, true);
                if (result.isSuccess()) {
                    log.info("出入库单出区[" + stockHeadType.getSasStockNo() + "]的表体占用成功：" + result.getResult());
                } else {
                    throw new RuntimeException("占用失败：" + result.getMessage());
                }
            }
        }
        // 2024/3/13 15:16@ZHANGCHAO 追加/变更/完善：入区的回仓单也走占用逻辑！！以下逻辑可以与上面合并，暂不处理！！！！！
        List<StockHeadType> stockHeadTypesI = stockHeadTypes.stream()
                .filter(i -> I.equals(i.getStockTypecd()) && isNotBlank(i.getRltSasStockNo())).collect(Collectors.toList());
        if (isNotEmpty(stockHeadTypesI)) {
            for (StockHeadType stockHeadType : stockHeadTypesI) {
                // 2024/3/13 9:20@ZHANGCHAO 追加/变更/完善：只有第一次发送时，才走占用逻辑！！
                if ("1".equals(stockHeadType.getSend())) {
                    continue;
                }
                List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                        .eq(StockGoodsType::getStockId, stockHeadType.getId()));
                Result<?> result = currentProxy.handleOccupyBatch(stockHeadType, stockGoodsTypeList, true);
                if (result.isSuccess()) {
                    log.info("出入库单入区[" + stockHeadType.getSasStockNo() + "]的表体占用成功：" + result.getResult());
                } else {
                    throw new RuntimeException("占用失败：" + result.getMessage());
                }
            }
        }

        // 2024/3/8 18:02@ZHANGCHAO 追加/变更/完善：发送之前先处理占用！！
        // 防止事务失效！！
//        StockHeadTypeServiceImpl currentProxy = (StockHeadTypeServiceImpl) AopContext.currentProxy(); // 获取代理对象
//        Result<?> result = currentProxy.handleOccupy(ids);
//        if (!result.isSuccess()) {
//            return result;
//        }
        String errorId = "";
        for (String id : ids.split(",")) {
            Result<StockHeadType> stockHeadYmMsg = this.sendMessageById(id);
            if (stockHeadYmMsg.getCode() != 200) {
                if (isBlank(errorId)) {
                    errorId = new StringBuilder().append(stockHeadYmMsg.getMessage()).append(",出入库单流水号：").append(id).toString();
                } else {
                    errorId = new StringBuilder().append(errorId).append(";").append(stockHeadYmMsg.getMessage()).append(",出入库单流水号：").append(id).toString();
                }

            }
        }
        if (isNotBlank(errorId)) {
            return Result.error(errorId);
        }
        return Result.ok("发送成功");
    }

    /**
     * 根据统一编号更新出入库单编号
     *
     * @param sasStockNo
     * @param seqNo
     * @param stockStatus
     * @return
     */
    @Override
    public Result<StockHeadType> updateSasStockNoBySeqNo(String sasStockNo, String seqNo, String stockStatus) {
        StockHeadType stockHeadType = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda().eq(StockHeadType::getSeqNo,seqNo));
        if (stockHeadType == null){
            return Result.error("未根据统一编号查询到对应的出入库单，统一编号："+seqNo);
        }
        stockHeadType.setSasStockNo(sasStockNo);
        if (isNotEmpty(stockStatus) && !"B".equals(stockHeadType.getDecStatus())) {
            stockHeadType.setDecStatus(
                    isNotEmpty(stockHeadType.getDecStatus()) && stockHeadType.getDecStatus().compareTo(stockStatus) > 0
                            ? stockHeadType.getDecStatus() : stockStatus);
        }
        this.updateById(stockHeadType);
        if ("E".equals(stockHeadType.getStockTypecd())){
            StockHeadType stockHeadType_I = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda().eq(StockHeadType::getRltWarehousingId,stockHeadType.getId()));
            if (stockHeadType_I != null){
                stockHeadType_I.setRltSasStockNo(sasStockNo);
                this.updateById(stockHeadType_I);
            }
        }
        return Result.ok("更新成功");
    }

    /**
     * @param stockHeadType
     * @return
     */
    @Override
    public Result<StockHeadType> saveStockHeadTypeForReceipt(StockHeadType stockHeadType) {
        if (stockHeadType == null){
            return Result.error("回执更新出入库单时传入信息为空");
        }
        if (isEmpty(stockHeadType.getSeqNo())){
            return Result.error("回执更新出入库单时传入统一编号为空");
        }
        StockHeadType headType = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda()
                .eq(StockHeadType::getSeqNo,stockHeadType.getSeqNo()));
        if (headType == null){
            return Result.error("回执更新出入库单时未找到本地出入库单");
        }

// 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        stockHeadType.setId(headType.getId());
        stockHeadType.setRltWarehousingId(headType.getRltWarehousingId());
        stockHeadType.setHsType(headType.getHsType());
        stockHeadType.setTaxSign(headType.getTaxSign());
        stockHeadType.setEmsFlowsId(headType.getEmsFlowsId());
        stockHeadType.setSend(headType.getSend());
        stockHeadType.setImportId(headType.getImportId());
        stockHeadType.setCreateDate(headType.getCreateDate());
        stockHeadType.setIssueDateTime(headType.getIssueDateTime());
        stockHeadType.setDclcusFlag(headType.getDclcusFlag());
        stockHeadType.setDclTenantId(headType.getDclTenantId());
        stockHeadType.setUpdateBy(headType.getUpdateBy());
        stockHeadType.setUpdateDate(headType.getUpdateDate());
        stockHeadType.setAircraftType(headType.getAircraftType());
        stockHeadType.setTransMode(headType.getTransMode());
        stockHeadType.setAircraftRegistrationNumber(headType.getAircraftRegistrationNumber());
        stockHeadType.setOwner(headType.getOwner());
        stockHeadType.setTradeCountry(headType.getTradeCountry());
        stockHeadType.setAircraftEntryNo(headType.getAircraftEntryNo());
        stockHeadType.setInvtHeadId(headType.getInvtHeadId());
        stockHeadType.setSpecialMark(headType.getSpecialMark());
        stockHeadType.setCreatePassPort(headType.getCreatePassPort());
        stockHeadType.setTenantId(headType.getTenantId());
        stockHeadType.setAreainOriactNo(headType.getAreainOriactNo());

        stockHeadType.setDecStatus("B");

        List<StockGoodsType> goodsTypeList = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
                .eq(StockGoodsType::getStockId,headType.getId()));
        if (goodsTypeList != null && !goodsTypeList.isEmpty() && stockHeadType.getStockGoodsTypeList()!=null
                && !stockHeadType.getStockGoodsTypeList().isEmpty()){
            Map<Integer,StockGoodsType> goodsTypeMap = goodsTypeList.stream()
                    .collect(Collectors.toMap(StockGoodsType::getSasStockSeqno,a->a,(k1,k2)->k1));

            stockHeadType.getStockGoodsTypeList().forEach(v->{
                if (goodsTypeMap.containsKey(v.getSasStockSeqno())){
                    StockGoodsType goodsType = goodsTypeMap.get(v.getSasStockSeqno());
                    v.setId(goodsType.getId());
                    v.setStockId(goodsType.getStockId());
                    v.setSequence(goodsType.getSequence());
                    v.setStockTypeId(goodsType.getStockTypeId());
                    v.setHsType(goodsType.getHsType());
                    v.setCiqName(goodsType.getCiqName());
                    v.setCiqCode(goodsType.getCiqCode());
                    v.setInvtListId(goodsType.getInvtListId());
                    v.setSupvModecd(goodsType.getSupvModecd());
                    //终审通过回填出入库清单数据，20231026 by zhengls
                    // 申报数量，申报单位，申报单价，申报总价，币制，出库单编号，入库单编号，出 入库申报日期
                    stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
                            .set(StockTypeImport::getDeclaredQty,v.getDclQty())
                            .set(StockTypeImport::getDeclarationUnit,v.getDclUnitcd())
                            .set(StockTypeImport::getDeclaredPrice,v.getDclUprcAmt())
                            .set(StockTypeImport::getDeclaredTotal,v.getDclTotalAmt())
                            .set(StockTypeImport::getCurrency,v.getDclCurrcd())
                            .set("E".equals(headType.getStockTypecd()),StockTypeImport::getDeliveryNoteNumber,v.getSasStockNo())
                            .set("I".equals(headType.getStockTypecd()),StockTypeImport::getWarehouseEntryNumber,v.getSasStockNo())
//                            .set("E".equals(headType.getStockTypecd()),StockTypeImport::getDeliveryDate,headType.getDeclarationDate())
//                            .set("I".equals(headType.getStockTypecd()),StockTypeImport::getWarehousingDate,headType.getDeclarationDate())
                            .set("E".equals(headType.getStockTypecd()),StockTypeImport::getDeliveryDate,stockHeadType.getDeclarationDate())
                            .set("I".equals(headType.getStockTypecd()),StockTypeImport::getWarehousingDate,stockHeadType.getDeclarationDate())
                            .set(StockTypeImport::getUniversalStatus,"0")//还原推送标识
                            .eq(StockTypeImport::getMrNo,goodsType.getMrNo())
                            .eq(StockTypeImport::getInvoicePartNo,v.getGdsMtno()));
                }else {
                    v.setId(IdWorker.getId());
                    v.setStockId(stockHeadType.getId());
                }


            });
        }else  if (goodsTypeList != null && !goodsTypeList.isEmpty()){
            stockHeadType.setStockGoodsTypeList(goodsTypeList);
        }
        this.updateById(stockHeadType);
        if (goodsTypeList != null && goodsTypeList.size() != stockHeadType.getStockGoodsTypeList().size()){
            stockGoodsTypeService.remove(new QueryWrapper<StockGoodsType>().lambda()
                    .eq(StockGoodsType::getStockId,headType.getId()));
        }
        stockGoodsTypeService.saveOrUpdateBatch(stockHeadType.getStockGoodsTypeList());
        //排除出库单回执生成核放单
        if ("B".equals(stockHeadType.getDecStatus()) && !"E".equals(stockHeadType.getStockTypecd())){
            passPortHeadService.generatePassPortByStockType(stockHeadType.getId().toString());
        }
        //2024/05/20 当申报类型为作废时解除库存占用
//        if ("3".equals(stockHeadType.getDclTypecd()) && "E".equals(stockHeadType.getStockTypecd())){
//            Result<String> rectifyYmMsg = emsStockApi.rectify(stockHeadType.getAreainOriactNo(), stockHeadType.getId().toString(),
//                    isNotEmpty(stockHeadType.getEmsFlowsId()) ? stockHeadType.getEmsFlowsId().toString() : null);
//            log.info("!!!!!!!!!!!！！！saveStockHeadTypeForReceipt：" + rectifyYmMsg.isSuccess());
//            log.info("!!!!!!!!!!!！！！saveStockHeadTypeForReceipt：" + rectifyYmMsg.getMessage());
//            log.info("!!!!!!!!!!!！！！saveStockHeadTypeForReceipt：" + rectifyYmMsg.getCode());
//            if (!rectifyYmMsg.isSuccess() && isNotEmpty(rectifyYmMsg.getData())){
//                log.info("saveStockHeadTypeForReceipt=>冲正数量："+rectifyYmMsg.getMessage());
//                return Result.error(rectifyYmMsg.getMessage());
//            }
//            baseMapper.update(null,new UpdateWrapper<StockHeadType>().lambda().set(StockHeadType::getEmsFlowsId,null)
//                    .eq(StockHeadType::getId,stockHeadType.getId()));
//        }
        //2024/03/20 移除只有出库才标记账册推送的标记，"E".equals(stockHeadType.getStockTypecd()) &&
        //2024/05/20 作废的数据重新处理推送标识
        //2024/09/27 出库单海关终审通过不再推送料件，改为核放单终审通过时推送。
//        if (("B".equals(stockHeadType.getDecStatus())
//                && stockHeadType.getStockGoodsTypeList() != null && !stockHeadType.getStockGoodsTypeList().isEmpty())
//                || "3".equals(stockHeadType.getDclTypecd())){
//        if ( "3".equals(stockHeadType.getDclTypecd())){
//            List<Integer> gNos = new ArrayList<>();
//            stockHeadType.getStockGoodsTypeList().forEach(v->{
//                if (!gNos.contains(gNos)){
//                    gNos.add(v.getOriactGdsSeqno());
//                }
//            });
//            invtListMapper.updatePushStatus(headType.getAreainOriactNo(),gNos,"0");
//        }

        //20240802 追加 出库单作废回执后回填 申报类型 到出入库清单
//        if(goodsTypeList.size()>0){
//            List<String> mrNoList= goodsTypeList.stream().map(StockGoodsType::getMrNo)
//                    .collect(Collectors.toList());
//            stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
//                    .set(StockTypeImport::getDclTypecd,stockHeadType.getDclTypecd())
//                    .set(StockTypeImport::getUniversalStatus,0)
//                    .in(StockTypeImport::getMrNo,mrNoList));
//        }
// 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("更新成功");
    }

//    /**
//     * 处理占用
//     *
//     * @param ids
//     * @return org.jeecg.common.api.vo.Result<?>
//     * <AUTHOR>
//     * @date 2024/3/8 18:14
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public Result<?> handleOccupy(String ids) {
//        List<StockHeadType> stockHeadTypes = baseMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
//                .in(StockHeadType::getId,Arrays.asList(ids.split(","))));
//        if (stockHeadTypes == null || stockHeadTypes.isEmpty()){
//            return Result.error("未找到对应出入库单,请核实数据");
//        }
//        for (StockHeadType stockHeadType : stockHeadTypes) {
//            if ("1".equals(stockHeadType.getDclcusFlag())) {
//                return Result.error("存在已经集中申报过的出入库单，请重新选择！");
//            }
//        }
//        // 只有出区类型的才走占用逻辑
//        List<StockHeadType> stockHeadTypesE = stockHeadTypes.stream()
//                .filter(i -> E.equals(i.getStockTypecd())).collect(Collectors.toList());
//        StringBuilder msg = new StringBuilder();
//        if (isNotEmpty(stockHeadTypesE)) {
//            StockHeadTypeServiceImpl currentProxy = (StockHeadTypeServiceImpl) AopContext.currentProxy(); // 获取代理对象
//            for (StockHeadType stockHeadType : stockHeadTypesE) {
//                // 2024/3/13 9:20@ZHANGCHAO 追加/变更/完善：只有第一次发送时，才走占用逻辑！！
//                if ("1".equals(stockHeadType.getSend())) {
//                    continue;
//                }
//                List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
//                        .eq(StockGoodsType::getStockId, stockHeadType.getId()));
//                Result<?> result = currentProxy.handleOccupyOrDeOccupy(stockHeadType, stockGoodsTypeList, true);
//                if (result.isSuccess()) {
//                    msg.append(result.getResult()).append("，");
//                } else {
//                    throw new RuntimeException("占用失败：" + result.getMessage());
//                }
//            }
//        }
//        log.info("占用成功：{}", msg);
//        return Result.ok("操作成功！");
//    }

    /**
     * 批量处理出入库单的占用逻辑
     *
     * @param stockHeadType
     * @param stockGoodsTypeList
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/3/13 11:45
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> handleOccupyBatch(StockHeadType stockHeadType, List<StockGoodsType> stockGoodsTypeList, boolean isOccupy) {
        if (isEmpty(stockHeadType) || isEmpty(stockGoodsTypeList)) {
            return Result.error("未找到对应出入库单，请核实数据！");
        }
        StringBuilder msg = new StringBuilder();
        StockParamVO stockParamVO = new StockParamVO();
        NemsInvtHead nemsInvtHead = new NemsInvtHead();
        nemsInvtHead.setId(stockHeadType.getId()); // 出入库单表头的ID
        nemsInvtHead.setPutrecNo(stockHeadType.getAreainOriactNo()); // 账册号
        nemsInvtHead.setImpexpMarkcd(stockHeadType.getStockTypecd());
        stockParamVO.setNemsInvtHead(nemsInvtHead);

        for (StockGoodsType stockGoodsType : stockGoodsTypeList) {
            if (I.equals(stockHeadType.getStockTypecd())) {
                stockGoodsType.setDclQty(stockGoodsType.getDclQty().negate()); // 如果是入区回仓单，则取负值来占用！！
            }
            NemsInvtList nemsInvtList = new NemsInvtList();
            nemsInvtList.setId(stockGoodsType.getId()); // 出库单表体ID
            nemsInvtList.setPutrecSeqno(Integer.valueOf(stockGoodsType.getOriactGdsSeqno()));
            nemsInvtList.setGdsMtno(stockGoodsType.getGdsMtno());
            nemsInvtList.setHscode(stockGoodsType.getGdecd());
            nemsInvtList.setHsname(stockGoodsType.getGdsNm());
            nemsInvtList.setDclQty(stockGoodsType.getDclQty());
            nemsInvtList.setEmsFlowId(stockGoodsType.getEmsFlowsId());
            stockParamVO.setNemsInvtList(nemsInvtList);
            try {
                // 解除占用
                Result<?> deOccResult = emsStocksFlowService.deOccupyHandleForStockGoods(stockParamVO);
                if (!deOccResult.isSuccess()) {
                    throw new RuntimeException(deOccResult.getMessage());
                } else {
                    msg.append(deOccResult.getResult());
                    stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                            .set(StockGoodsType::getEmsFlowsId, null)
                            .eq(StockGoodsType::getId, stockGoodsType.getId()));
                }
                if (isOccupy) {
                    // 占用
                    Result<?> result = emsStocksFlowService.occupyGoodsForStockGoods(stockParamVO);
                    if (!result.isSuccess()) {
                        throw new RuntimeException(result.getMessage());
                    } else {
                        stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                                .set(StockGoodsType::getEmsFlowsId, result.getResult())
                                .eq(StockGoodsType::getId, stockGoodsType.getId()));
                        msg.append("表体[备案序号：").append(nemsInvtList.getPutrecSeqno()).append("，物料号：")
                                .append(nemsInvtList.getGdsMtno()).append("]已核增占用数量：").append(stockParamVO.getNemsInvtList().getDclQty()).append("；");
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        }

        return Result.ok(msg.toString());
    }

    /**
     * 处理出入库单的占用逻辑
     *
     * @param stockHeadType
     * @param stockGoodsType
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/3/13 11:45
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> handleOccupy(StockHeadType stockHeadType, StockGoodsType stockGoodsType, boolean isOccupy) {
        if (isEmpty(stockHeadType) || isEmpty(stockGoodsType)) {
            return Result.error("未找到对应出入库单，请核实数据！");
        }
        StringBuilder msg = new StringBuilder();
        StockParamVO stockParamVO = new StockParamVO();
        // 表头
        NemsInvtHead nemsInvtHead = new NemsInvtHead();
        nemsInvtHead.setId(stockHeadType.getId()); // 出入库单表头的ID
        nemsInvtHead.setPutrecNo(stockHeadType.getAreainOriactNo()); // 账册号
        nemsInvtHead.setImpexpMarkcd(stockHeadType.getStockTypecd());
        stockParamVO.setNemsInvtHead(nemsInvtHead);
        // 表体
        if (I.equals(stockHeadType.getStockTypecd())) {
            stockGoodsType.setDclQty(stockGoodsType.getDclQty().negate()); // 如果是入区回仓单，则取负值来占用！！
        }
        NemsInvtList nemsInvtList = new NemsInvtList();
        nemsInvtList.setId(stockGoodsType.getId()); // 出库单表体ID
        nemsInvtList.setPutrecSeqno(Integer.valueOf(stockGoodsType.getOriactGdsSeqno()));
        nemsInvtList.setGdsMtno(stockGoodsType.getGdsMtno());
        nemsInvtList.setHscode(stockGoodsType.getGdecd());
        nemsInvtList.setHsname(stockGoodsType.getGdsNm());
        nemsInvtList.setDclQty(stockGoodsType.getDclQty());
        nemsInvtList.setEmsFlowId(stockGoodsType.getEmsFlowsId());
        stockParamVO.setNemsInvtList(nemsInvtList);
        try {
            // 解除占用
            Result<?> deOccResult = emsStocksFlowService.deOccupyHandleForStockGoods(stockParamVO);
            if (!deOccResult.isSuccess()) {
                throw new RuntimeException(deOccResult.getMessage());
            } else {
                msg.append(deOccResult.getResult());
                stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                        .set(StockGoodsType::getEmsFlowsId, null)
                        .eq(StockGoodsType::getId, stockGoodsType.getId()));
            }
            if (isOccupy) {
                // 占用
                Result<?> result = emsStocksFlowService.occupyGoodsForStockGoods(stockParamVO);
                if (!result.isSuccess()) {
                    throw new RuntimeException(result.getMessage());
                } else {
                    stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                            .set(StockGoodsType::getEmsFlowsId, result.getResult())
                            .eq(StockGoodsType::getId, stockGoodsType.getId()));
                    msg.append("表体[备案序号：").append(nemsInvtList.getPutrecSeqno()).append("，物料号：")
                            .append(nemsInvtList.getGdsMtno()).append("]已核增占用数量：").append(stockParamVO.getNemsInvtList().getDclQty()).append("；");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        return Result.ok(msg.toString());
    }

    /**
     * 单个发送报文
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StockHeadType>
     * <AUTHOR>
     * @date 2024/3/13 13:05
     */
    private Result<StockHeadType> sendMessageById(String id) {
        StockHeadType stockHeadType = baseMapper.selectById(id);
        if (stockHeadType == null) {
            return Result.error(new String().format("流水号%s:%s", id, "未找到对应出入库单"));
        }
        List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
                .eq(StockGoodsType::getStockId, id));
        if (isEmpty(stockGoodsTypeList)) {
            return Result.error(new String().format("流水号%s:%s", id, "未找到对应出入库单表体信息"));
        }
//        MsgFtpConfig ftpConfig = null;
//        ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendSasPath);
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendSasPath());
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
        String fileName = new StringBuilder(stockHeadType.getId().toString()).append("-SAS002").toString();//文件名称
        boolean uploadFlag = false;
        try {
            if (FTP.equals(ftpType)) {
                uploadFlag = new FTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(
                                MessageFileUtil.exportZip(
                                        StockTypeMessageUtil.generateSignature(stockHeadType, stockGoodsTypeList)
                                        ,  String.format("%s.xml", fileName)).toByteArray()));
            } else {
                uploadFlag = new SFTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(
                                MessageFileUtil.exportZip(
                                        StockTypeMessageUtil.generateSignature(stockHeadType, stockGoodsTypeList)
                                        ,  String.format("%s.xml", fileName)).toByteArray()));
            }

        } catch (Exception e) {
            e.getMessage();
            log.info(e.getMessage());
        }
        if (!uploadFlag) {
            return Result.error("发送失败");
        }

        stockHeadType.setSend("1");
        this.update(new UpdateWrapper<StockHeadType>().lambda().set(StockHeadType::getSend,"1")
                .eq(StockHeadType::getId,stockHeadType.getId()));

        return Result.ok("发送成功!");
    }

    /**
     * 出入库单生成核放单
     * @param passPortHead
     * @param stockHeadType
     */
    private void createPassPortHeadByStockHead(PassPortHead passPortHead, StockHeadType stockHeadType) {
        passPortHead.setId(IdWorker.getId());
        passPortHead.setPassportTypecd("4");//核放单类型代码
        passPortHead.setMasterCuscd("4301");//主管关区代码
        passPortHead.setDclTypecd("1");//申报类型代码
        passPortHead.setBindTypecd("2");//绑定类型代码
        passPortHead.setRltTbTypecd("2");//关联单证类型代码
//        passPortHead.setVehicleNo("鲁A7J5B1");//承运车车牌号
//        passPortHead.setVehicleIcNo("鲁A7J5B1");//IC卡号(电子车牌）
//        passPortHead.setVehicleWt(new BigDecimal("1619"));//车自重
        passPortHead.setDclEtpsno("3701983939");//申报企业编号
        passPortHead.setDclEtpsNm("山东迅吉安国际物流有限公司");//申报企业名称
        passPortHead.setDclEtpsSccd("91370100560757589T");//申报企业社会信用代码
        passPortHead.setInputCode("3701983939");//录入单位代码
        passPortHead.setInputSccd("91370100560757589T");//录入单位社会信用代码
        passPortHead.setInputName("山东迅吉安国际物流有限公司");//录入单位名称
        passPortHead.setCol1(true);//到货确认标志
        passPortHead.setAreainOriactNo("");//区内账册编号
        passPortHead.setContainerNo("");//集装箱号
        passPortHead.setVehicleFrameNo("");//车架号
        passPortHead.setVehicleFrameWt(null);//车架重
        passPortHead.setContainerType("");//集装箱箱型
        passPortHead.setContainerWt(null);//集装箱重
//        passPortHead.setEtpsPreentNo("");//企业内部编号
        passPortHead.setRmk("");//备注

        passPortHead.setAudited(false);//是否审核
        passPortHead.setSend(false);//是否发送报文
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        passPortHead.setDclErConc(sysUser.getUsername());//申请人及联系方式
        passPortHead.setCreateBy(sysUser.getUsername());//创建人
        passPortHead.setCreateDate(new Date());//创建时间
        passPortHead.setDecPushStatus("0");
        try{
            passPortHead.setInputDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));//录入日期
        }catch (Exception e){
            e.printStackTrace();
        }
        passPortHead.setInputId(Long.valueOf(TenantContext.getTenant()));//录入企业ID

        passPortHead.setAreainEtpsNo(stockHeadType.getAreainEtpsno());//区内企业编码
        passPortHead.setAreainEtpsNm(stockHeadType.getAreainEtpsNm());//区内企业名称
        passPortHead.setAreainEtpsSccd(stockHeadType.getAreainEtpsSccd());//区内企业社会信用代码
        passPortHead.setIoTypecd(stockHeadType.getStockTypecd());//进出标志代码
        passPortHead.setRltNo(stockHeadType.getSasStockNo());//关联单证编号
        passPortHead.setTotalGrossWt(stockHeadType.getGrossWt());//货物总毛重
        passPortHead.setTotalNetWt(stockHeadType.getNetWt());//货物总净重
        BigDecimal totalWt = (stockHeadType.getGrossWt() != null ? stockHeadType.getGrossWt() : BigDecimal.ZERO)
                .add(isNotEmpty(passPortHead.getVehicleWt()) ? passPortHead.getVehicleWt() : BigDecimal.ZERO);
        passPortHead.setTotalWt(totalWt);//总重量
        passPortHead.setRelationId(stockHeadType.getId().toString());//关联id
        passPortHead.setAreainOriactNo(stockHeadType.getAreainOriactNo());//账册号
        passPortHead.setEtpsPreentNo(stockHeadType.getEtpsPreentNo()+"_HF");//企业内部编号
        passPortHead.setReceiptDate(stockHeadType.getDeclarationDate());//收货时间
    }

    /**
     * 批量删除数据
     *
     * @param ids 要删除的数据的ID集合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        List idlist = Arrays.asList(ids.split(","));
        List<StockHeadType> stockHeadTypeList = baseMapper.selectBatchIds(idlist);
        if (isEmpty(stockHeadTypeList)) {
            return Result.ok("删除成功！");
        }
//        List<Long> warehousingIds = new ArrayList<>();//记录出库id
//        List<StockHeadType> headTypes_I = new ArrayList<>();
//        List<StockHeadType> headTypes_E = new ArrayList<>();
        for (StockHeadType stockHeadType : stockHeadTypeList) {
            if ("B".equals(stockHeadType.getDecStatus())){
                return Result.error("海关终审通过后不允许删除！");
            }
//            if ("I".equals(stockHeadType.getStockTypecd())){
//                headTypes_I.add(stockHeadType);
//            }else {
//                headTypes_E.add(stockHeadType);
//                warehousingIds.add(stockHeadType.getId());
//            }
        }
//         /*
//         针对处理关联的出入库单处理出现问题，所以先删除出库单，再删除入库单时若存在出库单（warehousingIds）时判断是否之前处理过
//         */
//        //出库单删除
//        handleReomveStockForQty(headTypes_E, null);
//        //入库单删除
//        handleReomveStockForQty(headTypes_I,warehousingIds);
//        //用于回退出入库清单中的
//        Map<String,BigDecimal> mrNoAndMcQtyMap = new HashMap<>();
//        if (!headTypes_I.isEmpty()){
//            List<Long> ids_I = headTypes_I.stream().map(StockHeadType::getId).collect(Collectors.toList());
//            List<StockGoodsType> goodsTypeList_I = stockGoodsTypeService.list(new QueryWrapper<StockGoodsType>().lambda()
//                    .in(StockGoodsType::getStockId,ids_I));
//            if (goodsTypeList_I != null && !goodsTypeList_I.isEmpty()){
//                goodsTypeList_I.forEach(v->{
//                    BigDecimal mcQty =v.getDclQty();
//                    if (mrNoAndMcQtyMap.containsKey(v.getMrNo())){
//                        mcQty = mrNoAndMcQtyMap.get(v.getMrNo()).add(v.getDclQty());
//                    }
//                    mrNoAndMcQtyMap.put(v.getMrNo(),mcQty);
//                });
//            }
//        }
        StockHeadTypeServiceImpl currentProxy = (StockHeadTypeServiceImpl) AopContext.currentProxy(); // 获取代理对象
        for (StockHeadType stockHeadType : stockHeadTypeList) {
            List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeService.list(new LambdaQueryWrapper<StockGoodsType>()
                    .eq(StockGoodsType::getStockId, stockHeadType.getId()));
            Result<?> result = currentProxy.handleOccupyBatch(stockHeadType, stockGoodsTypeList, false);
            if (result.isSuccess()) {
                log.info("出入库单[" + stockHeadType.getSasStockNo() + "]的表体占用成功：" + result.getResult());
            } else {
                throw new RuntimeException("占用失败：" + result.getMessage());
            }
//            StockParamVO stockParamVO = new StockParamVO();
//            NemsInvtHead nemsInvtHead = new NemsInvtHead();
//            nemsInvtHead.setId(stockHeadType.getId()); // 出入库单表头的ID
//            nemsInvtHead.setPutrecNo(stockHeadType.getAreainOriactNo()); // 账册号
//            nemsInvtHead.setImpexpMarkcd(stockHeadType.getStockTypecd());
//            stockParamVO.setNemsInvtHead(nemsInvtHead);
//            if (isNotEmpty(stockGoodsTypeList)) {
//                for (StockGoodsType stockGoodsType : stockGoodsTypeList) {
//                    NemsInvtList nemsInvtList = new NemsInvtList();
//                    nemsInvtList.setId(stockGoodsType.getId()); // 出库单表体ID
//                    nemsInvtList.setPutrecSeqno(Integer.valueOf(stockGoodsType.getOriactGdsSeqno()));
//                    nemsInvtList.setGdsMtno(stockGoodsType.getGdsMtno());
//                    nemsInvtList.setHscode(stockGoodsType.getGdecd());
//                    nemsInvtList.setHsname(stockGoodsType.getGdsNm());
//                    nemsInvtList.setDclQty(stockGoodsType.getDclQty());
//                    stockParamVO.setNemsInvtList(nemsInvtList);
//                    // 解除占用
//                    Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
//                    if (!deOccResult.isSuccess()) {
//                        throw new RuntimeException(deOccResult.getMessage());
//                    }
//                }
//            }
        }
        //删除关联表体信息
//        List<StockGoodsType> stockGoodsTypes = stockGoodsTypeService.list(new QueryWrapper<StockGoodsType>().lambda()
//                .in(StockGoodsType::getStockId,idlist));
//        List<String> mrNos = stockGoodsTypes.stream().map(StockGoodsType::getMrNo).collect(Collectors.toList());
        stockGoodsTypeService.remove(new LambdaQueryWrapper<StockGoodsType>()
                .in(isNotNull(idlist), StockGoodsType::getStockId, idlist));
//        if (removeByIds(idlist)) {
//            if (!headTypes_E.isEmpty()) {
//                stockTypeImportService.update(new UpdateWrapper<StockTypeImport>().lambda()
//                        .set(StockTypeImport::getGenerateStockType, "0").in(StockTypeImport::getMrNo, mrNos));
//            }
//            mrNoAndMcQtyMap.forEach((k,v)->{
//                stockTypeImportService.update(new UpdateWrapper<StockTypeImport>().lambda()
//                        .set(StockTypeImport::getHadWarehouseReceipt,"0")
//                        .set(StockTypeImport::getSurplusMcQty,v)
//                        .eq(StockTypeImport::getMrNo,k));
//            });
        removeByIds(idlist);
            return Result.ok("删除成功！");
//        } else {
//            return Result.error("删除失败！");
//        }
    }

    /**
     * 根据出库单id冲正数量
     *
     * @param stockTypeIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> rectifyQtyForStockTypeId(String stockTypeIds) {
        if (isBlank(stockTypeIds)){
            return Result.error("出库单id为空");
        }
        List<StockHeadType> stockHeadTypes = baseMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
                .in(StockHeadType::getId,Arrays.asList(stockTypeIds.split(","))));
        if (stockHeadTypes != null && stockHeadTypes.size() > 0){
            stockHeadTypes.forEach(v->{
                if ("I".equals(v.getStockTypecd())){
                    return;
                }
                // 表体
                List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
                        .eq(StockGoodsType::getStockId, v.getId()));
                if (isNotEmpty(stockGoodsTypeList)) {
                    for (StockGoodsType stockGoodsType : stockGoodsTypeList) {
                        StockParamVO stockParamVO = new StockParamVO();

                        NemsInvtHead nemsInvtHead = new NemsInvtHead();
                        nemsInvtHead.setPutrecNo(v.getAreainOriactNo());
                        nemsInvtHead.setId(v.getId());
                        nemsInvtHead.setImpexpMarkcd(I);

                        NemsInvtList nemsInvtList = new NemsInvtList();
                        nemsInvtList.setPutrecSeqno(Integer.valueOf(stockGoodsType.getOriactGdsSeqno()));
                        nemsInvtList.setGdsMtno(stockGoodsType.getGdsMtno());
                        nemsInvtList.setHscode(stockGoodsType.getGdecd());
                        nemsInvtList.setHsname(stockGoodsType.getGdsNm());
                        nemsInvtList.setDclQty(stockGoodsType.getDclQty());
                        nemsInvtList.setId(stockGoodsType.getId());

                        stockParamVO.setNemsInvtHead(nemsInvtHead);
                        stockParamVO.setNemsInvtList(nemsInvtList);
                        //冲正
                        Result<?> rectifyYmMsg = emsStocksFlowService.rectify(stockParamVO);
                        log.info("!!!!!!!!!!!！！！rectifyQtyForStockTypeId：" + rectifyYmMsg.isSuccess());
                        log.info("!!!!!!!!!!!！！！rectifyQtyForStockTypeId：" + rectifyYmMsg.getMessage());
                        log.info("!!!!!!!!!!!！！！rectifyQtyForStockTypeId：" + rectifyYmMsg.getResult());
                    }
                }
            });
        }
        return Result.ok("冲正成功");
    }

    /**
     * 出入库单生成核注单和报关单
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> generalInvtAndDec(String ids) {
        List<StockHeadType> stockHeadTypes = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
                .in(StockGoodsType::getStockId, Arrays.asList(ids.split(","))));
        if (isEmpty(stockGoodsTypeList)) {
            return Result.error("选择的出入库单中不存在表体数据！");
        }
        List<String> noConditions = new ArrayList<>();
        Map<String, StockHeadType> stockHeadTypeMap_I = new HashMap<>();
        for (StockHeadType stockHeadType : stockHeadTypes) {
            if (!"1".equals(stockHeadType.getSend()) || !"B".equals(stockHeadType.getDecStatus())) {
                noConditions.add(stockHeadType.getSasStockNo());
            }
            List<StockHeadType> stockHeadType_I_List = baseMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
                    .eq(StockHeadType::getRltWarehousingId, stockHeadType.getId()));
            if (isNotEmpty(stockHeadType_I_List)) {
                stockHeadType_I_List.forEach(v -> {
                    if (!"1".equals(v.getSend()) || !"B".equals(v.getDecStatus())) {
                        stockHeadTypeMap_I.put(stockHeadType.getSasStockNo(), v);
                    }
                });
            }
        }
        if (isNotEmpty(noConditions)) {
            return Result.error("必须已发送报文且数据状态为海关终审通过的出入库单才可集中申报，以下单号不符合条件：[" + CollUtil.join(noConditions, "，") + "]！");
        }
        if (isNotEmpty(stockHeadTypeMap_I)) {
            return Result.error("出入库单[" + CollUtil.join(stockHeadTypeMap_I.keySet(), "，") + "]关联的退货单还未发送报文或海关终审通过！");
        }

        StockHeadTypeToInvtDTO stockHeadTypeToInvtDTO = new StockHeadTypeToInvtDTO();
        stockHeadTypeToInvtDTO.setNemsInvtHead(toNemsInvtHead(stockHeadTypes));
        stockHeadTypeToInvtDTO.setNemsInvtList(toNemsInvtLists(stockGoodsTypeList));

        return Result.ok(stockHeadTypeToInvtDTO);
    }

    /**
     * 根据id查询货物清单类型
     *
     * @param ids 货物清单类型id
     * @return 货物清单类型
     */
    @Override
    public Result<?> getStockByIds(String ids) {
        List<StockHeadType> stockHeadTypes = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isNotEmpty(stockHeadTypes)) {
            stockHeadTypes.forEach(v -> {
                List<StockGoodsType> list = stockGoodsTypeMapper.selectList(new QueryWrapper<StockGoodsType>().lambda()
                        .eq(StockGoodsType::getStockId, v.getId()));
                v.setStockGoodsTypeList(list);
            });
        }
        return Result.ok(stockHeadTypes);
    }

    /**
     * 导出指定股票类型的数据批量导出功能。
     *
     * @param stockHeadType 股票头部类型，指定需要导出的股票类型。
     * @param request       HttpServletRequest对象，用于获取请求相关信息。
     * @param response      HttpServletResponse对象，用于设置响应头信息和输出数据。
     */
    @Override
    public void exportStockTypeBatch(StockHeadType stockHeadType, HttpServletRequest request, HttpServletResponse response) throws IOException {
        QueryWrapper queryWrapper;
        if (isNotBlank(stockHeadType.getIds())){
            queryWrapper = new QueryWrapper<StockHeadType>().apply(isNotEmpty(stockHeadType.getIds()), "STOCK_HEAD_TYPE.ID IN ("+stockHeadType.getIds()+")");
        } else {
            queryWrapper = setQueryWrapper(stockHeadType);
        }
        List<StockGoodsType> stockGoodsTypeList = stockGoodsTypeMapper.listStockGoodsType(queryWrapper);
        if (isEmpty(stockGoodsTypeList)) {
            throw new RuntimeException("未找到对应信息，请核对数据！");
        }
        //获取模板文件路径
        Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream("/templates/xls/出入库单导出模版.xlsx"));
        TemplateExportParams params = new TemplateExportParams();
        params.setTemplateWb(templateWorkbook);

        Map<String, Object> dataMap = new HashMap<>();
        List<Map<String, Object>> listMapList = new ArrayList<>();
        setExcleMap(listMapList, stockGoodsTypeList);

        dataMap.put("listMapList", listMapList);
        log.info("【exportStockTypeBatch】最终的dataMap===> " + dataMap);
        Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 查询出库单关联的入库单
     *
     * @param rltWarehousingId 要查询的出入库单ID
     * @return 查询结果
     */
    @Override
    public Result<?> getStockHeadByRltWarehousingId(String rltWarehousingId) {
        StockHeadType stockHeadType = baseMapper.selectOne(new QueryWrapper<StockHeadType>().lambda()
                .eq(StockHeadType::getRltWarehousingId,rltWarehousingId));
        return Result.ok(stockHeadType);
    }

    /**
     * 出入库单导出处理数据
     *
     * @param listMapList
     * @param stockGoodsTypeList
     * @return void
     * <AUTHOR>
     * @date 2024/3/5 11:14
     */
    private void setExcleMap(List<Map<String, Object>> listMapList, List<StockGoodsType> stockGoodsTypeList) {
        List<DictQuery> cjdwList = commonService.listDictQuery("erp_units", "code", "name", null, null);
        List<DictQuery> bzdmList = commonService.listDictQuery("erp_currencies", "code", "name", "currency", null);
        List<DictQuery> gbdqList = commonService.listDictQuery("erp_countries", "code", "name", null, null);
        List<DictQuery> zjmsfsList = commonService.listDict("ZJMSFS");
        stockGoodsTypeList.forEach(v -> {
            Map<String, Object> lm = new HashMap<>();
            lm.put("etpsPreentNo", v.getEtpsPreentNo());//企业内部编号
            lm.put("sasStockNo", v.getSasStockNo());//出入库单编号
            lm.put("rltBondInvtNo", v.getRltBondInvtNo());//
            lm.put("packageQty", v.getPackageQty() != null ? v.getPackageQty().stripTrailingZeros() : v.getPackageQty());//件数
            lm.put("headNetWt", v.getHeadNetWt() != null ? v.getHeadNetWt().stripTrailingZeros() : v.getHeadNetWt());//表头净重
            lm.put("headGrossWt", v.getHeadGrossWt() != null ? v.getHeadGrossWt().stripTrailingZeros() : v.getHeadGrossWt());//表头毛重
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            lm.put("createDate", v.getCreateDate() != null ? simpleDateFormat.format(v.getCreateDate()) : v.getCreateDate());//录入日期
            lm.put("declarationDate", v.getDeclarationDate() != null ? simpleDateFormat.format(v.getDeclarationDate()) : v.getDeclarationDate());//申报日期
            lm.put("sasStockSeqno", v.getSasStockSeqno());//商品序号
            lm.put("sasDclSeqno", v.getSasDclSeqno());//申报表序号
            lm.put("oriactGdsSeqno", v.getOriactGdsSeqno());//底账商品序号
            lm.put("gdsMtno", v.getGdsMtno());//商品料号
            lm.put("gdecd", v.getGdecd());//商品编码
            lm.put("gdsNm", v.getGdsNm());//商品名称
            lm.put("gdsSpcfModelDesc", v.getGdsSpcfModelDesc());//商品规格型号
            lm.put("dclUnitcd", isNotBlank(v.getDclUnitcd()) ? getTextByCodeFromList(v.getDclUnitcd(), cjdwList) : v.getDclUnitcd());//申报计量单位代码
            lm.put("dclCurrcd", isNotEmpty(v.getDclCurrcd()) ? getTextByCodeFromList(v.getDclCurrcd(), bzdmList) : v.getDclCurrcd());//申报币制代码
            lm.put("dclQty", v.getDclQty() != null ? v.getDclQty().stripTrailingZeros() : v.getDclQty());//申报数量
            lm.put("dclUprcAmt", v.getDclUprcAmt() != null ? v.getDclUprcAmt().stripTrailingZeros() : v.getDclUprcAmt());//申报单价
            lm.put("dclTotalAmt", v.getDclTotalAmt() != null ? v.getDclTotalAmt().stripTrailingZeros() : v.getDclTotalAmt());//申报总价
            lm.put("lawfUnitcd", isNotEmpty(v.getLawfUnitcd()) ? getTextByCodeFromList(v.getLawfUnitcd(), cjdwList) : v.getLawfUnitcd());//法定计量单位代码
            lm.put("secdLawfUnitcd", isNotEmpty(v.getSecdLawfUnitcd())
                    ? getTextByCodeFromList(v.getSecdLawfUnitcd(), cjdwList) : v.getSecdLawfUnitcd());//法定计量单位第二代码
            lm.put("lawfQty", v.getLawfQty() != null ? v.getLawfQty().stripTrailingZeros() : v.getLawfQty());//法定数量
            lm.put("secdLawfQty", v.getSecdLawfQty() != null ? v.getSecdLawfQty().stripTrailingZeros() : v.getSecdLawfQty());//第二法定数量
            lm.put("ucnsVerno", v.getUcnsVerno());//单耗版本号
            lm.put("clyMarkcd", v.getClyMarkcd());//归类标志
            lm.put("wtSfVal", v.getWtSfVal());//重量比例因子
            lm.put("fstSfVal", v.getFstSfVal());//第一比例因子
            lm.put("secdSfVal", v.getSecdSfVal());//第二比例因子
            lm.put("grossWt", v.getGrossWt() != null ? v.getGrossWt().stripTrailingZeros() : v.getGrossWt());//毛重
            lm.put("netWt", v.getNetWt() != null ? v.getNetWt().stripTrailingZeros() : v.getNetWt());//净重
            lm.put("rltGdsSeqno", v.getRltGdsSeqno());//关联商品序号
            lm.put("natcd", isNotEmpty(v.getNatcd()) ? getTextByCodeFromList(v.getNatcd(), gbdqList) : v.getNatcd());//原产国(地区)
            lm.put("destinationNatcd", isNotEmpty(v.getDestinationNatcd()) ? getTextByCodeFromList(v.getDestinationNatcd(), gbdqList) : v.getDestinationNatcd());//最终目的国
            String col1 = v.getCol1();
            if ("1".equals(v.getCol1())) {
                col1 = "是";
            } else if ("0".equals(v.getCol1())) {
                col1 = "否";
            }
            lm.put("col1", col1);//是否参与合并
            lm.put("lvyrlfModecd", isNotEmpty(v.getLvyrlfModecd()) ? getTextByCodeFromList(v.getLvyrlfModecd(), zjmsfsList) : v.getLvyrlfModecd());//征免方式
            lm.put("rmk", v.getRmk());
            SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
            lm.put("rltBondInvtNo", v.getRltBondInvtNo());
            lm.put("rltInvtDclTime", v.getRltInvtDclTime() != null ? simple.format(v.getRltInvtDclTime()) : "");
            lm.put("rltIclearanceNo", v.getRltIclearanceNo());
            lm.put("rltIappDate", v.getRltIappDate() != null ? simple.format(v.getRltIappDate()) : "");
            lm.put("rltEclearanceNo", v.getRltEclearanceNo());
            lm.put("rltEappDate", v.getRltEappDate() != null ? simple.format(v.getRltEappDate()) : "");
            lm.put("etpsInnerInvtNo", v.getEtpsInnerInvtNo() != null ? v.getEtpsInnerInvtNo() : "");
            listMapList.add(lm);
        });
    }

    /**
     * 出入库单表头生成核注单表头
     *
     * @param stockGoodsTypeList
     * @return java.util.List<org.jeecg.modules.business.entity.NemsInvtList>
     * <AUTHOR>
     * @date 2024/3/4 10:14
     */
    private List<NemsInvtList> toNemsInvtLists(List<StockGoodsType> stockGoodsTypeList) {
        List<NemsInvtList> invtLists = new ArrayList<>();
        log.info("出入库单原来条数：" + stockGoodsTypeList.size());
        Map<String, List<StockGoodsType>> stockGoodsTypeMap = stockGoodsTypeList.stream()
                .collect(Collectors.groupingBy(i -> isNotEmpty(i.getSasDclSeqno()) ? i.getSasDclSeqno().toString() : "-1"));
        List<StockGoodsType> mergeList = new ArrayList<>();
        // 遍历stockGoodsTypeMap
        for (Map.Entry<String, List<StockGoodsType>> entry : stockGoodsTypeMap.entrySet()) {
            if (entry.getValue().size() == 1) {
                StockGoodsType mergeItem = entry.getValue().get(0);
                mergeItem.setMergeIds(String.valueOf(mergeItem.getId()));
                mergeList.add(mergeItem);
            } else {
                StockGoodsType newItem = new StockGoodsType();
                BeanUtil.copyProperties(entry.getValue().get(0), newItem, CopyOptions.create().ignoreNullValue());
                List<String> mergeIds = entry.getValue().stream().map(i -> isNotEmpty(i.getId()) ? i.getId().toString() : "").collect(Collectors.toList());
                List<String> sasStockNos = entry.getValue().stream().map(StockGoodsType::getSasStockNo).collect(Collectors.toList());
                newItem.setMergeIds(CollUtil.join(mergeIds, ","));
                newItem.setSasStockNo(CollUtil.join(sasStockNos, ","));
                BigDecimal dclQtyAll = entry.getValue().stream().map(StockGoodsType::getDclQty).filter(ObjectUtil::isNotEmpty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal lawfQtyAll = entry.getValue().stream().map(StockGoodsType::getLawfQty).filter(ObjectUtil::isNotEmpty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal secdLawfQtyAll = entry.getValue().stream().map(StockGoodsType::getSecdLawfQty).filter(ObjectUtil::isNotEmpty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal grossWtAll = entry.getValue().stream().map(StockGoodsType::getGrossWt).filter(ObjectUtil::isNotEmpty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal netWtAll = entry.getValue().stream().map(StockGoodsType::getNetWt).filter(ObjectUtil::isNotEmpty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                newItem.setDclQty(dclQtyAll);
                newItem.setLawfQty(lawfQtyAll);
                newItem.setSecdLawfQty(secdLawfQtyAll);
                newItem.setGrossWt(grossWtAll);
                newItem.setNetWt(netWtAll);

                mergeList.add(newItem);
            }
        }
        log.info("出入库单归并后条数：" + mergeList.size());
        Integer number = 1;
        for (StockGoodsType goods : mergeList) {
            NemsInvtList invtList = new NemsInvtList();
            invtList.setGdsseqNo(number);
            invtList.setEntryGdsSeqno(number++);
            invtList.setPutrecSeqno(isNotBlank(goods.getOriactGdsSeqno()) ? Integer.valueOf(goods.getOriactGdsSeqno()) : null);
            invtList.setHscode(goods.getGdecd());
            invtList.setHsname(goods.getGdsNm());
            invtList.setHsmodel(goods.getGdsSpcfModelDesc());
            invtList.setGdsMtno(goods.getGdsMtno());
            invtList.setDclUnitcd(goods.getDclUnitcd());
            invtList.setLawfUnitcd(goods.getLawfUnitcd());
            invtList.setSecdlawfUnitcd(goods.getSecdLawfUnitcd());
            invtList.setNatcd(goods.getDestinationNatcd());
            invtList.setDclCurrcd(goods.getDclCurrcd());
            invtList.setLvyrlfModecd(goods.getLvyrlfModecd());
            invtList.setUcnsVerno(goods.getUcnsVerno());
            invtList.setApplyTbSeqnoB(goods.getSasDclSeqno() != null ? goods.getSasDclSeqno().toString() : null);
            invtList.setOriginCountry(goods.getNatcd());
            invtList.setRmk(goods.getRmk());
            invtList.setApplyTbSeqnoA(goods.getSasDclSeqno());
            // 2024/3/15 15:15@ZHANGCHAO 追加/变更/完善：计算出真实的申报数量来！！！
            invtList.setDclQty(dealDclQty(goods.getMergeIds()));
            invtList.setDclUprcamt(goods.getDclUprcAmt());
            invtList.setDclTotalamt(goods.getDclTotalAmt());
            invtList.setLawfQty(goods.getLawfQty());
            invtList.setSecdLawfQty(goods.getSecdLawfQty());
            invtList.setGrossWt(goods.getGrossWt());
            invtList.setNetWt(goods.getNetWt());
            invtList.setStockGoodsId(goods.getMergeIds());
            invtList.setSasStockNo(goods.getSasStockNo());
//            invtList.setMrNo(goods.getMrNo());

            //处理特殊税号的申报规范
            if ("3919109900".equals(goods.getGdecd()) || "3919909090".equals(goods.getGdecd())) {
                if ("3919109900".equals(goods.getGdecd()) && !"067".equals(goods.getDclUnitcd())) {

                } else {
//                    String hsModel = goods.getGdsSpcfModelDesc().replaceFirst("(.*\\*长)([\\d\\.]+)(.*\\*厚.*)","$1" + (goods.getDclQty().stripTrailingZeros().toPlainString())+ "$3");
                    String hsModel = goods.getGdsSpcfModelDesc().replaceFirst("(.*\\*长)([\\d\\.]+)(.*\\*单层厚度.*)", "$1" + (goods.getDclQty().stripTrailingZeros().toPlainString()) + "$3");
                    invtList.setHsmodel(hsModel);
                }
            }

            invtLists.add(invtList);
        }
        return invtLists;
    }

    /**
     * 获取真实的申报数量
     *
     * 所有出库-关联的所有入库回仓 = 真实申报数量
     *
     * @param stockGoodsIds
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2024/3/15 15:12
     */
    private BigDecimal dealDclQty(String stockGoodsIds) {
        // 计算实际占用：所有出 - 所有回仓 = 实际占用
        BigDecimal occupyQty = BigDecimal.ZERO;
        for (String stockGoodsId : stockGoodsIds.split(",")) {
            // 找出出区表体对应的入区表体
            StockGoodsType stockGoodsType = stockGoodsTypeMapper.selectById(stockGoodsId);
            if (isNotEmpty(stockGoodsType)) {
                occupyQty = occupyQty.add(stockGoodsType.getDclQty());
                StockHeadType stockHeadType = baseMapper.selectById(stockGoodsType.getStockId());
                if (isNotEmpty(stockHeadType)) {
                    List<StockHeadType> stockHeadType_I_List = baseMapper.selectList(new LambdaQueryWrapper<StockHeadType>()
                            .eq(StockHeadType::getRltWarehousingId, stockHeadType.getId()));
                    if (isNotEmpty(stockHeadType_I_List)) {
                        List<StockGoodsType> stockGoods_I_List = stockGoodsTypeMapper.selectList(new LambdaQueryWrapper<StockGoodsType>()
                                .in(StockGoodsType::getStockId, stockHeadType_I_List.stream().map(StockHeadType::getId).collect(Collectors.toList())));
                        if (isNotEmpty(stockGoods_I_List)) {
                            Map<String, List<StockGoodsType>> stockGoods_I_Map = stockGoods_I_List.stream().collect(Collectors.groupingBy(StockGoodsType::getOriactGdsSeqno));
                            if (stockGoods_I_Map.containsKey(stockGoodsType.getOriactGdsSeqno())) {
                                // 这些是出区表体ID对应的入区表体，可能多条
                                List<StockGoodsType> stockGoods_Is = stockGoods_I_Map.get(stockGoodsType.getOriactGdsSeqno());
                                if (isNotEmpty(stockGoods_Is)) {
                                    for (StockGoodsType stockGoodsI : stockGoods_Is) {
                                        occupyQty = occupyQty.add(isNotEmpty(stockGoodsI.getDclQty()) ? stockGoodsI.getDclQty().negate() : BigDecimal.ZERO);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info("获取到的真实申报数量为：" + occupyQty);
        return isNotEmpty(occupyQty) ? occupyQty : BigDecimal.ZERO;
    }

    /**
     * 出入库单表头生成核注单表头
     *
     * @param stockHeadTypes
     * @return org.jeecg.modules.business.entity.NemsInvtHead
     * <AUTHOR>
     * @date 2024/3/4 9:46
     */
    private NemsInvtHead toNemsInvtHead(List<StockHeadType> stockHeadTypes) {
        if (isEmpty(stockHeadTypes)) {
            return new NemsInvtHead();
        }
        // 获取本企业的保税仓库
        List<StoreInfo> storeInfoList = storeInfoMapper.selectList(new LambdaQueryWrapper<StoreInfo>()
                .eq(StoreInfo::getPurpose, "1"));
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        if (isEmpty(enterpriseInfo)) {
            enterpriseInfo = new EnterpriseInfo();
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        NemsInvtHead nemsInvtHead = new NemsInvtHead();
        nemsInvtHead.setPutrecNo(stockHeadTypes.get(0).getAreainOriactNo());

        // 经营单位 - 租户
        nemsInvtHead.setBizopEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        nemsInvtHead.setBizopEtpsno(enterpriseInfo.getCustomsDeclarationCode());
        nemsInvtHead.setBizopEtpsNm(enterpriseInfo.getEnterpriseFullName());

        // 加工单位 - 仓库
        nemsInvtHead.setRcvgdEtpsno(isNotEmpty(storeInfoList) ? storeInfoList.get(0).getStoreCode() : null);
        nemsInvtHead.setRvsngdEtpsSccd(null);
        nemsInvtHead.setRcvgdEtpsNm(isNotEmpty(storeInfoList) ? storeInfoList.get(0).getStoreName() : null);

        // 申报单位 - 租户
        nemsInvtHead.setDclEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        nemsInvtHead.setDclEtpsno(enterpriseInfo.getCustomsDeclarationCode());
        nemsInvtHead.setDclEtpsNm(enterpriseInfo.getEnterpriseFullName());

        // 录入单位 - 租户
        nemsInvtHead.setInputCode(enterpriseInfo.getCustomsDeclarationCode());
        nemsInvtHead.setInputCreditCode(enterpriseInfo.getUnifiedSocialCreditCode());
        nemsInvtHead.setInputName(enterpriseInfo.getEnterpriseFullName());

        nemsInvtHead.setRltEntryBizopEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        nemsInvtHead.setRltEntryBizopEtpsno(enterpriseInfo.getCustomsDeclarationCode());
        nemsInvtHead.setRltEntryBizopEtpsNm(enterpriseInfo.getEnterpriseFullName());

        nemsInvtHead.setRltEntryRvsngdEtpsSccd(enterpriseInfo.getUnifiedSocialCreditCode());
        nemsInvtHead.setRltEntryRcvgdEtpsno(enterpriseInfo.getCustomsDeclarationCode());
        nemsInvtHead.setRltEntryRcvgdEtpsNm(enterpriseInfo.getEnterpriseFullName());

        nemsInvtHead.setRltEntryDclEtpsSccd(null);
        nemsInvtHead.setRltEntryDclEtpsno(isNotEmpty(storeInfoList) ? storeInfoList.get(0).getStoreCode() : null);
        nemsInvtHead.setRltEntryDclEtpsNm(isNotEmpty(storeInfoList) ? storeInfoList.get(0).getStoreName() : null);

        nemsInvtHead.setImpexpPortcd("4301");
        nemsInvtHead.setDclplcCuscd("4301");
        nemsInvtHead.setImpexpMarkcd("E");
        nemsInvtHead.setMtpckEndprdMarkcd("I");
        nemsInvtHead.setSupvModecd("1200");
        nemsInvtHead.setTrspModecd("9");
        nemsInvtHead.setDclcusFlag("1");
        nemsInvtHead.setDclcusTypecd("1");
        nemsInvtHead.setVrfdedMarkcd("0");
        nemsInvtHead.setDecType("1");
        nemsInvtHead.setStshipTrsarvNatcd("142");
        nemsInvtHead.setInvtType("1");// 清单类型
        nemsInvtHead.setAudited(false);
        nemsInvtHead.setChecked(false);
        nemsInvtHead.setGenDecFlag("1");
        nemsInvtHead.setDclTypecd("1");// 申报类型-备案
        nemsInvtHead.setInputTime(new Date());// 录入日期
        nemsInvtHead.setCreateDate(new Date());// 创建日期
        nemsInvtHead.setSend(false);// 发送状态
        nemsInvtHead.setCreatePassPort(false);
        nemsInvtHead.setSysId("Z8");
        nemsInvtHead.setCreatePerson(sysUser.getUsername());
        nemsInvtHead.setStockHeadId(stockHeadTypes.stream().map(i -> i.getId().toString()).collect(Collectors.joining(",")));
        nemsInvtHead.setApplyNo(stockHeadTypes.get(0).getSasDclNo());
        nemsInvtHead.setAircraftRegistrationNumber(stockHeadTypes.get(0).getAircraftRegistrationNumber());
        nemsInvtHead.setAircraftType(stockHeadTypes.get(0).getAircraftType());
        nemsInvtHead.setInputId(Long.valueOf(TenantContext.getTenant()));
        nemsInvtHead.setDclTenantId(Long.valueOf(TenantContext.getTenant()));
        // 以下飞机类型？？？
//        String owner = stockHeadType.getOwner().indexOf("ST") == 0 ? "ST" : stockHeadType.getOwner();
//        String key = "";
//        String aircraftNumber = "太古自用".equals(stockHeadType.getAircraftRegistrationNumber()) ? "STAECO"
//                : stockHeadType.getAircraftRegistrationNumber();
//        if ("2".equals(stockHeadType.getAircraftType())) {
//            key = new StringBuilder(isNotEmpty(aircraftNumber) ? aircraftNumber : "")
//                    .append(owner).toString();
//        }
//        if ("3".equals(stockHeadType.getAircraftType())) {
//            key = new StringBuilder(aircraftNumber)
//                    .append(isNotEmpty(stockHeadType.getAircraftEntryNo()) ? stockHeadType.getAircraftEntryNo() : "01")
//                    .append(owner).toString();
//        }
//        if ("5".equals(stockHeadType.getAircraftType())) {
//            Integer day = Integer.valueOf(new SimpleDateFormat("dd").format(stockHeadType.getIssueDateTime()));
//            int frequency = 0;
//            if (day <= 10) {
//                frequency = 1;
//            } else if (day <= 20) {
//                frequency = 2;
//            } else {
//                frequency = 3;
//            }
//            String transMode = cjfsMap.get(stockHeadType.getTransMode()) != null ? cjfsMap.get(stockHeadType.getTransMode()).getItemName()
//                    : stockHeadType.getTransMode();
//            key = new StringBuilder(owner).append("PT").append(new SimpleDateFormat("yyMM").format(stockHeadType.getIssueDateTime()))
//                    .append(String.format("%02d", frequency))
//                    .append(aircraftNumber).append(transMode).toString();
//        }
//
//        Integer day = Integer.valueOf(new SimpleDateFormat("dd").format(stockHeadType.getIssueDateTime()));
//        String tailAffix = new SimpleDateFormat("yyMM").format(stockHeadType.getIssueDateTime());
//        if (day <= 10) {
//            tailAffix = tailAffix+"A";
//        } else if (day <= 20) {
//            tailAffix = tailAffix+"B";
//        } else {
//            tailAffix = tailAffix+"C";
//        }
//        Integer number;
//        if (etpsInnerInvtNoMap.containsKey(key)) {
//            number = etpsInnerInvtNoMap.get(key);
//        } else {
//            if ("2".equals(stockHeadType.getAircraftType()) || "3".equals(stockHeadType.getAircraftType())) {
//                List<NemsInvtHead> invtHeads = nemsInvtHeadMapper.selectList(new QueryWrapper<NemsInvtHead>().lambda().isNotNull(NemsInvtHead::getStockHeadId)
//                        .apply("STOCK_HEAD_ID <> ''").apply("ETPS_INNER_INVT_NO LIKE '" + key + "%'")
//                        .apply("ETPS_INNER_INVT_NO LIKE '%" + tailAffix + "'"));
//                if (invtHeads != null && !invtHeads.isEmpty()) {
//                    number = invtHeads.size()+1;
//                } else {
//                    number = 1;
//                }
//            }else if ("5".equals(stockHeadType.getAircraftType())) {
//                List<NemsInvtHead> invtHeads = nemsInvtHeadMapper.selectList(new QueryWrapper<NemsInvtHead>().lambda().isNotNull(NemsInvtHead::getStockHeadId)
//                        .apply("STOCK_HEAD_ID <> ''").apply("ETPS_INNER_INVT_NO LIKE '" + key + "%'"));
//                if (invtHeads != null && !invtHeads.isEmpty()) {
//                    number = invtHeads.size()+1;
//                } else {
//                    number = 1;
//                }
//            }else {
//                number = 1;
//            }
//        }
//        if ("2".equals(stockHeadType.getAircraftType()) || "3".equals(stockHeadType.getAircraftType())) {
//            invtHead.setEtpsInnerInvtNo(key + "-" + String.format("%03d", number++)+"-"+tailAffix);
//        } else if ("5".equals(stockHeadType.getAircraftType())) {
//            invtHead.setEtpsInnerInvtNo(key + String.format("%02d", number++));
//        }
        return nemsInvtHead;
    }

    /**
     * 处理查询条件
     * @param stockHeadType
     * @return
     */
    private QueryWrapper<StockHeadType> setQueryWrapper(StockHeadType stockHeadType){
        QueryWrapper<StockHeadType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(isNotNull(stockHeadType.getStockStatus()), StockHeadType::getStockStatus, stockHeadType.getStockStatus());
        queryWrapper.lambda().like(isNotNull(stockHeadType.getAreainEtpsNm()), StockHeadType::getAreainEtpsNm, stockHeadType.getAreainEtpsNm());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getDclTypecd()), StockHeadType::getDclTypecd, stockHeadType.getDclTypecd());
        queryWrapper.lambda().like(isNotNull(stockHeadType.getEtpsPreentNo()), StockHeadType::getEtpsPreentNo, stockHeadType.getEtpsPreentNo());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getAreainEtpsno()), StockHeadType::getAreainEtpsno, stockHeadType.getAreainEtpsno());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getBusinessTypecd()), StockHeadType::getBusinessTypecd, stockHeadType.getBusinessTypecd());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getStockTypecd()), StockHeadType::getStockTypecd, stockHeadType.getStockTypecd());
        queryWrapper.lambda().ge(isNotNull(stockHeadType.getStartCreateDate()), StockHeadType::getCreateDate, stockHeadType.getStartCreateDate());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getDecStatus()), StockHeadType::getDecStatus, stockHeadType.getDecStatus());
        queryWrapper.lambda().eq(isNotNull(stockHeadType.getInvtHeadId()), StockHeadType::getInvtHeadId, stockHeadType.getInvtHeadId());
        queryWrapper.lambda().eq(isNotEmpty(stockHeadType.getCreatePerson()), StockHeadType::getCreatePerson, stockHeadType.getCreatePerson());
        queryWrapper.lambda().eq(isNotEmpty(stockHeadType.getDclcusFlag()),StockHeadType::getDclcusFlag,stockHeadType.getDclcusFlag());
        queryWrapper.lambda().eq(isNotEmpty(stockHeadType.getAircraftType()),StockHeadType::getAircraftType,stockHeadType.getAircraftType());
        queryWrapper.lambda().eq(isNotEmpty(stockHeadType.getTenantId()),StockHeadType::getTenantId,stockHeadType.getTenantId());

        queryWrapper.lambda().apply(isNotNull(stockHeadType.getSeqNo()), "STOCK_HEAD_TYPE.SEQ_NO ='"+stockHeadType.getSeqNo()+"'");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getSasStockNo()), "STOCK_HEAD_TYPE.SAS_STOCK_NO ='"+stockHeadType.getSasStockNo()+"'");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getSasDclNo()), "STOCK_HEAD_TYPE.SAS_DCL_NO ='"+stockHeadType.getSasDclNo()+"'");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getCreatePassPort()), "STOCK_HEAD_TYPE.CREATE_PASS_PORT ="+stockHeadType.getCreatePassPort());
        if (isNotEmpty(stockHeadType.getIds())){
            List<String> ids = Arrays.asList(stockHeadType.getIds().split(","));
            queryWrapper.in("STOCK_HEAD_TYPE.id",ids);
        }
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltBondInvtNo()), "STOCK_HEAD_TYPE.RLT_BOND_INVT_NO LIKE '%"
                +stockHeadType.getRltBondInvtNo()+"%'");//关联核注清单编号
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltIclearanceNo()), "STOCK_HEAD_TYPE.RLT_ICLEARANCE_NO LIKE '%"
                +stockHeadType.getRltIclearanceNo()+"%'");//关联进口报关单号
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltEclearanceNo()), "STOCK_HEAD_TYPE.RLT_ECLEARANCE_NO LIKE '%"
                +stockHeadType.getRltEclearanceNo()+"%'");//关联出口报关单号

        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltInvtDclTimeStar()),
                "date_format (STOCK_HEAD_TYPE.RLT_INVT_DCL_TIME,'%Y-%m-%d') >= date_format('" + stockHeadType.getRltInvtDclTimeStar() + "','%Y-%m-%d')");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltInvtDclTimeEnd()),
                "date_format (STOCK_HEAD_TYPE.RLT_INVT_DCL_TIME,'%Y-%m-%d') <= date_format('" + stockHeadType.getRltInvtDclTimeEnd() + "','%Y-%m-%d')");

        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltIappDateStar()),
                "date_format (STOCK_HEAD_TYPE.RLT_IAPP_DATE,'%Y-%m-%d') >= date_format('" + stockHeadType.getRltIappDateStar() + "','%Y-%m-%d')");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltIappDateEnd()),
                "date_format (STOCK_HEAD_TYPE.RLT_IAPP_DATE,'%Y-%m-%d') <= date_format('" + stockHeadType.getRltIappDateEnd() + "','%Y-%m-%d')");

        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltEappDateStar()),
                "date_format (STOCK_HEAD_TYPE.RLT_EAPP_DATE,'%Y-%m-%d') >= date_format('" + stockHeadType.getRltEappDateStar() + "','%Y-%m-%d')");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getRltEappDateEnd()),
                "date_format (STOCK_HEAD_TYPE.RLT_EAPP_DATE,'%Y-%m-%d') <= date_format('" + stockHeadType.getRltEappDateEnd() + "','%Y-%m-%d')");



        queryWrapper.lambda().apply(isNotNull(stockHeadType.getStartCreateDate()),
                "date_format (STOCK_HEAD_TYPE.CREATE_DATE,'%Y-%m-%d') >= date_format('" + stockHeadType.getStartCreateDate() + "','%Y-%m-%d')");
        queryWrapper.lambda().apply(isNotNull(stockHeadType.getLastCreateDate()),
                "date_format (STOCK_HEAD_TYPE.CREATE_DATE,'%Y-%m-%d') <= date_format('" + stockHeadType.getLastCreateDate() + "','%Y-%m-%d')");

        queryWrapper.apply(isNotEmpty(stockHeadType.getOriactGdsSeqno()),"STOCK_GOODS_TYPE.ORIACT_GDS_SEQNO = '"+stockHeadType.getOriactGdsSeqno()+"'");
        queryWrapper.apply(isNotEmpty(stockHeadType.getGdsMtno()),"STOCK_GOODS_TYPE.GDS_MTNO LIKE '%"+stockHeadType.getGdsMtno()+"%'");
        queryWrapper.apply(isNotEmpty(stockHeadType.getMrNo()),"STOCK_GOODS_TYPE.RMK LIKE '%"+stockHeadType.getMrNo()+"%'");
        queryWrapper.lambda().eq(isNotEmpty(stockHeadType.getCreatePassPort()),StockHeadType::getCreatePassPort,stockHeadType.getCreatePassPort());
        return queryWrapper;
    }

    /**
     * 获取编码的itemName
     *
     * @param code
     * @param list
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/11/1 17:11
     */
    public static String getTextByCodeFromList(String code, List<DictQuery> list) {
        code = isNotBlank(code) ? code : "";
        String finalCode = code;
        List<DictQuery> filterList = list.stream().filter(DictQuery -> finalCode.equals(DictQuery.getCode())).collect(Collectors.toList());
        return !filterList.isEmpty() ? filterList.get(0).getName() : "";
    }

    /**
     * 驼峰字段转数据库字段
     * @param field
     * @return
     */
    private String mutateField(String field){
        Pattern pattern = Pattern.compile("[A-Z]");
        Matcher matcher = pattern.matcher(field);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()){
            String group = matcher.group(0);
            matcher.appendReplacement(sb,"_"+matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
