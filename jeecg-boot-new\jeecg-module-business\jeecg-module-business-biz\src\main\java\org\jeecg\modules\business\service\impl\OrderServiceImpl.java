package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.ImportExcelUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.entity.excel.OrderExcelEntity;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.excel.ExcelExportStylerBorderImpl;
import org.jeecg.modules.business.vo.DictModelVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.result.ExcelImportResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.PREFIX_TENANT_TOKEN_OBJ;
import static org.jeecg.common.constant.CommonConstant.X_ACCESS_TOKEN;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonMapper commonMapper;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private ICommissionerService commissionerService;
    @Autowired
    private IDomesticSuppliersInfoService domesticSuppliersInfoService;
    @Autowired
    private IOverseasPayerInfoService overseasPayerInfoService;
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private IOrderDetailService orderDetailService;
    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private IContractGoodsService contractGoodsService;
    @Autowired
    private CommissionerMapper commissionerMapper;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private ErpCityportsMapper erpCityportsMapper;

    private static final String URL = "https://api.jgsoft.com.cn:15555/open-api/sm/GetEnterInfoEx";
    //    private static final String URL = "http://*************:15555/open-api/sm/GetEnterInfoEx";
//    private static final String SWID = "8930000026341"; // 迅吉安的卡
    private static final String SWID = "2100090037794";

    /**
     * 订单列表 -- 分页
     *
     * @param pageNo
     * @param pageSize
     * @param order
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/18 16:24
     */
    @Override
    public Result<?> queryPageList(Integer pageNo, Integer pageSize, Order order, HttpServletRequest req) {
        Page<Order> page = new Page<>(pageNo, pageSize);
        if (isNotBlank(order.getOrderTypes())) {
            order.setOrderTypeList(Arrays.stream(order.getOrderTypes().split(",")).collect(Collectors.toList()));
        }
        order.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<Order> pageList = baseMapper.queryPageList(page, order);
        String tenantName = null;
        try {
            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
            Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
            if (isNotEmpty(tenant.getResult())) {
                tenantName = tenant.getResult().getName();
            } else {
                String accessToken = req.getHeader(X_ACCESS_TOKEN);
                String o = (String) redisUtil.get(PREFIX_TENANT_TOKEN_OBJ + accessToken);
                tenantName = (String) JSONObject.parseObject(o).get("name");
            }
        } catch (Exception e) {
            log.error("获取租户名出现异常：{}", e.getMessage());
        }
        if (isNotEmpty(pageList.getRecords())) {
            for (Order o : pageList.getRecords()) {
                // 委托方
                if (isNotBlank(o.getBuyer())) {
                    Commissioner commissioner = commissionerService.getById(o.getBuyer());
                    if (isNotEmpty(commissioner)) {
                        o.setBuyer(commissioner.getCommissionerFullName());
                    } else {
                        o.setBuyer(tenantName);
                    }
                }
                // 境外付款方
                if (isNotBlank(o.getOverseasPayerInfoId())) {
                    OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(o.getOverseasPayerInfoId());
                    if (isNotEmpty(overseasPayerInfo)) {
                        o.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
                    } else {
                        DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(o.getOverseasPayerInfoId());
                        if (isNotEmpty(domesticSuppliersInfo)) {
                            o.setOverseasPayerInfoId(domesticSuppliersInfo.getSuppliersFullName());
                        } else {
                            o.setOverseasPayerInfoId(o.getOverseasPayerInfoId());
                        }
                    }
                }
                // 2024/9/20 13:29@ZHANGCHAO 追加/变更/完善：是否生成业务！
                List<OrderInfo> orderInfoList = orderInfoService.list(new LambdaQueryWrapper<OrderInfo>()
                        .like(OrderInfo::getRelOrderNo, o.getOrderNo()));
                if (isNotEmpty(orderInfoList)) {
                    o.setRelBusinessNo(orderInfoList.stream().map(OrderInfo::getOrderProtocolNo).collect(Collectors.joining(",")));
                }
                List<OrderDetail> orderDetailList = orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                        .eq(OrderDetail::getOrderId, o.getId()));
                if (isNotEmpty(orderDetailList)) {
                    boolean isCannotBeginBusiness = orderDetailList.stream().allMatch(item -> isEmpty(item.getExecQty()) || (item.getExecQty().compareTo(BigDecimal.ZERO) <= 0));
                    o.setIsCannotBeginBusiness(isCannotBeginBusiness ? "yes" : null);
                } else {
                    o.setIsCannotBeginBusiness("yes");
                }
                // 2025/2/26 16:14@ZHANGCHAO 追加/变更/完善：加载表体数据！！
                o.setOrderDetailList(orderDetailList);
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 根据ID查询订单信息
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:37
     */
    @Override
    public Result<?> getOrderById(String id) {
        Order order = baseMapper.selectById(id);
        if (isEmpty(order)) {
            return Result.error("未找到ID为" + id + "的订单信息，请刷新页面重试！");
        }
        // 委托方
        if (isNotBlank(order.getBuyer())) {
            Commissioner commissioner = commissionerService.getById(order.getBuyer());
            if (isNotEmpty(commissioner)) {
                order.setBuyerName(commissioner.getCommissionerEnName());
            } else {
                // 找不到說明是默認的租户
                List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoService.list(new LambdaQueryWrapper<EnterpriseInfo>()
                        .eq(EnterpriseInfo::getTenantId, order.getBuyer()));
                if (isNotEmpty(enterpriseInfoList)) {
                    order.setBuyerName(enterpriseInfoList.get(0).getEnglishName());
                }
            }
        }
        // 境外付款方/境外供应商
        if (isNotBlank(order.getOverseasPayerInfoId())) {
            // 境外付款方
            OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(order.getOverseasPayerInfoId());
            if (isNotEmpty(overseasPayerInfo)) {
                order.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
            } else {
                // 境外供应商
                DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(order.getOverseasPayerInfoId());
                if (isNotEmpty(domesticSuppliersInfo)) {
                    order.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                } else {
                    order.setOverseasPayerInfoName(order.getOverseasPayerInfoId());
                }
            }
        }
        List<OrderDetail> orderDetailList = orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                .eq(OrderDetail::getOrderId, id));
        order.setOrderDetailList(orderDetailList);
        return Result.ok(order);
    }

    /**
     * 判断是否是字母加两位数字的格式
     *
     * @param a
     * @return boolean
     * <AUTHOR>
     * @date 2025/3/5 10:23
     */
    public static boolean isValidFormat(String a) {
        String pattern = "[A-Za-z]\\d{2}";
        return Pattern.matches(pattern, a);
    }

    /**
     * 保存订单
     *
     * @param order
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:12
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveOrder(Order order) {
        String tenantId = isNotEmpty(order.getTenantId()) ? String.valueOf(order.getTenantId()) : TenantContext.getTenant();
        order.setTenantId(Long.valueOf(tenantId));
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isBlank(order.getIeFlag())) {
            return Result.error("未知的进出口标志！");
        }
        // 暂存一下 20250226
        String sellerAddress = order.getSellerAddress();
        String buyerAddress = order.getBuyerAddress();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        // 新增
        if (isBlank(order.getId())) {
            String orderNo = "";
            if ("I".equals(order.getIeFlag())) {
                if (isNotBlank(order.getOrderNo())) {
                    Long isExist = baseMapper.selectCount(new LambdaQueryWrapper<Order>()
                            .eq(Order::getOrderNo, order.getOrderNo())
                            .eq(Order::getTenantId, tenantId));
                    if (isExist > 0) {
                        return Result.error("订单号已存在！");
                    }
                    orderNo = order.getOrderNo();
                } else {
                    orderNo = serialNumberService.getSerialnumberByCustomerCode("CG", 4);
                }
            } else if ("E".equals(order.getIeFlag())) {
                if (isNotBlank(order.getOrderNo())) {
                    Long isExist = baseMapper.selectCount(new LambdaQueryWrapper<Order>()
                            .eq(Order::getOrderNo, order.getOrderNo())
                            .eq(Order::getTenantId, tenantId));
                    if (isExist > 0) {
                        return Result.error("订单号已存在！");
                    }
                    orderNo = order.getOrderNo();
                } else {
                    orderNo = serialNumberService.getSerialnumberByCustomerCode("XS", 4);
                }
            }
            order.setOrderNo(orderNo);
            order.setTotalAmount(isNotEmpty(order.getTotalAmount()) ? order.getTotalAmount() : BigDecimal.ZERO);
            order.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
            order.setCreateDate(new Date());
            // 2024/10/16 15:11@ZHANGCHAO 追加/变更/完善：处理委托方和供应商、付款方的！
            dealBuyerAndOverseasPayerInfo(order);
            // 2024/10/23 22:20@ZHANGCHAO 追加/变更/完善：转换成交方式
            if (isNotBlank(order.getTransMode()) && !isNumeric(order.getTransMode())) {
                List<DictQuery> dictModels8 = commonMapper.listDict("trading_type");
                Map<String, String> dictMap8 = new HashMap<>();
                if (isNotEmpty(dictModels8)) {
                    dictModels8.forEach(dictModel -> dictMap8.put(dictModel.getName(), dictModel.getCode()));
                }
                if(dictMap8.containsKey(order.getTransMode())) {
                    order.setTransMode(dictMap8.get(order.getTransMode()));
                }
            }
            Commissioner commissioner = commissionerMapper.selectById(order.getBuyer());
            if (isNotEmpty(commissioner)) {
                order.setBuyerName(commissioner.getCommissionerFullName());
                order.setBuyerAddress(commissioner.getCommissionerAddressDetail());
            }
            if (isBlank(order.getBuyerName())) {
                // 找不到說明是默認的租户
                List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoService.list(new LambdaQueryWrapper<EnterpriseInfo>()
                        .eq(EnterpriseInfo::getTenantId, order.getBuyer()));
                if (isNotEmpty(enterpriseInfoList)) {
                    order.setBuyerName(enterpriseInfoList.get(0).getEnterpriseFullName());
                    order.setBuyerAddress(enterpriseInfoList.get(0).getRegisteredAddressDetail());
                }
            }
            // 境外付款方
            OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(order.getOverseasPayerInfoId());
            if (isNotEmpty(overseasPayerInfo)) {
                order.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
                order.setSellerAddress(overseasPayerInfo.getOverseasPayerAddressDetail());
            } else {
                // 境外供应商
                DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(order.getOverseasPayerInfoId());
                if (isNotEmpty(domesticSuppliersInfo)) {
                    order.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                    order.setSellerAddress(domesticSuppliersInfo.getSuppliersAddressDetail());
                }
            }
            if (isBlank(order.getSellerAddress())) {
                order.setSellerAddress(sellerAddress);
            }
            if (isBlank(order.getBuyerAddress())) {
                order.setBuyerAddress(buyerAddress);
            }
            // 2025/3/4 13:29@ZHANGCHAO 追加/变更/完善：推送过来的订单，如果运输方式为空，就替换为ship via
            if ("openApi".equals(order.getFrom())) {
                order.setShippingType(isNotBlank(order.getShippingType()) ? order.getShippingType() : order.getShipVia());
                // 2025/3/5 10:21@ZHANGCHAO 追加/变更/完善：处理支付条款！！
                if (isNotBlank(order.getPaymentClause()) && !isValidFormat(order.getPaymentClause())) {
                    List<DictQuery> dictModels8 = commonMapper.listDict("PAYMENT_CLAUSE_DICT");
                    Map<String, String> dictMap8 = new HashMap<>();
                    if (isNotEmpty(dictModels8)) {
                        dictModels8.forEach(dictModel -> dictMap8.put(dictModel.getName(), dictModel.getCode()));
                    }
                    if(dictMap8.containsKey(order.getPaymentClause())) {
                        order.setPaymentClause(dictMap8.get(order.getPaymentClause()));
                    }
                }
            }
            // 2025/3/6 13:32@ZHANGCHAO 追加/变更/完善：处理销售邮箱！！
            if (isNotBlank(order.getSalesEmail()) && !order.getSalesEmail().contains("@")) {
                List<DictQuery> dictModels = commonMapper.listDict("SALES_EMAIL_DICT");
                Map<String, String> dictMap = new HashMap<>();
                if (isNotEmpty(dictModels)) {
                    dictModels.forEach(dictModel -> dictMap.put(dictModel.getCode(), dictModel.getName()));
                }
                if(dictMap.containsKey(order.getSalesEmail())) {
                    order.setSalesEmail(dictMap.get(order.getSalesEmail()));
                }
            }
            baseMapper.insert(order);
            // 编辑
        } else {
            if (isNotBlank(order.getOrderNo())) {
                Long isExist = baseMapper.selectCount(new LambdaQueryWrapper<Order>()
                        .eq(Order::getOrderNo, order.getOrderNo())
                        .ne(Order::getId, order.getId())
                        .eq(Order::getTenantId, tenantId));
                if (isExist > 0) {
                    return Result.error("订单号已存在！");
                }
            } else {
                if ("I".equals(order.getIeFlag())) {
                    order.setOrderNo(serialNumberService.getSerialnumberByCustomerCode("CG", 4));
                } else if ("E".equals(order.getIeFlag())) {
                    order.setOrderNo(serialNumberService.getSerialnumberByCustomerCode("XS", 4));
                }
            }
            order.setTotalAmount(isNotEmpty(order.getTotalAmount()) ? order.getTotalAmount() : BigDecimal.ZERO);
            order.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
            order.setUpdateDate(new Date());
            // 2024/10/16 15:11@ZHANGCHAO 追加/变更/完善：处理委托方和供应商、付款方的！
            dealBuyerAndOverseasPayerInfo(order);
            // 2024/10/23 22:20@ZHANGCHAO 追加/变更/完善：转换成交方式
            if (isNotBlank(order.getTransMode()) && !isNumeric(order.getTransMode())) {
                List<DictQuery> dictModels8 = commonMapper.listDict("trading_type");
                Map<String, String> dictMap8 = new HashMap<>();
                if (isNotEmpty(dictModels8)) {
                    dictModels8.forEach(dictModel -> dictMap8.put(dictModel.getName(), dictModel.getCode()));
                }
                if(dictMap8.containsKey(order.getTransMode())) {
                    order.setTransMode(dictMap8.get(order.getTransMode()));
                }
            }
            Commissioner commissioner = commissionerMapper.selectById(order.getBuyer());
            if (isNotEmpty(commissioner)) {
                order.setBuyerName(commissioner.getCommissionerFullName());
                order.setBuyerAddress(commissioner.getCommissionerAddressDetail());
            }
            if (isBlank(order.getBuyerName())) {
                // 找不到說明是默認的租户
                List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoService.list(new LambdaQueryWrapper<EnterpriseInfo>()
                        .eq(EnterpriseInfo::getTenantId, order.getBuyer()));
                if (isNotEmpty(enterpriseInfoList)) {
                    order.setBuyerName(enterpriseInfoList.get(0).getEnterpriseFullName());
                    order.setBuyerAddress(enterpriseInfoList.get(0).getRegisteredAddressDetail());
                }
            }
            // 境外付款方
            OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(order.getOverseasPayerInfoId());
            if (isNotEmpty(overseasPayerInfo)) {
                order.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
                order.setSellerAddress(overseasPayerInfo.getOverseasPayerAddressDetail());
            } else {
                // 境外供应商
                DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(order.getOverseasPayerInfoId());
                if (isNotEmpty(domesticSuppliersInfo)) {
                    order.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                    order.setSellerAddress(domesticSuppliersInfo.getSuppliersAddressDetail());
                }
            }
            if (isBlank(order.getSellerAddress())) {
                order.setSellerAddress(sellerAddress);
            }
            if (isBlank(order.getBuyerAddress())) {
                order.setBuyerAddress(buyerAddress);
            }
            // 2025/3/6 13:32@ZHANGCHAO 追加/变更/完善：处理销售邮箱！！
            if (isNotBlank(order.getSalesEmail()) && !order.getSalesEmail().contains("@")) {
                List<DictQuery> dictModels = commonMapper.listDict("SALES_EMAIL_DICT");
                Map<String, String> dictMap = new HashMap<>();
                if (isNotEmpty(dictModels)) {
                    dictModels.forEach(dictModel -> dictMap.put(dictModel.getCode(), dictModel.getName()));
                }
                if(dictMap.containsKey(order.getSalesEmail())) {
                    order.setSalesEmail(dictMap.get(order.getSalesEmail()));
                }
            }
            baseMapper.updateById(order);
        }
        // 处理表体
        if (isNotEmpty(order.getOrderDetailList())) {
            List<OrderDetail> oldOrderDetailList = orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                    .eq(OrderDetail::getOrderNo, order.getOrderNo())
                    .eq(OrderDetail::getTenantId, tenantId));
            if (isNotEmpty(oldOrderDetailList)) {
                // 使用 Stream 进行过滤
                List<OrderDetail> dels = oldOrderDetailList.stream()
                        .filter(item -> order.getOrderDetailList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    orderDetailService.removeByIds(dels);
                }
            }
            // 2025/4/17 09:08@ZHANGCHAO 追加/变更/完善：卖方是不是BLOOMAGE开头的？？
            boolean isOther = isNotBlank(order.getOverseasPayerInfoName()) && order.getOverseasPayerInfoName().startsWith("BLOOMAGE");
            log.info("【从品名库带取数据】卖方是不是BLOOMAGE开头的：{}，中文品名：{}", isOther, order.getOverseasPayerInfoName());
            order.getOrderDetailList().forEach(od -> {
                if (isNotBlank(order.getFromOrder())) {
                    if (isNotEmpty(od.getContractGoodsId())) {
                        ContractGoods contractGoods = contractGoodsService.getById(od.getContractGoodsId());
                        if (isNotEmpty(contractGoods)) {
                            // 原已发货+此订单已发货=现合同已发货
                            BigDecimal shippedQty = (isNotEmpty(contractGoods.getShippedQty()) ? contractGoods.getShippedQty() : BigDecimal.ZERO).add(od.getQty());
                            // 合同商品总 - 现合同已发货 = 现可执行
                            BigDecimal execQty = contractGoods.getShipmentQuantity().subtract(shippedQty);
                            contractGoodsService.update(null, new UpdateWrapper<ContractGoods>().lambda()
                                    .set(ContractGoods::getShippedQty, shippedQty)
                                    .set(ContractGoods::getExecQty, execQty)
                                    .eq(ContractGoods::getId, contractGoods.getId()));
                        }
                    }
                }
                if (isNotBlank(od.getId())) {
                    od.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    od.setCreateDate(new Date());
                    od.setTenantId(Long.valueOf(tenantId));
                    // 处理已执行数量、可执行数量
                    if (isEmpty(od.getExecQty())) {
                        od.setExecQty(od.getQty().subtract(isNotEmpty(od.getShippedQty()) ? od.getShippedQty() : BigDecimal.ZERO));
                    }
                    orderDetailService.updateById(od);
                } else {
                    od.setOrderId(order.getId());
                    od.setOrderNo(order.getOrderNo());
                    od.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    od.setUpdateDate(new Date());
                    od.setTenantId(Long.valueOf(tenantId));
                    // 处理已执行数量、可执行数量
                    od.setExecQty(isNotEmpty(od.getExecQty()) ? od.getExecQty() : od.getQty());
                    od.setShippedQty(isNotEmpty(od.getShippedQty()) ? od.getShippedQty() : BigDecimal.ZERO);
                    // 2025/2/27 13:58@ZHANGCHAO 追加/变更/完善：从品名库带取数据！！
                    // 华熙导入订单，全部是新增的！！
                    if ("openApi".equals(order.getFrom())) {
                        List<ProductInfo> productInfos = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                                .eq(ProductInfo::getPn, od.getCopGno())
                                .eq(isNotBlank(od.getPn()) && !isOther, ProductInfo::getChineseName, od.getPn())
                                .eq(isNotBlank(od.getPnEn()) && !isOther, ProductInfo::getEnglishName, od.getPnEn()));
                        if (isNotEmpty(productInfos)) {
                            log.info("【从品名库带取数据】找到了物料号：{}，中文品名：{}关联的品名库数据：{}", od.getCopGno(), od.getPn(), productInfos.get(0));
                            od.setPn(productInfos.get(0).getChineseName());
                            od.setPnEn(productInfos.get(0).getEnglishName());
                            od.setModel(productInfos.get(0).getProductSpecificationModel());
                            od.setHscode(productInfos.get(0).getCustomsCodeInfoCode());
                            od.setHsname(productInfos.get(0).getHsname());
                            // 2025/2/28 18:01@ZHANGCHAO 追加/变更/完善：针对品名库中有变量的！！
                            String hsmodel = productInfos.get(0).getCustomsDeclarationElements();
                            if (isNotBlank(hsmodel) && hsmodel.contains("{}")) {
                                if (isNotBlank(order.getContractNo())) {
                                    Pattern pattern = Pattern.compile("-(\\d{8})-");
                                    Matcher matcher = pattern.matcher(order.getContractNo());
                                    if (matcher.find()) {
                                        String dateStr = matcher.group(1);
                                        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                                        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                                        LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                                        String formattedDate = date.format(outputFormatter);
                                        log.info("提取的日期: {}", dateStr);
                                        log.info("格式化后的日期: {}", formattedDate);
                                        hsmodel = hsmodel.replace("{}", formattedDate);
                                    } else {
                                        log.info("无法从合同编号提取日期: {}", order.getContractNo());
                                    }
                                }
                            }
                            od.setHsmodel(hsmodel);
                        }
                    }
                    orderDetailService.save(od);
                }
            });
        }
        Order returnOrder = baseMapper.selectById(order.getId());
        returnOrder.setOrderDetailList(orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                .eq(OrderDetail::getOrderNo, order.getOrderNo())
                .eq(OrderDetail::getTenantId, tenantId)));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK(returnOrder);
    }

    /**
     * 处理委托方和供应商、付款方的
     *
     * @param order
     * @return void
     * <AUTHOR>
     * @date 2024/10/16 15:12
     */
    private void dealBuyerAndOverseasPayerInfo(Order order) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 处理委托方
        if (isBlank(order.getBuyer()) && isNotBlank(order.getBuyerName())) {
            List<Commissioner> commissioners = commissionerMapper.selectList(new LambdaQueryWrapper<Commissioner>()
                    .eq(Commissioner::getCommissionerFullName, order.getBuyerName())
                    .eq(Commissioner::getTenantId, order.getTenantId()));
            if (isNotEmpty(commissioners)) {
                order.setBuyer(commissioners.get(0).getId());
            } else {
                // 取不到委托方就取租户信息
                EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(order.getBuyerName());
                if (isNotEmpty(enterpriseInfo)) {
                    order.setBuyer(String.valueOf(enterpriseInfo.getTenantId()));
                } else {
                    // 2024/10/25 08:30@ZHANGCHAO 追加/变更/完善：再取不到，就新增个委托方！！
                    Commissioner commissioner = new Commissioner();
                    commissioner.setCommissionerFullName(order.getBuyerName());
                    commissioner.setCommissionerAddressDetail(order.getBuyerAddress());
                    commissioner.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
                    commissioner.setCreateTime(new Date());
                    commissioner.setTenantId(order.getTenantId());
                    try {
                        Map<String, Object> jsonMap = new LinkedHashMap<>();
                        jsonMap.put("swid", SWID);
                        jsonMap.put("code", order.getBuyerName());
                        jsonMap.put("flag", "3");
                        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
                        String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
                        log.info("请求结果：{}", result);
                        JSONObject jsonObject = JSON.parseObject(result);
                        if (isNotEmpty(jsonObject)) {
                            if (isNotEmpty(jsonObject.getJSONObject("data").get("scCode"))) {
                                commissioner.setUnifiedSocialCreditCode(jsonObject.getJSONObject("data").getString("scCode"));
                            }
                            if (isNotEmpty(jsonObject.getJSONObject("data").get("regCusCode"))) {
                                commissioner.setDepartcd(jsonObject.getJSONObject("data").getString("regCusCode"));
                            }
                        }
                    } catch (Exception e) {
                        log.error("根据名称["+order.getBuyerName()+"]获取海关备案信息失败：" + e);
                    }
                    commissionerMapper.insert(commissioner);
                    order.setBuyer(commissioner.getId());
                }
            }
        } else {
            if (isBlank(order.getBuyerName())) {
                Commissioner commissioner = commissionerMapper.selectById(order.getBuyer());
                if (isNotEmpty(commissioner)) {
                    order.setBuyerName(commissioner.getCommissionerFullName());
                } else {
                    String departName = commonMapper.getDepartNameByTenantId(order.getTenantId());
                    order.setBuyerName(departName);
                }
            }
        }
        // 处理供应商-进口 或 买方/付款方-出口
        if (isBlank(order.getOverseasPayerInfoId()) && isNotBlank(order.getOverseasPayerInfoName())) {
            if ("I".equals(order.getIeFlag())) {
                List<DomesticSuppliersInfo> domesticSuppliersInfos = domesticSuppliersInfoService.list(new LambdaQueryWrapper<DomesticSuppliersInfo>()
                        .eq(DomesticSuppliersInfo::getSuppliersFullName, order.getOverseasPayerInfoName())
                        .eq(DomesticSuppliersInfo::getTenantId, order.getTenantId()));
                if (isNotEmpty(domesticSuppliersInfos)) {
                    order.setOverseasPayerInfoId(domesticSuppliersInfos.get(0).getId());
                } else {
                    DomesticSuppliersInfo suppliersInfo = new DomesticSuppliersInfo();
                    suppliersInfo.setSuppliersFullName(order.getOverseasPayerInfoName());
                    suppliersInfo.setSuppliersAddressDetail(order.getSellerAddress());
                    suppliersInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
                    suppliersInfo.setCreateTime(new Date());
                    suppliersInfo.setTenantId(order.getTenantId());
                    try {
                        Map<String, Object> jsonMap = new LinkedHashMap<>();
                        jsonMap.put("swid", SWID);
                        jsonMap.put("code", order.getOverseasPayerInfoName());
                        jsonMap.put("flag", "3");
                        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
                        String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
                        log.info("请求结果：{}", result);
                        JSONObject jsonObject = JSON.parseObject(result);
                        if (isNotEmpty(jsonObject)) {
                            if (isNotEmpty(jsonObject.getJSONObject("data").get("scCode"))) {
                                suppliersInfo.setUnifiedSocialCreditCode(jsonObject.getJSONObject("data").getString("scCode"));
                            }
                        }
                    } catch (Exception e) {
                        log.error("根据名称[{}]获取海关备案信息失败：{}", order.getOverseasPayerInfoName(), e);
                    }
                    domesticSuppliersInfoService.save(suppliersInfo);
                    order.setOverseasPayerInfoId(suppliersInfo.getId());
                }
            } else if ("E".equals(order.getIeFlag())) {
                List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
                        .eq(OverseasPayerInfo::getOverseasPayerName, order.getOverseasPayerInfoName())
                        .eq(OverseasPayerInfo::getTenantId, order.getTenantId()));
                if (isNotEmpty(overseasPayerInfos)) {
                    order.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
                    order.setOverseasPayerInfoName(overseasPayerInfos.get(0).getOverseasPayerName());
                } else {
                    OverseasPayerInfo overduePayerInfo =  new OverseasPayerInfo();
                    overduePayerInfo.setOverseasPayerName(order.getOverseasPayerInfoName());
                    overduePayerInfo.setOverseasPayerAddressDetail(order.getSellerAddress());
                    overduePayerInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
                    overduePayerInfo.setCreateTime(new Date());
                    overduePayerInfo.setTenantId(order.getTenantId());
                    overseasPayerInfoService.save(overduePayerInfo);
                    order.setOverseasPayerInfoId(overduePayerInfo.getId());
                    order.setOverseasPayerInfoName(overduePayerInfo.getOverseasPayerName());
                }
            }
        } else {
            if (isBlank(order.getOverseasPayerInfoName())) {
                if ("I".equals(order.getIeFlag())) {
                    DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(order.getOverseasPayerInfoId());
                    if (isNotEmpty(domesticSuppliersInfo)) {
                        order.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                    }
                } else if ("E".equals(order.getIeFlag())) {
                    OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(order.getOverseasPayerInfoId());
                    if (isNotEmpty(overseasPayerInfo)) {
                        order.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
                    }
                }
            }
        }
        // 处理启运地
        order.setArrivalPortEn(order.getArrivalPort());
        if (isNotBlank(order.getArrivalPort())) {
            DictQuery dictQuery = commonMapper.getCityportsByEnName(order.getArrivalPort());
            if (isNotEmpty(dictQuery)) {
                order.setArrivalPort(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : "");
            }
        }
        // 处理目的地
        order.setDesPortEn(order.getDesPort());
        if (isNotBlank(order.getDesPort())) {
            DictQuery dictQuery = commonMapper.getCityportsByEnName(order.getDesPort());
            if (isNotEmpty(dictQuery)) {
                order.setDesPort(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : "");
            }
        }
    }

    /**
     * 删除订单
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:27
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        List<Order> orderList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(orderList)) {
            return Result.ok("删除成功！");
        }
        List<Order> isLost = orderList.stream().filter(i -> !"-1".equals(i.getStatus())).collect(Collectors.toList());
        if (isNotEmpty(isLost)) {
            return Result.error("只能删除「已失效」的订单，请重新选择！");
        }
        // 2024/9/24 16:43@ZHANGCHAO 追加/变更/完善：回退订单数量
        List<OrderDetail> orderDetailList = orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                .in(OrderDetail::getOrderNo, orderList.stream().map(Order::getOrderNo).collect(Collectors.toList())));
        if (isNotEmpty(orderDetailList)) {
            orderDetailList.forEach(od -> {
                if (isNotEmpty(od.getContractGoodsId())) {
                    ContractGoods contractGoods = contractGoodsService.getById(od.getContractGoodsId());
                    if (isNotEmpty(contractGoods)) {
                        // 可执行
                        BigDecimal newExecQty = (isNotEmpty(contractGoods.getExecQty()) ? contractGoods.getExecQty() : BigDecimal.ZERO).add(isNotEmpty(od.getQty()) ? od.getQty() : BigDecimal.ZERO);
                        // 已执行
                        BigDecimal newShippedQty = (isNotEmpty(contractGoods.getShippedQty()) ? contractGoods.getShippedQty() : BigDecimal.ZERO).subtract(isNotEmpty(od.getQty()) ? od.getQty() : BigDecimal.ZERO);
                        contractGoodsService.update(null, new UpdateWrapper<ContractGoods>().lambda()
                                .set(ContractGoods::getExecQty, newExecQty)
                                .set(ContractGoods::getShippedQty, newShippedQty)
                                .eq(ContractGoods::getId, contractGoods.getId()));
                    }
                }
                orderDetailService.removeById(od.getId());
            });
        }
        baseMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        return Result.ok("删除成功！");
    }

    /**
     * 操作失效
     *
     * @param id
     * @param status
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 13:24
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleLose(String id, String status) {
        Order order = baseMapper.selectById(id);
        if (isEmpty(order)) {
            return Result.error("未找到ID为" + id + "的订单信息，请刷新页面重试！");
        }
        baseMapper.update(null, new LambdaUpdateWrapper<Order>()
                .set(Order::getStatus, status)
                .eq(Order::getId, id));
        return Result.ok("操作成功！");
    }

    /**
     * 导入订单
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 15:24
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> importOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String ieFlag = request.getParameter("ieFlag");
        if (isBlank(ieFlag)) {
            return Result.error("未知的进出口类型！");
        }
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        AtomicInteger successLines = new AtomicInteger();
        AtomicInteger errorLines = new AtomicInteger();
        List<DictModel> dictModelsPort = sysBaseApi.getDictItems("erp_cityports,cnname,cityport_code,isenabled=1");
        Map<String, String> dictMapPort = new HashMap<>();
        if (isNotEmpty(dictModelsPort)) {
            dictModelsPort.forEach(dictModel -> {
                dictMapPort.put(dictModel.getText(), dictModel.getValue());
            });
        }
        // 币制
        List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,code,currency,1=1");
        Map<String, String> dictMap3 = new HashMap<>();
        if (isNotEmpty(dictModels3)) {
            dictModels3.forEach(dictModel -> {
                dictMap3.put(dictModel.getValue(), dictModel.getText());
            });
        }
        // 计量单位
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getText(), dictModel.getValue());
            });
        }
        // 运输方式
        List<DictModel> dictModels = sysBaseApi.getDictItems("trans_type");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getText(), dictModel.getValue());
            });
        }
        // 成交方式
        List<DictModel> dictModels11 = sysBaseApi.getDictItems("trading_type");
        Map<String, String> dictMap11 = new HashMap<>();
        if (isNotEmpty(dictModels11)) {
            dictModels11.forEach(dictModel -> {
                dictMap11.put(dictModel.getText(), dictModel.getValue());
            });
        }
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            try {
                ExcelImportResult<OrderExcelEntity> excelImportResult = ExcelImportUtil.importExcelVerify(file.getInputStream(), OrderExcelEntity.class, params);
                if (null == excelImportResult || isEmpty(excelImportResult.getList())) {
                    return Result.error("文件格式错误，请严格按模版填写！");
                }
                List<OrderExcelEntity> orderExcelEntityList = excelImportResult.getList();
                Map<String, List<OrderExcelEntity>> orderExcelEntityMap = orderExcelEntityList.stream().collect(Collectors.groupingBy(OrderExcelEntity::getOrderNo));
                orderExcelEntityMap.forEach((orderNo, orderExcelEntityList2) -> {
                    try {
                        //-----------处理表头
                        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>()
                                .eq(Order::getOrderNo, orderNo)
                                .eq(Order::getIeFlag, ieFlag)); // 2024/9/20 09:27@ZHANGCHAO 追加/变更/完善：加个进出口限制！！
                        // 新增表头
                        if (isEmpty(order)) {
                            Order insertOrder = new Order();
                            BeanUtil.copyProperties(orderExcelEntityList2.get(0), insertOrder, CopyOptions.create().ignoreNullValue());
                            insertOrder.setId(null);
                            insertOrder.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            insertOrder.setCreateDate(new Date());
                            insertOrder.setIeFlag(ieFlag);
                            // 处理委托方
                            if (isNotBlank(insertOrder.getBuyer())) {
                                insertOrder.setBuyerName(insertOrder.getBuyer());
                                List<Commissioner> commissioners = commissionerService.list(new LambdaQueryWrapper<Commissioner>()
                                        .eq(Commissioner::getCommissionerFullName, insertOrder.getBuyer()));
                                if (isNotEmpty(commissioners)) {
                                    insertOrder.setBuyer(commissioners.get(0).getId());
                                }
                            }
                            // 处理供应商-进口 或 买方/付款方-出口
                            if (isNotBlank(insertOrder.getOverseasPayerInfoId())) {
                                insertOrder.setOverseasPayerInfoName(insertOrder.getOverseasPayerInfoId());
//                                if ("I".equals(ieFlag)) {
                                    List<DomesticSuppliersInfo> domesticSuppliersInfos = domesticSuppliersInfoService.list(new LambdaQueryWrapper<DomesticSuppliersInfo>()
                                            .eq(DomesticSuppliersInfo::getSuppliersFullName, insertOrder.getOverseasPayerInfoId()));
                                    if (isNotEmpty(domesticSuppliersInfos)) {
                                        insertOrder.setOverseasPayerInfoId(domesticSuppliersInfos.get(0).getId());
                                    } else {
                                        List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
                                                .eq(OverseasPayerInfo::getOverseasPayerName, insertOrder.getOverseasPayerInfoId()));
                                        if (isNotEmpty(overseasPayerInfos)) {
                                            insertOrder.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
                                        }
                                    }
//                                } else if ("E".equals(ieFlag)) {
//                                    List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
//                                            .eq(OverseasPayerInfo::getOverseasPayerName, insertOrder.getOverseasPayerInfoId()));
//                                    if (isNotEmpty(overseasPayerInfos)) {
//                                        insertOrder.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
//                                    }
//                                }
                            }
                            // 运输方式
                            if (isNotBlank(insertOrder.getShippingType())) {
                                if (dictMap.containsKey(insertOrder.getShippingType())) {
                                    insertOrder.setShippingType(dictMap.get(insertOrder.getShippingType()));
                                }
                            }
                            // 起运港
                            if (isNotBlank(insertOrder.getArrivalPort())) {
                                if (dictMapPort.containsKey(insertOrder.getArrivalPort())) {
                                    insertOrder.setArrivalPort(dictMapPort.get(insertOrder.getArrivalPort()));
                                }
                            }
                            // 目的港
                            if (isNotBlank(insertOrder.getDesPort())) {
                                if (dictMapPort.containsKey(insertOrder.getDesPort())) {
                                    insertOrder.setDesPort(dictMapPort.get(insertOrder.getDesPort()));
                                }
                            }
                            // 成交方式
                            if (isNotBlank(insertOrder.getTransMode())) {
                                if (dictMap.containsKey(insertOrder.getTransMode())) {
                                    insertOrder.setTransMode(dictMap.get(insertOrder.getTransMode()));
                                }
                            }
                            // 币制
//                            if (isNotBlank(insertOrder.getCurrency())) {
//                                if(dictMap3.containsKey(insertOrder.getCurrency())) {
//                                    insertOrder.setCurrency(dictMap3.get(insertOrder.getCurrency()));
//                                }
//                            }
                            List<OrderDetail> orderDetailList = new ArrayList<>();
                            for (OrderExcelEntity orderExcelEntity : orderExcelEntityList2) {
                                OrderDetail orderDetail = new OrderDetail();
                                BeanUtil.copyProperties(orderExcelEntity, orderDetail, CopyOptions.create().ignoreNullValue());
                                if (isNotBlank(orderDetail.getQunit())) {
                                    orderDetail.setQunit(dictMap1.get(orderDetail.getQunit()));
                                }
                                if (isNotBlank(orderDetail.getCurrency())) {
                                    orderDetail.setCurrency(dictMap3.get(orderDetail.getCurrency()));
                                }
                                orderDetailList.add(orderDetail);
                            }
                            insertOrder.setOrderDetailList(orderDetailList);
                            if ("I".equals(ieFlag)) {
                                insertOrder.setOrderType("2"); // 默认3外销订单
                            } else if ("E".equals(ieFlag)) {
                                insertOrder.setOrderType("3"); // 默认3外销订单
                            }
                            Result<?> result = this.saveOrder(insertOrder);
                            if (!result.isSuccess()) {
                               throw new RuntimeException(result.getMessage());
                            }
                            successLines.getAndIncrement();
                        } else {
                            BeanUtil.copyProperties(orderExcelEntityList2.get(0), order, CopyOptions.create().ignoreNullValue());
                            order.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            order.setUpdateDate(new Date());
                            // 处理委托方
                            if (isNotBlank(orderExcelEntityList2.get(0).getBuyer())) {
                                order.setBuyerName(orderExcelEntityList2.get(0).getBuyer());
                                List<Commissioner> commissioners = commissionerService.list(new LambdaQueryWrapper<Commissioner>()
                                        .eq(Commissioner::getCommissionerFullName, orderExcelEntityList2.get(0).getBuyer()));
                                if (isNotEmpty(commissioners)) {
                                    order.setBuyer(commissioners.get(0).getId());
                                }
                            }
                            // 处理供应商-进口 或 买方/付款方-出口
                            if (isNotBlank(orderExcelEntityList2.get(0).getOverseasPayerInfoId())) {
                                order.setOverseasPayerInfoName(orderExcelEntityList2.get(0).getOverseasPayerInfoId());
//                                if ("I".equals(ieFlag)) {
                                    List<DomesticSuppliersInfo> domesticSuppliersInfos = domesticSuppliersInfoService.list(new LambdaQueryWrapper<DomesticSuppliersInfo>()
                                            .eq(DomesticSuppliersInfo::getSuppliersFullName, orderExcelEntityList2.get(0).getOverseasPayerInfoId()));
                                    if (isNotEmpty(domesticSuppliersInfos)) {
                                        order.setOverseasPayerInfoId(domesticSuppliersInfos.get(0).getId());
                                    } else {
                                        List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
                                                .eq(OverseasPayerInfo::getOverseasPayerName, orderExcelEntityList2.get(0).getOverseasPayerInfoId()));
                                        if (isNotEmpty(overseasPayerInfos)) {
                                            order.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
                                        }
                                    }
//                                } else if ("E".equals(ieFlag)) {
//                                    List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
//                                            .eq(OverseasPayerInfo::getOverseasPayerName, orderExcelEntityList2.get(0).getOverseasPayerInfoId()));
//                                    if (isNotEmpty(overseasPayerInfos)) {
//                                        order.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
//                                    }
//                                }
                            }
                            if (isNotBlank(orderExcelEntityList2.get(0).getShippingType())) {
                                if (dictMap.containsKey(orderExcelEntityList2.get(0).getShippingType())) {
                                    order.setShippingType(dictMap.get(orderExcelEntityList2.get(0).getShippingType()));
                                }
                            }
                            if (isNotBlank(orderExcelEntityList2.get(0).getArrivalPort())) {
                                if (dictMapPort.containsKey(orderExcelEntityList2.get(0).getArrivalPort())) {
                                    order.setArrivalPort(dictMapPort.get(orderExcelEntityList2.get(0).getArrivalPort()));
                                }
                            }
                            if (isNotBlank(orderExcelEntityList2.get(0).getDesPort())) {
                                if (dictMapPort.containsKey(orderExcelEntityList2.get(0).getDesPort())) {
                                    order.setDesPort(dictMapPort.get(orderExcelEntityList2.get(0).getDesPort()));
                                }
                            }
                            // 成交方式
                            if (isNotBlank(orderExcelEntityList2.get(0).getTransMode())) {
                                if (dictMap11.containsKey(orderExcelEntityList2.get(0).getTransMode())) {
                                    order.setTransMode(dictMap11.get(orderExcelEntityList2.get(0).getTransMode()));
                                }
                            }
                            // 币制
//                            if (isNotBlank(orderExcelEntityList2.get(0).getCurrency())) {
//                                if(dictMap3.containsKey(orderExcelEntityList2.get(0).getCurrency())) {
//                                    order.setCurrency(dictMap3.get(orderExcelEntityList2.get(0).getCurrency()));
//                                }
//                            }
                            List<OrderDetail> orderDetailList = new ArrayList<>();
                            for (OrderExcelEntity orderExcelEntity : orderExcelEntityList2) {
                                OrderDetail orderDetail = new OrderDetail();
                                BeanUtil.copyProperties(orderExcelEntity, orderDetail, CopyOptions.create().ignoreNullValue());
                                if (isNotBlank(orderDetail.getQunit())) {
                                    orderDetail.setQunit(dictMap1.get(orderDetail.getQunit()));
                                }
                                if (isNotBlank(orderDetail.getCurrency())) {
                                    orderDetail.setCurrency(dictMap3.get(orderDetail.getCurrency()));
                                }
                                orderDetailList.add(orderDetail);
                            }
                            order.setOrderDetailList(orderDetailList);
                            Result<?> result = this.saveOrder(order);
                            if (!result.isSuccess()) {
                                throw new RuntimeException(result.getMessage());
                            }
                            successLines.getAndIncrement();
                        }
                    } catch (Exception e) {
                        errorLines.getAndIncrement();
                        log.error(e.getMessage(), e);
                        errorMessage.add(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败：" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                    log.error(e.getMessage(), e);
                }
            }

        }
        if (isNotEmpty(errorMessage)) {
            return Result.error("成功导入行数："+ successLines.get() + "；失败行数：" + errorLines.get() + "；" + CollUtil.join(errorMessage, ","));
        }
        return ImportExcelUtil.imporReturnRes(errorLines.get(), successLines.get(), errorMessage);
    }

    /**
     * 导出订单列表Excel
     *
     * @param request
     * @param response
     * @param order
     * @return void
     * <AUTHOR>
     * @date 2024/9/20 10:02
     */
    @Override
    public void exportOrderXls(HttpServletRequest request, HttpServletResponse response, Order order) {
        List<Order> orderList;
        if (isNotBlank(order.getIds())) {
            orderList = this.listByIds(Arrays.asList(order.getIds().split(",")));
        } else {
            Result<?> result = this.queryPageList(1, 1000000, order, request);
            if (result.isSuccess()) {
                orderList = ((IPage<Order>) result.getResult()).getRecords();
            } else {
                throw new RuntimeException(result.getMessage());
            }
        }
        // 运输方式
        List<DictModel> dictModels = sysBaseApi.getDictItems("trans_type");
        Map<String, String> dictMap = new HashMap<>();
        if (isNotEmpty(dictModels)) {
            dictModels.forEach(dictModel -> {
                dictMap.put(dictModel.getValue(), dictModel.getText());
            });
        }
        // 成交方式
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("trading_type");
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getValue(), dictModel.getText());
            });
        }
        // 起运港 目的港
        List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_cityports,cnname,cityport_code,isenabled=1");
        Map<String, String> dictMap2 = new HashMap<>();
        if (isNotEmpty(dictModels2)) {
            dictModels2.forEach(dictModel -> {
                dictMap2.put(dictModel.getValue(), dictModel.getText());
            });
        }
        // 币制
//        List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,name,code,currency,1=1");
//        Map<String, String> dictMap3 = new HashMap<>();
//        if (isNotEmpty(dictModels3)) {
//            dictModels3.forEach(dictModel -> {
//                dictMap3.put(dictModel.getValue(), dictModel.getText());
//            });
//        }
        if (isNotEmpty(orderList)) {
            for (Order o : orderList) {
                // 运输方式
                if (isNotBlank(o.getShippingType())) {
                    if (dictMap.containsKey(o.getShippingType())) {
                        o.setShippingType(dictMap.get(o.getShippingType()));
                    }
                }
                // 成交方式
                if (isNotBlank(o.getTransMode())) {
                    if (dictMap1.containsKey(o.getTransMode())) {
                        o.setTransMode(dictMap1.get(o.getTransMode()));
                    }
                }
                // 起运港
                if (isNotBlank(o.getArrivalPort())) {
                    if (dictMap2.containsKey(o.getArrivalPort())) {
                        o.setArrivalPort(dictMap2.get(o.getArrivalPort()));
                    }
                }
                // 目的港
                if (isNotBlank(o.getDesPort())) {
                    if (dictMap2.containsKey(o.getDesPort())) {
                        o.setDesPort(dictMap2.get(o.getDesPort()));
                    }
                }
                // 币制
//                if (isNotBlank(o.getCurrency())) {
//                    if (dictMap3.containsKey(o.getCurrency())) {
//                        o.setCurrency(dictMap3.get(o.getCurrency()));
//                    }
//                }
            }
        }
        ExportParams params = new ExportParams();
        params.setSheetName("订单列表导出");
        params.setType(ExcelType.XSSF);
        params.setStyle(ExcelExportStylerBorderImpl.class);
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> mapInv = new HashMap<>();
        mapInv.put("title", params);
        mapInv.put("entity", Order.class);
        mapInv.put("data", orderList);
        list.add(mapInv);
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 订单生成进口业务
     *
     * @param ids
     * @param ieFlag
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/24 10:47
     */
    @Override
    public Result<?> getCreateBusiness(String ids, String ieFlag, String type) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setIeFlag(ieFlag);
        orderInfo.setOrderStatus(1);
        List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
        List<Order> orderList = this.listByIds(Arrays.asList(ids.split(",")));
        if (isNotEmpty(orderList)) {
            BeanUtil.copyProperties(orderList.get(0), orderInfo, CopyOptions.create().ignoreNullValue());
            orderInfo.setId(null);
            orderInfo.setRelOrderNo(orderList.stream().map(Order::getOrderNo).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.joining(",")));
//            orderInfo.setCustomsDeclarationCurrency(orderList.get(0).getCurrency());
            orderInfo.setPayment(orderList.stream().map(Order::getPaymentClause).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.joining(",")));
            orderInfo.setExportContractNo(orderList.stream().map(Order::getContractNo).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.joining(","))); // 合同号 20241021
            orderInfo.setInvoiceNo(orderList.get(0).getInvoiceNo()); // 发票号20250210+
            orderInfo.setTotalContractAmount(orderList.stream().map(Order::getTotalAmount).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderInfo.setPortDestination(orderList.get(0).getDesPort()); // 目的港 -- 默认第一个
            orderInfo.setRemarks(orderList.stream().map(Order::getRemark).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.joining(",")));
            orderInfo.setTradingType(isNotBlank(orderList.get(0).getTransMode()) ? Integer.valueOf(orderList.get(0).getTransMode()) : null); // 成交方式  -- 默认第一个
            // 2025/3/3 14:09@ZHANGCHAO 追加/变更/完善：贸易国直接取订单的地址截取！！
            if (isNotBlank(orderList.get(0).getSellerAddress())) {
                String sellerAddress = orderList.get(0).getSellerAddress().trim();
                // 匹配最后一个逗号后面的内容
                Pattern pattern = Pattern.compile(".*,\\s*([A-Za-z\\s]+)\\s*$");
                Matcher matcher = pattern.matcher(sellerAddress);
                String tradeCountryEnName = "";
                String tradeCountry = "";
                if (matcher.find()) {
                    tradeCountryEnName = matcher.group(1).trim();
                }
                log.info("获取到的国家英文代码是：{}", tradeCountryEnName);
                if (isNotBlank(tradeCountryEnName)) {
                    DictQuery dictQuery = baseMapper.getDictByEnName(tradeCountryEnName);
                    if (isNotEmpty(dictQuery)) {
                        tradeCountry = dictQuery.getCode();
                    }
                }
                log.info("最终的国家三位代码是：{}", tradeCountry);
                orderInfo.setTradingCountry(isNotBlank(tradeCountry) ? tradeCountry : orderInfo.getTradingCountry());
                orderInfo.setCountryArrival(isNotBlank(tradeCountry) ? tradeCountry : orderInfo.getCountryArrival());
            }
            orderList.forEach(order -> {
                List<OrderDetail> orderDetailList = orderDetailService.list(new LambdaQueryWrapper<OrderDetail>()
                        .eq(OrderDetail::getOrderId, order.getId()));
                if (isNotEmpty(orderDetailList)) {
                    AtomicInteger i = new AtomicInteger(1);
                    for (OrderDetail orderDetail : orderDetailList) {
                        if (orderDetail.getExecQty().compareTo(BigDecimal.ZERO) <= 0) {
                            continue; // 无可执行数量了。。
                        }
                        OrderProductInfo orderProductInfo = new OrderProductInfo();
                        BeanUtil.copyProperties(orderDetail, orderProductInfo, CopyOptions.create().ignoreNullValue());
                        orderProductInfo.setId(null);
                        orderProductInfo.setPn(orderDetail.getCopGno());
                        orderProductInfo.setChineseName(orderDetail.getPn());
                        orderProductInfo.setEnglishName(orderDetail.getPnEn());
                        orderProductInfo.setSpecificationModel(orderDetail.getModel());
                        orderProductInfo.setCustomsDeclarationCurrency(orderDetail.getCurrency());
                        // 2024/12/31 14:18@ZHANGCHAO 追加/变更/完善：查询此订单的可执行的数量！ - 去已经生成业务的数量！
//                        orderProductInfo.setShipmentQuantity(orderDetail.getQty());
                        orderProductInfo.setShipmentQuantity(orderDetail.getExecQty()); // 改为可执行数量
                        orderProductInfo.setExecQty(orderDetail.getExecQty()); // 前端判断用
                        orderProductInfo.setOrderDetailId(orderDetail.getId()); // 新加

                        orderProductInfo.setDecQty(orderDetail.getQty());
                        orderProductInfo.setShipmentUnit(orderDetail.getQunit());
                        orderProductInfo.setDecQunit(orderDetail.getQunit());
                        orderProductInfo.setShipmentUnitPrice(orderDetail.getPrice());
                        orderProductInfo.setShipmentGoodsValue(orderDetail.getAmount());
                        orderProductInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                        orderProductInfo.setCreateTime(new Date());
                        orderProductInfo.setSequence(i.getAndIncrement());
                        // 2025/3/7 14:53@ZHANGCHAO 追加/变更/完善： 华熙净重赋值数量！！
                        orderProductInfo.setNetWeight(orderProductInfo.getShipmentQuantity());
                        /*
                         * 订单生成业务时，如果没有申报要素，则去商品库带取一次！！
                         * 2025/4/3 15:37@ZHANGCHAO
                         */
                        if (isBlank(orderProductInfo.getHscode()) || isBlank(orderProductInfo.getHsname()) || isBlank(orderProductInfo.getHsmodel())) {
                            List<ProductInfo> productInfos = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                                    .eq(ProductInfo::getPn, orderProductInfo.getPn())
                                    .eq(isNotBlank(orderProductInfo.getChineseName()), ProductInfo::getChineseName, orderProductInfo.getChineseName())
                                    .eq(isNotBlank(orderProductInfo.getEnglishName()), ProductInfo::getEnglishName, orderProductInfo.getEnglishName()));
                            if (isNotEmpty(productInfos)) {
                                orderProductInfo.setChineseName(isNotBlank(orderProductInfo.getChineseName()) ? orderProductInfo.getChineseName() : productInfos.get(0).getChineseName());
                                orderProductInfo.setEnglishName(isNotBlank(orderProductInfo.getEnglishName()) ? orderProductInfo.getEnglishName() : productInfos.get(0).getEnglishName());
                                orderProductInfo.setProductSpecificationModel(productInfos.get(0).getProductSpecificationModel());
                                orderProductInfo.setCustomsCodeInfoCode(productInfos.get(0).getCustomsCodeInfoCode());
                                orderProductInfo.setHscode(productInfos.get(0).getCustomsCodeInfoCode());
                                orderProductInfo.setHsname(productInfos.get(0).getHsname());
                                String hsmodel = productInfos.get(0).getCustomsDeclarationElements();
                                if (isNotBlank(hsmodel) && hsmodel.contains("{}")) {
                                    if (isNotBlank(orderInfo.getExportContractNo())) {
                                        Pattern pattern = Pattern.compile("-(\\d{8})-");
                                        Matcher matcher = pattern.matcher(orderInfo.getExportContractNo());
                                        if (matcher.find()) {
                                            String dateStr = matcher.group(1);
                                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                                            LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                                            String formattedDate = date.format(outputFormatter);
                                            log.info("提取的日期: {}", dateStr);
                                            log.info("格式化后的日期: {}", formattedDate);
                                            hsmodel = hsmodel.replace("{}", formattedDate);
                                        } else {
                                            log.info("无法从合同编号提取日期: {}", order.getContractNo());
                                        }
                                    }
                                }
                                orderProductInfo.setHsmodel(hsmodel);
                            }
                        }
                        orderProductInfoList.add(orderProductInfo);
                    }
                }
            });
        }
        orderInfo.setProductList(orderProductInfoList);
        orderInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
        orderInfo.setCreateTime(new Date());
        return Result.ok(orderInfo);
    }

    /**
     * 从品名库重新带取数据
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/2/27 11:19
     */
    @Override
    public Result<?> getRebringData(String ids) {
        if (isBlank(ids)) {
            return Result.error("未知的订单商品！");
        }
        List<OrderDetail> orderDetails = orderDetailService.listByIds(Arrays.asList(ids.split(",")));
        if (isEmpty(orderDetails)) {
            return Result.error("未知的订单商品！");
        }
        List<OrderDetail> orderDetailList = new ArrayList<>();
        Map<String, Order> orderMap = new HashMap<>();
        for (OrderDetail orderDetail : orderDetails) {
            Order order;
            if (orderMap.containsKey(orderDetail.getOrderId())) {
                order = orderMap.get(orderDetail.getOrderId());
            } else {
                order = this.getById(orderDetail.getOrderId());
                orderMap.put(orderDetail.getOrderId(), isNotEmpty(order) ? order : new Order());
            }

            List<ProductInfo> productInfos = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                    .eq(ProductInfo::getPn, orderDetail.getCopGno())
                    .eq(isNotBlank(orderDetail.getPn()), ProductInfo::getChineseName, orderDetail.getPn())
                    .eq(isNotBlank(orderDetail.getPnEn()), ProductInfo::getEnglishName, orderDetail.getPnEn()));
            if (isNotEmpty(productInfos)) {
                orderDetail.setRelGoodsId(productInfos.get(0).getId());
                orderDetail.setPn(productInfos.get(0).getChineseName());
                orderDetail.setPnEn(productInfos.get(0).getEnglishName());
                orderDetail.setModel(productInfos.get(0).getProductSpecificationModel());
                orderDetail.setHscode(productInfos.get(0).getCustomsCodeInfoCode());
                orderDetail.setHsname(productInfos.get(0).getHsname());
                // 2025/2/28 18:01@ZHANGCHAO 追加/变更/完善：针对品名库中有变量的！！
                String hsmodel = productInfos.get(0).getCustomsDeclarationElements();
                if (isNotBlank(hsmodel) && hsmodel.contains("{}")) {
                    if (isNotBlank(order.getContractNo())) {
                        Pattern pattern = Pattern.compile("-(\\d{8})-");
                        Matcher matcher = pattern.matcher(order.getContractNo());
                        if (matcher.find()) {
                            String dateStr = matcher.group(1);
                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                            LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                            String formattedDate = date.format(outputFormatter);
                            log.info("提取的日期: {}", dateStr);
                            log.info("格式化后的日期: {}", formattedDate);
                            hsmodel = hsmodel.replace("{}", formattedDate);
                        } else {
                            log.info("无法从合同编号提取日期: {}", order.getContractNo());
                        }
                    }
                }
                orderDetail.setHsmodel(hsmodel);
                orderDetailService.updateById(orderDetail);
            } else {
                orderDetail.setRelGoodsId(null);
            }
            orderDetailList.add(orderDetail);
        }
        return Result.ok(orderDetailList);
    }

    /**
     * 利用正则表达式判断字符串是否是数字
     * @param str
     * @return
     */
    private boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if( !isNum.matches() ){
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> importOrderByYSJ(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");
        if (file.isEmpty()) {
            return Result.error("文件不能为空");
        }
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 读取Excel文件
        try (InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream)){
        List<Order> orderList = new ArrayList<>();
        //循环Workbook里的所有sheet 每个sheet为一个订单
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            Order order = new Order();
            //订单号 为该sheetA列第二行的内容，并且进行截取，从第四位开始到第一个‘（’出现之前的内容
            String orderNo = sheet.getRow(1).getCell(0).getStringCellValue().
                    substring(3, sheet.getRow(1).getCell(0).getStringCellValue().indexOf("（"));
            order.setOrderNo(orderNo);
            // 境外客户 获取B列第9行内容
            String overseasPayerInfoName = sheet.getRow(8).getCell(1).getStringCellValue();
            order.setOverseasPayerInfoName(overseasPayerInfoName);
            order.setBuyerName("青岛亚是加食品有限公司");
            order.setBuyer("1874641044649848834");
            //签订日期 为Q6内容
            Date signDate = sheet.getRow(5).getCell(16).getDateCellValue();
            order.setSignDate(signDate);
            //收汇方式  获取G17
            String paymentClause = sheet.getRow(16).getCell(6).getStringCellValue();
            order.setPaymentClause(paymentClause);
            //启运港 获取L17
            String arrivalPort = sheet.getRow(16).getCell(11).getStringCellValue();
            order.setArrivalPort(arrivalPort);
            //交货日期 获取C18
            Date deliveryDate = sheet.getRow(17).getCell(2).getDateCellValue();
            order.setDeliveryDate(deliveryDate);//交货日期
            //成交方式 获取G18
            String transMode = sheet.getRow(17).getCell(6).getStringCellValue();
            order.setTransMode(transMode);
            //目的港 获取L18
            String desPort = sheet.getRow(17).getCell(11).getStringCellValue();
            order.setDesPort(desPort);
            //运输条款 获取G19
            String transportClause = sheet.getRow(18).getCell(6).getStringCellValue();
            order.setTransportClause(transportClause);
            //备注存放唛头信息 获取F28
            String remark = sheet.getRow(27).getCell(5).getStringCellValue();
            order.setRemark(remark);
            order.setCreateBy(loginUser.getUsername());
            order.setCreateDate(new Date());
            order.setOrderType("3");
            order.setIeFlag("E");
            order.setTenantId(Long.valueOf(loginUser.getTenantId()));
            //获取表体商品的信息，为从第25行开始，一直到A列为空结束
            List<OrderDetail> orderDetailList = new ArrayList<>();
            for (int j = 24; j < sheet.getLastRowNum(); j++) {
                Row row = sheet.getRow(j);
                //判断row.getCell(0)等于null或者等于空串
                log.info("row.getCell(0)的值为：{}",  row.getCell(0).getNumericCellValue());
                if (row.getCell(0) == null || (row.getCell(0).getCellType() == CellType.NUMERIC ?
                        row.getCell(0).getNumericCellValue() == 0 : row.getCell(0).getStringCellValue().trim().equals(""))) {
                    break;
                }
                OrderDetail orderDetail = new OrderDetail();
                //物料号 获取A列 //需判断是否为num 不是num用getStringCellValue
                // 获取物料号，处理数字类型避免出现 .0 后缀
                String copGno;
                if (row.getCell(0) != null && row.getCell(0).getCellType() == CellType.NUMERIC) {
                    double numericValue = row.getCell(0).getNumericCellValue();
                    // 判断是否为整数
                    if (numericValue == (long) numericValue) {
                        copGno = String.valueOf((long) numericValue);
                    } else {
                        copGno = String.valueOf(numericValue);
                    }
                } else {
                    copGno = row.getCell(0) != null ? row.getCell(0).getStringCellValue() : "";
                }
                orderDetail.setCopGno(copGno);
                //品名 获取B列  //需判断是否为num 不是num用getStringCellValue
                String pn = row.getCell(1).getCellType() == CellType.NUMERIC?
                        String.valueOf( row.getCell(1).getNumericCellValue()) : row.getCell(1).getStringCellValue();
                orderDetail.setPn(pn);
                //获取D,E,F三个值拼接成规格型号
                String model = row.getCell(3).getNumericCellValue() +
                        "g/个-" + row.getCell(4).getNumericCellValue() +
                        "(袋)-" + row.getCell(5).getNumericCellValue()+"个(袋)/箱";
                orderDetail.setModel(model);
                //数量 获取G列 //需判断是否为num 不是num用getStringCellValue
                String qty = row.getCell(6).getCellType() == CellType.NUMERIC?
                        String.valueOf( row.getCell(6).getNumericCellValue()) : row.getCell(6).getStringCellValue();
                orderDetail.setQty(new BigDecimal(qty));
                //将数量赋值为可执行数量
                orderDetail.setExecQty(new BigDecimal(qty));
                //单价 获取K 列 //需判断是否为num 不是num用getStringCellValue
                String price = row.getCell(10).getCellType() == CellType.NUMERIC?
                        String.valueOf(row.getCell(10).getNumericCellValue()) : row.getCell(10).getStringCellValue();
                orderDetail.setPrice(new BigDecimal(price));
                //总价 直接用单价*数量
                orderDetail.setAmount(new BigDecimal(price).multiply(new BigDecimal(qty)));
                //币制固定USD
                orderDetail.setCurrency("USD");
                orderDetail.setOrderNo(orderNo);
                orderDetail.setCreateBy(loginUser.getUsername());
                orderDetail.setCreateDate(new Date());
                orderDetailList.add(orderDetail);
            }
            order.setOrderDetailList(orderDetailList);
            //汇总订单总金额 = orderDetailList的amount之和 需要排除amount为null的情况
            BigDecimal totalAmount = orderDetailList.stream().map(OrderDetail::getAmount).filter(
                    Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            order.setTotalAmount(totalAmount);
            orderList.add(order);
        }
        saveOrderByImportYSJ(orderList);
        return Result.OK("订单导入成功，共导入 " + orderList.size() + " 条订单");
    } catch (Exception e) {
        // 事务会自动回滚
        log.error("订单导入失败", e);
        return Result.error("订单导入失败：请联系管理员");
    }
    }

    private void saveOrderByImportYSJ(List<Order> orderList){
        List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list();
        List<DictModelVO> exchangeCollectionTypeDict = decListMapper.getDictItemByCode("exchange_collection_type");
        List<DictModelVO> tradingTypeDict = decListMapper.getDictItemByCode("trading_type");
        List<ErpCityports> erpCityports = erpCityportsMapper.selectList(new LambdaQueryWrapper<ErpCityports>()
                .eq(ErpCityports::getIsenabled, 1));
        for(Order order : orderList){
            if(isNotBlank(order.getOverseasPayerInfoName())){
                //转换境外客户 java8方式获取（List<OverseasPayerInfo>接收） 通过OverseasPayerInfoName overseasPayerInfos 并赋值OverseasPayerInfoid
                Optional<OverseasPayerInfo> overseasPayerInfoOptional =
                        overseasPayerInfos.stream().filter(overseasPayerInfo ->
                                overseasPayerInfo.getOverseasPayerName().equals(order.getOverseasPayerInfoName())).findFirst();
                order.setOverseasPayerInfoId(overseasPayerInfoOptional.isPresent() ? overseasPayerInfoOptional.get().getId() : null);
            }
            if(isNotBlank(order.getArrivalPort())){
                //转换起运港 java8方式获取（List<ErpCityports>接收） 通过ArrivalPort erpCityports 并赋值ArrivalPortEn
                Optional<ErpCityports> erpCityportsOptional =
                        erpCityports.stream().filter(erpCityport ->
                                erpCityport.getCnname().equals(order.getArrivalPort())).findFirst();
                order.setArrivalPort(erpCityportsOptional.isPresent()? erpCityportsOptional.get().getCityportCode() : null);
            }
            if(isNotBlank(order.getDesPort())){
                //转换目的港 java8方式获取（List<ErpCityports>接收） 通过DesPort erpCityports 并赋值DesPortEn
                Optional<ErpCityports> erpCityportsOptional2 =
                        erpCityports.stream().filter(erpCityport ->
                                erpCityport.getCnname().equals(order.getDesPort())).findFirst();
                order.setDesPort(erpCityportsOptional2.isPresent()? erpCityportsOptional2.get().getCityportCode() : null);
            }
            if(isNotBlank(order.getPaymentClause())){
                Optional<DictModelVO> dictModelVOOptional =
                        exchangeCollectionTypeDict.stream().filter(dictModelVO ->
                                dictModelVO.getText().equals(order.getPaymentClause())).findFirst();
                order.setPaymentClause(dictModelVOOptional.isPresent()? dictModelVOOptional.get().getValue() : null);
            }
            if(isNotBlank(order.getTransMode())){
                //成交方式转换
                Optional<DictModelVO> dictModelVOOptional2 =
                        tradingTypeDict.stream().filter(dictModelVO ->
                                dictModelVO.getText().equals(order.getTransMode())).findFirst();
                order.setTransMode(dictModelVOOptional2.isPresent()? dictModelVOOptional2.get().getValue() : null);
            }
            //根据订单号查询是否存在
            Order order1 = this.getOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderNo, order.getOrderNo()));
            //新增
            if(isEmpty(order1)){
                super.save(order);
                //将order的orderDetailList中的orderId赋值为order的id
                order.getOrderDetailList().stream().forEach(orderDetail -> {
                    orderDetail.setOrderId(order.getId());
                    orderDetailService.save(orderDetail);
                });
            }else {
                //更新
                BeanUtil.copyProperties(order, order1, CopyOptions.create().ignoreNullValue());
                super.updateById(order1);
                //将该订单下表体全删除，重新全部添加
                orderDetailService.remove(new LambdaQueryWrapper<OrderDetail>().eq(OrderDetail::getOrderId, order1.getId()));
                order.getOrderDetailList().stream().forEach(orderDetail -> {
                    orderDetail.setOrderId(order1.getId());
                    orderDetailService.save(orderDetail);
                });
            }
        }
    }
}
