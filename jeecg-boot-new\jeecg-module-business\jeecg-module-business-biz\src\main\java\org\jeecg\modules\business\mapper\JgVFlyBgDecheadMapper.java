package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.business.entity.JgVFlyBgDechead;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface JgVFlyBgDecheadMapper extends BaseMapper<JgVFlyBgDechead> {

    IPage<JgVFlyBgDechead> queryPageList(Page<JgVFlyBgDechead> page, String customerName, String starDate, String lastDate);

    IPage<JgVFlyBgDechead> queryPageListCommon(Page<JgVFlyBgDechead> page, String ownerCode, String ownerName, String startDate, String lastDate, String isAll);

    IPage<JgVFlyBgDechead> queryPageListSBCommon(Page<JgVFlyBgDechead> page, String agentCode, String agentName, String startDate, String lastDate, String isAll);
}
