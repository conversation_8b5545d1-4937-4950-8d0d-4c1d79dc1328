<template>
	<div>
		<a-modal :confirm-loading="confirmLoading" :destroyOnClose="true" :maskClosable="false"
			:ok-button-props="{ props: { disabled: fileList.length === 0, loading: uploadLoading } }" :title="title"
			:visible="visible" :width="900" cancel-text="取消" ok-text="上传" @cancel="handleCancel" @ok="handleUpload">
			<template slot="footer">
				<div class="modal-footer">
					<div class="left-section">
						<span class="tipsText">
							<a-icon style="margin-right: 8px" theme="twoTone" type="exclamation-circle" />
							支持文件格式.pdf、.docx、.doc、.jpg、.jpeg、.png、.xlsx、.xls、.zip。准确率99%以上。
						</span>
					</div>
					<div class="right-section">
						<a-tag color="blue">当前账户余额: <span
								:class="{ 'footer-balance-amount': true, 'footer-balance-low': currentBalance <= 5 }">{{
									currentBalance.toFixed(2) }}</span>
							元 <a class="recharge-link" @click="goToRecharge">我要充值</a></a-tag>
						<a-button type="default" @click="handleCancel">取消</a-button>
						<a-button :loading="confirmLoading" type="primary" @click="handleUpload">上传</a-button>
					</div>
				</div>
			</template>
			<j-form-container :disabled="disableSubmit">
				<a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules">
					<a-row :gutter="24" justify="start" type="flex">
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="模版选择" prop="tplName">
								<a-select v-model="model.tplName" allowClear placeholder="请选择" showSearch
													@change="handleTplChange">
									<a-select-option
										v-for="tpl in tplList"
										:key="tpl.value"
									:value="tpl.value">
									{{ tpl.label }}
									</a-select-option>
								</a-select>
							</a-form-model-item>
						</a-col>
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出口标识" prop="ieFlag">
								<a-select v-model="model.ieFlag" defaultValue="E" placeholder="请选择" showSearch
									@change="handleChange">
									<a-select-option value="I">进口</a-select-option>
									<a-select-option value="E">出口</a-select-option>
								</a-select>
							</a-form-model-item>
						</a-col>
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="收发货人" prop="customerName">
								<a-input v-model="model.customerName" placeholder='请输入收发货人'></a-input>
							</a-form-model-item>
						</a-col>
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申报地海关" prop="declarePlace">
								<j-dict-select-tag v-model="model.declarePlace" dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
																	 placeholder="请选择，支持搜索" type="node-limit" />
							</a-form-model-item>
						</a-col>
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出境关别" prop="outPortCode">
								<j-dict-select-tag v-model="model.outPortCode" dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
																	 placeholder="请选择，支持搜索" type="node-limit" />
							</a-form-model-item>
						</a-col>
						<a-col :span="8">
							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="运输方式" prop="shipTypeCode">
								<j-dict-select-tag v-model="model.shipTypeCode" dictCode="trans_type"
																	 placeholder="请选择，支持搜索" type="node-limit" />
							</a-form-model-item>
						</a-col>
<!--						<a-col :span="8">-->
<!--							<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="价格基准" prop="priceReference">-->
<!--								<a-select v-model="model.priceReference" allowClear defaultValue="1" placeholder="请选择" showSearch>-->
<!--									<a-select-option value="1">单价</a-select-option>-->
<!--									<a-select-option value="2">总价</a-select-option>-->
<!--								</a-select>-->
<!--							</a-form-model-item>-->
<!--						</a-col>-->
						<!-- <a-col :span="8">
							<a-form-model-item label="AI模型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="modelProvider">
								<a-select placeholder="请选择" allowClear showSearch v-model="model.modelProvider" defaultValue="bailian">
									<a-select-option value="bailian">阿里云百炼</a-select-option>
									<a-select-option value="doubao">豆包</a-select-option>
									<a-select-option value="deepseek">DeepSeek</a-select-option>
									<a-select-option value="ollama">ollama</a-select-option>
									<a-select-option value="vllm">VLLM</a-select-option>
								</a-select>
							</a-form-model-item>
						</a-col> -->
					</a-row>
				</a-form-model>
			</j-form-container>
			<a-upload-dragger :before-upload="beforeUpload" :file-list="fileList" :multiple="true" :remove="handleRemove"
				name="file">
				<p class="ant-upload-drag-icon">
					<a-icon type="inbox" />
				</p>
				<p class="ant-upload-text">单击或拖动文件到此区域上传，默认生成一票报关单</p>
				<p class="ant-upload-hint">
					文件合计大小不能超过10MB
				</p>
			</a-upload-dragger>
		</a-modal>

		<!-- 余额不足提示弹窗 -->
		<a-modal :footer="null" :visible="rechargeModalVisible" title="账户余额不足" @cancel="closeRechargeModal">
			<div class="balance-info">
				<a-alert show-icon type="warning">
					<div slot="message">
						当前账户余额: <span class="balance-amount">{{ currentBalance.toFixed(2) }}</span> 元
						<p>余额不足，无法使用关智宝™️服务，请先充值。</p>
					</div>
				</a-alert>

				<div class="action-buttons">
					<a-button @click="closeRechargeModal">取消</a-button>
					<a-button type="primary" @click="goToRecharge">
						<a-icon type="dollar" />去充值
					</a-button>
				</div>
			</div>
		</a-modal>
	</div>
</template>
<script>
import { getAction, uploadAction } from "@/api/manage";
import Vue from "vue";
import { ACCESS_TOKEN, TENANT_ID } from "@/store/mutation-types";

export default {
	name: "AiMakerModal",
	data() {
		return {
			title: '关智宝',
			visible: false,
			fileList: [],
			tplList: [],
			confirmLoading: false,
			uploadLoading: false,
			disableSubmit: false,
			headers: {},
			rechargeModalVisible: false,
			currentBalance: 0,
			labelCol: {
				xs: { span: 24 },
				sm: { span: 7 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			decId: null,
			model: {
				ieFlag: 'E',
				modelProvider: 'bailian',
				priceReference: '1'
			},
			validatorRules: {
			},
			url: {
				aiMaker: '/DecHead/dec-head/aiMaker',
				getAiSettings: '/DecHead/dec-head/listAiSettings',
				queryBalance: '/sys/sso/queryBalance',
			}
		}
	},
	created() {
		const token = Vue.ls.get(ACCESS_TOKEN)
		this.headers = { "X-Access-Token": token }
		let tenantId = Vue.ls.get(TENANT_ID)
		if (!tenantId) {
			tenantId = 0
		}
		this.dictOwner =
			'ai_config,tpl_name,id,tenant_id=' + tenantId
		this.loadDictOptions()
	},
	methods: {
		// 查询模版
		async loadDictOptions() {
			this.tplList = []
			await getAction(`/sys/dict/getDictItems/${this.dictOwner}`,{}).then(res=>{
				if (res.success && res.result && res.result.length > 0) {
					this.tplList = res.result.filter(item => {
						return item.text && item.text.toString().trim() !== ''
					})
						.map(item => ({ value: item.text, label: item.text }))
				} else {
					console.error('getDictItems error: : ', res)
					this.tplList = []
				}
			})
		},
		/**
		 * 先校验账户余额
		 */
		async checkBalance() {
			let tenantId = Vue.ls.get(TENANT_ID)
			if (!tenantId) {
				tenantId = 0
			}
			try {
				const res = await getAction(this.url.queryBalance, { tenantId: tenantId });

				if (res && res.success) {
					const balance = parseFloat(res.result);
					this.currentBalance = balance; // 保存余额到组件数据中

					if (isNaN(balance)) { // 检查转换后的余额是否为有效数字
						console.log('获取账户余额失败：无效的余额数据');
						this.$message.error('获取账户余额失败，请联系管理员');
						return false;
					}

					if (balance >= 5) {
						console.log('账户余额充足，继续往下走...');
						return true; // 余额充足
					} else if (balance > 0 && balance < 5) {
						this.$message.warning(`温馨提示：您的企业账户余额为 ${balance.toFixed(2)} 元，已不足5元，请及时充值！`);
						return true;
					} else { // balance <= 0
						this.showRechargeModal(balance);
						return false;
					}
				} else {
					const errorMessage = res && res.message ? res.message : '查询余额接口调用失败，请稍后重试或联系管理员';
					this.$message.error(errorMessage);
					return false; // API调用失败或业务失败，阻止操作
				}
			} catch (error) {
				this.$message.error('检查账户余额时发生网络或系统错误，请联系管理员');
				return false;
			}
		},

		/**
		 * 显示充值提示弹窗
		 */
		showRechargeModal(balance) {
			this.currentBalance = balance;
			this.rechargeModalVisible = true;
		},

		/**
		 * 跳转到充值页面
		 */
		goToRecharge() {
			window.open('https://www.jgsoft.com.cn/member/#/business/accountRecharge', '_blank');
			this.rechargeModalVisible = false;
		},

		/**
		 * 关闭充值弹窗
		 */
		closeRechargeModal() {
			this.rechargeModalVisible = false;
		},
		async show() {
			await this.loadDictOptions()
			// 先查询企业余额
			const canProceed = await this.checkBalance()
			if (!canProceed) {
				 return;
			}
			this.model = {
				ieFlag: 'E',
				modelProvider: 'bailian',
			}
			let params = {
				ieFlag: this.model.ieFlag
			}
			await this.setModelValue(params)
			this.visible = true
		},
		async handleTplChange(selectedValue) {
			console.log('Selected Value:', selectedValue);
			let params = {
				tplName: selectedValue
			}
			await this.setModelValue(params)
		},
		async handleChange(value) {
			let params = {
				ieFlag: value
			}
			await this.setModelValue(params)
		},
		/**
		 * 设置数据
		 * @param params
		 * @returns {Promise<void>}
		 */
		async setModelValue(params) {
			const res = await getAction(this.url.getAiSettings, params)
			if (res && res.success) {
				if (res.result && res.result.length > 0) {
					const config = res.result[0];
					this.model = Object.assign({}, this.model, {
						tplName: config.tplName,
						ieFlag: config.ieFlag,
						declarePlace: config.declarePlace,
						outPortCode: config.outPortCode,
						shipTypeCode: config.shipTypeCode,
						customerName: config.optUnitName,
						priceReference: config.priceReference
					});
					if (this.$refs.form) {
						this.$nextTick(() => {
							this.$refs.form.setFieldsValue({
								tplName: config.tplName,
								ieFlag: config.ieFlag,
								declarePlace: config.declarePlace,
								outPortCode: config.outPortCode,
								shipTypeCode: config.shipTypeCode,
								customerName: config.optUnitName,
								priceReference: config.priceReference
							});
						});
					}
				}
			}
		},
		handleRemove(file) {
			const index = this.fileList.indexOf(file)
			const newFileList = this.fileList.slice()
			newFileList.splice(index, 1)
			this.fileList = newFileList
		},
		beforeUpload(file) {
			// 判断单个文件是否小于10MB
			const isLt10M = file.size / 1024 / 1024 < 10;
			if (!isLt10M) {
				this.$message.error('单个文件不能超过10MB，请重新选择文件');
				return false;
			}

			// 计算添加当前文件后的总大小
			let totalSize = this.getTotalFileSize() + file.size;
			if (totalSize / 1024 / 1024 > 10) {
				this.$message.error('所有文件大小总和不能超过10MB，请重新选择文件');
				this.fileList = []
				return false;
			}

			this.fileList.push(file)
			return false
		},
		// 计算当前文件列表的总大小
		getTotalFileSize() {
			let totalSize = 0;
			this.fileList.forEach(file => {
				totalSize += file.size;
			});
			return totalSize;
		},
		handleCancel() {
			this.close()
		},
		close() {
			this.fileList = []
			this.visible = false
			this.model = {
				ieFlag: 'E',
				customerName: undefined,
				declarePlace: undefined,
				outPortCode: undefined,
				shipTypeCode: undefined,
				modelProvider: 'bailian'
			}
			// 重置表单验证
			if (this.$refs.form) {
				this.$refs.form.resetFields()
			}
		},
		// 处理文件上传
		async handleUpload() {
			if (this.fileList.length === 0) {
				this.$message.warning('请选择要上传的文件');
				return;
			}

			// 再次验证总大小
			const totalSize = this.getTotalFileSize();
			if (totalSize / 1024 / 1024 > 10) {
				this.$message.error('所有文件大小总和不能超过10MB，请重新选择文件');
				return;
			}

			// 表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					this.confirmLoading = true;
					this.uploadLoading = true;

					const formData = new FormData();
					this.fileList.forEach(file => {
						formData.append('files[]', file);
					});

					formData.append('ieFlag', this.model.ieFlag || '');
					formData.append('customerName', this.model.customerName || '');
					formData.append('declarePlace', this.model.declarePlace || '');
					formData.append('outPortCode', this.model.outPortCode || '');
					formData.append('shipTypeCode', this.model.shipTypeCode || '');
					formData.append('modelProvider', this.model.modelProvider || '');
					formData.append('priceReference', this.model.priceReference || '');

					formData.append('decId', this.decId || '');

					this.uploadFiles(formData);
				}
			});
		},

		// 上传文件和表单数据
		async uploadFiles(formData) {
			try {
				const result = await uploadAction(this.url.aiMaker, formData);
				if (result.success) {
					console.log(result)
					this.$message.success(result.message || '上传成功');
					this.$emit('ok');
					this.close();
				} else {
					this.$message.error(result.message || '上传失败');
				}
			} catch (error) {
				console.error('上传出错', error);
				this.$message.error('上传失败，请重试');
			} finally {
				this.confirmLoading = false;
				this.uploadLoading = false;
			}
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';

.balance-info {
	text-align: center;

	.balance-amount {
		color: #ff4d4f;
		font-weight: bold;
		font-size: 16px;
	}

	.action-buttons {
		margin-top: 24px;

		.ant-btn {
			margin: 0 8px;
		}
	}

	p {
		margin-top: 8px;
	}
}

.ant-alert {
	margin-bottom: 16px;
}

.modal-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.left-section {
	flex: 1;
	text-align: left;
	padding-right: 10px;
	max-width: 65%;
}

.right-section {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	white-space: nowrap;

	.ant-tag {
		margin-right: 10px;
		font-size: 13px;
		padding: 3px 8px;
	}

	.ant-btn {
		margin-left: 8px;
	}
}

.tipsText {
	color: rgba(0, 0, 0, 0.45);
	font-size: 12px;
	display: block;
	text-align: left;
}

.footer-balance-amount {
	font-weight: bold;
	color: #1890ff;
}

.footer-balance-low {
	color: #ff4d4f;
	/* 红色 */
}

.recharge-link {
	color: #1890ff;
	cursor: pointer;
	text-decoration: underline;
}
</style>