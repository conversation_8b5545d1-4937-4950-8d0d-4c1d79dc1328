package org.jeecg.modules.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.DecHead;
import org.jeecg.modules.business.entity.RecShipPlanI;
import org.jeecg.modules.business.mapper.DecHeadMapper;
import org.jeecg.modules.business.mapper.RecShipPlanIMapper;
import org.jeecg.modules.business.service.IRecShipPlanIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.I;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * <p>
 * 进口船舶计划推送 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Slf4j
@Service
public class RecShipPlanIServiceImpl extends ServiceImpl<RecShipPlanIMapper, RecShipPlanI>
        implements IRecShipPlanIService {
    @Autowired
    private DecHeadMapper decHeadMapper;

    private static final String CQ_QUERY_URL = "https://api.jgsoft.com.cn:15555/open-api/sg/CqQuery"; // 船期

    private static final String DP_IMP_QUERY_URL = "https://api.jgsoft.com.cn:15555/open-api/sg/DpImpQuery"; // 进口单票查询(箱动态)
    private static final String DP_EXP_QUERY_URL = "https://api.jgsoft.com.cn:15555/open-api/sg/DpExpQuery"; // 出口单票查询(箱动态)

    /**
     * 进口船舶计划列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param recShipPlanI
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/2 16:32
     */
    @Override
    public Result<?> queryPageList(Integer pageNo, Integer pageSize, RecShipPlanI recShipPlanI,
            HttpServletRequest req) {
        Page<RecShipPlanI> page = new Page<>(pageNo, pageSize);
        IPage<RecShipPlanI> pageList = baseMapper.queryPageList(page, recShipPlanI);
        return Result.OK(pageList);
    }

    /**
     * 运抵回执列表查询
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/4 17:08
     */
    @Override
    public Result<?> listContainerCargoNodeByDecId(String decId, HttpServletRequest req) {
        DecHead decHead = decHeadMapper.selectById(decId);
        if (isEmpty(decHead)) {
            return Result.error("未查询到相关数据！");
        }
        if (isBlank(decHead.getShipName()) || isBlank(decHead.getVoyage())) {
            return Result.error("运输工具名称或航次号为空，无法查看船舶计划！");
        }

        // 组装请求参数
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("YWCM", decHead.getShipName());
        if (I.equals(decHead.getIeFlag())) {
            jsonMap.put("JKHC", decHead.getVoyage());
        } else {
            jsonMap.put("CKHC", decHead.getVoyage()); // 出口使用CKHC
        }

        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(CQ_QUERY_URL, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);

        try {
            JSONObject jsonObject = JSON.parseObject(result);

            // 检查接口调用是否成功
            if (!jsonObject.getBoolean("successful")) {
                log.error("接口调用失败：{}", jsonObject.getString("message"));
                return Result.error("查询船舶计划失败：" + jsonObject.getString("message"));
            }

            // 获取data数据
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (isEmpty(dataObject)) {
                log.warn("接口返回数据为空");
                return Result.OK(new JSONArray());
            }

            // 获取res数组
            JSONArray resArray = dataObject.getJSONArray("res");
            if (isEmpty(resArray) || resArray.size() == 0) {
                log.warn("接口返回res数组为空");
                return Result.OK(new JSONArray());
            }

            // 获取第一个元素的data数组
            JSONObject firstRes = resArray.getJSONObject(0);
            if (isEmpty(firstRes)) {
                log.warn("接口返回第一个res元素为空");
                return Result.OK(new JSONArray());
            }

            JSONArray dataArray = firstRes.getJSONArray("data");
            if (isEmpty(dataArray)) {
                log.warn("接口返回data数组为空");
                return Result.OK(new JSONArray());
            }

            // 转换数据格式，将接口返回的字段映射到前端需要的字段
            JSONArray resultArray = new JSONArray();
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                JSONObject mappedItem = new JSONObject();

                mappedItem.put("serialNumber", i + 1); // 序号
                mappedItem.put("zwcm", item.getString("ZWCM")); // 中文船名
                mappedItem.put("ywcm", item.getString("YWCM")); // 英文船名
                mappedItem.put("jkhc", item.getString("JKHC")); // 航次（进口）
                mappedItem.put("ckhc", item.getString("CKHC")); // 航次（出口）
                mappedItem.put("eta", item.getString("ETA")); // 预计到港时间
                mappedItem.put("etd", item.getString("ETD")); // 预计离港时间
                mappedItem.put("sjdgsj", item.getString("SJDGSJ")); // 实际到港时间
                mappedItem.put("sjlgsj", item.getString("SJLGSJ")); // 实际离港时间
                mappedItem.put("sxkssj", item.getString("SXKSSJ")); // 收箱开始时间
                mappedItem.put("sxjssj", item.getString("SXJSSJ")); // 收箱结束时间
                mappedItem.put("dlwz", item.getString("DLWZ")); // 位置
                mappedItem.put("cimo", item.getString("CIMO")); // IMO号

                resultArray.add(mappedItem);
            }

            log.info("成功查询到船舶计划数据，共{}条", resultArray.size());
            return Result.OK(resultArray);

        } catch (Exception e) {
            log.error("解析接口返回数据异常：", e);
            return Result.error("解析船舶计划数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取船信息数据
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/7/24 17:08
     */
    @Override
    public Result<?> getShipInfoByDecId(String decId, HttpServletRequest req) {
        DecHead decHead = decHeadMapper.selectById(decId);
        if (isEmpty(decHead)) {
            return Result.error("未查询到相关数据！");
        }
        if (isBlank(decHead.getBillCode())) {
            return Result.error("提运单号为空，无法查看！");
        }

        // 组装请求参数
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("FS", "");
        jsonMap.put("DH", decHead.getBillCode());

        log.info("组装的船信息请求参数：{}", JSON.toJSONString(jsonMap));

        try {
            // 根据进出口标识选择不同的接口
            String url = I.equals(decHead.getIeFlag()) ? DP_IMP_QUERY_URL : DP_EXP_QUERY_URL;
            String response = sendOpenApi(url, JSON.toJSONString(jsonMap));
            log.info("船信息API响应: {}", response);

            JSONObject apiResult = JSON.parseObject(response);
            if (!apiResult.getBooleanValue("successful")) {
                return Result.error("获取船信息数据失败: " + apiResult.getString("message"));
            }

            JSONObject data = apiResult.getJSONObject("data");
            if (isEmpty(data)) {
                // 如果没有数据，返回一个包含空数组的结构
                return Result.OK(buildEmptyShipInfo(decHead));
            }

            // 获取实际的数据对象
            JSONObject actualData = data.getJSONObject("data");
            if (isEmpty(actualData)) {
                return Result.OK(buildEmptyShipInfo(decHead));
            }

            // 解析并组装返回数据
            JSONObject result = parseShipInfoData(actualData, decHead);

            log.info("成功查询到船信息数据");
            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取船信息数据异常：", e);
            return Result.error("获取船信息数据失败：" + e.getMessage());
        }
    }

    /**
     * 构建空的船信息数据结构
     *
     * @param decHead 海关数据头信息
     * @return 空的船信息数据结构
     */
    private JSONObject buildEmptyShipInfo(DecHead decHead) {
        JSONObject result = new JSONObject();
        JSONObject baseInfo = new JSONObject();
        baseInfo.put("zwcm", decHead.getShipName());
        baseInfo.put("ywcm", decHead.getShipName());
        baseInfo.put("voyage", decHead.getVoyage());
        baseInfo.put("ieFlag", decHead.getIeFlag());
        baseInfo.put("zxl", ""); // 装箱量
        baseInfo.put("sxkssj", ""); // 收箱开始时间
        baseInfo.put("sxjssj", ""); // 收箱结束时间
        result.put("baseInfo", baseInfo);

        if (I.equals(decHead.getIeFlag())) {
            // 进口空数据结构
            result.put("portInfo", new JSONArray());
            result.put("manifestInfo", new JSONArray());
            result.put("tallyInfo", new JSONArray());
            result.put("inspectionInfo", new JSONArray());
            result.put("supervisionInfo", new JSONArray());
            result.put("customsReleaseInfo", new JSONArray());
            result.put("portEvacuationAndDiversionInfo", new JSONArray());
            result.put("distributionDiversionReleaseInfo", new JSONArray());
            result.put("distributionDiversionArrivalInfo", new JSONArray());
            result.put("customsDeclarationReleaseInfo", new JSONArray());
            result.put("billOfLadingReleaseInfo", new JSONArray());
        } else {
            // 出口空数据结构
            result.put("portInfo", new JSONArray());
            result.put("containerInfo", new JSONArray());
            result.put("arrivalInfo", new JSONArray());
            result.put("supervisionInfo", new JSONArray());
            result.put("customsReleaseInfo", new JSONArray());
            result.put("customsDeclarationReleaseInfo", new JSONArray());
            result.put("loadingReleaseInfo", new JSONArray());
            result.put("externalAuditReleaseInfo", new JSONArray());
            result.put("externalTallyReportInfo", new JSONArray());
        }

        return result;
    }

    /**
     * 解析船信息数据
     *
     * @param data    外部接口返回的数据
     * @param decHead 海关数据头信息
     * @return 解析后的船信息数据
     */
    private JSONObject parseShipInfoData(JSONObject data, DecHead decHead) {
        JSONObject result = new JSONObject();

        // 构建基础信息
        JSONObject baseInfo = buildBaseInfo(data, decHead);
        result.put("baseInfo", baseInfo);

        // 根据进出口标识解析不同的数据
        if (I.equals(decHead.getIeFlag())) {
            parseImportData(result, data);
        } else {
            parseExportData(result, data);
        }

        return result;
    }

    /**
     * 构建基础信息
     */
    private JSONObject buildBaseInfo(JSONObject data, DecHead decHead) {
        JSONObject baseInfo = new JSONObject();
        if (I.equals(decHead.getIeFlag())) {
            JSONArray yscdList = data.getJSONArray("yscdList");
            // 从API数据中获取基础信息，如果没有则使用报关单数据
            JSONObject jcxx = isNotEmpty(yscdList) ? yscdList.getJSONObject(0) : null; // 基础信息
            if (jcxx != null) {
                baseInfo.put("zwcm", isNotBlank(jcxx.getString("VESSEL_NAME_CN")) ? jcxx.getString("VESSEL_NAME_CN") : ""); // 中文船名
                baseInfo.put("ywcm", isNotBlank(jcxx.getString("VESSEL_NAME_EN")) ? jcxx.getString("VESSEL_NAME_EN") : decHead.getShipName()); // 英文船名
                baseInfo.put("voyage", isNotBlank(jcxx.getString("VOYAGE_NUMBER")) ? jcxx.getString("VOYAGE_NUMBER") : decHead.getVoyage()); // 航次
                baseInfo.put("zxl", ""); // 装箱量
                baseInfo.put("sxkssj", ""); // 收箱开始时间
                baseInfo.put("sxjssj", ""); // 收箱结束时间
            } else {
                // 使用报关单数据作为默认值
                baseInfo.put("zwcm", "");
                baseInfo.put("ywcm", decHead.getShipName());
                baseInfo.put("voyage", decHead.getVoyage());
                baseInfo.put("zxl", "");
                baseInfo.put("sxkssj", "");
                baseInfo.put("sxjssj", "");
            }
        } else {
            // 从API数据中获取基础信息，如果没有则使用报关单数据
            JSONObject wcxx = data.getJSONObject("wcxx"); // 基础信息
            if (wcxx != null) {
                baseInfo.put("zwcm", isNotBlank(wcxx.getString("ZWCM")) ? wcxx.getString("ZWCM") : ""); // 中文船名
                baseInfo.put("ywcm", isNotBlank(wcxx.getString("YWCM")) ? wcxx.getString("YWCM") : decHead.getShipName()); // 英文船名
                baseInfo.put("voyage", isNotBlank(wcxx.getString("CKHC")) ? wcxx.getString("CKHC") : decHead.getVoyage()); // 航次
                baseInfo.put("zxl", wcxx.getString("ZXDZT")); // 装箱单状态 1. 完成 0. 未完成
                baseInfo.put("sxkssj", wcxx.getString("sxkssj")); // 收箱开始时间
                baseInfo.put("sxjssj", wcxx.getString("sxjssj")); // 收箱结束时间
            } else {
                // 使用报关单数据作为默认值
                baseInfo.put("zwcm", "");
                baseInfo.put("ywcm", decHead.getShipName());
                baseInfo.put("voyage", decHead.getVoyage());
                baseInfo.put("zxl", "");
                baseInfo.put("sxkssj", "");
                baseInfo.put("sxjssj", "");
            }
        }

        baseInfo.put("ieFlag", decHead.getIeFlag());
        return baseInfo;
    }

    /**
     * 解析进口数据
     */
    private void parseImportData(JSONObject result, JSONObject data) {
        // 码头信息
        JSONArray mtxxList = data.getJSONArray("mtxxList");
        result.put("portInfo", mtxxList != null ? mtxxList : new JSONArray());

        // 原始舱单
        JSONArray yscdList = data.getJSONArray("yscdList");
        result.put("manifestInfo", yscdList != null ? yscdList : new JSONArray());

        // 处理理货报告
        JSONArray clbgList = data.getJSONArray("clbgList");
        result.put("tallyInfo", clbgList != null ? clbgList : new JSONArray());

        // 查验委托信息
        JSONArray cywtList = data.getJSONArray("cywtList");
        result.put("inspectionInfo", cywtList != null ? cywtList : new JSONArray());

        // 在途监管信息
        JSONArray ztjgList = data.getJSONArray("ztjgList");
        result.put("supervisionInfo", ztjgList != null ? ztjgList : new JSONArray());

        // 疏港分流申请
        JSONArray sgflsqList = data.getJSONArray("sgflsqList");
        result.put("portEvacuationAndDiversionInfo", sgflsqList != null ? sgflsqList : new JSONArray());

        // 分拨分流放行
        JSONArray fbflfxList = data.getJSONArray("fbflfxList");
        result.put("distributionDiversionReleaseInfo", fbflfxList != null ? fbflfxList : new JSONArray());

        // 分拨分流运抵
        JSONArray fbflydList = data.getJSONArray("fbflydList");
        result.put("distributionDiversionArrivalInfo", fbflydList != null ? fbflydList : new JSONArray());

        // 海关报关单放行
        JSONArray bgdfxList = data.getJSONArray("bgdfxList");
        result.put("customsDeclarationReleaseInfo", bgdfxList != null ? bgdfxList : new JSONArray());

        // 提单放行（码头进口放行的依据）
        JSONArray tdfxList = data.getJSONArray("tdfxList");
        result.put("billOfLadingReleaseInfo", tdfxList != null ? tdfxList : new JSONArray());
    }

    /**
     * 解析出口数据
     */
    private void parseExportData(JSONObject result, JSONObject data) {
        // 码头信息
        JSONArray mtxxList = data.getJSONArray("mtxxList");
        result.put("portInfo", mtxxList != null ? mtxxList : new JSONArray());

        // 装箱单信息
        JSONArray zxdxxList = data.getJSONArray("zxdxxList");
        result.put("containerInfo", zxdxxList != null ? zxdxxList : new JSONArray());

        // 运抵报告
        JSONArray ydbgList = data.getJSONArray("ydbgList");
        result.put("arrivalInfo", ydbgList != null ? ydbgList : new JSONArray());

        // 在途监管信息
        JSONArray ztjgList = data.getJSONArray("ztjgList");
        result.put("supervisionInfo", ztjgList != null ? ztjgList : new JSONArray());

        // 海关报关单放行
        JSONArray bgdfxList = data.getJSONArray("bgdfxList");
        result.put("customsReleaseInfo", bgdfxList != null ? bgdfxList : new JSONArray());

        // 海关报关单放行（出口专用）
        JSONArray ckhgbgdfxList = data.getJSONArray("ckhgbgdfxList");
        result.put("customsDeclarationReleaseInfo", ckhgbgdfxList != null ? ckhgbgdfxList : new JSONArray());

        // 装载放行
        JSONArray zzfxList = data.getJSONArray("zzfxList");
        result.put("loadingReleaseInfo", zzfxList != null ? zzfxList : new JSONArray());

        // 外理审核放行
        JSONArray wlshfxList = data.getJSONArray("wlshfxList");
        result.put("externalAuditReleaseInfo", wlshfxList != null ? wlshfxList : new JSONArray());

        // 外理理货报告
        JSONArray wllhbgList = data.getJSONArray("wllhbgList");
        result.put("externalTallyReportInfo", wllhbgList != null ? wllhbgList : new JSONArray());
    }
}
