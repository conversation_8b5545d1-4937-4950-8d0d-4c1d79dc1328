package org.jeecg.modules.business.entity.excel;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ExportInvoiceExcel
 * <pre>
 *   发票导出实体
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
public class ExportInvoiceExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 分单号
     */
    @Excel(name = "分单号", width = 15)
    private String categoryNo;
    
    /**
     * 发票号
     */
    @Excel(name = "发票号", width = 20)
    private String goodsNo;

    /**
     * 日期
     */
    @Excel(name = "日期", width = 20)
    private String goodsName;
    
    /**
     * 项目
     */
    @Excel(name = "项目", width = 25)
    private String item;
    
    /**
     * 物料号
     */
    @Excel(name = "物料号", width = 15)
    private String copGno;
    
    /**
     * 品名
     */
    @Excel(name = "品名", width = 15)
    private String pn;
    
    /**
     * 英文
     */
    @Excel(name = "英文", width = 15)
    private String pnEn;

    /**
     * 数量
     */
    @Excel(name = "数量", width = 15)
    private String qty;

    /**
     * 单位
     */
    @Excel(name = "单位", width = 15)
    private String qunit;
    
    /**
     * 金额
     */
    @Excel(name = "金额", width = 18)
    private String amount;

    /**
     * 重量KGS
     */
    @Excel(name = "重量KGS", width = 18)
    private String weight;

    /**
     * 装运号
     */
    @Excel(name = "装运号", width = 20)
    private String waybillNo;

    /**
     * 箱单号
     */
    @Excel(name = "箱单号", width = 20)
    private String packingListNo;

    /**
     * 原产国
     */
    @Excel(name = "原产国", width = 15)
    private String countryOfOrigin;

    /**
     * 外包装号
     */
    @Excel(name = "外包装号", width = 20)
    private String outerPackagingNo;

    /**
     * 内包装号
     */
    @Excel(name = "内包装号", width = 20)
    private String innerPackagingNo;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 20)
    private String orderNo;

    /**
     * 货物信息
     */
    @Excel(name = "货物信息", width = 30)
    private String goodsInfo;

    /**
     * 手动标志
     */
    @Excel(name = "手动标志", width = 15)
    private String manualFlag;

    /**
     * 类型
     */
    @Excel(name = "类型", width = 15)
    private String type;

    /**
     * 币种
     */
    @Excel(name = "币种", width = 15)
    private String currency;
}