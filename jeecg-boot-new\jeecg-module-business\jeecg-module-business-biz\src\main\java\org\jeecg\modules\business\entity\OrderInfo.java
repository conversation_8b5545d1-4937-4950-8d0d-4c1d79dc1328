package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.DecHeadFields;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelVerify;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * @Description: 订单信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-17
 * @Version: V1.0
 */
@Data
@TableName("order_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="order_info对象", description="订单信息表")
public class OrderInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "主键")
	private String id;
	/**租户ID*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	@Excel(name = "租户ID", width = 15)
	@ApiModelProperty(value = "租户ID")
	private Long tenantId;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
	/**订单协议号*/
	@Excel(name = "业务编号", width = 15)
	@ApiModelProperty(value = "业务编号")
	private String orderProtocolNo;
	/**订单号（9710）*/
	@Excel(name = "订单号（9710）", width = 15)
	@ApiModelProperty(value = "订单号（9710）")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String orderNoNineSeven;
	/**订仓单号（9810）*/
	@Excel(name = "订仓单号（9810）", width = 15)
	@ApiModelProperty(value = "订仓单号（9810）")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String orderStockNo;
	/**监管模式*/
	@Excel(name = "监管模式", width = 15, dicCode = "JGFS")
	@Dict(dicCode = "JGFS")
	@ApiModelProperty(value = "监管模式")
	private String supervisionMode;
	/**备案号*/
	@ApiModelProperty(value = "备案号")
	private String recordNumber;
	/**贸易国(地区)*/
	@Excel(name = "贸易国(地区)", width = 15, dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@ApiModelProperty(value = "贸易国(地区)")
	private String tradingCountry;
	/**运抵国(地区)*/
	@Excel(name = "运抵国(地区)", width = 15, dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@ApiModelProperty(value = "运抵国(地区)")
	private String countryArrival;
	/**指运港*/
	@Excel(name = "指运港", width = 15, dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	@Dict(dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	@ApiModelProperty(value = "指运港")
	private String portDestination;
	/**指运港名称*/
	@Excel(name = "指运港名称", width = 15, dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	@Dict(dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	@ApiModelProperty(value = "指运港名称")
	private String portDestinationName;
	/**境外付款方*/
	@Excel(name = "境外付款方", width = 15, dictTable = "overseas_payer_info", dicText = "overseas_payer_name", dicCode = "id")
	@Dict(dictTable = "overseas_payer_info", dicText = "overseas_payer_name", dicCode = "id")
	@ApiModelProperty(value = "境外付款方")
	private String overseasPayerInfoId;
	/**报关币种*/
	@Excel(name = "报关币种", width = 15, dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
	@Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
	@ApiModelProperty(value = "报关币种")
	private String customsDeclarationCurrency;
	/**收汇方式*/
	@Excel(name = "收汇方式", width = 15, dicCode = "exchange_collection_type")
	@Dict(dicCode = "exchange_collection_type")
	@ApiModelProperty(value = "收汇方式")
	private Integer exchangeCollectionType;
	/**合同总金额*/
	@Excel(name = "合同总金额", width = 15)
	@ApiModelProperty(value = "合同总金额")
	private BigDecimal totalContractAmount;
	/**发票号*/
	@Excel(name = "发票号", width = 15)
	@ApiModelProperty(value = "发票号")
	private String invoiceNo;
	/**预计出货日期*/
	@Excel(name = "发票日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "发票日期")
	private Date invoiceDate;
	/**外销合同号*/
	@Excel(name = "外销合同号", width = 15)
	@ApiModelProperty(value = "外销合同号")
	private String exportContractNo;
	/**客服经理*/
	@Excel(name = "客服经理", width = 15)
	@ApiModelProperty(value = "客服经理")
	private String customerServiceManager;
	/**销售经理*/
	@Excel(name = "销售经理", width = 15)
	@ApiModelProperty(value = "销售经理")
	private String salesManager;
	/**海关编号/报关单号*/
	@Excel(name = "海关编号/报关单号", width = 15)
	@ApiModelProperty(value = "海关编号/报关单号")
	private String customsNumber;
	/**提运单号*/
	@Excel(name = "提运单号", width = 15)
	@ApiModelProperty(value = "提运单号")
	private String deliveryNumbers;
	/**预计出货日期*/
	@Excel(name = "预计出货日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "预计出货日期")
	private Date estimatedShipmentDate;
	/**提单收货人/进口时报关单tab的境内收发货人，默认委托方*/
	@Excel(name = "提单收货人", width = 15)
	@ApiModelProperty(value = "提单收货人/进口时报关单tab的境内收发货人，默认委托方名称")
	private String receiver;
	/**提单收货人/进口时报关单tab的境内收发货人，默认委托方*/
	@ApiModelProperty(value = "提单收货人/进口时报关单tab的境内收发货人，默认委托方ID")
	private String receiverId;
	/**供应商*/
	@ApiModelProperty(value = "供应商ID，报关单tab页的供应商ID")
	private String domesticSuppliersInfoId;
	/**供应商*/
	@Excel(name = "供应商", width = 15)
	@ApiModelProperty(value = "供应商名称，报关单tab页的供应商名称")
	private String domesticSuppliersInfoName;
	/**出境关别*/
	@Excel(name = "出境关别", width = 15, dictTable = "erp_customs_ports", dicText = "name", dicCode = "customs_port_code")
	@Dict(dictTable = "erp_customs_ports", dicText = "name", dicCode = "customs_port_code")
	@ApiModelProperty(value = "出境关别")
	private String exitClearance;
	/**离境口岸*/
	@Excel(name = "离境口岸", width = 15, dictTable = "erp_china_ports", dicText = "name", dicCode = "china_port_code")
	@Dict(dictTable = "erp_china_ports", dicText = "name", dicCode = "china_port_code")
	@ApiModelProperty(value = "离境口岸")
	private String departurePort;
	/**国际运费/运费金额*/
	@Excel(name = "国际运费/运费金额", width = 15)
	@ApiModelProperty(value = "国际运费/运费金额")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private BigDecimal freightAmount;
	/**运费种类*/
	@Excel(name = "运费种类", width = 15)
	@ApiModelProperty(value = "运费种类")
	private String freightAmountType;
	/**单位种类运费*/
	@Excel(name = "单位种类运费", width = 15)
	@ApiModelProperty(value = "单位种类运费")
	private BigDecimal unitTypeFreight;
	/**国际保费/保费金额*/
	@Excel(name = "国际保费/保费金额", width = 15)
	@ApiModelProperty(value = "国际保费/保费金额")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private BigDecimal premiumAmount;
	/**保费种类*/
	@Excel(name = "保费种类", width = 15)
	@ApiModelProperty(value = "保费种类")
	private String premiumAmountType;
	/**单位种类保费*/
	@Excel(name = "单位种类保费", width = 15)
	@ApiModelProperty(value = "单位种类保费")
	private BigDecimal unitTypePremium;
	/**杂费金额*/
	@Excel(name = "杂费金额", width = 15)
	@ApiModelProperty(value = "杂费金额")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private BigDecimal miscellaneousAmount;
	/**境内货源地*/
	@Excel(name = "境内货源地", width = 15, dictTable = "erp_districts", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_districts", dicText = "name", dicCode = "code")
	@ApiModelProperty(value = "境内货源地")
	private String domesticSourceOfGoods;
	/**最终目的国(地区)*/
	@Excel(name = "最终目的国(地区)", width = 15, dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@ApiModelProperty(value = "最终目的国(地区)")
	private String finalContry;
	/**贸易方式*/
	@Excel(name = "贸易方式", width = 15, dicCode = "trading_type")
	@Dict(dicCode = "trading_type")
	@ApiModelProperty(value = "贸易方式")
	private Integer tradingType;
	/**报关行id*/
	@Excel(name = "报关行id", width = 15)
	@ApiModelProperty(value = "报关行id")
	private String customsBrokerId;
	/**结关日期*/
	@Excel(name = "结关日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "结关日期")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private Date closeDate;
	/**海外仓名称*/
	@Excel(name = "海外仓名称", width = 15)
	@ApiModelProperty(value = "海外仓名称")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String overseasWarehouseName;
	/**海外仓地址*/
	@Excel(name = "海外仓地址", width = 15)
	@ApiModelProperty(value = "海外仓地址")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String overseasWarehouseAddress;
	/**海外仓国别*/
	@Excel(name = "海外仓国别", width = 15, dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@ApiModelProperty(value = "海外仓国别")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String overseasWarehouseCountry;
	/**境外电商平台*/
	@Excel(name = "境外电商平台", width = 15)
	@ApiModelProperty(value = "境外电商平台")
	@TableField(updateStrategy=FieldStrategy.IGNORED)
	private String overseasWarehousePlatform;
	/**外汇收齐金额*/
	@Excel(name = "外汇收齐金额", width = 15)
	@ApiModelProperty(value = "外汇收齐金额")
	private BigDecimal foreignExchangeAmount;
	/**订单状态*/
	@Excel(name = "订单状态", width = 15, dicCode = "order_status")
	@Dict(dicCode = "order_status")
	@ApiModelProperty(value = "订单状态")
	private Integer orderStatus;
	/**外汇收齐完成标记*/
	@Excel(name = "外汇收齐完成标记", width = 15, dicCode = "receive_money_flag")
	@Dict(dicCode = "receive_money_flag")
	@ApiModelProperty(value = "外汇收齐完成标记")
	private Integer receiveMoneyFlag;
	/**外汇收齐完成日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "外汇收齐完成日期")
	private Date receiveMoneyDate;
	/**外汇付款完成标记*/
	@Excel(name = "外汇付款完成标记", width = 15, dicCode = "pay_money_flag")
	@Dict(dicCode = "pay_money_flag")
	@ApiModelProperty(value = "外汇付款完成标记")
	private Integer payMoneyFlag;
	/**外汇付款标记*/
	@Excel(name = "外汇付款标记", width = 15)
	@ApiModelProperty(value = "外汇付款标记")
	private Integer hasPayMoneyFlag;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15, dicCode = "del_flag")
	@Dict(dicCode = "del_flag")
	@ApiModelProperty(value = "删除标记")
	private Integer delFlag;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "备注")
	private String remarks;
	/**提醒状态*/
	@Excel(name = "提醒状态", width = 15)
	@ApiModelProperty(value = "提醒状态")
	private Integer remindStatus;
	/**入货通知录入标记*/
	@Excel(name = "入货通知录入标记", width = 15)
	@ApiModelProperty(value = "入货通知录入标记")
	private Integer incomingNoticeFlag;
	/**推送状态*/
	@Excel(name = "推送状态", width = 15, dicCode = "push_status")
	@Dict(dicCode = "push_status")
	@ApiModelProperty(value = "推送状态")
	private Integer pushStatus;
	/**海关关区id*/
	@Excel(name = "海关关区id", width = 15)
	@ApiModelProperty(value = "海关关区id")
	private String customsAreaInfoId;
	/**是否正常 是否正常 1正常2冻结3申请解冻*/
	@ApiModelProperty(value = "是否正常")
	private Integer hasNormalFlag;
	/**进出口标识 I进口E出口*/
	@ApiModelProperty(value = "进出口标识")
	private String ieFlag;
	/**预估汇率*/
	@ApiModelProperty(value = "预估汇率")
	private BigDecimal guessRate;
	/**实际汇率*/
	@ApiModelProperty(value = "实际汇率")
	private BigDecimal trueRate;
	/**订单标志 1正常2失效*/
	@ApiModelProperty(value = "订单标志")
	private Integer orderFlag;
	/**是否撤销 0未撤销1已撤销*/
	@ApiModelProperty(value = "是否撤销 0未撤销1已撤销")
	private Integer hasBack;
	/**是否催单0未催单1已催单*/
	@ApiModelProperty(value = "是否催单0未催单1已催单")
	private Integer hasUrge;
	/**第三方的状态 暂定0未下单 1已下单*/
	@ApiModelProperty(value = "第三方的状态 暂定0未下单 1已下单")
	private Integer thirdPartyStatus;
	/**订单海运船是否危险 （0正常1危险）*/
	@ApiModelProperty(value = "订单海运船是否危险 （0正常1危险）")
	private Integer hasDanger;
	/**危险备注*/
	@ApiModelProperty(value = "危险备注")
	private String dangerRemarks;
	/**英文名称 查询表体用*/
	@TableField(exist = false)
	private String englishName;
	/**中文名称 查询表体用*/
	@TableField(exist = false)
	private String chineseName;
	/**首付款比例*/
	@ApiModelProperty(value = "首付款比例")
	private BigDecimal sfPer;
	/**尾款比例*/
	@ApiModelProperty(value = "尾款比例")
	private BigDecimal wkPer;
	/**关税税额*/
	@ApiModelProperty(value = "关税税额")
	private BigDecimal tariffRate;

	/**
	 * 消费使用单位/生产销售单位
	 */
	@ApiModelProperty(value = "消费使用单位/生产销售单位")
	private String deliverUnitName;

	/**
	 * 征免性质
	 */
	@ApiModelProperty(value = "征免性质")
	private String taxTypeCode;
	/**
	 * 许可证号
	 */
	@ApiModelProperty(value = "许可证号")
	private String licenceNumber;
	/** 运费代码 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String shipFeeCode;
	/** 运费币制 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String shipCurrencyCode;
	/** 保费代码 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String insuranceCode;
	/** 保费币制 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String insuranceCurr;
	/** 杂费代码 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String extrasCode;
	/** 杂费币制 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String otherCurr;
	/**
	 * 合同表ID
	 */
	private String contractId;
	/**
	 * 订单类型（1国内采购 2进口采购 3外销订单 4内销订单 5二线出区）
	 */
	private String orderType;
	/** 业务员 */
	@ApiModelProperty(name = "业务员/采购人")
	private String salesman;
	/** 购买方 */
	@ApiModelProperty(name = "购买方/委托方")
	private String buyer;
	/** 购买方地址联系人等 */
	@ApiModelProperty(name = "购买方地址联系人等")
	private String buyerAddress;
	/** 供应商地址联系人等 */
	@ApiModelProperty(name = "供应商地址联系人等")
	private String sellerAddress;
	/** 签订日期 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@TableField(updateStrategy = FieldStrategy.IGNORED )
	@ApiModelProperty(name = "签订日期",notes = "")
	private Date signDate;
	/** 付款日期 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@TableField(updateStrategy = FieldStrategy.IGNORED )
	@ApiModelProperty(name = "付款日期",notes = "")
	private Date payDate;
	/** 交货日期 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@TableField(updateStrategy = FieldStrategy.IGNORED )
	@ApiModelProperty(name = "交货日期",notes = "")
	private Date deliveryDate;
	/** 支付条款 */
	@ApiModelProperty(name = "支付条款",notes = "")
	private String payment;
	/** 运输条款 */
	@ApiModelProperty(name = "运输条款",notes = "")
	private String transportClause;
	/** 成交方式 */
	@ApiModelProperty(name = "成交方式",notes = "")
	@Dict(dicCode = "trading_type")
	private String transMode;
	/** 启运港 */
	@ApiModelProperty(name = "启运港",notes = "")
	@Dict(dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	private String arrivalPort;
	/** 目的港 */
	@ApiModelProperty(name = "目的港",notes = "")
	@Dict(dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
	private String desPort;
	/** 审核标记（0未审核 1已审核） */
	private String isAudits;
	/**
	 * 订单号
	 */
	@TableField("REL_ORDER_NO")
	private String relOrderNo;
	/**
	 * 关联提单编号
	 */
	private String linkedBillOfLadingNo;
	/**
	 * 关联票据号
	 */
	private String relNoteNo;
	/**
	 * 关联票据号项目
	 */
	private Integer relNoteNoProject;
	/**
	 * 保完税标志
	 */
	private String bondedMark;
	private String orderNumber;
	private String orderLineNumber;
	private String companyCode;
	@Excel(name = "交货单号")
	private String deliveryNoteNumber;
	@Excel(name = "加工费")
	private BigDecimal processingFee;
	@Excel(name = "加工费币制")
	private String processingFeeCurr;
	@Excel(name = "料件费")
	private BigDecimal materialFee;
	@Excel(name = "材料费币制")
	private String materialFeeCurr;
	@Excel(name = "单据内部编号")
	private String internalNumberingOfDocuments;
	/** 出货日期 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date shipmentDate;
	@Excel(name = "包装种类", width = 15, dictTable = "erp_packages_types", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_packages_types", dicText = "name", dicCode = "code")
	private String packsKinds;
	/**
	 * 件数
	 */
	private Integer packs;
	private String cbm;
	/**
	 * 运输方式
	 */
	@Excel(name = "运输方式", width = 15, dictTable = "erp_transport_types", dicText = "name", dicCode = "code")
	private String shippingType;
	/**
	 * 申报单位海关代码
	 */
	private String declareUnit;
	/**
	 * 申报单位社会统一信用代码
	 */
	@Excel(name = "社会信用代码")
	private String declareUnitSocialCode;
	/*
	 * 申报单位名称
	 */
	@Excel(name = "申报单位名称")
	private String declareUnitName;
	private String customsDeclarationMergeType; // 报关单归并类型
	/**运输工具名称*/
	@Excel(name = "运输工具名称", width = 15)
	private String transportName;
	/**航次号*/
	private String voy;
	/**
	 * 申报地海关
	 */
	@Excel(name = "申报地海关",width = 16)
	private String declarePlace;
	@Excel(name = "出口日期",width = 18)
	private String outDate;
	/**
	 * 料件成品标记代码 （I：料件，E：成品）
	 */
	@Excel(name = "料件、成品标志", replace = {"料件_I", "成品_E"})
	private String mtpckEndprdMarkcd;
	/**
	 * 集装箱号
	 */
	@Excel(name = "集装箱号", width = 15)
	private String containerId;
	/**
	 * 集装箱规格
	 */
	@Excel(name = "集装箱规格", width = 15)
	private String containerMd;
	/**
	 * 自重
	 */
	@Excel(name = "自重", width = 15)
	private BigDecimal goodsContaWt;
	/**
	 * 拼箱标识
	 */
	@Excel(name = "拼箱标识", width = 15)
	private String lclFlag;
	/**
	 * 商品项号(用半角逗号分隔，如“1,3”)
	 */
	@Excel(name = "商品项号关系", width = 15)
	private String goodsNo;
	/**
	 * 收发货单号
	 */
	@TableField("SHIPMENT_NO")
	private String shipmentNo;
//	/**
//	 * 进口-消费使用单位(出口-生产销售单位)
//	 */
//	@TableField("USE_CONSIGNER")
//	private String useConsigner;
	/**
	 * 货物分类 系统用
	 */
	@TableField("GOODS_TYPE")
	private String goodsType;
	/**
	 * 计费重量
	 */
	@TableField("CHARGED_WEIGHT")
	private BigDecimal chargedWeight;
	/**
	 * 其他包装
	 */
	@TableField("OTHER_PACK")
	private String otherPack;


	@TableField(exist = false)
	private String relDecNos;
	@TableField(exist = false)
	private String hasRelDecNos;
	@TableField(exist = false)
	private String relStorageNos;
	@TableField(exist = false)
	private Boolean isSFJ;
	@TableField(exist = false)
	private String relCiqNos;
	private BigDecimal pcs;
	private BigDecimal gw;
	private BigDecimal nw;
	private String overseasPayerInfoName;
	private String buyerName;
	/**
	 * 业务种类 业务范围一线1 二线2
	 */
	@TableField("APPLY_KIND")
	private String applyKind;
	/**
	 * 出区模式（1分拨出区、2整进整出、I料件、E成品）
	 */
	@TableField(value = "OUT_ZONE_MODE")
	private String outZoneMode;
	/**
	 * 联系人
	 */
	@TableField("CONTACT_PERSON")
	private String contactPerson;
	/**
	 * 主单号 舱单信息
	 */
	@TableField("M_BILL_NO")
	private String mBillNo;
	/**
	 * 分单号 舱单信息
	 */
	@TableField("HBL")
	private String hbl;
	/**
	 * 报关备注
	 */
	@TableField("DEC_REMARK")
	private String decRemark;
	/**
	 * 关联单号 系统用
	 */
	@TableField("RELATION_ID")
	private String relationId;


	@TableField(exist = false)
	private List<OrderPackingInfo> packingList;
	@TableField(exist = false)
	private String orderTypes;

	@TableField(exist = false)
	private List<OrderProductInfo> productList;

	/**
	 * 申报前配置信息
	 */
	@TableField(exist = false)
	private Map<String, ApplyConfig> applyConfigMap;
	/**
	 * 核注单表头信息
	 */
	@TableField(exist = false)
	private List<NemsInvtHead> nemsInvtHeads;
	/**
	 * 报关单信息
	 */
	@TableField(exist = false)
	private List<DecHead> decHeads;
	/**
	 * 快捷生成申报单（默认不是快捷（false））
	 */
	@TableField(exist = false)
	private Boolean quick;
	/**
	 * 标记(1:用于标记一线入二线出)
	 */
	@TableField(exist = false)
	private String sign;
	/**
	 * 关联备案编号 （结转类专用）
	 */
	@TableField(exist = false)
	private String rltputrecNo;
	/**
	 * 是否是区外单据（true：是，false：否）
	 */
	@TableField(exist = false)
	private Boolean outer;
	@TableField(exist = false)
	private Map<String, BigDecimal> exchangeRateMaps;

	/**
	 * 进口预计到港时间(出口预计离港时间)
	 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date estimatedPortTime;

	/**
	 * 进口实际到港时间（出口实际离港时间）
	 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date actualPortTime;
	/**
	 * 快递快运企业名称，如DHL等
	 */
	@TableField("SHIP_VIA")
	private String shipVia;
	/**
	 * 销售邮箱
	 */
	@TableField("SALES_EMAIL")
	private String salesEmail;
	/**
	 * 银行信息
	 */
	@TableField("BANK_INFO_ID")
	private String bankInfoId;
	@TableField(exist = false)
	private String bankInfoMark;


	private String arrivalPortEn;
	private String desPortEn;

	@TableField(exist = false)
	private String estimatedPortTimeStr;
	@TableField(exist = false)
	private String todayStr;
	/**
	 * 首项货名
	 */
	@TableField(exist = false)
	private String firstGoodsName;

	/**
	 * 根据监管方式取(生成)业务配置Key
	 *
	 * @param tradeType
	 * @return
	 */
	public String getConfigKey(String tradeType) {
		return (isNotBlank(buyer)) ? buyer + "|" + tradeType : "|" + tradeType;
	}
}
