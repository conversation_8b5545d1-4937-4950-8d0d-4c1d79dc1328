<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<search-select-popconfirm :def-columns="searchColumns"  v-if="toggleSearchStatus"
																	:original-columns="originalColumns" @updatePopconfirm="updatePopconfirm"/>
				<a-row :gutter="24">
					<a-col v-if="isAdmin" :md="12" :sm="24" :xl="6" :xxl="6">
						<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属企业">
							<j-search-select-tag v-model="queryParam.tenantId" :async="true" :pageSize="50"
																	 dict="sys_tenant,name,id,status=1" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[0].visible">
						<a-form-item label="境内收发货人" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<input-history v-model="queryParam.optUnitName" placeholder="请输入境内收发货人" queryParamKey="optUnitName">
							</input-history>
<!--							<j-input placeholder="请输入境内收发货人" v-model="queryParam.optUnitName"></j-input>-->
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[1].visible">
						<a-form-item label="统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<input-history v-model="queryParam.seqNo" placeholder="请输入统一编号" queryParamKey="seqNo">
							</input-history>
<!--							<j-input placeholder="请输入统一编号" v-model="queryParam.seqNo"></j-input>-->
						</a-form-item>
					</a-col>
					<a-col v-if="searchColumns[2].visible && !isAdmin" :md="12" :sm="24" :xl="6" :xxl="6">
						<a-form-item label="报关单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<input-history v-model="queryParam.clearanceNos" placeholder="请输入报关单号" queryParamKey="clearanceNo">
							</input-history>
<!--							<j-input placeholder="请输入报关单号" v-model="queryParam.clearanceNo"></j-input>-->
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12" >
			  <span style="float: left" class="table-page-search-submitButtons">
				<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
				<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
				  <a @click="handleToggleSearch" style="margin-left: 8px">
					{{ toggleSearchStatus ? '收起' : '展开' }}
					<a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
				  </a>
			  </span>
					</a-col>
					<template v-if="toggleSearchStatus">
						<a-col v-if="searchColumns[2].visible && isAdmin" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报关单号">
								<input-history v-model="queryParam.clearanceNo" placeholder="请输入报关单号" queryParamKey="clearanceNo">
								</input-history>
								<!--							<j-input placeholder="请输入报关单号" v-model="queryParam.clearanceNo"></j-input>-->
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[3].visible">
							<a-form-item label="提运单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.billCode" placeholder="请输入提运单号" queryParamKey="billCode">
								</input-history>
<!--								<j-input placeholder="请输入提运单号" v-model="queryParam.billCode"></j-input>-->
							</a-form-item>
						</a-col>
						<!--						<a-col :xl="6" :sm="24" :xxl="6" :md="12">-->
						<!--							<a-form-item label="进出口" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
						<!--								<a-select placeholder="" v-model="queryParam.ieFlag">-->
						<!--									<a-select-option value="I">进口</a-select-option>-->
						<!--									<a-select-option value="E">出口</a-select-option>-->
						<!--								</a-select>-->
						<!--							</a-form-item>-->
						<!--						</a-col>-->
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[4].visible">
							<a-form-item label="监管方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<!--              <j-input placeholder="请输入监管方式" v-model="queryParam.tradeTypeCode"></j-input>-->
								<j-dict-select-tag
									v-model="queryParam.tradeTypeCode"
									type="list"
									dictCode="JGFS"
									:allowClear="true"
									placeholder="请选择监管方式"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[5].visible">
							<a-form-item label="企业内部编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.etpsInnerInvtNo" placeholder="请输入企业内部编号"
															 queryParamKey="etpsInnerInvtNo">
								</input-history>
<!--								<j-input placeholder="请输入企业内部编号" v-model="queryParam.etpsInnerInvtNo"></j-input>-->
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[6].visible">
							<a-form-item label="进出口" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select
									:allowClear="true"
									placeholder="请选择进出口"
									v-model="queryParam.ieFlag"
								>
									<a-select-option value="I">进口</a-select-option>
									<a-select-option value="E">出口</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[7].visible">
							<a-form-item label="报关单类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select
									:allowClear="true"
									placeholder="请选择报关单类型"
									v-model="queryParam.dclTrnRelFlag"
								>
									<a-select-option value="0">一般报关单</a-select-option>
									<a-select-option value="1">转关提前报关单</a-select-option>
									<a-select-option value="2">备案清单</a-select-option>
									<a-select-option value="3">转关提前备案清单</a-select-option>
									<a-select-option value="4">出口二次转关</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[8].visible">
							<a-form-item label="是否有舱单" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select
									:allowClear="true"
									placeholder="请选择"
									v-model="queryParam.hasCd"
								>
									<a-select-option value="1">是</a-select-option>
									<a-select-option value="0">否</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[9].visible">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="运抵状态">
								<j-multi-select-tag
									:allowClear="true"
									:maxTagCount="1"
									placeholder="请选择"
									v-model="queryParam.hasYd"
									:options="ydOptions"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[10].visible">
							<a-form-item label="申报日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectAppDate" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="selectAppDateChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[11].visible">
							<a-form-model-item label="运输方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag v-model="queryParam.shipTypeCode" dict="trans_type" />
							</a-form-model-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[12].visible">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="制单人">
								<input-history v-model="queryParam.createPerson" placeholder="请输入制单人" queryParamKey="createPerson">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[13].visible">
							<a-form-item label="申报地海关" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.declarePlace"
									dict="erp_customs_ports,name,customs_port_code, 1=1"
									type="dec"
									:pageSize="50"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[14].visible">
							<a-form-item label="创建日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectCreateTime" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="selectCreateTimeChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[15].visible">
							<a-form-item label="合同协议号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.contract" placeholder="请输入合同协议号" queryParamKey="contract">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[16].visible">
							<a-form-item label="许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.licenceNumber" placeholder="请输入许可证号" queryParamKey="licenceNumber">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[17].visible">
							<a-form-item label="申报状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.decStatus"
									:dictOptions="decStatus"
									:pageSize="50"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[18].visible">
							<a-form-item label="推送状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select
									:allowClear="true"
									placeholder="请选择推送状态"
									v-model="queryParam.pushStatusFlag"
								>
									<a-select-option value="1">已推送</a-select-option>
									<a-select-option value="0">未推送</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[19].visible">
							<a-form-item label="进出境关别" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.outPortCode"
									dict="erp_customs_ports,name,customs_port_code, 1=1"
									type="dec"
									:pageSize="50"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[20].visible">
							<a-form-item label="消费使用单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.deliverUnitName" placeholder="请输入消费使用单位" queryParamKey="deliverUnitName">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[21].visible">
							<a-form-item label="备案号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.recordNumber" placeholder="请输入备案号" queryParamKey="recordNumber">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[22].visible">
							<a-form-item label="申报单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.declareUnitName" placeholder="请输入申报单位" queryParamKey="declareUnitName">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[23].visible">
							<a-form-item label="报关员卡号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.icNumber" placeholder="请输入报关员卡号" queryParamKey="icNumber">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[24].visible">
							<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<input-history v-model="queryParam.hsname" placeholder="请输入商品名称" queryParamKey="hsname">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[25].visible">
							<a-form-item label="最近操作时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectUpdateTime" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="selectUpdateTimeChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12" v-if="searchColumns[26].visible">
							<a-form-item label="初复审状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择" v-model="queryParam.initialReviewStatus">
									<a-select-option value="0">未审核</a-select-option>
									<a-select-option value="1">已初审/未复审</a-select-option>
									<a-select-option value="2">已复审</a-select-option>
								</a-select>
							</a-form-item>


						</a-col>
						<a-col v-if="searchColumns[27].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="贸易国别">
								<j-search-select-tag
									v-model="queryParam.tradeCountry"
									:pageSize="50"
									dict="GBDQ-DEC"
									type="dec"
								/>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[28].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="原产国">
								<j-search-select-tag
									v-model="queryParam.desCountry"
									:pageSize="50"
									dict="GBDQ-DEC"
									type="dec"
								/>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[29].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="检验检疫受理机关">
								<j-search-select-tag
									v-model="queryParam.orgCode"
									:pageSize="50"
									dict="JYJG"
									type="dec"
								/>
							</a-form-item>
						</a-col>

						<a-col v-if="searchColumns[30].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="启运/运抵国">
								<j-search-select-tag
									v-model="queryParam.arrivalArea"
									:pageSize="50"
									dict="GBDQ-DEC"
									type="dec"
								/>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[31].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="商品编码">
								<input-history v-model="queryParam.hscode" placeholder="请输入商品编码" queryParamKey="hscode">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[32].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="创建人">
								<input-history v-model="queryParam.tenantName" placeholder="请输入创建人" queryParamKey="tenantName">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[33].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="集装箱号">
								<input-history v-model="queryParam.allContainerId" placeholder="请输入集装箱号" queryParamKey="allContainerId">
								</input-history>
							</a-form-item>
						</a-col>
						<a-col v-if="searchColumns[34].visible" :md="12" :sm="24" :xl="6" :xxl="6">
							<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="包装种类">
								<j-search-select-tag
									v-model="queryParam.packsKinds"
									:pageSize="50"
									dict="erp_packages_types,name,code,isenabled=1"
									type="dec"
								/>
							</a-form-item>
						</a-col>
					</template>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->



		<!-- table区域-begin -->
		<div style="position: relative; top: -5px">
<!--			新表格-->
			<query-vxe-grid
				class="xGrid-style"
				ref="xGrid"
				size="mini"
				height="500"
				:loading="loading"
				:gridOptions="gridOptions"
				:dataSource="dataSource"
				@cell-click="cellClick"
				@checkbox-change="checkboxChangeEvent"
				@checkbox-all="checkboxChangeEvent"
				@cell-dblclick="cellDblclick"
				@page-change="handlePageChange"
				@sort-change="sortChange"
			>
				<template v-slot:toolbar_buttons>
					<!-- 操作按钮区域 -->
					<div class="table-operator">
						<!--			<a-button size="small" icon="plus" type="primary" @click="handleDetail('1')">-->
						<!--				新增-->
						<!--			</a-button>-->
						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item key="1" @click="aiSettingHandle">
									报关单AI配置
								</a-menu-item>
								<a-menu-item key="2" @click="aiAuditHandle">
									关智宝
								</a-menu-item>
							</a-menu>
							<a-button size="small" type="primary">
								<j-gpt-icon :size="16" style="margin-right: 5px;margin-bottom: 3px" />
								关智宝
								<a-icon type="down" />
							</a-button>
						</a-dropdown>
						<a-button v-has="'dec:edit'" type="primary" icon="copy" @click="copyDecHead">复制</a-button>
						<a-dropdown class="sc">
							<a-menu slot="overlay">
<!--								<a-menu-item @click="handleGetExportDecFields" key="1">-->
<!--									详情导出-->
<!--								</a-menu-item>-->
								<a-menu-item @click="handleGetExportDecFields(1)" key="2">
									查询条件导出
								</a-menu-item>
								<a-menu-item @click="handleGetExportDecFields(2)" key="3">
									选择导出
								</a-menu-item>
							</a-menu>
							<a-button v-has="'dec:down'" :loading="exportButtonLoading" size="small" icon="download" type="primary">
								数据导出
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<!--			打印（导出）-->
						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item @click="handleExportDecComplete('EXCEL')" key="1">
									下载EXCEL
								</a-menu-item>
								<a-menu-item @click="handleExportDecComplete('PDF')" key="2">
									下载PDF
								</a-menu-item>
								<a-menu-item key="2-1" @click="handleDecPrint('PDF')">
									打印PDF
								</a-menu-item>
								<a-menu-item @click="handleExportDecComplete('SHDPDF')" key="3">
									下载审核单PDF
								</a-menu-item>
								<a-menu-item key="3-1" @click="handleDecPrint('SHDPDF')">
									打印审核单PDF
								</a-menu-item>
								<a-menu-item @click="handlePrintBatch('PDF')" key="4">
									批量下载
								</a-menu-item>
							</a-menu>
							<a-button v-has="'dec:down'" :loading="printButtonLoading" size="small" icon="download" type="primary">
								下载
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
<!--						<a-button v-has="'dec:down'" type="primary" icon="printer" :loading="printBatchLoading" @click="handlePrintBatch('PDF')">批量打印</a-button>-->
						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item @click="handleFirstTrial" key="1">
									<a-icon type="check-circle"/>
									初审
								</a-menu-item>
								<a-menu-item key="1-1" @click="handleFirstTrial">
									<a-icon type="close-circle"/>
									取消初审
								</a-menu-item>
								<a-menu-item @click="handleReview" key="2">
									<a-icon type="check-circle"/>
									复审
								</a-menu-item>
								<a-menu-item key="2-1" @click="handleReview">
									<a-icon type="close-circle"/>
									取消复审
								</a-menu-item>
							</a-menu>
							<a-button v-has="'dec:edit'" :loading="auditLoading" icon="edit" size="small" type="primary">
								初复审
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<a-button v-has="'dec:push'"  type="primary" icon="to-top" @click="handlePushDec">推送</a-button>
						<a-button @click="showOriginalEdi()" type="primary" icon="diff">回执系统处理信息</a-button>
						<a-button v-has="'dec:edit'" type="primary" icon="property-safety" @click="handleTax">查看税单信息</a-button>

						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item @click="handleDownloadTax" key="1">
									下载税单核对单PDF
								</a-menu-item>
								<a-menu-item @click="handleDownloadRax" key="2">
									下载退税联PDF
								</a-menu-item>
								<a-menu-item @click="handleDownloadReleaseNote" key="3">
									下载放行通知PDF
								</a-menu-item>
								<a-menu-item @click="handleDownloadCheck" key="4">
									下载查验通知PDF
								</a-menu-item>
							</a-menu>
							<a-button v-has="'dec:down'" size="small" icon="export" type="primary">
								下载申报相关PDF文件
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<a-button v-has="'dec:push'" type="primary" icon="plus" @click="isCreateAndSendByDecHead">生成电子委托协议</a-button>
<!--						<a-button icon="bot" type="primary" @click="aiAuditHandle">AI审单</a-button>-->
						<a-button
							v-has="'dec:edit'"
							@click="batchDel"
							v-if="selectedRowKeys.length > 0"
							ghost
							type="primary"
							icon="delete">批量删除
						</a-button>
<!--						<a-button v-has="'dec:down'" type="primary" icon="export" :loading="handleTaxLoading" @click="handleDownloadTax">下载税单核对单PDF</a-button>-->
<!--						<a-button v-has="'dec:down'" type="primary" icon="export" :loading="handleRaxLoading" @click="handleDownloadRax">下载退税联PDF</a-button>-->
<!--						<a-button v-has="'dec:down'" type="primary" icon="export" :loading="handleReleaseNoteLoading" @click="handleDownloadReleaseNote">下载放行通知PDF</a-button>-->
<!--						<a-button v-has="'dec:down'" type="primary" icon="export" :loading="handleCheckLoading" @click="handleDownloadCheck">下载查验通知PDF</a-button>-->

						<!--      <a-dropdown>
							<a-button type="primary" icon="plus">新增</a-button>
							<a-menu slot="overlay">
								<a-menu-item>
									<a @click="handleDetail('1')">进口报关单</a>
								</a-menu-item>
								<a-menu-item>
									<a @click="handleDetail('2')">出口报关单</a>
								</a-menu-item>
							</a-menu>
						</a-dropdown>-->
						<!-- <a-button type="primary" icon="download" @click="handleExportXls('货代信息表')">导出</a-button> -->
					</div>
				</template>
				<template #firstOpinion="{ row }">
					{{subStrForColumns(row.firstOpinion, 15)}}

				</template>
				<template #reviewOpinion="{ row }">
					{{subStrForColumns(row.reviewOpinion, 15)}}

				</template>
				<template #hasCdSlot="{ row }">
					<span v-if="row.hasCd=='1'" style="color: green">
            有舱单
					</span>
					<span v-else style="color: red">
						无舱单
					</span>
				</template>
				<template #hasYdSlot="{ row }">
					<span v-if="row.hasYd=='0'">
            无运抵
					</span>
					<a-tooltip title="点击查看运抵回执">
						<span v-if="row.hasYd=='1'">
							<a style="color: green" @click="handleYd(row)">部分运抵(运抵正常)</a>
						</span>
						<span v-else-if="row.hasYd=='2'">
							<a style="color: red" @click="handleYd(row)">部分运抵(运抵异常)</a>
						</span>
						<span v-else-if="row.hasYd=='3'">
							<a style="color: green" @click="handleYd(row)">有运抵(运抵正常)</a>
						</span>
						<span v-else-if="row.hasYd=='4'" >
							<a style="color: red" @click="handleYd(row)">有运抵(运抵异常)</a>
						</span>
					</a-tooltip>
				</template>
				<template #billCodeSlot="{ row }">
					<a-tooltip title='点击查看单票查询信息'>
						<span>
							<a @click="handleCXX(row)">{{ row.billCode }}</a>
						</span>
					</a-tooltip>
				</template>
				<template #shipNameSlot="{ row }">
					<a-tooltip title="点击查看集装箱船舶计划">
						<span>
							<a @click="handleCBJH(row)">{{ row.shipName }}</a>
						</span>
					</a-tooltip>
				</template>
				<template #statusSlot="{ row }">
					<a-popover :overlayStyle="{ width: '700px' }" :title="'状态记录'" placement="right" trigger="hover">
						<template slot="content">
							<div v-if="statusLoading" style="text-align: center; padding: 20px;">
								<a-spin size="small" />
								<span style="margin-left: 8px;">加载中...</span>
							</div>
							<div v-else-if="!statusRecords || statusRecords.length === 0" style="text-align: center; padding: 20px; color: #999;">
								暂无状态记录数据
							</div>
							<a-table v-else :columns="statusColumns" :dataSource="statusRecords" :loading="statusLoading" :pagination="false"
											 size="small">
								<template slot="reason" slot-scope="text, record">
									<!-- 原始单证校验状态的特殊处理 -->
									<template v-if="record.status === '原始单证校验'">
										<!-- 没有数据时显示正常 -->
										<template v-if="!text || text === '-' || text === 'undefined'">
											正常
										</template>
										<!-- 有数据时显示AI审单异常 -->
										<template v-else>
											<a size="small" type="link" style="color: red;" @click="aiCheckHandle(row)">AI审单异常</a>
										</template>
									</template>
									<!-- 待逻辑复审状态的特殊处理 -->
									<template v-else-if="record.status === '待逻辑复审'">
										<!-- 没有数据时显示导出发票按钮 -->
										<template v-if="!text || text === '-' || text === 'undefined'">
											<a size="small" type="link" @click="handleExportInvoice(record)">导出原始发票清单</a>
										</template>
										<!-- 有数据且包含验证失败信息时显示验证失败按钮 -->
										<template v-else-if="isValidationFailureData(text)">
											<a size="small" type="link" @click="handleExportInvoice(record)">导出原始发票清单</a><br/>
											<a size="small" type="link" style="color: red;" @click="showValidationErrors(record)">历史数据校验异常</a>
										</template>
										<!-- 其他有数据的情况 -->
										<template v-else-if="text.length <= 20">{{ text }}</template>
										<div v-else>
											<span>{{ text.substring(0, 20) }}...</span>
											<a size="small" type="link" @click="showDetailData(record)">显示更多</a>
										</div>
									</template>
									<!-- 非待逻辑复审状态的正常处理 -->
									<template v-else-if="!text || text === '-' || text === 'undefined'">-</template>
									<template v-else-if="text.length <= 20">{{ text }}</template>
									<div v-else>
										<span>{{ text.substring(0, 20) }}...</span>
										<a size="small" type="link" @click="showDetailData(record)">显示更多</a>
									</div>
								</template>
							</a-table>
						</template>
						<span v-if="row.status == '1'" @mouseenter="getStatusRecords(row)">
							<a class="ai-processing-text">
								AI制单中
								<span class="dot-loader">
									<span class="dot"></span>
									<span class="dot"></span>
									<span class="dot"></span>
								</span>
							</a>
						</span>
						<span v-if="row.status == '2'" @mouseenter="getStatusRecords(row)">
							<a class="ai-processing-text">
								数据清洗处理中
								<span class="dot-loader">
									<span class="dot"></span>
									<span class="dot"></span>
									<span class="dot"></span>
								</span>
							</a>
						</span>
						<span v-if="row.status == '3'" @mouseenter="getStatusRecords(row)">
							<a>待逻辑复审</a>
						</span>
						<span v-if="row.status == '-1'" @mouseenter="getStatusRecords(row)">
							<a style="color: red">大模型调用失败</a>
						</span>
					</a-popover>
				</template>
				<template #action="{ row }">
					<a-dropdown>
						<a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
						<!-- <a class="ant-dropdown-link">更多 <a-icon type="down" /></a> -->
						<a-menu slot="overlay">
							<a-menu-item>
								<a @click="handleEdit(row)">编辑</a>
							</a-menu-item>
							<a-menu-item v-has="'dec:edit'">
								<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(row.id, row.updateTime,row)">
									<a>删除</a>
								</a-popconfirm>
							</a-menu-item>
							<!--              <a-menu-item>
															<a @click="handlePushDec(record.id)">推送</a>
														</a-menu-item>-->
							<a-menu-item v-has="'dec:edit'">
								<a @click="handleContrastDec(row)">数据对比</a>
							</a-menu-item>
							<!--              <a-menu-item>-->
							<!--                <a-popconfirm title="确定打印吗?" @confirm="() => handleprintDec(record.id)">-->
							<!--                  <a>打印</a>-->
							<!--                </a-popconfirm>-->
							<!--								&lt;!&ndash;                <a @click="handleprintDec(record.id)">打印</a>&ndash;&gt;-->
							<!--              </a-menu-item>-->
							<a-menu-item>
								<a @click="handleEditHis(row)">回执</a>
							</a-menu-item>
						</a-menu>
					</a-dropdown>

				</template>
				<template #ediInfo="{ row }">
					<a @click="handleEditHis(row)">{{ row.ediInfo }}</a>
				</template>

				<template #etaSlot="{ row }">
					<a-tooltip title="点击查看船舶计划详情">
						<a @click="handleEta(row)">{{ row.eta }}</a>
					</a-tooltip>
				</template>

			</query-vxe-grid>



		</div>
		<order-info-by-dec ref="orderInfoByDec"></order-info-by-dec>
		<edi-status-list ref="ediStatusList"></edi-status-list>
		<!--初复审modal-->
		<j-modal
			:title="title"
			:width="600"
			:visible="visible"
			@ok="handleOk"
			@cancel="handleCancel"
			cancelText="关闭"
		>
			<template slot="footer">
				<a-button type="default" @click="handleCancel">取消</a-button>
				<a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
			</template>
			<a-spin :spinning="confirmLoading">
				<a-form-model ref="form" :model="model" :rules="validatorRules">
					<a-row>
						<a-card size="small" :bordered="false" title="">
							<a-col v-if="isFirst">
								<a-form-model-item
									label="初审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="firstOpinion"
								>
									<j-remarks-component
										v-model="model.firstOpinion"
										placeholder="请输入初审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
							<a-col v-if="!isFirst">
								<a-form-model-item
									label="复审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="reviewOpinion"
								>
									<j-remarks-component
										v-model="model.reviewOpinion"
										placeholder="请输入复审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
						</a-card>
					</a-row>
				</a-form-model>
			</a-spin>
		</j-modal>
		<!--		导出报关单详情选择字段列modal-->
		<a-modal
			title="请选择要导出的字段"
			:width="800"
			:visible="customExportVisible"
			@ok="handleCustomExportDec"
			@cancel="customExportVisible=false"
			cancelText="关闭"
			:confirmLoading="handleCustomExportDecLoading"
		>
<!--			<div :style="{ borderBottom: '1px solid #E9E9E9' }">-->
<!--				<a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">-->
<!--					全选-->
<!--				</a-checkbox>-->
<!--			</div>-->
<!--			<br />-->

<!--			<a-checkbox-group-->
<!--				@change="onColSettingsChange"-->
<!--				v-model="settingColumns"-->
<!--				:default-value="settingColumns"-->
<!--			>-->
<!--			表头-->
				<span style="font-weight: bold;font-size: 15px">表头字段</span>
				<a-checkbox style="margin-left: 10px;font-size: 12px" class="myCheckBox" :indeterminate="indeterminateDecHead"
										:checked="checkAllDecHead" @change="onCheckDecHeadChange">
					表头全选
				</a-checkbox>
				<a-checkbox-group
					@change="onDecHeadColumnsChange"
					v-model="decHeadColumnsField"
					:default-value="decHeadColumnsField"
				>
				<a-row>
					<template v-for="item in decHeadColumns">
						<template>
							<a-col :span="6">
								<a-checkbox :value="item.field">{{ item.fieldName }}</a-checkbox>
							</a-col>
						</template>
					</template>
				</a-row>
				</a-checkbox-group>
			<!--			表体-->
				<span style="font-weight: bold;font-size: 15px">表体字段</span>
			<a-checkbox style="margin-left: 10px;font-size: 12px" class="myCheckBox" :indeterminate="indeterminateDecList"
									:checked="checkAllDecList" @change="onCheckDecListChange">
				表体全选
			</a-checkbox>
			<a-checkbox-group
				@change="onDecListColumnsChange"
				v-model="decListColumnsField"
				:default-value="decListColumnsField"
			>
				<a-row>
					<template v-for="item in decListColumns">
						<template>
							<a-col :span="6">
								<a-checkbox :value="item.field">{{ item.fieldName }}</a-checkbox>
							</a-col>
						</template>
					</template>
				</a-row>
			</a-checkbox-group>
			<!--			集装箱-->
			<a-row>
			<span style="font-weight: bold;font-size: 15px">集装箱字段</span>
			<a-checkbox style="margin-left: 10px;font-size: 12px" class="myCheckBox" :indeterminate="indeterminateBox"
									:checked="checkAllBox" @change="onCheckBoxChange">
				集装箱全选
			</a-checkbox>
			</a-row>
			<a-checkbox-group
				style="width: 100%"
				@change="onBoxColumnsChange"
				v-model="boxColumnsField"
				:default-value="boxColumnsField"
			>
					<template v-for="item in boxColumns">
						<template>
							<a-col :span="6">
								<a-checkbox :value="item.field">{{ item.fieldName }}</a-checkbox>
							</a-col>
						</template>
					</template>
			</a-checkbox-group>


<!--			</a-checkbox-group>-->
		</a-modal>
		<!-- 查看报关单税单信息 -->
		<tax-modal ref="taxModal" />
		<!-- 查看船期计划详情 -->
		<ship-plan-modal ref="shipPlanModal" />
		<!-- 查看船舶计划 -->
		<container-cargo-node-modal ref="containerCargoNodeModal" />
		<!-- 查看船信息 -->
		<ship-info-modal ref="shipInfoModal" />

		<!-- AI制单 -->
		<ai-maker-modal ref="aiMakerModal" @ok="loadData(1)" />
		<!-- 报关单AI配置 -->
		<ai-setting-modal ref="aiSettingModal" />
		<!-- 原始数据详情弹窗 -->
		<a-modal :footer="null" :visible="detailDataVisible" class="detail-data-modal" destroyOnClose
						 title="原始数据详情" width="900px" @cancel="detailDataVisible = false">
			<a-tabs v-model="activeTabKey" defaultActiveKey="1">
				<a-tab-pane key="1" tab="OCR识别结果">
					<div class="json-detail-container">
						<pre>{{ formatJson(currentDetailData && currentDetailData.detail_ocr) }}</pre>
					</div>
				</a-tab-pane>
				<a-tab-pane key="2" tab="格式化结果">
					<div class="json-detail-container">
						<pre>{{ formatJson(currentDetailData && currentDetailData.detail_format) }}</pre>
					</div>
				</a-tab-pane>
				<a-tab-pane key="3" tab="合并去重结果">
					<div class="json-detail-container">
						<pre>{{ formatJson(currentDetailData && currentDetailData.result) }}</pre>
					</div>
				</a-tab-pane>
			</a-tabs>
			<div style="margin-top: 12px; text-align: right;">
				<a-button size="small" style="margin-right: 8px;" type="primary" @click="copyCurrentTabData()">
					<a-icon type="copy" />复制当前标签页
				</a-button>
				<a-button size="small" style="margin-right: 8px;" @click="copyToClipboard(JSON.stringify(currentDetailData))">
					<a-icon type="copy" />复制全部内容
				</a-button>
				<a-button size="small" @click="detailDataVisible = false">关闭</a-button>
			</div>
		</a-modal>

		<!-- 验证失败错误信息弹窗 -->
		<a-modal :footer="null" :visible="validationErrorVisible" class="validation-error-modal" destroyOnClose
					 title="历史数据校验异常详情" width="800px" @cancel="validationErrorVisible = false">
			<div v-if="validationErrors && validationErrors.length > 0">
				<a-collapse :default-active-key="['0']" :bordered="false">
					<a-collapse-panel v-for="(item, index) in validationErrors" :key="index" :header="getCollapseHeader(item, index)">
						<template slot="extra">
							<a-tag :color="item.状态 === '失败' ? 'red' : 'green'" @click="e => e.stopPropagation()">
								{{ item.状态 || '未知' }}
							</a-tag>
						</template>
						<div class="collapse-content">
							<div class="material-info">
								<p><strong>物料号：</strong>{{ item.物料号 || '未知' }}</p>
								<p><strong>商品名称：</strong>{{ item.商品名称 || '未知' }}</p>
							</div>
							<div v-if="item.错误 && item.错误.length > 0" class="error-list">
								<h4><a-icon type="exclamation-circle" style="color: #ff4d4f; margin-right: 8px;" />异常详情：</h4>
								<div class="error-items">
									<div v-for="(error, errorIndex) in item.错误" :key="errorIndex" class="error-item">
										<a-icon type="close-circle" style="color: #ff4d4f; margin-right: 8px;" />
										{{ error }}
									</div>
								</div>
							</div>
							<div v-else class="no-errors">
								<a-icon type="check-circle" style="color: #52c41a; margin-right: 8px;" />
								暂无异常信息
							</div>
						</div>
					</a-collapse-panel>
				</a-collapse>
			</div>
			<div v-else style="text-align: center; padding: 20px; color: #999;">
				暂无校验异常信息
			</div>
			<div style="margin-top: 12px; text-align: right;">
				<a-button size="small" @click="validationErrorVisible = false">关闭</a-button>
			</div>
		</a-modal>

		<!-- edi内部处理过程详情 -->
		<original-edi
			ref="originalEdi"
			:visible.sync="recycleOriginalBinVisible"
			@ok="modalFormOk"
		/>

		<!-- AI审单 -->
		<ai-check-modal ref="aiCheckModal" />
	</a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {mixinDevice} from "@/utils/mixin"
import {filterDictTextByCache} from "@comp/dict/JDictSelectUtil"
import {subStrForAddr, subStrForColumns} from "@/utils/util"
import Sortable from 'sortablejs'
import {
	deleteAction,
	putAction,
	getAction,
	downloadFile,
	postAction,
	_postAction,
	downloadFileBase64, openPdfInBrowser
} from '@api/manage'
import OrderInfoByDec from "@views/Business/customs-declaration/OrderInfoByDec.vue"
import {getDecById} from "@/api/dec/dec"
import EdiStatusList from '@/views/Business/component/modal/EdiStatusList.vue'
import InputHistory from '@/components/inputHistory/InputHistory'
import TaxModal from "@/views/Business/customs-declaration/components/TaxModal.vue";
import { MyXGridMixin } from '@/mixins/MyXGridMixin'
import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid'
import SearchSelectPopconfirm from "@/components/yorma/SearchSelectPopconfirm/SearchSelectPopconfirm.vue";
import store from '@/store'
import Vue from 'vue'
import {TENANT_ID, UI_CACHE_DB_DICT_DATA, USER_INFO} from '@/store/mutation-types'
import ShipPlanModal from "@/views/Business/customs-declaration/components/ShipPlanModal.vue";
import ContainerCargoNodeModal from "@/views/Business/customs-declaration/components/ContainerCargoNodeModal.vue";
import ShipInfoModal from "@/views/Business/customs-declaration/components/ShipInfoModal.vue";
import AiMakerModal from "@/views/Business/customs-declaration/components/AiMakerModal.vue";
import AiSettingModal from "@/views/Business/customs-declaration/components/AiSettingModal.vue";
import JGptIcon from '@/components/jeecg/JGptIcon.vue';
import OriginalEdi from "@/views/Business/customs-declaration/OriginalEdi.vue";
import AiCheckModal from "@/views/Business/customs-declaration/components/AiCheckModal.vue";
export default {
	name: "Declaration",
	components: {
		AiCheckModal,
		EdiStatusList, OrderInfoByDec, InputHistory, OriginalEdi,
		TaxModal, Sortable, QueryVxeGrid, SearchSelectPopconfirm, AiMakerModal, AiSettingModal,
		ShipPlanModal, ContainerCargoNodeModal, ShipInfoModal, JGptIcon
	},
	mixins: [MyXGridMixin,mixinDevice],
	data() {
		return {
			recycleOriginalBinVisible: false,
			isAdmin: false, // 是否超级管理员
			statusLoading: false, // 状态记录加载状态
			statusRecords: [], // 状态记录数据
			statusColumns: [
				{
					title: '状态',
					dataIndex: 'status',
					align: 'center',
					width: 120
				},
				{
					title: '时间',
					dataIndex: 'time',
					align: 'center',
					width: 180
				},
				{
					title: '操作人',
					dataIndex: 'operator',
					align: 'center',
				},
				{
					title: 'AI解析原始数据',
					dataIndex: 'reason',
					align: 'center',
					width: 180,
					scopedSlots: { customRender: 'reason' }
				}
			],
			// 添加数据详情弹窗相关状态
			detailDataVisible: false,
			currentDetailData: '',
			// 验证失败弹窗相关数据
			validationErrorVisible: false,
			validationErrors: [],
			activeTabKey: '1',
			ydOptions: [
				{ label: '无运抵', value: '0' },
				{ label: '部分运抵(运抵正常)', value: '1' },
				{ label: '部分运抵(运抵异常)', value: '2' },
				{ label: '有运抵(运抵正常)', value: '3' },
				{ label: '有运抵(运抵异常)', value: '4' }
			],
			customExportDecType:'',//数据导出的类型 1 查询导出 2选择导出
			dictCodeCus:'',
			decStatus: [
				{ text: '保存', value: '1' },
				{ text: '结关', value: '10' },
				{ text: '查验通知', value: '11' },
				{ text: '已申报', value: '2' },
				{ text: '海关入库成功', value: '4' },
				{ text: '退单', value: '6' },
				{ text: '审结', value: '7' },
				{ text: '删单', value: '8' },
				{ text: '放行', value: '9' },
				{ text: '公自用物品核准通过', value: 'S' },
				{ text: '公自用物品退单', value: 'T' },
				{ text: '公自用物品待核准', value: 'U' },
				// { title: '查验通知', value: 'C' }
			],
			selectCreateTime:'',
			selectUpdateTime:'',
			selectAppDate:'',
			searchColumns: this.$queryCondOption.decSearchColumns,
			originalColumns: this.$queryCondOption.decOriginalColumns,
			handleCheckLoading: false,
			tableKey:'',
			tableRef:'xGrid',
			handleReleaseNoteLoading: false,
			handleRaxLoading: false,
			handleTaxLoading: false,
			printButtonLoading:false,
			printBatchLoading:false,
			handleCustomExportDecLoading:false,
			exportButtonLoading:false,
			settingColumns:[],
			decHeadColumnsField:[],
			decListColumnsField:[],
			boxColumnsField:[],
			checkAll: false,
			checkAllDecHead:false,
			checkAllDecList:false,
			checkAllBox:false,
			indeterminate:false,
			indeterminateDecHead:false,
			indeterminateDecList:false,
			indeterminateBox:false,
			auditLoading:false,
			decHeadColumns:[],
			decListColumns:[],
			boxColumns:[],
			customExportVisible:false,
			model: {firstOpinion: '', reviewOpinion: ''},
			confirmLoading: false,
			visible: false,
			isFirst: false,
			title: '初审意见',
			validatorRules: {
				firstOpinion: [{ required: true, message: '请输入审核意见!' }],
				reviewOpinion: [{ required: true, message: '请输入复审意见!' }]
			},
			queryParam:{},
			url: {
				list: '/DecHead/dec-head/list',
				delete: '/DecHead/dec-head/delete',
				deleteBatch: '/DecHead/dec-head/deleteBatch',
				updatePushStatusById: '/DecHead/dec-head/updatePushStatusById',
				exportDecPDF: '/DecHead/dec-head/exportDecPDF',
				getCDTaxPdf: '/DecHead/dec-head/getCDTaxPdf',
				getCDPdfRtx: '/DecHead/dec-head/getCDPdfRtx',
				getCDReleaseNote: '/DecHead/dec-head/getCDReleaseNote',
				getCDCheckNote: '/DecHead/dec-head/getCDCheckNote',
				saveAndSendByDecId: '/business/elecProtocol/saveAndSendByDecId',
			},
			/* 数据源 */
			dataSource: [],
			/* 分页参数 */
			ipagination: {
				total: 0,
				currentPage: 1,
				pageSize: 15,
				pageSizes: [15, 30, 50, 100, 200],
				perfect: true
			},
			defColumns:[
				{
					type: 'checkbox',
					title: '',
					align: 'center',
					width: 50,
					fixed: 'left',
				},
				{
				title: '统一编号',
				align: 'center',
				sorter: false,
					field: 'seqNo',
					width: 160,
				// scopedSlots: { customRender: 'seqNo' },
			},
				{
					title: '报关单号',
					align: 'center',
					sorter: false,
					field: 'clearanceNo',
					width: 160,
				},
				{
					title: '提运单号',
					align: 'center',
					sorter: false,
					field: 'billCode',
					width: 160,
				},
				{
					title: '申报状态',
					align: 'center',
					sorter: false,
					field: 'decStatus',
					width: 100,
					formatter:this.formatterDecStatus
				},
				{
					title: '境内收发货人',
					align: 'center',
					sorter: false,
					field: 'optUnitName',
					width: 180,
				},
				{
					title: '进出口',
					align: 'center',
					sorter: false,
					field: 'ieFlag',
					width: 100,
					formatter:function ({text}) {
						if(text=='I'){
							return "进口";
						}else if(text=='E'){
							return "出口";
						}else{
							return text;
						}
					}
				},
				{
					title: '报关单类型',
					align: 'center',
					sorter: false,
					field: 'dclTrnRelFlag',
					width: 100,
					formatter:function ({text}) {
						if(text=='0'){
							return "一般报关单";
						}else if(text=='1'){
							return "转关提前报关单";
						}else if(text=='2'){
							return "备案清单";
						}else if(text=='3'){
							return "转关提前备案清单";
						}else if(text=='4'){
							return "出口二次转关";
						}else{
							return text;
						}
					}
				},
				{
					title: '备案号',
					align: 'center',
					sorter: false,
					width: 100,
					field: 'recordNumber',
				},
				{
					title: '监管方式',
					align: 'center',
					sorter: false,
					width: 100,
					field: 'tradeTypeCode',
					formatter: ({text}) => {
						//字典值替换通用方法
						return filterDictTextByCache('JGFS', text);
					}
				},
				{
					title: '消费使用单位',
					align: 'center',
					sorter: false,
					width: 200,
					field: 'deliverUnitName',
				},
				{
					title: '申报日期',
					align: 'center',
					sorter: false,
					width: 100,
					field: 'appDate',
				},
				{
					title: '订单号',
					align: 'center',
					sorter: false,
					width: 100,
					field: 'orderProtocolNo',
				},
				{
					title: '是否有舱单',
					align: 'center',
					field: 'hasCd',
					width: 100,
					formatter:function ({text}) {
						if (text == '1') {
							return "是";
						} else {
							return '否';
						}
					}
				},
				{
					title: '运输方式',
					align: 'center',
					sorter: false,
					width: 100,
					field: 'shipTypeCode_dictText',
				},
				{
					field: 'initialReviewStatus',
					title: '初复审状态',
					align: 'center',
					width: 120,
					formatter:function ({cellValue}) {
						if (cellValue=='0'){
							return '未审核'
						}else if(cellValue=='1'){
							return "已初审/未复审";
						}else if(cellValue=='2'){
							return '已复审'
						}else {
							return '未审核'
						}
					},
				},
				{
					field: 'firstTrialBy',
					align: 'center',
					width: 100,
					title: '初审人'
				},
				{
					field: 'firstTrialDate',
					align: 'center',
					width: 160,
					title: '初审时间'
				},
				{
					field: 'firstOpinion',
					align: 'center',
					title: '初审意见',
					width: 100,
					slots: {
						default: 'firstOpinion'
					}
				},
				{
					field: 'reviewBy',
					align: 'center',
					width: 100,
					title: '复审人'
				},
				{
					field: 'reviewDate',
					align: 'center',
					width: 160,
					title: '复审时间'
				},
				{
					field: 'reviewOpinion',
					align: 'center',
					width: 200,
					title: '复审意见',
					slots: {
						default: 'reviewOpinion'
					}
				},
				{
					title: '操作',
					field: 'action',
					align: 'center',
					fixed: 'right',
					width: 80,
					slots: {
						default: 'action'
					}
				},
			],


			columns: [
				{
					title: '统一编号',
					align: 'center',
					sorter: false,
					dataIndex: 'seqNo',
					// scopedSlots: { customRender: 'seqNo' },
				},
				{
					title: '报关单号',
					align: 'center',
					sorter: false,
					dataIndex: 'clearanceNo',
				},
				{
					title: '提运单号',
					align: 'center',
					sorter: false,
					dataIndex: 'billCode',
				},
				{
					title: '申报状态',
					align: 'center',
					sorter: false,
					dataIndex: 'decStatus',
					customRender: (text) => {
						return this.formatterDecStatus(text)
					}
				},
				{
					title: '境内收发货人',
					align: 'center',
					sorter: false,
					dataIndex: 'optUnitName',
				},
				{
					title: '进出口',
					align: 'center',
					sorter: false,
					dataIndex: 'ieFlag',
					customRender:function (text) {
						if(text=='I'){
							return "进口";
						}else if(text=='E'){
							return "出口";
						}else{
							return text;
						}
					}
				},
				{
					title: '报关单类型',
					align: 'center',
					sorter: false,
					dataIndex: 'dclTrnRelFlag',
					customRender:function (text) {
						if(text=='0'){
							return "一般报关单";
						}else if(text=='1'){
							return "转关提前报关单";
						}else if(text=='2'){
							return "备案清单";
						}else if(text=='3'){
							return "转关提前备案清单";
						}else if(text=='4'){
							return "出口二次转关";
						}else{
							return text;
						}
					}
				},
				{
					title: '备案号',
					align: 'center',
					sorter: false,
					dataIndex: 'recordNumber',
				},
				{
					title: '监管方式',
					align: 'center',
					sorter: false,
					dataIndex: 'tradeTypeCode',
					customRender: (text) => {
						//字典值替换通用方法
						return filterDictTextByCache('JGFS', text);
					}
				},
				{
					title: '消费使用单位',
					align: 'center',
					sorter: false,
					dataIndex: 'deliverUnitName',
				},
				{
					title: '申报日期',
					align: 'center',
					sorter: false,
					dataIndex: 'appDate',
				},
				{
					title: '订单号',
					align: 'center',
					sorter: false,
					dataIndex: 'orderProtocolNo',
				},
				{
					title: '是否有舱单',
					align: 'center',
					dataIndex: 'hasCd',
					customRender: function (text) {
						if (text == '1') {
							return "有舱单";
						} else {
							return '无舱单';
						}
					}
				},
				{
					title: '是否有运抵',
					align: 'center',
					dataIndex: 'hasCd',
					customRender: function (text) {
						if (text == '1') {
							return "有运抵";
						} else {
							return '无运抵';
						}
					}
				},
				{
					title: '运输方式',
					align: 'center',
					sorter: false,
					dataIndex: 'shipTypeCode_dictText',
				},
				{
					dataIndex: 'initialReviewStatus',
					title: '初复审状态',
					align: 'center',
					width: 120,
					customRender: function (cellValue) {
						if (cellValue=='0'){
							return '未审核'
						}else if(cellValue=='1'){
							return "已初审/未复审";
						}else if(cellValue=='2'){
							return '已复审'
						}else {
							return '未审核'
						}
					},
				},
				{
					dataIndex: 'firstTrialBy',
					align: 'center',
					title: '初审人'
				},
				{
					dataIndex: 'firstTrialDate',
					align: 'center',
					title: '初审时间'
				},
				{
					dataIndex: 'firstOpinion',
					align: 'center',
					title: '初审意见',
					scopedSlots: { customRender: 'firstOpinion' }
				},
				{
					dataIndex: 'reviewBy',
					align: 'center',
					title: '复审人'
				},
				{
					dataIndex: 'reviewDate',
					align: 'center',
					width: 200,
					title: '复审时间'
				},
				{
					dataIndex: 'reviewOpinion',
					align: 'center',
					width: 200,
					title: '复审意见',
					scopedSlots: { customRender: 'reviewOpinion' }
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 20,
					scopedSlots: { customRender: 'action' },
				},
			],
			tableToolbar: {
				perfect: true,
				refresh: {
					query: () => this.loadData(1)
				},
				zoom: true,
				custom: true,
				slots: {
					buttons: 'toolbar_buttons'
				}
			},
			labelCol: {
				xs: { span: 9 },
				// sm: { span: 9 },
				xl:{ span: 12 }
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 14 },
			},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
		}
	},
	created() {
		try {
			const userInfo = Vue.ls.get(USER_INFO)
			this.isAdmin = userInfo &&
				typeof userInfo.username === 'string' &&
				userInfo.username === 'admin'
			this.queryParam.tenantId = userInfo.tenantId
		} catch (error) {
			console.error('获取用户信息失败:', error)
			this.isAdmin = false
			this.queryParam.tenantId = null
		}
		this.updatePopconfirm()
		this.searchQuery()
		// 查询当前租户id
		let tenantId = Vue.ls.get(TENANT_ID)
		if (!tenantId) {
			tenantId = 0
		}
		this.dictCodeCus = 'customs_broker_info,customs_broker_name,id,tenant_id=' + tenantId + ' and del_flag=0'
		// this.rowDrop() ;
		// this.columnDrop();
	},
	computed: {
		gridOptions() {
			const gridOptions = {
				id: 'InComeCostTable',
				pagerConfig: {
					currentPage:this.ipagination.currentPage,
					pageSize:this.ipagination.pageSize,
					pageSizes: [15, 30, 50, 100, 200],
					total:this.ipagination.total
				},
				toolbarConfig: {
					perfect: true,
					refresh: {
						query: () => this.loadData(1)
					},
					zoom: true,
					custom: true,
					slots: {
						buttons: 'toolbar_buttons'
					}
				},
				columns: [
					{
						type: 'checkbox',
						field: 'checkbox',
						align: 'center',
						width: 50,
						fixed: 'left',
					},
					{
						title: '统一编号',
						align: 'center',
						sorter: false,
						field: 'seqNo',
						width: 160,
						sortable: true,
						remoteSort:true,
						// scopedSlots: { customRender: 'seqNo' },
					},
					{
						title: '海关编号',
						align: 'center',
						sorter: false,
						field: 'clearanceNo',
						width: 160,
						sortable: true,
						remoteSort:true,
					},
					{
						title: '进出口',
						align: 'center',
						sorter: false,
						field: 'ieFlag',
						width: 80,
						sortable: true,
						remoteSort:true,
						formatter:function ({ cellValue, row, column }) {
							if(cellValue=='I'){
								return "进口";
							}else if(cellValue=='E'){
								return "出口";
							}else{
								return cellValue;
							}
						}
					},
					{
						title: '申报日期',
						align: 'center',
						sorter: false,
						field: 'appDate',
						width: 90,
						sortable: true,
						remoteSort:true,
					},
					{
						title: '报关单状态',
						align: 'center',
						sorter: false,
						field: 'status',
						width: 140,
						sortable: true,
						remoteSort: true,
						slots: {
							default: 'statusSlot'
						}
					},
					{
						title: '申报状态',
						align: 'center',
						sorter: false,
						field: 'decStatus',
						width: 100,
						sortable: true,
						remoteSort:true,
						formatter:this.formatterDecStatus
					},
					{
						title: '提运单号',
						align: 'center',
						sorter: false,
						field: 'billCode',
						sortable: true,
						remoteSort:true,
						width: 160,
						slots: {
							default: 'billCodeSlot'
						}
					},
					{
						title: '是否有舱单',
						align: 'center',
						field: 'hasCd',
						width: 100,
						sortable: true,
						remoteSort:true,
						slots: {
							default: 'hasCdSlot'
						}
					},
					{
						title: '运抵回执',
						align: 'center',
						field: 'hasYd',
						width: 130,
						sortable: true,
						remoteSort:true,
						slots: {
							default: 'hasYdSlot'
						}
					},
					{
						title: '运输工具名称',
						align: 'center',
						field: 'shipName',
						width: 130,
						sortable: true,
						remoteSort:true,
						slots: {
							default: 'shipNameSlot'
						}
					},
					{
						title: '预计到港时间',
						align: 'center',
						field: 'eta',
						width: 140,
						sortable: true,
						remoteSort:true,
						slots: {
							default: 'etaSlot'
						}
					},
					{
						title: '装载放行时间',
						align: 'center',
						field: 'loadReleaseTime',
						width: 140,
						sortable: true,
						remoteSort:true
					},
					{
						title: 'EDI状态信息',
						align: 'center',
						sorter: false,
						field: 'ediInfo',
						width: 120,
						slots: {
							default: 'ediInfo'
						}
					},
					{
						title: '境内收发货人',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'optUnitName',
						width: 180,
					},
					{
						title: '境内收发货人信用代码',
						align: 'center',
						sorter: false,
						field: 'optUnitSocialCode',
						width: 180,
					},

					{
						title: '监管方式',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'tradeTypeCode_dictText',
						width: 160,
					},
					{
						title: '合同协议号',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'contract',
						width: 160,
					},
					{
						title: '首项商品名称',
						align: 'center',
						sorter: false,
						field: 'firstGoodsName',
						width: 160,
					},
					{
						title: '件数',
						align: 'center',
						sorter: false,
						field: 'packs',
						sortable: true,
						remoteSort:true,
						width: 160,
					},
					{
						title: '毛重',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'grossWeight',
						width: 160,
					},
					{
						title: '申报金额',
						align: 'center',
						sorter: false,
						field: 'total',
						sortable: true,
						remoteSort:true,
						width: 160,
					},
					{
						title: '商品名称汇总',
						align: 'center',
						sorter: false,
						field: 'allGoodsName',
						width: 160,
					},
					{
						title: '集装箱号汇总',
						align: 'center',
						sorter: false,
						field: 'allContainerId',
						width: 160,
					},
					{
						title: '集装箱数',
						align: 'center',
						sorter: false,
						field: 'containerIdCount',
						width: 160,
					},
					{
						title: '商品项数',
						align: 'center',
						sorter: false,
						field: 'goodsItemCount',
						width: 160,
					},
					{
						title: '制单人',
						align: 'center',
						sorter: false,
						field: 'createPerson',
						width: 160,
					},
					{
						title: '创建日期',
						align: 'center',
						sorter: false,
						field: 'createTime',
						sortable: true,
						remoteSort:true,
						width: 160,
					},
					{
						title: '申报人员',
						align: 'center',
						sorter: false,
						field: 'declarant',
						width: 160,
					},

					{
						title: '放行日期',
						align: 'center',
						sorter: false,
						field: 'releaseDate',
						sortable: true,
						remoteSort:true,
						width: 160,
					},
					{
						title: '结关日期',
						align: 'center',
						sorter: false,
						field: 'finalDate',
						sortable: true,
						remoteSort:true,
						width: 160,
					},

					{
						title: '申报单位名称',
						align: 'center',
						sorter: false,
						field: 'declareUnitName',
						width: 160,
					},
					{
						title: '申报地海关',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'declarePlace_dictText',
						width: 160,
					},
					{
						title: '进出境关别',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'outPortCode_dictText',
						width: 160,
					},

					{
						title: '入境/离境口岸',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'entyPortCode_dictText',
						width: 160,
					},
					{
						title: '贸易国别',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'tradeCountry_dictText',
						width: 160,
					},
					{
						title: '启运/抵运国',
						align: 'center',
						sorter: false,
						sortable: true,
						remoteSort:true,
						field: 'arrivalArea_dictText',
						width: 160,
					},
					{
						title: '报关单类型',
						align: 'center',
						sorter: false,
						field: 'dclTrnRelFlag',
						sortable: true,
						remoteSort:true,
						width: 100,
						formatter:function ({ cellValue, row, column }) {
							if(cellValue=='0'){
								return "一般报关单";
							}else if(cellValue=='1'){
								return "转关提前报关单";
							}else if(cellValue=='2'){
								return "备案清单";
							}else if(cellValue=='3'){
								return "转关提前备案清单";
							}else if(cellValue=='4'){
								return "出口二次转关";
							}else{
								return cellValue;
							}
						}
					},
					{
						title: '订单号',
						align: 'center',
						sorter: false,
						width: 100,
						field: 'orderProtocolNo',
						sortable: true,
						remoteSort:true,
					},
					{
						field: 'pushStatus',
						title: '推送状态',
						align: 'center',
						sortable: true,
						remoteSort:true,
						width: 100,
						formatter: function ({ cellValue, row, column }) {
						if(cellValue=='1'){
								return "已推送";
							}else {
								return '未推送'
							}
						},
					},
					{
						field: 'initialReviewStatus',
						title: '初复审状态',
						align: 'center',
						sortable: true,
						remoteSort:true,
						visible:false,
						width: 100,
						formatter: function ({ cellValue, row, column }) {
							if (cellValue=='0'){
								return '未审核'
							}else if(cellValue=='1'){
								return "已初审/未复审";
							}else if(cellValue=='2'){
								return '已复审'
							}else {
								return '未审核'
							}
						},
					},
					{
						field: 'firstTrialBy',
						align: 'center',
						title: '初审人',
						visible:false,
						width: 100,
					},
					{
						field: 'firstTrialDate',
						align: 'center',
						title: '初审时间',
						visible:false,
						sortable: true,
						remoteSort:true,
						width: 160,
					},
					{
						field: 'firstOpinion',
						align: 'center',
						title: '初审意见',
						visible:false,
						width: 100,
						slots: {
							default: 'firstOpinion'
						}
					},
					{
						field: 'reviewBy',
						align: 'center',
						title: '复审人',
						visible:false,
						width: 100,
					},
					{
						field: 'reviewDate',
						align: 'center',
						width: 160,
						visible:false,
						sortable: true,
						remoteSort:true,
						title: '复审时间'
					},
					{
						field: 'reviewOpinion',
						align: 'center',
						width: 100,
						title: '复审意见',
						visible:false,
						slots: {
							default: 'reviewOpinion'
						}
					},



					{
						title: '操作',
						field: 'action',
						align: 'center',
						fixed: 'right',
						width: 50,
						slots: {
							default: 'action'
						}
					},
				],
			}
			return gridOptions
		}
	},
	mounted() {
		// 页面加载时检查字典缓存
		this.checkAndLoadDictCache()
	},

	methods: {
		/**
		 * 查看船信息
		 * @param record
		 */
		handleCXX(record) {
			console.log('=====即将显示船信息=====')
			this.$refs.shipInfoModal.open(record)
		},
		/**
		 * 查看船舶计划
		 * @param record
		 */
		handleCBJH(record) {
			console.log('=====即将显示船舶计划=====')
			if (!record.shipName || record.shipName.trim() === '') {
				this.$message.warning('运输工具名称为空，无法查看船舶计划！')
				return
			}
			if (!record.voyage || record.voyage.trim() === '') {
				this.$message.warning('航次号为空，无法查看船舶计划！')
				return
			}
			this.$refs.containerCargoNodeModal.open(record)
		},
		/**
		 * AI审单
		 */
		aiCheckHandle(row) {
			this.$refs.aiCheckModal.show(row.id)
		},
		// 导出发票方法
		handleExportInvoice(record) {
			console.log('handleExportInvoice', record);
			if (!record) {
				this.$message.warning('请选择要导出发票的记录');
				return;
			}
			try {
				this.$message.loading('正在导出发票，请稍候...', 0);
				const params = {
					id: record.sourceId
				};
				// 生成带日期时间的文件名
				const now = new Date();
				const dateTimeStr = now.getFullYear() + 
					String(now.getMonth() + 1).padStart(2, '0') + 
					String(now.getDate()).padStart(2, '0') + '_' + 
					String(now.getHours()).padStart(2, '0') + 
					String(now.getMinutes()).padStart(2, '0') + 
					String(now.getSeconds()).padStart(2, '0');
				const fileName = `发票_${record.sourceId}_${dateTimeStr}.xlsx`;

				downloadFile('/DecHead/dec-head/exportInvoice', fileName, params)
					.then((res) => {
						this.$message.destroy(); // 关闭加载提示
						this.$message.success('发票导出成功');
					})
					.catch((error) => {
						this.$message.destroy(); // 关闭加载提示
						console.error('导出发票失败:', error);
						this.$message.error('导出发票失败，请稍后重试');
					});

			} catch (error) {
				this.$message.destroy(); // 关闭加载提示
				console.error('导出发票异常:', error);
				this.$message.error('导出发票失败，请稍后重试');
			}
		},
		//显示系统内部全部edi回执处理信息
		showOriginalEdi() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (this.selectedRowKeys.length !== 1) {
				this.$message.warning('请选择最多一条记录！')
			} else {
				let row = this.selectionRows[0]
				this.recycleOriginalBinVisible = true
				this.$refs.originalEdi.title = '回执系统处理信息'
				this.$refs.originalEdi.show(this.recycleOriginalBinVisible, row.seqNo)
			}
		},
		/**
		 * 检查字典缓存是否存在，如果不存在则加载
		 */
		checkAndLoadDictCache() {
			const dictCache = Vue.ls.get(UI_CACHE_DB_DICT_DATA)
			if (!dictCache || Object.keys(dictCache).length === 0) {
				console.log('字典缓存不存在，开始加载...')
				this.refleshCache()
			} else {
				console.log('字典缓存已存在')
			}
		},
		/**
		 * 刷新字典缓存
		 */
		refleshCache() {
			getAction("sys/dict/refleshCache").then((res) => {
				if (res.success) {
					getAction("sys/dict/queryAllDictItems").then((res) => {
						if (res.success) {
							Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
							Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
							console.log('字典缓存加载完成')
						}
					})
					// this.$message.success("刷新缓存完成！")
				}
			}).catch(e => {
				// this.$message.warn("刷新缓存失败！")
				console.log("刷新失败", e)
			})
		},
		/**
		 * 报关单AI配置
		 */
		aiSettingHandle() {
			this.$refs.aiSettingModal.show()
		},
		/**
		 * AI制单
		 */
		aiAuditHandle() {
			this.$refs.aiMakerModal.show()
		},
		/**
		 * AI审单
		 */
		// aiAuditHandle() {
		// 	if (this.selectedRowKeys.length !== 1) {
		// 		this.$message.warning('请选择一票报关单！')
		// 		return
		// 	}
		// 	console.log(this.selectedRowKeys)
		// 	this.$router.push({
		// 		path: '/Ai/AiIndex',
		// 		query: {
		// 			id: this.selectedRowKeys[0],
		// 		}
		// 	})
		// },
		isCreateAndSendByDecHead() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请至少选择一条记录！')
				return
			}
			console.log(this.selectionRows)
			for (let i = 0; i < this.selectionRows.length; i++) {
				if (this.selectionRows[i].decStatus != undefined
					&& this.selectionRows[i].decStatus != null
					&& this.selectionRows[i].decStatus != ''
					&& this.selectionRows[i].decStatus != '1') {
					this.$message.warning('所选的报关单包含已申报状态，请检查！')
					return
				}
			}
			let that = this
			this.$confirm({
				title: '操作确认',
				content: '是否生成电子委托协议?',
				onOk: function() {
					that.createAndSendByDecHead()
				},
				onCancel: function() {
				}
			})
		},
		async createAndSendByDecHead() {
			let that = this
			var ids = this.selectedRowKeys.join(',')
			_postAction(that.url.saveAndSendByDecId, {
				ids: ids
			}).then(res => {
				if (res.success) {
					that.$message.success(res.message)
					// that.$tabs.refresh()
					that.onClearSelected()
				} else {
					that.$message.warning(res.message)
				}
			})
		},
		selectAppDateChange(value, dateString){
			this.queryParam.startAppDate = dateString[0].toString()+' 00:00:00'
			this.queryParam.lastAppDate = dateString[1].toString()+' 23:59:59'
		},
		selectCreateTimeChange(value, dateString){
			this.queryParam.startCreateTime = dateString[0].toString()+' 00:00:00'
			this.queryParam.lastCreateTime = dateString[1].toString()+' 23:59:59'
		},
		selectUpdateTimeChange(value, dateString){
			this.queryParam.startUpdateTime = dateString[0].toString()+' 00:00:00'
			this.queryParam.lastUpdateTime = dateString[1].toString()+' 23:59:59'
		},
		updatePopconfirm(args) {
			if (this.isEmpty(args)) {
				var key = this.$route.name + store.getters.userInfo.id + ':colsettings'
				let colSettings = Vue.ls.get(key)
				if (this.isEmpty(colSettings)) {
					return
				}
				args = colSettings
			}
			if (args === 'no') {
				args = []
			}
			for (let searchColumn of this.searchColumns) {
				let item = args.find(item => item === searchColumn.field)
				if (item) {
					searchColumn.visible = true
				} else {
					searchColumn.visible = false
				}
			}
			// this.searchReset()
		},
		// //行拖拽
		// rowDrop() {
		// 	this.$nextTick(() => {
		// 		let xTable = this.$refs[this.tableRef]
		// 		this.sortableObj = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
		// 			handle: 'td',
		// 			onEnd: ({
		// 								newIndex,
		// 								oldIndex
		// 							}) => {
		// 				// 换序操作
		// 				//let tmpdata = this.tdata.map(el=>{return el._X_ID})
		// 				let currRow = this.tdata.splice(oldIndex, 1)[0]
		// 				this.tdata.splice(newIndex, 0, currRow)
		// 				//this.tdata.forEach((el,index) => {el._X_ID = tmpdata[index]})
		//
		// 				//const codeCurrRow = this.code.splice(oldIndex, 1)[0]
		// 				//this.code.splice(newIndex, 0, codeCurrRow)
		//
		// 				this.tableKey ++;
		// 			}
		// 		})
		// 	})
		// },
		// //列拖拽
		// columnDrop() {
		// 	this.$nextTick(() => {
		// 		const $table = this.$refs.xGrid;
		// 		this.sortable = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
		// 			handle: '.vxe-header--column',
		// 			onEnd: ({ item, newIndex, oldIndex }) => {
		// 				const { fullColumn, tableColumn } = $table.getTableColumn();
		// 				const targetThElem = item;
		// 				const wrapperElem = targetThElem.parentNode;
		// 				const newColumn = fullColumn[newIndex];
		// 				if (newColumn.fixed) {
		// 					const oldThElem = wrapperElem.children[oldIndex];
		// 					// 错误的移动
		// 					if (newIndex > oldIndex) {
		// 						wrapperElem.insertBefore(targetThElem, oldThElem);
		// 					} else {
		// 						wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem);
		// 					}
		// 					VXETable.modal.message({ content: '固定列不允许拖动，即将还原操作！', status: 'error' });
		// 					return;
		// 				}
		// 				// 获取列索引 columnIndex > fullColumn
		// 				const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex]);
		// 				const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex]);
		// 				// 移动到目标列
		// 				const currRow = fullColumn.splice(oldColumnIndex, 1)[0];
		// 				fullColumn.splice(newColumnIndex, 0, currRow);
		// 				console.log('111111111111')
		// 				console.log(fullColumn)
		// 				$table.reloadColumn(fullColumn);
		// 			}
		// 		});
		// 	});
		//
		// },

		// 多选行被单击
		async cellClick({ row, column }) {
			this.selectedRowKeys.splice(0, this.selectedRowKeys.length)
			if (column.type !== 'checkbox') {
				this.$refs.xGrid.clearCheckboxRow()
				this.$refs.xGrid.setCheckboxRow(row, true)
			}
			this.selectionRows = await this.$refs.xGrid.getCheckboxRecords()
			let selectedRowKeys = await this.$refs.xGrid.getCheckboxRecords()
			selectedRowKeys.forEach((selectedRowKey, index) => {
				this.$set(this.selectedRowKeys, index, selectedRowKey.id)
			})
		},
		/**
		 * 报关单查验通知(PDF)
		 */
		handleDownloadCheck() {
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			if (!this.selectionRows[0].clearanceNo && !this.selectionRows[0].seqNo) {
				this.$message.warning('统一编号或报关单号必须有其一 !')
				return false
			}
			this.handleCheckLoading = true
			downloadFileBase64(this.url.getCDCheckNote, "查验通知_" + (this.selectionRows[0].clearanceNo || this.selectionRows[0].seqNo) + ".pdf", {
				cusCiqNo: this.selectionRows[0].clearanceNo || this.selectionRows[0].seqNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在查验通知文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.handleCheckLoading = false
			})
		},
		/**
		 * 报关单放行通知(PDF)
		 */
		handleDownloadReleaseNote() {
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			if (!this.selectionRows[0].clearanceNo && !this.selectionRows[0].seqNo) {
				this.$message.warning('统一编号或报关单号必须有其一 !')
				return false
			}
			this.handleReleaseNoteLoading = true
			downloadFileBase64(this.url.getCDReleaseNote, "放行通知_" + (this.selectionRows[0].clearanceNo || this.selectionRows[0].seqNo) + ".pdf", {
				cusCiqNo: this.selectionRows[0].clearanceNo || this.selectionRows[0].seqNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在放行通知文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.handleReleaseNoteLoading = false
			})
		},
		/**
		 * 报关单文件退税联(PDF)
		 */
		handleDownloadRax() {
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			if (!this.selectionRows[0].clearanceNo) {
				this.$message.warning('无报关单号，无法下载文件!')
				return false
			}
			this.handleRaxLoading = true
			downloadFileBase64(this.url.getCDPdfRtx, "退税联_" + this.selectionRows[0].clearanceNo + ".pdf", {
				entryId: this.selectionRows[0].clearanceNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在退税联文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.handleRaxLoading = false
			})
		},
		/**
		 * 下载税单PDF
		 */
		async handleDownloadTax() {
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			if (!this.selectionRows[0].clearanceNo) {
				this.$message.warning('无报关单号，无法下载文件!')
				return false
			}
			this.handleTaxLoading = true
			downloadFileBase64(this.url.getCDTaxPdf, "税单_" + this.selectionRows[0].clearanceNo + ".pdf", {
				entryId: this.selectionRows[0].clearanceNo
			}).then((res) => {
				if (res.success) {
					if (!res.result) {
						this.$message.warning(`此报关单不存在税单文件！`)
					}
				} else {
					this.$message.warning(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.handleTaxLoading = false
			})
		},
		/**
		 * 查看税单信息
		 */
		handleTax() {
			console.log(this.dataSource)
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			if (!this.selectionRows[0].clearanceNo) {
				this.$message.warning('无报关单号，无法查询税单信息!')
				return false
			}
			this.$refs.taxModal.open(this.selectionRows[0])
		},
		/**
		 * 打印报关单
		 * @param flag
		 */
		handleDecPrint(flag) {
			if (this.selectedRowKeys.length !== 1) {
				this.$message.error('请选择一票报关单!')
				return
			}
			let params = {}
			params.decId = this.selectedRowKeys.join(',')
			params.flag = flag
			this.printButtonLoading = true
			// 直接在浏览器中打开PDF
			openPdfInBrowser('/DecHead/dec-head/exportDecComplete', params)
				.then(res => {
					if (!res.success) {
						this.$message.warning(`导出失败! ${res.message}`)
					}
				})
				.finally(() => {
					this.printButtonLoading = false
				})
		},
		//打印下载
		handleExportDecComplete(flag){
			if(this.selectedRowKeys.length==0){
				this.$message.error('请选择至少一票报关单!')
				return
			}
			let params={}
			params.decId=this.selectedRowKeys.join(',')
			params.flag=flag
			this.printButtonLoading=true
			let fileName=''
			if(this.selectedRowKeys.length>1){
				fileName='报关单批量导出.zip'
			}else if('EXCEL'==flag){
				let contract = this.selectionRows[0].contract?this.selectionRows[0].contract:'无合同号'
				let clearanceNo = this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:this.selectionRows[0].id
				// 拼接文件名，格式为 "合同号_报关单号.xls"
				fileName = `${contract}_${clearanceNo}.xls`;
			}else if('PDF'==flag){
				let contract = this.selectionRows[0].contract?this.selectionRows[0].contract:'无合同号'
				let clearanceNo = this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:this.selectionRows[0].id
				// 拼接文件名，格式为 "合同号_报关单号.xls"
				fileName = `${contract}_${clearanceNo}.pdf`;
			}else {
				let contract = this.selectionRows[0].contract?this.selectionRows[0].contract:'无合同号'
				let clearanceNo = this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:this.selectionRows[0].id
				// 拼接文件名，格式为 "合同号_报关单号.xls"
				fileName = `${contract}_${clearanceNo}.pdf`;
			}
			downloadFile('/DecHead/dec-head/exportDecComplete', fileName,
				params).then((res) => {
				if (res.success) {
				} else {
					this.$message.warn(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.printButtonLoading=false
			})

		},
		//打印下载
		handlePrintBatch(flag){
			if(this.selectedRowKeys.length==0){
				this.$message.error('请选择至少一票报关单!')
				return
			}
			let params={}
			params.decId=this.selectedRowKeys.join(',')
			params.flag=flag
			this.printBatchLoading=true
			let fileName=''
			if(this.selectedRowKeys.length>1){
				fileName='报关单批量导出.zip'
			}else if('EXCEL'==flag){
				fileName='报关单'+(this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:
					this.selectionRows[0].id)+'.xlsx'
			}else if('PDF'==flag){
				fileName='报关单'+(this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:
					this.selectionRows[0].id)+'.pdf'
			}else {
				fileName='报关单审核单'+(this.selectionRows[0].clearanceNo?this.selectionRows[0].clearanceNo:
					this.selectionRows[0].id)+'.pdf'
			}
			downloadFile('/DecHead/dec-head/exportDecComplete', fileName,
				params).then((res) => {
				if (res.success) {
				} else {
					this.$message.warn(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.printBatchLoading=false
			})

		},
		handleCustomExportDec(){
			if(this.selectedRowKeys.length==0&&this.ipagination.total>1000){
				this.$message.warn('目前最大导出的票数上限为1000票，请减少票数进行导出')
				return
			}
			let params={}
			if(1===this.customExportDecType){
				const queryParam=JSON.parse(JSON.stringify(this.queryParam))
				params=queryParam
			}
			let settingColumns = []
			settingColumns=this.decHeadColumnsField.concat(this.decListColumnsField,this.boxColumnsField)

			params.columnList=settingColumns.join(',')
			params.id=2===this.customExportDecType? this.selectedRowKeys.join(','):null

			this.handleCustomExportDecLoading=true
			downloadFile('/DecHead/dec-head/customExportDec', '报关单详情导出.xlsx',
				params).then((res) => {
				if (res.success) {
					this.customExportVisible=false
				} else {
					this.$message.warn(`导出失败!${res.message}`)
				}
			}).finally(() => {
				this.customExportVisible=false
				this.handleCustomExportDecLoading=false
			})

		},
		onColSettingsChange (checkedValues) {
			this.settingColumns = checkedValues
			this.indeterminate = !!this.settingColumns.length && this.settingColumns.length <
				this.decHeadColumns.length+this.decListColumns.length;
			this.checkAll = this.settingColumns.length === this.decHeadColumns.length+this.decListColumns.length;
		},
		onDecHeadColumnsChange(checkedValues){
			this.decHeadColumnsField = checkedValues
			this.indeterminateDecHead = !!this.decHeadColumnsField.length && this.decHeadColumnsField.length <
				this.decHeadColumns.length;
			this.checkAllDecHead = this.decHeadColumnsField.length === this.decHeadColumns.length;
		},
		onDecListColumnsChange(checkedValues){
			this.decListColumnsField = checkedValues
			this.indeterminateDecList = !!this.decListColumnsField.length && this.decListColumnsField.length <
				this.decListColumns.length;
			this.checkAllDecList = this.decListColumnsField.length === this.decListColumns.length;
		},
		onBoxColumnsChange(checkedValues){
			this.boxColumnsField = checkedValues
			this.indeterminateBox = !!this.boxColumnsField.length && this.boxColumnsField.length <
				this.boxColumns.length;
			this.checkAllBox = this.boxColumnsField.length === this.boxColumns.length;
		},
		onCheckAllChange(e) {
			if (e.target.checked) {
				let allSettingColumn = []
				let decHeadColumnsField = []
				let decListColumnsField = []
				this.decHeadColumns.forEach(function(item, i, array) {
					allSettingColumn.push(item.field)
					decHeadColumnsField.push(item.field)
				})
				this.decListColumns.forEach(function(item, i, array) {
					allSettingColumn.push(item.field)
					decListColumnsField.push(item.field)
				})
				allSettingColumn.push('decHeadCheckAll')
				this.settingColumns = allSettingColumn
				this.decHeadColumnsField=decHeadColumnsField
				this.decListColumnsField=decListColumnsField
			} else {
				this.settingColumns = []
				this.decHeadColumnsField=[]
				this.decListColumnsField=[]
			}
			Object.assign(this, {
				settingColumns: e.target.checked ? this.settingColumns : [],
				indeterminate: false,
				checkAll: e.target.checked,
			});
		},
		onCheckDecHeadChange(e){
			if (e.target.checked) {
				let allSettingColumn = []
				this.decHeadColumns.forEach(function(item, i, array) {
					allSettingColumn.push(item.field)
				})
				this.decHeadColumnsField = allSettingColumn
				// this.settingColumns.push('decHeadCheckAll')
				console.log(this.settingColumns)
				this.$forceUpdate()
			} else {
				this.decHeadColumnsField = []
				// this.settingColumns=this.settingColumns.filter(i=>'decHeadCheckAll'!==i)
			}
			console.log('222222')
			Object.assign(this, {
				decHeadColumnsField: e.target.checked ? this.decHeadColumnsField : [],
				indeterminateDecHead: false,
				checkAllDecHead: e.target.checked,
			});
		},
		onCheckDecListChange(e){
			if (e.target.checked) {
				let allSettingColumn = []
				this.decListColumns.forEach(function(item, i, array) {
					allSettingColumn.push(item.field)
				})
				this.decListColumnsField = allSettingColumn
				this.$forceUpdate()
			} else {
				this.decListColumnsField = []
			}
			Object.assign(this, {
				decListColumnsField: e.target.checked ? this.decListColumnsField : [],
				indeterminateDecList: false,
				checkAllDecList: e.target.checked,
			});
		},
		onCheckBoxChange(e){
			if (e.target.checked) {
				let allSettingColumn = []
				this.boxColumns.forEach(function(item, i, array) {
					allSettingColumn.push(item.field)
				})
				this.boxColumnsField = allSettingColumn
				this.$forceUpdate()
			} else {
				this.boxColumnsField = []
			}
			Object.assign(this, {
				boxColumnsField: e.target.checked ? this.boxColumnsField : [],
				indeterminateBox: false,
				checkAllBox: e.target.checked,
			});
		},

		async handleGetExportDecFields(type){
			this.customExportDecType=type
			//为选择导出时，验证是否选择了数据
			if(2===type&&this.selectedRowKeys.length===0){
				this.$message.error('选择导出请选择至少一条数据进行导出。')
				return
			}

			this.exportButtonLoading=true
			this.decHeadColumns=[]
			this.decListColumns=[]
			this.boxColumns=[]
			await getAction('/DecHead/dec-head/getExportDecFields').then((res) => {
				if (res.success ) {
					//表头字段
					const decHeadMap = res.result.decHeadFields
					for (let key in decHeadMap) {
						const obj = {field:key,fieldName:decHeadMap[key]}
						this.decHeadColumns.push(obj)
					}
					//表体字段
					const decListMap = res.result.decListFields
					for (let key in decListMap) {
						const obj = {field:key,fieldName:decListMap[key]}
						this.decListColumns.push(obj)
					}
					//集装箱字段
					const boxMap = res.result.decContainerFields
					for (let key in boxMap) {
						const obj = {field:key,fieldName:boxMap[key]}
						this.boxColumns.push(obj)
					}
					console.log('boxColumnsboxColumnsboxColumns')
					console.log(this.boxColumns)


					//默认全选
					let allSettingColumn = []
					let decHeadColumnsField = []
					let decListColumnsField = []
					let boxColumnsField = []
					this.decHeadColumns.forEach(function(item, i, array) {
						decHeadColumnsField.push(item.field)
						allSettingColumn.push(item.field)
					})
					this.decListColumns.forEach(function(item, i, array) {
						decListColumnsField.push(item.field)
						allSettingColumn.push(item.field)
					})
					this.boxColumns.forEach(function(item, i, array) {
						boxColumnsField.push(item.field)
						allSettingColumn.push(item.field)
					})

					// allSettingColumn.push('decHeadCheckAll')
					this.settingColumns = allSettingColumn
					this.decHeadColumnsField=decHeadColumnsField
					this.decListColumnsField=decListColumnsField
					this.boxColumnsField=boxColumnsField
					this.checkAll=true
					this.checkAllDecHead=true
					this.checkAllDecList=true
					this.checkAllBox=true
					console.log('???????')
					console.log(this.settingColumns)
				}else {
					this.$message.error('获取导出项失败，请联系管理员！')
				}
			})
			this.customExportVisible=true
			this.exportButtonLoading=false
		},
		/**
		 * 复制报关单
		 */
		copyDecHead() {
			if (this.selectionRows.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return
			}
			let record = this.selectionRows[0]
			let imSignId = ''
			if (record.ieFlag === 'I') {
				imSignId = '1'
				this.$router.push({
					path: '/Business/customs-declaration/details_I',
					query: {
						id: record.id,
						imSignId: imSignId,
						isCopy: true
					}
				})
			} else if (record.ieFlag === 'E') {
				imSignId = '2'
				this.$router.push({
					path: '/Business/customs-declaration/details_E',
					query: {
						id: record.id,
						imSignId: imSignId,
						isCopy: true
					}
				})
			}

		},
		cellDblclick({ row }){
			if(row.ieFlag=='I'){
				this.$router.push({
					path: '/Business/customs-declaration/details_I',
					query: {
						id: row.id,
						imSignId: row.ieFlag == 'I' ? '1' : '2'
					}
				})
			}else {
				this.$router.push({
					path: '/Business/customs-declaration/details_E',
					query: {
						id: row.id,
						imSignId: row.ieFlag == 'I' ? '1' : '2'
					}
				})

			}

		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					dblclick: () => {
						console.log('双击了我')
						if(record.ieFlag == 'I' ){
							this.$router.push({
								path: '/Business/customs-declaration/details_I',
								query: {
									id: record.id,
									imSignId: record.ieFlag == 'I' ? '1' : '2'
								}
							})
						}else {
							this.$router.push({
								path: '/Business/customs-declaration/details_E',
								query: {
									id: record.id,
									imSignId: record.ieFlag == 'I' ? '1' : '2'
								}
							})
						}

					},
					click: () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
					}
				}
			}
		},
		handleFirstTrial(key) {
			console.log(key)
			if (this.selectionRows.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (key.key === '1') {
				this.model = {firstOpinion: '', reviewOpinion: ''}
				this.title = '初审意见'
				this.isFirst = true
				this.visible = true
			} else if (key.key === '1-1') {
				let arr = []
				let idArr = []
				this.selectionRows.forEach(item => {
					if (item.initialReviewStatus!='1') {
						arr.push(item)
					}
					idArr.push(item.id)
				})
				if (arr.length > 0) {
					this.$message.warning('只有已初审/未复审状态的才能取消初审，请重新选择!')
					return
				}
				let that = this
				that.$confirm({
					title: '操作确认',
					content: '确定要取消初审吗？',
					onOk: function () {
						that.auditLoading = true
						_postAction('/DecHead/dec-head/handleInitialReview', {
							ids: idArr.join(','),
							initialReviewStatus: '1-1',
							opinion: '取消初审'
						}).then(res => {
							if (res.success) {
								that.$message.success(res.message)
								that.searchQuery()
							} else {
								that.$message.warning(res.message)
							}
							that.auditLoading = false
						})
					},
					onCancel: function () {
					}
				})
			}
		},
		handleReview(key) {
			if (this.selectionRows.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (key.key === '2') {
				this.model = {firstOpinion: '', reviewOpinion: ''}
				this.title = '复审意见'
				this.isFirst = false
				this.visible = true
 			} else if (key.key === '2-1') {
				let arr = []
				let idArr = []
				this.selectionRows.forEach(item => {
					if (item.initialReviewStatus!='2') {
						arr.push(item)
					}
					idArr.push(item.id)
				})
				if (arr.length > 0) {
					this.$message.warning('只有已复审状态的才能取消复审，请重新选择!')
					return
				}
				let that = this
				that.$confirm({
					title: '操作确认',
					content: '确定要取消复审吗？',
					onOk: function () {
						that.auditLoading = true
						_postAction('/DecHead/dec-head/handleInitialReview', {
							ids: idArr.join(','),
							initialReviewStatus: '2-1',
							opinion: '取消复审'
						}).then(res => {
							if (res.success) {
								that.$message.success(res.message)
								that.searchQuery()
							} else {
								that.$message.warning(res.message)
							}
							that.auditLoading = false
						})
					},
					onCancel: function () {
					}
				})
			}
		},
		handleOk() {
			let idArr = []
			let arr = []
			let arr1 = []
			this.selectionRows.forEach(item => {
				if (item.initialReviewStatus!='0' && item.initialReviewStatus!='' && item.initialReviewStatus!=null) {
					arr.push(item)
				}
				if (item.decStatus && item.decStatus!='1') {
					arr1.push(item)
				}
				idArr.push(item.id)
			})
			if (this.isFirst) {
				if (arr.length > 0) {
					this.$message.error('只有未审核状态的才能进行初审，请重新选择!')
					return
				}
			}
			if (arr1.length > 0) {
				this.$message.error('已申报的报关单，不允许再进行初复审，请重新选择!')
				return
			}
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					this.confirmLoading = true
					_postAction('/DecHead/dec-head/handleInitialReview', {
						ids: idArr.join(','),
						initialReviewStatus: this.isFirst ? '1' : '2',
						opinion: this.isFirst ? this.model.firstOpinion : this.model.reviewOpinion
					}).then(res => {
						if (res.success) {
							this.$message.success(res.message)
							this.searchQuery()
						} else {
							this.$message.warning(res.message)
						}
						this.confirmLoading = false
						this.visible = false
					})
				}
			})
		},
		subStrForAddr,
		subStrForColumns,
		searchReset() {
			this.queryParam = {}
			this.loadData(1)
			this.selectAppDate=''
			this.selectCreateTime=''
			this.selectUpdateTime=''
			// this.onClearSelected()
		},
		handlePushDec(id){
			if (this.selectionRows.length ==0 || this.selectionRows.length >1){
				this.$message.warning("请选择一条记录推送")
				return
			}
			let that = this
			that.$confirm({
				title: '操作确认',
				content: '确定推送该报关单？',
				onOk: function () {
					id = that.selectedRowKeys[0]
					let params = {
						pushStatus:'1',
						id:id
					}
					putAction(that.url.updatePushStatusById,params)
						.then((res)=>{
							if (res.success){
								that.$message.success("推送成功")
							}else {
								that.$message.warning(res.message)
							}
						})
				},
				onCancel: function () {
				}
			})
		},
		handleContrastDec(record){
			this.$message.success("对比成功，数据无异常")
		},
		async handleprintDec(id){
			const res = await getDecById(id).catch(reason => this.$message.error('加载信息失败'))
			if (res.success) {
				if (!res.result ||  res.result.decLists.length==0){
					this.$message.error('报关单表体信息不存在，无法打印!')
					return
				}
			}
			let params = {
				id: id
			}
			downloadFile(this.url.exportDecPDF,id+'.pdf',params).then((res) => {
			})
			// getAction(this.url.exportDecPDF,{id:id}).then((res)=>{
			//
			// })
		},
		batchDel(){
			if(this.selectedRowKeys.length>0){
				for(let t of this.selectionRows){
					if(t.decStatus==1||!t.decStatus){

					}else {
						this.$message.error('包含无法删除的报关单！')
						return
					}
				}
				let that = this
				that.$confirm({
					title: '操作确认',
					content: '确定删除选中报关单吗？',
					onOk: async function () {
							 await deleteAction(that.url.deleteBatch, { ids: that.selectedRowKeys.join(',') }).then((res) => {
								 if (res.success) {
									 that.$message.success('操作成功')
									 that.loadData()
								 } else {
									 that.$message.warning(res.message)
								 }
							 })
					},
					onCancel: function () {
					}
				})
			}
		},

		handleDelete: function (id, updateTime,row) {
			if (!this.url.delete) {
				this.$message.error('请设置url.delete属性!')
				return
			}
			/*
			申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，11查验通知，S公自用物品核准通过，
			T公自用物品退单，U公自用物品待核准）1或空可以删  其他都不能删.
			 */
			if(row.decStatus==1||!row.decStatus){

			}else {
				this.$message.error('该报关单无法进行删除')
				return
			}


			var that = this
			deleteAction(that.url.delete, { id: id }).then((res) => {
				if (res.success) {
					//重新计算分页问题
					// this.reCalculatePage(1)
					that.$message.success(res.message)
					that.loadData()
				} else {
					that.$message.warning(res.message)
				}
			})
		},
		handleOrderDetail(e){
			this.$refs.orderInfoByDec.chooseOrderInfo(e)
		},
		handleDetail: function (e) {
			this.$router.push({
				path: '/Business/customs-declaration/details_I',
				query: {
					imSignId: e,
					decNew:true,
					dclTrnRelFlag:'0'
				}
			})
		},
		handleEdit(record) {
			console.log(record)
			if(record.ieFlag == 'I'){
				this.$router.push({
					path: '/Business/customs-declaration/details_I',
					query: {
						id: record.id,
						imSignId: record.ieFlag == 'I' ? '1' : '2'
					}
				})
			}else {
				this.$router.push({
					path: '/Business/customs-declaration/details_E',
					query: {
						id: record.id,
						imSignId: record.ieFlag == 'I' ? '1' : '2'
					}
				})

			}
		},
		/**
		 * 查看运抵回执
		 * @param record
		 */
		handleYd(record) {
			console.log('=====即将显示查看运抵回执=====')
			this.$refs.containerCargoNodeModal.open(record)
		},
		/**
		 * 预计到港时间
		 * @param record
		 */
		handleEta(record) {
			console.log('=====即将显示船舶计划详情=====')
			this.$refs.shipPlanModal.open(record)
		},
		handleEditHis(record){
			this.$refs.ediStatusList.visible= true;
			this.$nextTick(()=>{
				this.$refs.ediStatusList.getEdis(record.customsCode,record.seqNo);
			})
		},
		// handleEditXQ(record){
		//   this.$router.push({
		//     path: '/Business/customs-declaration/details',
		//     query: {
		//       id: record.id,
		//       imSignId: record.ieFlag == 'I' ? '1' : '2',
		//       xiangqing:true
		//     }
		//   })
		// },
		formatterDecStatus({ cellValue, row, column }) {
			let list = [
				{ title: '保存', value: '1' },
				{ title: '结关', value: '10' },
				{ title: '查验通知', value: '11' },
				{ title: '已申报', value: '2' },
				{ title: '海关入库成功', value: '4' },
				{ title: '退单', value: '6' },
				{ title: '审结', value: '7' },
				{ title: '删单', value: '8' },
				{ title: '放行', value: '9' },
				{ title: '公自用物品核准通过', value: 'S' },
				{ title: '公自用物品退单', value: 'T' },
				{ title: '公自用物品待核准', value: 'U' },
			]
			let item = list.find(item => item.value === cellValue)
			return item ? item.title : ""
		},
		handleCancel() {
			this.visible = false
		},
		//排序
		sortChange({ column, property, order, sortBy, sortList, $event }) {
			if (order != null) {
				this.queryParam.fields = property
				this.queryParam.sortType = order
			} else {
				this.queryParam.fields = ''
				this.queryParam.sortType = ''
			}
			console.log(order)
			console.log(this.queryParam.fields)
			console.log(this.queryParam.sortType)
			this.sortChangeEndfresh(this.queryParam.fields,this.queryParam.sortType)
		},
		sortChangeEndfresh(fields,sortType){
			var sortQuery={
				fields:fields,
				sortType:sortType,
			}
			this.loadData(1,sortQuery)
		},
		/**
		 * 获取状态记录（实时获取最新数据，不使用缓存）
		 * 因为后台持续处理数据，状态会实时变化，需要每次都获取最新状态
		 */
		async getStatusRecords(row) {
			const { id } = row;

			if (!id) {
				console.warn('获取状态记录失败：缺少必要的ID参数');
				this.statusLoading = false;
				this.$message.warning('无法获取状态记录：缺少必要参数');
				return;
			}

			// 每次都获取最新数据，不使用缓存
			this.statusRecords = [];
			this.statusLoading = true;

			try {
				const res = await getAction('/common/getStatusRecords', {
					logType: '521',
					sourceId: id
				});

				if (res && res.success) {
					if (res.result && Array.isArray(res.result) && res.result.length > 0) {
						const statusRecords = [];
						let hasStatusTwo = false;
						let hasStatusThree = false;
						let hasStatusfy = false;

						for (let i = 0; i < res.result.length; i++) {
							const item = res.result[i];
							const recordItem = {
								...item,
								status: item.requestParam || '-',
								time: item.createTime || '-',
								operator: item.username || '-',
								reason: item.logContent === undefined || item.logContent === null ? '-' : item.logContent
							};
							statusRecords.push(recordItem);

							// 检查是否有状态为2（数据清洗回填中）的记录
							if (item.requestType == '2') {
								hasStatusTwo = true;
							}
							// 检查是否有状态为3的记录
							if (item.requestType == '3') {
								hasStatusThree = true;
							}
							// 检查是否有状态为-1的记录
							if (item.requestType == '-1') {
								hasStatusfy = true;
							}
						}

						// 更新行状态
						if (hasStatusTwo && row.status != '2') {
							row.status = '2'; // 直接修改行对象的状态为2
						}
						if (hasStatusThree && row.status != '3') {
							row.status = '3';
						}
						if (hasStatusfy && row.status != '-1') {
							row.status = '-1';
						}

						// 直接设置状态记录，不使用缓存
						this.statusRecords = statusRecords;
						console.log('状态记录获取成功，记录数量：', statusRecords.length);
					} else {
						// 没有数据时确保清空状态记录
						this.statusRecords = [];
						console.log('获取状态记录成功，但没有数据');
					}
				} else {
					// API调用成功但业务失败
					const errorMsg = (res && res.message) ? res.message : '获取状态记录失败';
					console.error('获取状态记录业务失败：', errorMsg);
					this.$message.error(errorMsg);
					this.statusRecords = [];
				}
			} catch (error) {
				// API调用异常
				console.error('获取状态记录API调用异常：', error);
				this.$message.error('获取状态记录失败，请稍后重试');
				this.statusRecords = [];
			} finally {
				this.statusLoading = false;
			}
		},
		showDetailData(record) {
			if (!record || !record.reason) {
				this.currentDetailData = {
					detail_ocr: '无数据',
					detail_format: '无数据',
					result: '无数据'
				};
				this.detailDataVisible = true;
				return;
			}

			try {
				if (typeof record.reason === 'string') {
					const cleanedString = record.reason.replace(/^\s+|\s+$/g, '');
					try {
						this.currentDetailData = JSON.parse(cleanedString);
					} catch (parseError) {
						console.error('JSON解析错误:', parseError);
						this.currentDetailData = {
							detail_ocr: record.reason,
							detail_format: '-',
							result: '-'
						};
					}
				} else if (typeof record.reason === 'object') {
					this.currentDetailData = record.reason;
				} else {
					this.currentDetailData = {
						detail_ocr: String(record.reason),
						detail_format: '-',
						result: '-'
					};
				}
			} catch (e) {
				console.error('处理详情数据出错:', e);
				this.currentDetailData = {
					detail_ocr: record.reason ? String(record.reason) : '无数据',
					detail_format: '-',
					result: '-'
				};
			}
			this.detailDataVisible = true;
		},

		/**
		 * 判断是否为验证失败数据
		 * @param {string} text - AI解析原始数据文本
		 * @returns {boolean} 是否为验证失败数据
		 */
		isValidationFailureData(text) {
			if (!text || text === '-' || text === 'undefined') {
				return false;
			}

			try {
				// 尝试解析JSON数据
				const data = JSON.parse(text);
				
				// 检查是否为数组格式
				if (Array.isArray(data)) {
					// 检查数组中是否包含验证失败的物料信息
					return data.some(item => 
						item && 
						typeof item === 'object' && 
						item.hasOwnProperty('物料号') && 
						item.hasOwnProperty('商品名称') && 
						item.hasOwnProperty('状态') && 
						item.hasOwnProperty('错误') &&
						item.状态 === '失败' &&
						Array.isArray(item.错误)
					);
				}
			} catch (e) {
				// JSON解析失败，不是验证失败数据
				return false;
			}
			
			return false;
		},

		/**
		 * 显示验证失败错误信息弹窗
		 * @param {Object} record - 记录对象
		 */
		showValidationErrors(record) {
			if (!record || !record.reason) {
				this.$message.warning('暂无验证错误信息');
				return;
			}

			try {
				// 解析验证失败数据
				const data = JSON.parse(record.reason);
				
				if (Array.isArray(data)) {
					// 过滤出验证失败的数据
					this.validationErrors = data.filter(item => 
						item && 
						typeof item === 'object' && 
						item.状态 === '失败' &&
						Array.isArray(item.错误)
					);
					
					if (this.validationErrors.length > 0) {
						this.validationErrorVisible = true;
					} else {
						this.$message.warning('未找到验证失败的错误信息');
					}
				} else {
					this.$message.warning('数据格式不正确');
				}
			} catch (e) {
				console.error('解析验证错误数据失败:', e);
				this.$message.error('解析验证错误数据失败');
			}
		},

		/**
		 * 生成折叠面板标题
		 * @param {Object} item - 验证项目
		 * @param {number} index - 索引
		 * @returns {string} 折叠面板标题
		 */
		getCollapseHeader(item, index) {
			const materialNumber = item.物料号 || '未知物料号';
			const productName = item.商品名称 || '未知商品';
			const errorCount = item.错误 && Array.isArray(item.错误) ? item.错误.length : 0;
			
			if (errorCount > 0) {
				return `物料 ${index + 1}: ${materialNumber} - ${productName} (${errorCount}个错误)`;
			} else {
				return `物料 ${index + 1}: ${materialNumber} - ${productName}`;
			}
		},

		formatJson(json) {
			if (!json || json === '-' || json === 'undefined') {
				return '无数据';
			}

			try {
				if (Array.isArray(json)) {
					const formattedItems = [];
					for (let i = 0; i < json.length; i++) {
						let item = json[i];
						if (typeof item === 'object' && item !== null) {
							formattedItems.push(JSON.stringify(item, null, 2));
						} else {
							formattedItems.push(String(item));
						}
					}
					return formattedItems.join('\n\n');
				}

				if (typeof json === 'string') {
					try {
						const obj = JSON.parse(json);
						return JSON.stringify(obj, null, 2);
					} catch (e) {
						return json;
					}
				} else if (typeof json === 'object' && json !== null) {
					return JSON.stringify(json, null, 2);
				}

				return String(json);
			} catch (e) {
				console.error('格式化JSON出错:', e);
				return String(json);
			}
		},

		copyToClipboard(text) {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(text)
					.then(() => {
						this.$message.success('复制成功');
					})
					.catch(() => {
						this.fallbackCopyToClipboard(text);
					});
			} else {
				this.fallbackCopyToClipboard(text);
			}
		},

		fallbackCopyToClipboard(text) {
			const textarea = document.createElement('textarea');
			textarea.value = text;
			textarea.style.position = 'fixed';
			document.body.appendChild(textarea);
			textarea.select();

			try {
				document.execCommand('copy');
				this.$message.success('复制成功');
			} catch (err) {
				this.$message.error('复制失败，请手动复制');
			}

			document.body.removeChild(textarea);
		},
		copyCurrentTabData() {
			let data = '';

			// 根据当前激活的标签页获取相应的数据
			if (this.activeTabKey === '1' && this.currentDetailData && this.currentDetailData.detail_ocr) {
				data = typeof this.currentDetailData.detail_ocr === 'string'
					? this.currentDetailData.detail_ocr
					: JSON.stringify(this.currentDetailData.detail_ocr, null, 2);
			} else if (this.activeTabKey === '2' && this.currentDetailData && this.currentDetailData.detail_format) {
				data = typeof this.currentDetailData.detail_format === 'string'
					? this.currentDetailData.detail_format
					: JSON.stringify(this.currentDetailData.detail_format, null, 2);
			} else if (this.activeTabKey === '3' && this.currentDetailData && this.currentDetailData.result) {
				data = typeof this.currentDetailData.result === 'string'
					? this.currentDetailData.result
					: JSON.stringify(this.currentDetailData.result, null, 2);
			}

			if (data) {
				this.copyToClipboard(data);
			} else {
				this.$message.warning('当前标签页无数据可复制');
			}
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-card-body{
	padding-top: 4px;

}
.myCheckBox /deep/ .ant-checkbox + span{
	padding-right: 0px;
	padding-left: 0px
}

.ai-processing-text {
	color: #1890ff;
	position: relative;
	display: inline-flex;
	align-items: center;
	background: rgba(24, 144, 255, 0.1);
	padding: 2px 8px;
	border-radius: 12px;

	.ai-icon {
		margin-right: 4px;
		animation: pulse 1.5s infinite;
	}

	.dot-loader {
		display: inline-flex;
		margin-left: 4px;

		.dot {
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: #1890ff;
			margin: 0 2px;
			display: inline-block;
			animation: dotPulse 1.5s infinite;

			&:nth-child(2) {
				animation-delay: 0.2s;
			}

			&:nth-child(3) {
				animation-delay: 0.4s;
			}
		}
	}
}

@keyframes pulse {
	0% {
		opacity: 0.6;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.2);
	}

	100% {
		opacity: 0.6;
		transform: scale(1);
	}
}

@keyframes dotPulse {
	0% {
		transform: scale(0.8);
		opacity: 0.5;
	}

	50% {
		transform: scale(1.2);
		opacity: 1;
	}

	100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
}

@keyframes aiProcessing {
	0% {
		opacity: 0.9;
	}

	50% {
		opacity: 1;
	}

	100% {
		opacity: 0.9;
	}
}

.json-detail-container {
	max-height: 500px;
	overflow: auto;
	background-color: #f5f5f5;
	padding: 8px;
	border-radius: 4px;
	border: 1px solid #e8e8e8;
}

.json-detail-container pre {
	white-space: pre-wrap;
	word-wrap: break-word;
	margin: 0;
	font-size: 13px;
	line-height: 1.4;
}

/deep/ .ant-tabs-nav {
	margin-bottom: 16px;
}

/deep/ .ant-tabs-tab {
	font-size: 14px;
	padding: 12px 16px;
}

/deep/ .ant-modal-body {
	padding-top: 12px;
}

/deep/ .detail-data-modal .ant-tabs-bar {
	margin-bottom: 8px;
}

/deep/ .detail-data-modal .ant-tabs-nav {
	margin-bottom: 8px;
}

/* 验证失败弹窗样式 */
.validation-error-modal .ant-collapse {
	background-color: #fafafa;
	border: 1px solid #d9d9d9;
	border-radius: 6px;
}

.validation-error-modal .ant-collapse-item {
	border-bottom: 1px solid #f0f0f0;
}

.validation-error-modal .ant-collapse-item:last-child {
	border-bottom: none;
}

.validation-error-modal .ant-collapse-header {
	padding: 12px 16px;
	font-weight: 500;
	color: #262626;
	background-color: #fff;
}

.validation-error-modal .ant-collapse-header:hover {
	background-color: #f5f5f5;
}

.validation-error-modal .ant-collapse-content {
	background-color: #fff;
}

.validation-error-modal .ant-collapse-content-box {
	padding: 16px;
}

.validation-error-modal .collapse-content {
	padding: 0;
}

.validation-error-modal .material-info {
	margin-bottom: 16px;
	padding: 12px;
	background-color: #f9f9f9;
	border-radius: 4px;
	border-left: 4px solid #1890ff;
}

.validation-error-modal .material-info p {
	margin: 4px 0;
	color: #595959;
	font-size: 14px;
}

.validation-error-modal .material-info strong {
	color: #262626;
}

.validation-error-modal .error-list {
	margin-top: 8px;
}

.validation-error-modal .error-list h4 {
	margin: 0 0 12px 0;
	font-size: 14px;
	font-weight: 600;
	color: #ff4d4f;
	display: flex;
	align-items: center;
}

.validation-error-modal .error-items {
	padding-left: 0;
}

.validation-error-modal .error-item {
	margin-bottom: 8px;
	padding: 10px 12px;
	background-color: #fff2f0;
	border: 1px solid #ffccc7;
	border-radius: 4px;
	color: #a8071a;
	font-size: 13px;
	line-height: 1.5;
	display: flex;
	align-items: flex-start;
}

.validation-error-modal .error-item:last-child {
	margin-bottom: 0;
}

.validation-error-modal .no-errors {
	text-align: center;
	color: #52c41a;
	padding: 20px;
	font-size: 14px;
	background-color: #f6ffed;
	border: 1px solid #b7eb8f;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.validation-error-modal .ant-tag {
	margin-right: 8px;
	margin-bottom: 8px;
	font-weight: 500;
}

.validation-error-modal .ant-icon {
	margin-right: 4px;
}

/* 折叠面板额外样式优化 */
.validation-error-modal .ant-collapse-item-active .ant-collapse-header {
	background-color: #e6f7ff;
	border-bottom: 1px solid #91d5ff;
}

.validation-error-modal .ant-collapse .ant-collapse-item-disabled .ant-collapse-header {
	cursor: default;
}

.validation-error-modal .ant-collapse-arrow {
	color: #1890ff;
	font-size: 12px;
}

.validation-error-modal .ant-collapse-extra {
	margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.validation-error-modal {
		width: 95% !important;
		max-width: none !important;
	}
	
	.validation-error-modal .material-info p {
		font-size: 13px;
	}
	
	.validation-error-modal .error-item {
		padding: 8px 10px;
		font-size: 12px;
	}
}
</style>