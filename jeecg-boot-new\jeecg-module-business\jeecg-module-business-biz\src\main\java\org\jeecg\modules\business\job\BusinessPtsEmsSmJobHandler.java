package org.jeecg.modules.business.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.service.IPtsEmsHeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * BusinessPtsEmsSmJobHandler
 * <pre>
 *   业务模块定时任务-手帐册数茂接口
 * </pre>
 *
 * <AUTHOR>  2025/4/24 13:54
 * @version 1.0
 */
@Component
@Slf4j
public class BusinessPtsEmsSmJobHandler {

    @Autowired
    private IPtsEmsHeadService ptsEmsHeadService;
    /**
     * 同步树毛手帐册表头定时任务
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2025/04/24 10:51
     */
    @XxlJob(value = "SyncSmPtsEmsHeadDataJob")
    public ReturnT<String> SyncSmPtsEmsHeadDataJob(String params) {
        log.info("同步树毛手帐册表头定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String swid = jsonObject.getString("swid");
        String systemId = jsonObject.getString("systemId");
        String seqNo = jsonObject.getString("seqNo");
        String tradeCode = jsonObject.getString("tradeCode");
        String manualNo = jsonObject.getString("manualNo");
        log.info("【SyncSmPtsEmsHeadDataJob】获取到的参数：{}", params);
        Result<?> result = ptsEmsHeadService.syncSmPtsEmsHeadData(swid,systemId,seqNo,tradeCode,manualNo);
        log.info("【同步树毛手帐册表头定时任务】执行结果：{}", result.getResult());
        log.info("同步树毛手帐册表头定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }
}
