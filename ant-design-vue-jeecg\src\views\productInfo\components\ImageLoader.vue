<template>
  <div style="display: flex; flex-direction: row; align-items: center;">
    <img v-viewer v-if="resolvedUrl" :src="resolvedUrl" height="17px" alt=""
         style="max-width:80px;font-size: 12px;font-style: italic;" />
  </div>
</template>

<script>
import {getAction, getFileAccessHttpUrl} from "@/api/manage";

export default {
  props: {
    avatar: String, // 传入的文件名
  },
  data() {
    return {
      resolvedUrl: '', // 解析后的 URL
    };
  },
  async mounted() {
    this.resolvedUrl = await this.fetchMinioUrl(this.avatar);
  },
  methods: {
    async fetchMinioUrl(avatar) {
      try {
        const url = getFileAccessHttpUrl(avatar);
        if (url && url.indexOf('trade-service-platform') !== -1) {
          const res = await getAction(window._CONFIG['staticMinioURL'] + url.split('trade-service-platform')[1]);
          return res.result;
        } else if (url && url.indexOf('aliyuncs.com') !== -1) {
          const res = await getAction(window._CONFIG['staticMinioURL'] + url.split('aliyuncs.com')[1]);
          return res.result;
        }
        return url;
      } catch (err) {
        console.error(err);
        return ''; // 返回空字符串作为兜底值
      }
    },
  },
};
</script>