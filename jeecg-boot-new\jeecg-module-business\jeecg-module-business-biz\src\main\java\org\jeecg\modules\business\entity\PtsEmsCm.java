package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 手账册单损耗表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@Accessors(chain = true)
@TableName("pts_ems_cm")
public class PtsEmsCm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 手册流水
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("EMS_ID")
    private Long emsId;

    /**
     * 手册号
     */
    @TableField("EMS_NO")
    private String emsNo;
    /**
     * 统一编号
     */
    @TableField("SEQ_NO")
    private String seqNo;
    /**
     * 序号（报关用）(备案号)
     */
    @JsonProperty("gNo")
    @TableField("G_NO")
    private Integer gNo;

    /**
     * 成品序号
     */
    @TableField("EXG_NO")
    private Integer exgNo;

    /**
     * 料件序号
     */
    @TableField("IMG_NO")
    private Integer imgNo;

    /**
     * 单耗/净耗
     */
    @TableField("DEC_CM")
    private BigDecimal decCm;

    /**
     * 损耗率
     */
    @TableField("DEC_DM")
    private BigDecimal decDm;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 单耗版本号
     */
    @TableField("UCNSVERNO")
    private String ucnsverno;
    /**
     * 成品计量单位
     */
    @TableField(exist = false)
    private String exgUnit;
    /**
     * 实际出口数量
     */
    @TableField(exist = false)
    private String exportQty;
    /**
     * 料件计量单位
     */
    @TableField(exist = false)
    private String imgUnit;
    /**
     * 实际进口数量
     */
    @TableField(exist = false)
    private String importQty;
    /**
     * 无形损耗率
     */
    @TableField(exist = false)
    private String decDmWx;
    /**
     *保税料件比例
     */
    @TableField(exist = false)
    private String bsljbl;
    /**
     *出口耗用
     */
    @TableField(exist = false)
    private String ckhy;
    /**
     *工艺损耗
     */
    @TableField(exist = false)
    private String gysh;
    /**
     *保税料件耗用
     */
    @TableField(exist = false)
    private String bsljhy;
    /**
     * 非保税料件耗用
     */
    @TableField(exist = false)
    private String fbsljhy;

    /**
     * 单耗申报状态
     */
    private String unitConsumptionStatus;

    /**
     * 无形损耗率 %
     */
    private BigDecimal intangibleLossRate;

    /**
     * 保税料件比例%
     */
    private BigDecimal proportionOfBondedMaterials;
    /**
     * 修改标志
     */
    private String modifyFlag;
    /**
     * 企业执行标志
     */
    private String enterpriseExecutionFlag;

    /**
     * 成品料号
     */
    private String exgCopGno;
    /**
     * 成品海关编码
     */
    private String exgHscode;

    /**
     * 成品料名
     */
    private String exgGname;

    /**
     * 料件料号
     */
    private String imgCopGno;

    /**
     * 料件海关编码
     */
    private String imgHscode;

    /**
     * 料件料名
     */
    private String imgGname;


}
