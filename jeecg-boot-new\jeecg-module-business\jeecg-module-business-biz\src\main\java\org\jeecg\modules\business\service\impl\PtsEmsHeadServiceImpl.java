package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.export.styler.ExcelExportStylerBorderImpl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.enums.OptTypeEnum;
import org.jeecg.modules.business.entity.excel.ExportDeclarationRecordExcel;
import org.jeecg.modules.business.entity.excel.ExportDeclarationRecordStockExcel;
import org.jeecg.modules.business.entity.excel.ExportEmsAimg;
import org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.IPtsEmsAexgService;
import org.jeecg.modules.business.service.IPtsEmsAimgService;
import org.jeecg.modules.business.service.IPtsEmsCmService;
import org.jeecg.modules.business.service.IPtsEmsHeadService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.*;
import org.jeecg.modules.business.vo.DictModelVO;
import org.jeecg.modules.business.vo.OrderDaiLiFeiExcelVO;
import org.jeecg.modules.business.vo.PtsEmsAexgWeightStatisticsVO;
import org.jeecg.modules.business.vo.PtsEmsAimgWeightStatisticsVO;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static org.jeecg.common.constant.CommonConstant.HAS_OWN_FTP;
import static org.jeecg.common.constant.CommonConstant.SFTP;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * <p>
 * 手账册表头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Slf4j
@Service
public class PtsEmsHeadServiceImpl extends ServiceImpl<PtsEmsHeadMapper, PtsEmsHead> implements IPtsEmsHeadService {
    private static final String URL_GET_EMS_HEAD = "https://api.jgsoft.com.cn:15555/open-api/sm/GetManualList "; // 账册表头
    private static final String URL_GET_EMS_DATA = "https://api.jgsoft.com.cn:15555/open-api/sm/GetManualData "; // 账册详情
    // 设置个限流的令牌桶
    private static final RateLimiter rateLimiter = RateLimiter.create(1.0); // 每秒1个令牌
    @Autowired
    private IPtsEmsAimgService emsAimgService;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private PtsEmsAexgMapper emsAexgMapper;
    @Autowired
    private PtsEmsCmMapper emsCmMapper;
    @Autowired
    private PtsEmsFlowsMapper emsFlowsMapper;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private ErpUnitsMapper erpUnitsMapper;
    @Autowired
    private ErpCurrenciesMapper erpCurrenciesMapper;
    @Autowired
    private ErpCountriesMapper erpCountriesMapper;
    @Autowired
    private ErpCustomsPortsMapper erpCustomsPortsMapper;
    @Autowired
    private ErpDistrictsMapper erpDistrictsMapper;
    @Autowired
    private RateInfoMapper rateInfoMapper;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private PtsEmsAimgMapper ptsEmsAimgMapper;
    @Autowired
    private IPtsEmsAimgService ptsEmsAimgService;
    @Autowired
    private IPtsEmsAexgService ptsEmsAexgService;
    @Autowired
    private IPtsEmsCmService ptsEmsCmService;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private EdiStatusHistoryMapper ediStatusHistoryMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private FtpProperties ftpProperties;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendInvtPath}") // /ImpPath/Sas/OutBox
//    private String remoteSendInvtPath;
//    @Value(value = "${ftp.remoteSendNptsPath}") // /ImpPath/Npts/OutBox
//    private String remoteSendNptsPath;

    /**
     * 查询指定分页列表的PtsEmsHead对象
     *
     * @param page       分页对象
     * @param ptsEmsHead PtsEmsHead对象
     * @return 指定分页列表的PtsEmsHead对象
     */
    @Override
    public IPage<PtsEmsHead> queryPageList(Page<PtsEmsHead> page, PtsEmsHead ptsEmsHead) {
        ptsEmsHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<PtsEmsHead> pageList = baseMapper.queryPageList(page, ptsEmsHead);
        if (isNotEmpty(pageList.getRecords())) {
            //赋值最新的edi信息
            //全部id list<String>
            List<Long> emsIdList = pageList.getRecords().stream().map(PtsEmsHead::getId).collect(Collectors.toList());
            //全部的seqNo list<String>
            List<String> seqNoList = pageList.getRecords().stream().map(PtsEmsHead::getSeqNo).collect(Collectors.toList());
            List<EdiStatusHistory> ediStatusHistoryList = ediStatusHistoryMapper.selectList(new QueryWrapper<EdiStatusHistory>().lambda()
                    .in(EdiStatusHistory::getRelatedId, emsIdList).or().in(EdiStatusHistory::getSeqNo, seqNoList));
            if (!ediStatusHistoryList.isEmpty()) {
                pageList.getRecords().forEach(i -> {
                    List<EdiStatusHistory> ediStatusHistory2 = ediStatusHistoryList.stream().filter(j -> j.getRelatedId().equals(i.getId().toString())
                            || j.getSeqNo().equals(i.getSeqNo()))
                            .sorted(Comparator.comparing(EdiStatusHistory::getReceiverTime).reversed()).collect(Collectors.toList());
                    if (!ediStatusHistory2.isEmpty()) {
                        i.setEdiInfo(ediStatusHistory2.get(0).getNote());
                    }
                });
            }
        }

        return pageList;
    }

    /**
     * 保存电子手册信息
     *
     * @param ptsEmsHead 电子手册信息
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveEmsHead(PtsEmsHead ptsEmsHead) {
//        if (isBlank(ptsEmsHead.getEmsNo())) {
//            return Result.error("账册编号不能为空！");
//        }
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isEmpty(ptsEmsHead.getId())) {
//            Long rows = baseMapper.selectCount(new QueryWrapper<PtsEmsHead>().lambda()
//                    .eq(PtsEmsHead::getEmsNo, ptsEmsHead.getEmsNo()));
//            if (rows > 0) {
//                return Result.error("账册编号已存在！");
//            }
            if ("L".equals(ptsEmsHead.getEmsType())) {
                Long count = baseMapper.selectCount(new LambdaQueryWrapper<PtsEmsHead>()
                        .eq(PtsEmsHead::getOwnerCode, ptsEmsHead.getOwnerCode())
                        .eq(PtsEmsHead::getEmsType, "L"));
                if (count > 0) {
                    return Result.error("该仓库代码已被绑定！");
                }
            }
            ptsEmsHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
            ptsEmsHead.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            ptsEmsHead.setCreateDate(new Date());
            baseMapper.insert(ptsEmsHead);
            // 编辑
        } else {
//            Long rows = baseMapper.selectCount(new QueryWrapper<PtsEmsHead>().lambda()
//                    .eq(PtsEmsHead::getEmsNo, ptsEmsHead.getEmsNo())
//                    .ne(PtsEmsHead::getId, ptsEmsHead.getId()));
//            if (rows > 0) {
//                return Result.error("账册编号已存在！");
//            }
            if ("L".equals(ptsEmsHead.getEmsType())) {
                Long count = baseMapper.selectCount(new LambdaQueryWrapper<PtsEmsHead>()
                        .eq(PtsEmsHead::getOwnerCode, ptsEmsHead.getOwnerCode())
                        .eq(PtsEmsHead::getEmsType, "L")
                        .ne(PtsEmsHead::getId, ptsEmsHead.getId()));
                if (count > 0) {
                    return Result.error("该仓库代码已被绑定！");
                }
            }
            ptsEmsHead.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            ptsEmsHead.setUpdateDate(new Date());
            //修改表头，将审核状态改为未审核
            ptsEmsHead.setFirstEStatus("0");
            ptsEmsHead.setReEStatus("0");
            baseMapper.updateById(ptsEmsHead);
            // 2024/11/5 09:53@ZHANGCHAO 追加/变更/完善：如果编号改了，同步更新其下的表体的编号！
            emsAimgMapper.update(null, new LambdaUpdateWrapper<PtsEmsAimg>()
                    .eq(PtsEmsAimg::getEmsId, ptsEmsHead.getId())
                    .set(PtsEmsAimg::getEmsNo, ptsEmsHead.getEmsNo()));
            emsAexgMapper.update(null, new LambdaUpdateWrapper<PtsEmsAexg>()
                    .eq(PtsEmsAexg::getEmsId, ptsEmsHead.getId())
                    .set(PtsEmsAexg::getEmsNo, ptsEmsHead.getEmsNo()));
        }
        PtsEmsHead resultEmsHead =  baseMapper.selectById(ptsEmsHead.getId());
        return Result.ok(resultEmsHead);
    }

    /**
     * 根据id获取EmsHead对象
     *
     * @param id EmsHead的唯一标识
     * @return 返回对应的EmsHead对象，如果不存在则返回null
     */
    @Override
    public Result<?> getEmsHeadById(String id) {
        PtsEmsHead emsHead = baseMapper.selectById(id);
        return Result.ok(emsHead);
    }

    /**
     * 批量删除方法
     *
     * @param ids 要删除的ID集合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        for (String id : ids.split(",")) {
            PtsEmsHead emsHead = baseMapper.selectById(id);
            if (isEmpty(emsHead)) {
                continue;
            }
            Long aimgCount = emsAimgMapper.selectCount(new LambdaQueryWrapper<PtsEmsAimg>().eq(PtsEmsAimg::getEmsId, id));
            if (aimgCount != null && aimgCount > 0) {
                throw new RuntimeException("当前账册下含有料件数据,不允许删除！");
            }
            Long aexgCount = emsAexgMapper.selectCount(new LambdaQueryWrapper<PtsEmsAexg>().eq(PtsEmsAexg::getEmsId, id));
            if (aexgCount != null && aexgCount > 0) {
                throw new RuntimeException("当前账册下含有成品数据,不允许删除！");
            }
            Long cmCount = emsCmMapper.selectCount(new LambdaQueryWrapper<PtsEmsCm>().eq(PtsEmsCm::getEmsId, id));
            if (cmCount != null && cmCount > 0) {
                throw new RuntimeException("当前账册下含有单损耗数据,不允许删除！");
            }
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * @param page
     * @param emsQueryDto
     * @return
     */
    @Override
    public IPage listEmsDetail(Page page, EmsQueryDto emsQueryDto) {
        IPage pageList = null;
        if ("1".equals(emsQueryDto.getType())) {
            IPage<PtsEmsAimg> ptsEmsAimgIPage = emsAimgMapper.listEmsDetail(page, emsQueryDto);
            pageList = ptsEmsAimgIPage;
            if("1".equals(emsQueryDto.getIsFromDec())){
                //币制
                List<ErpCurrencies> erpCurrenciesList = erpCurrenciesMapper.selectList(null);
                //国别地区
                List<ErpCountries> erpCountriesList = erpCountriesMapper.selectList(null);
                for (PtsEmsAimg ptsEmsAimg : ptsEmsAimgIPage.getRecords()) {
                    //币制数字转字母
                    if(isNotBlank(ptsEmsAimg.getCurr())){
                        erpCurrenciesList.stream().filter(i->i.getCode().equals(ptsEmsAimg.getCurr()))
                                .findFirst().ifPresent(i->ptsEmsAimg.setCurr(i.getCurrency()));
                    }
                    //处理国家
                    if(isNotBlank(ptsEmsAimg.getCountryCode())){
                        List<ErpCountries> collect = erpCountriesList.stream().filter(i -> i.getCode().equals(ptsEmsAimg.getCountryCode()))
                                .collect(Collectors.toList());
                        if(!collect.isEmpty()){
                            erpCountriesList.stream().filter(i -> i.getName().equals(collect.get(0).getName())
                                    &&i.getIsenabled()==1)
                                    .findFirst().ifPresent(i->ptsEmsAimg.setCountryCode(i.getCode()));
                        }
                    }
                }
            }
        } else if ("2".equals(emsQueryDto.getType())) {
            IPage<PtsEmsAexg> ptsEmsAexgIPage = emsAexgMapper.listEmsDetail(page, emsQueryDto);
            pageList = ptsEmsAexgIPage;
            if("1".equals(emsQueryDto.getIsFromDec())){
                //币制
                List<ErpCurrencies> erpCurrenciesList = erpCurrenciesMapper.selectList(null);
                //国别地区
                List<ErpCountries> erpCountriesList = erpCountriesMapper.selectList(null);
                for (PtsEmsAexg ptsEmsAexg : ptsEmsAexgIPage.getRecords()) {
                    //币制数字转字母
                    if(isNotBlank(ptsEmsAexg.getCurr())){
                        erpCurrenciesList.stream().filter(i->i.getCode().equals(ptsEmsAexg.getCurr()))
                                .findFirst().ifPresent(i->ptsEmsAexg.setCurr(i.getCurrency()));
                    }
                    //处理国家
                    if(isNotBlank(ptsEmsAexg.getCountryCode())){
                        List<ErpCountries> collect = erpCountriesList.stream().filter(i -> i.getCode().equals(ptsEmsAexg.getCountryCode()))
                                .collect(Collectors.toList());
                        if(!collect.isEmpty()){
                            erpCountriesList.stream().filter(i -> i.getName().equals(collect.get(0).getName())
                                            &&i.getIsenabled()==1)
                                    .findFirst().ifPresent(i->ptsEmsAexg.setCountryCode(i.getCode()));
                        }
                    }
                }
            }
        } else if ("3".equals(emsQueryDto.getType())) {
            IPage<PtsEmsCm> ptsEmsCmIPage = emsCmMapper.listEmsDetail(page, emsQueryDto);
            pageList = ptsEmsCmIPage;
        }
        return pageList;
    }
    /**
     * 查询手帐册表体信息 用于报表中心用
     * @param page
     * @param emsQueryDto
     * @return
     */
    @Override
    public IPage listEmsDetailByReport(Page page, EmsQueryDto emsQueryDto) {
        IPage pageList = null;
        //料件
        if ("1".equals(emsQueryDto.getType())) {
            IPage<PtsEmsAimg> ptsEmsAimgIPage = emsAimgMapper.listEmsAimgByReport(page, emsQueryDto);
            pageList = ptsEmsAimgIPage;
        } else if ("2".equals(emsQueryDto.getType())) {
            //成品
            IPage<PtsEmsAexg> ptsEmsAexgIPage = emsAexgMapper.listEmsAexgByReport(page, emsQueryDto);
            pageList = ptsEmsAexgIPage;
        } else if ("3".equals(emsQueryDto.getType())) {
            //单损耗
            IPage<PtsEmsCm> ptsEmsCmIPage = emsCmMapper.listEmsCmByReport(page, emsQueryDto);
            pageList = ptsEmsCmIPage;

            if(!ptsEmsCmIPage.getRecords().isEmpty()){
                //全部成品的项号
                List<Integer> exgNoList = ptsEmsCmIPage.getRecords().stream().map(PtsEmsCm::getExgNo)
                        .distinct().collect(Collectors.toList());
                //查询出成品的实际出口数量
                List<PtsEmsAexg> ptsEmsAexgList = emsAexgMapper.listEmsAexgByReportC(emsQueryDto.getEmsNo(),emsQueryDto.getTenantId(),
                        exgNoList);
                //全部料件的项号
                List<Integer> imgNoList = ptsEmsCmIPage.getRecords().stream().map(PtsEmsCm::getImgNo)
                        .distinct().collect(Collectors.toList());
                //查询出进口的实际进口数量
                List<PtsEmsAimg> ptsEmsAimgList = emsAimgMapper.listEmsAimgByReportC(emsQueryDto.getEmsNo(),emsQueryDto.getTenantId(),
                        imgNoList);
                for(PtsEmsCm ptsEmsCm:ptsEmsCmIPage.getRecords()){
                    //赋值实际出口数量
                    if(!ptsEmsAexgList.isEmpty()){
                        List<PtsEmsAexg> ptsEmsAexgList2 = ptsEmsAexgList.stream().
                                filter(i ->
                                        i.getGNo().equals(ptsEmsCm.getExgNo())).
                                collect(Collectors.toList());
                        ptsEmsCm.setExportQty(ptsEmsAexgList2.isEmpty()?"0":ptsEmsAexgList2.get(0).getSjcksl());

                    }
                    //赋值实际进口数量
                    if(!ptsEmsAimgList.isEmpty()){
                        List<PtsEmsAimg> ptsEmsAimgList2 = ptsEmsAimgList.stream().
                                filter(i ->
                                        i.getGNo().equals(ptsEmsCm.getImgNo())).
                                collect(Collectors.toList());
                        ptsEmsCm.setImportQty(ptsEmsAimgList2.isEmpty()?"0":ptsEmsAimgList2.get(0).getSjjksl());

                    }
                    //出口耗用 = 实际出口数量*净耗 / (1-损耗率/100)
                    ptsEmsCm.setCkhy(new BigDecimal(ptsEmsCm.getExportQty())
                            .multiply(ptsEmsCm.getDecCm())
                            .divide(new BigDecimal(1).subtract(ptsEmsCm.getDecDm().divide(new BigDecimal(100))),4,BigDecimal.ROUND_HALF_UP)
                            .stripTrailingZeros().toPlainString());
                    //工艺损耗 = 出口耗用*有形损耗率/100
                    ptsEmsCm.setGysh(new BigDecimal(ptsEmsCm.getCkhy())
                            .multiply(ptsEmsCm.getDecDm().divide(new BigDecimal(100)))
                            .setScale(4,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                    //保税料件耗用 = 出口耗用*保税料件比例/100
                    ptsEmsCm.setBsljhy(new BigDecimal(ptsEmsCm.getCkhy())
                           .multiply(
                                   null==ptsEmsCm.getProportionOfBondedMaterials()?BigDecimal.ZERO:
                                           ptsEmsCm.getProportionOfBondedMaterials()
                                                   .divide(new BigDecimal(100)))
                           .setScale(4,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                    //非保税料件耗用 = 出口耗用*非保税料件比例/100
                    ptsEmsCm.setFbsljhy(new BigDecimal(ptsEmsCm.getCkhy())
                           .multiply(new BigDecimal(1).subtract(
                                   null==ptsEmsCm.getProportionOfBondedMaterials()?BigDecimal.ZERO:
                                           ptsEmsCm.getProportionOfBondedMaterials().divide(new BigDecimal(100))))
                          .setScale(4,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());


                }

            }
        }
        return pageList;
    }

    @Override
    public IPage listEmsDetailWriteOffBalance(Page page, EmsQueryDto emsQueryDto) {
        //1 查询出料件信息
        IPage<PtsEmsAimg> ptsEmsAimgIPage = emsAimgMapper.listEmsAimgByReport(page, emsQueryDto);
        //深拷贝一个 ptsEmsAimgIPage 完全拷贝
        IPage<PtsEmsAimg> ptsEmsAimgIPageCopy =
                new Page<>(ptsEmsAimgIPage.getCurrent(), ptsEmsAimgIPage.getSize(), ptsEmsAimgIPage.getTotal());
        ptsEmsAimgIPageCopy.setRecords(ptsEmsAimgIPage.getRecords());
        //查询出出口耗用数据
        emsQueryDto.setType("3");
        IPage<PtsEmsCm> listEmsDetailByReportPage = listEmsDetailByReport(page, emsQueryDto);
        List<PtsEmsCm> listEmsDetailByReportPageRecords = listEmsDetailByReportPage.getRecords();
        if(!ptsEmsAimgIPageCopy.getRecords().isEmpty()){
            for(PtsEmsAimg ptsEmsAimg:ptsEmsAimgIPageCopy.getRecords()){
                if(!listEmsDetailByReportPageRecords.isEmpty()){
                    //出口成品耗料合计
                    List<PtsEmsCm> ptsEmsCms = listEmsDetailByReportPageRecords.stream().filter(i ->
                            i.getImgNo().equals(ptsEmsAimg.getGNo())).collect(Collectors.toList());
                    //出口成品耗料合计 = 汇总ptsEmsCms的出口耗用
                    ptsEmsAimg.setCkcphlhj(
                            ptsEmsCms.stream().map(PtsEmsCm::getCkhy).filter(Objects::nonNull).map(BigDecimal::new).
                                    reduce(BigDecimal.ZERO, BigDecimal::add));
                    //保税料件耗用合计 = 汇总ptsEmsCms的保税料件耗用
                    ptsEmsAimg.setBsljhyhj(
                            ptsEmsCms.stream().map(PtsEmsCm::getBsljhy).filter(Objects::nonNull).map(BigDecimal::new).
                                    reduce(BigDecimal.ZERO, BigDecimal::add));
                    //非保税料件耗用合计 = 汇总ptsEmsCms的非保税料件耗用
                    ptsEmsAimg.setFbsljhyhj(
                            ptsEmsCms.stream().map(PtsEmsCm::getFbsljhy).filter(Objects::nonNull).map(BigDecimal::new).
                                    reduce(BigDecimal.ZERO, BigDecimal::add));
                    //工艺耗损合计 = 汇总ptsEmsCms的工艺耗损
                    ptsEmsAimg.setGyhshj(
                            ptsEmsCms.stream().map(PtsEmsCm::getGysh).filter(Objects::nonNull).map(BigDecimal::new).
                                    reduce(BigDecimal.ZERO, BigDecimal::add));
                    //理论剩余料件 = 实际进口数量 - 出口成品耗料合计
                    ptsEmsAimg.setLlsylj(
                            new BigDecimal(ptsEmsAimg.getSjjksl()).subtract(ptsEmsAimg.getCkcphlhj())
                    );
                    //保税边角料应补数量 = 工艺耗损合计
                    ptsEmsAimg.setBsbjlybsl(
                            ptsEmsAimg.getGyhshj()
                    );
                }

            }

        }
        return ptsEmsAimgIPageCopy;
    }

    @Override
    public Result<?> listEmsDetailWeightStatistics(EmsQueryDto emsQueryDto) {
        //获取进口保税科件重量统计信息
        List<PtsEmsAimgWeightStatisticsVO> ptsEmsAimgWeightStatisticsVOS =
                emsAimgMapper.listEmsDetailWeightStatisticsAimg(emsQueryDto.getEmsNo(), emsQueryDto.getTenantId());
        //获取出口保税成品重量统计信息
        List<PtsEmsAexgWeightStatisticsVO> ptsEmsAexgWeightStatisticsVOS =
                emsAexgMapper.listEmsDetailWeightStatisticsAexg(emsQueryDto.getEmsNo(), emsQueryDto.getTenantId());
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("aimg", ptsEmsAimgWeightStatisticsVOS);
        resultMap.put("aexg", ptsEmsAexgWeightStatisticsVOS);
        return Result.OK(resultMap);
    }

    /**
     * 导出记录
     *
     * @param request  请求对象
     * @param putrecNo 记录编号
     * @param gNos     记录编号列表
     * @param type     类型
     * @return 返回导出记录的视图对象
     */
    @Override
    public void exportRecord(HttpServletRequest request, HttpServletResponse response, String putrecNo, String gNos, String type) {
        List<String> gNos_ = Arrays.asList(gNos.split(","));
        //h核注单申报记录
        List<ExportDeclarationRecordExcel> exportDeclarationRecordExcels = baseMapper.listDeclarationRecord(putrecNo, gNos_, type);
        //出入库单申报记录
//        List<ExportDeclarationRecordStockExcel> exportDeclarationRecordStockExcels = baseMapper.listDeclarationStockRecord(putrecNo, gNos_);
        List<ExportDeclarationRecordStockExcel> exportDeclarationRecordStockExcels = new ArrayList<>(16);
        if (isNotEmpty(exportDeclarationRecordStockExcels)) {
            for (ExportDeclarationRecordStockExcel exportDeclarationRecordStockExcel : exportDeclarationRecordStockExcels) {
                if (isNotBlank(exportDeclarationRecordStockExcel.getDeclarationDate())) {
                    exportDeclarationRecordStockExcel.setDeclarationDate(
                            exportDeclarationRecordStockExcel.getDeclarationDate().substring(0, 10));
                }
                if (isNotBlank(exportDeclarationRecordStockExcel.getInvtDclTime())) {
                    exportDeclarationRecordStockExcel.setInvtDclTime(
                            exportDeclarationRecordStockExcel.getInvtDclTime().substring(0, 10)
                    );
                }
                if (isNotBlank(exportDeclarationRecordStockExcel.getDclDate())) {
                    exportDeclarationRecordStockExcel.setDclDate(exportDeclarationRecordStockExcel.getDclDate().substring(0, 10));
                }
            }
        }
        ExportParams params = new ExportParams();
        params.setSheetName("核注单申报记录");
        params.setType(ExcelType.XSSF);
        ExportParams params2 = new ExportParams();
        params2.setSheetName("出入库单申报记录");
        params2.setType(ExcelType.XSSF);
        //20220927追加两个sheet导出
        List<Map<String, Object>> list =new ArrayList<>();
        Map<String,Object> mapInv=new HashMap<>();
        mapInv.put("title",params);
        mapInv.put("entity",ExportDeclarationRecordExcel.class);
        mapInv.put("data",exportDeclarationRecordExcels);
        list.add(mapInv);
        Map<String,Object> mapIn2=new HashMap<>();
        mapIn2.put("title",params2);
        mapIn2.put("entity",ExportDeclarationRecordStockExcel.class);
        mapIn2.put("data",exportDeclarationRecordStockExcels);
        list.add(mapIn2);
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 批量删除指定类型的细节信息
     *
     * @param ids  分隔符为","的细节信息ID集合
     * @param type 细节信息类型
     * @return 返回删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteDetailBatch(String ids, String type) {
        if ("1".equals(type)) {
            emsAimgMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        } else if ("2".equals(type)) {
            emsAexgMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        } else if ("3".equals(type)) {
            emsCmMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        }
        return Result.ok("删除成功！");
    }

    /**
     * 通过字段批量导出料件
     *
     * @param emsQueryDto 查询条件DTO对象
     * @param request     HTTP请求对象
     * @param response    HTTP响应对象
     */
    @Override
    public void exportAimgByFieldsBatch(EmsQueryDto emsQueryDto, HttpServletRequest request, HttpServletResponse response) {
        TimeInterval timer = DateUtil.timer();
        List<PtsEmsAimg> emsAimgs = emsAimgService.searchAimg(emsQueryDto);
        // 2022/5/26 16:50@ZHANGCHAO 追加/变更/完善：
        // 1.如果物流账册的备案号对应的核注单的报关标志为“非报关”，则物流账册导出Excel“报关单号”这一列需要体现“非报关”三个字，见下面老关务的导出表。
        // 2.目前账册列表显示的预核扣数量是通过计算算出来的。导出加上预核扣数量（计算逻辑跟列表一样，注意占用的数量要减）。
        List<String> bondInvtNos = emsAimgMapper.isDclCusFlag(emsQueryDto.getEmsNo());
        if (isNotEmpty(bondInvtNos)) {
            bondInvtNos = CollUtil.distinct(CollUtil.removeBlank(bondInvtNos));
        }
        log.info("[bondInvtNos]: " + bondInvtNos);
        List<HashMap<String, String>> gNoQtyMapList = baseMapper.gNoQtyStatistics();
        Map<String, String> gNoAndNumMap = new HashMap<>(16);
        if (isNotEmpty(gNoQtyMapList)) {
            for (HashMap<String, String> hashMap : gNoQtyMapList) {
                gNoAndNumMap.put(String.valueOf(hashMap.get("gNo")), String.valueOf(hashMap.get("num")));
            }
        }
        log.info("[gNoAndNumMap]: " + gNoAndNumMap);
        List<ExportEmsAimg> emsAimgList = new ArrayList<>();
        List<Integer> number = new ArrayList<>();
        number.add(1);
        List<String> finalBondInvtNos = bondInvtNos;
        emsAimgs.forEach(v -> {
            ExportEmsAimg exportEmsAimg = new ExportEmsAimg();
            BeanUtils.copyProperties(v,exportEmsAimg);
            int num = number.get(0);
            exportEmsAimg.setSerialNumber(num);
            number.set(0,++num);

            BigDecimal importQty = v.getImportedQty() != null ? v.getImportedQty() : BigDecimal.ZERO;
            BigDecimal stockQty = v.getStockQty() != null ? v.getStockQty() : BigDecimal.ZERO;
            exportEmsAimg.setExportQty(importQty.subtract(stockQty));
            // 2022/5/26 16:50@ZHANGCHAO 追加/变更/完善：
            // 1.如果物流账册的备案号对应的核注单的报关标志为“非报关”，则物流账册导出Excel“报关单号”这一列需要体现“非报关”三个字，见下面老关务的导出表。
            // 2.目前账册列表显示的预核扣数量是通过计算算出来的。导出加上预核扣数量（计算逻辑跟列表一样，注意占用的数量要减）。
//            NemsInvtHead nemsInvtHead = aimgMapper.isDclCusFlag(exportEmsAimg.getBondInvtNo());
            if (isNotEmpty(finalBondInvtNos) && finalBondInvtNos.contains(exportEmsAimg.getBondInvtNo())) {
                exportEmsAimg.setClearanceNo("非报关");
            }
            if(gNoQtyMapList.size()>0&&"L4301D18A001".equals(exportEmsAimg.getEmsNo())) {

                for(HashMap<String,String> hashMap:gNoQtyMapList){
                    if(String.valueOf(hashMap.get("gNo")).equals(String.valueOf(exportEmsAimg.getGNo()))){
                        BigDecimal bd=new BigDecimal(String.valueOf(hashMap.get("num")));
                        exportEmsAimg.setPredistribution(bd);
                        exportEmsAimg.setOccupyQty(exportEmsAimg.getOccupyQty().subtract(exportEmsAimg.getPredistribution()));
                    }
                }

            }
//            if (isNotEmpty(gNoAndNumMap) && isNotEmpty(exportEmsAimg.getGNo()) && isNotBlank(gNoAndNumMap.get(String.valueOf(exportEmsAimg.getGNo())))) {
//                if (gNoAndNumMap.containsKey(String.valueOf(exportEmsAimg.getGNo()))) {
//                    exportEmsAimg.setPredistribution(new BigDecimal(gNoAndNumMap.get(String.valueOf(exportEmsAimg.getGNo()))));
//                    exportEmsAimg.setOccupyQty(exportEmsAimg.getOccupyQty().subtract(exportEmsAimg.getPredistribution()));
//                }
//            }

            emsAimgList.add(exportEmsAimg);
        });

        // 2022/3/15 15:43@ZHANGCHAO 追加/变更/完善：优化为一条SQL查出数据！
//        List<ExportEmsAimg> emsAimgList = emsAimgService.exportAimgByFieldsBatch(emsQueryDto);

        log.info("组装数据用时：" + timer.intervalRestart() + " == " + DateUtil.now());

        org.jeecgframework.poi.excel.entity.ExportParams exportParams = new org.jeecgframework.poi.excel.entity.ExportParams();
        exportParams.setSheetName("导出物流账册");
        exportParams.setType(org.jeecgframework.poi.excel.entity.enmus.ExcelType.XSSF);
        exportParams.setMaxNum(1000000);
//        Workbook workbook = MyExcelExportUtil.exportExcel(exportParams, ExportEmsAimg.class, emsAimgList, null);
        int totalPage = getTotalPage(emsAimgList.size(), 10000);
//        int totalPage = 1000; // 写死
        Workbook workbook = org.jeecgframework.poi.excel.ExcelExportUtil.exportBigExcel(exportParams, ExportEmsAimg.class, (o, page) -> {
            //page每次加一，当等于obj的值时返回空，代码结束；
            //特别注意，最好每次10000条，否则，可能有内存溢出风险
//                if (((int) o) == page) {
//                    return null;
//                }
            //不是空时：一直循环运行selectListForExcelExport。每次返回1万条数据。
            List<Object> list = new ArrayList<>();
            list.addAll(startPage(emsAimgList, page, 10000));
//            List<ExportEmsAimg> emsAimgList = getExportEmsAimgs(page, 10000, emsQueryDto, bzdm, gbdq, cjdw);
//            if (isEmpty(emsAimgList)) {
//                return null;
//            } else {
//                list.addAll(emsAimgList);
//            }
            return list;
        }, totalPage);

        log.info("生成Excel用时：" + timer.intervalRestart() + " == " + DateUtil.now());
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 检查库存
     *
     * @param emsNo 物流单号
     * @param gNos  商品编号集合
     * @return 结果对象
     */
    @Override
    public Result<?> checkInventory(String emsNo, String gNos) {
        List<String> gNos_=null;
        if(isNotBlank(gNos)){
            gNos_ = Arrays.asList(gNos.split(","));
        }
        //---处理核注单的汇总
        List<HashMap<String,String>> checkInventoryByNemList=baseMapper.checkInventoryByNem(emsNo,gNos_);
        Map<String,String> checkInventoryMapByNem=new HashMap<>();
        for(HashMap<String,String> map:checkInventoryByNemList) {
            checkInventoryMapByNem.put(map.get("gNo"),map.get("num"));
        }
        //--处理料件的汇总
        List<HashMap<String,String>> checkInventoryByEmsList=baseMapper.checkInventoryByEms(emsNo,gNos_);
        Map<String,String> checkInventoryMapByEms=new HashMap<>();
        for(HashMap<String,String> map:checkInventoryByEmsList){
            checkInventoryMapByEms.put(map.get("gNo"),map.get("num"));
        }
        //存在不同的序号集合
        int sameCount=0;
        int difCount=0;
        List<String> difList=new ArrayList<>();
        for(Map.Entry<String, String> entry1:checkInventoryMapByEms.entrySet()){
            String m1value = entry1.getValue() == null?"":String.valueOf(entry1.getValue());
            String m2value = checkInventoryMapByNem.get(entry1.getKey())==null?"":String.valueOf(checkInventoryMapByNem.get(entry1.getKey()));
            if (!m1value.equals(m2value)) {//若两个map中相同key对应的value不相等
                difList.add(entry1.getKey());
                difCount++;
            }else {
                sameCount++;
            }
        }
        String info=null;
        if(difList.size()>0){
            info="检测成功，库存一致条数:"+sameCount+",不一致条数:"+difCount+",不一致的备案号为:"+ Joiner.on(",").join(difList);
        }else {
            info="检测成功，库存一致条数:"+sameCount+",不一致条数:"+difCount;
        }
        return Result.ok(info);
    }

    /**
     * 获取EMS统计信息
     *
     * @param emsNo EMS编号
     * @return 统计结果
     */
    @Override
    public Result<?> emsStatistics(String emsNo) {
        List<PtsEmsAimg> emsStatisticsList = emsAimgMapper.emsStatistics(emsNo);
        if (emsStatisticsList == null || emsStatisticsList.size() == 0) {
            return Result.error("未查询到该账册的汇总数据");
        }
        Map<String, List<PtsEmsAimg>> emsStatisticsMap = new HashMap<>();
        List<PtsEmsAimg> emsAimgListTotal = new ArrayList<>();//存放汇总的数据，金额，净重，为单条数据
        BigDecimal totalNetWeight = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);

        if (isNotEmpty(emsStatisticsList)) {
            for (PtsEmsAimg emsAimg : emsStatisticsList) {
                //根据汇率统一转为美元
                totalAmount = totalAmount.add(isNotEmpty(emsAimg.getDecPrice()) ? emsAimg.getDecPrice() : BigDecimal.ZERO);
                //汇总总净重
                totalNetWeight = totalNetWeight.add(isNotEmpty(emsAimg.getNetWeight()) ? emsAimg.getNetWeight() : BigDecimal.ZERO);
            }
        }
        PtsEmsAimg emsAimg = new PtsEmsAimg();
        emsAimg.setDecPrice(totalAmount);
        emsAimg.setNetWeight(totalNetWeight);
        emsAimgListTotal.add(emsAimg);
        emsStatisticsMap.put("statisticsList", emsStatisticsList);
        emsStatisticsMap.put("statisticsTotal", emsAimgListTotal);
        return Result.ok(emsStatisticsMap);
    }

    /**
     * 获取可用的AEXG结果
     *
     * @param emsId 仓库ID
     * @param gNo   商品编号
     * @return Result<?> 获取结果
     */
    @Override
    public Result<?> getUsableAexg(String emsId, String gNo) {
        // 根据手册流水号、成品序号查询单损耗关系
        List<PtsEmsCm> emsCmList = emsCmMapper.selectList(new QueryWrapper<PtsEmsCm>().eq("EMS_ID", emsId).eq("EXG_NO", gNo));
        log.info("usableAexg方法,通过手册流水号:{},成品序号:{},查询单损耗关系数量是:{}", emsId, gNo,
                null != emsCmList ? emsCmList.size() : 0);
        if (null == emsCmList || emsCmList.size() == 0) { // 未查询出单损耗关系
            log.info("usableAexg方法,通过手册流水号:{},成品序号:{},未查询出单损耗关系,理论可生产数量:0", emsId, gNo);
            return Result.ok(0L);
        }
        // 理论可生产数量集合,用于筛选最低理论可生产数量
        List<Long> produceQtyList = new ArrayList<Long>();
        for (PtsEmsCm emsCm : emsCmList) {
            // 根据手册流水号、序号查询料件
            PtsEmsAimg emsAimg = emsAimgMapper
                    .selectOne(new QueryWrapper<PtsEmsAimg>().eq("EMS_ID", emsId).eq("G_NO", emsCm.getImgNo()));
            log.info("usableAexg方法,成品序号:{},通过手册流水号:{},料件序号:{},查询料件内容是否为空:{}", gNo, emsId, emsCm.getImgNo(),
                    null != emsAimg ? "不为空" : "为空");
            if (null == emsAimg) {
                log.info("手册流水号:{},成品序号:{},料件序号:{},查询料件对象为空,理论可生产数量:0", emsId, gNo, emsCm.getImgNo());
                return Result.ok(0L);
            }
            // ZHANGCHAO@2021/5/19 8:48 追加/变更/完善：料件有负库存时，直接返回0！
            if (emsAimg.getStockQty().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.ok(0L);
            }
            //单耗 =净耗/（1-损耗率/100）
            // 理论可生产数量=理论库存数量/单耗
            BigDecimal loss = emsCm.getDecCm().divide(BigDecimal.ONE.subtract(emsCm.getDecDm().divide(new BigDecimal(100))), 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal produceQty = emsAimg.getStockQty().divide(loss, 0, BigDecimal.ROUND_DOWN);
            log.info("usableAexg方法,手册流水号:{},成品序号:{},料件序号:{},理论库存数量:{},单耗/净耗:{},计算结果:{}", emsId, gNo,
                    emsCm.getImgNo(), emsAimg.getStockQty(), emsCm.getDecCm(), produceQty);
            produceQtyList.add(produceQty.longValue());
        }
        Collections.sort(produceQtyList); // 集合排序,升序排列
        log.info("手册流水号:{},成品序号:{},理论可生产数量:{}", emsId, gNo, produceQtyList.size() > 0 ? produceQtyList.get(0) : 0L);
        return Result.ok(produceQtyList.size() > 0 ? produceQtyList.get(0) : 0L);
    }

    /**
     * 检查所有者编码
     *
     * @param ownerCode 所有者编码
     * @param id        ID
     * @return 结果
     */
    @Override
    public Result<?> checkOwnerCode(String ownerCode, String id) {
        if (isNotBlank(id)) {
            Long count = baseMapper.selectCount(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getOwnerCode, ownerCode)
                    .eq(PtsEmsHead::getEmsType, "L")
                    .ne(PtsEmsHead::getId, id));
            if (count > 0) {
                return Result.error("该仓库代码已存在");
            }
        } else {
            Long count = baseMapper.selectCount(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getOwnerCode, ownerCode)
                    .eq(PtsEmsHead::getEmsType, "L"));
            if (count > 0) {
                return Result.error("该仓库代码已存在");
            }
        }
        return Result.ok(true);
    }

    /**
     * 根据门店编码获取配送头部信息
     *
     * @param storeCode 门店编码
     * @return 配送头部信息的结果
     */
    @Override
    public Result<?> getEmsHeadByStoreCode(String storeCode) {
        List<PtsEmsHead> emsHeads = baseMapper.selectList(new LambdaQueryWrapper<PtsEmsHead>()
                .eq(PtsEmsHead::getOwnerCode, storeCode).eq(PtsEmsHead::getTenantId, TenantContext.getTenant())
//                .eq(PtsEmsHead::getEmsType, "L")
        );
        if (isNotEmpty(emsHeads)) {
            return Result.ok(emsHeads.get(0));
        }
        return Result.error("未获取到仓库[" + storeCode + "]的备案信息！");
    }

    /**
     * 账册库存查询列表
     *
     * @param page
     * @param inventoryFlowsVO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO>
     * <AUTHOR>
     * @date 2024/2/1 11:53
     */
    @Override
    public IPage<InventoryFlowsVO> listInventoryFlows(IPage<InventoryFlowsVO> page, InventoryFlowsVO inventoryFlowsVO) {
        inventoryFlowsVO.setTenantId(TenantContext.getTenant());
        IPage<InventoryFlowsVO> inventoryFlowsVOIPage = emsAimgMapper.listInventoryFlows(page, inventoryFlowsVO);
        if (isNotEmpty(inventoryFlowsVOIPage.getRecords())) {
            for (InventoryFlowsVO record : inventoryFlowsVOIPage.getRecords()) {
                if (record.getStockQty().equals(record.getBeginQty())) {
                    record.setIsBalanced("1");
                } else {
                    record.setIsBalanced("0");
                }
            }
        }
        return inventoryFlowsVOIPage;
    }

    @Autowired
    private NemsInvtHeadMapper invtHeadMapper;
    @Autowired
    private NemsInvtListMapper invtListMapper;
    /**
     * @return
     */
    @Override
    public Result<?> xxx(String emsNo, String gNo) {
        PtsEmsHead emsHead = baseMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                .eq(PtsEmsHead::getEmsNo, emsNo));
        if (isEmpty(emsHead)) {
            return Result.error("系统中不存在编号[" + emsNo + "]的账册，请检查！");
        }
        List<PtsEmsAimg> emsAimgList = emsAimgMapper.selectList(new LambdaQueryWrapper<PtsEmsAimg>()
                .in(isNotBlank(gNo), PtsEmsAimg::getGNo, isNotBlank(gNo)?Arrays.asList(gNo.split(",")): null)
                .eq(PtsEmsAimg::getEmsId, emsHead.getId()));
        //成品数据
        List<PtsEmsAexg> emsAexgList = emsAexgMapper.selectList(new LambdaQueryWrapper<PtsEmsAexg>()
                .in(isNotBlank(gNo), PtsEmsAexg::getGNo, isNotBlank(gNo)?Arrays.asList(gNo.split(",")): null)
                .eq(PtsEmsAexg::getEmsId, emsHead.getId()));
        if (isNotEmpty(emsAimgList)) {
            Map<String, List<PtsEmsAimg>> aimgMap = emsAimgList.stream().collect(Collectors.groupingBy(i -> i.getGNo() + "|" + i.getCopGno() + "|" + i.getCodet()));
            aimgMap.forEach((k, v) -> {
                List<NemsInvtList> invtList2 = emsAimgMapper.listInvtList2(k, emsNo);
                BigDecimal yjk = BigDecimal.ZERO;
                if (isNotEmpty(invtList2)) {
                    for (NemsInvtList i : invtList2) {
                        yjk = yjk.add(i.getDclQty());
                    }
                }
                List<NemsInvtList> invtList = emsAimgMapper.listInvtList(k,emsNo);
                BigDecimal yck = BigDecimal.ZERO;
                if (isNotEmpty(invtList)) {
                    for (NemsInvtList i : invtList) {
                        yck = yck.add(i.getDclQty());
                    }
                }
                List<NemsInvtList> invtList1 = emsAimgMapper.listInvtList1(k, emsNo);
                BigDecimal yhk = BigDecimal.ZERO;
                if (isNotEmpty(invtList1)) {
                    for (NemsInvtList i : invtList1) {
                        yhk = yhk.add(i.getDclQty());
                    }
                }
                BigDecimal finalYjk = yjk;
                BigDecimal finalYck = yck;
                BigDecimal finalYhk = yhk;
                v.forEach(i -> {
                    emsAimgMapper.update(null, new LambdaUpdateWrapper<PtsEmsAimg>()
                            .set(PtsEmsAimg::getImportedQty, finalYjk)
                            .set(PtsEmsAimg::getExpQty, finalYck)
                            .set(PtsEmsAimg::getStockQty, finalYjk.subtract(finalYck))
                            .set(PtsEmsAimg::getPredistribution, finalYhk)
                            .eq(PtsEmsAimg::getId, i.getId()));
                    log.info("料件：【{}|{}|{}】 更新：已进口数量：{}，已出口数量：{}，预核扣数量：{}，库存数量：{}",
                            i.getGNo(), i.getCopGno(), i.getCodet(), finalYjk, finalYck, finalYhk, finalYjk.subtract(finalYck));
                });
            });
        }
        //处理成品
        if (isNotEmpty(emsAexgList)) {
            Map<String, List<PtsEmsAexg>> aexgMap = emsAexgList.stream().collect(Collectors.groupingBy(i -> i.getGNo() + "|" + i.getCopGno() + "|" + i.getCodet()));
            aexgMap.forEach((k, v) -> {
                List<NemsInvtList> invtList2 = emsAexgMapper.listInvtList2(k, emsNo);
                BigDecimal yck = BigDecimal.ZERO;
                if (isNotEmpty(invtList2)) {
                    for (NemsInvtList i : invtList2) {
                        yck = yck.add(i.getDclQty());
                    }
                }
//                List<NemsInvtList> invtList = emsAimgMapper.listInvtList(k, TenantContext.getTenant());
//                BigDecimal yck = BigDecimal.ZERO;
//                if (isNotEmpty(invtList)) {
//                    for (NemsInvtList i : invtList) {
//                        yck = yck.add(i.getDclQty());
//                    }
//                }
//                List<NemsInvtList> invtList1 = emsAimgMapper.listInvtList1(k, TenantContext.getTenant());
//                BigDecimal yhk = BigDecimal.ZERO;
//                if (isNotEmpty(invtList1)) {
//                    for (NemsInvtList i : invtList1) {
//                        yhk = yhk.add(i.getDclQty());
//                    }
//                }
//                BigDecimal finalYjk = yjk;
                BigDecimal finalYck = yck;
//                BigDecimal finalYhk = yhk;
                v.forEach(i -> {
                    emsAexgMapper.update(null, new LambdaUpdateWrapper<PtsEmsAexg>()
                            .set(PtsEmsAexg::getExportedQty, finalYck)
                            .eq(PtsEmsAexg::getId, i.getId()));
                    log.info("成品：【{}|{}|{}】 更新：已出口数量：{}，",
                            i.getGNo(), i.getCopGno(), i.getCodet(),  finalYck);
                });
            });
        }
        return null;
    }

    /**
     * 获得总页数
     *
     * @param total 总记录数
     * @param pageSize  每页多少条
     * @return
     */
    protected int getTotalPage(int total, int pageSize) {
        return total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
    }

    /**
     * 开始List分页
     *
     * @param list
     * @param pageNum  页码
     * @param pageSize 每页多少条数据
     * @return
     */
    public List startPage(List list, Integer pageNum, Integer pageSize) {
        if (list == null) {
            return null;
        }
        if (list.size() == 0) {
            return null;
        }
        Integer count = list.size(); // 记录总数
        Integer pageCount = 0; // 页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        int fromIndex = 0; // 开始索引
        int toIndex = 0; // 结束索引
        fromIndex = pageNum * pageSize - pageSize;
        if (pageNum == 0) {
            throw new ArithmeticException("第0页无法展示");
        } else if (pageNum > pageCount) {
            //如果查询的页码数大于总的页码数，list设置为[]
            list = new ArrayList<>();
        } else if (pageNum.equals(pageCount)) {
            //如果查询的当前页等于总页数，直接索引到total处
            toIndex = count;
        } else {
            //如果查询的页码数小于总页数，不用担心切割List的时候toIndex索引会越界，直接等
            toIndex = pageNum * pageSize;
        }
        if (list.size() == 0) {
            list = list;
        } else {
            list = list.subList(fromIndex, toIndex);
        }
        return list;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public Result<Map<String,Object>> importEms(Integer type, PtsEmsHead emsHead) {
        //征免方式
        List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");
        //币制
        List<ErpCurrencies> erpCurrenciesList = erpCurrenciesMapper.selectList(null);
        //国别地区
        List<ErpCountries> erpCountriesList = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                .eq(ErpCountries::getIsenabled,0));
        //单位
        List<ErpUnits> erpUnitsList = erpUnitsMapper.selectList(null);


        switch (type) {
            case 0: // 料件
//                setEmsIdAndEmsNo(emsHead);// 设置表体项
//                return batchInsertEmsAimg(emsHead.getEmsAimgList());
            case 1: // 成品
//                setEmsIdAndEmsNo(emsHead); // 设置表体项
//                return batchInsertEmsAexg(emsHead.getEmsAexgList());
            case 2: // 单损耗
//                setEmsIdAndEmsNo(emsHead); // 设置表体项
//                return batchInsertEmsCm(emsHead.getEmsCmList());
            case 3: // 全部
            default:
                return insertAll(emsHead,zjmsfsList,erpCurrenciesList,erpCountriesList,erpUnitsList);
        }
    }

    /**
     * 账册发送报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/1/20 15:16
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handlePush(String ids,String type,String flag) {
        List<PtsEmsHead> ptsEmsHeadList = baseMapper.selectList(new QueryWrapper<PtsEmsHead>().lambda()
                .in(PtsEmsHead::getId, Arrays.asList(ids.split(","))));
        if (isEmpty(ptsEmsHeadList)) {
            return Result.error("未找到推送手账册,请核实数据");
        }
        Map<Long, List<PtsEmsAimg>> ptsEmsAimgMap = new HashMap<>();
        Map<Long, List<PtsEmsAexg>> ptsEmsAexgMap = new HashMap<>();
        Map<Long, List<PtsEmsCm>> ptsEmsCmMap = new HashMap<>();
        List<PtsEmsAimg> ptsEmsAimgList = emsAimgMapper.selectList(new QueryWrapper<PtsEmsAimg>().lambda()
                .in(PtsEmsAimg::getEmsId, ptsEmsHeadList.stream().map(PtsEmsHead::getId).collect(Collectors.toList())));
        if (isNotEmpty(ptsEmsAimgList)) {
            ptsEmsAimgMap = ptsEmsAimgList.stream().collect(Collectors.groupingBy(PtsEmsAimg::getEmsId));
        }
        List<PtsEmsAexg> ptsEmsAexgList = emsAexgMapper.selectList(new QueryWrapper<PtsEmsAexg>().lambda()
                .in(PtsEmsAexg::getEmsId, ptsEmsHeadList.stream().map(PtsEmsHead::getId).collect(Collectors.toList())));
        if (isNotEmpty(ptsEmsAexgList)) {
            ptsEmsAexgMap = ptsEmsAexgList.stream().collect(Collectors.groupingBy(PtsEmsAexg::getEmsId));
        }
        List<PtsEmsCm> ptsEmsCmList = emsCmMapper.selectList(new QueryWrapper<PtsEmsCm>().lambda()
                .in(PtsEmsCm::getEmsId, ptsEmsHeadList.stream().map(PtsEmsHead::getId).collect(Collectors.toList())));
        if (isNotEmpty(ptsEmsCmList)) {
            ptsEmsCmMap = ptsEmsCmList.stream().collect(Collectors.groupingBy(PtsEmsCm::getEmsId));
        }
        String errors = "";
        //"2".equals(type)时用remoteSendInvtPath，"1".equals( type)时用remoteSendNptsPath
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = null;
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
        if( "2".equals(type)){
            ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendInvtPath());
        }else if( "1".equals( type)){
            ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), "/ImpPath/Npts/OutBox/");
        }
        for (PtsEmsHead ptsEmsHead : ptsEmsHeadList) {
            ptsEmsHead.setEmsAimgList(ptsEmsAimgMap.get(ptsEmsHead.getId()));
            ptsEmsHead.setEmsAexgList(ptsEmsAexgMap.get(ptsEmsHead.getId()));
            ptsEmsHead.setEmsCmList(ptsEmsCmMap.get(ptsEmsHead.getId()));
//            String fileName = ptsEmsHead.getId() + "_" + ptsEmsHead.getEmsNo() + "-EMS111" + ".xml";
//            String fileName = ptsEmsHead.getId() + "_" + ptsEmsHead.getEmsNo() + "-EMS111" + ".zip";
            String fileName = null;
            if( "2".equals(type)){
                fileName = new StringBuilder(ptsEmsHead.getId().toString()).append("-EMS111").toString();//文件名称
            }else if ( "1".equals( type)){
                fileName = new StringBuilder(ptsEmsHead.getId().toString()).append("-NPTS001").toString();//文件名称
            }
            boolean uploadFlag = false;
            try {
                if("2".equals(type)){//加工贸易账册
                    if (SFTP.equals(ftpType)) {
                        uploadFlag = new SFTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                                new ByteArrayInputStream(MessageFileUtil.exportZip(EmsMessageUtil.generateMessage(ptsEmsHead, fileName),
                                        String.format("%s.xml", fileName)).toByteArray()));
                    } else {
                        uploadFlag = new FTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                                new ByteArrayInputStream(MessageFileUtil.exportZip(EmsMessageUtil.generateMessage(ptsEmsHead, fileName),
                                        String.format("%s.xml", fileName)).toByteArray()));
                    }
                }
                if("1".equals( type)){//加工贸易手册
                    //获取当前登录的企业信息
                    EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                            .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
                    if (SFTP.equals(ftpType)) {
                        uploadFlag = new SFTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                                new ByteArrayInputStream(MessageFileUtil.exportZip(EmlMessageUtil.generateMessage(ptsEmsHead, fileName,enterpriseInfo,flag),
                                        String.format("%s.xml", fileName)).toByteArray()));
                    } else {
                        uploadFlag = new FTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                                new ByteArrayInputStream(MessageFileUtil.exportZip(EmlMessageUtil.generateMessage(ptsEmsHead, fileName,enterpriseInfo,flag),
                                        String.format("%s.xml", fileName)).toByteArray()));
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
                if (isBlank(errors)){
                    errors = new StringBuilder("发送失败的手账册为：").append(ptsEmsHead.getEmsNo()).toString();
                }else {
                    errors = new StringBuilder(errors).append(",").append(ptsEmsHead.getEmsNo()).toString();
                }
                continue;
            }
            if (!uploadFlag){
                if (isBlank(errors)){
                    errors = new StringBuilder("发送失败的手账册为：").append(ptsEmsHead.getEmsNo()).toString();
                }else {
                    errors = new StringBuilder(errors).append(",").append(ptsEmsHead.getEmsNo()).toString();
                }
            } else {
                baseMapper.update(null, new UpdateWrapper<PtsEmsHead>().lambda()
                        .set(PtsEmsHead::getSend, "1")
                        .eq(PtsEmsHead::getId, ptsEmsHead.getId()));
            }
        }
        if (isNotBlank(errors)) {
            return Result.error(errors);
        }
        return Result.ok("发送成功");
    }

    /**
     * 回执回填统一编号
     *
     * @param id
     * @param etpsPreentNo
     * @param seqNo
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.PtsEmsHead>
     * <AUTHOR>
     * @date 2025/1/22 10:02
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<PtsEmsHead> setSeqNoByEtpsNoOrId(String id, String etpsPreentNo, String seqNo) {
        PtsEmsHead ptsEmsHead = baseMapper.selectOne(new QueryWrapper<PtsEmsHead>().lambda()
                .eq(isNotBlank(id), PtsEmsHead::getId, id)
                .eq(isNotBlank(etpsPreentNo), PtsEmsHead::getCopEmsNo, etpsPreentNo));
        if (isEmpty(ptsEmsHead)) {
            return Result.error("未找到对应账册！流水号:{" + id + "},企业内部编号:{" + etpsPreentNo + "}");
        }
        baseMapper.update(null, new UpdateWrapper<PtsEmsHead>().lambda()
                .set(PtsEmsHead::getSeqNo, seqNo)
                .set(PtsEmsHead::getStatus, "0")
                .eq(PtsEmsHead::getId, ptsEmsHead.getId()));
        return Result.ok(ptsEmsHead);
    }

    /**
     * 插入所有(包含手/账册表头、手/账册归并后料件、手/账册归并后成品、手/账册单损耗)
     *
     * @param emsHead 手/账册表头对象
     * @return 处理数据库操作结果DTO对象
     */

    private Result<Map<String,Object>> insertAll(PtsEmsHead emsHead,List<DictModelVO> zjmsfsList,
                                                 List<ErpCurrencies> erpCurrenciesList,List<ErpCountries> erpCountriesList,
                                                 List<ErpUnits> erpUnitsList){
        try {


        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 错误信息集合
        List<String> errorMessageList = new ArrayList<String>();
        // 成功行数
        int successRows = 1;
        // 插入手/账册表头
        String errorEmsHeadMessage = null;
        String insert = insertEmsHead(emsHead);
        if (insert != null && !"".equals(insert) && !insert.contains("ID")) {
            errorEmsHeadMessage = insert;
        }
        if (StringUtils.isNotBlank(errorEmsHeadMessage)) {
            errorMessageList.add(errorEmsHeadMessage);
            successRows -= 1;
        }
        // 设置表体项
        setEmsIdAndEmsNo(emsHead);
        // 批量插入手/账册归并后料件
        Map<String, Object> stringObjectMap = batchInsertEmsAimg(emsHead.getEmsAimgList(),zjmsfsList,erpCurrenciesList,
                erpCountriesList,erpUnitsList);
        successRows += (int)stringObjectMap.get("successRows");
        errorMessageList.addAll((List<String>)stringObjectMap.get("errorMessageList"));
        // 批量插入手/账册归并后成品
        Map<String, Object> stringObjectMap1 = batchInsertEmsAexg(emsHead.getEmsAexgList(),zjmsfsList,erpCurrenciesList,
                erpCountriesList,erpUnitsList);
        successRows += (int)stringObjectMap1.get("successRows");
        errorMessageList.addAll((List<String>)stringObjectMap1.get("errorMessageList"));
        // 批量插入手/账册单损耗
        Map<String, Object> stringObjectMap2 = batchInsertEmsCm(emsHead.getEmsCmList());
        successRows += (int)stringObjectMap2.get("successRows");
        errorMessageList.addAll((List<String>)stringObjectMap2.get("errorMessageList"));
        /*
         * 导入手账册也加流水记录
         * 2025/01/14 13:39@zls
         */
        Map<String, Object> allMap = new HashMap<>(16);
        for (PtsEmsAimg aimg : emsHead.getEmsAimgList()) {
            aimg.setType("LJ");
        }
        for (PtsEmsAexg aexg : emsHead.getEmsAexgList()) {
            aexg.setType("CP");
        }
        allMap.put("LJ", emsHead.getEmsAimgList());
        allMap.put("CP", emsHead.getEmsAexgList());
        PtsEmsFlows flows = new PtsEmsFlows(loginUser.getUsername(), null, emsHead.getEmsNo(),
                Long.valueOf(TenantContext.getTenant()), OptTypeEnum.IMPORT_ADD.getV(), 1, new Date(), new Date(),
                JSON.toJSONString(allMap), null, 1);
        emsFlowsMapper.insert(flows);
        //变更流水状态
        flows.setOkDate(new Date());
        flows.setOpStatus(1);
        flows.setMessage("操作成功");
        emsFlowsMapper.updateById(flows);
        // 2025/1/13 17:33@zls 追加/变更/完善：新增流水明细！
//        emsDetailService.addEmsDetails(flows.getId().toString(), null, null);

        Map<String,Object> map = new HashMap<>();
        map.put("successRows",successRows);
        map.put("errorMessageList",errorMessageList);
        map.put("insert",insert);
        return Result.ok(map);
        }catch (Exception e){
            //回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            log.info("导入出现异常请联系管理员。" + e.getMessage());
            return Result.error("导入出现异常请联系管理员。" );
        }

    }
    /**
     * 插入手/账册表头
     *
     * @param emsHead 手/账册表头对象
     * @return 错误信息
     */
    private String insertEmsHead(PtsEmsHead emsHead) {
        PtsEmsHead dbEmsHead = baseMapper.selectOne(
                new QueryWrapper<PtsEmsHead>().eq("TENANT_ID", emsHead.getTenantId()).eq("EMS_NO", emsHead.getEmsNo()));
        log.info("插入手/账册表头,通过租户ID:{},手/账册编号:{},查询手/账册表头是否为空:{}", emsHead.getTenantId(), emsHead.getEmsNo(),
                dbEmsHead == null);
        if (null != dbEmsHead) {
            emsHead.setId(dbEmsHead.getId());
            return String.format("手/账册表头,当前租户下,手/账册编号:%s,数据已存在,导入失败", emsHead.getEmsNo());
        }
        // 2020/12/3 15:55@ZHANGCHAO 追加/变更/完善：不知为何登记日期没有设置？？
        if (isEmpty(emsHead.getByDate())) {
            emsHead.setByDate(new Date());
        }
        // 2021/1/19 10:39@ZHANGCHAO 追加/变更/完善：物流账册导入USE_TYPE默认W
        if ("TW".equals(emsHead.getEmsType()) || "L".equals(emsHead.getEmsType())) {
            emsHead.setUseType(isBlank(emsHead.getUseType()) ? "W" : emsHead.getUseType());
        }
        //转换加工企业地区代码 20250415
        if(isNotBlank(emsHead.getRegionCode())){
            erpDistrictsMapper.selectList(null).stream().filter(i->i.getName().equals(emsHead.getRegionCode()))
                  .forEach(i->emsHead.setRegionCode(i.getCode()));
        }
        //转换监管方式
        if(isNotBlank(emsHead.getTradeTypeCode())){
            List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
            List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getText()
                            .equals(emsHead.getTradeTypeCode()))
                    .collect(Collectors.toList());
            emsHead.setTradeTypeCode(dictModelVO1.isEmpty()?null:dictModelVO1.get(0).getValue());
        }
        //征免性质
        if(isNotBlank(emsHead.getTaxTypeCode())){
            List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
            List<DictModelVO> dictModelVO1=zmxz.stream().filter(i->i.getText()
                            .equals(emsHead.getTaxTypeCode()))
                    .collect(Collectors.toList());
            emsHead.setTaxTypeCode(dictModelVO1.isEmpty()?null:dictModelVO1.get(0).getValue());
        }
        //加工种类
        if(isNotBlank(emsHead.getProcessingType())){
            List<DictModelVO> jgzl = decListMapper.getDictItemByCode("JGZL");
            List<DictModelVO> dictModelVO1=jgzl.stream().filter(i->i.getText()
                            .equals(emsHead.getProcessingType()))
                    .collect(Collectors.toList());
            emsHead.setProcessingType(dictModelVO1.isEmpty()?null:dictModelVO1.get(0).getValue());
        }
        List<ErpCustomsPorts> erpCustomsPortList = erpCustomsPortsMapper.selectList(null);
//进出口岸
        if(isNotBlank(emsHead.getIePort())){
            erpCustomsPortList.stream().filter(i->i.getName().equals(emsHead.getIePort()))
                 .forEach(i->emsHead.setIePort(i.getCustomsPortCode()));
        }
        List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
        //进口币制
        if(isNotBlank(emsHead.getImportCurrency())){
            erpCurrencies.stream().filter(i->i.getName().equals(emsHead.getImportCurrency()))
                .forEach(i->emsHead.setImportCurrency(i.getCode()));
        }
        //出口币制
        if(isNotBlank(emsHead.getExportCurrency())){
            erpCurrencies.stream().filter(i->i.getName().equals(emsHead.getExportCurrency()))
               .forEach(i->emsHead.setExportCurrency(i.getCode()));
        }
        //转换海关关区
        if(isNotBlank(emsHead.getMasterCustoms())){

            List<ErpCustomsPorts> erpCustomsPorts = erpCustomsPortList.stream().filter(
                    i->i.getName().equals(emsHead.getMasterCustoms())).collect(Collectors.toList());
            emsHead.setMasterCustoms(erpCustomsPorts.isEmpty()?null:erpCustomsPorts.get(0).getCustomsPortCode());
        }
        //转换区域场所类型
        if(isNotBlank(emsHead.getRegionalSiteType())){
            //区域场所类型
            List<DictModelVO> qycslb = decListMapper.getDictItemByCode("QYCSLB");
            List<DictModelVO> qycslbInfo = qycslb.stream().filter(i->i.getText().equals(emsHead.getRegionalSiteType()))
                    .collect(Collectors.toList());
            emsHead.setRegionalSiteType(qycslbInfo.isEmpty()?null:qycslbInfo.get(0).getValue());
        }
        //转换企业类型
        if(isNotBlank(emsHead.getBusinessType())){
            //转换企业类型
            List<DictModelVO> qycslb = decListMapper.getDictItemByCode("QYCSLB");
            List<DictModelVO> qycslbInfo = qycslb.stream().filter(i->i.getText().equals(emsHead.getBusinessType()))
                    .collect(Collectors.toList());
            emsHead.setBusinessType(qycslbInfo.isEmpty()?null:qycslbInfo.get(0).getValue());
        }


        int rows = baseMapper.insert(emsHead);
        log.info("插入手/账册表头,通过租户ID:{},手/账册编号:{},插入受影响行数:{}", emsHead.getTenantId(), emsHead.getEmsNo(), rows);
        if (rows <= 0) {
            return String.format("手/账册表头,当前租户下,手/账册编号:%s,导入失败", emsHead.getEmsNo());
        }
        return "ID" + emsHead.getId().toString();
    }
    /**
     * 批量插入手/账册归并后料件
     *
     * @param emsAimgList 手/账册归并后料件集合
     * @return 处理数据库操作结果DTO对象
     */
    private Map<String,Object> batchInsertEmsAimg(List<PtsEmsAimg> emsAimgList,List<DictModelVO> zjmsfsList,
                                                  List<ErpCurrencies> erpCurrenciesList,List<ErpCountries> erpCountriesList,
                                                  List<ErpUnits> erpUnitsList) {
        // 错误信息集合
        List<String> errorMessageList = new ArrayList<String>();
        // 成功条数
        int successRows = 0;
        for (PtsEmsAimg emsAimg : emsAimgList) {
//            int count = emsAimgMapper.selectCount(new QueryWrapper<EmsAimg>().eq("EMS_ID", emsAimg.getEmsId())
//                    .eq("EMS_NO", emsAimg.getEmsNo()).eq("G_NO", emsAimg.getgNo()));
//			if (count > 0) {
//				errorMessageList.add(
//						String.format("手/账册归并后料件,手/账册编号:%s,料件序号:%s,数据已存在,导入失败", emsAimg.getEmsNo(), emsAimg.getgNo()));
//				continue;
//			}
            //征免方式
            if(isNotBlank(emsAimg.getDutyMode())){
                List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(i->i.getText().equals(emsAimg.getDutyMode()))
                        .collect(Collectors.toList());
                emsAimg.setDutyMode(dictModelVOS.isEmpty()?null:dictModelVOS.get(0).getValue());
            }
            //单位
            if(isNotBlank(emsAimg.getUnit())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAimg.getUnit()))
                        .collect(Collectors.toList());
                emsAimg.setUnit(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            if(isNotBlank(emsAimg.getUnit1())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAimg.getUnit1()))
                        .collect(Collectors.toList());
                emsAimg.setUnit1(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            if(isNotBlank(emsAimg.getUnit2())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAimg.getUnit2()))
                        .collect(Collectors.toList());
                emsAimg.setUnit2(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            //币制
            if(isNotBlank(emsAimg.getCurr())){
                List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getName().equals(emsAimg.getCurr()))
                        .collect(Collectors.toList());
                emsAimg.setCurr(erpCurrenciesList1.isEmpty()?null:erpCurrenciesList1.get(0).getCode());
            }
            //原产国
            if(isNotBlank(emsAimg.getCountryCode())){
                List<ErpCountries> erpCountriesList1=erpCountriesList.stream().filter(i->i.getName().equals(emsAimg.getCountryCode()))
                        .collect(Collectors.toList());
                emsAimg.setCountryCode(erpCountriesList1.isEmpty()?null:erpCountriesList1.get(0).getCode());
            }

            int rows = emsAimgMapper.insert(emsAimg);
//			if (rows <= 0) {
//				errorMessageList
//						.add(String.format("手/账册归并后料件,手/账册编号:%s,料件序号:%s,导入失败", emsAimg.getEmsNo(), emsAimg.getgNo()));
//				continue;
//			}
            // 累计成功条数
            successRows += rows;
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("successRows",successRows);
        resultMap.put("errorMessageList",errorMessageList);
        return resultMap;
    }
    /**
     * 批量插入手/账册归并后成品
     *
     * @param emsAexgList 手/账册归并后成品集合
     * @return 处理数据库操作结果DTO对象
     */
    private Map<String,Object> batchInsertEmsAexg(List<PtsEmsAexg> emsAexgList,List<DictModelVO> zjmsfsList,
                                                  List<ErpCurrencies> erpCurrenciesList,List<ErpCountries> erpCountriesList,
                                                  List<ErpUnits> erpUnitsList) {
        // 错误信息集合
        List<String> errorMessageList = new ArrayList<String>();
        // 成功条数
        int successRows = 0;
        for (PtsEmsAexg emsAexg : emsAexgList) {
//            int count = emsAexgMapper.selectCount(new QueryWrapper<EmsAexg>().eq("EMS_ID", emsAexg.getEmsId())
//                    .eq("EMS_NO", emsAexg.getEmsNo()).eq("G_NO", emsAexg.getgNo()));
//			if (count > 0) {
//				errorMessageList.add(
//						String.format("手/账册归并后成品,手/账册编号:%s,成品序号:%s,数据已存在,导入失败", emsAexg.getEmsNo(), emsAexg.getgNo()));
//				continue;
//			}
            //征免方式
            if(isNotBlank(emsAexg.getDutyMode())){
                List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(i->i.getText().equals(emsAexg.getDutyMode()))
                        .collect(Collectors.toList());
                emsAexg.setDutyMode(dictModelVOS.isEmpty()?null:dictModelVOS.get(0).getValue());
            }
            //单位
            if(isNotBlank(emsAexg.getUnit())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAexg.getUnit()))
                        .collect(Collectors.toList());
                emsAexg.setUnit(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            if(isNotBlank(emsAexg.getUnit1())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAexg.getUnit1()))
                        .collect(Collectors.toList());
                emsAexg.setUnit1(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            if(isNotBlank(emsAexg.getUnit2())){
                List<ErpUnits> erpUnitsList1=erpUnitsList.stream().filter(i->i.getName().equals(emsAexg.getUnit2()))
                        .collect(Collectors.toList());
                emsAexg.setUnit2(erpUnitsList1.isEmpty()?null:erpUnitsList1.get(0).getCode());
            }
            //币制
            if(isNotBlank(emsAexg.getCurr())){
                List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getName().equals(emsAexg.getCurr()))
                        .collect(Collectors.toList());
                emsAexg.setCurr(erpCurrenciesList1.isEmpty()?null:erpCurrenciesList1.get(0).getCode());
            }
            //原产国
            if(isNotBlank(emsAexg.getCountryCode())){
                List<ErpCountries> erpCountriesList1=erpCountriesList.stream().filter(i->i.getName().equals(emsAexg.getCountryCode()))
                        .collect(Collectors.toList());
                emsAexg.setCountryCode(erpCountriesList1.isEmpty()?null:erpCountriesList1.get(0).getCode());
            }


            int rows = emsAexgMapper.insert(emsAexg);
//			if (rows <= 0) {
//				errorMessageList
//						.add(String.format("手/账册归并后成品,手/账册编号:%s,成品序号:%s,导入失败", emsAexg.getEmsNo(), emsAexg.getgNo()));
//				continue;
//			}
            // 累计成功条数
            successRows += rows;
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("successRows",successRows);
        resultMap.put("errorMessageList",errorMessageList);
        return resultMap;
    }
    /**
     * 批量插入手/账册单损耗
     *
     * @param emsCmList 手/账册单损耗集合
     * @return 处理数据库操作结果DTO对象
     */
    private Map<String,Object> batchInsertEmsCm(List<PtsEmsCm> emsCmList) {
        // 错误信息集合
        List<String> errorMessageList = new ArrayList<String>();
        // 成功条数
        int successRows = 0;
        for (PtsEmsCm emsCm : emsCmList) {
//            int count = emsCmMapper.selectCount(new QueryWrapper<EmsCm>().eq("EMS_ID", emsCm.getEmsId())
//                    .eq("EMS_NO", emsCm.getEmsNo()).eq("EXG_NO", emsCm.getExgNo()).eq("IMG_NO", emsCm.getImgNo()));
//			if (count > 0) {
//				errorMessageList.add(String.format("手/账册单损耗,手/账册编号:%s,料件序号:%s,成品序号:%s,数据已存在,导入失败", emsCm.getEmsNo(),
//						emsCm.getImgNo(), emsCm.getExgNo()));
//				continue;
//			}
            int rows = emsCmMapper.insert(emsCm);
//			if (rows <= 0) {
//				errorMessageList.add(String.format("手/账册单损耗,手/账册编号:%s,料件序号:%s,成品序号:%s,导入失败", emsCm.getEmsNo(),
//						emsCm.getImgNo(), emsCm.getExgNo()));
//				continue;
//			}
            // 累计成功条数
            successRows += rows;
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("successRows",successRows);
        resultMap.put("errorMessageList",errorMessageList);
        return resultMap;
    }
    /**
     * 设置表体项的emsId和emsNo值
     *
     * @param emsHead 手账册表头数据
     */
    private void setEmsIdAndEmsNo(PtsEmsHead emsHead) {
        // 手/账册归并后料件集合
        if (CollectionUtils.isNotEmpty(emsHead.getEmsAimgList())) {
            for (PtsEmsAimg emsAimg : emsHead.getEmsAimgList()) {
                emsAimg.setEmsId(emsHead.getId());
                emsAimg.setEmsNo(emsHead.getEmsNo());
            }
        }
        // 手/账册归并后成品集合
        if (CollectionUtils.isNotEmpty(emsHead.getEmsAexgList())) {
            for (PtsEmsAexg emsAexg : emsHead.getEmsAexgList()) {
                emsAexg.setEmsId(emsHead.getId());
                emsAexg.setEmsNo(emsHead.getEmsNo());
            }
        }
        // 手/账册单损耗集合
        if (CollectionUtils.isNotEmpty(emsHead.getEmsCmList())) {
            for (PtsEmsCm emsCm : emsHead.getEmsCmList()) {
                emsCm.setEmsId(emsHead.getId());
                emsCm.setEmsNo(emsHead.getEmsNo());
            }
        }
    }

    @Override
    public IPage listEmsDetailAmountByReport(Page page, EmsQueryDto emsQueryDto) {
        IPage pageList = null;
        //料件
        if ("1".equals(emsQueryDto.getType())) {
            IPage<PtsEmsAimg> ptsEmsAimgIPage = emsAimgMapper.listEmsDetailAimgAmountByReport(page, emsQueryDto);
            IPage<PtsEmsAimg> ptsEmsAimgIPageCopy =
                    new Page<>(ptsEmsAimgIPage.getCurrent(), ptsEmsAimgIPage.getSize(), ptsEmsAimgIPage.getTotal());
            ptsEmsAimgIPageCopy.setRecords(ptsEmsAimgIPage.getRecords());
            if(!ptsEmsAimgIPageCopy.getRecords().isEmpty()){
                //出口耗用
                emsQueryDto.setType("3");
                IPage<PtsEmsCm> listEmsDetailByReportPage = listEmsDetailByReport(page, emsQueryDto);
                List<PtsEmsCm> listEmsDetailByReportPageRecords = listEmsDetailByReportPage.getRecords();
                List<PtsEmsCm> listEmsDetailByReportPageRecordsCopy = new ArrayList<>(listEmsDetailByReportPageRecords);
                //转换币制 查询汇率表为英文 ，查出的数据为数字代码需要转换
                List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
                //昨天汇率
                //获取昨天的汇率信息
                LocalDate yesterday = LocalDate.now().minusDays(1);

                List<RateInfo> rateInfos = rateInfoMapper.selectList(new LambdaQueryWrapper<RateInfo>()
                        .like(RateInfo::getRateDate, yesterday));
                for(PtsEmsAimg ptsEmsAimg:ptsEmsAimgIPageCopy.getRecords()){
                    //计算加权平均单价 = 实际进口金额合计/直接进口数量(进口汇总中的直接进口数量字段)保留五位小数
                    ptsEmsAimg.setJqpjdj(ptsEmsAimg.getZjjkslQty().compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO:
                            ptsEmsAimg.getSjjkjehj().divide(ptsEmsAimg.getZjjkslQty(),5, RoundingMode.HALF_UP));
                    //计算 已出口成品折料金额合计 = 出口成品耗料合计*加权平均单价 出口成品耗料合计来自核销平衡页面（或者出口耗用该料号汇总）
                    //出口成品耗料合计
                    List<PtsEmsCm> ptsEmsCms = listEmsDetailByReportPageRecordsCopy.stream().filter(i ->
                                    i.getImgNo().equals(ptsEmsAimg.getGNo())).collect(Collectors.toList());
                    //出口成品耗料合计 = 汇总ptsEmsCms的出口耗用
                    BigDecimal ckcphlhj =
                            ptsEmsCms.stream().map(PtsEmsCm::getCkhy).filter(Objects::nonNull).map(BigDecimal::new).
                                    reduce(BigDecimal.ZERO, BigDecimal::add);
                    ptsEmsAimg.setYckcpzljehj(ckcphlhj.multiply(ptsEmsAimg.getJqpjdj()));
                    if(isNotBlank(ptsEmsAimg.getCurr())){
                        List<ErpCurrencies> collectErpCurrencies = erpCurrencies.stream().filter(i -> i.getCode().equals(ptsEmsAimg.getCurr()))
                                .collect(Collectors.toList());
                        ptsEmsAimg.setCurr(collectErpCurrencies.isEmpty()?ptsEmsAimg.getCurr():collectErpCurrencies.get(0).getCurrency());
                        //不为美元需要转换为美元
                        if(!"USD".equals(ptsEmsAimg.getCurr())&&!rateInfos.isEmpty()){
                            List<RateInfo> collect = rateInfos.stream().filter(i -> i.getCurrency().equals(ptsEmsAimg.getCurr()))
                                    .collect(Collectors.toList());
                            if(!collect.isEmpty()){
                                RateInfo rateInfo = collect.get(0);
                                //直接进口金额合计A
                                ptsEmsAimg.setZjjksl(new BigDecimal(ptsEmsAimg.getZjjksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //深加工结转进口金额合计B
                                ptsEmsAimg.setSjgjzjksl(new BigDecimal(ptsEmsAimg.getSjgjzjksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //余料转入金额合计C
                                ptsEmsAimg.setYljzjksl(new BigDecimal(ptsEmsAimg.getYljzjksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //料件退换进口金额合计(D)
                                ptsEmsAimg.setLjthjksl(new BigDecimal(ptsEmsAimg.getLjthjksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //料件退换出口金额合计(E)
                                ptsEmsAimg.setLjthcksl(new BigDecimal(ptsEmsAimg.getLjthcksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //进口金额合计(F)
                                ptsEmsAimg.setJkjehj(ptsEmsAimg.getJkjehj().multiply(rateInfo.getUsd()));
                                //内销征税金额合计(G)
                                ptsEmsAimg.setNxzssl(new BigDecimal(ptsEmsAimg.getNxzssl()).multiply(rateInfo.getUsd()).toPlainString());
                                //转复出金额合计(H)
                                ptsEmsAimg.setZfcsl(new BigDecimal(ptsEmsAimg.getZfcsl()).multiply(rateInfo.getUsd()).toPlainString());
                                //销毁金额合计(I)
                                ptsEmsAimg.setXhsl(new BigDecimal(ptsEmsAimg.getXhsl()).multiply(rateInfo.getUsd()).toPlainString());
                                //余料转出金额合计(J)
                                ptsEmsAimg.setYlckjzsl(new BigDecimal(ptsEmsAimg.getYlckjzsl()).multiply(rateInfo.getUsd()).toPlainString());
                                //实际金额进口合计(K=F-G-H-I-J)
                                ptsEmsAimg.setSjjkjehj(ptsEmsAimg.getSjjkjehj().multiply(rateInfo.getUsd()));
                                //已出口成品折料金额合计
                                ptsEmsAimg.setYckcpzljehj(ptsEmsAimg.getYckcpzljehj().multiply(rateInfo.getUsd()));
                                //加权平均单价
                                ptsEmsAimg.setJqpjdj(ptsEmsAimg.getJqpjdj().multiply(rateInfo.getUsd()));
                                //边角料征税金额合计
                                ptsEmsAimg.setBjlybsl(ptsEmsAimg.getBjlybsl().multiply(rateInfo.getUsd()));
                                //边角料复出金额合计
                                ptsEmsAimg.setBjlfcsl(ptsEmsAimg.getBjlfcsl().multiply(rateInfo.getUsd()));

                            }


                        }

                    }

                }
                //根据料件序号汇总合并
                // 使用Stream API按项号分组并汇总金额
                Map<Integer, PtsEmsAimg> resultMap = ptsEmsAimgIPageCopy.getRecords().stream()
                        .collect(Collectors.toMap(
                                PtsEmsAimg::getGNo, // 以料号作为key
                                material -> material,   // 值就是material对象本身
                                (existing, replacement) -> { // 合并函数，当key冲突时执行
                                    // 汇总各个金额字段
                                    existing.setZjjksl(new BigDecimal(existing.getZjjksl()).add(new BigDecimal(replacement.getZjjksl())).toPlainString());
                                    existing.setSjgjzjksl(new BigDecimal(existing.getSjgjzjksl()).add(new BigDecimal(replacement.getSjgjzjksl())).toPlainString());
                                    existing.setYljzjksl(new BigDecimal(existing.getYljzjksl()).add(new BigDecimal(replacement.getYljzjksl())).toPlainString());
                                    existing.setLjthjksl(new BigDecimal(existing.getLjthjksl()).add(new BigDecimal(replacement.getLjthjksl())).toPlainString());
                                    existing.setLjthcksl(new BigDecimal(existing.getLjthcksl()).add(new BigDecimal(replacement.getLjthcksl())).toPlainString());
                                    existing.setJkjehj(existing.getJkjehj().add(replacement.getJkjehj()));
                                    existing.setNxzssl(new BigDecimal(existing.getNxzssl()).add(new BigDecimal(replacement.getNxzssl())).toPlainString());
                                    existing.setZfcsl(new BigDecimal(existing.getZfcsl()).add(new BigDecimal(replacement.getZfcsl())).toPlainString());
                                    existing.setXhsl(new BigDecimal(existing.getXhsl()).add(new BigDecimal(replacement.getXhsl())).toPlainString());
                                    existing.setYlckjzsl(new BigDecimal(existing.getYlckjzsl()).add(new BigDecimal(replacement.getYlckjzsl())).toPlainString());
                                    existing.setSjjkjehj(existing.getSjjkjehj().add(replacement.getSjjkjehj()));
                                    existing.setYckcpzljehj(existing.getYckcpzljehj().add(replacement.getYckcpzljehj()));
                                    existing.setJqpjdj(existing.getJqpjdj().add(replacement.getJqpjdj()));
                                    existing.setBjlybsl(existing.getBjlybsl().add(replacement.getBjlybsl()));
                                    existing.setBjlfcsl(existing.getBjlfcsl().add(replacement.getBjlfcsl()));
                                    return existing;
                                }
                        ));

                ptsEmsAimgIPageCopy.setRecords(new ArrayList<>(resultMap.values())) ;


            }


            pageList = ptsEmsAimgIPageCopy;
        } else if ("2".equals(emsQueryDto.getType())) {
            //成品
            IPage<PtsEmsAexg> ptsEmsAexgIPage = emsAexgMapper.listEmsDetailAexgAmountByReport(page, emsQueryDto);
            IPage<PtsEmsAexg> ptsEmsAexgIPageCopy =
                    new Page<>(ptsEmsAexgIPage.getCurrent(), ptsEmsAexgIPage.getSize(), ptsEmsAexgIPage.getTotal());
            ptsEmsAexgIPageCopy.setRecords(ptsEmsAexgIPage.getRecords());
            if(!ptsEmsAexgIPageCopy.getRecords().isEmpty()){
                //转换币制 查询汇率表为英文 ，查出的数据为数字代码需要转换
                List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
                //昨天汇率
                //获取昨天的汇率信息
                LocalDate yesterday = LocalDate.now().minusDays(1);
                List<RateInfo> rateInfos = rateInfoMapper.selectList(new LambdaQueryWrapper<RateInfo>()
                        .like(RateInfo::getRateDate, yesterday));
                for(PtsEmsAexg ptsEmsAexg:ptsEmsAexgIPageCopy.getRecords()){
                    //加权平均单价 = 实际出口金额合计/直接出口数量(出口汇总中的直接出口数量字段)保留五位小数
                    ptsEmsAexg.setJqpjdj(ptsEmsAexg.getZjckslQty().compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO:
                            ptsEmsAexg.getSjckjehj().divide(
                            ptsEmsAexg.getZjckslQty(),5,BigDecimal.ROUND_HALF_UP));
                    if(isNotBlank(ptsEmsAexg.getCurr())){
                        //数字转英文
                        List<ErpCurrencies> collectErpCurrencies = erpCurrencies.stream().filter(i -> i.getCode().equals(ptsEmsAexg.getCurr()))
                                .collect(Collectors.toList());
                        ptsEmsAexg.setCurr(collectErpCurrencies.isEmpty()?ptsEmsAexg.getCurr():collectErpCurrencies.get(0).getCurrency());
                        //不为美元需要转换为美元
                        if(!"USD".equals(ptsEmsAexg.getCurr())&&!rateInfos.isEmpty()){
                            List<RateInfo> collect = rateInfos.stream().filter(i -> i.getCurrency().equals(ptsEmsAexg.getCurr()))
                                    .collect(Collectors.toList());
                            if(!collect.isEmpty()){
                                RateInfo rateInfo = collect.get(0);
                                //直接出口金额合计A
                                ptsEmsAexg.setZjcksl(new BigDecimal(ptsEmsAexg.getZjcksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //深加工结转出口金额合计B
                                ptsEmsAexg.setSjgjzcksl(new BigDecimal(ptsEmsAexg.getSjgjzcksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //成品退换出口金额合计(C)
                                ptsEmsAexg.setCpthcksl(new BigDecimal(ptsEmsAexg.getCpthcksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //成品退换进口金额合计(D)
                                ptsEmsAexg.setCpthjksl(new BigDecimal(ptsEmsAexg.getCpthjksl()).multiply(rateInfo.getUsd()).toPlainString());
                                //实际出口金额合计(E=A+B+C-D)
                                ptsEmsAexg.setSjckjehj(ptsEmsAexg.getSjckjehj().multiply(rateInfo.getUsd()));
                                //加权平均单价
                                ptsEmsAexg.setJqpjdj(ptsEmsAexg.getJqpjdj().multiply(rateInfo.getUsd()));
                            }
                        }

                    }

                }

                //根据料件序号汇总合并
                // 使用Stream API按项号分组并汇总金额
                Map<Integer, PtsEmsAexg> resultMap = ptsEmsAexgIPageCopy.getRecords().stream()
                        .collect(Collectors.toMap(
                                PtsEmsAexg::getGNo, // 以料号作为key
                                material -> material,   // 值就是material对象本身
                                (existing, replacement) -> { // 合并函数，当key冲突时执行
                                    // 汇总各个金额字段
                                    existing.setZjcksl(new BigDecimal(existing.getZjcksl()).add(new BigDecimal(replacement.getZjcksl())).toPlainString());
                                    existing.setSjgjzcksl(new BigDecimal(existing.getSjgjzcksl()).add(new BigDecimal(replacement.getSjgjzcksl())).toPlainString());
                                    existing.setCpthcksl(new BigDecimal(existing.getCpthcksl()).add(new BigDecimal(replacement.getCpthcksl())).toPlainString());
                                    existing.setCpthjksl(new BigDecimal(existing.getCpthjksl()).add(new BigDecimal(replacement.getCpthjksl())).toPlainString());
                                    existing.setSjckjehj(existing.getSjckjehj().add(replacement.getSjckjehj()));
                                    existing.setJqpjdj(existing.getJqpjdj().add(replacement.getJqpjdj()));
                                    return existing;
                                }
                        ));

                ptsEmsAexgIPageCopy.setRecords(new ArrayList<>(resultMap.values())) ;

            }
            pageList = ptsEmsAexgIPageCopy;

        }

        return pageList;
    }

    @Override
    public IPage<PtsEmsAimg> listEmsDetailByUnitPriceComparison(Page page, EmsQueryDto emsQueryDto) {
        IPage<PtsEmsAimg> ptsEmsAimgIPage = emsAimgMapper.listEmsDetailByUnitPriceComparison(page, emsQueryDto);
        //深拷贝一个 ptsEmsAimgIPage 完全拷贝
        IPage<PtsEmsAimg> ptsEmsAimgIPageCopy =
                new Page<>(ptsEmsAimgIPage.getCurrent(), ptsEmsAimgIPage.getSize(), ptsEmsAimgIPage.getTotal());
        ptsEmsAimgIPageCopy.setRecords(ptsEmsAimgIPage.getRecords());
        if(!ptsEmsAimgIPage.getRecords().isEmpty()){
            //转换币制 查询汇率表为英文 ，查出的数据为数字代码需要转换
            List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
            //昨天汇率
            //获取昨天的汇率信息
            LocalDate yesterday = LocalDate.now().minusDays(1);
            List<RateInfo> rateInfos = rateInfoMapper.selectList(new LambdaQueryWrapper<RateInfo>()
                    .like(RateInfo::getRateDate, yesterday));
            //获取加权平均单价 = 取进口保税料件金额统计中的加权平均单价
            emsQueryDto.setType("1");
            IPage<PtsEmsAimg> listEmsDetailAmountByReportPage = listEmsDetailAmountByReport(page, emsQueryDto);
            List<PtsEmsAimg> listEmsDetailAmountByReport = listEmsDetailAmountByReportPage.getRecords();
            //存储加权平均单价
            if(!listEmsDetailAmountByReport.isEmpty()){
                Map<Integer, PtsEmsAimg> mergedMap = new HashMap<>();
                for(PtsEmsAimg ptsEmsAimg:ptsEmsAimgIPageCopy.getRecords()){
                    List<PtsEmsAimg> collect = listEmsDetailAmountByReport.stream().filter(i -> i.getGNo().equals(ptsEmsAimg.getGNo()))
                            .collect(Collectors.toList());
                    ptsEmsAimg.setJqpjdj(collect.isEmpty()?BigDecimal.ZERO:collect.get(0).getJqpjdj());
                    //币值的转换
                    if(isNotBlank(ptsEmsAimg.getCurr())){
                        //数字转英文
                        List<ErpCurrencies> collectErpCurrencies = erpCurrencies.stream().filter(i -> i.getCode().equals(ptsEmsAimg.getCurr()))
                                .collect(Collectors.toList());
                        ptsEmsAimg.setCurr(collectErpCurrencies.isEmpty()?ptsEmsAimg.getCurr():collectErpCurrencies.get(0).getCurrency());
                        //不为美元需要转换为美元
                        if(!"USD".equals(ptsEmsAimg.getCurr())&&!rateInfos.isEmpty()){
                            List<RateInfo> collect2 = rateInfos.stream().filter(i -> i.getCurrency().equals(ptsEmsAimg.getCurr()))
                                    .collect(Collectors.toList());
                            if(!collect2.isEmpty()){
                                RateInfo rateInfo = collect2.get(0);
                                //通关最低单价
                                ptsEmsAimg.setTgzddj(ptsEmsAimg.getTgzddj().multiply(rateInfo.getUsd()));
                                //通关最高单价
                                ptsEmsAimg.setTgzgdj(ptsEmsAimg.getTgzgdj().multiply(rateInfo.getUsd()));
                                //加权平均单价
                                ptsEmsAimg.setJqpjdj(ptsEmsAimg.getJqpjdj().multiply(rateInfo.getUsd()));
                            }

                        }
                    }
                    Integer gNo = ptsEmsAimg.getGNo();
                    if (mergedMap.containsKey(gNo)) {
                        PtsEmsAimg existing = mergedMap.get(gNo);
                        BigDecimal minPrice = null!= existing.getTgzddj()?(existing.getTgzddj().min(ptsEmsAimg.getTgzddj())):null;
                        BigDecimal maxPrice = null!= existing.getTgzgdj()?(existing.getTgzgdj().max(ptsEmsAimg.getTgzgdj())):null;
                        ptsEmsAimg.setTgzddj(minPrice);
                        ptsEmsAimg.setTgzgdj(maxPrice);
                        mergedMap.put(gNo, ptsEmsAimg);
                    } else {
                        mergedMap.put(gNo, ptsEmsAimg);
                    }
                }
                ptsEmsAimgIPageCopy.setRecords(new ArrayList<>(mergedMap.values())) ;
            }
        }
        return ptsEmsAimgIPageCopy;
    }

    @Override
    @Transactional
    public Result<?> syncSmPtsEmsHeadData(String swid, String systemId, String seqNo, String tradeCode, String manualNo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }

        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", swid);
        jsonMap.put("systemId", systemId);
        jsonMap.put("tradeCode", tradeCode);
        jsonMap.put("seqNo", seqNo);
        jsonMap.put("manualNo", manualNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        StringBuilder msg = new StringBuilder();
        int totalCount = 0;
        try {
            // 等待直到获取到令牌
            rateLimiter.acquire();
            String result = sendOpenApi(URL_GET_EMS_HEAD, JSON.toJSONString(jsonMap));
            JSONObject jsonObject = JSON.parseObject(result);
            if (!(jsonObject.getBoolean("ok"))) {
                log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                return Result.error("返回错误信息了：：：" + jsonObject.getString("errors"));
            }
            if(isEmpty(jsonObject.get("data"))){
                return Result.error("未获取到手帐册信息。");
            }
            JSONArray jsonArray = JSON.parseArray(jsonObject.getString("data"));
            log.info("返回的手帐册表头列表个数：{}", jsonArray.size());
            totalCount=jsonArray.size();
            List<PtsEmsHead> ptsEmsHeadList = new ArrayList<>();
            if("ZC".equals(systemId)){
                //加贸账册处理
                for (Object item : jsonArray) {
                    JSONObject jo = (JSONObject) item;
                    PtsEmsHead ptsEmsHead = new PtsEmsHead();
                    ptsEmsHead.setOwnerCode(jo.getString("ownerCode"));//加工单位编码
                    ptsEmsHead.setOwnerSccd(jo.getString("ownerSccd"));//信用代码
                    ptsEmsHead.setDeclareDate(isNotBlank(jo.getString("declareDate"))?
                            DateUtil.parse(jo.getString("declareDate"),DatePattern.PURE_DATE_PATTERN):null);//申报日期
                    ptsEmsHead.setOwnerName(jo.getString("ownerName"));//加工单位名称
                    ptsEmsHead.setSeqNo(jo.getString("seqNo"));//统一编号
                    ptsEmsHead.setEndDate(isNotBlank(jo.getString("endDate"))?
                            DateUtil.parse(jo.getString("endDate"),DatePattern.PURE_DATE_PATTERN):null);//有效期
                    ptsEmsHead.setSuspendChangeMark(jo.getString("exeMark"));//暂停变更标记
                    ptsEmsHead.setEmsNo(jo.getString("emsNo"));//账册号
                    ptsEmsHead.setDclTypeCd(jo.getString("declareType"));//申报类型
                    ptsEmsHead.setStatus(jo.getString("status"));//状态
                    ptsEmsHead.setTradeCode(jo.getString("tradeCode"));//经营单位编码
                    ptsEmsHead.setTradeSccd(jo.getString("tradeSccd"));//信用代码
                    ptsEmsHead.setTradeName(jo.getString("tradeName"));//经营单位名称
                    ptsEmsHead.setTenantId(Long.valueOf(tenantId));
                    //账册类型 取账册号的第一位字母
                    ptsEmsHead.setEmsType(jo.getString("emsType"));
                    ptsEmsHeadList.add(ptsEmsHead);
                }
            }else if("WL".equals(systemId)){
                //物流账册处理
                for (Object item : jsonArray) {
                    JSONObject jo = (JSONObject) item;
                    PtsEmsHead ptsEmsHead = new PtsEmsHead();
                    ptsEmsHead.setDeclareDate(isNotBlank(jo.getString("dclTime"))?
                            DateUtil.parse(jo.getString("dclTime"),DatePattern.PURE_DATE_PATTERN):null);//申报日期
                    ptsEmsHead.setSeqNo(jo.getString("seqNo"));//统一编号
                    ptsEmsHead.setEndDate(isNotBlank(jo.getString("finishValidDate"))?
                            DateUtil.parse(jo.getString("finishValidDate"),DatePattern.PURE_DATE_PATTERN):null);//有效期
                    ptsEmsHead.setEmsNo(jo.getString("bwlNo"));//账册号
                    ptsEmsHead.setDclTypeCd(jo.getString("dclTypeCd"));//申报类型
                    ptsEmsHead.setStatus(jo.getString("status"));//状态
                    ptsEmsHead.setTradeCode(jo.getString("bizopEtpsno"));//经营单位编码
                    ptsEmsHead.setTradeSccd(jo.getString("bizopEtpsSccd"));//信用代码
                    ptsEmsHead.setTradeName(jo.getString("bizopEtpsNm"));//经营单位名称
                    ptsEmsHead.setTenantId(Long.valueOf(tenantId));
                    //账册类型 取账册号的第一位字母
//                    ptsEmsHead.setEmsType(jo.getString("emsType"));
                    ptsEmsHeadList.add(ptsEmsHead);
                }
            }else if("SC".equals(systemId)){
                //手册处理
                for (Object item : jsonArray) {
                    JSONObject jo = (JSONObject) item;
                    PtsEmsHead ptsEmsHead = new PtsEmsHead();
                    ptsEmsHead.setOwnerCode(jo.getString("ownerEtpsno"));//加工单位编码
                    ptsEmsHead.setDeclareDate(isNotBlank(jo.getString("dclTime"))?
                            DateUtil.parse(jo.getString("dclTime"),DatePattern.PURE_DATE_PATTERN):null);//申报日期
                    ptsEmsHead.setOwnerName(jo.getString("ownerEtpsNm"));//加工单位名称
                    ptsEmsHead.setSeqNo(jo.getString("seqNo"));//统一编号
                    ptsEmsHead.setEndDate(isNotBlank(jo.getString("validDate"))?
                            DateUtil.parse(jo.getString("validDate"),DatePattern.PURE_DATE_PATTERN):null);//有效期
                    ptsEmsHead.setSuspendChangeMark(jo.getString("exeMarkcd"));//暂停变更标记
                    ptsEmsHead.setEmsNo(jo.getString("emlNo"));//账册号
                    ptsEmsHead.setDclTypeCd(jo.getString("dclType"));//申报类型
                    ptsEmsHead.setStatus(jo.getString("status"));//状态
                    ptsEmsHead.setTradeCode(jo.getString("bizopEtpsno"));//经营单位编码
                    ptsEmsHead.setTradeName(jo.getString("bizopEtpsNm"));//经营单位名称
                    ptsEmsHead.setTenantId(Long.valueOf(tenantId));
                    //账册类型 取账册号的第一位字母
                    ptsEmsHead.setEmsType(isNotBlank(ptsEmsHead.getEmsNo())?
                            String.valueOf(ptsEmsHead.getEmsNo().charAt(0)):null);
                    ptsEmsHeadList.add(ptsEmsHead);
                }
            }
            List<String> emsNoList = ptsEmsHeadList.stream().map(PtsEmsHead::getEmsNo).collect(Collectors.toList());
            //根据账册号查询是否存在
            List<PtsEmsHead> ptsEmsHeadListByEmsNo = super.list(
                    new QueryWrapper<PtsEmsHead>().lambda().in(PtsEmsHead::getEmsNo, emsNoList));
//            if(ptsEmsHeadListByEmsNo.isEmpty()){
                //将list<PtsEmsHead> 转为map，key为emsNo,value为ID
                Map<String,Long> emsNoIdMap =
                        ptsEmsHeadListByEmsNo.stream().collect(Collectors.toMap(PtsEmsHead::getEmsNo,PtsEmsHead::getId));
                for (PtsEmsHead ptsEmsHead : ptsEmsHeadList) {
                    if(!ptsEmsHeadListByEmsNo.isEmpty()&&emsNoIdMap.containsKey(ptsEmsHead.getEmsNo())){
                        ptsEmsHead.setId(emsNoIdMap.get(ptsEmsHead.getEmsNo()));
                        ptsEmsHead.setByName(loginUser.getRealname());
                        ptsEmsHead.setUpdateBy("TASK");
                        ptsEmsHead.setUpdateDate(new Date());
                    }else {
                        ptsEmsHead.setByName(loginUser.getRealname());
                        ptsEmsHead.setCreateBy("TASK");
                        ptsEmsHead.setCreateDate(new Date());
                    }
                    //保存表头数据
                    super.saveOrUpdate(ptsEmsHead);
                    //请求表体数据
                    try {
                        Result<?> result1 = syncSmPtsEmsRecordData(swid, systemId, ptsEmsHead.getSeqNo(), tradeCode, ptsEmsHead.getEmsNo(),
                                tenantId, null, ptsEmsHead);
                    }catch (Exception e){
                        ExceptionUtil.getFullStackTrace(e);
                        log.error("获取手帐册表体出现异常：{}", e.getMessage());
                        return Result.error("获取手帐册表体出现异常：{}", e.getMessage());
                    }
                }
//            }
            //执行更新或者添加数据
//            super.saveOrUpdateBatch(ptsEmsHeadList);
        }catch (Exception e){
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取手帐册表头出现异常：{}", e.getMessage());
            return Result.error("获取手帐册表头出现异常：{}", e.getMessage());
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        msg.append("[").append("]本次共获取手帐册表头：").append(totalCount).append("个");
        return Result.ok(msg.toString());
    }

    @Override
    @Transactional
    public Result<?> syncSmPtsEmsRecordData(String swid, String systemId, String seqNo, String tradeCode,
                                            String manualNo,String tenantId,String PageNo,PtsEmsHead ptsEmsHead) {
        PageNo=isBlank(PageNo)?"1":PageNo;
        log.info("开始获取账册详情");
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", swid);
        jsonMap.put("systemId", systemId);
        jsonMap.put("tradeCode", tradeCode);
        jsonMap.put("seqNo", seqNo);
        jsonMap.put("manualNo", manualNo);
        jsonMap.put("PageNo", PageNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        try {
            // 等待直到获取到令牌
            rateLimiter.acquire();
            String result = sendOpenApi(URL_GET_EMS_DATA, JSON.toJSONString(jsonMap));
            JSONObject jsonObject = JSON.parseObject(result);
            if (!(jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")))) {
                log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                return Result.error("返回错误信息了：：：" + jsonObject.getString("errors"));
            }
            if("WL".equals(systemId)){
                dealWithWLData(jsonObject,swid, systemId,seqNo, tradeCode,
                        manualNo,tenantId, PageNo,ptsEmsHead);
            }
            if("ZC".equals(systemId)){
                dealWithZCData(jsonObject,swid, systemId,seqNo, tradeCode,
                        manualNo,tenantId, PageNo,ptsEmsHead);
            }
            //手册
            if("SC".equals(systemId)){
                dealWithSCData(jsonObject,swid, systemId,seqNo, tradeCode,
                        manualNo,tenantId, PageNo,ptsEmsHead);
            }

        }catch (Exception e){
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取手帐册详情出现异常：{}", e.getMessage());
            return Result.error("获取手帐册详情出现异常：{}", e.getMessage());
        }

        return null;
    }
    //处理物流账册详情
    private void dealWithWLData(JSONObject jsonObject,String swid, String systemId, String seqNo, String tradeCode,
                                String manualNo,String tenantId,String PageNo,PtsEmsHead ptsEmsHead){
        JSONObject data =jsonObject.getJSONObject("data");//返回结果的dadat信息
        JSONObject nptsEmlHead = data.getJSONObject("bwlHead");//返回的手帐册表头信息 需要再更新一次表头数据
        JSONArray nptsEmlImg = data.getJSONArray("bwlList");//返回的料件数据

        //打印日志 统一编号seqNo的料件nptsEmlImg个数、单损耗nptsEmlConsume个数、成品nptsEmlExg个数
        log.info("seqNo:{},料件个数:{}",seqNo,nptsEmlImg.size());
        //转换表头数据
        if(isNotEmpty(nptsEmlHead)){
            convertPtsEmsHeadWL(ptsEmsHead,nptsEmlHead);
            //更新表头数据 根据seqNo更新ptsEmsHead
            super.update(ptsEmsHead,new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsHead.getSeqNo()));
        }
        //转换料件数据
        if(isNotEmpty(nptsEmlImg)){
            List<PtsEmsAimg> ptsEmsImgList = new ArrayList<>();
            convertPtsEmsAimgWL(ptsEmsImgList,nptsEmlImg);
            //根据seqNo查询表头数据
            PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                    new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //查询存在的料件,存在则更新
            List<PtsEmsAimg> ptsEmsAimgList = ptsEmsAimgMapper.selectList(
                    new QueryWrapper<PtsEmsAimg>().lambda().in(PtsEmsAimg::getGNo,
                                    ptsEmsImgList.stream().map(PtsEmsAimg::getGNo).collect(Collectors.toList()))
                            .eq(PtsEmsAimg::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //将pstsEmsAimgList 转为map，key为gNo,value为ID
            Map<Integer,Long> gNoIdMap =
                    ptsEmsAimgList.stream().collect(Collectors.toMap(PtsEmsAimg::getGNo,PtsEmsAimg::getId));
            //赋值ID 根据gNo
            for (PtsEmsAimg ptsEmsAimg : ptsEmsImgList) {
                ptsEmsAimg.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                ptsEmsAimg.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                if(gNoIdMap.containsKey(ptsEmsAimg.getGNo())){
                    ptsEmsAimg.setId(gNoIdMap.get(ptsEmsAimg.getGNo()));
                }
            }
            ptsEmsAimgService.saveOrUpdateBatch(ptsEmsImgList);
        }
        //判断料件。成品。单损耗是否超过5000项，超过需要加页数请求
        //料件的总页数 = 总记录数 / 每页显示数（向上取整）
        int imgPageCount = (int) Math.ceil(data.getInteger("billMaxGNo") / 5000.0);
        //单损耗的总页数 = 总记录数 / 每页显示数（向上取整）
//            int consumePageCount = (int) Math.ceil(data.getInteger("CmTotal") / 5000.0);
        if(imgPageCount>Integer.parseInt(PageNo)){
            PageNo=String.valueOf(Integer.parseInt(PageNo)+1);
            syncSmPtsEmsRecordData(swid, systemId, seqNo, tradeCode,
                    manualNo, tenantId, PageNo, ptsEmsHead
            );
        }




    }

    //处理加贸账册详情
    private void dealWithZCData(JSONObject jsonObject,String swid, String systemId, String seqNo, String tradeCode,
                                String manualNo,String tenantId,String PageNo,PtsEmsHead ptsEmsHead){
        JSONObject data =jsonObject.getJSONObject("data");//返回结果的dadat信息
        JSONObject nptsEmlHead = data.getJSONObject("head");//返回的手帐册表头信息 需要再更新一次表头数据
        JSONArray nptsEmlImg = data.getJSONArray("ImgList");//返回的料件数据
        JSONArray nptsEmlExg = data.getJSONArray("ExgList");//返回的成品数据
        //判断data.getJSONArray里是否有"ConList"
        JSONArray nptsEmlConsume = data.containsKey("ConList")? data.getJSONArray("ConList"):null;//返回的单损耗数据
        //打印日志 统一编号seqNo的料件nptsEmlImg个数、单损耗nptsEmlConsume个数、成品nptsEmlExg个数
        log.info("seqNo:{},料件个数:{},成品个数:{}",seqNo,nptsEmlImg.size(),nptsEmlExg.size());
        //转换表头数据
        if(isNotEmpty(nptsEmlHead)){
            convertPtsEmsHeadZC(ptsEmsHead,nptsEmlHead);
            //更新表头数据 根据seqNo更新ptsEmsHead
            super.update(ptsEmsHead,new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsHead.getSeqNo()));
        }
        //转换料件数据
        if(isNotEmpty(nptsEmlImg)){
            List<PtsEmsAimg> ptsEmsImgList = new ArrayList<>();
            convertPtsEmsAimgZC(ptsEmsImgList,nptsEmlImg);
            //根据seqNo查询表头数据
            PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                    new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //查询存在的料件,存在则更新
            List<PtsEmsAimg> ptsEmsAimgList = ptsEmsAimgMapper.selectList(
                    new QueryWrapper<PtsEmsAimg>().lambda().in(PtsEmsAimg::getGNo,
                                    ptsEmsImgList.stream().map(PtsEmsAimg::getGNo).collect(Collectors.toList()))
                            .eq(PtsEmsAimg::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //将pstsEmsAimgList 转为map，key为gNo,value为ID
            Map<Integer,Long> gNoIdMap =
                    ptsEmsAimgList.stream().collect(Collectors.toMap(PtsEmsAimg::getGNo,PtsEmsAimg::getId));
            //赋值ID 根据gNo
            for (PtsEmsAimg ptsEmsAimg : ptsEmsImgList) {
                ptsEmsAimg.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                ptsEmsAimg.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                if(gNoIdMap.containsKey(ptsEmsAimg.getGNo())){
                    ptsEmsAimg.setId(gNoIdMap.get(ptsEmsAimg.getGNo()));
                }
            }
            ptsEmsAimgService.saveOrUpdateBatch(ptsEmsImgList);
        }
        //转换成品数据
        if(isNotEmpty(nptsEmlExg)){
            List<PtsEmsAexg> ptsEmsAexgList = new ArrayList<>();
            convertPtsEmsAexg(ptsEmsAexgList,nptsEmlExg);
            //根据seqNo查询表头数据
            PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                    new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsAexgList.get(0).getSeqNo()));
            //查询存在的成品,存在则更新
            List<PtsEmsAexg> ptsEmsAexgListHad = ptsEmsAexgService.list(
                    new QueryWrapper<PtsEmsAexg>().lambda().in(PtsEmsAexg::getGNo,
                                    ptsEmsAexgList.stream().map(PtsEmsAexg::getGNo).collect(Collectors.toList()))
                            .eq(PtsEmsAexg::getSeqNo,ptsEmsAexgList.get(0).getSeqNo()));
            //将ptsEmsAexgListHad 转为map，key为gNo,value为ID
            Map<Integer,Long> gNoIdMap =
                    ptsEmsAexgListHad.stream().collect(Collectors.toMap(PtsEmsAexg::getGNo,PtsEmsAexg::getId));
            //赋值ID 根据gNo
            for (PtsEmsAexg ptsEmsAexg : ptsEmsAexgList) {
                ptsEmsAexg.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                ptsEmsAexg.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                if(gNoIdMap.containsKey(ptsEmsAexg.getGNo())){
                    ptsEmsAexg.setId(gNoIdMap.get(ptsEmsAexg.getGNo()));
                }
            }
            ptsEmsAexgService.saveOrUpdateBatch(ptsEmsAexgList);
        }
        //转换单损耗数据
            if(isNotEmpty(nptsEmlConsume)){
                List<PtsEmsCm> ptsEmsCmList = new ArrayList<>();
                convertPtsEmsCmZC(ptsEmsCmList,nptsEmlConsume);
                //根据seqNo查询表头数据
                PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                        new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsCmList.get(0).getSeqNo()));
                for (PtsEmsCm ptsEmsCm : ptsEmsCmList) {
                    ptsEmsCm.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                    ptsEmsCm.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                }
                ptsEmsCmService.saveOrUpdateBatch(ptsEmsCmList);
            }
        //判断料件。成品。单损耗是否超过5000项，超过需要加页数请求
        //料件的总页数 = 总记录数 / 每页显示数（向上取整）
        int imgPageCount = (int) Math.ceil(data.getInteger("ImgTotal") / 5000.0);
        //成品的总页数 = 总记录数 / 每页显示数（向上取整）
        int exgPageCount = (int) Math.ceil(data.getInteger("ExgTotal") / 5000.0);
        //单损耗的总页数 = 总记录数 / 每页显示数（向上取整）
//            int consumePageCount = (int) Math.ceil(data.getInteger("CmTotal") / 5000.0);
        if(imgPageCount>Integer.parseInt(PageNo)||
                exgPageCount>Integer.parseInt(PageNo)){
            PageNo=String.valueOf(Integer.parseInt(PageNo)+1);
            syncSmPtsEmsRecordData(swid, systemId, seqNo, tradeCode,
                    manualNo, tenantId, PageNo, ptsEmsHead
            );
        }

    }
    private void dealWithSCData(JSONObject jsonObject,String swid, String systemId, String seqNo, String tradeCode,
                                String manualNo,String tenantId,String PageNo,PtsEmsHead ptsEmsHead){
        JSONObject data =jsonObject.getJSONObject("data");//返回结果的dadat信息
        JSONObject nptsEmlHead = data.getJSONObject("nptsEmlHead");//返回的手帐册表头信息 需要再更新一次表头数据
        JSONArray nptsEmlImg = data.getJSONArray("nptsEmlImg");//返回的料件数据
        JSONArray nptsEmlExg = data.getJSONArray("nptsEmlExg");//返回的成品数据
        JSONArray nptsEmlConsume = data.getJSONArray("nptsEmlConsume");//返回的单损耗数据
        //打印日志 统一编号seqNo的料件nptsEmlImg个数、单损耗nptsEmlConsume个数、成品nptsEmlExg个数
        log.info("seqNo:{},料件个数:{},成品个数:{}",seqNo,nptsEmlImg.size(),nptsEmlExg.size());
        //转换表头数据
        if(isNotEmpty(nptsEmlHead)){
            convertPtsEmsHeadSC(ptsEmsHead,nptsEmlHead);
            //更新表头数据 根据seqNo更新ptsEmsHead
            super.update(ptsEmsHead,new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsHead.getSeqNo()));
        }
        //转换料件数据
        if(isNotEmpty(nptsEmlImg)){
            List<PtsEmsAimg> ptsEmsImgList = new ArrayList<>();
            convertPtsEmsAimgSC(ptsEmsImgList,nptsEmlImg);
            //根据seqNo查询表头数据
            PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                    new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //查询存在的料件,存在则更新
            List<PtsEmsAimg> ptsEmsAimgList = ptsEmsAimgMapper.selectList(
                    new QueryWrapper<PtsEmsAimg>().lambda().in(PtsEmsAimg::getGNo,
                                    ptsEmsImgList.stream().map(PtsEmsAimg::getGNo).collect(Collectors.toList()))
                            .eq(PtsEmsAimg::getSeqNo,ptsEmsImgList.get(0).getSeqNo()));
            //将pstsEmsAimgList 转为map，key为gNo,value为ID
            Map<Integer,Long> gNoIdMap =
                    ptsEmsAimgList.stream().collect(Collectors.toMap(PtsEmsAimg::getGNo,PtsEmsAimg::getId));
            //赋值ID 根据gNo
            for (PtsEmsAimg ptsEmsAimg : ptsEmsImgList) {
                ptsEmsAimg.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                ptsEmsAimg.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                if(gNoIdMap.containsKey(ptsEmsAimg.getGNo())){
                    ptsEmsAimg.setId(gNoIdMap.get(ptsEmsAimg.getGNo()));
                }
            }
            ptsEmsAimgService.saveOrUpdateBatch(ptsEmsImgList);
        }
        //转换成品数据
        if(isNotEmpty(nptsEmlExg)){
            List<PtsEmsAexg> ptsEmsAexgList = new ArrayList<>();
            convertPtsEmsAexgSC(ptsEmsAexgList,nptsEmlExg);
            //根据seqNo查询表头数据
            PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                    new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsAexgList.get(0).getSeqNo()));
            //查询存在的成品,存在则更新
            List<PtsEmsAexg> ptsEmsAexgListHad = ptsEmsAexgService.list(
                    new QueryWrapper<PtsEmsAexg>().lambda().in(PtsEmsAexg::getGNo,
                                    ptsEmsAexgList.stream().map(PtsEmsAexg::getGNo).collect(Collectors.toList()))
                            .eq(PtsEmsAexg::getSeqNo,ptsEmsAexgList.get(0).getSeqNo()));
            //将ptsEmsAexgListHad 转为map，key为gNo,value为ID
            Map<Integer,Long> gNoIdMap =
                    ptsEmsAexgListHad.stream().collect(Collectors.toMap(PtsEmsAexg::getGNo,PtsEmsAexg::getId));
            //赋值ID 根据gNo
            for (PtsEmsAexg ptsEmsAexg : ptsEmsAexgList) {
                ptsEmsAexg.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                ptsEmsAexg.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                if(gNoIdMap.containsKey(ptsEmsAexg.getGNo())){
                    ptsEmsAexg.setId(gNoIdMap.get(ptsEmsAexg.getGNo()));
                }
            }
            ptsEmsAexgService.saveOrUpdateBatch(ptsEmsAexgList);
        }
        //转换单损耗数据
            if(isNotEmpty(nptsEmlConsume)){
                List<PtsEmsCm> ptsEmsCmList = new ArrayList<>();
                convertPtsEmsCm(ptsEmsCmList,nptsEmlConsume);
                //根据seqNo查询表头数据
                PtsEmsHead ptsEmsHeadBySeqNo = super.getOne(
                        new QueryWrapper<PtsEmsHead>().lambda().eq(PtsEmsHead::getSeqNo,ptsEmsCmList.get(0).getSeqNo()));
                for (PtsEmsCm ptsEmsCm : ptsEmsCmList) {
                    ptsEmsCm.setEmsId(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getId():null);
                    ptsEmsCm.setEmsNo(isNotEmpty(ptsEmsHeadBySeqNo)?ptsEmsHeadBySeqNo.getEmsNo():null);
                }
                ptsEmsCmService.saveOrUpdateBatch(ptsEmsCmList);
            }
        //判断料件。成品。单损耗是否超过5000项，超过需要加页数请求
        //料件的总页数 = 总记录数 / 每页显示数（向上取整）
        int imgPageCount = (int) Math.ceil(data.getInteger("ImgTotal") / 5000.0);
        //成品的总页数 = 总记录数 / 每页显示数（向上取整）
        int exgPageCount = (int) Math.ceil(data.getInteger("ExgTotal") / 5000.0);
        //单损耗的总页数 = 总记录数 / 每页显示数（向上取整）
//            int consumePageCount = (int) Math.ceil(data.getInteger("CmTotal") / 5000.0);
        if(imgPageCount>Integer.parseInt(PageNo)||
                exgPageCount>Integer.parseInt(PageNo)){
            PageNo=String.valueOf(Integer.parseInt(PageNo)+1);
            syncSmPtsEmsRecordData(swid, systemId, seqNo, tradeCode,
                    manualNo, tenantId, PageNo, ptsEmsHead
            );
        }

    }

    private void convertPtsEmsHeadZC(PtsEmsHead ptsEmsHead,JSONObject nptsEmlHead){
        ptsEmsHead.setEmsNo(nptsEmlHead.getString("emsNo"));//账册号
        ptsEmsHead.setSeqNo(nptsEmlHead.getString("seqNo"));//seqNo
        ptsEmsHead.setRegionCode(nptsEmlHead.getString("rcvgdEtpsDtcd"));//加工企业地区代码
        ptsEmsHead.setCopEmsNo(nptsEmlHead.getString("etpspreentno"));//企业内编
        ptsEmsHead.setTaxTypeCode(nptsEmlHead.getString("reduNatrcd"));//征免性质
        ptsEmsHead.setTelephone(nptsEmlHead.getString("linkManTel"));//联系电话
        ptsEmsHead.setByDate(DateUtil.parse(nptsEmlHead.getString("inputtime"), "yyyyMMdd"));//登记日期
        ptsEmsHead.setDeclareSccd(nptsEmlHead.getString("dcletpssccd"));//申报i单位信用编码
        ptsEmsHead.setInputDate(DateUtil.parse(nptsEmlHead.getString("inputtime"), "yyyyMMdd"));
        ptsEmsHead.setAssociates(nptsEmlHead.getString("linkMan"));//联系人
        ptsEmsHead.setDeclareType(nptsEmlHead.getString("dcletpstypecd"));//申报企业类型
        ptsEmsHead.setContractNoI(nptsEmlHead.getString("impCtrtNo"));//进口合同号
        ptsEmsHead.setContractNoE(nptsEmlHead.getString("expCtrtNo"));//出口合同号
        ptsEmsHead.setMasterCustoms(nptsEmlHead.getString("mastercuscd"));//主管海关
        ptsEmsHead.setDeclareCode(nptsEmlHead.getString("dcletpsno"));//申报单位十位
        ptsEmsHead.setSuspendChangeMark(nptsEmlHead.getString("pauseImpexpMarkcd"));//暂停变更标记
        ptsEmsHead.setChgTmsCnt(Integer.valueOf(nptsEmlHead.getString("chgtmscnt")));//变更次数
        ptsEmsHead.setProcessingType(nptsEmlHead.getString("produceTypecd"));//加工种类
        ptsEmsHead.setDeclareName(nptsEmlHead.getString("dcletpsnm"));//申报单位名称
        ptsEmsHead.setOwnerSccd(nptsEmlHead.getString("rvsngdetpssccd"));//加工单位信用代码
        ptsEmsHead.setOwnerName(nptsEmlHead.getString("rcvgdetpsnm"));//加工单位名称
        ptsEmsHead.setOwnerCode(nptsEmlHead.getString("rcvgdetpsno"));//加工单位十位
        ptsEmsHead.setManualPurpose(nptsEmlHead.getString("stndbkBankcd"));//手册用途
        ptsEmsHead.setCodeDeclarationUnitConsumption(nptsEmlHead.getString("ucnsdclsegcd"));//单耗申报环节代码
        ptsEmsHead.setExportCurrency(nptsEmlHead.getString("expCurrcd"));//出口币制
        ptsEmsHead.setImportCurrency(nptsEmlHead.getString("impCurrcd"));//进口币制
        ptsEmsHead.setTradeTypeCode(nptsEmlHead.getString("supvModecd"));//监管方式
        ptsEmsHead.setInputName(nptsEmlHead.getString("inputetpsnm"));//录入单位
        ptsEmsHead.setInputCode(nptsEmlHead.getString("inputetpstypecd"));//十位
        ptsEmsHead.setInputCreditCode(nptsEmlHead.getString("inputetpssccd"));//信用编码
        ptsEmsHead.setTradeCode(nptsEmlHead.getString("bizopetpsno"));//经营单位编码
        ptsEmsHead.setTradeName(nptsEmlHead.getString("bizopetpsnm"));//经营单位名称
        ptsEmsHead.setTradeSccd(nptsEmlHead.getString("bizopetpssccd"));//经营单位信用编码
        ptsEmsHead.setDclTypeCd(nptsEmlHead.getString("dcltypecd"));//申报类型
        ptsEmsHead.setCol1(nptsEmlHead.getString("col1"));//重点标识
    }
    private void convertPtsEmsHeadWL(PtsEmsHead ptsEmsHead,JSONObject nptsEmlHead){
        ptsEmsHead.setDeclareDate(isNotBlank(nptsEmlHead.getString("dclTime"))?
                DateUtil.parse(nptsEmlHead.getString("dclTime"),DatePattern.PURE_DATE_PATTERN):null);//申报日期
        ptsEmsHead.setRegionalSiteType(nptsEmlHead.getString("houseTypeCd"));//区域场所类型
        ptsEmsHead.setSeqNo(nptsEmlHead.getString("seqNo"));//seqNo
        ptsEmsHead.setEmsNo(nptsEmlHead.getString("bwlNo"));//账册号
        ptsEmsHead.setByDate(DateUtil.parse(nptsEmlHead.getString("addTime"), "yyyyMMdd"));//登记日期
        ptsEmsHead.setTelephone(nptsEmlHead.getString("contactTele"));//电话
        ptsEmsHead.setDeclareSccd(nptsEmlHead.getString("dclEtpsSccd"));//申报i单位信用编码
        ptsEmsHead.setDclTypeCd(nptsEmlHead.getString("dclTypeCd"));//申报类型
        ptsEmsHead.setInputName(nptsEmlHead.getString("inputName"));//录入单位
        ptsEmsHead.setKeepingMode(nptsEmlHead.getIntValue("appendTypeCd"));//记账模式
        ptsEmsHead.setAssociates(nptsEmlHead.getString("contactEr"));//联系人
        ptsEmsHead.setOwnerCode(nptsEmlHead.getString("houseNo"));//仓库代码
        ptsEmsHead.setArea(new BigDecimal(nptsEmlHead.getString("houseArea")));//面积
        ptsEmsHead.setMasterCustoms(nptsEmlHead.getString("masterCuscd"));//主管海关
        ptsEmsHead.setTradeName(nptsEmlHead.getString("bizopEtpsNm"));//经营单位名称
        ptsEmsHead.setBusinessType(nptsEmlHead.getString("bwlTypeCd"));//企业类型
        ptsEmsHead.setInputCode(nptsEmlHead.getString("inputCode"));//十位
        ptsEmsHead.setDeclareCode(nptsEmlHead.getString("dclEtpsno"));//申报单位十位
        ptsEmsHead.setInputCreditCode(nptsEmlHead.getString("inputSccd"));//信用编码
        ptsEmsHead.setOwnerName(nptsEmlHead.getString("houseNm"));//仓库名称
        ptsEmsHead.setChgTmsCnt(Integer.valueOf(nptsEmlHead.getString("chgTmsCnt")));//变更次数
        ptsEmsHead.setInputDate(DateUtil.parse(nptsEmlHead.getString("inputDate"), "yyyyMMdd"));
        ptsEmsHead.setDeclareType(nptsEmlHead.getString("dclEtpsTypeCd"));//申报企业类型
        ptsEmsHead.setDeclareName(nptsEmlHead.getString("dclEtpsNm"));//申报单位名称
        ptsEmsHead.setTradeCode(nptsEmlHead.getString("bizopEtpsno"));//经营单位编码
        ptsEmsHead.setTradeSccd(nptsEmlHead.getString("bizopEtpsSccd"));//经营单位信用编码
        ptsEmsHead.setAddress(nptsEmlHead.getString("houseAddress"));//仓库地址
        ptsEmsHead.setCol1(nptsEmlHead.getString("col1"));//重点标识
    }

    private void convertPtsEmsHeadSC(PtsEmsHead ptsEmsHead,JSONObject nptsEmlHead){
        ptsEmsHead.setEmsNo(nptsEmlHead.getString("emlNo"));//账册号
        ptsEmsHead.setSeqNo(nptsEmlHead.getString("seqNo"));//seqNo
        ptsEmsHead.setRegionCode(nptsEmlHead.getString("rcvgdEtpsDtcd"));//加工企业地区代码
        ptsEmsHead.setCopEmsNo(nptsEmlHead.getString("etpsPreentNo"));//企业内编
        ptsEmsHead.setTaxTypeCode(nptsEmlHead.getString("reduNatrcd"));//征免性质
        ptsEmsHead.setTelephone(nptsEmlHead.getString("linkManTel"));//联系电话
        ptsEmsHead.setByDate(DateUtil.parse(nptsEmlHead.getString("addTime"), "yyyyMMddHHmmssSSS"));//登记日期
        ptsEmsHead.setDeclareSccd(nptsEmlHead.getString("dclEtpsSccd"));//申报i单位信用编码
        ptsEmsHead.setInputDate(DateUtil.parse(nptsEmlHead.getString("inputTime"), "yyyyMMdd"));
        ptsEmsHead.setAssociates(nptsEmlHead.getString("linkMan"));//联系人
        ptsEmsHead.setDeclareType(nptsEmlHead.getString("dclEtpsTypecd"));//申报企业类型
        ptsEmsHead.setContractNoI(nptsEmlHead.getString("impCtrtNo"));//进口合同号
        ptsEmsHead.setContractNoE(nptsEmlHead.getString("expCtrtNo"));//出口合同号
        ptsEmsHead.setMasterCustoms(nptsEmlHead.getString("masterCuscd"));//主管海关
        ptsEmsHead.setDeclareCode(nptsEmlHead.getString("dclEtpsno"));//申报单位十位
        ptsEmsHead.setSuspendChangeMark(nptsEmlHead.getString("pauseImpexpMarkcd"));//暂停变更标记
        ptsEmsHead.setChgTmsCnt(Integer.valueOf(nptsEmlHead.getString("chgTmsCnt")));//变更次数
        ptsEmsHead.setProcessingType(nptsEmlHead.getString("produceTypecd"));//加工种类
        ptsEmsHead.setDeclareName(nptsEmlHead.getString("dclEtpsNm"));//申报单位名称
        ptsEmsHead.setOwnerSccd(nptsEmlHead.getString("rvsngdEtpsSccd"));//加工单位信用代码
        ptsEmsHead.setOwnerName(nptsEmlHead.getString("rcvgdEtpsNm"));//加工单位名称
        ptsEmsHead.setOwnerCode(nptsEmlHead.getString("rcvgdEtpsno"));//加工单位十位
        ptsEmsHead.setManualPurpose(nptsEmlHead.getString("stndbkBankcd"));//手册用途
        ptsEmsHead.setCodeDeclarationUnitConsumption(nptsEmlHead.getString("ucnsDclSegcd"));//单耗申报环节代码
        ptsEmsHead.setExportCurrency(nptsEmlHead.getString("expCurrcd"));//出口币制
        ptsEmsHead.setImportCurrency(nptsEmlHead.getString("impCurrcd"));//进口币制
        ptsEmsHead.setTradeTypeCode(nptsEmlHead.getString("supvModecd"));//监管方式
        ptsEmsHead.setInputName(nptsEmlHead.getString("inputEtpsNm"));//录入单位
        ptsEmsHead.setInputCode(nptsEmlHead.getString("inputEtpsTypecd"));//十位
        ptsEmsHead.setInputCreditCode(nptsEmlHead.getString("inputEtpsSccd"));//信用编码
        ptsEmsHead.setTradeCode(nptsEmlHead.getString("bizopEtpsno"));//经营单位编码
        ptsEmsHead.setTradeName(nptsEmlHead.getString("bizopEtpsNm"));//经营单位名称
        ptsEmsHead.setTradeSccd(nptsEmlHead.getString("bizopEtpsSccd"));//经营单位信用编码
        ptsEmsHead.setCol1(nptsEmlHead.getString("col1"));//重点标识代码
    }

    private void convertPtsEmsAimgZC(List<PtsEmsAimg> ptsEmsImgList,JSONArray nptsEmlImg){
        //循环 nptsEmlImg
        for(int i=0;i<nptsEmlImg.size();i++){
            PtsEmsAimg ptsEmsAimg = new PtsEmsAimg();
            JSONObject jsonObject = nptsEmlImg.getJSONObject(i);
            ptsEmsAimg.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsAimg.setCopGno(jsonObject.getString("gdsmtno"));//货号
            ptsEmsAimg.setModifyMark(jsonObject.getString("adjmtrmarkcd"));//处理标志
            ptsEmsAimg.setGNo(Integer.valueOf(jsonObject.getString("gdsseqno")));//序号
            ptsEmsAimg.setGName(jsonObject.getString("gdsnm"));//商品名称
            ptsEmsAimg.setKeyProductIdentification(jsonObject.getString("gdsSceneUrdfName"));//重点标识
            ptsEmsAimg.setCodet(jsonObject.getString("gdecd"));//税号
            ptsEmsAimg.setUnit1(jsonObject.getString("lawfunitcd"));//法一
            ptsEmsAimg.setUnit2(jsonObject.getString("secdLawfUnitcd"));//法二
            ptsEmsAimg.setCustomsEnforcementMark(jsonObject.getString("cusmexemarkcd"));//海关执行标识
            ptsEmsAimg.setApprAmt(isNotBlank(jsonObject.getString("dclTotalAmt"))?
                    new BigDecimal(jsonObject.getString("dclTotalAmt")): null);//申报总价
            ptsEmsAimg.setModifyFlag(jsonObject.getString("modfmarkcd"));//修改标识
            ptsEmsAimg.setQty(isNotBlank(jsonObject.getString("dclqty"))?
                    new BigDecimal(jsonObject.getString("dclqty")):  null);//备案数量
            ptsEmsAimg.setDecPrice(isNotBlank(jsonObject.getString("dcluprcamt"))?
                    new BigDecimal(jsonObject.getString("dcluprcamt")):  null);//申报单价
            ptsEmsAimg.setCurr(jsonObject.getString("dclCurrcd"));//币制
            ptsEmsAimg.setUnit(jsonObject.getString("dclunitcd"));//单位
            ptsEmsAimg.setDutyMode(jsonObject.getString("lvyrlfmodecd"));
            ptsEmsAimg.setCountryCode(jsonObject.getString("natcd"));//产销国
            ptsEmsAimg.setGModel(jsonObject.getString("endprdgdsspcfmodeldesc"));//规格型号
            ptsEmsAimg.setCol1(jsonObject.getString("param1"));//来源标识
            ptsEmsImgList.add(ptsEmsAimg);
        }

    }
    private void convertPtsEmsAimgWL(List<PtsEmsAimg> ptsEmsImgList,JSONArray nptsEmlImg){
        //循环 nptsEmlImg
        for(int i=0;i<nptsEmlImg.size();i++){
            PtsEmsAimg ptsEmsAimg = new PtsEmsAimg();
            JSONObject jsonObject = nptsEmlImg.getJSONObject(i);
//            ptsEmsAimg.setCount2()
            ptsEmsAimg.setCopGno(jsonObject.getString("gdsMtno"));//货号
            ptsEmsAimg.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsAimg.setCurr(jsonObject.getString("dclCurrCd"));//币制
            ptsEmsAimg.setUnit(jsonObject.getString("dclUnitCd"));//单位
            ptsEmsAimg.setGName(jsonObject.getString("gdsNm"));//商品名称
            ptsEmsAimg.setCodet(jsonObject.getString("gdecd"));//税号
            ptsEmsAimg.setCustomsEnforcementMark(jsonObject.getString("cusmExeMarkcd"));//海关执行标识
            ptsEmsAimg.setCountryCode(jsonObject.getString("natCd"));//产销国
            ptsEmsAimg.setGModel(jsonObject.getString("gdsSpcfModelDesc"));//规格型号
            ptsEmsAimg.setDecPrice(isNotBlank(jsonObject.getString("dclUprcAmt"))?
                    new BigDecimal(jsonObject.getString("dclUprcAmt")):  null);//申报单价
            ptsEmsAimg.setGNo(Integer.valueOf(jsonObject.getString("gdsSeqNo")));//序号
            ptsEmsAimg.setUnit1(jsonObject.getString("lawfUnitCd"));//法一
            ptsEmsAimg.setUnit2(jsonObject.getString("secdLawfUnitCd"));//法二
            ptsEmsAimg.setBondInvtNo(jsonObject.getString("invtNo"));
            ptsEmsAimg.setExpiryDate(isNotBlank(jsonObject.getString("limitDate"))?
                    DateUtil.parse(jsonObject.getString("limitDate"),DatePattern.PURE_DATE_PATTERN):null);
            ptsEmsAimg.setBondInvtItem(jsonObject.getString("invtGNo"));
            ptsEmsAimg.setModifyFlag(jsonObject.getString("modfmarkcd"));//修改标识
            ptsEmsAimg.setCol1(jsonObject.getString("param1"));//来源标识
            ptsEmsImgList.add(ptsEmsAimg);
        }

    }

    private void convertPtsEmsAimgSC(List<PtsEmsAimg> ptsEmsImgList,JSONArray nptsEmlImg){
        //循环 nptsEmlImg
        for(int i=0;i<nptsEmlImg.size();i++){
            PtsEmsAimg ptsEmsAimg = new PtsEmsAimg();
            JSONObject jsonObject = nptsEmlImg.getJSONObject(i);
            ptsEmsAimg.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsAimg.setCopGno(jsonObject.getString("gdsMtno"));//货号
            ptsEmsAimg.setModifyMark(jsonObject.getString("adjmtrMarkcd"));//处理标志
            ptsEmsAimg.setGNo(Integer.valueOf(jsonObject.getString("gdsSeqno")));//序号
            ptsEmsAimg.setGName(jsonObject.getString("gdsNm"));//商品名称
            ptsEmsAimg.setKeyProductIdentification(jsonObject.getString("gdsSceneUrdfName"));//重点标识
            ptsEmsAimg.setCodet(jsonObject.getString("gdecd"));//税号
            ptsEmsAimg.setUnit1(jsonObject.getString("lawfUnitcd"));//法一
            ptsEmsAimg.setUnit2(jsonObject.getString("secdLawfUnitcd"));//法二
            ptsEmsAimg.setCustomsEnforcementMark(jsonObject.getString("cusmExeMarkcd"));//海关执行标识
            ptsEmsAimg.setApprAmt(new BigDecimal(jsonObject.getString("dclTotalAmt")));//申报总价
            ptsEmsAimg.setModifyFlag(jsonObject.getString("modfMarkcd"));//修改标识
            ptsEmsAimg.setQty(new BigDecimal(jsonObject.getString("dclQty")));//备案数量
            ptsEmsAimg.setDecPrice(new BigDecimal(jsonObject.getString("dclUprcAmt")));//申报单价
            ptsEmsAimg.setCurr(jsonObject.getString("dclCurrcd"));//币制
            ptsEmsAimg.setUnit(jsonObject.getString("dclUnitcd"));//单位
            ptsEmsAimg.setDutyMode(jsonObject.getString("lvyrlfModecd"));
            ptsEmsAimg.setCountryCode(jsonObject.getString("natcd"));//产销国
            ptsEmsAimg.setGModel(jsonObject.getString("endprdGdsSpcfModelDesc"));//规格型号
            ptsEmsAimg.setCol1(jsonObject.getString("sourceMarkcd"));//来源标识
            ptsEmsImgList.add(ptsEmsAimg);
        }
    }

    private void convertPtsEmsAexg(List<PtsEmsAexg> ptsEmsAexgList,JSONArray nptsEmlExg){
        for(int i=0;i<nptsEmlExg.size();i++){
            PtsEmsAexg ptsEmsAexg = new PtsEmsAexg();
            JSONObject jsonObject = nptsEmlExg.getJSONObject(i);
            ptsEmsAexg.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsAexg.setCopGno(jsonObject.getString("gdsmtno"));//货号
            ptsEmsAexg.setGNo(Integer.valueOf(jsonObject.getString("gdsseqno")));//序号
            ptsEmsAexg.setGName(jsonObject.getString("gdsnm"));//商品名称
            ptsEmsAexg.setKeyProductIdentification(jsonObject.getString("gdsSceneUrdfName"));//重点标识
            ptsEmsAexg.setCodet(jsonObject.getString("gdecd"));//税号
            ptsEmsAexg.setUnit1(jsonObject.getString("lawfunitcd"));//法一
            ptsEmsAexg.setUnit2(jsonObject.getString("secdLawfUnitcd"));//法二
            ptsEmsAexg.setCustomsEnforcementMark(jsonObject.getString("cusmexemarkcd"));//海关执行标识
            ptsEmsAexg.setApprAmt(isNotBlank(jsonObject.getString("dclTotalAmt"))?
                    new BigDecimal(jsonObject.getString("dclTotalAmt")): null);//申报总价
            ptsEmsAexg.setModifyFlag(jsonObject.getString("modfmarkcd"));//修改标识
            ptsEmsAexg.setQty(isNotBlank(jsonObject.getString("dclqty"))?
                    new BigDecimal(jsonObject.getString("dclqty")):  null);//备案数量
            ptsEmsAexg.setDecPrice(isNotBlank(jsonObject.getString("dcluprcamt"))?
                    new BigDecimal(jsonObject.getString("dcluprcamt")):  null);//申报单价
            ptsEmsAexg.setCurr(jsonObject.getString("dclCurrcd"));//币制
            ptsEmsAexg.setUnit(jsonObject.getString("dclunitcd"));//单位
            ptsEmsAexg.setDutyMode(jsonObject.getString("lvyrlfmodecd"));//征免方式
            ptsEmsAexg.setCountryCode(jsonObject.getString("natcd"));//产销国
            ptsEmsAexg.setGModel(jsonObject.getString("endprdgdsspcfmodeldesc"));//规格型号
            ptsEmsAexg.setUnitConsumptionQueryFlag(jsonObject.getString("ucnstqsnflag"));//单耗质疑标志
            ptsEmsAexg.setNegotiationSymbol(jsonObject.getString("csttnflag"));//磋商标志
            ptsEmsAexgList.add(ptsEmsAexg);
        }
    }

    private void convertPtsEmsAexgSC(List<PtsEmsAexg> ptsEmsAexgList,JSONArray nptsEmlExg){
        for(int i=0;i<nptsEmlExg.size();i++){
            PtsEmsAexg ptsEmsAexg = new PtsEmsAexg();
            JSONObject jsonObject = nptsEmlExg.getJSONObject(i);
            ptsEmsAexg.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsAexg.setCopGno(jsonObject.getString("gdsMtno"));//货号
            ptsEmsAexg.setGNo(Integer.valueOf(jsonObject.getString("gdsSeqno")));//序号
            ptsEmsAexg.setGName(jsonObject.getString("gdsNm"));//商品名称
            ptsEmsAexg.setKeyProductIdentification(jsonObject.getString("gdsSceneUrdfName"));//重点标识
            ptsEmsAexg.setCodet(jsonObject.getString("gdecd"));//税号
            ptsEmsAexg.setUnit1(jsonObject.getString("lawfUnitcd"));//法一
            ptsEmsAexg.setUnit2(jsonObject.getString("secdLawfUnitcd"));//法二
            ptsEmsAexg.setCustomsEnforcementMark(jsonObject.getString("cusmExeMarkcd"));//海关执行标识
            ptsEmsAexg.setApprAmt(new BigDecimal(jsonObject.getString("dclTotalAmt")));//申报总价
            ptsEmsAexg.setModifyFlag(jsonObject.getString("modfMarkcd"));//修改标识
            ptsEmsAexg.setQty(new BigDecimal(jsonObject.getString("dclQty")));//备案数量
            ptsEmsAexg.setDecPrice(new BigDecimal(jsonObject.getString("dclUprcAmt")));//申报单价
            ptsEmsAexg.setCurr(jsonObject.getString("dclCurrcd"));//币制
            ptsEmsAexg.setUnit(jsonObject.getString("dclUnitcd"));//单位
            ptsEmsAexg.setDutyMode(jsonObject.getString("lvyrlfModecd"));//征免方式
            ptsEmsAexg.setCountryCode(jsonObject.getString("natcd"));//产销国
            ptsEmsAexg.setGModel(jsonObject.getString("endprdGdsSpcfModelDesc"));//规格型号
            ptsEmsAexg.setUnitConsumptionQueryFlag(jsonObject.getString("ucnsTqsnFlag"));//单耗质疑标志
            ptsEmsAexg.setNegotiationSymbol(jsonObject.getString("csttnFlag"));//磋商标志
            ptsEmsAexgList.add(ptsEmsAexg);
        }
    }

    private void convertPtsEmsCm(List<PtsEmsCm> ptsEmsCmList,JSONArray nptsEmlConsume){
        for(int i=0;i<nptsEmlConsume.size();i++){
            PtsEmsCm ptsEmsCm = new PtsEmsCm();
            JSONObject jsonObject = nptsEmlConsume.getJSONObject(i);
            ptsEmsCm.setGNo(Integer.valueOf(jsonObject.getString("gseqNo")));//序号
            ptsEmsCm.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsCm.setUcnsverno(jsonObject.getString("ucnsVerno"));//单耗版本号
            ptsEmsCm.setUnitConsumptionStatus(jsonObject.getString("ucnsDclStucd"));//申报状态
            ptsEmsCm.setModifyFlag(jsonObject.getString("modfMarkcd"));//修改标识
            ptsEmsCm.setImgNo(Integer.valueOf(jsonObject.getString("mtpckSeqno")));//料件序号
            ptsEmsCm.setImgCopGno(jsonObject.getString("mtpckGdsMtno"));//料件料号
            ptsEmsCm.setImgHscode(jsonObject.getString("mtpckGdecd"));//料件hsCode
            ptsEmsCm.setImgGname(jsonObject.getString("mtpckGdsNm"));//料件名称
            ptsEmsCm.setDecDm(new BigDecimal(jsonObject.getString("tgblLossRate")));//损耗率
            ptsEmsCm.setExgNo(Integer.valueOf(jsonObject.getString("endprdSeqno")));//成品序号
            ptsEmsCm.setExgCopGno(jsonObject.getString("endprdGdsMtno"));//成品料号
            ptsEmsCm.setExgHscode(jsonObject.getString("endprdGdecd"));//成品hsCode
            ptsEmsCm.setExgGname(jsonObject.getString("endprdGdsNm"));//成品名称
            ptsEmsCm.setEnterpriseExecutionFlag(jsonObject.getString("etpsExeMarkcd"));//执行标志
            ptsEmsCm.setIntangibleLossRate(new BigDecimal(jsonObject.getString("intgbLossRate")));//无形损耗率
            ptsEmsCm.setDecCm(new BigDecimal(jsonObject.getString("netUseupQty")));//净耗
            ptsEmsCm.setProportionOfBondedMaterials(new BigDecimal(jsonObject.getString("bondMtpckPrpr")));//保税料件比例%
            ptsEmsCmList.add(ptsEmsCm);
        }

    }

    private void convertPtsEmsCmZC(List<PtsEmsCm> ptsEmsCmList,JSONArray nptsEmlConsume){
        for(int i=0;i<nptsEmlConsume.size();i++){
            PtsEmsCm ptsEmsCm = new PtsEmsCm();
            JSONObject jsonObject = nptsEmlConsume.getJSONObject(i);
            ptsEmsCm.setSeqNo(jsonObject.getString("seqNo"));//seqNo
            ptsEmsCm.setUcnsverno(jsonObject.getString("ucnsVerno"));//单耗版本号
            ptsEmsCm.setUnitConsumptionStatus(jsonObject.getString("ucnsDclStucd"));//申报状态
            ptsEmsCm.setModifyFlag(jsonObject.getString("modfMarkcd"));//修改标识
            ptsEmsCm.setImgNo(Integer.valueOf(jsonObject.getString("mtpckSeqno")));//料件序号
            ptsEmsCm.setDecDm(new BigDecimal(jsonObject.getString("tgblLossRate")));//损耗率
            ptsEmsCm.setExgNo(Integer.valueOf(jsonObject.getString("endprdSeqno")));//成品序号
            ptsEmsCm.setEnterpriseExecutionFlag(jsonObject.getString("etpsExeMarkcd"));//执行标志
            ptsEmsCm.setIntangibleLossRate(new BigDecimal(jsonObject.getString("intgbLossRate")));//无形损耗率
            ptsEmsCm.setDecCm(new BigDecimal(jsonObject.getString("netUseupQty")));//净耗
            ptsEmsCm.setProportionOfBondedMaterials(new BigDecimal(jsonObject.getString("bondMtpckPrpr")));//保税料件比例%
            ptsEmsCmList.add(ptsEmsCm);
        }
    }

    @Override
    public Result<?> updatePtsEmsByReceipt(PtsEmsHead ptsEmsHead, String status) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        //暂时根据企业内编号 更新状态，账册号等 @TODO
        super.update(ptsEmsHead,new LambdaUpdateWrapper<PtsEmsHead>()
                .set(PtsEmsHead::getStatus,status)
        .set(isNotBlank(ptsEmsHead.getEmsNo()),PtsEmsHead::getEmsNo,ptsEmsHead.getEmsNo())
        .eq(PtsEmsHead::getCopEmsNo,ptsEmsHead.getCopEmsNo()));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("更新成功");
    }
}
