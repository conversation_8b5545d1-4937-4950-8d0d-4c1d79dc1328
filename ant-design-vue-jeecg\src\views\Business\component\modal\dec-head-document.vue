<template>
<!--随附单据-->
<a-modal :centered="true"
         :visible="showModal"
         :maskClosable= "maskClosable"
         @cancel="showModal = !showModal"
         cancel-text=''
         title="编辑随附单据信息"
         width="1000px">
    <a-card :bordered="false" :bodyStyle="{padding:'0px'}">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
            <a-form-model  ref="ruleForm"
                           :model="record" layout="inline" @keyup.enter.native="searchQuery" :rules="rules">
                <a-row :gutter="24">
                    <a-col :xl="12" :lg="14" :md="16" :sm="24">
                        <a-form-model-item required prop="attachmentType" label="随附单据文件类别">
                            <DictSelect
                                :dictType = 'false'
                                style="max-width: 260px"
                                v-model="record.attachmentType"
                                dictKey="SFDZWJLX"
                                placeholder="请输入随附单据文件类别"
                            ></DictSelect>
                        </a-form-model-item>
                    </a-col>
                    <a-col :xl="12" :lg="14" :md="16" :sm="24">
                        <a-form-model-item label="随附单据编号">
                            <a-input @pressEnter="attachmentNoEnter" style="width:78%" placeholder="请输入随附单据编号" v-model="record.attachmentNo"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :xl="12" :lg="14" :md="16" :sm="24">
                        <a-form-model-item label="随附单据文件 ">
                                <a-button   @click="handleTabListAdd2" style="width:82%"> <a-icon type="upload" /> 随附单据文件 </a-button>
                        </a-form-model-item>
                    </a-col>

<!--                    <a-col :xl="12" :lg="14" :md="16" :sm="24">-->

<!--                            <a-form-model-item label="标记唛码附件">-->
<!--                                <a-button  style="width:78%"> <a-icon type="upload" /> 标记唛码附件 </a-button>-->
<!--&lt;!&ndash;                                    <a-button @click="handleTabListAdd2" style="width:78%"> <a-icon type="upload" /> 标记唛码附件 </a-button>&ndash;&gt;-->
<!--                            </a-form-model-item>-->

<!--                    </a-col>-->
                    <a-col :xl="12" :lg="14" :md="16" :sm="24">
                            <a-form-model-item label="商品项号关系">
                                <a-button   @click="choiseDecList" style="width:85%">商品项号关系 </a-button>
                            </a-form-model-item>

                    </a-col>
                </a-row>
            </a-form-model>
            <document-uploading ref="modalForm" :cid="String(this.record.id)" :type="String(this.attachmenttypes)" @getDataSource="getDataSource" ></document-uploading>
            <choise-dec-list ref="choiseDecListRef" @choiseDecListAfter = "choiseDecListAfter"></choise-dec-list>
        </div>

        <div>
            <!--列表-->
            <a-table
                :bordered="true"
                :columns="columns"
                :dataSource="dataSource"
                :loading="loading"
                :pagination="false"
                :rowSelection="{type:'checkbox', selectedRowKeys: selectedRowKeys,onChange:handleTableSelectionChange}"
                :scroll="{ x:false, y: 280,scrollToFirstRowOnChange:true,}"
                rowKey="rowIndex">
                <span slot="action" slot-scope="text, record">
<!--                    <a @click="handleEdit(record)">预览</a><a-divider type="vertical" />-->
                    <a v-if='!!record.attachmentName' @click="downloadEdit(record)">下载</a><a-divider type="vertical" />
                    <a @click="deleteEdit(record)" v-if="saveShow">删除</a>
                </span>
            </a-table>
        </div>

    </a-card>
    <template slot="footer">
        <a-button type="primary" @click="handleCancel" size="small">取消</a-button>
        <a-button type="primary" v-if="saveShow" size="small" @click="handleOk">确认</a-button>
    </template>


</a-modal>
</template>

<script>

import {getAction, postAction,getFile} from '@/api/manage'
import DictSelect from '@views/_components/dict-select'
import DocumentUploading from '@views/Business/component/modal/Document-Uploading'
import {saveDecDocument,deleteDecDocument,getDecDocument}    from "@/api/dec/dec"
import decSelectItem from '@views/Business/component/m-table-select-item-fuben'
import {axios} from '@/utils/request'
import ChoiseDecList from "@/views/Business/component/modal/ChoiseDecList";
import {forEach} from "vxe-table/lib/index.common";

export default {
    name: 'dec-goods-limittype',
    components: {ChoiseDecList, DictSelect,DocumentUploading,decSelectItem},
    data() {
        return {

            formItem: {
                labelCol: {
                    span: 5,
                },
                wrapperCol: {
                    span: 19,
                },
            },
            advanced: true,

            record: {
                id:'',
                attachmentType:'',
                attachmentNo:'',
                attachmentName:'',
                gNoStr:'',
            },
            attachmenttypes:'DEC',
            // 已选的键
            selectedRowKeys: [],
            selectionRows:[],
            // 已选的内容
            selectedRows: [],
            // value副本
            val: {},
            recordVal   : {},
            // show副本
            showModal: false,
            dataSource: [],
            rules: {
                attachmentType: [{ required: true, message: '请选择随附单据文件类别!', trigger: 'change' }],
            },
            columns: [
              {
                    title: '序号',
                    key: 'rowIndex',
                    width: 80,
                    align: 'center',
                    customRender: (t, r, i) => 1 + i,
                },
                {
                    title: '类别',
                    width: 160,
                    dataIndex: 'attachmentType',
                    key: 'attachmentType',
                    ellipsis: true,
                },
                {
                    title: '商品项号关系',
                    width: 160,
                    dataIndex: 'gNoStr',
                    key: 'gNoStr',
                    ellipsis: true,
                },
                {
                    title: '文件名称',
                    dataIndex: 'attachmentName',
                    key: 'attachmentName',
                    ellipsis: true,
                },
                {
                    title: '单据编号',
                    dataIndex: 'attachmentNo',
                    key: 'attachmentNo',
                    ellipsis: true,
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    align: "center",
                    scopedSlots: {
                        customRender: 'action'
                    },
                }
            ],

            loading: false,
            //许可证类别代码
            XKZLBList : [],
            maskClosable:false,
        }
    },
    created() {

    },
    props: {
        value: {
            type: Object,
            require: true,
        },
        show: {
            type: Boolean,
        },
        saveShow: {
            type: Boolean,
        },
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    watch: {
        value: {
            async  handler(val) {
                this.recordVal = val
                if( !!this.recordVal.id){
                    console.log("lalalala=>",this.recordVal)
                    this.initData(this.recordVal)
                }
            },
        },
        val: {
            handler() {

                this.$emit('change', this.val)
            },
        },
        show: {
            handler(val) {

                this.showModal = !this.showModal
            },
        },


    },
    methods: {
        async initData(recordVal) {
          console.log("initDataTTTTTTTTTTTT", recordVal)
            const  auditRess = await getDecDocument(recordVal.id).catch(reason => this.$message.error(reason));
            if (auditRess.success) {
                this.dataSource = auditRess.result
                console.log( this.dataSource )
            } else {
                this.$message.error(auditRess.message)
            }
        },
        /**
         * 取消修改
         */
        handleCancel() {
            this.showModal = !this.showModal
        },
        /**
         * 确认修改
         */
        async handleOk() {
          console.log("handleOkTTTTTTTTTTTT", this.dataSource)

          for (let i = 0; i < this.dataSource.length; i++) {
            // this.dataSource[i].path =null
						const  auditRess = await saveDecDocument(this.dataSource[i]).catch(reason => this.$message.error(reason));
						if (auditRess.success) {

						} else {
							this.$message.error(auditRess.message)
						}
          }
					this.$message.success('保存成功')

            this.showModal = !this.showModal
        },
        //展开关闭高级按钮
        toggleAdvanced () {
            this.advanced = !this.advanced
        },

        /**
         * 列选择变更
         * @param selectedRowKeys 选中列的rowKey
         */
        handleTableSelectionChange(selectedRowKeys,selectionRows) {

            this.selectedRowKeys = selectedRowKeys
            this.selectionRows = selectionRows
        },

        // // 单击行默认选中
        // onClickRow(record) {
        //     return {
        //         on: {
        //             click: () => {
        //                 record
        //             },
        //             dblclick: () => {
        //
        //                 console.log('双击')
        //             }
        //         }
        //     }
        // },

        handleChange(){

        },
        //预览
        handleEdit(){

        },
        //下载
			async downloadEdit(record){
            if(!!record.path){
							//截取path里 字符trade-service-platform后面的内容
							const parts = record.path.split('trade-service-platform');
							const contentAfter = parts[1];

							const res = await getAction(window._CONFIG['staticMinioURL'] + contentAfter)
							let url = res.result
							console.log(url)
							window.open(url)
                // let sd  = record.path.split("/Filedata")
                // let urlName = ''
                // if(record.path.indexOf("http")>-1){
                //     urlName = record.path
                // }else{
                //     urlName = window._CONFIG['staticDomainURL'] + '/' +record.path
                // }
                // console.log("随附单据文件下载地址==》",urlName)
                // window.open(urlName,"_blank")

						}

        },
        //删除
        async deleteEdit(obj){
          console.log("dec-head-document.deleteEdit",obj);
					if(!obj.id){
						//筛选出this.dataSource中的path 不是obj.path的数据
						this.dataSource = this.dataSource.filter(item => item.path !== obj.path)
						this.$forceUpdate()
					}else {
          const auditRess = await deleteDecDocument(obj.id).catch(reason => this.$message.error(reason));
          if (auditRess.success) {
            this.initData(this.recordVal)
            this.$message.success('删除成功')
          } else {
            this.$message.error(auditRess.message)
         	 }
					}
        },
        handleTabListAdd2(){
            this.$refs.modalForm.add()
            this.$refs.modalForm.title = '新增'
            this.$refs.modalForm.disableSubmit = false
        },
        //保存暂时有问题
        async  getDataSource(data){
           console.log(data)

            if (data.code != '0'){
                this.$message.warning(data.status.substring(6,data.status.length))
                return;
            }
            console.log("getDataSource.record",this.record)
            console.log("getDataSource.record.attachmentType",this.record.attachmentType)
          let decAttachmentDate = {};
          decAttachmentDate.attachmentType = this.record.attachmentType
          decAttachmentDate.attachmentNo = this.record.attachmentNo
          decAttachmentDate.dclId = this.recordVal.id
          decAttachmentDate.relatedId = this.recordVal.id
          decAttachmentDate.gNoStr = this.record.gNoStr
            // let index = data.url.indexOf('/');
            // let urlList = data.url.split('15550')

          decAttachmentDate.path = data.message
					console.log(decAttachmentDate.path)
					let name = ''
					if (data.message.includes("http://")) {
						name = data.message.substring(data.message.lastIndexOf("/") + 1);
					} else {
						name = data.message.split('/')[1]
					}
					console.log('name')
					console.log(name)
					console.log('name')
          decAttachmentDate.attachmentName = name.substring(0,name.lastIndexOf("_"))+'.'+name.split(".")[1]
          decAttachmentDate.dclType = "DEC"
          if (this.dataSource.length>0){
            for(let att of this.dataSource){
              if (decAttachmentDate.path != att.path){//decAttachmentDate.attachmentType != att.attachmentType &&

                console.log("有数据==》",decAttachmentDate)
                this.dataSource.push( decAttachmentDate)
								break
              }
            }
          }else {
            console.log("无数据==》",decAttachmentDate)
            this.dataSource.push( decAttachmentDate)
          }
          this.$refs.modalForm.visible = false
          console.log("添加后随附单据集合信息==》",this.dataSource)

        },
        //编号回车操作
        attachmentNoEnter(){
            console.log( this.dataSource)
            if(this.record.attachmentType==undefined||this.record.attachmentType==null||this.record.attachmentType==''){
                this.$message.error("请选择随附单据文件类别")
                return
            }
            if(this.record.attachmentType=='10000001'){
                for(let i=0;i<this.dataSource.length;i++){
                    if(this.dataSource[i].attachmentType=='10000001'){
                        this.$message.error("已经存在类别为代理报关委托协议(电子)类型")
                        return
                    }
                }
            }
           //this.$refs.modalForm.attachmentNoEnter();
          let data={};
          data.attachmentType=this.record.attachmentType;
          data.attachmentNo=this.record.attachmentNo;
          data.dclId=this.recordVal.id;
          data.relatedId= this.recordVal.id;
          data.gNoStr= this.record.gNoStr;
          data.dclType = "DEC"

            this.dataSource.push(data);
        },
        choiseDecList(){
            if (!this.record.attachmentType){
                this.$message.warning("请先选择随附单据文件类型")
                return;
            }
            let dateList = [];
            console.log("choiseDecList.recordVal.decLists=>",this.recordVal.decLists)
            console.log("choiseDecList.record=>",this.record)

            for (let decList of this.recordVal.decLists){
                if (decList.goodsAttr){
                    if (this.record.attachmentType="80000001"){
                        if (decList.goodsAttr.indexOf("31") !=-1 ||decList.goodsAttr.indexOf("32") !=-1){
                            dateList.push(decList);
                        }
                    }else  if (this.record.attachmentType="80000003"){
                        if (decList.goodsAttr.indexOf("32") !=-1){
                            dateList.push(decList);
                        }
                    }else  if (this.record.attachmentType="80000004"){
                        if (decList.goodsAttr.indexOf("31") !=-1 ||decList.goodsAttr.indexOf("32") !=-1){
                            dateList.push(decList);
                        }
                    }
                }
            }
            if (dateList.length>0){
                this.$refs.choiseDecListRef.choiseDecListVisble = !this.$refs.choiseDecListRef.choiseDecListVisble;
                this.$refs.choiseDecListRef.dataSource = dateList;
                this.$refs.choiseDecListRef.selectionRowsKeys=[]
                this.$refs.choiseDecListRef.selectionRows=[]
                // this.$forceUpdate()
                // this.$refs.choiseDecListRef.$refs.xGrid.refreshColumn()


            }else {
                this.$message.warning("报关单中未发现符合条件的表体")
            }
        },
        choiseDecListAfter(gnoStrs){

            this.record.gNoStr = gnoStrs;
            console.log( this.record)
            console.log("gnoStrs",gnoStrs)
            this.$refs.choiseDecListRef.choiseDecListVisble = false;
        },
    },
}
</script>
