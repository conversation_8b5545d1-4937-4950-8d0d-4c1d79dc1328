// 动态加载工具 - 用于按需加载非关键组件
import Vue from 'vue'

// 缓存已加载的组件
const loadedComponents = new Map()

/**
 * 动态加载并注册Vue组件
 * @param {string} name 组件名称
 * @param {Function} loader 组件加载函数
 * @returns {Promise} 返回加载的组件
 */
export const loadComponent = async (name, loader) => {
	// 如果已经加载过，直接返回
	if (loadedComponents.has(name)) {
		return loadedComponents.get(name)
	}

	try {
		console.log(`正在加载组件: ${name}`)
		const component = await loader()
		const componentToUse = component.default || component

		// 注册到Vue
		Vue.use(componentToUse)

		// 缓存组件
		loadedComponents.set(name, componentToUse)

		console.log(`组件 ${name} 加载完成`)
		return componentToUse
	} catch (error) {
		console.error(`加载组件 ${name} 失败:`, error)
		throw error
	}
}

/**
 * 预定义的组件加载器
 */
export const componentLoaders = {
	// 打印组件
	Print: () => import('vue-print-nb-jeecg'),

	// 图片预览组件
	preview: () => import('vue-photo-preview'),

	// 图片查看器
	Viewer: () => import('v-viewer'),

	// Echarts相关组件（按需加载）
	echarts: () => import('echarts'),

	// TinyMCE编辑器
	tinymce: () => import('tinymce'),

	// CodeMirror编辑器
	codemirror: () => import('codemirror')
}

/**
 * 批量加载组件
 * @param {Array} componentNames 组件名称数组
 * @returns {Promise} 返回所有组件加载完成的Promise
 */
export const loadComponents = async componentNames => {
	const promises = componentNames.map(name => {
		if (componentLoaders[name]) {
			return loadComponent(name, componentLoaders[name])
		} else {
			console.warn(`未找到组件加载器: ${name}`)
			return Promise.resolve(null)
		}
	})

	return Promise.all(promises)
}

/**
 * 预加载关键组件（在空闲时间加载）
 */
export const preloadCriticalComponents = () => {
	if (window.requestIdleCallback) {
		window.requestIdleCallback(() => {
			// 在浏览器空闲时预加载一些可能用到的组件
			loadComponents(['Print', 'preview'])
		})
	} else {
		// 降级方案：延迟加载
		setTimeout(() => {
			loadComponents(['Print', 'preview'])
		}, 3000)
	}
}

/**
 * 检查组件是否已加载
 * @param {string} name 组件名称
 * @returns {boolean} 是否已加载
 */
export const isComponentLoaded = name => {
	return loadedComponents.has(name)
}

/**
 * 获取已加载的组件
 * @param {string} name 组件名称
 * @returns {Object|null} 组件对象或null
 */
export const getLoadedComponent = name => {
	return loadedComponents.get(name) || null
}

// 导出默认对象
export default {
	loadComponent,
	loadComponents,
	preloadCriticalComponents,
	isComponentLoaded,
	getLoadedComponent,
	componentLoaders
}
