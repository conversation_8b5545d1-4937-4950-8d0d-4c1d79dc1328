<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <!-- <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="费用类别">
              <j-dict-select-tag v-model="queryParam.costCategory" :allowClear="true" placeholder="请选择费用类别"
                dictCode="cost_category" />
            </a-form-item>
          </a-col> -->
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="费用项目名称">
              <j-input placeholder="请输入费用项目名称" v-model="queryParam.feeName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- <a-button type="primary" icon="download" @click="handleExportXls('费用项目表')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="small" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap"
        @change="handleTableChange">

        <span slot="disableFlag" slot-scope="text, record" :title="record.disableFlag">
          <a-tag v-if="record.disableFlag == 1" color="red">停用</a-tag>
          <a-tag v-else color="green">启用</a-tag>
        </span>
        <span slot="action" slot-scope="text, record">
          <!-- <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" /> -->
          <a-dropdown>
            <a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
            <!-- <a class="ant-dropdown-link">更多 <a-icon type="down" /></a> -->
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm v-if="record.disableFlag == 1" title="确定启用吗?"
                  @confirm="() => handleDisabled(record,0)">启用</a-popconfirm>
                <a-popconfirm v-else title="确定停用吗?" @confirm="() => handleDisabled(record,1)">停用</a-popconfirm>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <fee-item-modal ref="modalForm" @ok="modalFormOk"></fee-item-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import FeeItemModal from './modules/FeeItemModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import { _postAction } from '@api/manage'

export default {
  name: 'FeeItemList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    FeeItemModal,
  },
  data() {
    return {
      description: '费用项目表管理页面',
      // 表头
      columns: [
        {
          title: '费用项目名称',
          align: 'left',
          dataIndex: 'feeName',
        },
        // {
        //   title: '费用类别',
        //   align: 'center',
        //   dataIndex: 'costCategory_dictText',
        // },
        // {
        //   title: '付报关行',
        //   align: 'center',
        //   dataIndex: 'feeToBroker_dictText',
        //   sorter: false,
        // },
        // {
        //   title: '付货代公司',
        //   align: 'center',
        //   dataIndex: 'feeToLogistics_dictText',
        //   sorter: false,
        // },
        // {
        //   title: '付平台',
        //   align: 'center',
        //   dataIndex: 'feeToPlatform_dictText',
        //   sorter: false,
        // },
        // {
        //   title: '付其他',
        //   align: 'center',
        //   dataIndex: 'feeToOthers_dictText',
        //   sorter: false,
        // },
        {
          title: '备注',
          align: 'center',
          dataIndex: 'remarks',
          sorter: false,
        },
        {
          title: '费用状态',
          align: 'center',
          dataIndex: 'disableFlag',
          scopedSlots: { customRender: 'disableFlag' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 20,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/fee/feeItem/list',
        delete: '/fee/feeItem/delete',
        deleteBatch: '/fee/feeItem/deleteBatch',
        exportXlsUrl: '/fee/feeItem/exportXls',
        importExcelUrl: 'fee/feeItem/importExcel',
        setDisabled: '/fee/feeItem/disabled'
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    handleDisabled(record, type) {
      _postAction(this.url.setDisabled, {
        id: record.id,
        type: type
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || res)
          this.loadData()
        } else {
          this.$message.warning(res.message || res)
        }
      })
        .finally(() => {
          this.handleEmptyIcon(params.pageSize)
          this.loading = false
        })
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'feeName', text: '费用项目名称', dictCode: '' })
      fieldList.push({ type: 'int', value: 'feeToBroker', text: '付报关行', dictCode: 'fee_to_broker' })
      fieldList.push({ type: 'int', value: 'feeToLogistics', text: '付货代公司', dictCode: 'fee_to_logistics' })
      fieldList.push({ type: 'int', value: 'feeToPlatform', text: '付平台', dictCode: 'fee_to_platform' })
      fieldList.push({ type: 'string', value: 'remarks', text: '备注', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>