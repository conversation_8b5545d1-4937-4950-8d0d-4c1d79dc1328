package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.NemsInvtHeadDTO;
import org.jeecg.modules.business.entity.dto.StatisticiansDTO;
import org.jeecg.modules.business.entity.enums.OptTypeEnum;
import org.jeecg.modules.business.entity.excel.NemsInvtHeadEntity;
import org.jeecg.modules.business.entity.excel.NemsInvtListEntity;
import org.jeecg.modules.business.entity.paramVo.StockParamVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.DateRangeSplitter;
import org.jeecg.modules.business.util.excel.ExcelImportResult;
import org.jeecg.modules.business.util.excel.ExcelImportUtil;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.*;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.common.constant.CommonConstant.SFTP;
import static org.jeecg.modules.business.service.impl.BasicServiceImpl.isBSWL;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;
import static org.jeecg.modules.business.util.DateRangeSplitter.splitDateRange;
import static org.jeecg.modules.business.util.JsonBackupService.saveJsonToFile;
import static org.jeecg.modules.business.util.StockUtil.getGoodsText;
import static org.jeecg.modules.business.util.excel.ExcelImportUtil.*;

/**
 * <p>
 * 核注清单表头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Slf4j
@Service
public class NemsInvtHeadServiceImpl extends ServiceImpl<NemsInvtHeadMapper, NemsInvtHead> implements INemsInvtHeadService {
    private static final String URL_GET_INVT_LIST = "https://api.jgsoft.com.cn:15555/open-api/sm/GetInvtList"; // 报关单列表
    private static final String URL_GET_INVT_DATA = "https://api.jgsoft.com.cn:15555/open-api/sm/GetInvtData"; // 报关单列表
    // 设置个限流的令牌桶
    private static final RateLimiter rateLimiter = RateLimiter.create(1.0); // 每秒1个令牌
    @Autowired
    private NemsInvtListMapper nemsInvtListMapper;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private PtsEmsHeadMapper emsHeadMapper;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private PtsEmsAexgMapper emsAexgMapper;
    @Autowired
    private IDecHeadService decHeadService;
    @Autowired
    private INemsInvtListService nemsInvtListService;
    @Autowired
    private IEmsStockService emsStockService;
    @Autowired
    private IEmsStocksFlowService emsStocksFlowService;
    @Autowired
    private StorageDetailMapper storageDetailMapper;
    @Autowired
    private StorageInfoMapper storageInfoMapper;
    @Autowired
    private IStorageInfoService storageInfoService;
    @Autowired
    private CustomerEnterpriseMapper customerEnterpriseMapper;
    @Autowired
    private IDecListService decListService;
    @Autowired
    private DecLicenseDocusMapper decLicenseDocusMapper;
    @Autowired
    private PassPortHeadMapper passPortHeadMapper;
    @Autowired
    private StockHeadTypeMapper stockHeadTypeMapper;
    @Autowired
    private StockGoodsTypeMapper stockGoodsTypeMapper;
    @Autowired
    private ICommissionerService commissionerService;
    @Autowired
    private ISyncNemsInvtHeadService syncNemsInvtHeadService;
    @Autowired
    private SyncNemsInvtHeadMapper syncNemsInvtHeadMapper;
    @Autowired
    private ISyncNemsInvtListService syncNemsInvtListService;
    @Autowired
    private IDockingEasyPassService dockingEasyPassService;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private ErpCurrenciesMapper erpCurrenciesMapper;
    @Autowired
    private ErpCountriesMapper erpCountriesMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private IPtsEmsHeadService ptsEmsHeadService;
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendInvtPath}")
//    private String remoteSendInvtPath;
    @Autowired
    private FtpProperties ftpProperties;

    /**
     * 分页列表查询
     *
     * @param page
     * @param nemsInvtHeadDTO
     * @param request
     * @return
     */
    @Override
    public IPage<NemsInvtHead> queryPageList(Page<NemsInvtHead> page, NemsInvtHeadDTO nemsInvtHeadDTO, HttpServletRequest request) {
        /*
         * 企业数据隔离
         * 2024/8/21 下午3:46@ZHANGCHAO
         */
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
            Commissioner commissioner = commissionerService.getById(loginUser.getUserTypeRel());
            if (isNotEmpty(commissioner)) {
                nemsInvtHeadDTO.setBizopEtpsno(commissioner.getDepartcd());
            }
        }
        nemsInvtHeadDTO.setTenantId(Long.valueOf(TenantContext.getTenant()));
        IPage<NemsInvtHead> nemsInvtHeadIPage = baseMapper.queryPageList(page, nemsInvtHeadDTO);
        if (isNotEmpty(nemsInvtHeadIPage.getRecords())) {
            for (NemsInvtHead nemsInvtHead : nemsInvtHeadIPage.getRecords()) {
                List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new LambdaQueryWrapper<NemsInvtList>()
                        .select(NemsInvtList::getStorageNo)
                        .eq(NemsInvtList::getInvId, nemsInvtHead.getId())
                        .ne(NemsInvtList::getStorageNo, "")
                        .isNotNull(NemsInvtList::getStorageNo));
                if (isNotEmpty(nemsInvtLists)) {
                    nemsInvtHead.setHasSC("1");
                    nemsInvtHead.setStorageNos(nemsInvtLists.stream().map(NemsInvtList::getStorageNo).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.joining(",")));
                }
            }
        }
        return nemsInvtHeadIPage;
    }

    /**
     * 根据id获取NemsInvtHead对象
     *
     * @param id NemsInvtHead的id
     * @return NemsInvtHead对象
     */
    @Override
    public NemsInvtHead getInvtById(String id) {
        NemsInvtHead nemsInvtHead = baseMapper.selectById(id);
        if (nemsInvtHead == null) {
            return null;
        }

        // 根据id查询NemsInvtList列表
        List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                .eq(NemsInvtList::getInvId, id)
                .orderByAsc(NemsInvtList::getGdsseqNo));

        // 将NemsInvtList列表设置到NemsInvtHead对象中
        nemsInvtHead.setNemsInvtLists(nemsInvtLists);

        return nemsInvtHead;
    }

    /**
     * 保存核注单
     *
     * @param nemsInvtHead 库存头节点对象
     * @return 保存结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveInvt(NemsInvtHead nemsInvtHead) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isEmpty(nemsInvtHead.getId())) {
            nemsInvtHead.setId(IdWorker.getId());
            if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
            }
            nemsInvtHead.setCreateDate(isNotEmpty(nemsInvtHead.getCreateDate()) ? nemsInvtHead.getCreateDate() : new Date());
            nemsInvtHead.setInputTime(isNotEmpty(nemsInvtHead.getInputTime()) ? nemsInvtHead.getInputTime() : new Date());
            nemsInvtHead.setCreatePerson(isNotBlank(nemsInvtHead.getCreatePerson()) ? nemsInvtHead.getCreatePerson() : loginUser.getUsername());
            nemsInvtHead.setVrfdedMarkcd(isNotBlank(nemsInvtHead.getVrfdedMarkcd()) ? nemsInvtHead.getVrfdedMarkcd() : "0");
            // 默认tenant_id
            nemsInvtHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
            nemsInvtHead.setInputId(Long.valueOf(TenantContext.getTenant()));
            nemsInvtHead.setCreatePassPort(isNotEmpty(nemsInvtHead.getCreatePassPort()));
            nemsInvtHead.setDclTenantId(isNotEmpty(nemsInvtHead.getDclTenantId()) ? nemsInvtHead.getDclTenantId() : Long.valueOf(TenantContext.getTenant()));
            NemsInvtHead ni = this.getOne(new QueryWrapper<NemsInvtHead>().lambda()
                    .eq(NemsInvtHead::getEtpsInnerInvtNo, nemsInvtHead.getEtpsInnerInvtNo()));
            if (ni != null) {
                return Result.error("企业内部编号重复，请重新修改");
            }
            baseMapper.insert(nemsInvtHead);
        } else {
            if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
            }
            NemsInvtHead invt = this.getOne(new QueryWrapper<NemsInvtHead>().lambda()
                    .eq(NemsInvtHead::getId, nemsInvtHead.getId()));
            if ("1".equals(nemsInvtHead.getGenDecFlag()) && (isEmpty(invt.getEtpsInnerInvtNo()) || !invt.getEtpsInnerInvtNo().equals(nemsInvtHead.getEtpsInnerInvtNo()))) {
                //todo 生成报关单！！
                LambdaUpdateWrapper<DecHead> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(DecHead::getInvId, nemsInvtHead.getId()).set(DecHead::getEtpsInnerInvtNo, nemsInvtHead.getEtpsInnerInvtNo());
                decHeadService.update(lambdaUpdateWrapper);
            }
            nemsInvtHead.setUpdateBy(loginUser.getUsername());
            nemsInvtHead.setUpdateDate(new Date());
            nemsInvtHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
            nemsInvtHead.setDclTenantId(isNotEmpty(nemsInvtHead.getDclTenantId()) ? nemsInvtHead.getDclTenantId() : Long.valueOf(TenantContext.getTenant()));
            NemsInvtHead ni = this.getOne(new QueryWrapper<NemsInvtHead>().lambda()
                    .eq(NemsInvtHead::getEtpsInnerInvtNo, nemsInvtHead.getEtpsInnerInvtNo()));
            if (ni != null) {
                if (!ni.getId().equals(nemsInvtHead.getId())) {
                    return Result.error("企业内部编号重复，请重新修改");
                }
            }
            baseMapper.updateById(nemsInvtHead);
        }
//        nemsInvtListMapper.delete(new QueryWrapper<NemsInvtList>().lambda()
//                .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
        if (isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
            List<NemsInvtList> oldInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
            if (isNotEmpty(oldInvtLists)) {
                // 2024/3/13 9:55@ZHANGCHAO 追加/变更/完善：核注单发送报文后不可再修改申报数量！！
                if (isNotEmpty(nemsInvtHead.getSend()) && nemsInvtHead.getSend() && E.equals(nemsInvtHead.getImpexpMarkcd())) {
                    for (NemsInvtList invtList : nemsInvtHead.getNemsInvtLists()) {
                        if (isEmpty(invtList.getId())) {
                            continue;
                        }
                        for (NemsInvtList oldList : oldInvtLists) {
                            if (invtList.getId().equals(oldList.getId())) {
                                BigDecimal oldQty = isNotEmpty(oldList.getDclQty()) ? oldList.getDclQty() : BigDecimal.ZERO;
                                BigDecimal newQty = isNotEmpty(invtList.getDclQty()) ? invtList.getDclQty() : BigDecimal.ZERO;
                                if (oldQty.compareTo(newQty) != 0) {
                                    throw new RuntimeException("核注单已发送报文，无法再修改申报数量！序号[" + invtList.getGdsseqNo() + "]原申报数量[" + oldList.getDclQty() + "]");
                                }
                            }
                        }
                    }
                }
                // 使用 Stream 进行过滤
                List<NemsInvtList> dels = oldInvtLists.stream()
                        .filter(item -> nemsInvtHead.getNemsInvtLists().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList del : dels) {
                        stockParamVO.setNemsInvtList(del);
                        // 解除占用
                        Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
                        if (!deOccResult.isSuccess()) {
                            throw new RuntimeException(deOccResult.getMessage());
                        }
                        nemsInvtListMapper.deleteById(del.getId());
                    }
                }
            }
            Map<Long, String> map = new HashMap<>();
            for (NemsInvtList nemsInvtList : nemsInvtHead.getNemsInvtLists()) {
                if (isEmpty(nemsInvtList.getId())) {
                    nemsInvtList.setInvId(nemsInvtHead.getId());
                    nemsInvtList.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
                    nemsInvtListMapper.insert(nemsInvtList);
                } else {
                    nemsInvtListMapper.updateById(nemsInvtList);
                }
                if (isNotEmpty(nemsInvtList.getStorageDetailId())) {
                    map.put(nemsInvtList.getId(), String.valueOf(nemsInvtList.getStorageDetailId()));
                }
            }
            if (isNotEmpty(map)) {
                // 遍历map
                for (Map.Entry<Long, String> entry : map.entrySet()) {
                    storageDetailMapper.update(null, new UpdateWrapper<StorageDetail>().lambda()
                            .set(StorageDetail::getInvtListId, entry.getKey())
                            .eq(StorageDetail::getId, entry.getValue()));
                }
            }
        } else {
            List<NemsInvtList> oldInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
            if (isNotEmpty(oldInvtLists)) {
                StockParamVO stockParamVO = new StockParamVO();
                stockParamVO.setNemsInvtHead(nemsInvtHead);
                for (NemsInvtList del : oldInvtLists) {
                    stockParamVO.setNemsInvtList(del);
                    // 解除占用
                    Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
                    if (!deOccResult.isSuccess()) {
                        throw new RuntimeException(deOccResult.getMessage());
                    }
                    nemsInvtListMapper.deleteById(del.getId());
                }
            }
        }
        NemsInvtHead invtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda()
                .eq(NemsInvtHead::getId, nemsInvtHead.getId()));
        List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                .eq(NemsInvtList::getInvId, invtHead.getId())
                .orderByAsc(NemsInvtList::getGdsseqNo));
        // 将NemsInvtList列表设置到NemsInvtHead对象中
        invtHead.setNemsInvtLists(nemsInvtLists);
        /*
         * 默认 是否生成报关单为“是”时，保存核注单自动生成关联的进口报关单!!
         * 2024/2/18 10:08@ZHANGCHAO
         */
        if ("1".equals(nemsInvtHead.getGenDecFlag())) {
//            try {
            /*
             * 新：集中申报：列表选择N条出入库单，跳转到核注单编辑页面，出入库单表体放到核注单表体中，
             * 如果选择「是否生成报关单」为是，则根据条件（品名、税号、成交单位、币制、原产）合并报关单表体，超过50项则提示，终止操作。
             * 2024/3/4 11:15@ZHANGCHAO
             */
            if (isNotEmpty(nemsInvtHead.getFromStock()) && nemsInvtHead.getFromStock()) {
                if (isEmpty(nemsInvtHead.getNemsInvtLists())) {
                    throw new RuntimeException("集中申报时，核注单表体不可为空！");
                }
                Map<String, List<NemsInvtList>> groupMap = nemsInvtHead.getNemsInvtLists().stream()
                        .collect(Collectors.groupingBy(i -> i.getHsname() + i.getHscode() + i.getDclUnitcd() + i.getDclCurrcd() + i.getOriginCountry()));
                // 遍历groupMap
                List<NemsInvtList> mergeList = new ArrayList<>();
                AtomicInteger item = new AtomicInteger(1);
                groupMap.forEach((k, v) -> {
                    if (v.size() == 1) {
                        NemsInvtList oldItem = v.get(0);
                        oldItem.setGoodsId(String.valueOf(oldItem.getGdsseqNo()));
                        oldItem.setEntryGdsSeqno(item.get());
                        mergeList.add(oldItem);
                    } else {
                        NemsInvtList newItem = new NemsInvtList();
                        BeanUtil.copyProperties(v.get(0), newItem, CopyOptions.create().ignoreNullValue());
                        List<String> goodsIds = v.stream().map(i -> isNotEmpty(i.getGdsseqNo()) ? i.getGdsseqNo().toString() : "").collect(Collectors.toList());
                        newItem.setGoodsId(CollUtil.join(goodsIds, ","));
                        BigDecimal dclQtyAll = v.stream().map(NemsInvtList::getDclQty).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal secdLawfQtyAll = v.stream().map(NemsInvtList::getSecdLawfQty).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal grossWtAll = v.stream().map(NemsInvtList::getGrossWt).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal netWtAll = v.stream().map(NemsInvtList::getNetWt).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal dclUprcamtAll = v.stream().map(NemsInvtList::getDclUprcamt).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal dclTotalamtAll = v.stream().map(NemsInvtList::getDclTotalamt).filter(ObjectUtil::isNotEmpty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        newItem.setEntryGdsSeqno(item.get());
                        newItem.setDclQty(dclQtyAll);
                        newItem.setSecdLawfQty(secdLawfQtyAll);
                        newItem.setGrossWt(grossWtAll);
                        newItem.setNetWt(netWtAll);
                        newItem.setDclUprcamt(dclUprcamtAll);
                        newItem.setDclTotalamt(dclTotalamtAll);
                        mergeList.add(newItem);
                    }
                    item.getAndIncrement();
                });
                if (mergeList.size() > 50) {
                    throw new RuntimeException("当前合并的报关单表体超过50项，无法继续生成报关单！");
                }
                log.info("归并后的核注单表体数量：" + mergeList.size());
                log.info("归并后的核注单表体：" + JSONObject.toJSONString(mergeList));
                nemsInvtHead.setNemsInvtLists(mergeList);
                this.addDecByInvt(nemsInvtHead);
                stockHeadTypeMapper.update(null, new UpdateWrapper<StockHeadType>().lambda()
                        .set(StockHeadType::getDclcusFlag, "1")
                        .set(StockHeadType::getInvtHeadId, nemsInvtHead.getId())
                        .in(StockHeadType::getId, Arrays.asList(nemsInvtHead.getStockHeadId().split(","))));
                groupMap.forEach((k, v) -> v.forEach(i -> stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                        .set(StockGoodsType::getInvtListId, i.getId())
                        .in(StockGoodsType::getId, Arrays.asList(i.getStockGoodsId().split(","))))));
            } else {
                this.addDecByInvt(nemsInvtHead);
            }
//            } catch (Exception e) {
//                log.error("生成报关单异常：", e.getMessage());
//            }
        }
        return Result.ok(invtHead);
    }

    /**
     * 根据核注单生成报关单
     *
     * @param invtHead
     * @return void
     * <AUTHOR>
     * @date 2024/2/18 10:21
     */
    private void addDecByInvt(NemsInvtHead invtHead) {
        if (isEmpty(invtHead)) {
            return;
        }
        List<DecHead> oldDecHeads = decHeadMapper.selectList(new QueryWrapper<DecHead>().lambda()
                .eq(DecHead::getInvId, invtHead.getId()));
        if (isNotEmpty(oldDecHeads)) {
            log.info("已生成报关单，不允许重复生成！");
            return;
        }
        List<NemsInvtList> invtLists = invtHead.getNemsInvtLists();
        String optUnitName = null;
        String deliverUnitName = null;
        String declareUnitName = null;
        if ("1".equals(invtHead.getDclcusTypecd())) { // 关联报关
            optUnitName = invtHead.getRltEntryBizopEtpsNm();
            deliverUnitName = invtHead.getRltEntryRcvgdEtpsNm();
            declareUnitName = invtHead.getRltEntryDclEtpsNm();
        } else if ("2".equals(invtHead.getDclcusTypecd())) { // 对应报关
            optUnitName = invtHead.getBizopEtpsNm();
            deliverUnitName = invtHead.getRcvgdEtpsNm();
            declareUnitName = invtHead.getCorrEntryDclEtpsNm();
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        DecHead decHead = new DecHead();
        //20241030追加 根据前端录入的报关单字段继续追加到报关单
        BeanUtil.copyProperties(invtHead,decHead,"status");

        // 境内收发货人
        List<CustomerEnterprise> optUnitCustomers = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, optUnitName));
        CustomerEnterprise optUnitCustomer = isNotEmpty(optUnitCustomers) ? optUnitCustomers.get(0) : null;
        // 消费使用单位
        List<CustomerEnterprise> deliverUnitCustomers = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, deliverUnitName));
        CustomerEnterprise deliverUnitCustomer = isNotEmpty(deliverUnitCustomers) ? deliverUnitCustomers.get(0) : null;
        // 申报单位
        List<CustomerEnterprise> declareUnitNames = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, declareUnitName));
        CustomerEnterprise declareUnitCustomer = isNotEmpty(declareUnitNames) ? declareUnitNames.get(0) : null;

        if (isNotBlank(invtHead.getDclcusTypecd())) {
            decHead.setTradeCiqCode(optUnitCustomer != null ? optUnitCustomer.getCiqCode() : null);//境内收发货人检验检疫编码
            decHead.setOwnerCiqCode(deliverUnitCustomer != null ? deliverUnitCustomer.getCiqCode() : null);//消费使用单位检验检疫编码
            decHead.setDeclCiqCode(declareUnitCustomer != null ? declareUnitCustomer.getCiqCode() : null);//申报企业检验检疫编码
        }

        decHead.setId(IdWorker.getIdStr());
        decHead.setOutPortCode("4301"); // 默认泉城海关
        decHead.setShipTypeCode("8"); // 默认保税仓库
        decHead.setTradeTypeCode("0110"); // 默认一般贸易
        decHead.setTaxTypeCode("101"); // 默认一般征税
        decHead.setArrivalArea("142"); // 默认中国
        decHead.setDesPort("CHN000"); // 默认中国境内
        decHead.setTermsTypeCode("1"); // 默认CIF
        decHead.setPacksKinds("01"); // 默认裸装
        decHead.setEntyPortCode("370001"); // 默认济南
        decHead.setGoodsPlace(invtHead.getRcvgdEtpsNm()); // 核注单加工单位
        decHead.setClearanceType("M"); // 默认通关无纸化

        decHead.setAudited(false);
        decHead.setSend("0");
        decHead.setCreateTime(new Date());
        decHead.setSynchronism(false);
        decHead.setCustomsCode("D" + decHead.getId());
        decHead.setCreatePerson(loginUser.getUsername()); // 录入人

        decHead.setInvId(String.valueOf(invtHead.getId()));
        decHead.setSeqNo(invtHead.getImportSeqNo());//统一编号
        decHead.setEtpsInnerInvtNo(invtHead.getEtpsInnerInvtNo());
        decHead.setShipTypeCode(invtHead.getTrspModecd());//运输方式
        decHead.setDclTenantId(TenantContext.getTenant());// 申请人租户ID
        decHead.setRecordNumber(invtHead.getPutrecNo());//备案号
        //转换数字到字母
        if(isNotBlank(invtHead.getStshipTrsarvNatcd())){
            List<ErpCountries> erpCountries = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                    .eq(ErpCountries::getCode, invtHead.getStshipTrsarvNatcd()));
            if(erpCountries.size()>0){
                List<ErpCountries> erpCountries2 = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                        .eq(ErpCountries::getName, erpCountries.get(0).getName())
                        .eq(ErpCountries::getIsenabled,"1"));
                decHead.setArrivalArea(erpCountries2.size()>0?erpCountries2.get(0).getCode():"");//运抵国
            }
        }

        if ("1".equals(invtHead.getDclcusTypecd())) {//关联
            decHead.setOptUnitId(invtHead.getRltEntryBizopEtpsno());//境内收发货人（经营单位）
            decHead.setOptUnitName(invtHead.getRltEntryBizopEtpsNm());
            decHead.setOptUnitSocialCode(invtHead.getRltEntryBizopEtpsSccd());
            decHead.setDeliverUnit(invtHead.getRltEntryRcvgdEtpsno());//消费使用单位（加工单位）
            decHead.setDeliverUnitName(invtHead.getRltEntryRcvgdEtpsNm());
            decHead.setDeliverUnitSocialCode(invtHead.getRltEntryRvsngdEtpsSccd());
            decHead.setDeclareUnit(invtHead.getRltEntryDclEtpsno());//申报企业
            decHead.setDeclareUnitName(invtHead.getRltEntryDclEtpsNm());
            decHead.setDeclareUnitSocialCode(invtHead.getRltEntryDclEtpsSccd());
        } else if ("2".equals(invtHead.getDclcusTypecd())) {
            decHead.setOptUnitId(invtHead.getBizopEtpsno());//境内收发货人（经营单位）
            decHead.setOptUnitName(invtHead.getBizopEtpsNm());
            decHead.setOptUnitSocialCode(invtHead.getBizopEtpsSccd());
            decHead.setDeliverUnit(invtHead.getRcvgdEtpsno());//消费使用单位（加工单位）
            decHead.setDeliverUnitName(invtHead.getRcvgdEtpsNm());
            decHead.setDeliverUnitSocialCode(invtHead.getRvsngdEtpsSccd());
            decHead.setDeclareUnit(invtHead.getCorrEntryDclEtpsno());//申报企业
            decHead.setDeclareUnitName(invtHead.getCorrEntryDclEtpsNm());
            decHead.setDeclareUnitSocialCode(invtHead.getCorrEntryDclEtpsSccd());
        }


//        decHead.setIeFlag(invtHead.getImpexpMarkcd());//进出口
        decHead.setDeclarePlace(invtHead.getDclplcCuscd());//申报地海关
        String clearanceNo = null;
        if ("1".equals(invtHead.getDclcusFlag())) {
            if ("1".equals(invtHead.getDclcusTypecd())) {//关联
                clearanceNo = invtHead.getRltEntryNo();
            } else if ("2".equals(invtHead.getDclcusTypecd())) {//对应
                clearanceNo = invtHead.getEntryNo();
            }
        }
        decHead.setClearanceNo(clearanceNo);

        // 根据类型判断报关单的进出口和类型
        if (null == invtHead.getDecType()) {
            invtHead.setDecType("");
        }
        judgeDdeclarationType(invtHead.getDecType(), decHead);

        //全部币制信息
        List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
        List<ErpCountries> erpCountries = erpCountriesMapper.selectList(null);

        Map<Integer, DecList> decListMap = new HashMap<>();
        int i = 1;
        for (NemsInvtList v : invtLists) {
            if (isEmpty(v.getEntryGdsSeqno())) {
                throw new RuntimeException("核注单表体项的「报关单商品序号」有空值，无法生成关联报关单，请检查！");
            }
            // 2024/3/4 13:51@ZHANGCHAO 追加/变更/完善：出入库单生成时，逻辑不同！！
            if (isNotEmpty(invtHead.getFromStock()) && invtHead.getFromStock()) {
                DecList decList = new DecList();
                decList.setId(null);
                toDecList(v, decList, decHead.getId(), invtHead);
                nemsInvtListMapper.update(null, new UpdateWrapper<NemsInvtList>().lambda()
                        .set(NemsInvtList::getEntryGdsSeqno, v.getEntryGdsSeqno())
                        .eq(NemsInvtList::getInvId, invtHead.getId())
                        .in(NemsInvtList::getGdsseqNo, Arrays.asList(v.getGoodsId().split(","))));
                if (decHead.getNetWeight() == null) {
                    decHead.setNetWeight(decList.getNetWeight());
                } else {
                    BigDecimal netWeight = decList.getNetWeight() == null ? BigDecimal.ZERO.stripTrailingZeros() : decList.getNetWeight();
                    decHead.setNetWeight(decHead.getNetWeight().add(netWeight));
                }
                if (decHead.getGrossWeight() == null) {
                    decHead.setGrossWeight(decList.getGrossWeight());
                } else {
                    BigDecimal grossWeight = decList.getGrossWeight() == null ? BigDecimal.ZERO.stripTrailingZeros() : decList.getGrossWeight();
                    decHead.setGrossWeight(decHead.getGrossWeight().add(grossWeight));
                }
                decListMap.put(v.getEntryGdsSeqno(), decList);
            } else {
                if (decListMap.containsKey(v.getEntryGdsSeqno())) {
                    //申报数量
                    if (decListMap.get(v.getEntryGdsSeqno()).getGoodsCount() == null || v.getDclQty() == null) {
                        throw new RuntimeException("核注单表体项的「申报数量」有空值，无法生成关联报关单，请检查！");
                    } else {
                        decListMap.get(v.getEntryGdsSeqno())
                                .setGoodsCount(decListMap.get(v.getEntryGdsSeqno()).getGoodsCount() != null
                                        ? decListMap.get(v.getEntryGdsSeqno()).getGoodsCount().add(v.getDclQty()) : v.getDclQty());
                    }

                    //法定第一数量
                    if (decListMap.get(v.getEntryGdsSeqno()).getCount1() == null || v.getLawfQty() == null) {
                        throw new RuntimeException("核注单表体项的「法定第一数量」有空值，无法生成关联报关单，请检查！");
                    } else {
                        decListMap.get(v.getEntryGdsSeqno())
                                .setCount1(decListMap.get(v.getEntryGdsSeqno()).getCount1().add(v.getLawfQty()));
                    }

                    //法定第二数量
                    decListMap.get(v.getEntryGdsSeqno())
                            .setCount2(decListMap.get(v.getEntryGdsSeqno()).getCount2() != null
                                    ? decListMap.get(v.getEntryGdsSeqno()).getCount2().add(v.getSecdLawfQty()) : v.getSecdLawfQty());
                    //总价
                    if (decListMap.get(v.getEntryGdsSeqno()).getTotal() == null || v.getDclTotalamt() == null) {
                        throw new RuntimeException("核注单表体项的「总价」有空值，无法生成关联报关单，请检查！");
                    } else {
                        decListMap.get(v.getEntryGdsSeqno())
                                .setTotal(decListMap.get(v.getEntryGdsSeqno()).getTotal().add(v.getDclTotalamt()));
                        //单价
                        decListMap.get(v.getEntryGdsSeqno()).setPrice(
                                decListMap.get(v.getEntryGdsSeqno()).getTotal().divide(decListMap.get(v.getEntryGdsSeqno()).getGoodsCount(), 4, RoundingMode.HALF_UP));
                    }

                    //净重
                    if (decListMap.get(v.getEntryGdsSeqno()).getNetWeight() != null && v.getNetWt() != null) {
                        decListMap.get(v.getEntryGdsSeqno())
                                .setNetWeight(decListMap.get(v.getEntryGdsSeqno()).getNetWeight().add(v.getNetWt()));
                        decHead.setNetWeight(decHead.getNetWeight().add(v.getNetWt()));
                    }
                    //毛重
                    if (decListMap.get(v.getEntryGdsSeqno()).getGrossWeight() != null && v.getGrossWt() != null) {
                        decListMap.get(v.getEntryGdsSeqno())
                                .setGrossWeight(decListMap.get(v.getEntryGdsSeqno()).getGrossWeight().add(v.getGrossWt()));
                        decHead.setGrossWeight(decHead.getNetWeight().add(v.getGrossWt()));
                    }
                } else {
                    DecList decList = new DecList();
                    decList.setId(null);
                    decList.setItem(i);
                    toDecList(v, decList, decHead.getId(), invtHead);
                    //转换币值
                    if(isNotBlank(v.getDclCurrcd())){
                        List<ErpCurrencies> erpCurrencies1=erpCurrencies.stream().filter(s->s.getCode().equals(v.getDclCurrcd()))
                                .collect(Collectors.toList());
                        decList.setCurrencyCode(erpCurrencies1.size()>0?erpCurrencies1.get(0).getCurrency():null);
                    }                //转换国别地区
                    if(isNotBlank(v.getNatcd())){
                        List<ErpCountries> erpCountries1= erpCountries.stream().filter(z->z.getCode().equals(v.getNatcd()))
                                .collect(Collectors.toList());
                        if(erpCountries1.size()>0){
                            List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                            &&o.getIsenabled()==1)
                                    .collect(Collectors.toList());
                            decList.setDestinationCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                        }
                    }
                    if(isNotBlank(v.getOriginCountry())){
                        List<ErpCountries> erpCountries1= erpCountries.stream().filter(p->p.getCode().equals(v.getOriginCountry()))
                                .collect(Collectors.toList());
                        if(erpCountries1.size()>0){
                            List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                            &&o.getIsenabled()==1)
                                    .collect(Collectors.toList());
                            decList.setDesCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                        }
                    }


                    if (decHead.getNetWeight() == null) {
                        decHead.setNetWeight(decList.getNetWeight());
                    } else {
                        BigDecimal netWeight = decList.getNetWeight() == null ? BigDecimal.ZERO.stripTrailingZeros() : decList.getNetWeight();
                        decHead.setNetWeight(decHead.getNetWeight().add(netWeight));
                    }
                    if (decHead.getGrossWeight() == null) {
                        decHead.setGrossWeight(decList.getGrossWeight());
                    } else {
                        BigDecimal grossWeight = decList.getGrossWeight() == null ? BigDecimal.ZERO.stripTrailingZeros() : decList.getGrossWeight();
                        decHead.setGrossWeight(decHead.getGrossWeight().add(grossWeight));
                    }
                    decListMap.put(v.getEntryGdsSeqno(), decList);
                    i++;
                }
            }
        }
        List<DecList> decLists = new ArrayList<>(decListMap.values());
        decHead.setDecLists(decLists);

        //生成随附单证
        DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
        decLicenseDocus.setDecId(decHead.getId());
        decLicenseDocus.setDocuCode("a");
        decLicenseDocus.setCertCode(invtHead.getBondInvtNo());

        if ("I".equals(decHead.getIeFlag())) {
            decHead.setOutDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }

        decHeadMapper.insert(decHead);
        decListService.saveBatch(decLists);
        decLicenseDocusMapper.insert(decLicenseDocus);

        log.info("生成报关单成功，id为：" + decHead.getId());
    }

    /**
     * 删除一批库存数据
     *
     * @param ids id字符串，多个id用逗号分隔
     * @return Result<?>对象，表示删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> delBatch(String ids) {
        List<NemsInvtHead> nemsInvtHeadList = this.listByIds(Arrays.asList(ids.split(",")));
        if (isNotEmpty(nemsInvtHeadList)) {
            for (NemsInvtHead nemsInvtHead : nemsInvtHeadList) {
                if ("1".equals(nemsInvtHead.getVrfdedMarkcd()) || "2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    return Result.error("已核扣或预核扣的核注单[" + nemsInvtHead.getId() + "]，不允许删除!");
                }
                List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                        .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
                if (isNotEmpty(nemsInvtListList)) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList nemsInvtList : nemsInvtListList) {
                        stockParamVO.setNemsInvtList(nemsInvtList);
                        // 解除占用
                        if (isNotEmpty(nemsInvtHead.getSend()) && nemsInvtHead.getSend()) {
                            Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
                            if (!deOccResult.isSuccess()) {
                                throw new RuntimeException(deOccResult.getMessage());
                            }
                        }
                        storageDetailMapper.update(null, new UpdateWrapper<StorageDetail>().lambda()
                                .set(StorageDetail::getInvtListId, null)
                                .eq(StorageDetail::getInvtListId, nemsInvtList.getId()));
                        if (isNotBlank(nemsInvtList.getStockGoodsId())) {
                            stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                                    .set(StockGoodsType::getInvtListId, null)
                                    .in(StockGoodsType::getId, Arrays.asList(nemsInvtList.getStockGoodsId().split(","))));
                        }
                    }
                }
                if (isNotBlank(nemsInvtHead.getStockHeadId())) {
                    stockHeadTypeMapper.update(null, new UpdateWrapper<StockHeadType>().lambda()
                            .set(StockHeadType::getDclcusFlag, "2")
                            .set(StockHeadType::getInvtHeadId, null)
                            .in(StockHeadType::getId, Arrays.asList(nemsInvtHead.getStockHeadId().split(","))));
                }
                nemsInvtListMapper.delete(new QueryWrapper<NemsInvtList>().lambda()
                        .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
                baseMapper.deleteById(nemsInvtHead.getId());
            }
        }
        return Result.ok("删除成功!");
    }

    /**
     * 处理初步审核
     *
     * @param ids                 要处理的文档ID
     * @param initialReviewStatus 初步审核状态
     * @param opinion             意见
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleInitialReview(String ids, String initialReviewStatus, String opinion) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if ("1".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                    .set(NemsInvtHead::getInitialReviewStatus, initialReviewStatus)
                    .set(NemsInvtHead::getFirstTrialBy, loginUser.getUsername())
                    .set(NemsInvtHead::getFirstTrialDate, new Date())
                    .set(NemsInvtHead::getFirstOpinion, opinion)
                    .in(NemsInvtHead::getId, Arrays.asList(ids.split(","))));
        } else if ("2".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                    .set(NemsInvtHead::getInitialReviewStatus, initialReviewStatus)
                    .set(NemsInvtHead::getReviewBy, loginUser.getUsername())
                    .set(NemsInvtHead::getReviewDate, new Date())
                    .set(NemsInvtHead::getReviewOpinion, opinion)
                    .in(NemsInvtHead::getId, Arrays.asList(ids.split(","))));
        }
        return Result.OK("1".equals(initialReviewStatus) ? "初审成功！" : "复审成功！");
    }

    /**
     * @param nemsInvtHeadList
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Result<?> saveNemsInvtHeadBatch_(List<NemsInvtHead> nemsInvtHeadList) {
        if (isEmpty(nemsInvtHeadList)) {
            return Result.error("核注单数据为空！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<NemsInvtHead> addHeads = new ArrayList<>();
        List<NemsInvtHead> updateHeads = new ArrayList<>();
        List<NemsInvtList> addLists = new ArrayList<>();
        List<NemsInvtList> updateLists = new ArrayList<>();
//        List<Long> headIdList = nemsInvtHeadList.stream().map(NemsInvtHead::getId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
//        if (isNotEmpty(headIdList)) {
//            nemsInvtListService.remove(new LambdaQueryWrapper<NemsInvtList>().in(NemsInvtList::getInvId, headIdList));
//        }
        for (NemsInvtHead nemsInvtHead : nemsInvtHeadList) {
            if (isEmpty(nemsInvtHead.getId())) {
                nemsInvtHead.setId(IdWorker.getId());
                if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                    nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
                }
                addHeads.add(nemsInvtHead);
                // 新增时的表体
                if (isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
                    nemsInvtHead.getNemsInvtLists().forEach(v -> {
                        v.setId(IdWorker.getId());
                        v.setInvId(nemsInvtHead.getId());
                        v.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
                    });
                    addLists.addAll(nemsInvtHead.getNemsInvtLists());
                }
            } else {
                if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                    nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
                }
                updateHeads.add(nemsInvtHead);
                // 编辑时的表体
                if (isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
                    nemsInvtHead.getNemsInvtLists().forEach(v -> {
                        v.setInvId(nemsInvtHead.getId());
                        v.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
                    });
                    updateLists.addAll(nemsInvtHead.getNemsInvtLists());
                }
            }
        }
        if (isNotEmpty(addHeads)) {
            log.info("走插入核注单条数:{}", addHeads.size());
//            baseMapper.insertBatchSomeColumn(addHeads);
            for (NemsInvtHead addHead : addHeads) {
                int insert = baseMapper.insert(addHead);
                if (insert > 0) {
                    syncNemsInvtHeadMapper.update(null, new LambdaUpdateWrapper<SyncNemsInvtHead>()
                            .set(SyncNemsInvtHead::getIsSync, "1")
                            .eq(SyncNemsInvtHead::getId, addHead.getFlyId())
                            .eq(SyncNemsInvtHead::getTenantId, addHead.getTenantId()));
                }
            }
        }
        if (isNotEmpty(updateHeads)) {
            log.info("走编辑核注单条数:{}", updateHeads.size());
            // TODO: 2023/11/18 不支持批量编辑：sql injection violation, multi-statement not allow ！！待解决！！
//            baseMapper.updateBatchById(updateHeads);
//            this.updateBatchById(updateHeads);
            for (NemsInvtHead updateHead : updateHeads) {
                int update = baseMapper.updateById(updateHead);
                if (update > 0) {
                    syncNemsInvtHeadMapper.update(null, new LambdaUpdateWrapper<SyncNemsInvtHead>()
                            .set(SyncNemsInvtHead::getIsSync, "1")
                            .eq(SyncNemsInvtHead::getId, updateHead.getFlyId())
                            .eq(SyncNemsInvtHead::getTenantId, updateHead.getTenantId()));
                }
            }
        }
        if (isNotEmpty(addLists)) {
            log.info("新增核注单表体条数:{}", updateLists.size());
            baseMapper.insertBatchLists(addLists);
        }
        if (isNotEmpty(updateLists)) {
            log.info("编辑核注单表体条数:{}", updateLists.size());
            updateLists.forEach(v -> {
                if (isNotEmpty(v.getId())) {
                    nemsInvtListMapper.updateById(v);
                } else {
                    nemsInvtListMapper.insert(v);
                }
            });
        }
        //20250613优化，保存完毕核注单后，对对应的手帐册号，包含的表体备案序号进行计算数量
        for(NemsInvtHead nemsInvtHead:nemsInvtHeadList){
            //已核扣的进行统计数量
            if("2".equals(nemsInvtHead.getVrfdedMarkcd())
                    &&isNotBlank(nemsInvtHead.getPutrecNo())&&!nemsInvtHead.getNemsInvtLists().isEmpty()){
                //筛选出表体nemsInvtHead.getNemsInvtLists()的putrecSeqno （排除空的） list<Integer> 接收
                List<Integer> putrecSeqnos=nemsInvtHead.getNemsInvtLists().stream().map(NemsInvtList::getPutrecSeqno)
                        .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
                //putrecSeqnos转为字符串，用逗号分隔
                String putrecSeqnosStr= StringUtils.join(putrecSeqnos,",");
                ptsEmsHeadService.xxx(nemsInvtHead.getPutrecNo(),putrecSeqnosStr);
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK("插入核注单数：" + addHeads.size() + "；" + "更新核注单数：" + updateHeads.size());
    }

    @Override
    public Result<NemsInvtHead> getInvtBySeqNo(String seqNo) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        NemsInvtHead invtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().eq("SEQ_NO", seqNo));
        List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda().eq(NemsInvtList::getInvId, invtHead.getId()));
        invtHead.setNemsInvtLists(invtLists);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(invtHead);
    }

    /**
     * @param nemsInvtHeadList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveNemsInvtHeadBatch(List<NemsInvtHead> nemsInvtHeadList) {
        if (isEmpty(nemsInvtHeadList)) {
            return Result.error("核注单数据为空！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<NemsInvtHead> addHeads = new ArrayList<>();
        List<NemsInvtHead> updateHeads = new ArrayList<>();
        List<NemsInvtList> addLists = new ArrayList<>();
        List<NemsInvtList> updateLists = new ArrayList<>();
//        List<Long> headIdList = nemsInvtHeadList.stream().map(NemsInvtHead::getId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
//        if (isNotEmpty(headIdList)) {
//            nemsInvtListService.remove(new LambdaQueryWrapper<NemsInvtList>().in(NemsInvtList::getInvId, headIdList));
//        }
        // 存储所有的已核扣核注单数据
        List<NemsInvtHead> newHeadsVrfdedMarkcdList = new ArrayList<>();
        for (NemsInvtHead nemsInvtHead : nemsInvtHeadList) {
            // 新增
            if (isEmpty(nemsInvtHead.getId())) {
                nemsInvtHead.setId(IdWorker.getId());
                if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                    nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
                }
                addHeads.add(nemsInvtHead);
                // 新增时的表体
                if (isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
                    nemsInvtHead.getNemsInvtLists().forEach(v -> {
                        v.setId(IdWorker.getId());
                        v.setInvId(nemsInvtHead.getId());
                        v.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
                    });
                    addLists.addAll(nemsInvtHead.getNemsInvtLists());
                }
                if ("I".equals(nemsInvtHead.getImpexpMarkcd()) && "2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    newHeadsVrfdedMarkcdList.add(nemsInvtHead);
                }
            } else {
                if (isBlank(nemsInvtHead.getEtpsInnerInvtNo())) {
                    nemsInvtHead.setEtpsInnerInvtNo("H" + nemsInvtHead.getId());
                }
                updateHeads.add(nemsInvtHead);
                // 编辑时的表体
                if (isNotEmpty(nemsInvtHead.getNemsInvtLists())) {
                    nemsInvtHead.getNemsInvtLists().forEach(v -> {
                        v.setInvId(nemsInvtHead.getId());
                        v.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
                    });
                    updateLists.addAll(nemsInvtHead.getNemsInvtLists());
                }
                if ("I".equals(nemsInvtHead.getImpexpMarkcd()) && "2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    newHeadsVrfdedMarkcdList.add(nemsInvtHead);
                }
            }
        }
        if (isNotEmpty(addHeads)) {
            log.info("走插入核注单条数:{}", addHeads.size());
            baseMapper.insertBatchSomeColumn(addHeads);
        }
        if (isNotEmpty(updateHeads)) {
            log.info("走编辑核注单条数:{}", updateHeads.size());
            // TODO: 2023/11/18 不支持批量编辑：sql injection violation, multi-statement not allow ！！待解决！！
//            baseMapper.updateBatchById(updateHeads);
            this.updateBatchById(updateHeads);
        }
        if (isNotEmpty(addLists)) {
            log.info("新增核注单表体条数:{}", updateLists.size());
            baseMapper.insertBatchLists(addLists);
        }
        if (isNotEmpty(updateLists)) {
            log.info("编辑核注单表体条数:{}", updateLists.size());
            updateLists.forEach(v -> {
                if (isNotEmpty(v.getId())) {
                    nemsInvtListMapper.updateById(v);
                } else {
                    nemsInvtListMapper.insert(v);
                }
            });
        }
        NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
        /*
         * 核注清单下行后，根据核注单的核扣状态去回填这个项号（已核扣），
         * 只要核注单有了项号，且核注单有关联的入库单，系统自动回填到入库单上，
         * 如果入库单未提交入库，则自动提交入库。
         * 2024/11/21 09:41@ZHANGCHAO
         */
        try {
            if (isNotEmpty(newHeadsVrfdedMarkcdList)) {
                currentProxy.automatedBackfillAndSubmitForEntry(newHeadsVrfdedMarkcdList);
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("回填项号及自动提交入库失败，失败原因：{}", e.getMessage());
//            throw new RuntimeException(e);
        }
        /*
         * 1. 下行核注单时，自动根据进出口申报数量增减数量。要考虑账册记账模式-累计或者不累计。
         * 进口已核扣累计的核增或者不累计的新增料件，出口已核扣核减数量（如果库里已有预核扣核注单要减账册预核扣数量）
         * ，预核扣增加预核扣数量，删单或者反核扣的回退库存。
         * 2024/11/25 10:16@ZHANGCHAO
         */
        try {
            // TODO: 2024/11/25 10:16@ZHANGCHAO
            Result<?> result = currentProxy.processingEmsStockQuantities(nemsInvtHeadList);
            if (result.isSuccess()) {
                log.info("处理账册库存数量成功：{}", result.getResult());
            } else {
                log.error("处理账册库存数量失败，失败原因：{}", result.getMessage());
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("处理账册库存出现异常，原因：{}", e.getMessage());
//            throw new RuntimeException(e); // 2024/11/26 14:31@ZHANGCHAO 追加/变更/完善：不影响下行处理！！
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK("插入核注单数：" + addHeads.size() + "；" + "更新核注单数：" + updateHeads.size());
    }

    /**
     * 处理账册库存数量
     *
     * @param nemsInvtHeads
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/25 10:29
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public Result<?> processingEmsStockQuantities(List<NemsInvtHead> nemsInvtHeads) {
        if (isEmpty(nemsInvtHeads)) {
            return Result.error("未获取到核注单数据！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        Map<String, PtsEmsHead> emsHeadMap = new HashMap<>();
        Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
        // 2024/2/19 15:07@ZHANGCHAO 追加/变更/完善：提前验证下吧！！
        for (NemsInvtHead nemsInvtHead : nemsInvtHeads) {
            if (isBlank(nemsInvtHead.getPutrecNo())) {
                log.info("【processingEmsStockQuantities】核注单的手账册编号不能为空，请检查！");
                return Result.error("核注单的手账册编号不能为空，请检查！");
            }
            // 2024/12/2 09:38@ZHANGCHAO 追加/变更/完善：只处理物流账册！！
            if (!isBSWL(nemsInvtHead.getPutrecNo())) {
                log.info("{}不是物流账册，不需要处理库存~~~~", nemsInvtHead.getPutrecNo());
                continue;
            }
            PtsEmsHead emsHead = emsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getEmsNo, nemsInvtHead.getPutrecNo())
                    .eq(PtsEmsHead::getTenantId, nemsInvtHead.getTenantId()));
            if (isEmpty(emsHead)) {
                log.info("【processingEmsStockQuantities】系统中不存在编号[{}]的账册，请检查！", nemsInvtHead.getPutrecNo());
                return Result.error("系统中不存在编号[" + nemsInvtHead.getPutrecNo() + "]的账册，请检查！");
            }
            emsHeadMap.put(nemsInvtHead.getPutrecNo(), emsHead);
            List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
            if (isEmpty(invtLists)) {
                log.info("【processingEmsStockQuantities】核注单[{}]无表体数据，请检查！", nemsInvtHead.getId());
                return Result.error("核注单[" + nemsInvtHead.getId() + "]无表体数据，请检查！");
            } else {
                boolean hasEmtpyGno = invtLists.stream().anyMatch(v -> isEmpty(v.getPutrecSeqno()) && isBlank(v.getAutoNo()));
                if (hasEmtpyGno) {
                    log.info("【processingEmsStockQuantities】核注单[{}]表体的备案序号存在空数据，请检查！", nemsInvtHead.getId());
                    return Result.error("核注单[" + nemsInvtHead.getId() + "]表体的备案序号存在空数据，请检查！");
                }
            }
            nemsInvtListMap.put(nemsInvtHead.getId(), invtLists);
        }
        for (NemsInvtHead nemsInvtHead : nemsInvtHeads) {
            PtsEmsHead emsHead = emsHeadMap.get(nemsInvtHead.getPutrecNo());
            List<NemsInvtList> invtLists = nemsInvtListMap.get(nemsInvtHead.getId());
            if (isEmpty(invtLists)) {
                continue;
            }
            /*
             * 物流账册，只有料件！！
             * 2024/11/25 13:22@ZHANGCHAO
             */
            // 进口核注单
            if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                // 2.已核扣 - 物流账册商品入库记账/核增数量
                if ("2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            Result<?> result = emsStocksFlowService.increaseGoods(stockParamVO);
                            if (!result.isSuccess()) {
                                log.info("【processingEmsStockQuantities】已核扣核注单 - 物流账册商品入库记账/核增数量失败！，返回消息：{}", result.getMessage());
                                throw new RuntimeException(result.getMessage());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("【processingEmsStockQuantities】已核扣核注单 - 物流账册商品入库记账/核增数量失败！，异常信息：{}", e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                    // 反核扣 - 回退库存数量
                } else if ("4".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    stockParamVO.setOptTypeEnum(OptTypeEnum.GOODS_ADD);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            // 回退库存
                            Result<?> result = emsStocksFlowService.rectify(stockParamVO);
                            if (!result.isSuccess()) {
                                log.info(result.getMessage());
                                throw new RuntimeException(result.getMessage());
                            } else {
                                log.info("回退库存成功：{}", getGoodsText(stockParamVO));
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                }
                // 出口核注单 - 核减库存
            } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                // 已核扣 - 核减库存（有占用删占用数量，即预核扣数量）
                if ("2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            Result<?> result = emsStocksFlowService.reduceGoods(stockParamVO);
                            if (!result.isSuccess()) {
                                log.info("【processingEmsStockQuantities】出口已核扣核注单 - 核减库存失败！，返回消息：{}", result.getMessage());
                                throw new RuntimeException(result.getMessage());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("【processingEmsStockQuantities】出口已核扣核注单 - 核减库存失败！，异常信息：{}", e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                    // 预核扣 - 核增占用数量（相当于占用）
                } else if ("1".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            // 解除占用
                            Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
                            if (!deOccResult.isSuccess()) {
                                log.info(deOccResult.getMessage());
                                throw new RuntimeException(deOccResult.getMessage());
                            }
                            // 占用
                            Result<?> result = emsStocksFlowService.occupyGoods(stockParamVO);
                            if (!result.isSuccess()) {
                                log.info(result.getMessage());
                                throw new RuntimeException(result.getMessage());
                            } else {
                                StringBuilder msg = new StringBuilder();
                                msg.append("表体[备案序号：").append(invtList.getPutrecSeqno()).append("，物料号：")
                                        .append(invtList.getGdsMtno()).append("]已核增占用数量：").append(stockParamVO.getNemsInvtList().getDclQty()).append("；");
                                log.info(msg.toString());
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                    // 反核扣 - 回退库存数量
                } else if ("4".equals(nemsInvtHead.getVrfdedMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    stockParamVO.setOptTypeEnum(OptTypeEnum.GOODS_REDUCE);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            // 回退库存
                            Result<?> result = emsStocksFlowService.rectify(stockParamVO);
                            if (!result.isSuccess()) {
                                log.info(result.getMessage());
                                throw new RuntimeException(result.getMessage());
                            } else {
                                log.info("回退库存成功：{}", getGoodsText(stockParamVO));
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                }
            }
            // 2024/2/19 15:36@ZHANGCHAO 追加/变更/完善：回填物流账册数据！！
            String clearanceNo = "";
            if (isNotBlank(nemsInvtHead.getDclcusTypecd())) {
                if ("1".equals(nemsInvtHead.getDclcusTypecd())) { // 关联报关
                    clearanceNo = nemsInvtHead.getRltEntryNo();
                } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) { // 对应报关
                    clearanceNo = nemsInvtHead.getEntryNo();
                }
            } else {
                if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                    clearanceNo = nemsInvtHead.getEntryNo();
                } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                    clearanceNo = nemsInvtHead.getRltEntryNo();
                }
            }
            List<Integer> gnos = invtLists.stream().map(NemsInvtList::getPutrecSeqno).collect(Collectors.toList());
            List<String> copgnos = invtLists.stream().map(NemsInvtList::getGdsMtno).collect(Collectors.toList());

            try {
                emsAimgMapper.update(null, new UpdateWrapper<PtsEmsAimg>().lambda()
                        .set(PtsEmsAimg::getBondInvtNo, nemsInvtHead.getBondInvtNo())
                        .set(PtsEmsAimg::getClearanceNo, clearanceNo)
                        .set(PtsEmsAimg::getInternalNo, nemsInvtHead.getEtpsInnerInvtNo())
                        .set(PtsEmsAimg::getWarehousingDate, new Date())
                        .eq(PtsEmsAimg::getEmsId, emsHead.getId())
                        .in(PtsEmsAimg::getGNo, gnos)
                        .in(PtsEmsAimg::getCopGno, copgnos));
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("核注单" + nemsInvtHead.getId() + "回填物流账册时出现异常：" + e.getMessage());
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("[processingEmsStockQuantities]处理成功！");
    }

    /**
     * 已核扣核注单回填入库单项号以及自动提交入库
     *
     * @param nemsInvtHeads
     * @return void
     * <AUTHOR>
     * @date 2024/11/21 11:06
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void automatedBackfillAndSubmitForEntry(List<NemsInvtHead> nemsInvtHeads) {
        if (isEmpty(nemsInvtHeads)) {
            return;
        }
        // 2024/12/9 13:04@ZHANGCHAO 追加/变更/完善：查询企业配置！！
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "AutomatedBackfillAndSubmit")
                .eq(SysConfig::getTenantId, nemsInvtHeads.get(0).getTenantId()));
        boolean isEnable = false;
        if (isNotEmpty(sysConfigs) && "1".equals(sysConfigs.get(0).getConfigValue())) {
            // 如果启用自动回填入库...继续...
            isEnable = true;
            log.info("【automatedBackfillAndSubmitForEntry】启用自动回填入库！！{}", nemsInvtHeads.get(0).getTenantId());
        } else {
            log.info("【automatedBackfillAndSubmitForEntry】未启用自动回填入库！！{}", nemsInvtHeads.get(0).getTenantId());
        }
        // 筛选进口已核扣核注单
        List<NemsInvtHead> vrfdedMarkcdList = nemsInvtHeads.stream().filter(i -> "I".equals(i.getImpexpMarkcd()) && "2".equals(i.getVrfdedMarkcd())).collect(Collectors.toList());
        if (isEmpty(vrfdedMarkcdList)) {
            log.info("【automatedBackfillAndSubmitForEntry】不存在进口已核扣的核注单，无需回填及自动入库！！");
            return;
        }
        List<Long> headIds = vrfdedMarkcdList.stream().map(NemsInvtHead::getId).collect(Collectors.toList());
        List<NemsInvtList> nemsInvtLists = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
                .in(NemsInvtList::getInvId, headIds));
        Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
        if (isNotEmpty(nemsInvtLists)) {
            nemsInvtListMap = nemsInvtLists.stream().collect(Collectors.groupingBy(NemsInvtList::getInvId));
        }
        Map<Long, List<NemsInvtList>> finalNemsInvtListMap = nemsInvtListMap;
        List<StorageInfo> storageInfoResultList = new ArrayList<>(); // 用于收集所有的关联到的入库单
        vrfdedMarkcdList.forEach(i -> {
            String clearanceNo=null;
            if ("1".equals(i.getDclcusTypecd())) {//关联
                clearanceNo = i.getRltEntryNo();
            } else if ("2".equals(i.getDclcusTypecd())) {//对应
                clearanceNo = i.getEntryNo();
            }

            List<NemsInvtList> nemsInvtList = finalNemsInvtListMap.get(i.getId());
            if (isNotEmpty(nemsInvtList)) {
                // 只要核注单有了项号，且核注单有关联的入库单，系统自动回填到入库单上，如果入库单未提交入库，则自动提交入库
                List<NemsInvtList> hasStorageList = nemsInvtList.stream().filter(j -> isNotBlank(isNotEmpty(j.getPutrecSeqno()) ? String.valueOf(j.getPutrecSeqno()) : j.getAutoNo())
                        && isNotBlank(j.getStorageNo()) && isNotEmpty(j.getStorageDetailId())).collect(Collectors.toList());
                if (isNotEmpty(hasStorageList)) {
                    List<StorageInfo> storageInfoList = storageInfoMapper.selectList(new LambdaQueryWrapper<StorageInfo>()
                            .in(StorageInfo::getStorageNo, hasStorageList.stream().map(NemsInvtList::getStorageNo).collect(Collectors.toList())));
                    Map<String, List<StorageInfo>> storageInfoMap = storageInfoList.stream().collect(Collectors.groupingBy(StorageInfo::getStorageNo));
                    storageInfoMap = isNotEmpty(storageInfoMap) ? storageInfoMap : new HashMap<>();
                    List<StorageDetail> storageDetailListList = storageDetailMapper.selectList(new LambdaQueryWrapper<StorageDetail>()
                            .in(StorageDetail::getStorageNo, hasStorageList.stream().map(NemsInvtList::getStorageNo).collect(Collectors.toList())));
                    Map<String, List<StorageDetail>> storageDetailMap = storageDetailListList.stream().collect(Collectors.groupingBy(StorageDetail::getStorageNo));
                    storageDetailMap = isNotEmpty(storageDetailMap) ? storageDetailMap : new HashMap<>();
                    Map<String, List<StorageInfo>> finalStorageInfoMap = storageInfoMap;
                    Map<String, List<StorageDetail>> finalStorageDetailMap = storageDetailMap;
                    String finalClearanceNo = clearanceNo;
                    hasStorageList.forEach(j -> {
                        try {
                            // 自动提交入库
                            StorageInfo storageInfo = isNotEmpty(finalStorageInfoMap.get(j.getStorageNo())) ? finalStorageInfoMap.get(j.getStorageNo()).get(0) : null;
                            // 已经提交入库了，就不管了！！！
                            if (isEmpty(storageInfo) || "2".equals(storageInfo.getStatus())) {
                                log.info("【automatedBackfillAndSubmitForEntry】核注单{}的入库单{}已经提交入库，无需回填项号！！！", i.getId(), j.getStorageNo());
                                return;
                            }
                            List<StorageDetail> storageDetailList = isNotEmpty(finalStorageDetailMap.get(j.getStorageNo())) ? finalStorageDetailMap.get(j.getStorageNo()) : null;
                            if (isEmpty(storageDetailList)) {
                                return;
                            }
                            // 回填项号
                            storageDetailList.stream()
                                    .filter(detail -> detail.getId().equals(j.getStorageDetailId()))
                                    .findFirst()
                                    .ifPresent(detail -> {
                                        detail.setItemNumber(isNotEmpty(j.getPutrecSeqno())
                                                ? String.valueOf(j.getPutrecSeqno()) : j.getAutoNo());
                                        //回填批次号/报关单号
                                        storageDetailList.forEach(p->{
                                            p.setBatchNo(isNotBlank(finalClearanceNo)? finalClearanceNo :null);
                                        });

                                    });
//                            if (isNotEmpty(storageInfo)) {
                                storageInfo.setStorageDetailList(storageDetailList);
//                                storageInfoService.submitInventoryAuto(storageInfo);
//                            }
                            // 判断并添加
                            if (storageInfoResultList.stream().noneMatch(info ->
                                    Objects.equals(info.getStorageNo(), storageInfo.getStorageNo()))) {
                                storageInfo.setInvtNo(i.getBondInvtNo()); // 用于放到入库单备注里。
                                storageInfoResultList.add(storageInfo);
                            }
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("处理入库单失败，核注单ID: {}, 错误: {}", i.getId(), e.getMessage(), e);
                        }
                    });
                }
            }
        });
        // 自动提交入库
        if (isNotEmpty(storageInfoResultList)) {
            log.info("【automatedBackfillAndSubmitForEntry】回填项号后，准备自动提交入库，数量：{}", storageInfoResultList.size());
            for (StorageInfo storageInfo : storageInfoResultList) {
                boolean isAllhasItemNumber = storageInfo.getStorageDetailList().stream().allMatch(detail -> isNotBlank(detail.getItemNumber()));
                if (isAllhasItemNumber) {
                    storageInfoService.submitInventoryAuto(storageInfo, isEnable);
                }
            }
        }
    }

    /**
     * 查询清单记录列表
     *
     * @param putrecNo          入库条码
     * @param putrecSeqno       入库顺序号
     * @param mtpckEndprdMarkcd 包装结束标记
     * @param vrfdedMarkcd      核对标记
     * @param pageNum           当前页码
     * @param pageSize          每页数量
     * @return 符合条件的清单记录结果
     */
    @Override
    public Result<?> listDeclarationRecord(String putrecNo, String putrecSeqno, String mtpckEndprdMarkcd, String vrfdedMarkcd, Integer pageNum, Integer pageSize) {
        IPage<NemsInvtHead> pageList = baseMapper.listDeclarationRecord(putrecNo, putrecSeqno, mtpckEndprdMarkcd, vrfdedMarkcd, new Page<>(pageNum, pageSize));
        return Result.ok(pageList);
    }

    /**
     * 修改DCL数量
     *
     * @param invtListId 发票列表ID
     * @param emsNo      仓库编码
     * @param gNo        标签号
     * @param dclQty     DCL数量
     * @param stockQty   库存数量
     * @param type       类型
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> editDclQty(String invtListId, String emsNo, String gNo, String dclQty, String stockQty, String type) {
        if (StringUtils.isBlank(dclQty)) {
            return Result.error("请输入修改后的申报数量");
        }
        if (StringUtils.isBlank(invtListId)) {
            return Result.error("未查询到此申报记录，请刷新页面重试");
        }
        //根据核注单表体id查询表体内容
        NemsInvtList nemsInvtList = nemsInvtListMapper.selectById(invtListId);
        if (null == nemsInvtList) {
            return Result.error("未查询到此申报记录，请刷新页面重试");
        }

        try {
            //放入更新后的表体申报数量
            BigDecimal dclQtyBd = new BigDecimal(dclQty);
            BigDecimal stockQtyBd = new BigDecimal(stockQty);
            nemsInvtList.setDclQty(dclQtyBd);
            //更新表体操作
            nemsInvtListMapper.updateById(nemsInvtList);
            //==========处理手册数据和流水记录
            NemsInvtHead nemsInvtHead = baseMapper.selectById(nemsInvtList.getInvId());
            if (null == nemsInvtHead) {
                return Result.error("未查询到此申报记录，请刷新页面重试");
            }
            //要获取手册id
            PtsEmsHead getByEmsNoAndTenantId = emsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getEmsNo, emsNo));
            if (isNotEmpty(getByEmsNoAndTenantId)) {
                PtsEmsAimg getEmsAimgByGno = emsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                        .eq(PtsEmsAimg::getGNo, gNo)
                        .eq(PtsEmsAimg::getEmsId, getByEmsNoAndTenantId.getId()));
                PtsEmsAexg getEmsAexgByGno = emsAexgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAexg>()
                        .eq(PtsEmsAexg::getGNo, gNo)
                        .eq(PtsEmsAexg::getEmsId, getByEmsNoAndTenantId.getId()));

                //类型为料件
                if ("I".equals(type)) {
                    if (isNotEmpty(getEmsAimgByGno)) {
                        List<PtsEmsAimg> emsAimgList = new ArrayList<>();
                        getEmsAimgByGno.setStockQty(stockQtyBd);
                        emsAimgList.add(getEmsAimgByGno);
                        Result<String> increaseBookAimg = emsStockService.increaseBookAimg(emsNo, String.valueOf(nemsInvtHead.getId()), emsAimgList, "26");
                        //如果操作成功需要关联核注单添加流水id
                        if (increaseBookAimg.isSuccess()) {
                            nemsInvtHead.setEmsFlowsId(Long.valueOf(increaseBookAimg.getResult()));
                            baseMapper.updateById(nemsInvtHead);//存入流水id后再更新一下
                        } else {
                            return Result.error("料件操作失败");
                        }
                    }
                } else if ("E".equals(type)) { //类型为成品
                    if (isNotEmpty(getEmsAexgByGno)) {
                        List<PtsEmsAexg> emsAexgList = new ArrayList<>();
                        getEmsAexgByGno.setStockQty(stockQtyBd);
                        emsAexgList.add(getEmsAexgByGno);
                        Result<String> reduceAexg = emsStockService.reduceBookAexg(emsNo, String.valueOf(nemsInvtHead.getId()), emsAexgList, "26");
                        //如果操作成功需要关联核注单添加流水id
                        if (reduceAexg.isSuccess()) {
                            nemsInvtHead.setEmsFlowsId(Long.valueOf(reduceAexg.getResult()));
                            baseMapper.updateById(nemsInvtHead);//存入流水id后再更新一下
                        } else {
                            return Result.error("成品操作失败");
                        }
                    }
                }
            }
            return Result.ok("修改申报记录成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("操作失败,请重试或联系管理员");
    }

    /**
     * 预核扣130：核注单预核扣回执，检测此委托下的所有核注单是否都大于等于预核扣（即：预核扣或者已核扣）
     *
     * @param nemsInvtHead
     * @return com.yorma.entity.YmMsg<com.yorma.dcl.entity.NemsInvtHead>
     * @apiNote <pre>
     *   预核扣130：核注单预核扣回执，检测此委托下的所有核注单是否都大于等于预核扣（即：预核扣或者已核扣）
     * </pre>
     * <AUTHOR> 2022/6/2 9:46
     * @version 1.0
     */
    @Override
    public Result<NemsInvtHead> handleWithholdingUpdateApplyNodeStatus(NemsInvtHead nemsInvtHead) {
        if (isEmpty(nemsInvtHead)) {
            return Result.error("参数不能为空！");
        }
        try {
            NemsInvtHead invtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda().eq(NemsInvtHead::getSeqNo, nemsInvtHead.getSeqNo()));
            // 预核扣130：核注单预核扣回执，检测此委托下的所有核注单是否都大于等于预核扣（即：预核扣或者已核扣）
            if (isNotEmpty(invtHead) && isNotEmpty(invtHead.getApplyNumber())) {
//                List<NemsInvtHead> nemsInvtHeadList = listInvtByApplyNumber(invtHead.getApplyNumber().toString()).getList();
//                boolean allLt = isNotEmpty(nemsInvtHeadList) ? nemsInvtHeadList.stream().allMatch(i -> (isNotBlank(i.getVrfdedMarkcd()) ? Integer.parseInt(i.getVrfdedMarkcd()) : -1) >= 1) : false;
//                if (allLt) {
//                    YmMsg<ApplyNode> applyNodeYmMsg = applyNodeStatusApi.updateApplyNodeStatus(invtHead.getApplyNumber().toString(), 1L, 130L, 2, null);
//                    if (!applyNodeYmMsg.isSuccess()) {
//                        log.info("[updateApplyInvoices]更新报关节点放行状态失败：" + applyNodeYmMsg.getMessage());
//                    }
//                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            ExceptionUtil.getFullStackTrace(e);
            return Result.error("出现异常：" + ExceptionUtil.resExHandle(e).getMessage());
        }
        return Result.ok("操作成功！");
    }

    @Override
    public Result<DecHead> setSeqNoByInvtSeqNo(String invtSeqNo, String seqNo) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if (StringUtils.isBlank(invtSeqNo)) {
            return Result.error("核注单统一编号为空");
        }
        if (StringUtils.isBlank(seqNo)) {
            return Result.error("报关单统一编号为空");
        }
        NemsInvtHead invtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda().eq(NemsInvtHead::getSeqNo, invtSeqNo));
        if (invtHead == null) {
            return Result.error(400, String.format("根据核注单统一编号未找到核注单信息,统一编号为:%s", invtSeqNo));
        }
        DecHead decHead = decHeadMapper.selectOne(new QueryWrapper<DecHead>().lambda().eq(DecHead::getInvId, invtHead.getId()));
        if (decHead == null) {
            return Result.error(400, String.format("根据企业内部编号未找到报关单信息,企业内部编号为:%s", invtHead.getEtpsInnerInvtNo()));
        }
        decHead.setSeqNo(seqNo);
        int n = decHeadMapper.updateById(decHead);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(decHead);
    }

    /**
     * 变更核注清单表体备案序号
     *
     * @param seqNo        统一编号
     * @param putrecSeqnos 表体序号，备案序号
     * @return 核注单信息
     */
    @Override
    public Result<Boolean> updateInvtLists(String seqNo, List<String> putrecSeqnos) {
        return null;
    }

    /**
     * 删除库存列表
     *
     * @param invtListId 库存列表ID
     * @param emsNo      物流公司单号
     * @param gNo        商品编号
     * @param dclQty     商品数量
     * @param type       类型
     * @return Result<?>   结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> delInvtList(String invtListId, String emsNo, String gNo, String dclQty, String type) {
        if (StringUtils.isBlank(invtListId)) {
            return Result.error("未查询到此申报记录，请刷新页面重试");
        }
        //根据核注单表体id查询表体内容
        NemsInvtList nemsInvtList = nemsInvtListMapper.selectById(invtListId);
        if (null == nemsInvtList) {
            return Result.error("未查询到此申报记录，请刷新页面重试");
        }
        try {
            //==========处理手册数据和流水记录
            NemsInvtHead nemsInvtHead = baseMapper.selectById(nemsInvtList.getInvId());

            if (null == nemsInvtHead) {
                return Result.error("未查询到此申报记录，请刷新页面重试");
            }
            BigDecimal dclQtyBg = new BigDecimal(dclQty);
            //要获取手册id
            PtsEmsHead getByEmsNoAndTenantId = emsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getEmsNo, emsNo));
            if (isNotEmpty(getByEmsNoAndTenantId)) {
                PtsEmsAimg getEmsAimgByGno = emsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                        .eq(PtsEmsAimg::getGNo, gNo)
                        .eq(PtsEmsAimg::getEmsId, getByEmsNoAndTenantId.getId()));
                PtsEmsAexg getEmsAexgByGno = emsAexgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAexg>()
                        .eq(PtsEmsAexg::getGNo, gNo)
                        .eq(PtsEmsAexg::getEmsId, getByEmsNoAndTenantId.getId()));
                //类型为料件
                if ("I".equals(type)) {
                    if (isNotEmpty(getEmsAimgByGno)) {
                        List<PtsEmsAimg> emsAimgList = new ArrayList<>();
                        getEmsAimgByGno.setStockQty(dclQtyBg.negate());
                        emsAimgList.add(getEmsAimgByGno);
                        Result<String> increaseBookAimg = emsStockService.increaseBookAimg(emsNo, String.valueOf(nemsInvtHead.getId()), emsAimgList, "26");
                        //如果操作成功需要关联核注单添加流水id
                        if (increaseBookAimg.isSuccess()) {
                            nemsInvtHead.setEmsFlowsId(Long.valueOf(increaseBookAimg.getResult()));
                            baseMapper.updateById(nemsInvtHead);//存入流水id后再更新一下
                        } else {
                            return Result.error("料件操作失败");
                        }
                    }
                } else if ("E".equals(type)) { //类型为成品
                    if (isNotEmpty(getEmsAexgByGno)) {
                        List<PtsEmsAexg> emsAexgList = new ArrayList<>();
                        getEmsAexgByGno.setStockQty(dclQtyBg);
                        emsAexgList.add(getEmsAexgByGno);
                        Result<String> reduceAexg = emsStockService.reduceBookAexg(emsNo, String.valueOf(nemsInvtHead.getId()), emsAexgList, "26");
                        //如果操作成功需要关联核注单添加流水id
                        if (reduceAexg.isSuccess()) {
                            nemsInvtHead.setEmsFlowsId(Long.valueOf(reduceAexg.getResult()));
                            baseMapper.updateById(nemsInvtHead);//存入流水id后再更新一下
                        } else {
                            return Result.error("成品操作失败");
                        }
                    }
                }
            }
            //删除表体操作
            nemsInvtListMapper.deleteById(nemsInvtList.getId());
            //查询还有没有表体
            NemsInvtList nemsInvtListnew = nemsInvtListMapper.selectById(invtListId);
            if (null == nemsInvtListnew) {
                baseMapper.deleteById(invtListId);
            }
            return Result.ok("刪除申报记录成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("操作失败,请重试或联系管理员");
    }

    /**
     * 手动扣除处理方法
     *
     * @param ids             待处理的ID集合
     * @param warehousingDate 正式生效时间
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleManualDeduction(String ids, String warehousingDate, String type) {
        List<NemsInvtHead> nemsInvtHeadList = baseMapper.selectList(new QueryWrapper<NemsInvtHead>().lambda()
                .in(NemsInvtHead::getId, Arrays.asList(ids.split(","))));
        if (isEmpty(nemsInvtHeadList)) {
            return Result.error("未查询到核注单数据！");
        }
        Map<String, PtsEmsHead> emsHeadMap = new HashMap<>();
        Map<Long, List<NemsInvtList>> nemsInvtListMap = new HashMap<>();
        // 2024/2/19 15:07@ZHANGCHAO 追加/变更/完善：提前验证下吧！！
        for (NemsInvtHead nemsInvtHead : nemsInvtHeadList) {
            if (isBlank(nemsInvtHead.getPutrecNo())) {
                return Result.error("核注单的手账册编号不能为空，请检查！");
            }
            PtsEmsHead emsHead = emsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                    .eq(PtsEmsHead::getEmsNo, nemsInvtHead.getPutrecNo()));
            if (isEmpty(emsHead)) {
                return Result.error("系统中不存在编号[" + nemsInvtHead.getPutrecNo() + "]的账册，请检查！");
            }
            emsHeadMap.put(nemsInvtHead.getPutrecNo(), emsHead);
            List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
            if (isEmpty(invtLists)) {
                return Result.error("核注单[" + nemsInvtHead.getId() + "]无表体数据，请检查！");
            } else {
                boolean hasEmtpyGno = invtLists.stream().anyMatch(v -> isEmpty(v.getPutrecSeqno()));
                if (hasEmtpyGno) {
                    return Result.error("核注单[" + nemsInvtHead.getId() + "]表体的备案序号存在空数据，请检查！");
                }
            }
            nemsInvtListMap.put(nemsInvtHead.getId(), invtLists);
            // 手动核扣回填物流账册时用
            if (isBlank(nemsInvtHead.getBondInvtNo())) {
                return Result.error("核注单的清单编号不能为空，请检查！");
            }
            if (isNotBlank(nemsInvtHead.getDclcusTypecd())) {
                if ("1".equals(nemsInvtHead.getDclcusTypecd())) { // 关联报关
                    if (isBlank(nemsInvtHead.getRltEntryNo())) {
                        return Result.error("核注单[" + nemsInvtHead.getId() + "]关联报关单号不能为空，请检查！");
                    }
                } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) { // 对应报关
                    if (isBlank(nemsInvtHead.getEntryNo())) {
                        return Result.error("核注单[" + nemsInvtHead.getId() + "]对应报关单号不能为空，请检查！");
                    }
                }
            } else {
                if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                    if (isBlank(nemsInvtHead.getEntryNo())) {
                        return Result.error("核注单[" + nemsInvtHead.getId() + "]对应报关单号不能为空，请检查！");
                    }
                } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                    if (isBlank(nemsInvtHead.getRltEntryNo())) {
                        return Result.error("核注单[" + nemsInvtHead.getId() + "]关联报关单号不能为空，请检查！");
                    }
                }
            }
        }
        NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy(); // 获取代理对象
        for (NemsInvtHead nemsInvtHead : nemsInvtHeadList) {
            PtsEmsHead emsHead = emsHeadMap.get(nemsInvtHead.getPutrecNo());
            List<NemsInvtList> invtLists = nemsInvtListMap.get(nemsInvtHead.getId());
            // 手动核扣
            if ("1".equals(type)) {
                baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                        .set(NemsInvtHead::getVrfdedMarkcd, "2")
                        .set(NemsInvtHead::getWarehousingDate, warehousingDate)
                        .set(NemsInvtHead::getVrfdedType, "1") // 0:回执核扣 1:手动核扣
                        .eq(NemsInvtHead::getId, nemsInvtHead.getId()));

//                // 加贸账册/加贸手册
//                if (isJMZC(emsHead.getEmsNo()) || isJMSC(emsHead.getEmsNo())) {
//                    // 加贸手账册为备案
//                    // 核注单为进口且料件成品标记为料件时处理料件核增
//                    if ("I".equals(nemsInvtHead.getImpexpMarkcd()) && "I".equals(nemsInvtHead.getMtpckEndprdMarkcd())) {
//                        List<PtsEmsAimg> emsAimgList = new ArrayList<>();
//                        if (invtLists != null && !invtLists.isEmpty()) {
//                            invtLists.forEach(v -> {
//                                PtsEmsAimg emsAimg = new PtsEmsAimg();
//                                emsAimg.setGNo(v.getPutrecSeqno());
//                                emsAimg.setStockQty(v.getDclQty());
//                                emsAimg.setEmsNo(nemsInvtHead.getPutrecNo());
//                                emsAimgList.add(emsAimg);
//                            });
//
//                            Result<?> emsAimgYmMsg = null;
//                            if (isJMZC(emsHead.getEmsNo())) {//账册
//                                emsAimgYmMsg = emsStockService.increaseAimg(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAimgList);
//                            } else if (isJMSC(emsHead.getEmsNo())) {//手册
//                                emsAimgYmMsg = emsStockService.increaseBookAimg(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAimgList,null);
//                            }
//                            if (!emsAimgYmMsg.isSuccess()) {
//                                throw new RuntimeException(emsAimgYmMsg.getMessage());
//    //                                return YmMsg.error(emsAimgYmMsg.getMessage());
//                            }
//                            nemsInvtHead.setEmsFlowsId(Long.valueOf((String) emsAimgYmMsg.getResult()));
//                        }
//                    }
//                    // 核注单为出口且料件成品标记为成品时处理成品核减并扣除相应料件
//                    if ("E".equals(nemsInvtHead.getImpexpMarkcd()) && "E".equals(nemsInvtHead.getMtpckEndprdMarkcd())) {
//                        List<PtsEmsAexg> emsAexgList = new ArrayList<>();
//                        if (invtLists != null && !invtLists.isEmpty()) {
//                            invtLists.forEach(v -> {
//                                PtsEmsAexg emsAexg = new PtsEmsAexg();
//                                emsAexg.setGNo(v.getPutrecSeqno());
//                                emsAexg.setStockQty(v.getDclQty());
//                                emsAexg.setEmsNo(nemsInvtHead.getPutrecNo());
//                                emsAexgList.add(emsAexg);
//                            });
//                            //处理成品数量核减
//                            Result<?> emsAexgYmMsg = new Result<>();
//                            // ZHANGCHAO@2021/5/17 10:05 追加/变更/完善：加贸账册
//                            if (isJMZC(emsHead.getEmsNo())) {
//                                emsAexgYmMsg = emsStockService.reduceAexg(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAexgList);
//                                // ZHANGCHAO@2021/5/17 10:05 追加/变更/完善：加贸手册
//                            } else if (isJMSC(emsHead.getEmsNo())) {
//                                emsAexgYmMsg = emsStockService.reduceBookAexg(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAexgList,null);
//                            }
//                            if (!emsAexgYmMsg.isSuccess()) {
//                                throw new RuntimeException(emsAexgYmMsg.getMessage());
//    //                                return YmMsg.error(emsAexgYmMsg.getMessage());
//                            }
//    //                            nemsInvtHead.setEmsFlowsId(Long.valueOf(emsAexgYmMsg.getData()));
//                        }
//                    }
//                    // 加贸账册-核注单为出口且料件成品标记为料件时核减料件
//                    if (isJMZC(emsHead.getEmsNo()) && "E".equals(nemsInvtHead.getImpexpMarkcd()) && "I".equals(nemsInvtHead.getMtpckEndprdMarkcd())) {
//                        List<PtsEmsAimg> emsAimgList = new ArrayList<>();
//                        if (invtLists != null && !invtLists.isEmpty()) {
//                            invtLists.forEach(v -> {
//                                PtsEmsAimg emsAimg = new PtsEmsAimg();
//                                emsAimg.setGNo(v.getPutrecSeqno());
//                                emsAimg.setStockQty(v.getDclQty());
//                                emsAimg.setEmsNo(nemsInvtHead.getPutrecNo());
//                                emsAimgList.add(emsAimg);
//                            });
//                            Result<?> reduceAimgYmMsg = emsStockService.reduceAimg(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAimgList);
//                            if (!reduceAimgYmMsg.isSuccess()) {
//                                throw new RuntimeException(reduceAimgYmMsg.getMessage());
//    //                                return YmMsg.error(emsAexgYmMsg.getMessage());
//                            }
//    //                            nemsInvtHead.setEmsFlowsId(Long.valueOf(reduceAimgYmMsg.getData()));
//                        }
//                    }
//                    // 物流账册
//                } else if (isBSWL(emsHead.getEmsNo())) {
                // 进口核注单 - 物流账册商品入库记账/核增数量
                if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList invtList : invtLists) {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            Result<?> result = emsStocksFlowService.increaseGoods(stockParamVO);
                            if (!result.isSuccess()) {
                                throw new RuntimeException(result.getMessage());
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                    // 出口核注单 - 核减库存
                } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                    StockParamVO stockParamVO = new StockParamVO();
                    stockParamVO.setNemsInvtHead(nemsInvtHead);
                    for (NemsInvtList invtList : invtLists) {
                        // 2024/3/11 11:49@ZHANGCHAO 追加/变更/完善：针对出入库单生成的核注单，核减库存时要根据关联的出入库单表体ID去处理解除占用！
                        if (isNotBlank(invtList.getStockGoodsId())) {
                            try {
                                Result<?> result = currentProxy.handleOccupyGoods(stockParamVO, invtList, true);
                                if (!result.isSuccess()) {
                                    throw new RuntimeException(result.getMessage());
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e.getMessage());
                            }
                        } else {
                            stockParamVO.setNemsInvtList(invtList);
                            try {
                                Result<?> result = emsStocksFlowService.reduceGoods(stockParamVO);
                                if (!result.isSuccess()) {
                                    throw new RuntimeException(result.getMessage());
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e.getMessage());
                            }
                        }
                    }
                }

                // 2024/2/19 15:36@ZHANGCHAO 追加/变更/完善：回填物流账册数据！！
                String clearanceNo = "";
                if (isNotBlank(nemsInvtHead.getDclcusTypecd())) {
                    if ("1".equals(nemsInvtHead.getDclcusTypecd())) { // 关联报关
                        clearanceNo = nemsInvtHead.getRltEntryNo();
                    } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) { // 对应报关
                        clearanceNo = nemsInvtHead.getEntryNo();
                    }
                } else {
                    if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                        clearanceNo = nemsInvtHead.getEntryNo();
                    } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                        clearanceNo = nemsInvtHead.getRltEntryNo();
                    }
                }
                List<Integer> gnos = invtLists.stream().map(NemsInvtList::getPutrecSeqno).collect(Collectors.toList());
                List<String> copgnos = invtLists.stream().map(NemsInvtList::getGdsMtno).collect(Collectors.toList());

                try {
                    emsAimgMapper.update(null, new UpdateWrapper<PtsEmsAimg>().lambda()
                            .set(PtsEmsAimg::getBondInvtNo, nemsInvtHead.getBondInvtNo())
                            .set(PtsEmsAimg::getClearanceNo, clearanceNo)
                            .set(PtsEmsAimg::getInternalNo, nemsInvtHead.getEtpsInnerInvtNo())
                            .set(PtsEmsAimg::getWarehousingDate, warehousingDate)
                            .eq(PtsEmsAimg::getEmsId, emsHead.getId())
                            .in(PtsEmsAimg::getGNo, gnos)
                            .in(PtsEmsAimg::getCopGno, copgnos));
                } catch (Exception e) {
                    log.error("核注单" + nemsInvtHead.getId() + "回填物流账册时出现异常：" + e.getMessage());
                }

//                    Result<NemsInvtHead> invtHeadYmMsg = increaseOrDecreaseQty(nemsInvtHead, invtLists);
//                    if (!invtHeadYmMsg.isSuccess()) {
//                        throw new RuntimeException(invtHeadYmMsg.getMessage());
//    //                        return invtHeadYmMsg;
//                    }
//                    nemsInvtHead.setEmsFlowsId(invtHeadYmMsg.getResult().getEmsFlowsId());
                //                if (nemsInvtHead.getImpexpMarkcd().equals("E") && isNotEmpty(nemsInvtHead.getStockHeadId())
                //                        &&"2".equals(nemsInvtHead.getVrfdedMarkcd())) {
                //                    //冲正
                //                    Result<String> rectifyYmMsg = stockHeadTypeService.rectifyQtyForStockTypeId(nemsInvtHead.getStockHeadId());
                //                    log.info("!!!!!!!!!!!！！！核注单审核rectifyQtyForStockTypeId：" + rectifyYmMsg.isSuccess());
                //                    log.info("!!!!!!!!!!!！！！核注单审核rectifyQtyForStockTypeId：" + rectifyYmMsg.getMessage());
                //                }
//                } else {
//                    log.info("【核注单手动核扣】" + id + "账册类型错误：" + nemsInvtHead.getPutrecNo());
//                }
//                baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
//                        .set(NemsInvtHead::getEmsFlowsId, nemsInvtHead.getEmsFlowsId())
//                        .eq(NemsInvtHead::getId, id));
                // 取消手动核扣
            } else if ("0".equals(type)) {
                StockParamVO stockParamVO = new StockParamVO();
                stockParamVO.setNemsInvtHead(nemsInvtHead);
                if (I.equals(nemsInvtHead.getImpexpMarkcd())) {
                    stockParamVO.setOptTypeEnum(OptTypeEnum.GOODS_ADD);
                } else if (E.equals(nemsInvtHead.getImpexpMarkcd())) {
                    stockParamVO.setOptTypeEnum(OptTypeEnum.GOODS_REDUCE);
                }
                for (NemsInvtList invtList : invtLists) {
                    // 2024/3/11 11:49@ZHANGCHAO 追加/变更/完善：针对出入库单生成的核注单，核减库存时要根据关联的出入库单表体ID去处理解除占用！
                    if (isNotBlank(invtList.getStockGoodsId())) {
                        try {
                            Result<?> result = currentProxy.handleOccupyGoods(stockParamVO, invtList, false);
                            if (!result.isSuccess()) {
                                throw new RuntimeException(result.getMessage());
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
                    } else {
                        stockParamVO.setNemsInvtList(invtList);
                        try {
                            Result<?> result = emsStocksFlowService.rectify(stockParamVO);
                            if (!result.isSuccess()) {
                                throw new RuntimeException(result.getMessage());
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
                    }
                }
                baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                        .set(NemsInvtHead::getEmsFlowsId, null)
                        .set(NemsInvtHead::getVrfdedMarkcd, "0")
                        .set(NemsInvtHead::getWarehousingDate, null)
                        .set(NemsInvtHead::getVrfdedType, null)
                        .eq(NemsInvtHead::getId, nemsInvtHead.getId()));

                // 2024/2/19 15:36@ZHANGCHAO 追加/变更/完善：回填物流账册数据！！
                try {
                    List<Integer> gnos = invtLists.stream().map(NemsInvtList::getPutrecSeqno).collect(Collectors.toList());
                    List<String> copgnos = invtLists.stream().map(NemsInvtList::getGdsMtno).collect(Collectors.toList());
                    emsAimgMapper.update(null, new UpdateWrapper<PtsEmsAimg>().lambda()
                            .set(PtsEmsAimg::getBondInvtNo, null)
                            .set(PtsEmsAimg::getClearanceNo, null)
                            .set(PtsEmsAimg::getInternalNo, null)
                            .set(PtsEmsAimg::getWarehousingDate, null)
                            .eq(PtsEmsAimg::getEmsId, emsHead.getId())
                            .in(PtsEmsAimg::getGNo, gnos)
                            .in(PtsEmsAimg::getCopGno, copgnos));
                } catch (Exception e) {
                    log.error("核注单" + nemsInvtHead.getId() + "回填物流账册时出现异常：" + e.getMessage());
                }
            }
        }
        return Result.ok("操作成功！");
    }

    /**
     * 针对集报业务的核扣库存处理
     *
     * @param stockParamVO
     * @param invtList
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/3/14 14:26
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> handleOccupyGoods(StockParamVO stockParamVO, NemsInvtList invtList, boolean isOccupy) {
        // 计算实际占用：所有出 - 所有回仓 = 实际占用
        BigDecimal occupyQty = BigDecimal.ZERO;
        Map<StockGoodsType, BigDecimal> idAndQtyMap = new HashMap<>();
        for (String stockGoodsId : invtList.getStockGoodsId().split(",")) {
            // 找出出区表体对应的入区表体
            StockGoodsType stockGoodsType = stockGoodsTypeMapper.selectById(stockGoodsId);
            if (isNotEmpty(stockGoodsType)) {
                occupyQty = occupyQty.add(stockGoodsType.getDclQty());
                idAndQtyMap.put(stockGoodsType, stockGoodsType.getDclQty());
                StockHeadType stockHeadType = stockHeadTypeMapper.selectById(stockGoodsType.getStockId());
                if (isNotEmpty(stockHeadType)) {
                    List<StockHeadType> stockHeadType_I_List = stockHeadTypeMapper.selectList(new LambdaQueryWrapper<StockHeadType>()
                            .eq(StockHeadType::getRltWarehousingId, stockHeadType.getId()));
                    if (isNotEmpty(stockHeadType_I_List)) {
                        List<StockGoodsType> stockGoods_I_List = stockGoodsTypeMapper.selectList(new LambdaQueryWrapper<StockGoodsType>()
                                .in(StockGoodsType::getStockId, stockHeadType_I_List.stream().map(StockHeadType::getId).collect(Collectors.toList())));
                        if (isNotEmpty(stockGoods_I_List)) {
                            Map<String, List<StockGoodsType>> stockGoods_I_Map = stockGoods_I_List.stream().collect(Collectors.groupingBy(StockGoodsType::getOriactGdsSeqno));
                            if (stockGoods_I_Map.containsKey(stockGoodsType.getOriactGdsSeqno())) {
                                // 这些是出区表体ID对应的入区表体，可能多条
                                List<StockGoodsType> stockGoods_Is = stockGoods_I_Map.get(stockGoodsType.getOriactGdsSeqno());
                                if (isNotEmpty(stockGoods_Is)) {
                                    for (StockGoodsType stockGoodsI : stockGoods_Is) {
                                        occupyQty = occupyQty.add(isNotEmpty(stockGoodsI.getDclQty()) ? stockGoodsI.getDclQty().negate() : BigDecimal.ZERO);
                                        idAndQtyMap.put(stockGoodsI, isNotEmpty(stockGoodsI.getDclQty()) ? stockGoodsI.getDclQty().negate() : BigDecimal.ZERO);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info(invtList.getId() + "此核注单表体关联的所有出入库单表体实际占用数量为：" + occupyQty);
        log.info(invtList.getId() + "此核注单表体关联的所有出入库单表体Map：" + idAndQtyMap.keySet().stream().map(i -> i.getId().toString()).collect(Collectors.joining(",")));
        // 核扣核减逻辑
        if (isOccupy) {
            // 分别处理核注单表体关联的出入库单表体的回退占用逻辑
            for (Map.Entry<StockGoodsType, BigDecimal> entry : idAndQtyMap.entrySet()) {
                StockGoodsType stockGoodsType = entry.getKey();
                NemsInvtList invtL = new NemsInvtList();
                BeanUtil.copyProperties(invtList, invtL, CopyOptions.create().ignoreNullValue());
                invtL.setId(stockGoodsType.getId());
                invtL.setDclQty(entry.getValue());
                invtL.setEmsFlowId(stockGoodsType.getEmsFlowsId());
                stockParamVO.setNemsInvtList(invtL);
                try {
                    // 解除占用
                    Result<?> deOccResult = emsStocksFlowService.deOccupyHandleForStockGoods(stockParamVO);
                    if (!deOccResult.isSuccess()) {
                        throw new RuntimeException(deOccResult.getMessage());
                    } else {
                        stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                                .set(StockGoodsType::getEmsFlowsId, null)
                                .eq(StockGoodsType::getId, stockGoodsType.getId()));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            log.info("【核注单已核扣核减占用和库存】计算的要解除占用的数量：" + occupyQty);
            // 最后处理核注单表体的GOODS_REDUCE(3, "物流账册-商品数量核减")逻辑
            stockParamVO.setNemsInvtList(invtList);
            try {
                stockParamVO.setOccupyVar(occupyQty);
                Result<?> result = emsStocksFlowService.reduceGoodsForStockGoods(stockParamVO);
                if (!result.isSuccess()) {
                    throw new RuntimeException(result.getMessage());
                } else {
                    log.info("核注单表体[" + invtList.getId() + "]核减库存成功，流水号：" + result.getResult());
                    return Result.ok("核减库存成功，流水号：" + result.getResult());
                }
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
            // 取消核扣，冲正逻辑
        } else {
            for (Map.Entry<StockGoodsType, BigDecimal> entry : idAndQtyMap.entrySet()) {
                StockGoodsType stockGoodsType = entry.getKey();
                NemsInvtList invtL = new NemsInvtList();
                BeanUtil.copyProperties(invtList, invtL, CopyOptions.create().ignoreNullValue());
                invtL.setId(stockGoodsType.getId());
                invtL.setDclQty(entry.getValue());
                stockParamVO.setNemsInvtList(invtL);
                try {
                    // 重新占用
                    Result<?> occResult = emsStocksFlowService.occupyGoodsForStockGoods(stockParamVO);
                    if (!occResult.isSuccess()) {
                        throw new RuntimeException(occResult.getMessage());
                    } else {
                        stockGoodsTypeMapper.update(null, new UpdateWrapper<StockGoodsType>().lambda()
                                .set(StockGoodsType::getEmsFlowsId, occResult.getResult())
                                .eq(StockGoodsType::getId, stockGoodsType.getId()));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            // 最后处理核注单表体的GOODS_REDUCE(3, "物流账册-商品数量核减")冲正逻辑
            stockParamVO.setNemsInvtList(invtList);
            try {
                stockParamVO.setOptTypeEnum(OptTypeEnum.GOODS_REDUCE);
                Result<?> result = emsStocksFlowService.rectifyForStockGoods(stockParamVO);
                if (!result.isSuccess()) {
                    throw new RuntimeException(result.getMessage());
                } else {
                    log.info("核注单表体[" + invtList.getId() + "]核减库存冲正成功，流水号：" + result.getResult());
                    return Result.ok("核减库存冲正成功，流水号：" + result.getResult());
                }
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        }
    }

    /**
     * 核注单生成报关单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleCreateDecByInvt(String id) {
        /*
         * 此方法的逻辑处理大部分来自悦通关平台苏彪！！
         * 2024/1/18 11:09@ZHANGCHAO         */        NemsInvtHead invtHead = getInvtById(id);
        if (isEmpty(invtHead)) {
            return Result.error("核注单不存在！");
        }        List<DecHead> oldDecHeads = decHeadMapper.selectList(new QueryWrapper<DecHead>().lambda()
                .eq(DecHead::getInvId, id));
        if (isNotEmpty(oldDecHeads)) {
            return Result.error("已生成报关单，不允许重复生成！");
        }        List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                .eq(NemsInvtList::getInvId, id));

        String optUnitName = null;
        String deliverUnitName = null;
        String declareUnitName = null;
        if ("1".equals(invtHead.getDclcusTypecd())) { // 关联报关
            optUnitName = invtHead.getRltEntryBizopEtpsNm();
            deliverUnitName = invtHead.getRltEntryRcvgdEtpsNm();
            declareUnitName = invtHead.getRltEntryDclEtpsNm();
        } else if ("2".equals(invtHead.getDclcusTypecd())) { // 对应报关
            optUnitName = invtHead.getBizopEtpsNm();
            deliverUnitName = invtHead.getRcvgdEtpsNm();
            declareUnitName = invtHead.getCorrEntryDclEtpsNm();
        }
        // 境内收发货人
        List<CustomerEnterprise> optUnitCustomers = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, optUnitName));
        CustomerEnterprise optUnitCustomer = isNotEmpty(optUnitCustomers) ? optUnitCustomers.get(0) : null;
        // 消费使用单位
        List<CustomerEnterprise> deliverUnitCustomers = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, deliverUnitName));
        CustomerEnterprise deliverUnitCustomer = isNotEmpty(deliverUnitCustomers) ? deliverUnitCustomers.get(0) : null;
        // 申报单位
        List<CustomerEnterprise> declareUnitNames = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, declareUnitName));
        CustomerEnterprise declareUnitCustomer = isNotEmpty(declareUnitNames) ? declareUnitNames.get(0) : null;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        DecHead decHead = new DecHead();
        //20241030追加 根据前端录入的报关单字段继续追加到报关单
        BeanUtil.copyProperties(invtHead,decHead,"status");

        decHead.setId(IdWorker.getIdStr());
        decHead.setAudited(false);
        decHead.setSend("0");
        decHead.setCreateTime(new Date());
        decHead.setSynchronism(false);
        decHead.setCustomsCode("D" + decHead.getId());
        decHead.setCreatePerson(loginUser.getUsername()); // 录入人
        decHead.setUpdateBy(null);
        decHead.setUpdateDate(null);

        decHead.setInvId(String.valueOf(invtHead.getId()));
        decHead.setSeqNo(invtHead.getImportSeqNo());//统一编号
        decHead.setEtpsInnerInvtNo(invtHead.getEtpsInnerInvtNo());
        decHead.setShipTypeCode(invtHead.getTrspModecd());//运输方式
        decHead.setDclTenantId(TenantContext.getTenant());// 申请人租户ID
        decHead.setRecordNumber(invtHead.getPutrecNo());//备案号
        decHead.setOutPortCode(invtHead.getImpexpPortcd());//进出境关别
        decHead.setTradeTypeCode(invtHead.getSupvModecd());//监管方式
        //转换数字到字母
        if(isNotBlank(invtHead.getStshipTrsarvNatcd())){
            List<ErpCountries> erpCountries = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                    .eq(ErpCountries::getCode, invtHead.getStshipTrsarvNatcd()));
            if(erpCountries.size()>0){
                List<ErpCountries> erpCountries2 = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                        .eq(ErpCountries::getName, erpCountries.get(0).getName())
                        .eq(ErpCountries::getIsenabled,"1"));
                decHead.setArrivalArea(erpCountries2.size()>0?erpCountries2.get(0).getCode():"");//运抵国
            }
        }
        //申报单位
        decHead.setDeclareUnit(invtHead.getDclEtpsno());
        decHead.setDeclareUnitName(invtHead.getDclEtpsNm());
        decHead.setDeclareUnitSocialCode(invtHead.getDclEtpsSccd());
        //转换贸易国 从数字到字母
        if(isNotBlank(invtHead.getTradeCountry())){
            List<ErpCountries> erpCountries = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                    .eq(ErpCountries::getCode, invtHead.getTradeCountry()));
            if(erpCountries.size()>0){
                List<ErpCountries> erpCountries2 = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                        .eq(ErpCountries::getName, erpCountries.get(0).getName())
                        .eq(ErpCountries::getIsenabled,"1"));
                decHead.setTradeCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():"");//贸易国
            }
        }


        if ("1".equals(invtHead.getDclcusTypecd())){//关联
            decHead.setOptUnitId(invtHead.getRltEntryBizopEtpsno());//境内收发货人（经营单位）
            decHead.setOptUnitName(invtHead.getRltEntryBizopEtpsNm());
            decHead.setOptUnitSocialCode(invtHead.getRltEntryBizopEtpsSccd());
            decHead.setDeliverUnit(invtHead.getRltEntryRcvgdEtpsno());//消费使用单位（加工单位）
            decHead.setDeliverUnitName(invtHead.getRltEntryRcvgdEtpsNm());
            decHead.setDeliverUnitSocialCode(invtHead.getRltEntryRvsngdEtpsSccd());
            decHead.setDeclareUnit(invtHead.getRltEntryDclEtpsno());//申报企业
            decHead.setDeclareUnitName(invtHead.getRltEntryDclEtpsNm());
            decHead.setDeclareUnitSocialCode(invtHead.getRltEntryDclEtpsSccd());
        }else if ("2".equals(invtHead.getDclcusTypecd())){
            decHead.setOptUnitId(invtHead.getBizopEtpsno());//境内收发货人（经营单位）
            decHead.setOptUnitName(invtHead.getBizopEtpsNm());
            decHead.setOptUnitSocialCode(invtHead.getBizopEtpsSccd());
            decHead.setDeliverUnit(invtHead.getRcvgdEtpsno());//消费使用单位（加工单位）
            decHead.setDeliverUnitName(invtHead.getRcvgdEtpsNm());
            decHead.setDeliverUnitSocialCode(invtHead.getRvsngdEtpsSccd());
            decHead.setDeclareUnit(invtHead.getCorrEntryDclEtpsno());//申报企业
            decHead.setDeclareUnitName(invtHead.getCorrEntryDclEtpsNm());
            decHead.setDeclareUnitSocialCode(invtHead.getCorrEntryDclEtpsSccd());
        }        if (StrUtil.isNotEmpty(invtHead.getDclcusTypecd())){
            decHead.setTradeCiqCode(optUnitCustomer != null ? optUnitCustomer.getCiqCode() : null);//境内收发货人检验检疫编码
            decHead.setOwnerCiqCode(deliverUnitCustomer != null ? deliverUnitCustomer.getCiqCode() : null);//消费使用单位检验检疫编码
            decHead.setDeclCiqCode(declareUnitCustomer != null ? declareUnitCustomer.getCiqCode() : null);//申报企业检验检疫编码
        }

//        decHead.setIeFlag(invtHead.getImpexpMarkcd());//进出口
        decHead.setDeclarePlace(invtHead.getDclplcCuscd());//申报地海关
        String clearanceNo = null;
        if ("1".equals(invtHead.getDclcusFlag())){
            if ("1".equals(invtHead.getDclcusTypecd())){//关联
                clearanceNo = invtHead.getRltEntryNo();
            }else if ("2".equals(invtHead.getDclcusTypecd())){//对应
                clearanceNo = invtHead.getEntryNo();
            }        }        decHead.setClearanceNo(clearanceNo);

        // 根据类型判断报关单的进出口和类型
        if(null==invtHead.getDecType()){
            invtHead.setDecType("");
        }        judgeDdeclarationType(invtHead.getDecType(),decHead);

        //全部币制信息
        List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
        List<ErpCountries> erpCountries = erpCountriesMapper.selectList(null);
        Map<Integer, DecList> decListMap = new HashMap<>();
        int i = 1;
        for (NemsInvtList v : invtLists) {
            if (isEmpty(v.getEntryGdsSeqno())) {
                return Result.error("核注单表体项的「报关单商品序号」有空值，请检查！");
            }            if (decListMap.containsKey(v.getEntryGdsSeqno())) {
                //申报数量
                if (decListMap.get(v.getEntryGdsSeqno()).getGoodsCount() == null || v.getDclQty() == null){
                    return Result.error("核注单表体项的「申报数量」有空值，请检查！");
                } else {
                    decListMap.get(v.getEntryGdsSeqno())
                            .setGoodsCount(decListMap.get(v.getEntryGdsSeqno()).getGoodsCount() != null
                                    ? decListMap.get(v.getEntryGdsSeqno()).getGoodsCount().add(v.getDclQty()) : v.getDclQty());
                }
                //法定第一数量
                if (decListMap.get(v.getEntryGdsSeqno()).getCount1() == null || v.getLawfQty() == null){
                    return Result.error("核注单表体项的「法定第一数量」有空值，请检查！");
                } else {
                    decListMap.get(v.getEntryGdsSeqno())
                            .setCount1(decListMap.get(v.getEntryGdsSeqno()).getCount1().add(v.getLawfQty()));
                }
                //法定第二数量
                decListMap.get(v.getEntryGdsSeqno())
                        .setCount2(decListMap.get(v.getEntryGdsSeqno()).getCount2() != null
                                ? decListMap.get(v.getEntryGdsSeqno()).getCount2().add(v.getSecdLawfQty()) : v.getSecdLawfQty());
                //总价
                if (decListMap.get(v.getEntryGdsSeqno()).getTotal() == null || v.getDclTotalamt() == null){
                    return Result.error("核注单表体项的「总价」有空值，请检查！");
                } else {
                    decListMap.get(v.getEntryGdsSeqno())
                            .setTotal(decListMap.get(v.getEntryGdsSeqno()).getTotal().add(v.getDclTotalamt()));
                    //单价
                    decListMap.get(v.getEntryGdsSeqno()).setPrice(
                            decListMap.get(v.getEntryGdsSeqno()).getTotal().divide(decListMap.get(v.getEntryGdsSeqno()).getGoodsCount(), 4, RoundingMode.HALF_UP));
                }
                //净重
                if (decListMap.get(v.getEntryGdsSeqno()).getNetWeight() != null && v.getNetWt() != null){
                    decListMap.get(v.getEntryGdsSeqno())
                            .setNetWeight(decListMap.get(v.getEntryGdsSeqno()).getNetWeight().add(v.getNetWt()));
                    decHead.setNetWeight(decHead.getNetWeight().add(v.getNetWt()));
                }            }else{
                DecList decList = new DecList();
                decList.setId(null);
                decList.setItem(i);
                toDecList(v, decList, decHead.getId(), invtHead);
                //转换币值
                if(isNotBlank(v.getDclCurrcd())){
                    List<ErpCurrencies> erpCurrencies1=erpCurrencies.stream().filter(s->s.getCode().equals(v.getDclCurrcd()))
                            .collect(Collectors.toList());
                    decList.setCurrencyCode(erpCurrencies1.size()>0?erpCurrencies1.get(0).getCurrency():null);
                }                //转换国别地区
                if(isNotBlank(v.getNatcd())){
                    List<ErpCountries> erpCountries1= erpCountries.stream().filter(z->z.getCode().equals(v.getNatcd()))
                            .collect(Collectors.toList());
                    if(erpCountries1.size()>0){
                        List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                        &&o.getIsenabled()==1)
                                .collect(Collectors.toList());
                        decList.setDestinationCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                    }                }
                if(isNotBlank(v.getOriginCountry())){
                    List<ErpCountries> erpCountries1= erpCountries.stream().filter(p->p.getCode().equals(v.getOriginCountry()))
                            .collect(Collectors.toList());
                    if(erpCountries1.size()>0){
                        List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                        &&o.getIsenabled()==1)
                                .collect(Collectors.toList());
                        decList.setDesCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                    }
                }


                if (decHead.getNetWeight() == null) {
                    decHead.setNetWeight(decList.getNetWeight());
                } else {
                    BigDecimal netWeight = decList.getNetWeight() == null ? BigDecimal.ZERO.stripTrailingZeros() : decList.getNetWeight();
                    decHead.setNetWeight(decHead.getNetWeight().add(netWeight));
                }                decListMap.put(v.getEntryGdsSeqno(),decList);
                i++;
            }        }        List<DecList> decLists = new ArrayList<>(decListMap.values());
        decHead.setDecLists(decLists);

        //生成随附单证
        DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
        decLicenseDocus.setDecId(decHead.getId());
        decLicenseDocus.setDocuCode("a");
        decLicenseDocus.setCertCode(invtHead.getBondInvtNo());

        if ("I".equals(decHead.getIeFlag())){
            decHead.setOutDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }
        decHeadMapper.insert(decHead);
        decListService.saveBatch(decLists);
        decLicenseDocusMapper.insert(decLicenseDocus);

        return Result.ok(decHead);
    }

    /**
     * 核注单生成核放单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleCreatePassByInvt(String id) {
        /*
         * 此方法的逻辑处理大部分来自悦通关平台苏彪！！
         * 2024/1/18 11:09@ZHANGCHAO
         */
        NemsInvtHead invtHead = getInvtById(id);
        if (isEmpty(invtHead)) {
            return Result.error("核注单不存在！");
        }
        if (isNotEmpty(invtHead) && invtHead.getCreatePassPort()) {
            return Result.error("该核注单已生成核放单，无法重复生成！");
        }
        List<Long> dclcusFlags = new ArrayList<>();
        List<Long> notDclcusFlags = new ArrayList<>();

        if (isNotBlank(invtHead.getDclcusFlag()) && "1".equals(invtHead.getDclcusFlag())) {
            dclcusFlags.add(invtHead.getId()); // 报关
        } else {
            notDclcusFlags.add(invtHead.getId()); // 非报关
        }

        Map<String, DecHead> decHeadMap = new HashMap<>();
        Map<Long, NemsInvtList> invtListMap = new HashMap<>();
        if (isNotEmpty(dclcusFlags)) {
            List<DecHead> decHeads = decHeadMapper.selectList(new QueryWrapper<DecHead>().lambda()
                    .in(DecHead::getInvId, dclcusFlags));
            if (isNotEmpty(decHeads)) {
                decHeadMap = decHeads.stream().collect(Collectors.toMap(DecHead::getInvId, a -> a, (k1, k2) -> k1));
            }
        }
        if (isNotEmpty(notDclcusFlags)) {
            List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                    .in(NemsInvtList::getInvId, notDclcusFlags));
            if (isNotEmpty(nemsInvtLists)) {
                for (NemsInvtList invtList : nemsInvtLists) {
                    if (invtListMap.containsKey(invtList.getInvId())) {
                        NemsInvtList nemsInvtList = invtListMap.get(invtList.getInvId());
                        BigDecimal netWt = invtList.getNetWt() == null ? BigDecimal.ZERO : invtList.getNetWt();
                        BigDecimal grossWt = invtList.getGrossWt() == null ? BigDecimal.ZERO : invtList.getGrossWt();
                        nemsInvtList.setNetWt(nemsInvtList.getNetWt().add(netWt));
                        nemsInvtList.setGrossWt(nemsInvtList.getGrossWt().add(grossWt));
                        invtListMap.put(invtList.getInvId(), nemsInvtList);
                    } else {
                        if (invtList.getGrossWt() == null) {
                            invtList.setGrossWt(BigDecimal.ZERO);
                        }
                        if (invtList.getNetWt() == null) {
                            invtList.setNetWt(BigDecimal.ZERO);
                        }
                        invtListMap.put(invtList.getInvId(), invtList);
                    }
                }
            }
        }

//        List<PassPortHead> passPortHeads = new ArrayList<>();
//        for (NemsInvtHead invtHead: nemsInvtHeads) {
        PassPortHead passPortHead = new PassPortHead();
        createPassPortHeadByInvtHead(passPortHead, invtHead, decHeadMap, invtListMap);
//            pa.add(passPortHead);
//        }
        passPortHeadMapper.insert(passPortHead);
        baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                .set(NemsInvtHead::getCreatePassPort, true)
                .eq(NemsInvtHead::getId, id));

        return Result.ok(passPortHead);
    }

    /**
     * 核注单推送报文
     *
     * @param ids
     * @param passageway
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/30 9:15
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handlePush(String ids, String passageway) {
        StringBuilder returnMsg = new StringBuilder();
        StringBuilder successMsg = new StringBuilder();
        Set<String> successIds = new HashSet<>(16);
        Set<String> errorIds = new HashSet<>(16);
        for (String id : ids.split(",")) {
            try {
                // 防止事务失效！！
                NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy(); // 获取代理对象
                Result<?> result = currentProxy.sendMessage(id, passageway);
                if (result.isSuccess()) {
                    successIds.add(id);
                    returnMsg.append(result.getResult());
                } else {
                    errorIds.add(id);
                    returnMsg.append("核注单[").append(id).append("]推送报文出现异常：").append(result.getMessage()).append(";");
                }
            } catch (Exception e) {
                e.printStackTrace();
                ExceptionUtil.getFullStackTrace(e);
                returnMsg.append("核注单[").append(id).append("]推送报文出现异常：").append(e.getMessage()).append(";");
                errorIds.add(id);
            }
        }
        String msg = "共" + ids.split(",").length + "票核注单，成功数："
                + successIds.size() + (isNotEmpty(errorIds) ? ("，失败数："
                + errorIds.size() + "，原因：" + returnMsg) : "");
        return Result.ok(msg);
    }

    /**
     * 根据部门编码、社会编码或部门名称获取企业信息
     *
     * @param flag       标志位，取值为"deptCode"、"socialCode"或"deptName"
     * @param searchText 搜索关键字
     * @return 企业信息
     */
    @Override
    public Result<?> getEnterpriseByDepartcdOrSocialCodeOrDepartName(String flag, String searchText) {
        List<CustomerEnterprise> customerEnterprises = customerEnterpriseMapper.selectList(new QueryWrapper<CustomerEnterprise>().lambda()
                .eq(CustomerEnterprise::getSocialCode, searchText).or()
                .eq(CustomerEnterprise::getDepartcd, searchText).or().eq(CustomerEnterprise::getDepartName, searchText));
        CustomerEnterprise customerEnterprise = customerEnterprises != null && !customerEnterprises.isEmpty()
                ? customerEnterprises.get(0) : null;
        return Result.ok(customerEnterprise);
    }

    /**
     * 获取四个统计平方数的结果。
     *
     * @return 四个统计平方数的结果
     */
    @Override
    public Result<?> getFourStatisticalSquares() {
        StatisticiansDTO staticStatistics = new StatisticiansDTO();
        String yearMonth = DateUtil.format(new Date(), DatePattern.NORM_MONTH_PATTERN);
        Long count = baseMapper.selectCount(new LambdaQueryWrapper<NemsInvtHead>()
                .eq(NemsInvtHead::getImpexpMarkcd, I)
                .apply("date_format(CREATE_DATE,'%Y-%m') = '" + yearMonth + "'"));
        staticStatistics.setSbrkQty(isNotEmpty(count) ? BigDecimal.valueOf(count).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        Long count1 = baseMapper.selectCount(new LambdaQueryWrapper<NemsInvtHead>()
                .eq(NemsInvtHead::getImpexpMarkcd, E)
                .apply("date_format(CREATE_DATE,'%Y-%m') = '" + yearMonth + "'"));
        staticStatistics.setSbckQty(isNotEmpty(count1) ? BigDecimal.valueOf(count1).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        Long count2 = storageInfoMapper.selectCount(new LambdaQueryWrapper<StorageInfo>()
                .eq(StorageInfo::getIeFlag, I)
                .apply("date_format(CREATE_DATE,'%Y-%m') = '" + yearMonth + "'"));
        staticStatistics.setSjrkQty(isNotEmpty(count2) ? BigDecimal.valueOf(count2).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        Long count3 = storageInfoMapper.selectCount(new LambdaQueryWrapper<StorageInfo>()
                .eq(StorageInfo::getIeFlag, E)
                .apply("date_format(CREATE_DATE,'%Y-%m') = '" + yearMonth + "'"));
        staticStatistics.setSjckQty(isNotEmpty(count3) ? BigDecimal.valueOf(count3).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        return Result.OK(staticStatistics);
    }

    /**
     * 根据账册号获取清单数据
     *
     * @param page
     * @param emsNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    @Override
    public IPage<NemsInvtHead> listInvtByEmsNo(Page<NemsInvtHead> page, String emsNo, String putrecSeqno, String impexpMarkcd) {
        return baseMapper.listInvtByEmsNo(page, emsNo, putrecSeqno, impexpMarkcd);
    }

    /**
     * 根据账册号获取报关数据
     *
     * @param page
     * @param emsNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    @Override
    public IPage<DecHead> listDecByEmsNo(Page<DecHead> page, String emsNo, String putrecSeqno, String impexpMarkcd) {
        return baseMapper.listDecByEmsNo(page, emsNo, putrecSeqno, impexpMarkcd);
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/4 10:52
     */
    @Override
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String imSign = request.getParameter("imSign");
        if (isBlank(imSign)) {
            return Result.error("未知的进出口类型！");
        }
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        String[] sheetNames = {"Sheet1"};
        Map<String, Class<?>> pojoClassMap = new HashMap<>();
        pojoClassMap.put(sheetNames[0], NemsInvtHeadEntity.class);
//        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
        // 获取第一个键值对，只有1个文件
        Map.Entry<String, MultipartFile> firstEntry = fileMap.entrySet().iterator().next();
        MultipartFile file = firstEntry.getValue();// 获取上传文件对象
        try {
            // 检查sheet名称是否符合要求
            if (checkNotExistExcelForInvt(file.getInputStream())) {
                return Result.error("未识别的Excel模版文件，请检查模版类型！");
            }
            String type = getInvtType(file.getInputStream());
            ImportParams params = new ImportParams();
            params.setTitleRows(4);
            params.setNeedSave(false);
            params.setSheetNum(1); // 升级autopoi版本！！
            // 导入核注单
            ExcelImportResult<?> excelImportResult = ExcelImportUtil.importExcelVerify(file.getInputStream(), pojoClassMap, sheetNames, params, type);
            log.info("excelImportResult==> {}", excelImportResult.getList());
            NemsInvtHead invtHead = getFixImportInvtData(excelImportResult.getList());
            getImpexpMarkcdForInvt(invtHead, file.getInputStream());
            if (isEmpty(invtHead)) {
                throw new RuntimeException("核注单表头导入失败！");
            }
            if (!invtHead.getImpexpMarkcd().equalsIgnoreCase(imSign)) {
                return Result.error("请导入" + (imSign.equalsIgnoreCase("I") ? "进口" : "出口") + "类型的核注单！");
            }
            invtHead.setId(IdWorker.getId());
            invtHead.setDclTypecd("1");
            invtHead.setCreatePassPort(false);
            invtHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
            invtHead.setDclTenantId(Long.valueOf(TenantContext.getTenant()));
            if (StrUtil.isBlank(invtHead.getEtpsInnerInvtNo())) {
                invtHead.setEtpsInnerInvtNo(new StringBuffer().append("H").append(invtHead.getId()).toString());
            }
            if (invtHead.getCreateDate() == null) {
                invtHead.setCreateDate(new Date());
            }
            if (invtHead.getInputTime() == null) {
                invtHead.setInputTime(new Date());
            }
            if (StrUtil.isBlank(invtHead.getCreatePerson())) {
                invtHead.setCreatePerson(loginUser.getUsername());
            }
            if (invtHead.getSend() == null) {
                invtHead.setSend(false);
            }
            if (invtHead.getAudited() == null) {
                invtHead.setAudited(false);
            }
            if (StrUtil.isBlank(invtHead.getVrfdedMarkcd())) {
                invtHead.setVrfdedMarkcd("0");
            }

            if (isNotEmpty(excelImportResult.getVerfiyMsg())) {
                errorMessage.addAll(excelImportResult.getVerfiyMsg());
            }

            NemsInvtHead nemsInvtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda()
                    .eq(NemsInvtHead::getEtpsInnerInvtNo, invtHead.getEtpsInnerInvtNo()));
            if (nemsInvtHead != null) {
                return Result.error("企业内部编号重复，请重新修改");
            }
            if ("其他运输".equals(invtHead.getTrspModecd())) {
                invtHead.setTrspModecd("9");
            }
            log.info("invtHead==> {}", JSON.toJSONString(invtHead));
            int successRows = baseMapper.insert(invtHead);

            Map<Integer, PtsEmsAimg> emsAimgMap = new HashedMap();
            List<String> gNoList = new ArrayList<>();

            if (isNotEmpty(invtHead.getNemsInvtLists())) {
                int line = 0;
                for (NemsInvtList nemsInvtList : invtHead.getNemsInvtLists()) {
                    nemsInvtList.setInvId(invtHead.getId());
                    try {
                        successRows += nemsInvtListMapper.insert(nemsInvtList);
                    } catch (Exception e) {
                        e.printStackTrace();
                        errorMessage.add("核注单单表体：第 " + (line + 1) + " 行：出现错误(" + ExceptionUtil.resExHandle(e).getMessage() + ")，忽略导入");
                    }
                    line++;
//                    if (invtHead.getVrfdedMarkcd() != null && "0/1".contains(invtHead.getVrfdedMarkcd())
//                            && isNotEmpty(invtHead.getPutrecNo()) && "L".contains(invtHead.getPutrecNo().substring(0, 1))
//                            && "E".equals(invtHead.getImpexpMarkcd())) {
//                        if (emsAimgMap.containsKey(nemsInvtList.getPutrecSeqno())) {
//                            PtsEmsAimg emsAimg = emsAimgMap.get(nemsInvtList.getPutrecSeqno());
//                            emsAimg.setOccupyQty(emsAimg.getOccupyQty().add(nemsInvtList.getDclQty()));
//                            emsAimgMap.put(nemsInvtList.getPutrecSeqno(), emsAimg);
//                        } else {
//                            PtsEmsAimg emsAimg = new PtsEmsAimg();
//                            emsAimg.setGNo(nemsInvtList.getPutrecSeqno());
//                            emsAimg.setOccupyQty(nemsInvtList.getDclQty());
//                            emsAimg.setEmsNo(invtHead.getPutrecNo());
//                            emsAimgMap.put(nemsInvtList.getPutrecSeqno(), emsAimg);
//                            gNoList.add(nemsInvtList.getPutrecSeqno().toString());
//                        }
//                    }

                }
            }
            //处理核注单数量占用（只占用未核扣和预核扣，已核扣由用户手动审核处理）且只有保税仓库的账册处理
//            Boolean choise = false;
//            String gNos = "";
//            if (invtHead.getVrfdedMarkcd() != null && "0/1".contains(invtHead.getVrfdedMarkcd())
//                    && isNotEmpty(invtHead.getPutrecNo()) && "L".contains(invtHead.getPutrecNo().substring(0, 1))
//                    && emsAimgMap != null && !emsAimgMap.isEmpty() && "E".equals(invtHead.getImpexpMarkcd())) {
//                List<PtsEmsAimg> emsAimgList = emsAimgMapper.listAimgList(nemsInvtHead.getPutrecNo(), gNoList);
//                if (isEmpty(emsAimgList) || emsAimgList.size() != gNoList.size()) {
//                    throw new RuntimeException("有未存在于账册的备案序号，请核实数据");
//                }
//
//                for (PtsEmsAimg v : emsAimgList) {
//                    if (emsAimgMap.containsKey(v.getGNo())) {
//                        BigDecimal qty = v.getStockQty().subtract(v.getOccupyQty());
//                        BigDecimal occupyQty = emsAimgMap.get(v.getGNo()).getOccupyQty();
//                        if (occupyQty.compareTo(qty) > 0) {
//                            if (isNotEmpty(gNos)) {
//                                gNos = new StringBuilder(gNos).append(",").append(v.getGNo()).toString();
//                            } else {
//                                gNos = v.getGNo().toString();
//                            }
//                            choise = true;
//                        }
//                    }
//                }
//                for (NemsInvtList invtList : invtHead.getNemsInvtLists()) {
//                    StockParamVO stockParamVO = new StockParamVO();
//                    stockParamVO.setNemsInvtHead(invtHead);
//                    stockParamVO.setNemsInvtList(invtList);
//                    try {
//                        // 解除占用
//                        Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
//                        if (!deOccResult.isSuccess()) {
//                            throw new RuntimeException(deOccResult.getMessage());
//                        }
//                        // 占用
//                        Result<?> result = emsStocksFlowService.occupyGoods(stockParamVO);
//                        if (!result.isSuccess()) {
//                            throw new RuntimeException(result.getMessage());
//                        } else {
//                            StringBuilder msg = new StringBuilder();
//                            msg.append("表体[备案序号：").append(invtList.getPutrecSeqno()).append("，物料号：")
//                                    .append(invtList.getGdsMtno()).append("]已核增占用数量：").append(stockParamVO.getNemsInvtList().getDclQty()).append("；");
//                            log.info(msg.toString());
//                        }
//                    } catch (Exception e) {
//                        throw new RuntimeException(e.getMessage());
//                    }
//                }
//            }
//            if (choise) {
//                Result<String> resultYmMsg = ExcelImportUtil.importResp(excelImportResult.getList().size(), successRows,
//                        excelImportResult.getList().size() - successRows, errorMessage);
//                String message = "存在库存不足的料件,备案序号为：" + gNos;
//                if (isNotEmpty(resultYmMsg.getMessage())) {
//                    message = new StringBuilder(resultYmMsg.getMessage()).append(",").append(message).toString();
//                }
//                resultYmMsg.setMessage(message);
//                return resultYmMsg;
//            }
            return ExcelImportUtil.importResp(excelImportResult.getList().size(), successRows,
                    excelImportResult.getList().size() - successRows, errorMessage);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("文件导入失败：" + e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
                log.error(e.getMessage(), e);
            }
        }
//        }
    }

    @Override
    public Result<NemsInvtHead> setSeqNoByEtpsNoOrId(String id, String etpsNo, String seqNo) {
        if (StringUtils.isBlank(etpsNo)) {
            return Result.error("企业内部编号为空");
        }
        NemsInvtHead invtHead = null;
        invtHead = baseMapper.selectById(id);
        if (isEmpty(invtHead)) {
            invtHead = baseMapper.selectOne(new LambdaQueryWrapper<NemsInvtHead>().eq(NemsInvtHead::getEtpsInnerInvtNo, etpsNo));
        }
        if (isEmpty(invtHead)) {
            return Result.error("未找到对应的核注单！id:{" + id + "},etpsNo:{" + etpsNo + "}");
        }
        invtHead.setSeqNo(seqNo);
        baseMapper.updateById(invtHead);
//        int rows = nemsInvtListMapper.updateSeqNo(invtHead.getEtpsInnerInvtNo(),seqNo);
        nemsInvtListMapper.update( null, new UpdateWrapper<NemsInvtList>().lambda()
                .set(NemsInvtList::getEtpsInnerInvtNo, invtHead.getEtpsInnerInvtNo())
                .set(NemsInvtList::getSeqNo, invtHead.getSeqNo())
                .eq(NemsInvtList::getInvId, invtHead.getId()));
        return Result.ok(invtHead);
    }

    /**
     * @param id
     * @param etpsNo
     * @param seqNo
     * @return
     */
    @Override
    public Result<NemsInvtHead> setSeqNoByEtpsNoOrIdForRecepit(String id, String etpsNo, String seqNo) {
        if (StringUtils.isBlank(etpsNo)) {
            return Result.error("企业内部编号为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        NemsInvtHead invtHead = null;
        invtHead = baseMapper.selectById(id);
        if (isEmpty(invtHead)) {
            invtHead = baseMapper.selectOne(new LambdaQueryWrapper<NemsInvtHead>().eq(NemsInvtHead::getEtpsInnerInvtNo, etpsNo));
        }
        if (isEmpty(invtHead)) {
            return Result.error("未找到对应的核注单！id:{" + id + "},etpsNo:{" + etpsNo + "}");
        }
        invtHead.setSeqNo(seqNo);
        baseMapper.updateById(invtHead);
//        int rows = nemsInvtListMapper.updateSeqNo(invtHead.getEtpsInnerInvtNo(),seqNo);
        nemsInvtListMapper.update( null, new UpdateWrapper<NemsInvtList>().lambda()
                .set(NemsInvtList::getEtpsInnerInvtNo, invtHead.getEtpsInnerInvtNo())
                .set(NemsInvtList::getSeqNo, invtHead.getSeqNo())
                .eq(NemsInvtList::getInvId, invtHead.getId()));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(invtHead);
    }

    @Override
    public Result<NemsInvtHead> updateInvtPartBySeqNo(String seqNo, Date invtDclTime, Date entryDclTime, String vrfdedMarkcd,
                                                     String businessId, String invtIochkptStucd, String invtStatus) {
        if (StringUtils.isEmpty(seqNo)) {
            return Result.error("统一编号为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        NemsInvtHead invtHead = baseMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda().eq(NemsInvtHead::getSeqNo, seqNo));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (invtDclTime != null) {
            invtHead.setInvtDclTime(invtDclTime);
            log.info("updateInvtPartBySeqNo回填出入库单关联清单申报时间判断："+invtHead.getStockHeadId());
            if (isNotEmpty(invtHead.getStockHeadId())){
                stockHeadTypeMapper.update(null,new UpdateWrapper<StockHeadType>().lambda()
                        .set(StockHeadType::getRltInvtDclTime,invtDclTime).eq(StockHeadType::getInvtHeadId,invtHead.getId()));
                //更新相关关联出入库清单的申报日期（出口核注单）
//                if("E".equals(invtHead.getImpexpMarkcd())){
//                    StockHeadType stockHeadType=stockHeadTypeMapper.selectOne(new LambdaQueryWrapper<StockHeadType>()
//                            .eq(StockHeadType::getInvtHeadId,invtHead.getId()));
//                    if(null!=stockHeadType){
//                        List<StockGoodsType> stockGoodsTypeList=stockGoodsTypeMapper.selectList(
//                                new LambdaQueryWrapper<StockGoodsType>()
//                                        .eq(StockGoodsType::getStockId,stockHeadType.getId()));
//                        if(stockGoodsTypeList.size()>0){
//                            for(StockGoodsType stockGoodsType:stockGoodsTypeList){
//                                stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
//                                        .set(StockTypeImport::getInvtDeclarationDate,invtDclTime)
//                                        .set(StockTypeImport::getUniversalStatus,"0")//还原推送标识
//                                        .eq(StockTypeImport::getMrNo,stockGoodsType.getMrNo())
//                                        .eq(StockTypeImport::getInvoicePartNo,stockGoodsType.getGdsMtno()));
//                            }
//                        }
//                    }
//
//                }
            }
        }
        if (StringUtils.isNotEmpty(invtIochkptStucd)) {
            invtHead.setInvtIochkptStucd(invtIochkptStucd);
        }
        if (entryDclTime != null) {
            invtHead.setEntryDclTime(entryDclTime);
            //变更报关单申报日期
            DecHead decHead = decHeadMapper.selectOne(new QueryWrapper<DecHead>().lambda().eq(DecHead::getInvId, invtHead.getId()));
            if (decHead != null) {
                decHead.setAppDate(entryDclTime);
                decHeadMapper.updateById(decHead);

            }

        }
        if (StringUtils.isNotBlank(vrfdedMarkcd)) {
            invtHead.setVrfdedMarkcd(vrfdedMarkcd);
        }
        if (StringUtils.isNotBlank(invtStatus)) {
            invtHead.setStatus(invtStatus);
        }
        if (StringUtils.isNotBlank(businessId)) {
            invtHead.setBondInvtNo(businessId);
            nemsInvtListMapper.updateBondInvtNoByInvtId(invtHead.getId(), businessId);
            log.info("updateInvtPartBySeqNo回填出入库单关联核注单号判断："+invtHead.getStockHeadId());
            if (isNotEmpty(invtHead.getStockHeadId())){
                stockHeadTypeMapper.update(null,new UpdateWrapper<StockHeadType>().lambda()
                        .set(StockHeadType::getRltBondInvtNo,businessId).eq(StockHeadType::getInvtHeadId,invtHead.getId()));
                //更新相关关联出入库清单的清单编号（出口核注单）
//                if("E".equals(invtHead.getImpexpMarkcd())){
//
//                    List<StockHeadType> stockHeadTypeList=stockHeadTypeMapper.selectList(new LambdaQueryWrapper<StockHeadType>()
//                            .eq(StockHeadType::getInvtHeadId,invtHead.getId()));
//                    List<Long> headIdList=stockHeadTypeList.stream().map(StockHeadType::getId)
//                            .collect(Collectors.toList());
//                    //修复未存在数值的情况 -2023-11-21 -su
//                    if (headIdList != null && !headIdList.isEmpty()){
//                        //查询表头多id下所有表体
//                        List<StockGoodsType> stockGoodsTypeList=stockGoodsTypeMapper.selectList(
//                                new LambdaQueryWrapper<StockGoodsType>()
//                                        .in(StockGoodsType::getStockId,headIdList));
//                        if(stockGoodsTypeList.size()>0){
//                            List<String> updateMrNo=stockGoodsTypeList.stream().map(StockGoodsType::getMrNo)
//                                    .collect(Collectors.toList());;
//
//                            stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
//                                    .set(StockTypeImport::getInvtNumber,businessId)
//                                    .set(StockTypeImport::getUniversalStatus,"0")//还原推送标识
//                                    .in(StockTypeImport::getMrNo,updateMrNo));
//                        }
//                    }
//
//                }
            }
        }
        baseMapper.updateById(invtHead);
        return Result.ok(invtHead);
    }

    @Override
    @Transactional
    public Result<NemsInvtHead> setInvtBySeqNo(NemsInvtHead nemsInvtHead) {
        Result<NemsInvtHead> ym = new Result<NemsInvtHead>();
        String seqNo = nemsInvtHead.getSeqNo();
        LambdaQueryWrapper<NemsInvtHead> queryWrappervtHead = new LambdaQueryWrapper<NemsInvtHead>();
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if (StringUtils.isEmpty(seqNo)) {
            return ym.error("核注单统一编号为空");
        } else {
            NemsInvtHead invtHeas = new NemsInvtHead();
            NemsInvtHead invtHeasStart = baseMapper.selectOne(queryWrappervtHead.eq(NemsInvtHead::getSeqNo, seqNo));

            if (null != invtHeasStart) {
                if ((StringUtils.isNotEmpty(invtHeasStart.getRltEntryBizopEtpsNm())
                        || StringUtils.isNotEmpty(invtHeasStart.getRltEntryBizopEtpsSccd())
                        || StringUtils.isNotEmpty(invtHeasStart.getRltEntryBizopEtpsno())) && (
                        (StringUtils.isBlank(nemsInvtHead.getRltEntryBizopEtpsNm())
                                || StringUtils.isBlank(nemsInvtHead.getRltEntryBizopEtpsSccd())
                                || StringUtils.isBlank(nemsInvtHead.getRltEntryBizopEtpsno()))
                )
                ) {
                    nemsInvtHead.setRltEntryBizopEtpsNm(null);
                    nemsInvtHead.setRltEntryBizopEtpsno(null);
                    nemsInvtHead.setRltEntryBizopEtpsSccd(null);
                }
                //20220301追加如果核注单为非报关的把清单申报日期(INVT_DCL_TIME)同时赋值给申报日期(DECLARATION_DATE)
                if("2".equals(nemsInvtHead.getDclcusFlag())){
                    nemsInvtHead.setDeclarationDate(nemsInvtHead.getInvtDclTime());
                }
                EdiStatusHistory ediStatusHistory = new EdiStatusHistory();
                List<NemsInvtList> nemsInvtList = nemsInvtHead.getNemsInvtLists();
                saveOrUpdate(nemsInvtHead, new LambdaUpdateWrapper<NemsInvtHead>().eq(NemsInvtHead::getSeqNo, seqNo));
                if (isNotEmpty(invtHeasStart.getStockHeadId())){
                    stockHeadTypeMapper.update(null,new UpdateWrapper<StockHeadType>().lambda()
                            .set(StockHeadType::getRltInvtDclTime,nemsInvtHead.getInvtDclTime()).eq(StockHeadType::getInvtHeadId,invtHeasStart.getId()));
                }
                //20231115
                if("E".equals(invtHeasStart.getImpexpMarkcd()) && isNotEmpty(invtHeasStart.getStockHeadId())){
                    List<StockHeadType> stockHeadTypeList=stockHeadTypeMapper.selectList(new LambdaQueryWrapper<StockHeadType>()
                            .eq(StockHeadType::getInvtHeadId,invtHeasStart.getId()));
                    List<Long> headIdList=stockHeadTypeList.stream().map(StockHeadType::getId)
                            .collect(Collectors.toList());
//                    if (headIdList != null && !headIdList.isEmpty()){
//                        //查询表头多id下所有表体
//                        List<StockGoodsType> stockGoodsTypeList=stockGoodsTypeMapper.selectList(
//                                new LambdaQueryWrapper<StockGoodsType>()
//                                        .in(StockGoodsType::getStockId,headIdList));
//                        if(stockGoodsTypeList.size()>0){
//                            List<String> updateMrNo=stockGoodsTypeList.stream().map(StockGoodsType::getMrNo)
//                                    .collect(Collectors.toList());;
//
//                            stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
//                                    .set(StockTypeImport::getInvtDeclarationDate,nemsInvtHead.getInvtDclTime())
//                                    .set(StockTypeImport::getUniversalStatus,"0")//还原推送标识
//                                    .in(StockTypeImport::getMrNo,updateMrNo));
//                        }
//                    }

//                    StockHeadType stockHeadType=stockHeadTypeMapper.selectOne(new LambdaQueryWrapper<StockHeadType>()
//                            .eq(StockHeadType::getInvtHeadId,nemsInvtHead.getId()));
//                    if(null!=stockHeadType){
//                        List<StockGoodsType> stockGoodsTypeList=stockGoodsTypeMapper.selectList(
//                                new LambdaQueryWrapper<StockGoodsType>()
//                                        .eq(StockGoodsType::getStockId,stockHeadType.getId()));
//                        if(stockGoodsTypeList.size()>0){
//                            for(StockGoodsType stockGoodsType:stockGoodsTypeList){
//                                stockTypeImportMapper.update(null,new LambdaUpdateWrapper<StockTypeImport>()
//                                        .set(StockTypeImport::getInvtDeclarationDate,nemsInvtHead.getInvtDclTime())
//                                        .set(StockTypeImport::getUniversalStatus,"0")//还原推送标识
//                                        .eq(StockTypeImport::getMrNo,stockGoodsType.getMrNo())
//                                        .eq(StockTypeImport::getInvoicePartNo,stockGoodsType.getGdsMtno()));
//                            }
//                        }
//                    }
                }
                if (nemsInvtList != null && nemsInvtList.size() > 0 && nemsInvtHead.getVrfdedMarkcd().equals("2")) {
                    LambdaQueryWrapper<NemsInvtList> queryWrappervtList = new LambdaQueryWrapper<NemsInvtList>();
                    List<NemsInvtList> nemsInvtListStart = nemsInvtListService.list(queryWrappervtList.eq(NemsInvtList::getInvId, invtHeasStart.getId()));
                    Long APPLY_NUM = invtHeasStart.getApplyNumber();
                    Long INV_ID = invtHeasStart.getId();
                    String etpsInnerInvtNo = invtHeasStart.getEtpsInnerInvtNo();
                    Map<Integer,NemsInvtList> nemsInvtListWtMap = new HashMap<>();
                    if (nemsInvtListStart != null && !nemsInvtListStart.isEmpty()){
                        nemsInvtListStart.forEach(v->{
                            nemsInvtListWtMap.put(v.getGdsseqNo(),v);
                        });
                    }
                    if (nemsInvtListStart.size() == nemsInvtList.size()) {
                        for (int i = 0; i < nemsInvtList.size(); i++) {

                            NemsInvtList inemsInvtList = nemsInvtList.get(i);
                            inemsInvtList.setInvId(INV_ID);
                            inemsInvtList.setEtpsInnerInvtNo(etpsInnerInvtNo);
                            //处理净重
                            if (inemsInvtList.getNetWt() == null){
                                if ("035".equals(inemsInvtList.getLawfUnitcd())){
                                    inemsInvtList.setNetWt(inemsInvtList.getLawfQty());
                                }else if ("035".equals(inemsInvtList.getSecdlawfUnitcd())){
                                    inemsInvtList.setNetWt(inemsInvtList.getSecdLawfQty());
                                }else if (nemsInvtListWtMap.containsKey(inemsInvtList.getGdsseqNo())){
                                    inemsInvtList.setNetWt(nemsInvtListWtMap.get(inemsInvtList.getGdsseqNo()).getNetWt());
                                }
                            }
                            boolean invtList = false;
                            if (inemsInvtList.getPutrecSeqno() != null) {
                                LambdaUpdateWrapper<NemsInvtList> upWrappervtList = new LambdaUpdateWrapper<NemsInvtList>();
                                upWrappervtList.eq(NemsInvtList::getGdsseqNo, nemsInvtList.get(i).getGdsseqNo()).eq(NemsInvtList::getInvId, INV_ID);
//                          .eq(NemsInvtList::getSeqNo,seqNo);
                                invtList = nemsInvtListService.update(inemsInvtList, upWrappervtList);

                            } else {
                                int upTab = nemsInvtListMapper.updateListNotPutrecSeqnoById(inemsInvtList);
                                if (upTab >= 1) {
                                    invtList = true;
                                }

                            }

                            log.info(nemsInvtList.get(i).getGdsseqNo() + "_____________________" + INV_ID);
                            if (!invtList) {
                                ym.error("核注单表体更新错误");
                            }
                        }
                    } else {

                        LambdaUpdateWrapper<NemsInvtList> deWrappervtList = new LambdaUpdateWrapper<NemsInvtList>();
                        deWrappervtList.eq(NemsInvtList::getInvId, INV_ID);
                        boolean delList = nemsInvtListService.remove(deWrappervtList);
                        List<NemsInvtList> addList = new ArrayList<NemsInvtList>();
                        for (int i = 0; i < nemsInvtList.size(); i++) {
                            NemsInvtList nemsInvBody = nemsInvtList.get(i);
                            nemsInvBody.setApplyNumber(APPLY_NUM);
                            nemsInvBody.setInvId(INV_ID);
                            nemsInvBody.setEtpsInnerInvtNo(etpsInnerInvtNo);
                            if (nemsInvBody.getNetWt() == null){
                                if ("035".equals(nemsInvBody.getLawfUnitcd())){
                                    nemsInvBody.setNetWt(nemsInvBody.getLawfQty());
                                }else if ("035".equals(nemsInvBody.getSecdlawfUnitcd())){
                                    nemsInvBody.setNetWt(nemsInvBody.getSecdLawfQty());
                                } else if (nemsInvtListWtMap.containsKey(nemsInvBody.getGdsseqNo())){
                                    nemsInvBody.setNetWt(nemsInvtListWtMap.get(nemsInvBody.getGdsseqNo()).getNetWt());
                                }
                            }
                            addList.add(nemsInvBody);
                        }
                        boolean delSave = nemsInvtListService.saveBatch(addList);
                        if (!delSave) {
                            ym.error("项数不一致时，核注单表体更新错误");
                        }

                    }
                    // todo 账册类型
//                    if (invtHeasStart.getImpexpMarkcd().equals("E") && nemsInvtHead.getEmsFlowsId() != null) {
//                        //冲正
//                        YmMsg<String> rectifyYmMsg = emsStockApi.rectify(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(),
//                                isNotEmpty(nemsInvtHead.getEmsFlowsId()) ? nemsInvtHead.getEmsFlowsId().toString() : null);
//
////                        YmMsg<StockHandleResultDto> stockHandleResultDtoYmMsg = emsStockServe.stockRectification(invtHeasStart.getTenantId(),username,stockRectificationList);
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.isSuccess());
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.getMessage());
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.getCode());
//                    }
//                    // todo 账册类型
//                    if (invtHeasStart.getImpexpMarkcd().equals("E") && isNotEmpty(invtHeasStart.getStockHeadId())
//                            &&"2".equals(nemsInvtHead.getVrfdedMarkcd())) {
//                        //冲正
//                        YmMsg<String> rectifyYmMsg = stockHeadTypeService.rectifyQtyForStockTypeId(invtHeasStart.getStockHeadId());
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.isSuccess());
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.getMessage());
//                        log.info("!!!!!!!!!!!！！！rectifyYmMsg：" + rectifyYmMsg.getCode());
//                    }
                }

            } else {
                ym.setMessage("未找到相关seqno关联单据单");
            }
            invtHeas = baseMapper.selectOne(queryWrappervtHead);
            if (isEmpty(invtHeas)) {
                return Result.error("未查询到核注清单！");
            }
            nemsInvtHead.setId(invtHeas.getId());
            nemsInvtHead.setApplyNumber(invtHeas.getApplyNumber());
            //出口核注单预核扣且出口原装退运/保税结转业务：
//            if ("1".equals(nemsInvtHead.getVrfdedMarkcd()) && isNotEmpty(nemsInvtHead.getApplyNumber()) && "E".equals(nemsInvtHead.getImpexpMarkcd())){
//                Apply apply = decHeadMapper.getApplyById(String.valueOf(nemsInvtHead.getApplyNumber()));
//                if ("E".equals(apply.getImSign()) && "107".equals(apply.getGoodsType()) || "110".equals(apply.getGoodsType())){
//                    baseMapper.update(null,new UpdateWrapper<NemsInvtHead>().lambda().set(NemsInvtHead::getReceiptDate,apply.getCreateDate())
//                            .eq(NemsInvtHead::getApplyNumber,apply.getId()));
//                    passPortHeadService.generatePassPortByInvt(nemsInvtHead.getId().toString());
//                }
//                passPortHeadService.generatePassPortByInvt(nemsInvtHead.getId().toString());
//            }

            if ("1".equals(nemsInvtHead.getVrfdedMarkcd()) && "0".equals(nemsInvtHead.getInvtType())
                    && "E".equals(nemsInvtHead.getImpexpMarkcd())){
                List<Integer> gNos = new ArrayList<>();
                List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                        .eq(NemsInvtList::getInvId,nemsInvtHead.getId()));
                invtLists.forEach(v->{
                    if (!gNos.contains(gNos)){
                        gNos.add(v.getPutrecSeqno());
                    }
                });
                nemsInvtListMapper.updatePushStatus(nemsInvtHead.getPutrecNo(),gNos,"0");
            }


            LambdaQueryWrapper<NemsInvtList> queryWrappervtEndList = new LambdaQueryWrapper<NemsInvtList>();
            List<NemsInvtList> endnemsInvtList = nemsInvtListService.list(queryWrappervtEndList.eq(NemsInvtList::getInvId, invtHeas.getId()));
            invtHeas.setNemsInvtLists(endnemsInvtList);
            ym.setResult(invtHeas);
            ym.setCode(200);
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
            return ym;
        }
    }

    /**
     * @return
     */
    @Override
    public Result<?> handleSync() {
        Result<?> result = dockingEasyPassService.syncHzqdForJob("济南迅吉安保税物流有限公司", "1371,5000", "H430622A0004", null, null, null);
        Result<?> result1 = dockingEasyPassService.syncHzqdForJob("森峰（济南）进出口有限公司", "1371,5000", "H430621A0001", null, null, null);
        if (result.isSuccess()) {
            log.info("result:{}", result.getMessage());
        }
        if (result.isSuccess()) {
            log.info("result1:{}", result1.getMessage());
        }
        StringBuilder msg = new StringBuilder();
        msg.append("result:").append(result.getMessage()).append(";result1:").append(result1.getMessage());
        return Result.ok(msg.toString());
    }

    /**
     * 获取核注清单列表
     * 获取符合条件的核注清单列表
     * <p>
     * 参数说明：
     * etpsCategory：要查询企业类型
     * A – 经营单位 B-加工单位 C-申报单位 D-录入单位
     * 为空时，如果查询企业为收发货人，则默认为 A，否则默认为 C。
     * status(数据状态)代码表如下：
     * 0：暂存，1：申报成功 4：成功发送海关 5：海关接收成功 6：海关接收失败
     * B：海关终审通过 C：海关退单 E：删除 P：预审批通过
     * 空代表全部
     * vrfdedMarkcd(核扣标志)代码表如下：
     * 0：未核扣 1：预核扣 2：已核扣 3：已核销 4：反核扣
     * 空代表全部
     * startDate、endDate 为一组日期范围，dclStartDate、dclEndDate 为一组日期范围，形式都为
     * yyyyMMdd，只有当输入 invtNo 或 seqNo 进行查询时，才能两组日期都为空，否则至少输
     * 入一组日期。当使用时间段查询时，查询区间不能大于 90 天。
     *
     * @param swid         操作员卡号
     * @param systemId     子系统代码（必填）
     * @param status       数据状态（可选，默认全部）
     * @param etpsCategory 查询企业类型（选填，默认为 A 或 C 的一种）
     * @param tradeCode    查询企业 10 位海关代码（必填）
     * @param ieFlag       进出口类型（I/E）（可选）
     * @param dclTypecd    申报类型（可选， 1=备案 2=修改 3=删除）
     * @param invtNo       清单编号（可选）
     * @param seqNo        预录入统一编号（可选）
     * @param etpsNo       经营单位编码（可选）
     * @param putrecNo     账册编号（可选，12 位）
     * @param vrfdedMarkcd 核扣标志（可选，代码含义见参数说明）
     * @param startDate    录入日期范围起始（条件可选）
     * @param endDate      录入日期范围截止（条件可选）
     * @param dclStartDate 申报日期范围起始（条件可选）
     * @param dclEndDate   申报日期范围截止（条件可选）
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @Override
    public Result<?> GetInvtList(String swid, String systemId, String status, String etpsCategory, String tradeCode, String ieFlag, String dclTypecd,
                                 String invtNo, String seqNo, String etpsNo, String putrecNo, String vrfdedMarkcd, String startDate, String endDate, String dclStartDate, String dclEndDate) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有人的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        if (isBlank(dclStartDate)) {
            dclStartDate = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.PURE_DATE_PATTERN);
        }
        if (isBlank(dclEndDate)) {
            dclEndDate = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        }
        log.info("dclStartDate：{} dclEndDate：{}", dclStartDate, dclEndDate);
        // 获取日期分组
        List<DateRangeSplitter.DateRange> dateRangeList = splitDateRange(dclStartDate, dclEndDate);
        StringBuilder msg = new StringBuilder();
        int totalCount = 0;
        // 处理每个分组
        for (DateRangeSplitter.DateRange dateRange : dateRangeList) {
            // 记录处理信息
            String processInfo = String.format("处理时间段: %s 至 %s",
                    dateRange.getStartDate(),
                    dateRange.getEndDate());
            log.info("处理日期段开始：{}", processInfo);
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("swid", swid);
            jsonMap.put("systemId", systemId);
            jsonMap.put("tradeCode", tradeCode);
            jsonMap.put("dclStartDate", dateRange.getStartDate());
            jsonMap.put("dclEndDate", dateRange.getEndDate());
            log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
            // 等待直到获取到令牌
            rateLimiter.acquire();
            try {
                String result = sendOpenApi(URL_GET_INVT_LIST, JSON.toJSONString(jsonMap));
                JSONObject jsonObject = JSON.parseObject(result);
                if (!(jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")))) {
                    log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                    continue;
                }
                JSONArray jsonArray = JSON.parseArray(jsonObject.getString("data"));
                log.info("【{}】返回的核注单列表个数：{}", processInfo, jsonArray.size());
                totalCount += jsonArray.size();

                // 1. 先保存报关单列表
                List<SyncNemsInvtHead> syncNemsInvtHeads = new ArrayList<>();
                for (Object item : jsonArray) {
                    JSONObject jo = (JSONObject) item;
                    SyncNemsInvtHead syncNemsInvtHead = JSONObject.parseObject(jo.toString(), SyncNemsInvtHead.class);
                    SyncNemsInvtHead one = syncNemsInvtHeadMapper.selectOne(new LambdaQueryWrapper<SyncNemsInvtHead>()
                            .eq(SyncNemsInvtHead::getSeqNo, syncNemsInvtHead.getSeqNo())
                            .eq(SyncNemsInvtHead::getTenantId, tenantId));
                    if (isNotEmpty(one)) {
                        log.info("核注单统一编号：{} 已存在，跳过！", syncNemsInvtHead.getSeqNo());
                        continue;
                    }
                    syncNemsInvtHead.setTenantId(Long.valueOf(tenantId));
                    syncNemsInvtHeads.add(syncNemsInvtHead);
                }
                // 1. 保存核注单列表
                saveSyncInvts(syncNemsInvtHeads);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("获取核注单列表异常：{}", e.getMessage());
            }
            log.info("处理日期段结束：{}", processInfo);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        msg.append("[").append(dclStartDate).append("~").append(dclEndDate).append("]本次共获取核注单：").append(totalCount).append("个");
        return Result.ok(msg.toString());
    }

    /**
     * 获取核注清单电子数据
     * 获取核注清单的详细信息
     *
     * @param swid
     * @param systemId
     * @param seqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> GetInvtData(String swid, String systemId, String seqNo) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        StringBuilder msg = new StringBuilder();
        NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
        // 如果未传报关单统一编号，则同步未同步的数据
        if (isBlank(seqNo)) {
            // 还未同步的数据
            List<SyncNemsInvtHead> syncNemsInvtHeadList = syncNemsInvtHeadMapper.selectList(new LambdaQueryWrapper<SyncNemsInvtHead>()
                    .eq(SyncNemsInvtHead::getTenantId, tenantId)
                    .eq(SyncNemsInvtHead::getIsSync, "0"));
            if (isNotEmpty(syncNemsInvtHeadList)) {
                msg.append("未同步核注单数据：").append(syncNemsInvtHeadList.size());
                currentProxy.processSyncInvtList(syncNemsInvtHeadList, swid, systemId, msg);
            } else {
                msg.append("不存在未同步的报关单数据！");
            }
            // 如果传了报关单统一编号，则同步单个数据
        } else {
            currentProxy.processSyncInvtOne(seqNo, swid, systemId, tenantId, msg);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        log.info(msg.toString());
        return Result.ok(msg.toString());
    }

    /**
     * 再次同步未核扣和预核扣的核注单
     *
     * @param swid
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> syncInvtVrfdedMarkcd(String swid, String systemId, String tradeCode, String startTime, String endTime, String isAll) {
        // 设置个限流的令牌桶
        RateLimiter rateLimiterSync = RateLimiter.create(1.0/5);
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        StringBuilder msg = new StringBuilder();
        // vrfdedMarkcd(核扣标志)代码表如下：
        // 0：未核扣 1：预核扣 2：已核扣 3：已核销 4：反核扣
        // 未核扣和预核扣的数据需要再次同步
//        List<SyncNemsInvtHead> syncNemsInvtHeadList = syncNemsInvtHeadMapper.selectList(new LambdaQueryWrapper<SyncNemsInvtHead>()
//                .eq(SyncNemsInvtHead::getTenantId, tenantId)
//                .in(SyncNemsInvtHead::getVrfdedMarkcd, "0", "1"));
        if (isBlank(startTime)) {
            startTime = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(endTime)) {
            endTime = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }
        if (isNotBlank(isAll)) {
            startTime = null;
            endTime = null;
        }
        log.info("startTime：{} endTime：{}", startTime, endTime);
        List<SyncNemsInvtHead> syncNemsInvtHeadList = syncNemsInvtHeadMapper.selectUnClosed(tenantId, startTime, endTime);
        List<NemsInvtHead> nemsInvtHeadList = baseMapper.selectUnClosed(tenantId, startTime, endTime);
        if (isEmpty(syncNemsInvtHeadList) && isEmpty(nemsInvtHeadList)) {
            return Result.ok("不存在未核扣和预核扣的核注单数据！");
        }
        // 使用Stream API收集两个列表中的非空seqNo
        Set<String> unifiedIds = Stream.concat(
                Optional.ofNullable(syncNemsInvtHeadList).orElse(Collections.emptyList()).stream()
                        .map(SyncNemsInvtHead::getSeqNo)
                        .filter(CharSequenceUtil::isNotBlank),
                Optional.ofNullable(nemsInvtHeadList).orElse(Collections.emptyList()).stream()
                        .map(NemsInvtHead::getSeqNo)
                        .filter(CharSequenceUtil::isNotBlank)
        ).collect(Collectors.toSet());
        if (isEmpty(unifiedIds)) {
            return Result.ok("不存在未核扣和预核扣的核注单数据！");
        }
        List<String> unifiedIdList = new ArrayList<>(unifiedIds);
        log.info("获取未核扣和预核扣的核注单数据：{}", unifiedIdList.size());
        NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();

        // 统计信息
        int totalCount = unifiedIdList.size();
        int successCount = 0;
        int failureCount = 0;
        int skipCount = 0;
        int retryCount = 0;
        long startProcessTime = System.currentTimeMillis();

        // 使用索引控制循环，支持重试当前记录
        int currentIndex = 0;
        while (currentIndex < unifiedIdList.size()) {
            String seqNo = unifiedIdList.get(currentIndex);
            log.info("本次处理开始，当前时间：{}，处理第{}条，seqNo：{}",
                    DateUtil.formatDateTime(new Date()), currentIndex + 1, seqNo);
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("swid", swid);
            jsonMap.put("systemId", systemId);
            jsonMap.put("tradeCode", tradeCode);
            jsonMap.put("seqNo", seqNo);
            log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));

            boolean shouldRetryCurrentRecord = false;

            try {
                // 等待直到获取到令牌
                rateLimiterSync.acquire();
                String result = sendOpenApi(URL_GET_INVT_LIST, JSON.toJSONString(jsonMap));
                JSONObject jsonObject = JSON.parseObject(result);

                if (!(jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")))) {
                    log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                    // 检查是否为频繁调用错误
                    if (isNotBlank(jsonObject.getString("errors")) &&
                            jsonObject.getString("errors").contains("操作过于频繁")) {
                        log.info("遇到【调用频繁】了，先暂停30秒，当前时间：{}", DateUtil.formatDateTime(new Date()));
                        Thread.sleep(30000);
                        log.info("暂停了30秒了，重新处理当前记录，当前时间：{}", DateUtil.formatDateTime(new Date()));
                        shouldRetryCurrentRecord = true;
                        retryCount++;
                    } else {
                        // 其他错误，跳过当前记录
                        log.warn("seqNo: {} 处理失败，跳过：{}", seqNo, jsonObject.getString("errors"));
                        msg.append(String.format("[失败] 核注单 %s：%s\n", seqNo, jsonObject.getString("errors")));
                        failureCount++;
                    }
                } else if (isNotEmpty(jsonObject.getBoolean("ok")) && !jsonObject.getBoolean("ok")) {
                    log.info("获取核注单详情失败：{}", jsonObject.getString("errors"));
                    // 检查是否为频繁调用错误
                    if (isNotBlank(jsonObject.getString("errors")) &&
                            jsonObject.getString("errors").contains("操作过于频繁")) {
                        log.info("遇到【调用频繁】了，先暂停30秒，当前时间：{}", DateUtil.formatDateTime(new Date()));
                        Thread.sleep(30000);
                        log.info("暂停了30秒了，重新处理当前记录，当前时间：{}", DateUtil.formatDateTime(new Date()));
                        shouldRetryCurrentRecord = true;
                        retryCount++;
                    } else {
                        // 其他错误，跳过当前记录
                        log.warn("seqNo: {} 处理失败，跳过：{}", seqNo, jsonObject.getString("errors"));
                        msg.append(String.format("[失败] 核注单 %s：%s\n", seqNo, jsonObject.getString("errors")));
                        failureCount++;
                    }
                } else {
                    // 正常处理逻辑
                    JSONArray jsonArray = JSON.parseArray(jsonObject.getString("data"));
                    JSONObject jo = (JSONObject) jsonArray.get(0);
                    // 如果是未核扣或已核扣，就过滤掉
                    if ("0".equals(jo.getString("vrfdedMarkcd")) || "1".equals(jo.getString("vrfdedMarkcd"))) {
                        log.info("核注单 {} 未核扣或预核扣，过滤掉：{}", seqNo, jo.getString("vrfdedMarkcd"));
                        msg.append(String.format("[跳过] 核注单 %s：状态为%s（未核扣或预核扣）\n",
                                seqNo, "0".equals(jo.getString("vrfdedMarkcd")) ? "未核扣" : "预核扣"));
                        skipCount++;
                    } else {
                        try {
                            // 已核扣之后的，就去调取详情接口去更新本地数据
                            currentProxy.processSyncInvtOne(jo.getString("seqNo"), swid, systemId, tenantId, msg);
                            log.info("seqNo: {} 处理成功", seqNo);
                            msg.append(String.format("[成功] 核注单 %s：处理完成\n", seqNo));
                            successCount++;
                        } catch (Exception e) {
                            // 记录单个核注单处理的异常，但不阻止后续处理
                            log.error("处理核注单 {} 详情出现异常：{}", jo.getString("seqNo"), e.getMessage(), e);
                            msg.append(String.format("[异常] 核注单 %s：%s\n", jo.getString("seqNo"), e.getMessage()));
                            failureCount++;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取核注单列表出现异常：{}", e.getMessage(), e);
                msg.append(String.format("[异常] 核注单 %s：%s\n", seqNo, e.getMessage()));
                failureCount++;
            }
            // 根据是否需要重试来决定是否移动到下一条记录
            if (!shouldRetryCurrentRecord) {
                currentIndex++; // 只有在不需要重试时才移动到下一条
            }
        }
        // 计算处理时间
        long endProcessTime = System.currentTimeMillis();
        long totalProcessTime = endProcessTime - startProcessTime;
        String processTimeStr = String.format("%.2f秒", totalProcessTime / 1000.0);

        // 生成统计报告
        StringBuilder summaryMsg = new StringBuilder();
        summaryMsg.append("\n=== 同步执行统计报告 ===\n");
        summaryMsg.append(String.format("执行时间：%s\n", DateUtil.formatDateTime(new Date())));
        summaryMsg.append(String.format("处理耗时：%s\n", processTimeStr));
        summaryMsg.append(String.format("总记录数：%d 条\n", totalCount));
        summaryMsg.append(String.format("成功处理：%d 条\n", successCount));
        summaryMsg.append(String.format("处理失败：%d 条\n", failureCount));
        summaryMsg.append(String.format("跳过记录：%d 条\n", skipCount));
        summaryMsg.append(String.format("重试次数：%d 次\n", retryCount));
        summaryMsg.append(String.format("成功率：%.2f%%\n", totalCount > 0 ? (successCount * 100.0 / totalCount) : 0));
        summaryMsg.append("========================\n\n");

        // 将统计信息添加到消息开头
        msg.insert(0, summaryMsg.toString());

        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        log.info("同步完成，统计信息：总数={}, 成功={}, 失败={}, 跳过={}, 重试={}",
                totalCount, successCount, failureCount, skipCount, retryCount);
        log.info(msg.toString());
//        return Result.ok(msg.toString());
        return Result.ok(summaryMsg.toString());
    }

    /**
     * 同步单个数据
     *
     * @param seqNo
     * @param swid
     * @param systemId
     * @param tenantId
     * @param msg
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 16:42
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processSyncInvtOne(String seqNo, String swid, String systemId, String tenantId, StringBuilder msg) {
        try {
            // 设置忽略租户插件
            InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
            Map<String, Object> jm = new LinkedHashMap<>();
            jm.put("swid", swid);
            jm.put("systemId", systemId);
            jm.put("seqNo", seqNo);
            String r = sendOpenApi(URL_GET_INVT_DATA, JSON.toJSONString(jm));
            // 保存文件
            saveJsonToFile(r, "INVT", "SYNC_INVT_" + seqNo);
            JSONObject ro = JSON.parseObject(r);
            if (isNotEmpty(ro.getBoolean("success")) && !ro.getBoolean("success")) {
                log.info("获取核注单详情失败：{}", ro.getString("message"));
                throw new RuntimeException(ro.getString("message"));
            }
            if (isNotEmpty(ro.getBoolean("ok")) && !ro.getBoolean("ok")) {
                log.info("获取核注单详情失败：{}", ro.getString("errors"));
                throw new RuntimeException(ro.getString("errors"));
            }
            NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
            // 2024/11/8 14:15@ZHANGCHAO 追加/变更/完善：单独取状态！！
            String status = ro.getJSONObject("data").getString("listStat");

            JSONObject j = ro.getJSONObject("data").getJSONObject("invtHeadType");
            SyncNemsInvtHead invtHead = JSONObject.parseObject(j.toString(), SyncNemsInvtHead.class);
            JSONArray ja = ro.getJSONObject("data").getJSONArray("invtListType");
            List<SyncNemsInvtList> invtList = JSONObject.parseArray(ja.toString(), SyncNemsInvtList.class);
            invtHead.setInvtListType(invtList);

            SyncNemsInvtHead one = syncNemsInvtHeadMapper.selectOne(new LambdaQueryWrapper<SyncNemsInvtHead>()
                    .eq(SyncNemsInvtHead::getSeqNo, seqNo)
                    .eq(SyncNemsInvtHead::getTenantId, tenantId));
            SyncNemsInvtHead insertInvtHead = new SyncNemsInvtHead();
            if (isNotEmpty(one)) {
                BeanUtil.copyProperties(invtHead, one, CopyOptions.create().ignoreNullValue());
                insertInvtHead = one;
                insertInvtHead.setInvtListType(invtHead.getInvtListType());
            } else {
                BeanUtil.copyProperties(invtHead, insertInvtHead, CopyOptions.create().ignoreNullValue());
            }
            insertInvtHead.setListStat(status); // 状态防止被重置！！
            insertInvtHead.setTenantId(isNotEmpty(insertInvtHead.getTenantId()) ? insertInvtHead.getTenantId() : Long.valueOf(tenantId));
            // 1. 保存或更新同步报关单
            SyncNemsInvtHead ret = currentProxy.saveSyncInvt(insertInvtHead);
            // 转换报关单数据
//            List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>();
            List<NemsInvtHead> nemsInvtHeads = baseMapper.selectList(new LambdaQueryWrapper<NemsInvtHead>()
                    .eq(NemsInvtHead::getSeqNo, ret.getSeqNo())
                    .eq(NemsInvtHead::getTenantId, tenantId));
            if (isEmpty(nemsInvtHeads)) {
                nemsInvtHeads = new ArrayList<>();
                NemsInvtHead nemsInvtHead = new NemsInvtHead();
                nemsInvtHeads.add(nemsInvtHead);
            } else {
                nemsInvtHeads.forEach(nemsInvtHead -> {
                    List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                            .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
                    nemsInvtHead.setNemsInvtLists(nemsInvtLists);
                });
            }
            // 2. 转换核注单数据
            for (NemsInvtHead nemsInvtHead : nemsInvtHeads) {
                setInvtFromJson(ret, nemsInvtHead);
            }
            Result<?> result = saveNemsInvtHeadBatch_(nemsInvtHeads);
            msg.append(result.getMessage());
//            nemsInvtHeadList.add(nemsInvtHead);
//            if (isNotEmpty(nemsInvtHeadList)) {
////                NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
//                Result<?> result = saveNemsInvtHeadBatch_(nemsInvtHeadList);
//                msg.append(result.getMessage());
//            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取报关单详情异常：{}", e.getMessage());
            msg.append("获取报关单详情异常：").append(e.getMessage());
            // 重新抛出异常，确保事务回滚
            throw new RuntimeException("处理报关单详情失败", e);
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
    }

    /**
     * 保存单个同步报关单数据
     *
     * @param syncNemsInvtHead
     * @return org.jeecg.modules.business.entity.SyncNemsInvtHead
     * <AUTHOR>
     * @date 2024/10/31 16:50
     */
    @Transactional(rollbackFor = Exception.class)
    public SyncNemsInvtHead saveSyncInvt(SyncNemsInvtHead syncNemsInvtHead) {
        if (isEmpty(syncNemsInvtHead)) {
            return new SyncNemsInvtHead();
        }
        // 新增
        if (isBlank(syncNemsInvtHead.getId())) {
            syncNemsInvtHead.setCreateBy("TASK");
            syncNemsInvtHead.setCreateDate(new Date());
            syncNemsInvtHeadMapper.insert(syncNemsInvtHead);
            if (isNotEmpty(syncNemsInvtHead.getInvtListType())) {
                syncNemsInvtHead.getInvtListType().forEach(item -> {
                    item.setHeadId(syncNemsInvtHead.getId());
                    item.setTenantId(syncNemsInvtHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncNemsInvtListService.saveBatch(syncNemsInvtHead.getInvtListType());
            }
            // 编辑
        } else {
            syncNemsInvtHead.setUpdateBy("TASK");
            syncNemsInvtHead.setUpdateDate(new Date());
            syncNemsInvtHeadMapper.updateById(syncNemsInvtHead);
            syncNemsInvtListService.remove(new LambdaQueryWrapper<SyncNemsInvtList>()
                    .eq(SyncNemsInvtList::getHeadId, syncNemsInvtHead.getId()));
            if (isNotEmpty(syncNemsInvtHead.getInvtListType())) {
                syncNemsInvtHead.getInvtListType().forEach(item -> {
                    item.setHeadId(syncNemsInvtHead.getId());
                    item.setTenantId(syncNemsInvtHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncNemsInvtListService.saveBatch(syncNemsInvtHead.getInvtListType());
            }
        }
        SyncNemsInvtHead ret = syncNemsInvtHeadMapper.selectById(syncNemsInvtHead.getId());
        ret.setInvtListType(syncNemsInvtListService.list(new LambdaQueryWrapper<SyncNemsInvtList>()
                .eq(SyncNemsInvtList::getHeadId, syncNemsInvtHead.getId())));
        return ret;
    }

    /**
     * 处理未同步核注单数据
     *
     * @param syncNemsInvtHeadList
     * @param swid
     * @param systemId
     * @param msg
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 15:14
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processSyncInvtList(List<SyncNemsInvtHead> syncNemsInvtHeadList, String swid, String systemId, StringBuilder msg) {
        // 设置个限流令牌桶
        RateLimiter rateLimiterSync = RateLimiter.create(1.0/5);
        int chunkSize = 10;
        List<List<SyncNemsInvtHead>> syncNemsInvtHeadListList = CollUtil.split(syncNemsInvtHeadList, chunkSize);
        log.info("未同步的数据分片，每片{}条，共{}片", chunkSize, syncNemsInvtHeadListList.size());
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
        for (List<SyncNemsInvtHead> syncNemsInvtHeads : syncNemsInvtHeadListList) {
            try {
//                List<NemsInvtHead> nemsInvtHeadList = new ArrayList<>();
                for (SyncNemsInvtHead syncNemsInvtHead : syncNemsInvtHeads) {
                    try {
                        Map<String, Object> jm = new LinkedHashMap<>();
                        jm.put("swid", swid);
                        jm.put("systemId", systemId);
                        jm.put("seqNo", syncNemsInvtHead.getSeqNo());
                        // 等待直到获取到令牌
                        rateLimiterSync.acquire();
                        log.info("请求开始，当前时间：{}", DateUtil.formatDateTime(new Date()));
                        String r = sendOpenApi(URL_GET_INVT_DATA, JSON.toJSONString(jm));
                        // 保存文件
                        saveJsonToFile(r, "INVT", "SYNC_INVT_" + syncNemsInvtHead.getSeqNo());
                        JSONObject ro = JSON.parseObject(r);
                        if (isNotEmpty(ro.getBoolean("success")) && !ro.getBoolean("success")) {
                            log.info("获取核注单单详情失败：{}", ro.getString("message"));
                            continue;
                        }
                        if (isNotEmpty(ro.getBoolean("ok")) && !ro.getBoolean("ok")) {
                            log.info("获取核注单详情失败：{}", ro.getString("errors"));
                            // 出现错误后，就暂停30秒再次执行
                            if (isNotBlank(ro.getString("errors")) && ro.getString("errors").contains("操作过于频繁")) {
                                log.info("遇到【调用频繁】了，先暂停30秒，当前时间：{}", DateUtil.formatDateTime(new Date()));
                                Thread.sleep(30000);
                                log.info("暂停了30秒了，继续开始，当前时间：{}", DateUtil.formatDateTime(new Date()));
                            }
                            continue;
                        }
                        // 2024/11/8 14:15@ZHANGCHAO 追加/变更/完善：单独取状态！！
                        String status = ro.getJSONObject("data").getString("listStat");
                        JSONObject j = ro.getJSONObject("data").getJSONObject("invtHeadType");
                        SyncNemsInvtHead invtHead = JSONObject.parseObject(j.toString(), SyncNemsInvtHead.class);
                        JSONArray ja = ro.getJSONObject("data").getJSONArray("invtListType");
                        List<SyncNemsInvtList> invtList = JSONObject.parseArray(ja.toString(), SyncNemsInvtList.class);
                        invtHead.setInvtListType(invtList);
                        BeanUtil.copyProperties(invtHead, syncNemsInvtHead, CopyOptions.create().ignoreNullValue());
                        syncNemsInvtHead.setListStat(status); // 防止状态被重置！！
                        // 1. 更新同步核注单表数据补充数据
                        currentProxy.updateSyncInvt(syncNemsInvtHead);
//                        List<NemsInvtHead> nemsInvtHeads = baseMapper.selectList(new LambdaQueryWrapper<NemsInvtHead>()
//                                .eq(NemsInvtHead::getSeqNo, syncNemsInvtHead.getSeqNo())
//                                .eq(NemsInvtHead::getTenantId, syncNemsInvtHead.getTenantId()));
                        List<NemsInvtHead> nemsInvtHeads = baseMapper.listNemsInvtHead(syncNemsInvtHead.getSeqNo(), syncNemsInvtHead.getTenantId());
                        if (isEmpty(nemsInvtHeads)) {
                            nemsInvtHeads = new ArrayList<>();
                            NemsInvtHead nemsInvtHead = new NemsInvtHead();
                            nemsInvtHeads.add(nemsInvtHead);
                        } else {
                            nemsInvtHeads.forEach(nemsInvtHead -> {
                                List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                                        .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
                                nemsInvtHead.setNemsInvtLists(nemsInvtLists);
                            });
                        }
                        // 2. 转换核注单数据
                        for (NemsInvtHead nemsInvtHead : nemsInvtHeads) {
                            setInvtFromJson(syncNemsInvtHead, nemsInvtHead);
                        }
                        Result<?> result = saveNemsInvtHeadBatch_(nemsInvtHeads);
                        msg.append(result.getMessage());
                    } catch (Exception e) {
                        ExceptionUtil.getFullStackTrace(e);
                        log.error("获取核注单详情异常：{}", e.getMessage());
                    }
//                    nemsInvtHeadList.add(nemsInvtHead);
                }
//                if (isNotEmpty(nemsInvtHeadList)) {
////                    NemsInvtHeadServiceImpl currentProxy = (NemsInvtHeadServiceImpl) AopContext.currentProxy();
//                    Result<?> result = saveNemsInvtHeadBatch_(nemsInvtHeadList);
//                    msg.append(result.getMessage());
//                }
            } catch (Exception e) {
                e.printStackTrace();
                ExceptionUtil.getFullStackTrace(e);
                log.error("获取核注单详情异常：{}", e.getMessage());
            } finally {
                // 关闭忽略策略
                InterceptorIgnoreHelper.clearIgnoreStrategy();
            }
        }
    }

    /**
     * 转换核注单数据
     *
     * @param syncInvtHead
     * @param nemsInvtHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 15:31
     */
    private void setInvtFromJson(SyncNemsInvtHead syncInvtHead, NemsInvtHead nemsInvtHead) {
        if (null == syncInvtHead) {
            return;
        }
//        NemsInvtHead nemsInvtHead = baseMapper.selectOne(new LambdaQueryWrapper<NemsInvtHead>()
//                .eq(NemsInvtHead::getSeqNo, syncInvtHead.getSeqNo()));
//        if (null == nemsInvtHead) {
//            nemsInvtHead = new NemsInvtHead();
//        } else {
//            List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
//                    .eq(NemsInvtList::getInvId, nemsInvtHead.getId()));
//            nemsInvtHead.setNemsInvtLists(nemsInvtLists);
//        }
        nemsInvtHead.setTenantId(syncInvtHead.getTenantId());
        nemsInvtHead.setDclTenantId(nemsInvtHead.getTenantId());
        nemsInvtHead.setSeqNo(syncInvtHead.getSeqNo()); // 清单预录入统一编号 （返填 - 第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号）
        nemsInvtHead.setBondInvtNo(syncInvtHead.getBondInvtNo()); // 清单编号 （返填 - 海关审批通过后系统自动返填）
        nemsInvtHead.setPutrecNo(syncInvtHead.getPutrecNo()); // 备案编号
        nemsInvtHead.setEtpsInnerInvtNo(syncInvtHead.getEtpsInnerInvtNo()); // 企业内部清单编号 （由企业自行编写）
        nemsInvtHead.setBizopEtpsSccd(syncInvtHead.getBizopEtpsSccd()); // 经营企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setBizopEtpsno(syncInvtHead.getBizopEtpsno()); // 经营企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setBizopEtpsNm(syncInvtHead.getBizopEtpsNm()); // 经营企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRcvgdEtpsno(syncInvtHead.getRcvgdEtpsno()); // 收货企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRvsngdEtpsSccd(syncInvtHead.getRvsngdEtpsSccd()); // 收发货企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setRcvgdEtpsNm(syncInvtHead.getRcvgdEtpsNm()); // 收货企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
        nemsInvtHead.setDclEtpsSccd(syncInvtHead.getDclEtpsSccd()); // 申报企业社会信用代码
        nemsInvtHead.setDclEtpsno(syncInvtHead.getDclEtpsno()); // 申报企业编号
        nemsInvtHead.setDclEtpsNm(syncInvtHead.getDclEtpsNm()); // 申报企业名称
        try {
            nemsInvtHead.setInvtDclTime(isNotBlank(syncInvtHead.getInvtDclTime())
                    ? DateUtil.parse(syncInvtHead.getInvtDclTime(), DatePattern.PURE_DATE_PATTERN) : null); // 清单申报时间 （返填 - 系统自动反填）
            nemsInvtHead.setEntryDclTime(isNotBlank(syncInvtHead.getEntryDclTime())
                    ? DateUtil.parse(syncInvtHead.getEntryDclTime(), DatePattern.PURE_DATE_PATTERN) : null); // 报关单申报日期 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
        } catch (Exception e) {
            log.error("转换清单申报时间/报关单申报日期出现异常：{}", e.getMessage());
        }
        nemsInvtHead.setEntryNo(syncInvtHead.getEntryNo()); // 对应报关单编号 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
        nemsInvtHead.setRltinvtNo(syncInvtHead.getRltInvtNo()); // 关联清单编号 （结转类专用，检控要求复杂，见需求文档）
        nemsInvtHead.setRltputrecNo(syncInvtHead.getRltPutrecNo()); // 关联备案编号 （结转类专用）
        nemsInvtHead.setRltEntryNo(syncInvtHead.getRltEntryNo()); // 关联报关单编号 （可录入或者系统自动生成报关单后返填二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）

        nemsInvtHead.setRltEntryBizopEtpsSccd(syncInvtHead.getRltEntryRvsngdEtpsSccd()); // 关联报关单消费使用单位社会信用代码
        nemsInvtHead.setRltEntryBizopEtpsno(syncInvtHead.getRltEntryRcvgdEtpsno()); // 关联报关单消费使用单位编号
        nemsInvtHead.setRltEntryBizopEtpsNm(syncInvtHead.getRltEntryRcvgdEtpsNm()); // 关联报关单消费使用单位名称
        nemsInvtHead.setRltEntryRvsngdEtpsSccd(syncInvtHead.getRltEntryBizopEtpsSccd()); // 关联报关单境内收发货单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
        nemsInvtHead.setRltEntryRcvgdEtpsno(syncInvtHead.getRltEntryBizopEtpsno()); // 关联报关单境内收发货单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填，报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。）
        nemsInvtHead.setRltEntryRcvgdEtpsNm(syncInvtHead.getRltEntryBizopEtpsNm()); // 关联报关单境内收发货单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
        nemsInvtHead.setRltEntryDclEtpsSccd(syncInvtHead.getRltEntryDclEtpsSccd()); // 关联报关单申报单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
        nemsInvtHead.setRltEntryDclEtpsno(syncInvtHead.getRltEntryDclEtpsno()); // 关联报关单海关申报单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
        nemsInvtHead.setRltEntryDclEtpsNm(syncInvtHead.getRltEntryDclEtpsNm()); // 关联报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）

        nemsInvtHead.setImpexpPortcd(syncInvtHead.getImpexpPortcd()); // 进出口口岸代码
        nemsInvtHead.setDclplcCuscd(syncInvtHead.getDclPlcCuscd()); // 申报地关区代码
        nemsInvtHead.setImpexpMarkcd(syncInvtHead.getImpexpMarkcd()); // 进出口标记代码 （返填 - I：进口，E：出口）
        nemsInvtHead.setMtpckEndprdMarkcd(syncInvtHead.getMtpckEndprdMarkcd()); // 料件成品标记代码 （I：料件，E：成品）
        nemsInvtHead.setSupvModecd(syncInvtHead.getSupvModecd()); // 监管方式代码
        nemsInvtHead.setTrspModecd(syncInvtHead.getTrspModecd()); // 运输方式代码
        nemsInvtHead.setDclcusFlag(syncInvtHead.getDclcusFlag()); // 是否报关标志 （1.报关 2.非报关）
        nemsInvtHead.setDclcusTypecd(syncInvtHead.getDclcusTypecd()); // 报关类型代码 （1.关联报关 2.对应报关；当报关标志为“1.报关”时，企业可选择“关联报关单”/“对应报关单”；当报关标志填写为“2.非报关”时，报关标志填写为“2.非报关”该项不可填。）
        nemsInvtHead.setVrfdedMarkcd(syncInvtHead.getVrfdedMarkcd()); // 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
        nemsInvtHead.setInvtIochkptStucd(syncInvtHead.getInvtIochkptStucd()); // 清单进出卡口状态代码 （系统自动反填。未出卡口，已出卡口。需求不明确，暂留）
        try {
//            nemsInvtHead.setPrevdTime(isNotBlank(syncInvtHead.getPrevdTime()) ? DateUtil.parse(syncInvtHead.getPrevdTime(), DatePattern.PURE_DATE_PATTERN) : null); // 预核扣时间
        } catch (Exception e) {
            log.error("转换预核扣时间出现异常：" + e.getMessage());
        }
        try {
//            nemsInvtHead.setFormalVrfdedTime(isNotBlank(syncInvtHead.getFormalVrfdedTime()) ? DateUtil.parse(syncInvtHead.getFormalVrfdedTime(), DatePattern.PURE_DATE_PATTERN) : null); // 正式核扣时间 （返填）
        } catch (Exception e) {
            log.error("转换正式核扣时间 （返填）出现异常：" + e.getMessage());
        }
        nemsInvtHead.setApplyNo(syncInvtHead.getApplyNo()); // 申请表编号
        nemsInvtHead.setListType(syncInvtHead.getListType()); // 流转类型 （非流转类不填写，流转类填写：A：加工贸易深加工结转、B：加工贸易余料结转、C：不作价设备结转、D：区间深加工结转、E：区间料件结转）
        nemsInvtHead.setInputCode(syncInvtHead.getInputCode()); // 录入企业编号 （保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setInputCreditCode(syncInvtHead.getInputCreditCode()); // 录入企业社会信用代码 （返填 - 保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setInputName(syncInvtHead.getInputName()); // 录入单位名称 （保存首次暂存时IC卡的企业信息）
        nemsInvtHead.setIcCardNo(syncInvtHead.getIcCardNo()); // 申报人IC卡号 （企业端专用）
        try {
            nemsInvtHead.setInputTime(isNotBlank(syncInvtHead.getInputTime()) ? DateUtil.parse(syncInvtHead.getInputTime(), DatePattern.PURE_DATE_PATTERN) : null); // 录入日期 （企业端专用）
        } catch (Exception e) {
            log.error("转换录入日期 （企业端专用）出现异常：" + e.getMessage());
        }
        try {
//            nemsInvtHead.setAddTime(isNotBlank(syncInvtHead.getAddTime()) ? DateUtil.parse(syncInvtHead.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null); // 录入日期 （企业端专用）
        } catch (Exception e) {
            log.error("转换AddTime出现异常：" + e.getMessage());
        }
        nemsInvtHead.setListStat(syncInvtHead.getListStat()); // 清单状态 （系统自动反填。1-已申报、C-退单、改单、删单、审核通过）
        nemsInvtHead.setCorrEntryDclEtpsSccd(syncInvtHead.getCorrEntryDclEtpsSccd()); // 对应报关单申报单位社会统一信用代码
        nemsInvtHead.setCorrEntryDclEtpsno(syncInvtHead.getCorrEntryDclEtpsNo()); // 对应报关单申报单位代码 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
        nemsInvtHead.setCorrEntryDclEtpsNm(syncInvtHead.getCorrEntryDclEtpsNm()); // 对应报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
        nemsInvtHead.setDecType(syncInvtHead.getDecType()); // 报关单类型 （1-进口报关单
        nemsInvtHead.setStshipTrsarvNatcd(syncInvtHead.getStshipTrsarvNatcd()); // 起运运抵国别代码
        nemsInvtHead.setInvtType(syncInvtHead.getInvtType()); // 清单类型 （(SAS项目新增)标识清单类别，0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：异常补录，默认为0：普通清单）
//        nemsInvtHead.setEntryStucd(syncInvtHead.getEntryStucd()); // 报关状态 （(SAS项目新增)标明对应（关联）报关单放行状态，目前只区分 0：未放行，1：已放行。该字段用于区域或物流账册的清单，该类型清单满足两个条件才能核扣：报关单被放行+货物全部过卡）
        nemsInvtHead.setRmk(syncInvtHead.getRmk()); // 备注
        nemsInvtHead.setGenDecFlag(syncInvtHead.getGenDecFlag()); // 是否系统生成报关单,报文使用: 1生成,2不生成
        nemsInvtHead.setStatus(syncInvtHead.getListStat()); // 0-暂存、1-申报成功、4成功发送海关、5-海关接收成功、6-海关接收失败、B-海关终审通过、C-海关退单、E-删除、N-待导入其他报文、P-预审批通过
        nemsInvtHead.setFlyId(syncInvtHead.getId());

        if (isNotEmpty(nemsInvtHead.getId())) {
            nemsInvtHead.setUpdateBy("admin");
            Date updateDate = isNotBlank(syncInvtHead.getAddTime()) ? DateUtil.parse(syncInvtHead.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null;
            if (isEmpty(updateDate)) {
                updateDate = isNotBlank(syncInvtHead.getInputTime()) ? DateUtil.parse(syncInvtHead.getInputTime(), DatePattern.PURE_DATE_PATTERN) : null;
            }
            nemsInvtHead.setUpdateDate(updateDate);
        } else {
            nemsInvtHead.setCreatePerson(isNotBlank(nemsInvtHead.getCreatePerson()) ? nemsInvtHead.getCreatePerson() : "admin"); // 创建人员
            Date createdDate = isNotBlank(syncInvtHead.getAddTime()) ? DateUtil.parse(syncInvtHead.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null;
            if (isEmpty(createdDate)) {
                createdDate = isNotBlank(syncInvtHead.getInputTime()) ? DateUtil.parse(syncInvtHead.getInputTime(), DatePattern.PURE_DATE_PATTERN) : null;
            }
            nemsInvtHead.setCreateDate(createdDate); // 创建日期
        }
        /*
         * 转换报关单表体
         */
        setInvtListFromSync(nemsInvtHead, syncInvtHead);
    }

    /**
     * 转换核注单表体
     *
     * @param nemsInvtHead
     * @param syncInvtHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 16:25
     */
    private void setInvtListFromSync(NemsInvtHead nemsInvtHead, SyncNemsInvtHead syncInvtHead) {
        if (isNotEmpty(syncInvtHead.getInvtListType())) {
            Map<String, NemsInvtList> invtListMap = new HashMap<>(16);
            if (isNotEmpty(nemsInvtHead.getId())) {
                List<NemsInvtList> nemsInvtListList = nemsInvtHead.getNemsInvtLists();
                if (isNotEmpty(nemsInvtListList)) {
                    for (NemsInvtList nemsInvtList : nemsInvtListList) {
                        invtListMap.put(nemsInvtList.getInvId() + "|" + nemsInvtList.getGdsseqNo(), nemsInvtList);
                    }
                }
            }
            List<NemsInvtList> nemsInvtListList = new ArrayList<>(16);
            for (SyncNemsInvtList syncNemsInvtList : syncInvtHead.getInvtListType()) {
                NemsInvtList nemsInvtList = isNotEmpty(invtListMap.get(nemsInvtHead.getId() + "|" + syncNemsInvtList.getGdsSeqno())) ? invtListMap.get(nemsInvtHead.getId() + "|" + syncNemsInvtList.getGdsSeqno()) : new NemsInvtList();
                nemsInvtList.setSeqNo(syncNemsInvtList.getSeqNo()); // 中心统一编号 (返填 - 系统暂存时自动生成并返填）
                nemsInvtList.setGdsseqNo(isNotBlank(syncNemsInvtList.getGdsSeqno()) ? Integer.valueOf(syncNemsInvtList.getGdsSeqno()) : null); // 商品序号
                nemsInvtList.setPutrecSeqno(isNotBlank(syncNemsInvtList.getPutrecSeqno()) ? Integer.valueOf(syncNemsInvtList.getPutrecSeqno()) : null); // 备案序号(对应底账序号）
                // 2025/05/25 18:50@ZHANGCHAO 追加/变更/完善：自动备案序号！！
                nemsInvtList.setAutoNo(isNotBlank(syncNemsInvtList.getParam3()) ? syncNemsInvtList.getParam3() : null);
                if (isEmpty(nemsInvtList.getPutrecSeqno())) {
                    nemsInvtList.setPutrecSeqno(isNotBlank(nemsInvtList.getAutoNo()) ? Integer.valueOf(nemsInvtList.getAutoNo()) : null);
                }
                nemsInvtList.setGdsMtno(syncNemsInvtList.getGdsMtno()); // 商品料号 （企业可录入，也可根据企业录入的备案序号从备案数据中获取并返填）
                nemsInvtList.setHscode(syncNemsInvtList.getGdecd()); // 商品编码 （系统自动返填。参数值如下：0-未修改1-修改2-删除3-增加）
                nemsInvtList.setHsname(syncNemsInvtList.getGdsNm()); // 商品名称 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setHsmodel(syncNemsInvtList.getGdsSpcfModelDesc()); // 商品规格型号 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setDclUnitcd(syncNemsInvtList.getDclUnitcd()); // 申报计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setLawfUnitcd(syncNemsInvtList.getLawfUnitcd()); // 法定计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setSecdlawfUnitcd(syncNemsInvtList.getSecdLawfUnitcd()); // 法定第二计量 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setNatcd(syncNemsInvtList.getDestinationNatcd()); // 最终目的国
                nemsInvtList.setDclUprcamt(isNotBlank(syncNemsInvtList.getDclUprcAmt()) ? new BigDecimal(syncNemsInvtList.getDclUprcAmt()) : null); // 企业申报单价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setDclTotalamt(isNotBlank(syncNemsInvtList.getDclTotalAmt()) ? new BigDecimal(syncNemsInvtList.getDclTotalAmt()) : null); // 企业申报总价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setUsdstatTotalamt(isNotBlank(syncNemsInvtList.getUsdStatTotalAmt()) ? new BigDecimal(syncNemsInvtList.getUsdStatTotalAmt()) : null); // 美元统计总金额
                nemsInvtList.setDclCurrcd(syncNemsInvtList.getDclCurrcd()); // 币制
                nemsInvtList.setLawfQty(isNotBlank(syncNemsInvtList.getLawfQty()) ? new BigDecimal(syncNemsInvtList.getLawfQty()) : null); // 法定数量
                nemsInvtList.setSecdLawfQty(isNotBlank(syncNemsInvtList.getSecdLawfQty()) ? new BigDecimal(syncNemsInvtList.getSecdLawfQty()) : null); // 第二法定数量 （当法定第二计量单位为空时，该项为非必填）
                nemsInvtList.setWtsfVal(isNotBlank(syncNemsInvtList.getWtSfVal()) ? new BigDecimal(syncNemsInvtList.getWtSfVal()) : null); // 重量比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setFstsfVal(isNotBlank(syncNemsInvtList.getFstSfVal()) ? new BigDecimal(syncNemsInvtList.getFstSfVal()) : null); // 第一比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setSecdsfVal(isNotBlank(syncNemsInvtList.getSecdSfVal()) ? new BigDecimal(syncNemsInvtList.getSecdSfVal()) : null); // 第二比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                nemsInvtList.setDclQty(isNotBlank(syncNemsInvtList.getDclQty()) ? new BigDecimal(syncNemsInvtList.getDclQty()) : null); // 申报数量* （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                nemsInvtList.setGrossWt(isNotEmpty(syncNemsInvtList.getGrossWt()) ? syncNemsInvtList.getGrossWt() : null); // 毛重
                nemsInvtList.setNetWt(isNotEmpty(syncNemsInvtList.getNetWt()) ? syncNemsInvtList.getNetWt() : null); // 净重
                nemsInvtList.setUseCd(syncNemsInvtList.getUseCd()); // 用途代码*
                nemsInvtList.setLvyrlfModecd(syncNemsInvtList.getLvyrlfModecd()); // 征免方式
                nemsInvtList.setUcnsVerno(syncNemsInvtList.getUcnsVerno()); // 单耗版本号 （成品可填。手册不填，账册由开关控制是否必填。需看单耗该字段如何定义）（E02-04）
                nemsInvtList.setModfMarkcd(syncNemsInvtList.getModfMarkcd());
                nemsInvtList.setModfMarkcdName(syncNemsInvtList.getModfMarkcdName());
                nemsInvtList.setEntryGdsSeqno(isNotBlank(syncNemsInvtList.getEntryGdsSeqno()) ? Integer.valueOf(syncNemsInvtList.getEntryGdsSeqno()) : null); // 报关单商品序号  （企业可录入，如果企业不录入，系统自动返填）（E02-05）
                nemsInvtList.setClymarkcd(syncNemsInvtList.getClyMarkcd()); // 归类标志
                nemsInvtList.setApplyTbSeqnoA(isNotBlank(syncNemsInvtList.getApplyTbSeqno()) ? Integer.valueOf(syncNemsInvtList.getApplyTbSeqno()) : null); // 流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
                nemsInvtList.setApplyTbSeqnoB(syncNemsInvtList.getApplyTbSeqno()); // 流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
                try {
                    nemsInvtList.setAddTime(isNotBlank(syncNemsInvtList.getAddTime()) ? DateUtil.parse(syncNemsInvtList.getAddTime(), DatePattern.PURE_DATE_PATTERN) : null); // 入库时间 （返填）
                } catch (Exception e) {
                    log.error("转换入库时间 （返填）出现异常：" + e.getMessage());
                }
//                nemsInvtList.setPassPortusedQty(isNotBlank(syncNemsInvtList.getPassportUsedQty()) ? new BigDecimal(syncNemsInvtList.getPassportUsedQty()) : null); // 核放单已用数量 （(SAS项目新增) 已生成核放单的商品数量，用于控制核放单商品数量超量）
//                nemsInvtList.setRmk(syncNemsInvtList.getRmk()); // 备注
                nemsInvtList.setOriginCountry(syncNemsInvtList.getNatcd()); // 原产国
                nemsInvtList.setParam1(syncNemsInvtList.getParam1());//来源标识
                nemsInvtListList.add(nemsInvtList);
            }
            nemsInvtHead.setNemsInvtLists(nemsInvtListList);
        }
    }

    /**
     * 更新同步核注单数据，同时新增核注单表体
     *
     * @param syncNemsInvtHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 15:26
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSyncInvt(SyncNemsInvtHead syncNemsInvtHead) {
        if (isBlank(syncNemsInvtHead.getId())) {
            return;
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        syncNemsInvtHead.setUpdateBy("TASK");
        syncNemsInvtHead.setUpdateDate(new Date());
        syncNemsInvtHeadMapper.updateById(syncNemsInvtHead);
        if (isNotEmpty(syncNemsInvtHead.getInvtListType())) {
            List<SyncNemsInvtList> syncNemsInvtLists = new ArrayList<>();
            syncNemsInvtHead.getInvtListType().forEach(item -> {
                item.setHeadId(syncNemsInvtHead.getId());
                item.setTenantId(syncNemsInvtHead.getTenantId());
                item.setCreateBy("TASK");
                item.setCreateDate(new Date());
                syncNemsInvtLists.add(item);
            });
            if (isNotEmpty(syncNemsInvtLists)) {
                syncNemsInvtListService.remove(new LambdaQueryWrapper<SyncNemsInvtList>()
                        .eq(SyncNemsInvtList::getHeadId, syncNemsInvtHead.getId()));
                syncNemsInvtListService.saveBatch(syncNemsInvtLists);
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
    }

    /**
     * 保存同步核注单数据
     *
     * @param syncNemsInvtHeads
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 14:49
     */
    private void saveSyncInvts(List<SyncNemsInvtHead> syncNemsInvtHeads) {
        if (isEmpty(syncNemsInvtHeads)) {
            return;
        }
        List<SyncNemsInvtHead> syncNemsInvtHeadList = new ArrayList<>();
        syncNemsInvtHeads.forEach(syncNemsInvtHead -> {
            SyncNemsInvtHead one = syncNemsInvtHeadMapper.selectOne(new LambdaQueryWrapper<SyncNemsInvtHead>()
                    .eq(SyncNemsInvtHead::getSeqNo, syncNemsInvtHead.getSeqNo())
                    .eq(SyncNemsInvtHead::getTenantId, syncNemsInvtHead.getTenantId()));
            if (isEmpty(one)) {
                syncNemsInvtHead.setId(IdWorker.getIdStr());
                syncNemsInvtHead.setCreateBy("TASK");
                syncNemsInvtHead.setCreateDate(new Date());
                syncNemsInvtHeadList.add(syncNemsInvtHead);
            }
        });
        syncNemsInvtHeadService.saveBatch(syncNemsInvtHeadList);
    }

    /**
     * 将导入的核注单数据分组！
     *
     * @param dataList
     * @return com.yorma.dcl.entity.NemsInvtHead
     * <AUTHOR>
     * @date 2021/2/22 9:34
     **/
    private NemsInvtHead getFixImportInvtData(List<?> dataList) {
        List<NemsInvtHeadEntity> invtHeadEntityList = new ArrayList<>();
        List<NemsInvtListEntity> invtListEntityList = new ArrayList<>();
        if (isNotEmpty(dataList)) {
            for (Object o : dataList) {
                if (o instanceof NemsInvtHeadEntity) {
                    invtHeadEntityList.add((NemsInvtHeadEntity) o);
                } else if (o instanceof NemsInvtListEntity) {
                    invtListEntityList.add((NemsInvtListEntity) o);
                }
            }
        }
        List<NemsInvtHead> invtHeadList = new ArrayList<>();
        if (isNotEmpty(invtHeadEntityList)) {
            for (NemsInvtHeadEntity nemsInvtHeadEntity : invtHeadEntityList) {
                NemsInvtHead nemsInvtHead = new NemsInvtHead();
                BeanUtil.copyProperties(nemsInvtHeadEntity, nemsInvtHead, CopyOptions.create().ignoreNullValue());
                // 处理出入境关别和运抵国启运国
                if (isNotBlank(nemsInvtHeadEntity.getEmpexpPortcd())) {
                    nemsInvtHead.setImpexpPortcd(nemsInvtHeadEntity.getEmpexpPortcd());
                }
                if (isNotBlank(nemsInvtHeadEntity.getStshipTrsarvNatcd1())) {
                    nemsInvtHead.setStshipTrsarvNatcd(nemsInvtHeadEntity.getStshipTrsarvNatcd1());
                }
                invtHeadList.add(nemsInvtHead);
            }
        }
        if (invtHeadList.size() != 1) {
            return null;
        }
        List<NemsInvtList> invtListList = new ArrayList<>();
        if (isNotEmpty(invtListEntityList)) {
            for (NemsInvtListEntity nemsInvtListEntity : invtListEntityList) {
                NemsInvtList invtList = new NemsInvtList();
                BeanUtil.copyProperties(nemsInvtListEntity, invtList, CopyOptions.create().ignoreNullValue());
                if (isNotEmpty(nemsInvtListEntity.getSecdLawfQty1())) {
                    invtList.setSecdLawfQty(nemsInvtListEntity.getSecdLawfQty1());
                }
                invtListList.add(invtList);
            }
        }
        NemsInvtHead head = invtHeadList.get(0);
        head.setNemsInvtLists(invtListList);
        return head;
    }

    /**
     * 获取进出口标识
     *
     * @param invtHead
     * @param inputstream
     * @return void
     * <AUTHOR>
     * @date 2021/3/1 10:43
     **/
    private void getImpexpMarkcdForInvt(NemsInvtHead invtHead, InputStream inputstream) throws IOException, InvalidFormatException {
        Workbook book = null;
        if (!(inputstream.markSupported())) {
            inputstream = new PushbackInputStream(inputstream, 8);
        }
        if (hasPOIFSHeader(inputstream)) {
            book = new HSSFWorkbook(inputstream);
        } else if (hasOOXMLHeader(inputstream)) {
            book = new XSSFWorkbook(OPCPackage.open(inputstream));
        }
        Sheet sheet = book.getSheetAt(0);
        if (isNotEmpty(sheet.getRow(1))) {
            for (int i = sheet.getRow(1).getFirstCellNum(); i < sheet.getRow(1).getLastCellNum(); i++) {
                if (sheet.getRow(1).getCell(i).getCellType().equals(CellType.STRING)) {
                    if (isNotBlank(sheet.getRow(1).getCell(i).getStringCellValue())) {
                        if (sheet.getRow(1).getCell(i).getStringCellValue().contains("进口")) {
                            invtHead.setImpexpMarkcd("I");
                        }
                        if (sheet.getRow(1).getCell(i).getStringCellValue().contains("出口")) {
                            invtHead.setImpexpMarkcd("E");
                        }
                        break;
                    }
                }
            }
        }

    }

    /**
     * @param id
     * @param passageway
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/30 9:24
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> sendMessage(String id, String passageway) {
        if (isBlank(id)) {
            return Result.error("核注单ID不能为空！");
        }
        NemsInvtHead invtHead = baseMapper.selectById(id);
        if (isEmpty(invtHead)) {
            return Result.error("[" + id + "]未找到对应的核注单信息！");
        }
        // 已发送：
        if (isNotEmpty(invtHead.getSend()) && invtHead.getSend()) {
            // 已删单的：DCL_TYPECD=3或者STATUS=E，则不允许任何操作（编辑-保存、删除、审核、弃审、发送报文等）
            if ("3".equals(invtHead.getDclTypecd()) || "E".equals(invtHead.getStatus())) {
                return Result.error("已删单的核注单不允许发送报文！");
            }
            // 预核扣且DCL_TYPECD≠3且STATUS≠E
            if ("1".equals(invtHead.getVrfdedMarkcd())
                    && !"3".equals(invtHead.getDclTypecd())
                    && !"E".equals(invtHead.getStatus())) {
                // STATUS≠0或空/null，不允许删除、修改，弃审、审核。
                // STATUS=其他（除上面），不允许任何操作（包括删单申请）
                if ("0".equals(invtHead.getStatus()) || isBlank(invtHead.getStatus())) {
                } else {
                    return Result.error("预核扣且非暂存的核注单不允许发送报文！");
                }
            }
            // 已核扣且DCL_TYPECD≠3
            if ("2".equals(invtHead.getVrfdedMarkcd()) && !"3".equals(invtHead.getDclTypecd())) {
                return Result.error("已核扣的核注单不允许发送报文！");
            }
//            return Result.error("核注单[" + id + "]已推送，不允许重复推送！");
        }
        if (!"0".equals(invtHead.getVrfdedMarkcd())) {
            if ("1".equals(invtHead.getVrfdedMarkcd())) {
                return Result.error("核注单已经处于预核扣状态不允许发送报文");
            }
            if ("2".equals(invtHead.getVrfdedMarkcd())) {
                return Result.error("核注单已经处于已核扣状态不允许发送报文");
            }
        }
        if (isBlank(invtHead.getPutrecNo())) {
            return Result.error("核注单[" + id + "]无关联账册，无法处理账册数量！");
        }
        StringBuilder msg = new StringBuilder();
        // TODO: 核注单推送那里，也可以先实现到：变更状态，占用，后期补上实际发送报文就好了
        // 2024/3/11 11:33@ZHANGCHAO 追加/变更/完善：出入库单集中申报生成的核注单，不再处理占用！！
        // 2024/3/13 9:17@ZHANGCHAO 追加/变更/完善：只有在第一次发送时，才走占用逻辑！！
        PtsEmsHead emsHead = emsHeadMapper.selectOne(new QueryWrapper<PtsEmsHead>().lambda()
                .eq(PtsEmsHead::getEmsNo, invtHead.getPutrecNo())
                .eq(PtsEmsHead::getTenantId, (isBlank(TenantContext.getTenant()) || "0".equals(TenantContext.getTenant())) ? invtHead.getTenantId() : Long.parseLong(TenantContext.getTenant())));
        boolean isWLZC = false;
        if (isNotEmpty(emsHead)) {
            if ("TW".equals(emsHead.getEmsType()) || "L".equals(emsHead.getEmsType())) {
                isWLZC = true;
            }
        }
        // 只有物流账册才有此逻辑！！
        if (isWLZC) {
            if (!(isNotEmpty(invtHead.getSend()) && invtHead.getSend())) {
                if (E.equals(invtHead.getImpexpMarkcd()) && isBlank(invtHead.getStockHeadId())) { // 出口核注单
                    List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                            .eq(NemsInvtList::getInvId, id));
                    if (isNotEmpty(invtLists)) {
                        StockParamVO stockParamVO = new StockParamVO();
                        stockParamVO.setNemsInvtHead(invtHead);
                        for (NemsInvtList invtList : invtLists) {
                            stockParamVO.setNemsInvtList(invtList);
                            try {
                                // 解除占用
                                Result<?> deOccResult = emsStocksFlowService.deOccupyHandle(stockParamVO);
                                if (!deOccResult.isSuccess()) {
                                    throw new RuntimeException(deOccResult.getMessage());
                                }
                                // 占用
                                Result<?> result = emsStocksFlowService.occupyGoods(stockParamVO);
                                if (!result.isSuccess()) {
                                    throw new RuntimeException(result.getMessage());
                                } else {
                                    msg.append("表体[备案序号：").append(invtList.getPutrecSeqno()).append("，物料号：")
                                            .append(invtList.getGdsMtno()).append("]已核增占用数量：").append(stockParamVO.getNemsInvtList().getDclQty()).append("；");
                                    log.info(msg.toString());
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e.getMessage());
                            }
                        }
                    }
                } else {
                    log.info("这是出入库单生成的核注单的推送报文操作，不再处理占用逻辑！！！！！！！");
                }
            } else {
                log.info("这是重复推送，不处理占用逻辑！！！！！！！");
            }
        }
        // 2024/11/1 13:45@ZHANGCHAO 追加/变更/完善：核注单报文发送完善！！
        List<StockHeadType> stockHeadTypes = null;
        if (isNotEmpty(invtHead.getStockHeadId())){
            stockHeadTypes = stockHeadTypeMapper.selectBatchIds(Arrays.asList(invtHead.getStockHeadId().split(",")));
        }
        List<NemsInvtList> invtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>().lambda()
                .eq(NemsInvtList::getInvId, id)
                .orderByAsc(NemsInvtList::getGdsseqNo));
        String fileName = new StringBuffer(invtHead.getId().toString()).append("-INVT001").toString();//文件名称
        /*
         * 核注单报文默认不自动生成报关单
         * 2024/12/16 13:13@ZHANGCHAO
         */
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "NoAutoGenerateDecWhenSendInvt")
                .eq(SysConfig::getTenantId, invtHead.getTenantId()));
        if (isNotEmpty(sysConfigs) && "1".equals(sysConfigs.get(0).getConfigValue())) {
            // 核注单报文默认不自动生成报关单
            invtHead.setGenDecFlag("2");
            log.info("【NoAutoGenerateDecWhenSendInvt】「启用」核注单报文默认不自动生成报关单！！{}", invtHead.getTenantId());
        } else {
            log.info("【NoAutoGenerateDecWhenSendInvt】「未启用」核注单报文默认不自动生成报关单！！{}", invtHead.getTenantId());
        }

//        MsgFtpConfig ftpConfig = null;
//        ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendInvtPath);
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendInvtPath());
        MsgFtpConfig ftpConfigSc = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendNptsPath());
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
        boolean uploadFlag = false;
        try {
            if (SFTP.equals(ftpType)) {
                if ("B1".equals(invtHead.getSysId())) {
                    uploadFlag = new SFTPUtil(ftpConfigSc)
                            .upload(String.format("%s.zip", fileName),
                                    new ByteArrayInputStream(MessageFileUtil
                                            .exportZip(InvtMessageUtil.generateInvtMessage(invtHead, invtLists, stockHeadTypes),
                                                    String.format("%s.xml", fileName)).toByteArray()));
                } else {
                    uploadFlag = new SFTPUtil(ftpConfig)
                            .upload(String.format("%s.zip", fileName),
                                    new ByteArrayInputStream(MessageFileUtil
                                            .exportZip(NemsInvtMessageUtil.generateSignature(invtHead, invtLists, fileName,stockHeadTypes),
                                                    String.format("%s.xml", fileName)).toByteArray()));
                }
            } else if (FTP.equals(ftpType)) {
                if ("B1".equals(invtHead.getSysId())) {
                    uploadFlag = new FTPUtil(ftpConfigSc)
                            .upload(String.format("%s.zip", fileName),
                                    new ByteArrayInputStream(MessageFileUtil
                                            .exportZip(InvtMessageUtil.generateInvtMessage(invtHead, invtLists, stockHeadTypes),
                                                    String.format("%s.xml", fileName)).toByteArray()));
                } else {
                    uploadFlag = new FTPUtil(ftpConfig)
                            .upload(String.format("%s.zip", fileName),
                                    new ByteArrayInputStream(MessageFileUtil
                                            .exportZip(NemsInvtMessageUtil.generateSignature(invtHead, invtLists, fileName,stockHeadTypes),
                                                    String.format("%s.xml", fileName)).toByteArray()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            ExceptionUtil.getFullStackTrace(e);
        }
        if (!uploadFlag) {
            return Result.error("发送失败");
        }

        baseMapper.update(null, new UpdateWrapper<NemsInvtHead>().lambda()
                .set(NemsInvtHead::getSend, true)
                .eq(NemsInvtHead::getId, invtHead.getId()));
        return Result.ok(msg.toString());
    }

    /**
     * 核注单生成核放单
     *
     * @param passPortHead
     * @param nemsInvtHead
     */
    private void createPassPortHeadByInvtHead(PassPortHead passPortHead, NemsInvtHead nemsInvtHead, Map<String, DecHead> decHeadMap, Map<Long, NemsInvtList> invtListMap) {
        passPortHead.setId(IdWorker.getId());
        passPortHead.setMasterCuscd("4301");//主管关区代码
        passPortHead.setDclTypecd("1");//申报类型代码
        passPortHead.setBindTypecd("2");//绑定类型代码
        passPortHead.setRltTbTypecd("1");//关联单证类型代码
        passPortHead.setVehicleNo("鲁A7J5B1");//承运车车牌号
        passPortHead.setVehicleIcNo("鲁A7J5B1");//IC卡号(电子车牌）
        passPortHead.setVehicleWt(new BigDecimal("1619"));//车自重
        passPortHead.setDclEtpsno("3701983939");//申报企业编号
        passPortHead.setDclEtpsNm("山东迅吉安国际物流有限公司");//申报企业名称
        passPortHead.setDclEtpsSccd("91370100560757589T");//申报企业社会信用代码
        passPortHead.setInputCode("3701983939");//录入单位代码
        passPortHead.setInputSccd("91370100560757589T");//录入单位社会信用代码
        passPortHead.setInputName("山东迅吉安国际物流有限公司");//录入单位名称
        passPortHead.setCol1(true);//到货确认标志
        passPortHead.setAreainOriactNo("");//区内账册编号
        passPortHead.setContainerNo("");//集装箱号
        passPortHead.setVehicleFrameNo("");//车架号
        passPortHead.setVehicleFrameWt(null);//车架重
        passPortHead.setContainerType("");//集装箱箱型
        passPortHead.setContainerWt(null);//集装箱重
//        passPortHead.setEtpsPreentNo("");//企业内部编号
        passPortHead.setRmk("");//备注


        passPortHead.setAudited(false);//是否审核
        passPortHead.setSend(false);//是否发送报文

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        passPortHead.setDclErConc(loginUser.getUsername());//申请人及联系方式
        passPortHead.setCreateBy(loginUser.getUsername());//创建人
        passPortHead.setCreateDate(new Date());//创建时间
        passPortHead.setDecPushStatus("0");
        try {
            passPortHead.setInputDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));//录入日期
        } catch (Exception e) {
            e.printStackTrace();
        }

        passPortHead.setApplyNumber(nemsInvtHead.getApplyNumber());//委托流水号
        passPortHead.setAreainEtpsNo(nemsInvtHead.getBizopEtpsno());//区内企业编码
        passPortHead.setAreainEtpsNm(nemsInvtHead.getBizopEtpsNm());//区内企业名称
        passPortHead.setAreainEtpsSccd(nemsInvtHead.getBizopEtpsSccd());//区内企业社会信用代码
        passPortHead.setIoTypecd(nemsInvtHead.getImpexpMarkcd());//进出标志代码
        passPortHead.setRltNo(nemsInvtHead.getBondInvtNo());//关联单证编号
        passPortHead.setIsReceipt(nemsInvtHead.getIsReceipt());//确认收货
        passPortHead.setReceiptDate(nemsInvtHead.getReceiptDate());//收货时间
        if ("1".equals(nemsInvtHead.getDclcusFlag())) {
            if (decHeadMap.containsKey(nemsInvtHead.getId().toString())) {
                DecHead decHead = decHeadMap.get(nemsInvtHead.getId().toString());
                passPortHead.setTotalGrossWt(decHead.getGrossWeight());//货物总毛重
                passPortHead.setTotalNetWt(decHead.getNetWeight());//货物总净重
            }
            passPortHead.setPassportTypecd("2");//核放单类型代码
        } else {
            if (invtListMap.containsKey(nemsInvtHead.getId())) {
                NemsInvtList nemsInvtList = invtListMap.get(nemsInvtHead.getId());
                passPortHead.setTotalGrossWt(nemsInvtList.getGrossWt());//货物总毛重
                passPortHead.setTotalNetWt(nemsInvtList.getNetWt());//货物总净重
            }
            passPortHead.setPassportTypecd("3");//核放单类型代码
        }

        BigDecimal totalWt = (passPortHead.getTotalGrossWt() != null ? passPortHead.getTotalGrossWt() : BigDecimal.ZERO)
                .add(passPortHead.getVehicleWt());
        passPortHead.setTotalWt(totalWt);//总重量
        passPortHead.setRelationId(nemsInvtHead.getId().toString());//关联id
        passPortHead.setAreainOriactNo(nemsInvtHead.getPutrecNo());//账册号
        passPortHead.setEtpsPreentNo(nemsInvtHead.getEtpsInnerInvtNo() + "_HF");//企业内部编号
    }

    /**
     * 根据类型判断报关单的进出口和类型
     *
     * @param decType
     * @param decHead
     */
    private void judgeDdeclarationType(String decType, DecHead decHead) {
        switch (decType) {
            case "1"://进口报关单
            case "I"://进口提前/暂时进口报关单
            case "K"://进口提前/中欧班列报关单
            case "X"://进口两部申报报关单
            case "e"://进口两部申报一次录入报关单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("0");//一般报关单
                break;
            case "2"://出口报关单
            case "H"://出口提前/工厂验放报关单
            case "J"://出口提前/暂时出口报关单
            case "L"://出口提前/中欧班列报关单
            case "M"://出口提前/市场采购报关单
            case "N"://出口提前/空运连程报关单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("0");//一般报关单
                break;
            case "3"://进境备案清单
            case "5"://进境两单一审备案清单
            case "O"://进口提前/工厂验放备案清单
            case "Y"://进口两部申报备案清单
            case "f"://进口两部申报一次录入备案清单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("2");//备案清单
                break;
            case "4"://出境备案清单
            case "6"://出境两单一审备案清单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("2");//备案清单
                break;
            case "9"://转关提前进口报关单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("1");//转关提前报关单
                break;
            case "A"://转关提前出口报关单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("1");//转关提前报关单
                break;
            case "B"://转关提前进境备案清单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("3");//转关提前备案清单
                break;
            case "C"://转关提前出境备案清单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("3");//转关提前备案清单
                break;
            case "F":
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("4");//备案清单
                break;
            case "":
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("0");//一般报关单
                break;
        }
    }

    /**
     * 获取报关单表体
     *
     * @param nemsInvtList
     * @param decList
     */
    private void toDecList(NemsInvtList nemsInvtList, DecList decList, String decId, NemsInvtHead invtHead) {
//        decList.setApplyNumber(nemsInvtList.getApplyNumber());
        decList.setGoodsId(nemsInvtList.getGoodsId());
        decList.setDecId(decId);
        decList.setCiqCode(nemsInvtList.getCiqCode());
        decList.setCiqName(nemsInvtList.getCiqName());
        decList.setCount1(nemsInvtList.getLawfQty());
        decList.setCount2(nemsInvtList.getSecdLawfQty());
        decList.setCurrencyCode(nemsInvtList.getDclCurrcd());
        decList.setDesCountry(nemsInvtList.getOriginCountry());//原产国
        decList.setDestinationCountry(nemsInvtList.getNatcd());//最终目的国
        decList.setGoodsCount(nemsInvtList.getDclQty());//申报数量
        decList.setHscode(nemsInvtList.getHscode());//税号
        decList.setHsmodel(nemsInvtList.getHsmodel());//申报要素
        decList.setHsname(nemsInvtList.getHsname());//品名
        decList.setHstype(nemsInvtList.getHstype());//法捡类型
        decList.setNetWeight(nemsInvtList.getNetWt());//净重
        decList.setPrice(nemsInvtList.getDclUprcamt());//单价
        decList.setSupvModecd(nemsInvtList.getSupvModecd());
        decList.setTotal(nemsInvtList.getDclTotalamt());//总价
        decList.setUnit1(nemsInvtList.getLawfUnitcd());//法定单位
        decList.setUnit2(nemsInvtList.getSecdlawfUnitcd());//法定第二单位
        decList.setUnitCode(nemsInvtList.getDclUnitcd());//计量单位
        decList.setCurrencyCode(nemsInvtList.getDclCurrcd());//币制
        decList.setItem(isNotEmpty(nemsInvtList.getEntryGdsSeqno()) ? nemsInvtList.getEntryGdsSeqno() : decList.getItem());//序号
        //关联取模板，对应取核注单
        if (StrUtil.isNotEmpty(invtHead.getDclcusTypecd()) && "2".equals(invtHead.getDclcusTypecd())) {
            decList.setFaxTypeCode(nemsInvtList.getLvyrlfModecd());//征免方式
        }
        decList.setExgVersion(nemsInvtList.getUcnsVerno());//单耗版本号
    }

    /**
     * 库存冲正
     *
     * @param nemsInvtHead
     * @return
     */
    private Result<NemsInvtHead> stockRectify(NemsInvtHead nemsInvtHead) {
        Result<?> stockRectifyYmMsg = emsStockService.rectify(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), nemsInvtHead.getEmsFlowsId().toString());
        log.info("!!!!!!!!!!!！！！stockRectifyYmMsg：" + stockRectifyYmMsg.isSuccess());
        log.info("!!!!!!!!!!!！！！stockRectifyYmMsg：" + stockRectifyYmMsg.getMessage());
        log.info("!!!!!!!!!!!！！！stockRectifyYmMsg：" + stockRectifyYmMsg.getCode());
        if (!stockRectifyYmMsg.isSuccess()) {
            throw new RuntimeException(stockRectifyYmMsg.getMessage());
        }
        return Result.ok(nemsInvtHead);
    }

    /**
     * 物流账册,已核扣时入区核增或新备案，出区核减库存数
     *
     * @param nemsInvtHead 核注单
     * @param invtLists    核注单表体
     * @return 执行结果
     */
    private Result<NemsInvtHead> increaseOrDecreaseQty(NemsInvtHead nemsInvtHead, List<NemsInvtList> invtLists) {
        Result<NemsInvtHead> invtHeadYmMsg = new Result<>();
        List<PtsEmsAimg> emsAimgs = new ArrayList<>();//出区核扣商品数量/入区累计数量
        List<PtsEmsAimg> addAimgs = new ArrayList<>();//入区新备案
        //获取存在的料件集合
        Map<Integer, PtsEmsAimg> gNoMap = new HashMap<>();
        //入区判断是否存在新备案的料件
        if ("I".equals(nemsInvtHead.getImpexpMarkcd())) {
            //获取表体的备案序号判断哪些为新备案哪些为核增数量
            List<String> gNoList = new ArrayList<>();
            for (NemsInvtList invtList : invtLists) {
                if (invtList.getPutrecSeqno() == null) {
                    invtHeadYmMsg.error("表体的备案序号不允许为空");
                    break;
                }
                gNoList.add(invtList.getPutrecSeqno().toString());
            }
            if (!invtHeadYmMsg.isSuccess()) {
                return invtHeadYmMsg;
            }
            if (isNotEmpty(gNoList)) {
                List<PtsEmsAimg> emsAimgList = emsAimgMapper.listAimgList(nemsInvtHead.getPutrecNo(), gNoList);
                if (isNotEmpty(emsAimgList)) {
                    emsAimgList.forEach(v -> {
                        gNoMap.put(v.getGNo(), v);
                    });
                }
            }
        }

        invtLists.forEach(v -> {
            PtsEmsAimg emsAimg = new PtsEmsAimg();
            emsAimg.setStockQty(v.getDclQty());
            emsAimg.setEmsNo(nemsInvtHead.getPutrecNo());
            emsAimg.setGNo(v.getPutrecSeqno());
            if (!gNoMap.containsKey(v.getPutrecSeqno()) && "I".equals(nemsInvtHead.getImpexpMarkcd())) {
                emsAimg.setOccupyQty(BigDecimal.ZERO);
                emsAimg.setPredistribution(BigDecimal.ZERO);
                emsAimg.setCustomerId(Long.valueOf(TenantContext.getTenant()));

                emsAimg.setCopGno(v.getGdsMtno());
                emsAimg.setCodet(v.getHscode());
                emsAimg.setGName(v.getHsname());
                emsAimg.setGModel(v.getHsmodel());
                emsAimg.setUnit(v.getDclUnitcd());
                emsAimg.setUnit1(v.getLawfUnitcd());
                emsAimg.setUnit2(v.getSecdlawfUnitcd());
                emsAimg.setCountryCode(v.getOriginCountry());
                emsAimg.setDecPrice(v.getDclUprcamt());
                emsAimg.setCurr(v.getDclCurrcd());
                emsAimg.setImportedQty(v.getDclQty());
                emsAimg.setBondInvtNo(nemsInvtHead.getBondInvtNo());
                emsAimg.setBondInvtItem(v.getGdsseqNo() != null ? v.getGdsseqNo().toString() : "");
                emsAimg.setInternalNo(nemsInvtHead.getEtpsInnerInvtNo());
                emsAimg.setClearanceNo(isNotEmpty(nemsInvtHead.getEntryNo()) ? nemsInvtHead.getEntryNo() : nemsInvtHead.getRltEntryNo());
                emsAimg.setIappDate("2".equals(nemsInvtHead.getDclcusFlag()) ? nemsInvtHead.getInvtDclTime() : nemsInvtHead.getEntryDclTime());
                emsAimg.setNetWeight(v.getNetWt());
                //法定数量
                emsAimg.setCount1(v.getLawfQty());
                //法二数量
                emsAimg.setCount2(v.getSecdLawfQty());
                //单项法一数量（法定数量/申报数量）
                emsAimg.setSingCount1(v.getLawfQty().divide(v.getDclQty(), 4, BigDecimal.ROUND_HALF_UP));
                //单项法二数量（第二法定数量/申报数量）
                if (v.getSecdLawfQty() != null) {
                    emsAimg.setSingCount2(v.getSecdLawfQty().divide(v.getDclQty(), 4, BigDecimal.ROUND_HALF_UP));
                }
                //单项净重（净重/申报数量）
                if (emsAimg.getNetWeight() != null) {
                    emsAimg.setSingNetWt(emsAimg.getNetWeight().divide(v.getDclQty(), 4, BigDecimal.ROUND_HALF_UP));
                }

                if (emsAimg.getIappDate() != null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(emsAimg.getIappDate());
                    calendar.add(Calendar.YEAR, +1);
                    emsAimg.setExpiryDate(calendar.getTime());
                }
                addAimgs.add(emsAimg);
            } else {
                emsAimgs.add(emsAimg);
            }
        });

        if ("I".equals(nemsInvtHead.getImpexpMarkcd())) {
            // 入区新备案料件
            if (!addAimgs.isEmpty()) {
                Result<?> emsAimgYmMsg = emsStockService.increaseGoods(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), addAimgs, true);
                if (!emsAimgYmMsg.isSuccess()) {
                    return invtHeadYmMsg.error(emsAimgYmMsg.getMessage());
                }
                Long emsFlowsId = isNotEmpty(emsAimgYmMsg.getResult()) ? Long.valueOf((String) emsAimgYmMsg.getResult()) : null;
                nemsInvtHead.setEmsFlowsId(emsFlowsId);
            }
            // 入区核增料件数量
            if (!emsAimgs.isEmpty()) {
                Result<?> emsAimgYmMsg = emsStockService.increaseGoods(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAimgs, false);
                if (!emsAimgYmMsg.isSuccess()) {
                    throw new RuntimeException(emsAimgYmMsg.getMessage());
//                    return invtHeadYmMsg.error(emsAimgYmMsg.getMessage());
                }
                Long emsFlowsId = isNotEmpty(emsAimgYmMsg.getResult()) ? Long.valueOf((String) emsAimgYmMsg.getResult()) : null;
                nemsInvtHead.setEmsFlowsId(emsFlowsId);
            }
        } else if ("E".equals(nemsInvtHead.getImpexpMarkcd())) { //出区核扣料件数量
            Result<?> emsAimgYmMsg = emsStockService.reduceGoods(nemsInvtHead.getPutrecNo(), nemsInvtHead.getId().toString(), emsAimgs);
            if (!emsAimgYmMsg.isSuccess()) {
                return invtHeadYmMsg.error(emsAimgYmMsg.getMessage());
            }
            Long emsFlowsId = isNotEmpty(emsAimgYmMsg.getResult()) ? Long.valueOf((String) emsAimgYmMsg.getResult()) : null;
            nemsInvtHead.setEmsFlowsId(emsFlowsId);
        }
        return Result.ok(nemsInvtHead);
    }

    /**
     * @param page
     * @param invId
     * @return
     */
    @Override
    public IPage<NemsInvtList> listInvt(Page<NemsInvtList> page, String invId) {
        IPage<NemsInvtList> pageList = nemsInvtListMapper.selectPage(page, new LambdaQueryWrapper<NemsInvtList>()
                .eq(NemsInvtList::getInvId, invId)
                .orderByAsc(NemsInvtList::getGdsseqNo));
        return pageList;
    }

    /**
     * @param invId
     * @param item
     * @return
     */
    @Override
    public List<DecList> getDecListByInvtIdAndItem(String invId, String item,String entryNo) {
        List<DecList> decListByInvtId = decListMapper.getDecListByInvtIdAndItem(invId, item);
        if(!decListByInvtId.isEmpty()){
            return decListByInvtId;
        }
        List<DecList> decListByClearanceNoAndItem = decListMapper.
                getDecListByClearanceNoAndItem(entryNo, Arrays.asList(item.split(",")));
        return decListByClearanceNoAndItem;
    }

    /**
     * 根据核注单id获取报关单数据
     *
     * @param id
     * @return
     */
    @Override
    public DecHead getInvtIdDec(String id) {
        DecHead decHead = decHeadMapper.selectOne(new QueryWrapper<DecHead>().lambda()
                .eq(DecHead::getInvId, id));
        List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
                .eq(isNotEmpty(decHead.getId()), DecList::getDecId, decHead.getId()));
        decHead.setDecLists(decLists);
        return decHead;
    }

}
