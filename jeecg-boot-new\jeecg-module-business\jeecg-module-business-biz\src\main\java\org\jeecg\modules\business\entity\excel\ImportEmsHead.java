package org.jeecg.modules.business.entity.excel;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelVerify;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账册导入表头excel对应实体
 */
@Data
public class ImportEmsHead implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 预录入统一编号
     * 20240104
     */
    @Excel(name = "预录入统一编号")
    private String seqNo;
    /**
     * 手册编号
     */
    @Excel(name = "加工贸易账册编号")
    @TableField(value = "EMS_NO", updateStrategy = FieldStrategy.NEVER)
    private String emsNo;

    /**
     * 手册编号--导入用
     */
    @Excel(name = "仓库账册号")
    @ExcelVerify(notNull = true)
    @TableField(exist = false)
    private String emsNo_;

    /**
     * 手册编号
     */
    @Excel(name = "加工贸易手册编号")
    @TableField(exist = false)
    private String emsNoSC;

    /**
     * 手账册类型(加贸手册类型：B-来料加工 C-进料加工;加贸账册类型：1-E账册 2-H账册 3-耗料 4-工单;物流账册类型:TW-账册;L-账册)
     */
    @Excel(name = "手册类型", replace = { "来料加工_B", "进料加工_C", "E账册_1", "H账册_2", "耗料_3", "工单_4" })
    @TableField(exist = false)
    private String emsTypeSC;

    /**
     * 序号(自定义序号,排序用)
     */
    @Excel(name = "序号")
    @ExcelVerify(regex = "^[1-9]\\d*$")
    @TableField("ITEM")
    private Integer item;

    /**
     * 企业内部编号
     */
    @Excel(name = "企业内部编号")
    @TableField("COP_EMS_NO")
    private String copEmsNo;

    /**
     * 经营单位
     */
    @Excel(name = "经营单位编码")
    @ExcelVerify(notNull = true)
    @TableField("TRADE_CODE")
    private String tradeCode;

    /**
     * 经营单位社会信用代码
     */
    @Excel(name = "经营单位社会信用代码")
    @ExcelVerify(notNull = true)
    @TableField("TRADE_SCCD")
    private String tradeSccd;

    /**
     * 经营单位名称
     */
    @Excel(name = "经营单位名称")
    @ExcelVerify(notNull = true)
    @TableField("TRADE_NAME")
    private String tradeName;

    /**
     * 加工单位
     */
    @Excel(name = "加工单位编码")
    @ExcelVerify(notNull = true)
    @TableField("OWNER_CODE")
    private String ownerCode;

    /**
     * 加工单位名称
     */
    @Excel(name = "加工单位名称")
    @ExcelVerify(notNull = true)
    @TableField("OWNER_NAME")
    private String ownerName;

    /**
     * 加工单位社会信用代码
     */
    @Excel(name = "加工单位社会信用代码")
    @ExcelVerify(notNull = true)
    @TableField("OWNER_SCCD")
    private String ownerSccd;

    /**
     * 申报单位
     */
    @Excel(name = "申报单位编码")
    @TableField("DECLARE_CODE")
    private String declareCode;

    /**
     * 申报单位社会信用代码
     */
    @Excel(name = "申报单位社会信用代码")
    @TableField("DECLARE_SCCD")
    private String declareSccd;

    /**
     * 申报单位名称
     */
    @Excel(name = "申报单位名称")
    @TableField("DECLARE_NAME")
    private String declareName;

    /**
     * 手账册类型(加贸手册类型：B-来料加工 C-进料加工;加贸账册类型：1-E账册 2-H账册 3-耗料 4-工单;物流账册类型:TW-账册;L-账册)
     */
    @Excel(name = "账册类型", replace = { "来料加工_B", "进料加工_C", "E账册_1", "H账册_2", "耗料_3", "工单_4" })
    @ExcelVerify(notNull = true)
    @TableField("EMS_TYPE")
    private String emsType;

    /**
     * 自定义用途(物流账册：M贸易 W 物流)
     */
//	@Excel(name = "账册用途")
    @TableField("USE_TYPE")
    private String useType;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField("BEGIN_DATE")
    private Date beginDate;

    /**
     * 结束有效日期
     */
    @Excel(name = "账册结束有效期", format = "yyyyMMdd")
    @TableField("END_DATE")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 结束有效日期--导入用
     */
    @Excel(name = "账册有效期", format = "yyyyMMdd")
    @TableField(exist = false)
    @JSONField(format = "yyyy-MM-dd")
    private Date endDate_;

    /**
     * 结束有效日期--导入用
     */
    @Excel(name = "有效期", format = "yyyyMMdd")
    @TableField(exist = false)
    @JSONField(format = "yyyy-MM-dd")
    private Date endDateSC;

    /**
     * 进口货物项数
     */
    @Excel(name = "进口货物项数")
    @TableField("IMG_ITEMS")
    private Integer imgItems;

    /**
     * 出口货物项数
     */
    @Excel(name = "出口货物项数")
    @TableField("EXG_ITEMS")
    private Integer exgItems;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @TableField("NOTE")
    private String note;

    /**
     * 录入日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入日期", format = "yyyyMMdd")
    @TableField("INPUT_DATE")
    private Date inputDate;

    /**
     * 申报日期
     */
    @Excel(name = "申报日期", format = "yyyyMMdd")
    @ExcelVerify(notNull = true)
    @TableField("DECLARE_DATE")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date declareDate;

    /**
     * 展期
     */
    @Excel(name = "展期", replace = { "否_0", "是_1" })
    @TableField("MODIFY_MARK")
    private String modifyMark;

    /**
     * 主管海关
     */
    @Excel(name = "主管海关", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_CODE,GQDM", dicText = "ITEM_NAME")
    @TableField("MASTER_CUSTOMS")
    private String masterCustoms;

    /**
     * 自定义状态(1:正在使用2:暂不能使用3:已核销)
     */
    @TableField("STATUS")
    private String status;

    /**
     * 锁标识
     */
    @TableField("LOCK_TAG")
    private String lockTag;

    /**
     * 登记日期
     */
    @TableField("BY_DATE")
    @JSONField(format = "yyyy-MM-dd")
    private Date byDate;

    /**
     * 登记人
     */
    @TableField("BY_NAME")
    private String byName;

    /**
     * 记账模式 (0不累计 1累计)
     */
    @Excel(name = "记账模式", replace = { "不累计_0", "可累计_1", "累计_1" })
    @TableField("KEEPING_MODE")
    private Integer keepingMode;


    /**
     * 核销周期
     */
    @Excel(name = "核销周期")
    private String verificationCycle;
    /**
     * 申报单位类型
     * 20240104
     */
    @Excel(name = "申报企业类型", replace = { "企业_1", "代理公司_2", "报关行_3" })
    private String declareType;
    /**
     * 申报单位类型
     * 20240104
     */
    @Excel(name = "申报类型", replace = { "备案_1", "变更_2", "注销_3" })
    @TableField("DCL_TYPECD")
    private String dclTypeCd;
    /**
     * 区域场所类型
     * 20240104
     */
    @Dict(dicCode = "QYCSLB")
    @Excel(name = "区域场所类别")
    private String regionalSiteType;
    /**
     * 仓库代码
     */
    @Excel(name = "仓库代码")
    private String ownerCodeCkdm;
    /**
     * 仓库名称
     */
    @Excel(name = "仓库名称")
    private String ownerNameCkname;
    /**
     * 仓库面积
     * 20240104
     */
    @Excel(name = "仓库面积")
    private BigDecimal area;
    /**
     * 仓库容积
     * 20240104
     */
    @Excel(name = "仓库容积")
    private String volume;
    /**
     * 仓库地址
     * 20240104
     */
    @Excel(name = "仓库地址")
    private String address;
    /**
     * 联系人
     * 20240104
     */
    @Excel(name = "企业联系人")
    private String associates;
    /**
     * 联系电话
     * 20240104
     */
    @Excel(name = "联系人手机号")
    private String telephone;
    /**
     * 企业类型
     * 20240104
     */
    @Excel(name = "企业类型")
    private String businessType;
    /**
     * 备案批准日期
     */
    @Excel(name = "备案批准日期", format = "yyyyMMdd")
    @TableField(exist = false)
    @JSONField(format = "yyyy-MM-dd")
    private Date recordDate;
    /**
     * 变更批准日期
     */
    @Excel(name = "变更批准日期", format = "yyyyMMdd")
    @TableField(exist = false)
    @JSONField(format = "yyyy-MM-dd")
    private Date changeDate;
    /**
     * 录入单位编码
     */
    @Excel(name = "录入单位编码")
    private String inputCode;
    /**
     * 录入单位社会信用代码
     */
    @Excel(name = "录入单位社会信用代码")
    private String inputCreditCode;
    /**
     * 录入单位名称
     */
    @Excel(name = "录入单位名称")
    private String inputName;
    /**
     * 加工企业地区代码 20250414
     */
    @Excel(name = "加工企业地区代码")
    private String regionCode;
    /**
     * 监管方式 20250414
     */
    @Excel(name = "监管方式")
    private String tradeTypeCode;

    /**
     * 进口合同号 20250414
     */
    @Excel(name = "进口合同号")
    private String contractNoI;
    /**
     * 出口合同号 20250414
     */
    @Excel(name = "出口合同号")
    private String contractNoE;
    /**
     * 征免性质 20250414
     */
    @Excel(name = "征免性质")
    private String taxTypeCode;

    /**
     * 加工种类	 20250414
     */
    @Excel(name = "加工种类")
    private String processingType;

    /**
     * 进出口岸 20250414
     */
    @Excel(name = "进出口岸")
    private String iePort;
    /**
     * 进口币制 20250414
     */
    @Excel(name = "进口币制")
    private String importCurrency;
    /**
     * 出口币制     20250414
     */
    @Excel(name = "出口币制")
    private String exportCurrency;
    /**
     * 单耗申报环节代码 20250414
     */
    @Excel(name = "单耗申报环节代码" ,replace = { "出口前_1", "报核前_2" })
    private String codeDeclarationUnitConsumption;
    /**
     * 手册用途 20250414
     */
    @Excel(name = "手册用途",replace = { "保税加工_1", "特殊行业_2", "保税维修_3", "保税研发_4", "其他_9" })
    private String manualPurpose;

    /**
     * 暂停进出口标记	20250414
     */
    @Excel(name = "暂停进出口标记" ,replace = { "未暂停_0", "已暂停_1" })
    private String suspendIe;
    /**
     * 首次出口日期 20250414
     */
    @Excel(name = "首次出口日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date firstExportDate;

    /**
     * 暂停变更标记	 20250414
     */
    @Excel(name = "暂停变更标记" ,replace = { "正常执行_0"})
    private String suspendChangeMark;
    /**
     * 自核资格标记 20250414
     */
    @Excel(name = "自核资格标记",replace = { "不具备_0", "具备_1" })
    private String selfVerificationMark;
    /**
     * 变更次数(首次申请备案时填0)
     */
    @Excel(name = "手册变更次数")
    private Integer chgTmsCnt;


    /**
     * 查询手帐册种类
     * @return (1:加贸手册, 2:加贸账册, 3:物流账册，-1:未知类型)
     */
    public int getEmsClass() {
        int cls = -1;
        if (this.emsType != null) {
            if (!this.emsType.equalsIgnoreCase("B") && !this.emsType.equalsIgnoreCase("C")) {
                if (!this.emsType.equalsIgnoreCase("1") && !this.emsType.equalsIgnoreCase("2") && !this.emsType.equalsIgnoreCase("3")) {
                    if (this.emsType.equalsIgnoreCase("TW") || this.emsType.equalsIgnoreCase("L")) {
                        cls = 3;
                    }
                } else {
                    cls = 2;
                }
            } else {
                cls = 1;
            }
        }

        return cls;
    }


}
