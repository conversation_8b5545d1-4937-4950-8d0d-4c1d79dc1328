package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.business.entity.PtsEmsCm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;

/**
 * <p>
 * 手账册单损耗表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface PtsEmsCmMapper extends BaseMapper<PtsEmsCm> {

    /**
     * 查询Ems的详细信息
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsCm对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsCm> listEmsDetail(Page page, EmsQueryDto emsQueryDto);

    /**
     * 查询Ems的详细信息 - 报表中心
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsCm对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsCm> listEmsCmByReport(Page page, EmsQueryDto emsQueryDto);



}
