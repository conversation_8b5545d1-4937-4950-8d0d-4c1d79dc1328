<template>
	<div>
		<a-card :bordered="false">
			<!-- 查询区域 -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline" @keyup.enter.native="searchQuery">
					<a-row :gutter="24">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="账册编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入账册编号" v-model="queryParam.emsNo"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="经营单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入经营单位" v-model="queryParam.tradeName"></a-input>
							</a-form-item>
						</a-col>
<!--						<a-col :xl="6" :sm="24" :xxl="6" :md="12">-->
<!--							<a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--								<a-select placeholder="请选择状态" allowClear showSearch v-model="queryParam.status">-->
<!--									<a-select-option value="1">正在使用</a-select-option>-->
<!--									<a-select-option value="2">暂停使用</a-select-option>-->
<!--									<a-select-option value="3">已核销</a-select-option>-->
<!--								</a-select>-->
<!--							</a-form-item>-->
<!--						</a-col>-->
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 查询区域-END -->

			<!-- 操作按钮区域 -->
			<div class="table-operator">
				<a-row>
					<a-col :span="13" style="height: 32px">
						<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
						<a-button @click="handleImport" type="primary" icon="upload">导入账册</a-button>
						<a-dropdown class="sc">
							<a-menu slot="overlay">
								<a-menu-item @click="handleFirstTrial" key="1">
									初审
								</a-menu-item>
								<a-menu-item @click="handleReview" key="2">
									复审
								</a-menu-item>
							</a-menu>
							<a-button v-has="'tradeBooks:examine'" icon="bulb" type="primary">
								初复审
								<a-icon type="down"/>
							</a-button>
						</a-dropdown>
						<a-button type="primary" icon="sound" v-has="'tradeBooks:push'" :loading='pushLoading' @click="handlePush">推送</a-button>
						<a-button v-has="'tradeBooks:push'" size="small" @click="showOriginalEdi" type="primary" icon="cloud-sync"
						>回执系统处理信息
						</a-button>
						<a-button v-has="'tradeBooks:balance'" size="small" @click="showBalanceModal" type="primary" icon="hourglass"
						>核销平衡
						</a-button>
							<a-button @click="handleSynEmsBook" :loading = 'synLoading' type="primary" icon="redo" v-has="'ems::syn'">更新账册</a-button>
						<a-button
							@click="batchDel"
							v-if="selectedRowKeys.length > 0"
							ghost
							type="primary"
							icon="delete">批量删除
						</a-button>
					</a-col>
					<a-col :span="11" style="height: 32px">
						<span>颜色标识：</span>
						<span style="background-color: darkgrey;display: inline-block;width: 30px;
					text-align: center;color: white">灰</span>
						<span>：过期</span>
						<span style="background-color: red;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">红</span>
						<span>：30天以内</span>
						<span style="background-color: gold;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">金</span>
						<span>：30-90</span>
						<span style="background-color: orange;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">橙</span>
						<span>：90-180</span>
						<span style="background-color: green;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">绿</span>
						<span>：180+</span>
					</a-col>
				</a-row>
			</div>

			<!-- table区域-begin -->
			<div>
				<a-table
					ref="table"
					size="small"
					:scroll="{ x: true }"
					bordered
					rowKey="id"
					:columns="hasExamine?columnsExamine:columns"
					:dataSource="dataSource"
					:pagination="ipagination"
					:loading="loading"
					class="j-table-force-nowrap"
					:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio'}"
					:rowClassName="getRowClassname"
					@change="handleTableChange"
					:customRow="rowEvent"
				>
					<template slot="emsNoSlot" slot-scope="text, record">
						<a @click="handleEdit(record)">{{ text }}</a>
					</template>

					<span slot="action" slot-scope="text, record">
          <a-dropdown>
            <a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
				</a-table>
			</div>
			<!-- 新增编辑详情账册 -->
			<add-trade-books-edit-modal ref='modalForm' @ok="modalFormOk" @close="forceRerender"/>
		</a-card>
		<!-- 料件、成品、损耗表体列表 -->
		<add-trade-books-body-list ref='addTradeBooksBodyListRef' :hasExamine="hasExamine" @forceRerender="forceRerender"/>
		<!--		账册导入-->
		<import-modal ref='importModal' :downLoadUrl='url.downLoadTempUrl' :importUrl='url.importExcelUrl'
									downLoadButtonText='下载账册导入模板' emsType="1" type="3"
									title='账册导入'
									@closeUploadModal='closeUploadModal' @loadData="loadData(1)"
		></import-modal>
		<!--        引入其他组件部分-->
		<original-edi ref="originalEdi" :visible.sync="recycleOriginalBinVisible" @ok="modalFormOk" />
<!--		核销平衡模态框-->
		<WriteOffBalance ref="writeOffBalance" :visible.sync="writeOffBalanceVisible"/>
		<!--初复审modal-->
		<j-modal
			:title="title"
			:width="600"
			:visible="visible"
			@ok="handleOk"
			@cancel="visible=false"
			cancelText="关闭"
		>
			<template slot="footer">
				<a-button type="default" @click="visible=false">取消</a-button>
				<a-button type="primary" @click="handleOk" :loading="confirmLoading">确认</a-button>
			</template>
			<a-spin :spinning="confirmLoading">
				<a-form-model ref="form" :model="model" :rules="validatorRules">
					<a-row>
						<a-card size="small" :bordered="false" title="">
							<a-col v-if="isFirst">
								<a-form-model-item
									label="初审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="firstOpinion"
								>
									<j-remarks-component
										v-model="model.firstOpinion"
										placeholder="请输入初审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
							<a-col v-if="!isFirst">
								<a-form-model-item
									label="复审意见"
									:labelCol="labelCol1"
									:wrapperCol="wrapperCol1"
									prop="reviewOpinion"
								>
									<j-remarks-component
										v-model="model.reviewOpinion"
										placeholder="请输入复审意见"
										:max-length="500"
									></j-remarks-component>
								</a-form-model-item>
							</a-col>
						</a-card>
					</a-row>
				</a-form-model>
			</a-spin>
		</j-modal>
	</div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import { deleteAction, _postAction, getAction,postAction } from '@/api/manage'
import AddTradeBooksBodyList from '@/views/AddTradeBooks/modules/AddTradeBooksBodyList.vue'
import AddTradeBooksEditModal from '@/views/AddTradeBooks/modules/AddTradeBooksEditModal.vue'
import { USER_AUTH } from "@/store/mutation-types"
import ImportModal from '@/components/ImportModal/ImportModal.vue'
import OriginalEdi from "@/views/Business/customs-declaration/OriginalEdi.vue";
import WriteOffBalance from './modules/WriteOffBalance.vue'

export default {
	name: 'AddTradeBooksIndex',
	mixins: [JeecgListMixin, mixinDevice],
	components: {
		WriteOffBalance,
		OriginalEdi,
		ImportModal,
		AddTradeBooksEditModal,
		AddTradeBooksBodyList
	},
	data() {
		return {
			synLoading:false,
			writeOffBalanceVisible:false,
			hasExamine:'',
			model: {firstOpinion: '', reviewOpinion: ''},
			confirmLoading: false,
			visible: false,
			isFirst: false,
			pushLoading: false,
			//edi内部处理过程详情
			recycleOriginalBinVisible: false,
			title: '初审意见',
			validatorRules: {
				firstOpinion: [{ required: true, message: '请输入审核意见!' }],
				reviewOpinion: [{ required: true, message: '请输入复审意见!' }]
			},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			queryParam: {
				menuType: '2',
				// status: '1'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			/* 分页参数 */
			ipagination: {
				current: 1,
				pageSize: 5,
				pageSizeOptions: ['5', '10', '15'],
				showTotal: (total, range) => {
					return range[0] + '-' + range[1] + ' 共' + total + '条'
				},
				showQuickJumper: true,
				showSizeChanger: true,
				total: 0
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '账册编号',
					align: 'center',
					dataIndex: 'emsNo',
					scopedSlots: { customRender: 'emsNoSlot' }
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'tradeName'
				},
				{
					title: '类型',
					align: 'center',
					dataIndex: 'emsType',
					customRender: function(text) {
						if (text == '1') {
							return 'E账册'
						} else if (text == '2') {
							return 'H账册'
						}  else if (text == '3') {
							return '耗料'
						}  else if (text == '4') {
							return '工单'
						}  else if (text == '5') {
							return '企业为单元'
						}
					}
				},
				{
					title: '有效日期',
					align: 'center',
					dataIndex: 'endDate',
					customCell: (record, index) => {
						if(record.endDate){
							const days=this.calculateDays('',record.endDate)
							if(days>180){
								//绿色大于180
								return {
									style: {
										'background-color': 'green'
									},
								}
							}else if(days>90&&days<=180){
								//橙色大于90小于180
								return {
									style: {
										'background-color': 'orange'
									},
								}
							}else if(days>30&&days<=90){
								//金色大于30小于90
								return {
									style: {
										'background-color': 'gold'
									},
								}
							}else if(days<=30&&days>=0){
								//红色大于30
								return {
									style: {
										'background-color': 'red'
									},
								}
							}else {
								//过期
								return {
									style: {
										'background-color': 'darkgrey'
									},
								}
							}
						}
					},
				},
				{
					title: '状态',
					align: 'center',
					dataIndex: 'status',
					customRender: function(text) {
						if (text == 'B') {
							return '海关终审通过'
						} else if (text == '0') {
							return '暂存'
						} else if (text == 'C') {
							return '海关退单'
						} else if (text == 'L') {
							return '结案'
						} else {
							return text
						}
					}
				},
				{
					title: '登记日期',
					align: 'center',
					dataIndex: 'byDate'
				},
				{
					title: '登记人',
					align: 'center',
					dataIndex: 'byName'
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				},
			],
			columnsExamine: [
				{
					title: '账册编号',
					align: 'center',
					dataIndex: 'emsNo',
					scopedSlots: { customRender: 'emsNoSlot' }
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'tradeName'
				},
				{
					title: '类型',
					align: 'center',
					dataIndex: 'emsType',
					customRender: function(text) {
						if (text == '1') {
							return 'E账册'
						} else if (text == '2') {
							return 'H账册'
						}  else if (text == '3') {
							return '耗料'
						}  else if (text == '4') {
							return '工单'
						}  else if (text == '5') {
							return '企业为单元'
						}
					}
				},
				{
					title: '有效日期',
					align: 'center',
					dataIndex: 'endDate',
					customCell: (record, index) => {
						if(record.endDate){
							const days=this.calculateDays('',record.endDate)
							if(days>180){
								//绿色大于180
								return {
									style: {
										'background-color': 'green'
									},
								}
							}else if(days>90&&days<=180){
								//橙色大于90小于180
								return {
									style: {
										'background-color': 'orange'
									},
								}
							}else if(days>30&&days<=90){
								//金色大于30小于90
								return {
									style: {
										'background-color': 'gold'
									},
								}
							}else if(days<=30&&days>=0){
								//红色大于30
								return {
									style: {
										'background-color': 'red'
									},
								}
							}else {
								//过期
								return {
									style: {
										'background-color': 'darkgrey'
									},
								}
							}
						}
					},
				},
				{
					title: '状态',
					align: 'center',
					dataIndex: 'status',
					customRender: function(text) {
						if (text == 'B') {
							return '海关终审通过'
						} else if (text == '0') {
							return '暂存'
						} else if (text == 'C') {
							return '海关退单'
						} else if (text == 'L') {
							return '结案'
						} else {
							return text
						}
					}
				},
				{
					title: '登记日期',
					align: 'center',
					dataIndex: 'byDate'
				},
				{
					title: '登记人',
					align: 'center',
					dataIndex: 'byName'
				},
				{
					title: '初审状态',
					align: 'center',
					dataIndex: 'firstEStatus',
					customRender: function(text) {
						if (text == '1') {
							return '已初审'
						} else {
							return '未初审'
						}
					}
				},
				{
					title: '复审状态',
					align: 'center',
					dataIndex: 'reEStatus',
					customRender: function(text) {
						if (text == '1') {
							return '已复审'
						} else {
							return '未复审'
						}
					}
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				},
			],
			url: {
				list: '/business/ems/list',
				deleteBatch: '/business/ems/deleteBatch',
				downLoadTempUrl: '/template/加贸账册导入模板.xls',
				importExcelUrl: '/business/ems/importEms',
				push: '/business/ems/handlePush',
				handleSynEmsHandBook:'/business/ems/synEmsHead'
			},
		}
	},
	created() {
		//获取所有的权限
		let authList = JSON.parse(sessionStorage.getItem(USER_AUTH) || "[]")
		//是否存在审核权限，判断字段显示等内容
		for(let a of authList){
			if(a.action=='tradeBooks:examine' ){
				this.hasExamine=true
				break
			}else {
				this.hasExamine=false
			}
		}
		this.handleEmptyIcon()
	},
	methods: {
			//更新同步账册表头信息
		handleSynEmsBook(){
			if(!this.selectedRowKeys||this.selectedRowKeys.length==0){
				var that = this
			this.$confirm({
				title: '确认操作',
				content: '⚠️注意：当前未选中账册数据，是否全部更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook()
				}
			})



			}else{
				var that = this
this.$confirm({
				title: '确认操作',
				content: '是否对选中账册数据更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook(that.selectionRows[0].emsNo,that.selectionRows[0].seqNo)
				}
			})
			}

		},
				executeSynEmsHandBook(emsNo,seqNo){
			postAction(this.url.handleSynEmsHandBook, {
								type: 'ZC',
								emsNo:emsNo,
								seqNo:seqNo
							}).then((res) => {
									if (res.success) {
										this.$message.success('更新成功！')
										this.loadData()
									} else {
										this.$message.warning(res.message || res)
									}
								})
								.finally(() => {
									this.synLoading = false
								})
		},
		showBalanceModal(){
			// this.writeOffBalanceVisible=true
			this.$refs.writeOffBalance.dataSource=[]
			this.$refs.writeOffBalance.selectDate= ''
			this.$refs.writeOffBalance.visible=true
		},
		//显示系统内部全部edi回执处理信息
		showOriginalEdi() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (this.selectedRowKeys.length != 1) {
				this.$message.warning('请选择最多一条记录！')
			} else {
				let row = this.selectionRows[0]
				this.recycleOriginalBinVisible = true
				this.$refs.originalEdi.show(this.recycleOriginalBinVisible, row.seqNo, row.id, 'relId')
			}
		},
		/**
		 * 推送报文
		 */
		handlePush() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择至少一条记录！')
				return
			}
			let pushList = []
			for (let row of this.selectionRows){
				// if (row.appStatus && "0" != row.appStatus){
				// 	this.$message.warning('不允许暂存之后的商检单重新发送！')
				// 	return
				// }
				if (row.send == '1') {
					pushList.push(row.id)
				}
			}
			let msg = `确定要推送报文吗?`
			if (pushList.length > 0) {
				msg = `存在已推送数据，确定要重复推送吗?`
			}
			let that = this
			this.$confirm({
				title: '确认推送',
				content: msg,
				onOk: function() {
					that.pushLoading = true
					_postAction(that.url.push, {
						ids: that.selectedRowKeys.join(','),
						type:that.queryParam.menuType
					})
						.then((res) => {
							if (res.success){
								that.$message.success("推送成功")
								that.$tabs.refresh()
							}else {
								that.$message.warning(res.message)
							}
						}).finally(() => {
						that.pushLoading = false
					})
				}
			})
		},
		handleImport() {
			this.$refs.importModal.fileList = []
			this.$refs.importModal.visible = true
		},
		closeUploadModal() {
			this.$refs.importModal.visible = false
			this.$refs.importModal.fileList = []
			this.loadData(1)
			this.onClearSelected()
		},
		handleOk() {

			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					this.confirmLoading = true
					_postAction('/business/ems/handleInitialReview', {
						ids: this.selectedRowKeys.join(','),
						initialReviewStatus: this.isFirst ? '1' : '2',
						opinion: this.isFirst ? this.model.firstOpinion : this.model.reviewOpinion
					}).then(res => {
						if (res.success) {
							this.$message.success(res.message)
							this.searchQuery()
						} else {
							this.$message.warning(res.message)
						}
						this.confirmLoading = false
						this.visible = false
					})
				}
			})
		},

		//初审操作
		handleFirstTrial() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录进行初审！')
				return
			}
			this.model = {firstOpinion: '', reviewOpinion: ''}
			this.title = '初审意见'
			this.isFirst = true
			this.visible = true
		},
		//复审操作
		handleReview() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录进行复审！')
				return
			}
			this.model = {firstOpinion: '', reviewOpinion: ''}
			this.title = '复审意见'
			this.isFirst = false
			this.visible = true
		},
		calculateDays(startDate,endDate) {
			const start = new Date();
			const end = new Date(endDate);
			const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
			return days
		},
		handleEmptyIcon() {
			this.handleTableHeight('250px', '', '')
		},
		loadData(arg) {
			let isEmpty = false
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						//默认显示下方数据，取第一条表头，如果存在
						if(this.dataSource||this.dataSource.length>0){
							this.$refs.addTradeBooksBodyListRef.initHead(this.dataSource[0])
						}
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
						this.$refs.addTradeBooksBodyListRef.initHead(record)
					},
					dblclick: () => {
						this.handleEdit(record)
					},
					// ...
				}
			}
		},
		searchReset() {
			this.queryParam = {
				menuType: '2',
				// status: '1'
			}
			this.$refs.addTradeBooksBodyListRef.clear()
			this.searchQuery()
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids })
							.then(res => {
								if (res.success) {
									//重新计算分页问题
									that.reCalculatePage(that.selectedRowKeys.length)
									that.$message.success(res.message)
									that.loadData()
									that.onClearSelected()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		handleDelete: function (record) {
			var that = this;
			deleteAction(that.url.deleteBatch, {
				ids: record.id
			}).then((res) => {
				if (res.success) {
					//重新计算分页问题
					that.reCalculatePage(1)
					that.$message.success(res.message)
					that.loadData()
					that.onClearSelected()
				} else {
					that.$message.warning(res.message)
				}
			});
		},
		forceRerender() {
			this.loadData()
			// this.onClearSelected()
		},
		// 增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '2') {
				return 'data-rule-invalid'
			}
		},
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
</style>