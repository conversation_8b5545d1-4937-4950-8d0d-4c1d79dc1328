package org.jeecg.modules.business.config.message;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static org.jeecg.common.constant.CommonConstant.GENERAL;

/**
 * ftp配置类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/6/28 01:03
 */
@Data
@Component
@ConfigurationProperties(prefix = "ftp")
public class FtpProperties {
    // 企业连接配置
    private Map<String, FtpConnection> connections = new HashMap<>();

    // 通用路径配置
    private FtpPaths paths = new FtpPaths();

    // 根据key获取FTP连接配置
    public FtpConnection getConnection(String key) {
        return isBlank(key) ? connections.get(GENERAL) : connections.get(key.split("\\|")[0]);
    }

    // 获取通用路径配置
    public FtpPaths getPaths() {
        return paths;
    }

    // 获取所有企业代码
    public Set<String> getEnterpriseKeys() {
        return connections.keySet();
    }

    @Data
    public static class FtpConnection {
        private String url;
        private Integer port;
        private String username;
        private String password;
    }

    @Data
    public static class FtpPaths {
        private String remoteSendPath = "/ImpPath/DecCus001/OutBox/";
        private String remoteSendInvtPath = "/ImpPath/Nems/OutBox/";
        private String remoteSendSasPath = "/ImpPath/Sas/OutBox/";
        private String remoteSendElecPath = "/ImpPath/Acd/OutBox/";
        private String remoteCiqSendPath = "/ImpPath/Decciq001/OutBox/";
        private String remoteSendNptsPath = "/ImpPath/Npts/OutBox/";
    }
}
