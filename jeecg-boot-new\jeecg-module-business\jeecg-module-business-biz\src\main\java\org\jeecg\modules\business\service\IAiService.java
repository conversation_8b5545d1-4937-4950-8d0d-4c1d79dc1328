package org.jeecg.modules.business.service;

import org.jeecg.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * AI相关 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface IAiService {

    /**
     * AI制单
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @param priceReference
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/14 15:58
     */
    Result<?> aiMaker(MultipartFile[] files, String ieFlag, String customerName, String declarePlace,
                      String outPortCode, String shipTypeCode, String modelProvider, String priceReference, String decId);

    /**
     * 智能填写
     *
     * @param hscode
     * @param text
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/22 12:24
     */
    Result<?> smartFill(String hscode, String text);

    /**
     * AI制单 -- 对外
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/6/9 15:58
     */
    Result<?> aiMakerOut(MultipartFile[] files, String ieFlag, String customerName, String declarePlace,
                         String outPortCode, String shipTypeCode, String modelProvider, String clientId, String decId) throws IOException;

    /**
     * AI制单导出发票
     *
     * @param id
     * @param request
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2025/6/20 11:50
     */
    void exportInvoice(String id, HttpServletRequest request, HttpServletResponse response);
}
