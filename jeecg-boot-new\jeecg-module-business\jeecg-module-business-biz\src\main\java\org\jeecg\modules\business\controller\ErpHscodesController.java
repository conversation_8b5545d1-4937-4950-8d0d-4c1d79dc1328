package org.jeecg.modules.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.business.entity.ErpHscodeData;
import org.jeecg.modules.business.entity.ErpHscodes;
import org.jeecg.modules.business.entity.ErpHscodesChangeRecord;
import org.jeecg.modules.business.mapper.ErpHscodeDataMapper;
import org.jeecg.modules.business.service.IErpHscodesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 海关编码数据库
 * @Author: jeecg-boot
 * @Date: 2022-03-01
 * @Version: V1.0
 */
@Api(tags = "海关编码数据库")
@RestController
@RequestMapping("/dictionary/erpHscodes")
@Slf4j
public class ErpHscodesController extends JeecgController<ErpHscodes, IErpHscodesService> {
    @Autowired
    private IErpHscodesService erpHscodesService;
    @Autowired
    private ErpHscodeDataMapper erpHscodeDataMapper;

    /**
     * 分页列表查询
     *
     * @param erpHscodes
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "海关编码数据库-分页列表查询")
    @ApiOperation(value = "海关编码数据库-分页列表查询", notes = "海关编码数据库-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ErpHscodes erpHscodes,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ErpHscodes> queryWrapper = QueryGenerator.initQueryWrapper(erpHscodes, req.getParameterMap());
        Page<ErpHscodes> page = new Page<ErpHscodes>(pageNo, pageSize);
        IPage<ErpHscodes> pageList = erpHscodesService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param erpHscodes
     * @return
     */
    @AutoLog(value = "海关编码数据库-添加")
    @ApiOperation(value = "海关编码数据库-添加", notes = "海关编码数据库-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ErpHscodes erpHscodes) {
        erpHscodesService.save(erpHscodes);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param erpHscodes
     * @return
     */
    @AutoLog(value = "海关编码数据库-编辑")
    @ApiOperation(value = "海关编码数据库-编辑", notes = "海关编码数据库-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody ErpHscodes erpHscodes) {
        erpHscodesService.updateById(erpHscodes);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "海关编码数据库-通过id删除")
    @ApiOperation(value = "海关编码数据库-通过id删除", notes = "海关编码数据库-通过id删除")
    @RequestMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        erpHscodesService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "海关编码数据库-批量删除")
    @ApiOperation(value = "海关编码数据库-批量删除", notes = "海关编码数据库-批量删除")
    @RequestMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.erpHscodesService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "海关编码数据库-通过id查询")
    @ApiOperation(value = "海关编码数据库-通过id查询", notes = "海关编码数据库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ErpHscodes erpHscodes = erpHscodesService.getById(id);
        if (erpHscodes == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(erpHscodes);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param erpHscodes
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ErpHscodes erpHscodes) {
        return super.exportXls(request, erpHscodes, ErpHscodes.class, "海关商品编码数据库");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        Result<?> result = new Result<>();
        try {
            //清空数据库
            erpHscodesService.cleanData();
            //查询数据库是否已经清空
            Boolean isEmpty = erpHscodesService.selectData();
            if (isEmpty == false) {
                throw new JeecgBootException("匹配服务项目保存失败！！！");
            }
            //导入数据
            result = super.importExcel(request, response, ErpHscodes.class);
            if (result.getCode() != CommonConstant.SC_OK_200) {
                throw new JeecgBootException("匹配服务项目保存失败！！！");
            }
        } catch (Exception exp) {
            if (exp != null) {
                throw new JeecgBootException("匹配服务项目保存失败！！！");
            }
        }
        return result;
    }

    /**
     * 根据税号查询
     * @param codeTs
     * @return
     */
    @GetMapping(value = "/listTariffByHscode")
    public Result<?> listTariffByHscode(@RequestParam(name = "codeTs", required = true) String codeTs) {
        List<ErpHscodes> erpHscodes = erpHscodesService.list(new QueryWrapper<ErpHscodes>().lambda()
                .like(ErpHscodes::getHscode,codeTs));
        if (erpHscodes == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(erpHscodes);
    }

    /**
     * 根据税号查询
     * @param codeTs
     * @return
     */
    @GetMapping(value = "/listTariffByHscodeNew")
    public Result<?> listTariffByHscodeNew(@RequestParam(name = "codeTs", required = true) String codeTs) {
        List<ErpHscodeData> erpHscodes = erpHscodeDataMapper.selectList(new QueryWrapper<ErpHscodeData>().lambda()
                .like(ErpHscodeData::getHscode,codeTs));
        if (erpHscodes == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(erpHscodes);
    }

    /**
     * 货物编码和税则更新查询
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/21 下午1:38
     */
    @GetMapping(value = "/obtainingCodesAndTariffsFromThirdParty")
    public Result<?> obtainingCodesAndTariffsFromThirdParty(String date) {
        return erpHscodesService.obtainingCodesAndTariffsFromThirdParty(date);
    }
    /**
     * 货物编码和税则的补充信息
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/9 下午1:38
     */
    @GetMapping(value = "/hscodeTaxSupplement")
    public Result<?> hscodeTaxSupplement() {
        return erpHscodesService.hscodeTaxSupplement();
    }

    /**
     * 根据税号获取更多税率
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/27 下午1:38
     */
    @GetMapping(value = "/moreTaxByHscode")
    public Result<?> moreTaxByHscode(String hscode){
        return erpHscodesService.moreTaxByHscode(hscode);
    }

    /**
     * 获取变更记录
     * @apiNote
     * <pre>
     *   获取变更记录
     * </pre>
     *
     * @param []
     * @return org.jeecg.common.api.vo.Result<?>
     *
     * <AUTHOR>  2024/10/8 13:16
     * @version 1.0
     */
    @GetMapping(value = "/erpHscodesChangeRecordList")
    public Result<?> erpHscodesChangeRecordList(ErpHscodesChangeRecord erpHscodesChangeRecord,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize){
        return erpHscodesService.erpHscodesChangeRecordList(erpHscodesChangeRecord, pageNo, pageSize);
    }


}
