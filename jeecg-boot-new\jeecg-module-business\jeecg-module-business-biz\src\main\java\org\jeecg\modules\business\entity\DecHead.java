package org.jeecg.modules.business.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Data
@Accessors(chain = true)
@TableName("dec_head")
@EqualsAndHashCode(callSuper = false)
public class DecHead implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 委托流水号
     */
    private String applyNumber;

    /**
     * 报关单分类 （0：一般报关单 1：转关提前报关单 2：备案清单 3：转关提前备案清单 4：出口二次转关）
     */
    private String dclTrnRelFlag;

    /**
     * 申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，11查验通知，S公自用物品核准通过，T公自用物品退单，U公自用物品待核准）
     */
    private String decStatus;

    /**
     * 分票序号
     */
    private String partId;

    /**
     * 电子委托号
     */
    private String elecDelNo;

    /**
     * 客户端编号 原CUSTOMS_CODE 流水号+前缀(CLIENT_SEQ_NO)
     */
    private String customsCode;

    /**
     * 统一编号
     */
    private String seqNo;

    /**
     * 报关单号
     */
    private String clearanceNo;

    /**
     * 备案号
     */
    private String recordNumber;

    /**
     * 进出口标识
     */
    private String ieFlag;

    /**
     * 进出日期
     */
    private String outDate;

    /**
     * 申报日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date appDate;

    /**
     * 运输方式 海运2航空5
     */
    @Dict(dictTable = "erp_transport_types", dicText = "name", dicCode = "code")
    private String shipTypeCode;

    /**
     * 运输工具名称
     */
    private String shipName;

    /**
     * 航次
     */
    private String voyage;

    /**
     * 合同协议号
     */
    private String contract;

    /**
     * 提运单号
     */
    private String billCode;

    /**
     * 申报地海关
     */
    @Dict(dictTable = "erp_customs_ports", dicText = "name", dicCode = "customs_port_code")
    private String declarePlace;

    /**
     * 进出境关别
     */
    @Dict(dictTable = "erp_customs_ports", dicText = "name", dicCode = "customs_port_code")
    private String outPortCode;

    /**
     * 入境口岸/离境口岸
     */
    @Dict(dictTable = "erp_cityports", dicText = "cnname", dicCode = "cityport_code")
    private String entyPortCode;

    /**
     * 境外收货人代码
     */
    private String overseasConsigneeCode;

    /**
     * 境外收货人名称（外文）
     */
    private String overseasConsigneeEname;

    /**
     * 境外发货人代码
     */
    private String overseasConsignorCode;

    /**
     * 境外发货人名称（外文）
     */
    private String overseasConsignorEname;

    /**
     * 境内收发货人社会统一信用代码
     */
    private String optUnitSocialCode;

    /**
     * 境内收发货人海关代码
     */
    private String optUnitId;

    /**
     * 境内收发货人检验检疫编码
     */
    private String tradeCiqCode;

    /**
     * 境内收发货人名称
     */
    private String optUnitName;

    /**
     * 消费使用单位社会统一信用代码
     */
    private String deliverUnitSocialCode;

    /**
     * 消费使用单位海关代码
     */
    private String deliverUnit;

    /**
     * 消费使用单位检验检疫编码
     */
    private String ownerCiqCode;

    /**
     * 消费使用单位名称
     */
    private String deliverUnitName;

    /**
     * 申报单位社会统一信用代码
     */
    private String declareUnitSocialCode;

    /**
     * 申报单位海关代码
     */
    private String declareUnit;

    /**
     * 申报单位检验检疫编码
     */
    private String declCiqCode;

    /**
     * 申报单位名称
     */
    private String declareUnitName;

    /**
     * 贸易国
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    private String tradeCountry;

    /**
     * 启运国
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    private String arrivalArea;

    /**
     * 启运港代码
     */
    @Dict(dicCode = "GKDM")
    private String despPortCode;

    /**
     * 经停港/指运港
     */
    @Dict(dicCode = "GKDM")
    private String desPort;

    /**
     * 成交方式
     */
    @Dict(dicCode = "trading_type")
    private String termsTypeCode;

    /**
     * 监管方式
     */
    @Dict(dicCode = "JGFS")
    private String tradeTypeCode;

    /**
     * 征免性质
     */
    @Dict(dicCode = "ZMXZ")
    private String taxTypeCode;

    /**
     * 运费代码
     */
    @Dict(dicCode = "freight_amount_type")
    private String shipFeeCode;

    /**
     * 运费值
     */
    private BigDecimal shipFee;

    /**
     * 运费币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    private String shipCurrencyCode;

    /**
     * 保费代码
     */
    @Dict(dicCode = "premium_amount_type")
    private String insuranceCode;

    /**
     * 保费值
     */
    private BigDecimal insurance;

    /**
     * 保费币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "name", dicCode = "currency")
    private String insuranceCurr;

    /**
     * 杂费代码
     */
    @Dict(dicCode = "freight_amount_type")
    private String extrasCode;

    /**
     * 杂费值
     */
    private BigDecimal extras;

    /**
     * 杂费币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    private String otherCurr;

    /**
     * 件数
     */
    private Integer packs;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 许可证号
     */
    private String licenceNumber;

    /**
     * 包装种类
     */
    @Dict(dictTable = "erp_packages_types", dicText = "name", dicCode = "code")
    private String packsKinds;

    /**
     * 其他包装
     */
    private String packType;

    /**
     * 集装箱数
     */
    private String containerNum;

    /**
     * 报关单类型
     */
    private String clearanceType;
    /**
     * 清单类型，备案清单用（1：一般备案清单 2：先进区、后报关 3：集报备案清单 4：两单一审备案清单）
     */
    private String listType;

    /**
     * 货物存放地点
     */
    private String goodsPlace;

    /**
     * 随附单证
     */
    private String contractAtt;

    /**
     * 备注
     */
    private String markNumber;

    /**
     * 标记唛码
     */
    private String markNo;

    /**
     * 担保验放标识 1:是；0否
     */
    private String suretyFlag;

    /**
     * 是否担保验放 ？？？？？
     */
    private Boolean chkSurety;

    /**
     * 特殊关系
     */
    private String promiseItmes;

    /**
     * 备案清单类型
     */
    private String billType;

    /**
     * 关联报关单号
     */
    private String relId;

    /**
     * 关联备案号
     */
    private String relManNo;

    /**
     * 保税监管场地
     */
    private String bonNo;

    /**
     * 场地代码
     */
    private String cusFie;

    /**
     * 体积 ？？？？？
     */
    private BigDecimal volume;

    /**
     * 总金额
     */
    private BigDecimal total;
    /**
     * 币制
     */
    private String currency;

    /**
     * 总数量 ？？？？？
     */
    private BigDecimal goodsCount;

    /**
     * 检验检疫受理机关 商检信息
     */
    private String orgCode;

    /**
     * 口岸检验检疫机关 商检信息
     */
    private String inspOrgCode;

    /**
     * 目的地检验检疫机关 商检信息
     */
    private String purpOrgCode;

    /**
     * 领证机关 商检信息
     */
    private String vsaOrgCode;

    /**
     * B/LNO 商检信息
     */
    private String blNo;

    /**
     * 特殊业务标识 商检信息
     */
    private String specDeclFlag;

    /**
     * 启运日期 格式为：yyyyMMdd 商检信息
     */
    private String despDate;

    /**
     * 卸毕日期 格式为：yyyyMMdd ？？？？？？
     */
    private String cmplDschrgDt;

    /**
     * 关联号码 商检信息
     */
    private String correlationNo;

    /**
     * 关联理由 商检信息
     */
    private String correlationReasonFlag;

    /**
     * 原集装箱标识 商检信息
     */
    private String origBoxFlag;

    /**
     * 企业资质信息 商检信息[{"EntQualifNo":"123","EntQualifTypeCode":"456"}]
     */
    private String copLimitType;

    /**
     * 使用人信息表 商检信息[{"UseOrgPersonCode":"123","UseOrgPersonTel":"456"}]
     */
    private String decUserType;

    /**
     * 检验检疫签证申报要素 商检信息[{"AppCertCode":"123","ApplOri","456","ApplCopyQuan":"789"}]
     */
    private String requestCertType;

    /**
     * 无其他包装 ？？？？？？
     */
    private String noOtherPack;

    /**
     * 报关员
     */
    private String declarant;

    /**
     * 报关人员证号
     */
    private String declarantNo;

    /**
     * 报关单状态 保存；发送
     */
    private Integer status;

    /**
     * 转关类型 报文用
     */
    private String tranferType;

    /**
     * 通关模式 报文用
     */
    private String clearanceMode;

    /**
     * 申报单类型 报文用属地报关SD；备案清单：ML。LY：两单一审备案清单。CL:汇总征税报关单。SS:”属地申报，属地验放”
     */
    private String decType;

    /**
     * 报关标志 1：普通报关 3：北方转关提前 5：南方转关提前 6：普通报关，运输工具名称以‘◎’开头，南方H2000直转 报文用
     */
    private String ediId;

    private Boolean audited;
    /**
     * 录入人员
     */
    private String createPerson;

    /**
     * 录入日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 录入单位
     */
    private String createUnit;

    /**
     * 是否发送
     */
    private String send;
    private String flyId;
    /**
     * 创建人租户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tenantId;

    /** 租户名称 查询用*/
    @TableField(exist = false)
    private String tenantName;
    /** 数据下行标志 true为下行 */
    @TableField(exist = false)
    private Boolean downLinkMark;
    /** 是否同步 系统用 */
//    @Excel(name = "海关数据", replace = {"已同步_true", "未同步_false"})
    private Boolean synchronism;
    /** 同步日期 ？？？？？ */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date synchronismDate;
    /**
     * 申报人租户
     */
    private String dclTenantId;

    /**
     * IC卡号
     */
    private String icNumber;


    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;


    /**
     * 检验检疫(0：非检疫 1：检疫;)
     */
    private String inspMonitorCond;


    /**
     * 录入企业ID 创建人的企业ID
     */
    private String inputId;

    private String invId;

    /**
     * 录入人
     */
    private String inputErName;


    /**
     * 货物类别
     */
    private String goodsType;

    /*
     * 推送状态（0：未推送,1：已推送）
     */
    private String pushStatus;
    /*
     * 推送状态（0：未推送,1：已推送） 查询用
     */
    @TableField(exist = false)
    private String pushStatusFlag;

    /** 企业内部清单编号 （由企业自行编写） 与核注单关联 */
    private String etpsInnerInvtNo;

    /**
     * 放行日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date releaseDate;
    /**
     * 初复审状态（0未审核、1已初审/未复审、2已复核）
     */
    private String initialReviewStatus;
    /**
     * 初审人
     */
    private String firstTrialBy;
    /**
     * 初审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstTrialDate;
    /**
     * 初审意见
     */
    private String firstOpinion;
    /**
     * 复审人
     */
    private String reviewBy;
    /**
     * 复审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewDate;
    /**
     * 复审意见
     */
    private String reviewOpinion;
    /**
     * 结关日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date finalDate;

    /**订单协议号*/
    @ApiModelProperty(value = "订单协议号")
    private String orderProtocolNo;

    /**是否易通关同步*/
    @ApiModelProperty(value = "是否易通关同步")
    private String isDocking;
    /**市场采购推送状态（0，未推送，1已推送）*/
    @ApiModelProperty(value = "市场采购推送状态（0，未推送，1已推送）")
    private String pushMarketProcurementStatus;
    /** 委托创建人 */
    @Excel(name = "委托人")
    @TableField("APPLY_CREATE_NAME")
    private String applyCreateName;
    /**
     * 计费重量
     */
    @TableField("CHARGED_WEIGHT")
    private BigDecimal chargedWeight;

    /**
     * 是否通过AI制单创建
     */
    @TableField("IS_AI")
    private Boolean isAi;

    /**
     * 是否为外部创建的AI制单
     */
    @TableField("IS_AI_OUT")
    private Boolean isAiOut;

    /**
     * 报关单表体
     */
    @TableField(exist = false)
    private List<DecList> decLists;

    /**
     * 报关单随附单证
     */
    @TableField(exist = false)
    private List<DecAttachment> decAttachments;

    /**
     * 集装箱
     */
    @TableField(exist = false)
    private List<DecContainer> decContainers;

    /**
     * 随附单据
     */
    @TableField(exist = false)
    private List<DecLicenseDocus> decLicenseDocuses;
    /**
     * 处理字典转换
     */
    @TableField(exist = false)
    private Map<String, Map<String,String>> dictMap;
    /**
     * 关联商铺id（市场采购报关单用到）
     */
    @TableField(exist = false)
    private String shopsId;
    /**
     * 关联采购商id（市场采购报关单用到）
     */
    @TableField(exist = false)
    private String purchaserId;
    /**
     * 关联商铺名称（市场采购报关单用到）
     */
    @TableField(exist = false)
    private String shopName;
    /**
     * 关联采购商名称（市场采购报关单用到）
     */
    @TableField(exist = false)
    private String purchaserName;

    /**
     * 自定义列(导出用)
     */
    @TableField(exist = false)
    private String[] columnList;
    /**
     * 是否两步申报 1 是 2否
     */
    @TableField(exist = false)
    private String twoStep;

    /**
     * 是否有舱单 1 是 2否
     */
    private String hasCd;
    /**
     * 是否有运抵 1 是 2否
     */
    private String hasYd;
    /** 概要完整申报状态(1：概要申报2：完整申报;)*/
    private String profileType;
    /**
     * 预计到港时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date eta;
    /**
     * 预计到港时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date loadReleaseTime;
    /**
     * 价格基准（1单价 2总价）
     */
    private String priceReference;
    /**
     * 首项商品名称
     */
    @TableField(exist = false)
    private String firstGoodsName;
    /**
     * 商品名称汇总
     */
    @TableField(exist = false)
    private String allGoodsName;
    /**
     * 集装箱号汇总
     */
    @TableField(exist = false)
    private String allContainerId;
    /**
     * 集装箱数
     */
    @TableField(exist = false)
    private int containerIdCount;
    /**
     * 商品项数
     */
    @TableField(exist = false)
    private int goodsItemCount;
    @TableField(exist = false)
    private String vid;

    @TableField(exist = false)
    private String cusRemark;
    @TableField(exist = false)
    private String startAppDate;
    @TableField(exist = false)
    private String lastAppDate;
    @TableField(exist = false)
    private String startCreateTime;
    @TableField(exist = false)
    private String lastCreateTime;
    @TableField(exist = false)
    private String startUpdateTime;
    @TableField(exist = false)
    private String lastUpdateTime;
    @TableField(exist = false)
    private String fields;
    @TableField(exist = false)
    private String sortType;
    @TableField(exist = false)
    private String hsname;
    @TableField(exist = false)
    private String ediInfo;
    /**
     * 报关单号多个，查询用
     */
    @TableField(exist = false)
    String clearanceNos;
}
