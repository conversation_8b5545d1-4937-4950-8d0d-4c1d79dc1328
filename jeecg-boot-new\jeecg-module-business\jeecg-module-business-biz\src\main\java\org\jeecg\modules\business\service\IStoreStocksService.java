package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.business.entity.dto.InOrOutStorageDetailDTO;
import org.jeecg.modules.business.entity.dto.StoreStocksDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 仓库库存建账表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public interface IStoreStocksService extends IService<StoreStocks> {

     /**
     * 保存或更新库存和流量信息
     *
     * @param storageDetail 存储详情
     * @return 保存或更新的结果
     */
    String saveOrUpdateStocksAndFlow(StorageDetail storageDetail);

    /**
     * 根据条件获取指定商品信息的库存
     *
     * @param batchNo 批次号
     * @param copGno 物料号
     * @param storeCode      商店代码
     * @return 获取库存的结果
     */
    Result<?> getStoreStocksByCond(String customer, String batchNo, String copGno, String storeCode);

    /**
     * 根据条件获取指定商品信息的库存
     *
     * @param storeCode      商店代码
     * @return 获取库存的结果
     */
    Result<?> getStoreStocksByCond_(String customer, String batchNos, String copGnos, String storeCode);

    /**
     * 根据分页信息和查询条件查询店铺库存列表
     *
     * @param storeStocksDTO 店铺库存条件
     * @return 分页后的店铺库存列表
     */
    Result<?> queryPageList(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize);

    /**
     * 获取商品流转页面
     *
     * @param page 分页对象
     * @param storeStocksDTO 店铺名称
     * @return 商品流转页面对象
     */
    IPage<StoreStocksFlow> listGoodsFlow(Page<StoreStocksFlow> page, StoreStocksDTO storeStocksDTO);

    /**
     * 根据出入库单表体获取对应的仓库库存信息
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    Result<StoreStocks> getStockByDetail(StorageDetail storageDetail);

    /**
     * 根据维修单表体获取对应的仓库库存信息
     *
     * @param repairOrderDetail
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StoreStocks>
     * <AUTHOR>
     * @date 2024/8/9 下午3:35
     */
    Result<StoreStocks> getStockByRepairDetail(StorageRepairOrderDetail repairOrderDetail);

    /**
     * 根据调拨单表体获取对应的仓库库存信息
     *
     * @param transferDetail
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StoreStocks>
     * <AUTHOR>
     * @date 2024/8/26 11:31
     */
    Result<StoreStocks> getStockByTransferDetail(StorageTransferDetail transferDetail);

    /**
     * 根据出入库单表体获取对应的仓库库存信息
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    Result<StoreStocksFlow> getStockFlowByDetail(StorageDetail storageDetail);

    /**
     * 根据出入库单表体获取对应的仓库库存信息  - 一对多
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 16:45
     */
    Result<List<StoreStocksFlow>> getStockFlowByDetailOneToMany(StorageDetail storageDetail);

    /**
     * 库存记账/核增库存处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    Result<?> addStockHandle(StorageDetail storageDetail);

    /**
     * 增加占用处理器
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 15:05
     */
    Result<?> addOccupyHandle(StorageDetail storageDetail);

    /**
     * 增加占用处理器 - 一对多
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/25 15:05
     */
    Result<?> addOccupyHandleOneToMany(StorageDetail storageDetail);

    /**
     * 解除占用处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    Result<?> deOccupyHandle(StorageDetail storageDetail);

    /**
     * 解除占用处理器 - 一对多
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    Result<?> deOccupyHandleOneToMany(StorageDetail storageDetail);

    /**
     * 核减库存/占用处理器
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    Result<?> reduceStockHandle(StorageDetail storageDetail);

    /**
     * 核减库存/占用处理器 - 一对多
     *
     * @param storageDetail
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/25 14:37
     */
    Result<?> reduceStockHandleOneToMany(StorageDetail storageDetail);

    /**
     * 冲正处理器
     *
     * @param storageDetail
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/10 10:46
     */
    Result<?> rectify(StorageDetail storageDetail);

    /**
     * 导出库存Excel
     *
     * @param request
     * @param response
     * @param storeStocksDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/9 15:59
     */
    void exportStocks(HttpServletRequest request, HttpServletResponse response, StoreStocksDTO storeStocksDTO);

    /**
     * 导出出入库明细Excel
     *
     * @param request
     * @param response
     * @param inOrOutStorageDetailDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/13 14:51
     */
    void exportStoreDetail(HttpServletRequest request, HttpServletResponse response, InOrOutStorageDetailDTO inOrOutStorageDetailDTO);

    /**
     * 查询出库单商品库存
     *
     * @param storeCode
     * @param customer
     * @param itemNumber
     * @param copGno
     * @param batchNo
     * @param spaceName
     * @return
     */
    Result<?> getStoreStocksBy4Cond(String storeCode, String customer, String itemNumber, String copGno, String batchNo, String spaceName);
    Result<?> getStoreStocksBy4ModelCond(StorageDetail storageDetail);

    /**
     * 保税预警 有效期预警数据
     *
     * @param storeStocksDTO 有效期预警数据
     * @return 有效期预警数据
     */
    Result<?> limitedTermWarningList(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize);

    Result<?> loadTotalSummary(StoreStocksDTO storeStocksDTO);

    Result<?> queryPageList_(StoreStocksDTO storeStocksDTO, Integer pageNo, Integer pageSize);
}
