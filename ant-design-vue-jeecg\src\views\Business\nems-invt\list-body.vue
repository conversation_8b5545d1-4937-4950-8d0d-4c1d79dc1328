<template>
    <div>
        <!--表单-->
        <a-form-model :model="record" ref="nemsInvtForm" :rules="rules" v-enterToNext>
            <fieldset :disabled="addType" class="nems-invt-fieldset">
                <table class="my-table">
                    <tr>
                        <input-item :readonly="true" label="序号" iprop="gdsseqNo" v-model="record.gdsseqNo"/>
                        <input-item
                            :readonly="addType"
                            label="备案序号"
                            iprop="putrecSeqno"
                            :required="this.BAXHRequired"
                            v-model="record.putrecSeqno"
                            @pressEnter="putrecSeqnoEnter(record.putrecSeqno)"
                        />
                        <input-item
                            :readonly="addType"
                            label="商品料号"
                            iprop="gdsMtno"
                            required
                            v-model="record.gdsMtno"
														@pressEnter="gdsMtnoEnter(record.gdsMtno)"
                        />
                    </tr>
                    <tr>
                        <input-item
                            :readonly="addType"
                            label="报关单商品序号"
                            iprop="entryGdsSeqno"
                            v-model="record.entryGdsSeqno"
                        />
                        <input-item
                            :readonly="addType"
                            label="流转申报表序号"
                            iprop="applyTbSeqnoA"
                            v-model="record.applyTbSeqnoA"
                        />
                        <input-item
                            :readonly="addType"
                            label="商品编码"
                            iprop="hscode"
                            required
                            v-model="record.hscode"
                            @pressEnter="hscodeEnter(record.hscode)"
                        />
                    </tr>
                    <tr>
                        <input-item
                            :readonly="addType"
                            label="商品名称"
                            iprop="hsname"
                            required
                            :focusIndex='focusIndex' :vfocusNum='focusIndex===11111'
                            @focusIndexDate="myfocusIndexMethod"
                            v-model="record.hsname"
                        />
                        <input-item
                            :readonly="addType"
                            label="规格型号"
                            iprop="hsmodel"
                            required
                            v-model="record.hsmodel"
                        />
                        <select-item
														dict-key="erp_currencies,name,code,currency,1=1"
                            :readonly="addType"
                            iprop="dclCurrcd"
                            label="币制"
                            required
                            v-model="record.dclCurrcd"
                        />
                    </tr>
                    <tr>
                        <select-item
														dict-key="erp_units,name,code,enname,del_flag=0"
                            :readonly="addType"
                            label="申报计量单位"
                            iprop="dclUnitcd"
                            @select='lawfUnitcdDlur'
                            required
                            v-model="record.dclUnitcd"
                        />
                        <select-item
														dict-key="erp_units,name,code,enname,del_flag=0"
                            label="法定计量单位"
                            @select='lawfUnitcdDlur'
                            v-model="record.lawfUnitcd"
                        />
                        <select-item
														dict-key="erp_units,name,code,enname,del_flag=0"
                            label="法定第二计量单位"
                            @select='lawfUnitcdDlur'
                            v-model="record.secdlawfUnitcd"
                        />
                    </tr>
                    <tr>
                        <input-num-item
                            :readonly="addType"
                            stype="number"
                            label="申报数量"
                            iprop="dclQty"
                            @change='returnBackFnCount'
                            keyFocusTo="enterlawfQty"
                            required
                            v-model="record.dclQty"
                        />
                        <input-item
                            :readonly="addType"
                            label="法定数量"
                            ref="enterlawfQty"
                            iprop="lawfQty"
                            :focusIndex='focusIndex' :vfocusNum='focusIndex==22222'
                            @focusIndexDate="myfocusIndexMethod"
                            required
                            @change='unit1Qk'
                            v-model="record.lawfQty"
                        />
                        <input-num-item
                            :readonly="addType"
                            label="第二法定数量"
                            iprop="secdLawfQty"
                            v-model="record.secdLawfQty"
                            @change='secdLawfQtyChange'
                        />
                    </tr>
                    <tr>
                        <input-num-item
                            :readonly="addType"
                            label="企业申报单价"
                            iprop="dclUprcamt"
                            @change="priceChange"
                            required
                            v-model="record.dclUprcamt"
                        />
                        <input-num-item
                            :readonly="addType"
                            label="企业申报总价"
                            iprop="dclTotalamt"
                            @change="totalChange"
                            required
                            v-model="record.dclTotalamt"
                        />
                        <select-item
														dict-key="erp_countries,name,code,isenabled=0"
                            :readonly="addType"
                            label="原产国(地区)"
                            v-model="record.originCountry"
                        />
                    </tr>
                    <tr>
                        <input-num-item
                            :readonly="addType"
                            label="重量比例因子"
                            iprop="wtsfVal"
                            v-model="record.wtsfVal"
                        />
                        <input-num-item
                            :readonly="addType"
                            label="第一比例因子"
                            iprop="grofstsfValssWt"
                            v-model="record.fstsfVal"
                        />
                        <input-num-item
                            :readonly="addType"
                            label="第二比例因子"
                            iprop="secdsfVal"
                            v-model="record.secdsfVal"
                        />
                    </tr>
                    <tr>
                        <input-num-item :readonly="addType" label="毛重(KG)" iprop="grossWt" v-model="record.grossWt"/>
                        <input-num-item :readonly="addType" label="净重(KG)" iprop="netWt" @change='returnBackFnCount'
                                        v-model="record.netWt"/>
                        <select-item
														dict-key="erp_countries,name,code,isenabled=0"
                            :readonly="addType"
                            label="最终目的国(地区)"
                            required
                            iprop="natcd"
                            v-model="record.natcd"
                        />
                    </tr>
                    <tr>
                        <select-item
                            :dict-key="dictKeys.ZJMSFS"
                            :readonly="addType"
                            label="征免方式"
                            required
                            iprop="lvyrlfModecd"
                            v-model="record.lvyrlfModecd"
                        />
                        <input-item
                            :readonly="addType"
                            label="单耗版本号"
                            iprop="ucnsVerno"
                            v-model="record.ucnsVerno"
                        />
                        <input-item :readonly="true" label="自动备案序号" value=""/>
                    </tr>
                    <tr>
                        <select-item :readonly="true" label="修改标志" v-model="record.modfMarkcd"
																		 :options="[{text:'未修改',value:'0'},{text:'修改',value: '1'},{text:'删除',value: '2'},{text:'增加',value: '3'}]"/>
                        <select-item :readonly="true" label="危化品标识"  v-model="record.clymarkcd"
                                     :options="[{text:'是',value:'1'},{text:'否',value: '0'}]"/>
											<input-item
												v-if="InvatHeaderData.impexpMarkcd == 'I' "
												:readonly="addType"
												label="入库单号"
												v-model="record.storageNo"
												iprop="storageNo"
											/>
											<input-item
												v-if="InvatHeaderData.impexpMarkcd == 'E' "
												:readonly="addType"
												label="原清单企业内部编号"
												v-model="record.importBillNo"
												iprop="rmk"
											/>
                    </tr>
                    <tr>
											<input-item
												v-if="InvatHeaderData.impexpMarkcd == 'E' "
												:readonly="addType"
												label="出库单号"
												v-model="record.storageNo"
												iprop="storageNo"
											/>
											<input-item
												v-if="InvatHeaderData.impexpMarkcd == 'E' "
												:readonly="addType"
												label="集报出入库单号"
												v-model="record.sasStockNo"
												iprop="sasStockNo"
											/>
											<select-item
												v-if="InvatHeaderData.impexpMarkcd == 'I' "
												:dict-key="dictKeys.LYBS"
												:readonly="addType"
												label="来源标识"
												iprop="param1"
												v-model="record.param1"
											/>
											<select-item
												v-if="InvatHeaderData.impexpMarkcd == 'E' "
												:dict-key="dictKeys.LYBS"
												:readonly="addType"
												label="来源标识"
												iprop="param1"
												v-model="record.param1"
											/>
											<input-item
												v-if="InvatHeaderData.impexpMarkcd == 'I' "
												:readonly="addType"
												label="备注"
												v-model="record.rmk"
												iprop="rmk"
												placeholder='保存表体请先敲回车键保存至列表'
												@pressEnter="enterXSave(record)"
											/>
                    </tr>
									<tr>
										<input-item
											v-if="InvatHeaderData.impexpMarkcd == 'E' "
											:readonly="addType"
											label="备注"
											v-model="record.rmk"
											iprop="rmk"
											placeholder='保存表体请先敲回车键保存至列表'
											@pressEnter="enterXSave(record)"
										/>
									</tr>
                </table>
            </fieldset>
        </a-form-model>

        <!--列表-->
        <br/>
        <!-- 操作按钮区域START -->
        <div>
            <div
								class="table-opera"
                style="float: left;"
                v-show="(!(showBut || !disableBu)) || newStatus"
            >
                <!--  <a-button @click="addBody" icon="plus" type="primary">新增</a-button> -->
                <a-button @click="addXBody" icon="plus" type="primary" size="small"
                >新增
                </a-button
                >
                危化品标识批量设置：
                <a-radio-group v-model="clymarkcds" @change="setClymarkcdBatch">
                    <a-radio value='1'>
                        是
                    </a-radio>
                    <a-radio value='0' class="aradioClass">
                        否
                    </a-radio>
                </a-radio-group>

                <a-button
                    v-show="this.selectedRows.length == 1"
                    @click="moveXUp"
                    icon="plus"
                    type="primary"
                    size="small"
                >上移
                </a-button
                >
                <a-button
                    v-show="this.selectedRows.length == 1"
                    @click="moveXDown"
                    icon="plus"
                    type="primary"
                    size="small"
                >下移
                </a-button
                >

                <a-button
                    v-show="this.selectedRows.length == 1"
                    @click="copyXData"
                    icon="plus"
                    type="primary"
                    size="small"
                >复制
                </a-button
                >
                <a-dropdown v-show="this.selectedRows.length > 0" size="small">
                    <a-menu slot="overlay">
                        <a-menu-item @click="batchXDelbody" key="1">
                            <a-icon type="delete"/>
                            删除
                        </a-menu-item>
                    </a-menu>
                    <a-button style="margin-left: 8px" size="small">
                        批量操作
                        <a-icon type="down"/>
                    </a-button>
                </a-dropdown>

            </div>
            <div class="detailsRightDiv">
                <div class="detailsRightDiv1">申报数量合计: <span v-html="dclQtyText"></span></div>
                <div class="detailsRightDiv1">法定数量合计: <span v-html="lawfQtyText"></span></div>
                <div class="detailsRightDiv1">法定第二数量合计: <span v-html="secdLawfQtyText"></span></div>

                <div class="detailsRightDiv1">净重合计: <span v-html="netWtText"></span></div>
                <div class="detailsRightDiv1">总价合计: <span v-html="decTotalamtText"></span></div>

            </div>
        </div>

        <br/>

        <vxe-grid
            border="full"
            resizable
            ref="xTable2"
            height="250"
            size="mini"
            class="sortable-row-demo"
            :show-overflow="true"
            :loading="loading"
            :data="tableData"
            :columns="tableColumn"
            :custom-config="{ storage: true }"
            id="nemsBodyTable"
            :transfer="true"
            :auto-resize="true"
            :highlight-current-row="true"
            @header-cell-click="headerCellClickEvent"
            @header-cell-dblclick="headerCellDBLClickEvent"
            @header-cell-context-menu="headerCellContextMenuEvent"
            @cell-click="cellClickEvent"
            @cell-dblclick="cellDBLClickEvent"
            @cell-selected="cellSelected"
            @checkbox-all="this.handleVxeCheckboxChange"
            @checkbox-change="this.handleVxeCheckboxChange"
            @checkbox-range-change="this.checkboxRangeChange"
            :checkbox-config="{ trigger: 'col', highlight: true, range: true }"
        >
            <vxe-table-column width="60">
                <template v-slot>
                    <span class="drag-btn">
                        <i class="vxe-icon--menu"></i>
                    </span>
                </template>
                <template v-slot:header>
                    <vxe-tooltip v-model="showHelpTip1" content="按住后可以上下拖动排序！" enterable>
                        <i class="vxe-icon--question" @click="showHelpTip1 = !showHelpTip1"></i>
                    </vxe-tooltip>
                </template>
            </vxe-table-column>
        </vxe-grid>

        <!--报关单-->
        <a-divider orientation="left">报关单</a-divider>
        <vxe-grid
            resizable
            border="full"
            ref="xTable3"
            height="250"
            size="mini"
            :show-overflow="true"
            :loading="loading"
            :data="dataSourceBottom"
            :columns="customsXDeclarationColumns"
            @cell-dblclick="fineTableEventBGD"
        >
        </vxe-grid>

        <!--税则信息-->
        <invt-teriff
            ref="invtTeriff"
            :show.sync="invtTeriffShow"
            :txtrequestCertType.sync="record"
            v-model="record.hscode"
            @hscodeEnterNext="hscodeEnterNext"
        ></invt-teriff>

        <!--总价单价数量联动modal-->
        <!--总价单价数量联动modal-->
<!--        <a-modal v-model="showUnitTotal" title="修改单价/总价?" width="20%">-->
<!--            <p>-->
<!--                <a-button class="totalButton" @click="unitButton(1)" style="margin-left: 20px">-->
<!--                    修改单价-->
<!--                </a-button>-->

<!--                <a-button class="totalButton1" @click="totalButton(1)" style="margin-left: 40px">-->
<!--                    修改总价-->
<!--                </a-button>-->
<!--            </p>-->

<!--            <template slot="footer">-->
<!--                <a-button @click="handleCancel">取消</a-button>-->
<!--            </template>-->
<!--        </a-modal>-->
    </div>
</template>

<script>
    import { JeecgListMixin } from '@/mixins/JeecgListMixin'
    import InputNumItem from '@views/declaration/component/m-table-input-num-item'
    import InputItem from '@views/declaration/component/m-table-input-item'
    import SelectItem from '@views/declaration/component/m-table-select-item'
    import DateItem from '@views/declaration/component/m-table-date-item'
		import { getAction, postAction, deleteAction, _postAction } from '@/api/manage'
    import { ajaxGetDictItems, duplicateCheckByGrouping } from '@/api/api'
    import { nextTick } from 'vuedraggable'
    import InvtTeriff from '@views/Business/nems-invt/invt-teriff'
		import Sortable from 'sortablejs'
		import lodash from 'lodash'

    export default {
        mixins: [JeecgListMixin],
        name: 'list-body',
        components: {
            SelectItem,
            InputItem,
            DateItem,
            InputNumItem,
						Sortable,
            InvtTeriff
        },

        data() {
            return {
							disableMixinCreated: true,
							fromStore: false,
                focusIndex: 0,
                //数量修改更改单价还是总价模态框
                // showUnitTotal: false,
                showHelpTip1: false,
                //商品编号回车模态框显示隐藏（税则）
                invtTeriffShow: false,

                inputValues: [],
                fulltableData: null,
                fulldate: [],
                loading: false,
                // 编辑数据状态
                EditDataStatus: false,

                selectedRows: [],

                dataSource: [],

                tableColumn: [
                    { type: 'checkbox', width: 50 },
                    { title: '序号', field: 'gdsseqNo', type: 'seq', width: 50 },

                    {
                        title: '备案序号',
                        width: '80px',
                        field: 'putrecSeqno'
                    },
                    {
                        title: '报关单商品序号',
                        width: '80px',
                        field: 'entryGdsSeqno'
                    },
                    {
                        title: '商品料号',
                        width: '120px',
                        field: 'gdsMtno'
                    },

                    {
                        title: '商品编码',
                        width: '110px',
                        field: 'hscode'
                    },
                    {
                        title: '商品名称',
                        width: '180px',
                        field: 'hsname'
                    },
                    {
                        title: '规格型号',
                        width: '600px',
                        field: 'hsmodel'
                    },
                    {
                        title: '申报数量',
                        width: '100px',
                        field: 'dclQty'
                    },

                    {
                        title: '申报计量单位',
                        width: '80px',
                        field: 'dclUnitcd',
                        formatter: this.formatterJLDW
                    },
                    {
                        title: '法定数量',
                        width: '100px',
                        field: 'lawfQty'
                    },
                    {
                        title: '法定计量单位',
                        width: '80px',
                        field: 'lawfUnitcd',
                        formatter: this.formatterJLDW
                    },
                    {
                        title: '第二法定数量',
                        width: '100px',
                        field: 'secdLawfQty'
                    },
                    {
                        title: '法定第二计量单位',
                        width: '80px',
                        field: 'secdlawfUnitcd',
                        formatter: this.formatterJLDW
                    },

                    {
                        title: '企业申报单价',
                        width: '100px',
                        field: 'dclUprcamt'
                    },
                    {
                        title: '企业申报总价',
                        width: '100px',
                        field: 'dclTotalamt'
                    },
                    {
                        title: '币制',
                        width: '80px',
                        field: 'dclCurrcd',
                        formatter: this.formatterBZ
                    },
                    {
                        title: '原产国(地区)',
                        width: '120px',
                        field: 'originCountry',
                        formatter: this.formatterGBDQ
                    },
                    {
                        title: '最终目的国',
                        width: '120px',
                        field: 'natcd',
                        formatter: this.formatterGBDQ
                    },

                    {
                        title: '重量比例因子',
                        width: '100px',
                        field: 'wtsfVal'
                    },
                    {
                        title: '第一比例因子',
                        width: '100px',
                        field: 'fstsfVal'
                    },
                    {
                        title: '第二比例因子',
                        width: '100px',
                        field: 'secdsfVal'
                    },
                    {
                        title: '毛重(KG)',
                        width: '100px',
                        field: 'grossWt'
                    },
                    {
                        title: '净重(KG)',
                        width: '100px',
                        field: 'netWt'
                    },
                    {
                        title: '征免方式',
                        width: '100px',
                        field: 'lvyrlfModecd',
                        formatter: this.formatterlvyrlfModecd
                    },
                    {
                        title: '单耗版本号',
                        width: '100px',
                        field: 'ucnsVerno'
                    },
                    {
                        title: '备注',
                        width: '160px',
                        field: 'rmk'
                    },
									{
										title: '出入库单号',
										width: '100px',
										field: 'storageNo'
									},
									{
										title: '集报出入库单号',
										width: '100px',
										field: 'sasStockNo'
									}
                ],
                customsXDeclarationColumns: [
                    {
                        title: '统一编号',
                        width: '180px',
                        field: 'seqNo'
                    },
                    {
                        title: '报关单商品序号',
                        width: '80px',
                        field: 'item'
                    },
                    {
                        title: '商品编码',
                        width: '100px',
                        field: 'hscode'
                    },
                    {
                        title: '商品名称',
                        width: '160px',
                        field: 'hsname'
                    },

                    {
                        title: '规格型号',
                        width: '420px',
                        field: 'hsmodel'
                    },

                    {
                        title: '单价',
                        width: '80px',
                        field: 'price'
                    },
                    {
                        title: '申报数量',
                        width: '80px',
                        field: 'goodsCount'
                    },
                    {
                        title: '法定单位',
                        width: '80px',
                        field: 'unit1',
                        formatter: this.formatterJLDW
                    },
                    {
                        title: '总价',
                        width: '100px',
                        field: 'total'
                    },
                    {
                        title: '币制',
                        width: '80px',
                        field: 'currencyCode',
                    },

                    {
                        title: '原产国',
                        width: '100px',
                        field: 'desCountry'
                    },
                    {
                        title: '最终目的国',
                        width: '100px',
                        field: 'destinationCountry'
                    }
                ],
                tableData: [],
                //赞未使用字段禁用
                nosUsestype: true,
                irequired: true,
                isCopyEdit: false,
                //是否添加状态（可编辑时为false）
                addType: true,
                //是否显示操作按钮
                showBut:
                    this.$route.query.fromModel == 'EntrustedManagement' || this.$route.query.is == 'EntrustedManagement'
                        ? true
                        : false,
                ipagination: {
                    current: 1,
                    pageSize: 1000000,
                    pageSizeOptions: ['1000000', '1000000', '1000000'],
                    showTotal: (total, range) => {
                        return range[0] + '-' + range[1] + ' 共' + total + '条'
                    },
                    showQuickJumper: true,
                    showSizeChanger: true,
                    total: 0
                },

                dictKeys: {
                    // 币制代码
                    BZDM: 'BZDM',
                    BZDM_NUM: 'BZDM_NUM',
                    // 计量单位
                    JLDW: 'JLDW',
                    // 征免方式
                    ZJMSFS: 'ZJMSFS',
                    // 国别地区
                    GBDQ: 'GBDQ',
                    GBDQ_NUM: 'GBDQ_NUM',
									LYBS:'LYBS'

                },
                // 当前页面id
                id: '',
                numType: Number,
                //企业内部编号
                etpsInnerInvtNo:
                    this.$route.query.etpsInnerInvtNo == null || this.$route.query.etpsInnerInvtNo == undefined
                        ? ''
                        : this.$route.query.etpsInnerInvtNo,
                invId: this.$route.query.id == null || this.$route.query.id == undefined ? '' : this.$route.query.id,
                applyNumber: '',
                rules: {
                    putrecSeqno: [
                        {
                            required: this.BAXHRequired,
                            max: 8,
                            validator: this.checkBANo,
                            checkName: '备案序号'
                        }
                    ],
                    applyTbSeqnoA: [
                        {
                            max: 10,
                            validator: this.checkNo
                        }
                    ],
                    entryGdsSeqno: [
                        {
                            max: 10,
                            validator: this.handlePentryGdsSeqnoCheck
                        }
                    ],

                    gdsMtno: [
                        {
                            required: true,
                            message: '请填写商品料号!',
                            max: 32
                        }
                    ],
                    hscode: [
                        {
                            required: true,
                            message: '请填写商品编码!',
                            max: 10
                        }
                    ],
                    hsname: [
                        {
                            required: true,
                            checkName: '商品名称',
                            max: 500,
                            validator: this.checkRelated
                        }
                    ],
                    hsmodel: [
                        {
                            required: true,
                            message: '请填写规格型号!'
                        }
                    ],
                    dclCurrcd: [
                        {
                            required: true,
                            message: '请选择币制!'
                        }
                    ],

                    dclUnitcd: [
                        {
                            required: true,
                            message: '请选择申报计量单位!'
                        }
                    ],
                    lawfUnitcd: [
                        {
                            required: true,
                            message: '请选择法定计量单位!'
                        }
                    ],
                    dclQty: [
                        {
                            required: true,
                            max: 12,
                            validator: this.checkNo,
                            checkName: '申报数量'
                        }
                    ],
                    lawfQty: [
                        {
                            required: true,
                            max: 12,
                            validator: this.checkNo,
                            checkName: '法定数量'
                        }
                    ],
                    secdLawfQty: [
                        {
                            max: 12,
                            validator: this.checkNo,
                            checkName: '第二法定数量'
                        }
                    ],
                    wtsfVal: [
                        {
                            max: 8,
                            validator: this.checkNo,
                            checkName: '重量比例因子'
                        }
                    ],
                    fstsfVal: [
                        {
                            max: 8,
                            validator: this.checkNo,
                            checkName: '第一比例因子'
                        }
                    ],
                    secdsfVal: [
                        {
                            max: 8,
                            validator: this.checkNo,
                            checkName: '第二比例因子'
                        }
                    ],

                    grossWt: [
                        {
                            max: 10,
                            validator: this.checkNo,
                            checkName: '毛重'
                        }
                    ],
                    netWt: [
                        {
                            max: 10,
                            validator: this.checkNo,
                            checkName: '净重'
                        }
                    ],
                    ucnsVerno: [
                        {
                            max: 8,
                            validator: this.checkRelated
                        }
                    ],
                    rmk: [
                        {
                            max: 15,
                            validator: this.checkRelated
                        }
                    ],

                    dclUprcamt: [
                        {
                            required: true,
                            message: '请填写企业申报单价!'
                        }
                    ],
                    dclTotalamt: [
                        {
                            required: true,
                            message: '请填写企业申报总价!'
                        }
                    ],
                    natcd: [
                        {
                            required: true,
                            message: '请选择最终目的国(地区)!'
                        }
                    ],
                    lvyrlfModecd: [
                        {
                            required: true,
                            message: '请选择征免方式!'
                        }
                    ]
                },

                // 报关单数据
                dataSourceBottom: [],
                // 当前选中的列表的数据(上面的表格展示)

                record: {
                    //更新标识I:新纪录 U:更新
                    opt: '',
                    //表体流水
                    id: '',
                    //核注单流水
                    invId: '',

                    //委托流水号
                    applyNumber: '',
                    //清单企业内部编号
                    etpsInnerInvtNo: '',
                    //中心统一编号 (返填 - 系统暂存时自动生成并返填）
                    seqNo: '',
                    //商品序号
                    gdsseqNo: '',
                    //备案序号(对应底账序号）
                    putrecSeqno: '',
                    //商品料号 （企业可录入，也可根据企业录入的备案序号从备案数据中获取并返填）
                    gdsMtno: '',
                    //商品编码 （系统自动返填。参数值如下：0-未修改1-修改2-删除3-增加）
                    hscode: '',
                    //商品名称 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    hsname: '',
                    //商品规格型号 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    hsmodel: '',
                    //申报计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    dclUnitcd: '',
                    //法定计量单位 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    lawfUnitcd: '',
                    //法定第二计量 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    secdlawfUnitcd: '',
                    //最终目的国
                    natcd: '',
                    //企业申报单价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                    dclUprcamt: '',
                    //企业申报总价 （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                    dclTotalamt: '',
                    //美元统计总金额
                    usdstatTotalamt: '',
                    //币制
                    dclCurrcd: '',
                    //法定数量
                    lawfQty: '',
                    //第二法定数量 （当法定第二计量单位为空时，该项为非必填）
                    secdLawfQty: '',
                    //重量比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    wtsfVal: '',
                    //第一比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    fstsfVal: '',
                    //第二比例因子 （返填 - 根据企业录入的备案序号或商品料号从备案数据中获取并返填）
                    secdsfVal: '',
                    //申报数量* （录入申报数量、申报单价、申报总价任意两项，系统自动生成第三项）
                    dclQty: '',
                    //毛重
                    grossWt: '',
                    //净重
                    netWt: '',
                    //用途代码*
                    useCd: '',
                    //征免方式
                    lvyrlfModecd: '',
                    //单耗版本号 （成品可填。手册不填，账册由开关控制是否必填。需看单耗该字段如何定义）（E02-04）
                    ucnsVerno: '',
                    //报关单商品序号  （企业可录入，如果企业不录入，系统自动返填）（E02-05）
                    entryGdsSeqno: '',
                    //归类标志
                    clymarkcd: '',
                    //流转申报表序号 （流转类专用。用于建立清单商品与流转申请表商品之间的关系）
                    applyTbSeqnoA: '',
                    //申请表序号
                    applyTbSeqnoB: '',
                    //入库时间 （返填）
                    addTime: '',
                    //实际过卡数量 （(SAS项目新增) 卡口抬杆后，系统根据核放单数量累计申报表商品过卡数量）
                    actlPassQty: '',
                    //核放单已用数量 （(SAS项目新增) 已生成核放单的商品数量，用于控制核放单商品数量超量）
                    passPortusedQty: '',
                    //备注
                    rmk: '',
                    //法捡类型
                    hstype: '',
                    //111111
                    goodsId: '',
                    //原进口企业内部编号
                    importBillNo: '',
                    //111111
                    ciqCode: '',
                    //111111
                    ciqName: '',
                    //飞机注册号
                    aircraftRegistrationNumber: '',
                    //111111
                    customHscode: '',
                    //111111
                    customDname: '',
                    //111111
                    customHsname: '',
                    //成交方式
                    transMode: '',
                    //贸易国(不显示)
                    tradeCountry: '',
                    //订单号
                    po: '',
                    //供应商
                    supplier: '',
                    //原产国
                    originCountry: '',
                    //通用字段(用于存储各种暂存值)
                    universal: '',
                    //剩余数量
                    balanceQty: '',
                    //111111
                    version: '',
                    //监管条件
                    supvModecd: '',
                    //英文描述
                    enMemo: '',
                    //11111
                    packs: '',
									storageNo: '',
                },
                // 选择展示的列
                settingColumns: [],

                // 展示的列的配置
                columns: [],

                // curl 的链接
                url: {
                    delete: '/dcl/customs_ciq/del',
                    deleteBatch: '/dcl/invt/removeInvtListsByIds',
                    exportXlsUrl: '/sys/user/exportXls',
                    importExcelUrl: 'sys/user/importExcel'
                },
                //国家
                countryList: [],
                //计量单位
                unitList: [],
                //币值
                currencySystemList: [],
                //征免方式
                lvyrlfModecdList: [],
                //是否是新增状态
                newStatus:
                    this.$route.query.newStatus == null ||
                    this.$route.query.newStatus == undefined ||
                    this.$route.query.newStatus == false
                        ? false
                        : true,

                //复制标识1：复制状态
                copyStatus:
                    this.$route.query.copyStatus == null || this.$route.query.copyStatus == undefined
                        ? ''
                        : this.$route.query.copyStatus,
                netWtText: '',//净重
                decTotalamtText: '',//金额
                grossWtText: '',//毛重
                secdLawfQtyText: '',//法定第二数量
                lawfQtyText: '',//法定数量合计
                dclQtyText: '',//申报数量合计
                clymarkcds:''
            }
        },
        props: {
            disableEdit: {
                type: Boolean
            },
            disableBu: {
                type: Boolean
            },
            BAXHRequired: {
                type: Boolean
            },
            //是否是当前登录用户创建
            privateStatus: {
                type: Boolean
            },
            //表头查询条件类型和手(帐)册编号
            InvatHeaderData: {
                type: Object
            },
					invtList: {
						type: Array,
						default: []
					}
        },
        async created() {
					await this.inDixt()

					if (this.InvatHeaderData && this.InvatHeaderData.id) {
						console.log('这是编辑状态')
						this.loadData()
					} else {
						if (!this.isEmpty(this.invtList)) {
							this.fromStore = true
							console.log('-->这是来自出库单的问候...' + this.invtList)
							setTimeout(() => {
								this.tableData = this.invtList
							}, 500)
							// await this.$refs.xTable2.reloadData(this.invtList)
							// let temp = this.$refs.xTable2.getTableData().fullData
							// console.log(temp)
							// let row = temp[0]
							// await this.cellClickEvent({row})
						}
						// 2024/1/24 12:23@ZHANGCHAO 追加/变更/完善：复制功能！！
						if (this.copyStatus == 1) {
							this.loadData()
						}
						console.log('这是新增状态')
					}
				},
        filters: {},
        methods: {
            checkRelated(rule, value, callback) {
                let that = this
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                    }
                } else {
                    if (!this.isEmpty(value)) {
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                    }
                }
                callback()
            },

            handlePentryGdsSeqnoCheck(rule, value, callback) {
                this.$nextTick(() => {
                    let gdsseqNo = this.isEmpty(this.record.gdsseqNo) ? '' : this.record.gdsseqNo
                    if (!this.isEmpty(value)) {
                        let reg = /^(0|\+?[1-9][0-9]*)$/

                        if (!reg.test(value)) {
                            callback(new Error('请输入数字!'))
                        }

                        if (value < 0) {
                            callback(new Error('不能输入负数!'))
                        }
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        } else if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (value.toString().indexOf('.') != -1) {
                            callback(new Error('不能含有小数点!'))
                        }
                        callback()
                    }
                    callback()
                })
            },

            validateGdsseqNo(rule, value, callback) {
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        let reg = /^(0|\+?[1-9][0-9]*)$/
                        let params = {
                            tableName: 'NEMS_INVT_LIST',
                            fieldName: 'GDSSEQ_NO',
                            fieldGroupingName: 'ETPS_INNER_INVT_NO',
                            fieldGroupingVal: this.etpsInnerInvtNo,
                            fieldVal: value,
                            dataId: this.record.id
                        }

                        if (value.length > 0) {
                            if (!reg.test(value)) {
                                callback(new Error('请输入正整数!'))
                            } else if (value < 0) {
                                callback(new Error('不能输入负数!'))
                            } else if (!this.isEmpty(rule.max) && value.length > rule.max) {
                                callback(new Error(`长度不能大于${rule.max}位!`))
                            } else if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                                callback(new Error(`长度不能大于${rule.max}位!`))
                            } else if (value.toString().indexOf('.') != -1) {
                                callback(new Error('不能含有小数点!'))
                            } else {
                                duplicateCheckByGrouping(params).then(res => {
                                    if (res.success) {
                                        callback()
                                    } else {
                                        callback(new Error('该序列号已存在!'))
                                    }
                                })
                            }
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                } else {
                    if (!this.isEmpty(value)) {
                        let reg = /^(0|\+?[1-9][0-9]*)$/
                        let params = {
                            tableName: 'NEMS_INVT_LIST',
                            fieldName: 'GDSSEQ_NO',
                            fieldGroupingName: 'ETPS_INNER_INVT_NO',
                            fieldGroupingVal: this.etpsInnerInvtNo,
                            fieldVal: value,
                            dataId: this.record.id
                        }

                        if (value.length > 0) {
                            if (!reg.test(value)) {
                                callback(new Error('请输入正整数!'))
                            } else if (value < 0) {
                                callback(new Error('不能输入负数!'))
                            } else if (!this.isEmpty(rule.max) && value.length > rule.max) {
                                callback(new Error(`长度不能大于${rule.max}位!`))
                            } else if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                                callback(new Error(`长度不能大于${rule.max}位!`))
                            } else if (value.toString().indexOf('.') != -1) {
                                callback(new Error('不能含有小数点!'))
                            } else {
                                duplicateCheckByGrouping(params).then(res => {
                                    if (res.success) {
                                        callback()
                                    } else {
                                        callback(new Error('该序列号已存在!'))
                                    }
                                })
                            }
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            },

            checkNo(rule, value, callback) {
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        let reg = /^([1-9]\d*\.?\d*|0\.\d*)$/
                        if (!reg.test(value)) {
                            callback(new Error('请输入正数!'))
                        }

                        if (value < 0) {
                            callback(new Error('不能输入负数!'))
                        }
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                } else {
                    if (!this.isEmpty(value)) {
                        let reg = /^([1-9]\d*\.?\d*|0\.\d*)$/
                        if (!reg.test(value)) {
                            callback(new Error('请输入正数!'))
                        }

                        if (value < 0) {
                            callback(new Error('不能输入负数!'))
                        }
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && String(value).length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            },
            checkBANo(rule, value, callback) {
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        let reg = /^([1-9]\d*\.?\d*|0\.\d*)$/
                        if (!reg.test(value)) {
                            callback(new Error('请输入正数!'))
                        }

                        if (value < 0) {
                            callback(new Error('不能输入负数!'))
                        }
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                } else {
                    if (!this.isEmpty(value)) {
                        let reg = /^([1-9]\d*\.?\d*|0\.\d*)$/
                        if (!reg.test(value)) {
                            callback(new Error('请输入正数!'))
                        }

                        if (value < 0) {
                            callback(new Error('不能输入负数!'))
                        }
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && String(value).length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                        if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            },
            loadData() {
                let that = this
                this.etpsInnerInvtNo = this.record.etpsInnerInvtNo = this.$route.query.etpsInnerInvtNo
                let params = this.getQueryParams() //查询条件
                this.etpsInnerInvtNo = this.invId
                params.invId = this.invId
                this.addType = this.disableEdit

                getAction('/dcl/invt/listInvt', params).then(res => {
                    if (res.success) {
                        {
                            that.dataSource = res.result.records
                            this.tableData = res.result.records
                            if (that.copyStatus == '1') {
                                let newDataSource = []
                                let newTableData = []
                                for (let i = 0; i < that.dataSource.length; i++) {
                                    that.dataSource[i].opt = 'I'
                                    that.tableData[i].opt = 'I'
                                    newDataSource.push(that.copyNewRecord(that.dataSource[i]))
                                    newTableData.push(that.copyNewRecord(that.tableData[i]))
                                }
                                that.dataSource = newDataSource
                                this.tableData = newTableData
                            }

                            that.ipagination.total = res.result.total
                            if (res.result && res.result.records !== undefined && res.result.records.length > 0) {
                                let data = res.result.records
                                let parm = {}
                                parm.invId = data[0].invId
                                parm.entryNo = this.InvatHeaderData.entryNo
                                //获取list data.entryGdsSeqno，用逗号拼接
                                let itemList = []
                                data.forEach(item => {
                                  itemList.push(item.entryGdsSeqno)
                                })
                                parm.item = itemList.join(',')
                                    this.$nextTick(() => {
                                        that.dataSourceBottom = that.getDecList(parm)
                                        this.fulltableData = this.$refs.xTable2.getTableData()
                                    })
                            }
                        }
                    }
                })
            },
            copyNewRecord(record) {
                Object.keys(record).forEach(key => {
                    if (
                        key == 'applyNumber' ||
                        key == 'id' ||
                        key == 'etpsInnerInvtNo' ||
                        key == 'tenantId' ||
                        key == 'invId' ||
                        key == 'storageNo' ||

                        key == 'stockGoodsId' ||
                        key == 'sasStockNo' ||
                        key == 'storageDetailId'
                    ) {
                        record[key] = ''
                    }
                    if (key == 'opt') {
                        record[key] = 'I'
                    }
                })
                return record
            },
            inDixt() {
              ajaxGetDictItems('erp_countries,name,code,isenabled=0', null).then(res => {
                if (res.success) {
                  this.countryList = res.result
                }
              })
              ajaxGetDictItems('erp_units,name,code,1=1 order by code asc', null).then(res => {
                if (res.success) {
                  this.unitList = res.result
                }
              })
              ajaxGetDictItems('erp_currencies,name,code,currency,1=1', null).then(res => {
                if (res.success) {
                  this.currencySystemList = res.result
                }
              })
							ajaxGetDictItems('ZJMSFS', null).then(res => {
								if (res.success) {
									this.lvyrlfModecdList = res.result
								}
							})
            },
            formatterGBDQ({ cellValue }) {
                let item = this.countryList.find(item => item.value == cellValue)
                return item ? item.text : ''
            },

            formatterlvyrlfModecd({ cellValue }) {
                let item = this.lvyrlfModecdList.find(item => item.value == cellValue)
                return item ? item.text : ''
            },
            formatterJLDW({ cellValue }) {
                let item = this.unitList.find(item => item.value == cellValue)
                return item ? item.text : ''
            },
            formatterBZ({ cellValue }) {
                let item = this.currencySystemList.find(item => item.value == cellValue)
                return item ? item.text : '-'
            },
            //备案序号回车
            putrecSeqnoEnter(row) {
                let emstype = ''
                //0:料件;1:成品;2:单损耗"
                if (this.InvatHeaderData.mtpckEndprdMarkcd == 'I') {
                    emstype = '1'
                }
                if (this.InvatHeaderData.mtpckEndprdMarkcd == 'E') {
                    emstype = '2'
                }
                let that = this
							if(that.InvatHeaderData.putrecNo&&row&&emstype){
                getAction('/business/ems/listEmsDetail', {
                    pageNum: '1',
                    pageSize: '1',
                        emsNo: that.InvatHeaderData.putrecNo,
                        gNo: row,
											type: emstype,

                }).then(res => {
                    if (res.success) {
                        // 商品料号、商品编码、商品名称、规格型号、申报计量单位、法定计量单位、法定第二计量单位
											if (!this.isEmpty(res.result.records)) {
												//提示信息
												this.$message.success('已自动带取账册备案信息。')
                        let newEms = res.result.records[0]
                            // 商品料号
                            that.record.gdsMtno = newEms.copGno
                            // 商品编码
                            that.record.hscode = newEms.codet
                            //商品名称
                            that.record.hsname = newEms.gName
                            //规格型号、
                            that.record.hsmodel = newEms.gModel
                            //申报计量单位、
                            that.record.dclUnitcd = newEms.unit
                            //  法定计量单位、法定第二计量单位
                            that.record.lawfUnitcd = newEms.unit1
                            that.record.secdlawfUnitcd = newEms.unit2
												that.record.dclUprcamt= newEms.decPrice
                        }else {
												this.$message.warn('账册号'+that.InvatHeaderData.putrecNo+'下查询不到备案序号'+row+'的备案信息')
											}
                    }
                })
							}
            },
					//商品料号 回车
					gdsMtnoEnter(row){
						let emstype = ''
						//0:料件;1:成品;2:单损耗"
						if (this.InvatHeaderData.mtpckEndprdMarkcd == 'I') {
							emstype = '1'
						}
						if (this.InvatHeaderData.mtpckEndprdMarkcd == 'E') {
							emstype = '2'
						}
						let that = this
						if(that.InvatHeaderData.putrecNo&&row&&emstype){
							getAction('/business/ems/listEmsDetail', {
								pageNum: '1',
								pageSize: '1',
								emsNo: that.InvatHeaderData.putrecNo,
								copGno: row,
								type: emstype,

							}).then(res => {
								if (res.success) {
									// 商品料号、商品编码、商品名称、规格型号、申报计量单位、法定计量单位、法定第二计量单位
									if (!this.isEmpty(res.result.records)) {
										//提示信息
										this.$message.success('已自动带取账册备案信息。')

										let newEms = res.result.records[0]
										that.record.putrecSeqno = newEms.gNo
										// 商品料号
										that.record.gdsMtno = newEms.copGno
										// 商品编码
										that.record.hscode = newEms.codet
										//商品名称
										that.record.hsname = newEms.gName
										//规格型号、
										that.record.hsmodel = newEms.gModel
										//申报计量单位、
										that.record.dclUnitcd = newEms.unit
										//  法定计量单位、法定第二计量单位
										that.record.lawfUnitcd = newEms.unit1
										that.record.secdlawfUnitcd = newEms.unit2
										that.record.dclUprcamt= newEms.decPrice
									}else {
										this.$message.warn('账册号'+that.InvatHeaderData.putrecNo+'下查询不到商品料号'+row+'的备案信息')
									}
								}
							})
						}
					},
            //双击操作
            cellDBLClickEvent({ row }) {
            },
            //单击操作
            async cellClickEvent({ row }) {
							console.log(row)
                this.selectedRows = await this.$refs.xTable2.getCheckboxRecords()
                this.EditDataStatus = true
                this.addType = true
                this.record = row
                let parm = {}

                parm.item = this.record.entryGdsSeqno

                let invId = this.$route.query.id
                parm.invId = invId

                // await this.getDecList(parm)

                this.addType = false
            },

            // 当手动勾选全选时触发的事件
            handleVxeCheckboxAll(event) {

            },
            // // 当手动勾选并且值发生改变时触发的事件
            handleVxeCheckboxChange(event) {
                this.selectedRows =  this.$refs.xTable2.getCheckboxRecords()
                let newselectedRowKeys = []
                for (let i = 0; i < this.selectionRows.length; i++) {
                    newselectedRowKeys.push(this.selectionRows[i].id)
                }
                this.selectedRowKeys = newselectedRowKeys
            },
            headerCellClickEvent({ row, column }) {

            },
            headerCellDBLClickEvent({ column }) {
                //  console.log(`表头单元格双击${column.title}`);
            },
            headerCellContextMenuEvent({ column }) {
                //  console.log(`右键列 ${column.title}`);
            },
            cellSelected(row) {
            },
            checkboxRangeChange(records, reserves, $event) {
                this.selectedRows = this.$refs.xTable2.getCheckboxRecords()
            },
            //新上移
            async moveXUp() {
                let selectedrow = this.$refs.xTable2.getCheckboxRecords()[0]
                //let allSelectedrow = this.$refs.xTable2.getTableData().fullData;
                let allSelectedrow = this.$refs.xTable2.getTableData().visibleData
                //this.$refs.xTable2.remove(selectedrow)
                let index = allSelectedrow.indexOf(selectedrow)
                if (index > 0 && index < this.$refs.xTable2.getTableData().fullData.length) {
                    let ini = index - 1
                    let tempss = allSelectedrow[ini]
                    let temp2 = allSelectedrow[index]
                    if (this.isEmpty(tempss.opt) || tempss.opt == 'U') {
                        tempss.opt = 'U'
                        tempss.gdsseqNo = index + 1
                    }

                    if (this.isEmpty(temp2.opt) || temp2.opt == 'U') {
                        temp2.opt = 'U'
                        temp2.gdsseqNo = index
                    }
                    if (tempss.opt == 'I') {
                        tempss.opt = 'I'
                        tempss.gdsseqNo = index + 1
                    }
                    if (temp2.opt == 'I') {
                        temp2.opt = 'I'
                        temp2.gdsseqNo = index
                    }

                    allSelectedrow[index - 1] = temp2
                    allSelectedrow[index] = tempss
                    this.$refs.xTable2.reloadData(allSelectedrow)
                }
            },

            //新下移
            async moveXDown() {
                let selectedrow = this.$refs.xTable2.getCheckboxRecords()[0]
                let allSelectedrow = this.$refs.xTable2.getTableData().fullData
                let index = allSelectedrow.indexOf(selectedrow)
                if (index < allSelectedrow.length) {
                    let temp = allSelectedrow[index]
                    let temp2 = allSelectedrow[index + 1]
                    if (this.isEmpty(temp.opt) | (temp.opt == 'U')) {
                        temp.opt = 'U'
                        temp.gdsseqNo = index + 2
                    }
                    if (this.isEmpty(temp2.opt) | (temp.opt == 'U')) {
                        temp2.opt = 'U'
                        temp2.gdsseqNo = index + 1
                    }
                    if (temp.opt == 'I') {
                        temp.opt = 'I'
                        temp.gdsseqNo = index + 2
                    }
                    if (temp2.opt == 'I') {
                        temp2.opt = 'I'
                        temp2.gdsseqNo = index + 1
                    }
                    allSelectedrow[index] = temp2
                    allSelectedrow[index + 1] = temp
                    await this.$refs.xTable2.reloadData(allSelectedrow)
                }
            },

            updateFormValues() {
                let trs = this.getElement('tbody').getElementsByClassName('tr')
                let trEls = []
                for (let tr of trs) {
                    trEls.push(tr)
                }
                // 获取新增的 tr
                let newTrEls = trEls
                if (this.$refs.xTable2.visibleTrEls.length > 0) {
                    newTrEls = []
                    for (let tr of trEls) {
                        let isNewest = true
                        for (let vtr of this.$refs.xTable2.visibleTrEls) {
                            if (vtr.id === tr.id) {
                                isNewest = false
                                break
                            }
                        }
                        if (isNewest) {
                            newTrEls.push(tr)
                        }
                    }
                }
                this.$refs.xTable2.visibleTrEls = trEls
                // 向新增的tr中赋值
                newTrEls.forEach(tr => {
                    let { idx } = tr.dataset
                    let value = this.$refs.xTable2.inputValues[idx]
                    for (let key in value) {
                        if (value.hasOwnProperty(key)) {
                            let elid = `${key}${value.id}`
                            let el = document.getElementById(elid)
                            if (el) {
                                el.value = value[key]
                            }
                        }
                    }
                })
            },
            /** 触发已拖动事件 */
            emitDragged(oldIndex, newIndex) {
                this.$refs.xTable2.$emit('dragged', { oldIndex, newIndex, target: this })
            },
            /** 强制更新FormValues */
            forceUpdateFormValues() {
                this.$refs.xTable2.visibleTrEls = []
                this.$refs.xTable2.$forceUpdate()
                this.$refs.xTable2.$nextTick(() => this.$refs.xTable2.updateFormValues())
            },
            //新暂存
            addXBody() {
                let that = this
                this.$refs.xTable2.clearCheckboxReserve()
                this.$refs.xTable2.clearCheckboxRow()

                this.addType = true
                let row = JSON.parse(JSON.stringify(that.record))
                //this.$refs.nemsInvtForm.clearValidate()
                Object.keys(row).forEach(key => (row[key] = ''))
                // this.selectedRowKeys = []
                // this.selectionRows = []
                //  this.ipagination.total = this.ipagination.total + 1
                let num = this.$refs.xTable2.getTableData().fullData.length + 1
                row.gdsseqNo = num
                row.etpsInnerInvtNo = this.$route.query.etpsInnerInvtNo
                row.invId = this.$route.query.id
                row.opt = 'I'
                //变成可编辑
                this.addType = false
                //变为非编辑数据状态
                //
                that.record = row
                // this.$refs.xTable2.insert(that.record)
                this.fulltableData = this.$refs.xTable2.getTableData()

                //this.$refs.xTable2.setSelectCell(-1, field)
                this.EditDataStatus = false
                //
            },
            //新删除
            async batchXDelbody() {
                this.$refs.xTable2.removeCheckboxRow()
                this.fulltableData = this.$refs.xTable2.getTableData()
                let temp = this.$refs.xTable2.getTableData().fullData
                for (let i = 0; i < temp.length; i++) {
                    // 这里的i是代表数组的下标
                    if (this.isEmpty(temp[i].opt) | (temp[i].opt == 'U')) {
                        temp[i].opt = 'U'
                        temp[i].gdsseqNo = i + 1
                    }

                    if (temp[i].opt == 'I') {
                        temp[i].opt = 'I'
                        temp[i].gdsseqNo = i + 1
                    }
                }
                await this.$refs.xTable2.reloadData(temp)
                this.$message.info('数据已变动，保存后生效')
            },
            //新暂存
            SaveXData(row) {
                let that = this
                this.$refs.nemsInvtForm.validate(async valid => {
                    if (valid) {
                        if (that.EditDataStatus) {
                            if (that.isEmpty(row.opt)) {
                                row.opt = 'U'
                            }
                            Object.assign(that.record, row)
                            // Object.keys(that.record).forEach((key) => (that.record[key] = ''))
                            that.addType = true
                        } else {
                            row.opt = 'I'
                            if (row.gdsseqNo == this.$refs.xTable2.getTableData().fullData.length + 1) {
                                let { row: newRow } = await that.$refs.xTable2.insertAt(row, -1)
                                that.record = newRow
                            } else if (this.isEmpty(row.gdsseqNo)) {
                                row.gdsseqNo = this.$refs.xTable2.getTableData().fullData.length + 1
                                let { row: newRow } = await that.$refs.xTable2.insertAt(row, -1)
                                that.record = newRow
                            } else if (row.gdsseqNo > this.$refs.xTable2.getTableData().fullData.length + 1) {
                                let { row: newRow } = await that.$refs.xTable2.insertAt(row, -1)
                                that.record = newRow
                            } else {
                                let { row: newRow } = await that.$refs.xTable2.insertAt(row, row.gdsseqNo)
                                that.record = newRow
                            }

                            this.addType = true
                        }
                        this.$refs.xTable2.clearCheckboxReserve()
                        this.$refs.xTable2.clearCheckboxRow()
                        this.EditDataStatus = false
                    }
                })
                this.SortingNo()
                this.fulltableData = this.$refs.xTable2.getTableData()
            },
            //新复制
            copyXData() {

                this.EditDataStatus = false
                let that = this
                that.addType = false
                //获取选择数据
                let selectRecords = this.$refs.xTable2.getCheckboxRecords()

                if (selectRecords.length < 1) {
                    that.$message.warning('请勾选要复制的数据')
                }
                if (selectRecords.length) {
                    let row = JSON.parse(JSON.stringify(selectRecords[0]))
                    this.$refs.nemsInvtForm.clearValidate()
                    // Object.keys(row).forEach((key) => (row[key] = ''))
                    this.ipagination.total = this.ipagination.total + 1
                    let num = this.$refs.xTable2.getTableData().fullData.length + 1
                    row.gdsseqNo = num
                    row.etpsInnerInvtNo = this.$route.query.etpsInnerInvtNo
                    row.invId = this.$route.query.id
                    row.entryGdsSeqno = num
                    row.opt = 'I'
                    row.id = ''
                    that.record = row
                    that.$nextTick(() => {
                        this.$refs.nemsInvtForm.validate(async valid => {
                            if (valid) {
                                let { row: newRow } = await that.$refs.xTable2.insertAt(this.record, -1)
                                that.record = newRow
                                this.addType = true
                                that.EditDataStatus = true
                                this.$refs.xTable2.scrollTo(0, num * 36)
                            } else {
                            }
                        })

                        that.fulltableData = that.$refs.xTable2.getTableData()
                        this.SortingNo()
                    })
                }
            },
					enterXSave(data) {
						// if(num==1&&this.InvatHeaderData.impexpMarkcd == 'I'){
						// 	this.SaveXData(data)
						// }else if(num==2&&this.InvatHeaderData.impexpMarkcd == 'E'){
							this.SaveXData(data)
						// }
					},
            //获取对应报关单数据
            getDecList(parm) {
                let that = this
                getAction('/dcl/invt/getDecListByInvtIdAndItem', parm)
                    .then(res => {
                        if (res.success) {
                            that.dataSourceBottom = []
                            if (null != res.result) {
                                that.dataSourceBottom = res.result
                            }
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                    .finally(() => {
                        // this.saveingStatu = false
                    })
            },
            handleTableChangeBGD() {
                getDecList({
                    etpsInnerInvtNo: this.record.etpsInnerInvtNo,
                    item: this.record.entryGdsSeqno
                })
            },
            fineTableEventBGD({ row }) {
							if (row.ieFlag === 'I') {
								this.$router.push({
									path: '/Business/customs-declaration/details_I',
									query: {
										id: row.decId,
										imSignId: row.ieFlag == 'I' ? '1' : '2'
									}
								})
							} else if (row.ieFlag === 'E') {
								this.$router.push({
									path: '/Business/customs-declaration/details_E',
									query: {
										id: row.decId,
										imSignId: row.ieFlag == 'I' ? '1' : '2'
									}
								})
							}
            },

            defineTableEvent(record, index) {
                let that = this
                return {
                    on: {
                        click: () => {
                            this.addType = true
                            // this.record = JSON.parse(JSON.stringify(record))
                            that.record = record
                            let parm = {}

                            ;(parm.etpsInnerInvtNo = record.etpsInnerInvtNo),
                                (parm.item = record.gdsseqNo),
                                that.getDecList(parm)
                        },
                        dblclick: () => {
                            this.addType = false
                            // this.record = JSON.parse(JSON.stringify(record))
                            this.record = record
                            let parm = {}
                            ;(parm.etpsInnerInvtNo = record.etpsInnerInvtNo),
                                (parm.item = record.gdsseqNo),
                                that.getDecList(parm)
                            that.selectedRowKeys = []
                            that.selectionRows = []
                        }
                    }
                }
            },
            //导入
            handleImportExcel() {
            },
            //导出
            handleExportXls() {
            },

            // 向父类传值
            returnBackFn() {
                this.$emit('varietyDisableEditD', JSON.parse(JSON.stringify(this.record)))
                this.$emit('change', JSON.parse(JSON.stringify(this.dataSource)))
            },
            //自动排序
            SortingNo() {
                this.$refs.xTable2.sort('gdsseqNo', 'asc')
            },

            /**
             * 成交数量回车模态窗
             */
            // handleCancel() {
            //     this.showUnitTotal = false
            // },
            priceEnter() {
            },
            // toShowUnitTotal(e) {
						//
            //     let value = parseFloat(this.record.dclQty || 0) * parseFloat(this.record.dclUprcamt || 0)
            //     value = value || 0
            //     if (this.record.dclTotalamt != value.toFixed(2) || e == 1) {
            //         this.showUnitTotal = true
            //     }
            // },
            //如果法定第一数量或法定第二数量是千克的就取净重
            unit1unit2Qk() {

                let count = ''
                if (!this.isEmpty(this.record.netWt) && this.record.netWt != 0) {
                    if ('035' == this.record.lawfUnitcd) {
                        this.record.lawfQty = this.record.netWt
                        count = count + 'lawfUnitcd,'
                    } else if ('036' == this.record.lawfUnitcd) {
                        this.record.lawfQty = this.record.netWt / 1000
                        count = count + 'lawfUnitcd,'
                    }

                    if ('035' == this.record.secdlawfUnitcd) {
                        this.record.secdLawfQty = this.record.netWt
                        count = count + (count.length == 0 ? ',secdlawfUnitcd' : 'secdlawfUnitcd')
                    } else if ('036' == this.record.secdlawfUnitcd) {
                        this.record.secdLawfQty = this.record.netWt / 1000
                        count = count + (count.length == 0 ? ',secdlawfUnitcd' : 'secdlawfUnitcd')
                    }
                } else {
                    count = ','
                }
                return count

            },
            //如果法定第一数量是千克的就净重取法定数量
            unit1Qk() {
                if (!this.isEmpty(this.record.lawfQty) && this.record.lawfQty != 0) {
                    if ('035' == this.record.lawfUnitcd) {
                        this.record.netWt = this.record.lawfQty
                    }
                }
            },
            returnBackFnCount() {
                let count = this.unit1unit2Qk()
                let countSplit = count.split(',')
                let countSplit1 = countSplit[0]
                let countSplit2 = countSplit[1]

                if (this.record.dclUnitcd == this.record.lawfUnitcd && countSplit1.length == 0) {
                    this.record.lawfQty = this.record.dclQty
                } else if (this.record.dclUnitcd == this.record.secdlawfUnitcd && countSplit2.length == 0) {
                    this.record.secdLawfQty = this.record.dclQty
                }
                //3.除上面1和2的逻辑，其他都默认法定第一/二数量=成交数量。
                let bioList = ['007', '054', '044']
                let gramList = ['036', '035']

                if (bioList.indexOf(this.record.dclUnitcd) > -1) {
                    if (bioList.indexOf(this.record.lawfUnitcd) == -1 && !this.isEmpty(this.record.lawfUnitcd) && countSplit1.length == 0) {
                        this.record.lawfQty = this.record.dclQty
                    }
                    if (bioList.indexOf(this.record.secdlawfUnitcd) == -1 && !this.isEmpty(this.record.secdlawfUnitcd) && countSplit2.length == 0) {
                        this.record.secdLawfQty = this.record.dclQty
                    }
                } else if (gramList.indexOf(this.record.dclUnitcd) > -1) {
                    if (gramList.indexOf(this.record.lawfUnitcd) == -1 && !this.isEmpty(this.record.lawfUnitcd) && countSplit1.length == 0) {
                        this.record.lawfQty = this.record.dclQty
                    }
                    if (gramList.indexOf(this.record.secdlawfUnitcd) == -1 && !this.isEmpty(this.record.secdlawfUnitcd) && countSplit2.length == 0) {
                        this.record.secdLawfQty = this.record.dclQty
                    }
                } else {
                    if (!this.isEmpty(this.record.lawfUnitcd) && countSplit1.length == 0) {
                        this.record.lawfQty = this.record.dclQty
                    }
                    if (!this.isEmpty(this.record.secdlawfUnitcd) && countSplit2.length == 0) {
                        this.record.secdLawfQty = this.record.dclQty
                    }
                }
                this.transitionUnit()
            },
            /**
             * 修改单价
             */
            // unitButton() {
            //     if (!!this.record.dclTotalamt && !!this.record.dclQty) {
            //         this.record.dclUprcamt = this.record.dclTotalamt / this.record.dclQty
            //         this.record.dclUprcamt = this.record.dclUprcamt.toFixed(4)
            //     }
            //     this.showUnitTotal = false
            //     //模态窗需要手动加1
            // },
            /**
             * 修改总价
             */
            // totalButton(e) {
            //     if (!!this.record.dclQty && !!this.record.dclUprcamt) {
            //         this.record.dclTotalamt = this.record.dclQty * this.record.dclUprcamt
            //         this.record.dclTotalamt = this.record.dclTotalamt.toFixed(2)
            //     }
            //     this.showUnitTotal = false
            //     //模态窗需要手动加1
            // },
            //单价
            priceChange() {
                let value = parseFloat(this.record.dclQty || 0) * parseFloat(this.record.dclUprcamt || 0)
                value = value || 0
                this.record.dclTotalamt = this.isEmpty(value) ? '' : value.toFixed(2)
            },
            //总价
            totalChange() {
                if (this.record.dclTotalamt == 0 || this.isEmpty(this.record.dclQty)) return
                let value = parseFloat(this.record.dclTotalamt || 0) / parseFloat(this.record.dclQty || 0)
                value = value || 0
                this.record.dclUprcamt = this.isEmpty(value) ? '' : value.toFixed(4)
            },
            //商品编码回车
            hscodeEnter() {

                this.focusIndex = 0
                this.invtTeriffShow = !this.invtTeriffShow


            },
            hscodeEnterNext() {
                this.focusIndex = 11111
            },
            myfocusIndexMethod() {
                this.focusIndex = 0
            },
            enterlawfQtyEnterNext() {
                this.focusIndex = 22222
            },
            transitionUnit() {

                let count = this.unit1unit2Qk()
                let countSplit = count.split(',')
                let countSplit1 = countSplit[0]
                let countSplit2 = countSplit[1]
                // if(this.record.dclUnitcd==this.record.lawfUnitcd){
                //     this.record.lawfQty=this.record.dclQty
                // }else if(this.record.dclUnitcd==this.record.secdlawfUnitcd){
                //     this.record.secdLawfQty=this.record.dclQty
                // }
                //法定第一计量单位lawfUnitcd 法定第二secdlawfUnitcd 申报计量单位dclUnitcd
                if (this.record.dclUnitcd == '007') {//个
                    if (this.record.lawfUnitcd == '054') {//千个
                        this.record.lawfQty = this.record.dclQty / 1000
                    }
                    if (this.record.secdlawfUnitcd == '054') {//千个
                        this.record.secdLawfQty = this.record.dclQty / 1000
                    }
                    if (this.record.lawfUnitcd == '044') {//百片
                        this.record.lawfQty = this.record.dclQty / 100
                    }
                    if (this.record.secdlawfUnitcd == '044') {//百片
                        this.record.secdLawfQty = this.record.dclQty / 100
                    }
                } else if (this.record.dclUnitcd == '054') {
                    if (this.record.lawfUnitcd == '007') {//个
                        this.record.lawfQty = this.record.dclQty * 1000
                    }
                    if (this.record.secdlawfUnitcd == '007') {//千个
                        this.record.secdLawfQty = this.record.dclQty * 1000
                    }
                } else if (this.record.dclUnitcd == '044') {//百片
                    if (this.record.lawfUnitcd == '007') {//个
                        this.record.lawfQty = this.record.dclQty * 100
                    }
                    if (this.record.secdlawfUnitcd == '007') {//个
                        this.record.secdLawfQty = this.record.dclQty * 100
                    }
                }

                if (this.record.dclUnitcd == '036') {//个
                    if (this.record.lawfUnitcd == '035' && countSplit1.length == 0) {//千个
                        this.record.lawfQty = this.record.dclQty / 1000
                    }
                    if (this.record.secdlawfUnitcd == '035' && countSplit2.length == 0) {//千个
                        this.record.secdLawfQty = this.record.dclQty / 1000
                    }
                } else if (this.record.dclUnitcd == '035') {
                    if (this.record.lawfUnitcd == '036' && countSplit1.length == 0) {//个
                        this.record.lawfQty = this.record.dclQty * 1000
                    }
                    if (this.record.secdlawfUnitcd == '036' && countSplit2.length == 0) {//千个
                        this.record.secdLawfQty = this.record.dclQty * 1000
                    }
                }
                //法定第一计量单位lawfUnitcd 法定第二secdlawfUnitcd 申报计量单位dclUnitcd
                //成交计量单位是件 法一法二是千个
                if (this.record.dclUnitcd == '011') {//件
                    if (this.record.lawfUnitcd == '054' ) {//千个
                        this.record.lawfQty = this.record.dclQty / 1000
                    }
                    if (this.record.secdlawfUnitcd == '054' ) {//千个
                        this.record.secdLawfQty = this.record.dclQty / 1000
                    }
                } else if (this.record.dclUnitcd == '054') {//千个
                    if (this.record.lawfUnitcd == '011' ) {//件
                        this.record.lawfQty = this.record.dclQty * 1000
                    }
                    if (this.record.secdlawfUnitcd == '011' ) {//件
                        this.record.secdLawfQty = this.record.dclQty * 1000
                    }
                }
            },
            lawfUnitcdDlur(){

                this.transitionUnit()
            },
            secdLawfQtyChange(e){

                if(this.record.secdlawfUnitcd=='035'){
                    this.record.netWt = e
                }

            },
            setClymarkcdBatch(){

                if (this.selectedRows.length==0){
                    this.$message.error('请选择一条信息')
                    return
                }
                for(let i=0;i<this.selectedRows.length;i++){
                    this.selectedRows[i].clymarkcd = this.clymarkcds
                    this.selectedRows[i].opt = "U"
                }

            }

        },
        watch: {
            record: {
                handler(newValue, oldValue) {
                    this.returnBackFn()
                },
                deep: true
            },
            // showUnitTotal: {
            //     handler(newValue, oldValue) {
						//
            //         if (newValue == false) {
            //             this.enterlawfQtyEnterNext()
            //         }
            //     }
						//
            // },

            dataSource: {
                handler(newValue, oldValue) {

                    this.returnBackFn()

                },
                deep: true
            },
            // tableData: {
            //     handler(newValue, oldValue) {
						//
            //         this.dataSource
            //         this.returnBackFn()
            //     },
            //     deep: true
            // },
            fulltableData: {
                handler(newValue, oldValue) {

                    this.dataSource
                    this.SortingNo()
                    //重新计算重量
                    let netWtNum = 0
                    let decTotalamtNum = 0
                    let grossWtNum = 0
                    let addWeightNum = 0

                    let dclQtyNum = 0
                    let lawfQtyNum = 0
                    let secdLawfQtyNum = 0
                    for (let i = 0; i < newValue.fullData.length; i++) {
                        if (!!newValue.fullData[i].dclTotalamt) {
                            decTotalamtNum = decTotalamtNum + Number.parseFloat(newValue.fullData[i].dclTotalamt)
                        }
                        if (!!newValue.fullData[i].grossWt) {
                            grossWtNum = grossWtNum + Number.parseFloat(newValue.fullData[i].grossWt)
                        }

                        if (!!newValue.fullData[i].dclQty) {
                            dclQtyNum = dclQtyNum + Number.parseFloat(newValue.fullData[i].dclQty)
                        }
                        if (!!newValue.fullData[i].lawfQty) {
                            lawfQtyNum = lawfQtyNum + Number.parseFloat(newValue.fullData[i].lawfQty)
                        }
                        if (!!newValue.fullData[i].secdLawfQty) {
                            secdLawfQtyNum = secdLawfQtyNum + Number.parseFloat(newValue.fullData[i].secdLawfQty)
                        }


                        //核注单详情页面合计功能,克、吨单位需要转换为千克再累计净重！
                        if (newValue.fullData[i].lawfUnitcd == '035' || newValue.fullData[i].lawfUnitcd == '036' || newValue.fullData[i].lawfUnitcd == '070') {

                            if (!!newValue.fullData[i].lawfQty) {
                                if (newValue.fullData[i].lawfUnitcd == '036') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].lawfQty) / 1000)
                                } else if (newValue.fullData[i].lawfUnitcd == '035') {
                                    addWeightNum = addWeightNum + Number.parseFloat(newValue.fullData[i].lawfQty)
                                } else if (newValue.fullData[i].lawfUnitcd == '070') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].lawfQty) * 1000)
                                }
                            }

                        } else if (newValue.fullData[i].secdlawfUnitcd == '035' || newValue.fullData[i].secdlawfUnitcd == '036' || newValue.fullData[i].secdlawfUnitcd == '070') {

                            if (!!newValue.fullData[i].secdLawfQty) {
                                if (newValue.fullData[i].secdlawfUnitcd == '036') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].secdLawfQty) / 1000)
                                } else if (newValue.fullData[i].secdlawfUnitcd == '035') {
                                    addWeightNum = addWeightNum + Number.parseFloat(newValue.fullData[i].secdLawfQty)
                                } else if (newValue.fullData[i].secdlawfUnitcd == '070') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].secdLawfQty) * 1000)
                                }
                            }

                        } else if (newValue.fullData[i].dclUnitcd == '035' || newValue.fullData[i].dclUnitcd == '036' || newValue.fullData[i].dclUnitcd == '070') {
                            if (!!newValue.fullData[i].dclQty) {
                                if (newValue.fullData[i].dclUnitcd == '036') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].dclQty) / 1000)
                                } else if (newValue.fullData[i].dclUnitcd == '035') {
                                    addWeightNum = addWeightNum + Number.parseFloat(newValue.fullData[i].dclQty)
                                } else if (newValue.fullData[i].dclUnitcd == '070') {
                                    addWeightNum = addWeightNum + (Number.parseFloat(newValue.fullData[i].dclQty) * 1000)
                                }
                            }
                        }
                    }
                    this.netWtText = addWeightNum.toFixed(4)
                    this.netWtText  = lodash.round( this.netWtText ,4)

                    this.decTotalamtText = decTotalamtNum.toFixed(4)
                    this.decTotalamtText  = lodash.round( this.decTotalamtText ,4)

                    this.grossWtText = grossWtNum.toFixed(4)
                    this.grossWtText  = lodash.round( this.grossWtText ,4)

                    this.dclQtyText = dclQtyNum.toFixed(4)
                    this.dclQtyText  = lodash.round( this.dclQtyText ,4)

                    this.lawfQtyText = lawfQtyNum.toFixed(4)
                    this.lawfQtyText  = lodash.round( this.lawfQtyText ,4)

                    this.secdLawfQtyText = secdLawfQtyNum.toFixed(4)
                    this.secdLawfQtyText  = lodash.round( this.secdLawfQtyText ,4)
                    //  this.returnBackFullFn()
                },
                deep: true
            },

            addType: {
                handler(newValue, oldValue) {
                    if (this.addType) {
                        this.$nextTick(() => {
                            this.$refs.nemsInvtForm.clearValidate()
                        })
                    }
                },
                deep: true
            },

            // 'record.dclUnitcd'(newValue, oldValue) {
            //     this.transitionUnit()
            // },
            // 'record.lawfUnitcd'(newValue, oldValue) {
            //     this.transitionUnit()
            // },
            // 'record.secdlawfUnitcd'(newValue, oldValue) {
            //     this.transitionUnit()
            // }
        }
    }
</script>

<style scoped>
/** Button按钮间距 */
.table-opera .ant-btn {
	margin: 0 8px 2px 0;
}
    .detailsRightDiv {
        border: 1px solid #BDBDBD;
        background-color: #E6F7FF;
        float: right;
        font-size: 11px;
    }

    .detailsRightDiv1 {
        float: left;
        margin-left: 2px;
        font-size: 12px;
    }

    .detailsRightDiv2 {
        float: right;
        margin-left: 2px
    }
    .sortable-row-demo {
        margin-top: 10px;
    }
</style>
