package org.jeecg.modules.business.entity.receipt.vo.trade;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 加工贸易报文回执[业务实体]
 *
 * <AUTHOR>
 *
 * @date 2020年9月1日 上午8:34:21
 */
public class PackageReceiptBusiness extends PackageReceiptCommon {

	/**
	 * GET 回执类型
	 *
	 * @return 回执类型
	 */
	public String getReceiptType() {
		return super.getEnvelopInfo().getMessageType().trim();
	}

	/**
	 * 获取业务类型
	 *
	 * @return 业务类型
	 */
	public String getTypecd() {
		return super.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getTypecd().trim();
	}
	/**
	 * 获取业务类型
	 *
	 * @return 业务类型
	 */
	public Date getSendTime() {
		try {
			return parseDate(super.getEnvelopInfo().getSendTime().replace("T", " "));
		} catch (ParseException e) {
			e.printStackTrace();

		}
		return null;
	}

	private static final SimpleDateFormat YMD = new SimpleDateFormat("yyyyMMdd");
	private static final Pattern SYMD = Pattern.compile("\\d{4}[0,1]\\d[0-3]\\d");
	private static final SimpleDateFormat YMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final Pattern SYMDHMS = Pattern.compile("\\d{4}-[0,1]\\d-[0-3]\\d \\d+:\\d+:\\d+");
	private static final SimpleDateFormat YMDHMS_NH = new SimpleDateFormat("yyyyMMdd HHmmss");
	private static final Pattern SYMDHMS_NH = Pattern.compile("\\d{4}[0,1]\\d[0-3]\\d \\d{6}");
	private static final SimpleDateFormat TYMDHMS_NH = new SimpleDateFormat("yyyyMMdd'T'HH:mm:ss");
	private static final Pattern STYMDHMS_NH = Pattern.compile("\\d{4}[0,1]\\d[0-3]\\dT\\d+:\\d+:\\d+");
	private static final SimpleDateFormat TYMDHMS_NHC = new SimpleDateFormat("yyyyMMdd'T'HHmmss");
	private static final Pattern STYMDHMS_NHC = Pattern.compile("\\d{4}[0,1]\\d[0-3]\\dT\\d{6}");
	private static final SimpleDateFormat TYMDHMS = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
	private static final Pattern STYMDHMS = Pattern.compile("\\d{4}-[0,1]\\d-[0-3]\\dT\\d+:\\d+:\\d+");
	public static Date parseDate(String dateStr) throws ParseException {
		Date d = null;
		if (SYMD.matcher(dateStr).matches()) {
			d = YMD.parse(dateStr);
		} else if (SYMDHMS.matcher(dateStr).matches()) {
			d = YMDHMS.parse(dateStr);
		} else if (SYMDHMS_NH.matcher(dateStr).matches()) {
			d = YMDHMS_NH.parse(dateStr);
		} else if (STYMDHMS.matcher(dateStr).matches()) {
			d = TYMDHMS.parse(dateStr);
		} else if (STYMDHMS_NH.matcher(dateStr).matches()) {
			d = TYMDHMS_NH.parse(dateStr);
		} else if (STYMDHMS_NHC.matcher(dateStr).matches()) {
			d = TYMDHMS_NHC.parse(dateStr);
		} else {
			System.out.println("不支持的格式：" + dateStr);
		}

		return d;
	}

	/**
	 * 获取处理结果
	 * <p>
	 * 此处对回执类型:INV201,INV202,做了处理,其他值返回null
	 *
	 * 海关回执类型
	 *
	 * BWS201: 物流账册回执
	 * CMB201: 耗料单审核回执
	 * INV201: 核注单审核回执
	 * INV202: 核注单生成报关单回执
	 * INV211: 核注单清单记账回执
	 * SAS201: 申报表海关回执
	 * SAS202: 申报表暂停恢复回执
	 * SAS203: 申报表数据同步回执
	 * SAS211: 出入库单审核回执
	 * SAS212: 出入库单修改回执
	 * SAS213: 出入库单数据时同步回执
	 * SAS221: 核放单审核回执
	 * SAS222: 核放单修改回执
	 * SAS223: 核放单过卡回执
	 * SAS224: 核放单查验处置回执
	 * SAS231: 车辆信息审核回执
	 * SAS241: 清单结关申请回执
	 * EML211: 加贸手册回执
	 *
	 * </p>
	 *
	 * @return 处理结果
	 */
	public String getManageResult() {
		switch (super.getEnvelopInfo().getMessageType()) {
		case "INV201":
			return super.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getManageResult().trim();
		case "INV202":
			return super.getDataInfo().getBussinessData().getMessageType().getInvApprResult().getManageResult().trim();
		case"SAS201":
		case"SAS211":
			return super.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getManageResult().trim();
		case"SAS221":
			return super.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getManageResult().trim();
		case"SAS223":
			return super.getDataInfo().getBussinessData().getMessageType().getInvApprResult().getManageResult().trim();
		case"EML211":
			return super.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getManageResult().trim();
		default:
			return null;
		}
	}

	/**
	 * 获取进出口标记代码(I:进口,E:出口)
	 *
	 * @return 进出口标记代码(I:进口,E:出口)
	 */
	public String getImpexpMarkcd() {
		return super.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getImpexpMarkcd().trim();
	}
	/**
	 * 获取监管方式
	 *
	 * @return
	 */
	public String getSupvModecd() {
		return super.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getSupvModecd().trim();
	}
	/**
	 * 获取企业内部编号
	 *
	 * @return
	 */
	public String getEtpsInnerInvtNo() {
		return super.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getEtpsInnerInvtNo().trim();
	}


	/**
	 * 获取是否报关标记(1.报关;2.非报关)
	 *
	 * @return 是否报关标记(1.报关;2.非报关)
	 */
	public String getDclcusFlag() {
		return super.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getDclcusFlag().trim();
	}

	/**
	 * 获取业务编号
	 *
	 * @return 业务编号
	 */
	public String getBusinessId() {
		return this.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getBusinessId().trim();
	}

	/**
	 * 获取清单申报日期
	 *
	 * @return 清单申报日期
	 */
	public Date getInvtDclTime() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getInvtDclTime();
	}

	/**
	 * 获取报关单申报时间
	 *
	 * @return 报关单申报时间
	 */
	public Date getEntryDclTime() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getEntryDclTime();
	}

	/**
	 * 获取核扣标记代码
	 *
	 * @return 核扣标记代码
	 */
	public String getVrfdedMarkcd() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getVrfdedMarkcd().trim();
	}
	/**
	 * 获取卡口状态
	 *
	 * @return 核扣标记代码
	 */
	public String getInvtIochkptStucd() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getInvtIochkptStucd().trim();
	}
	/**
	 * 获取备案编号
	 *
	 * @return 备案编号
	 */
	public String getPutrecNo() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getPutrecNo().trim();
	}

	/**
	 * 获取料件成品标记代码
	 *
	 * @return 备案编号
	 */
	public String getMtpckEndprdMarkcd() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getMtpckEndprdMarkcd().trim();
	}
	/**
	 * 获取保税清单编号
	 *
	 * @return 保税清单编号
	 */
	public String getBondInvtNo() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getBondInvtNo().trim();
	}

	/**
	 * 获取保税清单基本
	 *
	 * @return 保税清单基本
	 */
	public BondInvtBsc getBondInvtBsc() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc();
	}

	/**
	 * 获取保税清单明细集合
	 *
	 * @return 保税清单明细集合
	 */
	public List<BondInvtDt> getBondInvtDt() {

		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtDt();
	}


	/**
	 * 获取报关单统一编号
	 *
	 * @return 报关单统一编号
	 */
	public String getEntrySeqNo() {
		return this.getDataInfo().getBussinessData().getMessageType().getInvApprResult().getEntrySeqNo();
	}

	/**
	 * 获取核注清单数据中心统一编号
	 *
	 * @return 核注清单数据中心统一编号
	 */
	public String getInvPreentNo() {
		return this.getDataInfo().getBussinessData().getMessageType().getInvApprResult().getInvPreentNo();
	}

	/**
	 * 获取物流账册明细集合
	 */
	public List<BwsDt> getBwsDtList() {
		return this.getDataInfo().getBussinessData().getMessageType().getBwsDt();
	}

	/**
	 * 获取核注单数据经营企业名称
	 */
	public String getBizopEtpsNm() {
		return this.getDataInfo().getBussinessData().getMessageType().getBondInvtBsc().getBizopEtpsNm().trim();
	}

	/**
	 * SAS201 申报表 统一编号
	 * SAS211 出入库单 统一编号
	 * SAS221 核放单 统一编号
	 *
	 * @return
	 */
	public String getEtpsPreentNo() {
		return this.getDataInfo().getBussinessData().getMessageType().getHdeApprResult().getEtpsPreentNo().trim();
	}

	/**
	 * SAS201 申报表表头
	 *
	 * @return
	 */
	public SasDclBsc getSasDclBsc(){
		return this.getDataInfo().getBussinessData().getMessageType().getSasDclBsc();
	}
	/**
	 * SAS201 申报表表体
	 *
	 * @return
	 */
	public List<SasDclDt> getSasDclDt() {
		return this.getDataInfo().getBussinessData().getMessageType().getSasDclDt();
	}

	/**
	 * SAS211 出入库单表头
	 *
	 * @return
	 */
	public SasStockBsc getSasStockBsc() {
		return this.getDataInfo().getBussinessData().getMessageType().getSasStockBsc();
	}
	/**
	 * EML211手册表头
	 */
	public EmlPutrecBsc getEmlPutrecBsc() {
		return this.getDataInfo().getBussinessData().getMessageType().getEmlPutrecBsc();
	}

	/**
	 * SAS211 出入库单表体
	 *
	 * @return
	 */
	public List<SasStockDt> getSasStockDts() {
		return this.getDataInfo().getBussinessData().getMessageType().getSasStockDt();
	}

	/**
	 * SAS221 核放单表头
	 *
	 * @return
	 */
	public SasPassportBsc getSasPassportBsc() {
		return this.getDataInfo().getBussinessData().getMessageType().getSasPassportBsc();
	}

}
