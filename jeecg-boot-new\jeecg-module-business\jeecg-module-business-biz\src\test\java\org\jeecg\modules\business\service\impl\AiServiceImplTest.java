package org.jeecg.modules.business.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.business.entity.excel.ExportInvoiceExcel;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class AiServiceImplTest {

    @Test
    void exportInvoice() throws JsonProcessingException {
        String logContent = "{\"detail_ocr\":[{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":\"\\\"PROFORMA INVOICE\\\\t \\\\t \\\\t \\\\nShipper\\\\tFesto Didactic SE\\\\t \\\\t \\\\t \\\\t \\\\tP/L No.\\\\t \\\\nAddress\\\\tPostfach 100710 D-73707 Esslingen Internet www.festo-didactic.com Telephone: 0711/3467-0 Fax: 0711/34754-88500 E-mail <EMAIL> Rechbergstraße 3 D-73770 Denkendorf\\\\t \\\\t \\\\t \\\\t \\\\tDATE              \\\\t2025-06-03 00:00:00\\\\t \\\\nTEL:\\\\t0711/3467-0\\\\t \\\\t \\\\t \\\\t \\\\tTOTAL PAGE  \\\\t1\\\\t \\\\nInvoice No.:\\\\t2025-FK-202\\\\t \\\\t \\\\nIncoterms: \\\\tCPT SHANGHAI\\\\t \\\\t \\\\nConsignee\\\\tFESTO ( China ) Ltd.\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nAddress\\\\tNo. 1156 Yunqiao Road. Jin Qiao  201206 Pudong, Shanghai, P.R.China\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nNick Huang\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\n+86(21)6061-5771\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nCONTAINER No.:\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nPart. No.\\\\tDESCRIPTION\\\\t \\\\tHS CODE\\\\tCountry of orgin\\\\tQUANTITY\\\\tUNIT PRICE\\\\tAMOUNT(EUR)\\\\t \\\\n8083539\\\\tMulti-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\\\\t \\\\t7307990000\\\\tKR\\\\t4\\\\t545.84\\\\t2183.36\\\\t \\\\nEUR\\\\t2183.36\\\\t \\\\t \\\\n\\\\nPACKING LIST\\\\t \\\\t \\\\t \\\\t \\\\tP/L No.\\\\t \\\\t \\\\nShipper\\\\tFesto Didactic SE\\\\t \\\\t \\\\t \\\\t \\\\tDATE              \\\\t2025-06-03 00:00:00\\\\t \\\\nAddress\\\\tPostfach 100710 D-73707 Esslingen Internet www.festo-didactic.com Telephone: 0711/3467-0 Fax: 0711/34754-88500 E-mail <EMAIL> Rechbergstraße 3 D-73770 Denkendorf\\\\t \\\\t \\\\t \\\\t \\\\tTOTAL PAGE  \\\\t1\\\\t \\\\nTEL:\\\\t0711/3467-0\\\\t \\\\t \\\\t \\\\t \\\\tInvoice No.:\\\\t2025-FK-202\\\\t \\\\nIncoterms: \\\\tCPT SHANGHAI\\\\t \\\\nConsignee\\\\tFESTO ( China ) Ltd.\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nAddress\\\\tNo. 1156 Yunqiao Road. Jin Qiao  201206 Pudong, Shanghai, P.R.China\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nNick Huang\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\n+86(21)6061-5771\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nCONTAINER No.:\\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\t \\\\nPart. No.\\\\tDESCRIPTION\\\\t \\\\tHS CODE\\\\tQUANTITY\\\\tPACKAGES  \\\\tN/W (KGS)\\\\tG/W (KGS)\\\\t \\\\n8083539\\\\tMulti-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\\\\t \\\\t7307990000\\\\t4\\\\t1\\\\t80.00\\\\t83.00\\\\t \\\\nTOTAL NET\\\\t \\\\t \\\\t:\\\\t80.000\\\\tKGS\\\\t \\\\t \\\\t \\\\nTOTAL GROSS\\\\t \\\\t \\\\t:\\\\t83.00\\\\tKGS\\\\t \\\\t \\\\t \\\\nTOTAL VOLUME\\\\t \\\\t \\\\t:\\\\t1.200\\\\tm3\\\\t \\\\t \\\\t \\\\nTOTALQUANTITY\\\\t \\\\t \\\\t:\\\\t4\\\\tPCS\\\\t \\\\t \\\\t \\\\n\\\\n物料号\\\\tHS\\\\t中文品名\\\\t申报要素\\\\t原产国\\\\t单价\\\\t币制\\\\t数量\\\\n8083539\\\\t8412310090999\\\\t直线作用气缸\\\\t4|3|由压缩空气驱动气缸体内的活塞，并带动所连接的活塞杆作直线往复运动|工业自动化气动控制系统|费斯托FESTO\\\\t韩国\\\\t545.84\\\\tEUR\\\\t20\\\"\"}],\"detail_format\":[{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":{\"境内收货人\":\"FESTO ( China ) Ltd.\",\"境外发货人\":\"Festo Didactic SE\",\"发票号\":\"2025-FK-202\",\"发票日期\":\"2025-06-03\",\"运抵国\":\"中国\",\"成交方式\":\"CPT\",\"商品信息\":[{\"物料号\":\"8083539\",\"商品名称\":\"Multi-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\",\"商品编号\":\"7307990000\",\"规格型号\":\"\",\"数量及单位\":\"4\",\"单价\":\"545.84\",\"总价\":\"2183.36\",\"币制\":\"EUR\",\"境内目的地\":\"\",\"申报要素\":\"\",\"品牌\":\"\",\"原产国\":\"韩国\"}],\"单据类型\":\"进口发票\"}},{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":{\"境内收货人\":\"FESTO ( China ) Ltd.\",\"境外发货人\":\"Festo Didactic SE\",\"总件数\":\"4\",\"总净重\":\"80.00KGS\",\"总毛重\":\"83.00KGS\",\"商品信息\":[{\"物料号\":\"8083539\",\"商品名称\":\"Multi-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\",\"商品编号\":\"7307990000\",\"规格型号\":\"\",\"数量及单位\":\"4件\",\"净重\":\"80.00KGS\",\"毛重\":\"83.00KGS\",\"品牌\":\"\",\"原产国\":\"\"}],\"单据类型\":\"进口箱单\"}},{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":{\"历史申报记录\":[{\"物料号\":\"8083539\",\"商品名称\":\"直线作用气缸\",\"商品编号\":\"8412310090999\",\"申报要素\":\"4|3|由压缩空气驱动气缸体内的活塞，并带动所连接的活塞杆作直线往复运动|工业自动化气动控制系统|费斯托FESTO\",\"品牌\":\"费斯托FESTO\",\"原产国\":\"韩国\",\"单价\":\"545.84\",\"币制\":\"EUR\"}],\"单据类型\":\"历史申报记录\"}}],\"result\":{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":{\"境内收货人\":\"FESTO ( China ) Ltd.\",\"境外发货人\":\"Festo Didactic SE\",\"发票号\":\"2025-FK-202\",\"发票日期\":\"2025-06-03\",\"运抵国\":\"中国\",\"成交方式\":\"CPT\",\"商品信息\":[{\"物料号\":\"8083539\",\"商品名称\":\"Multi-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\",\"商品编号\":\"7307990000\",\"数量及单位\":\"4\",\"单价\":\"545.84\",\"总价\":\"2183.36\",\"币制\":\"EUR\",\"原产国\":\"韩国\",\"净重\":\"80.00KGS\",\"毛重\":\"83.00KGS\",\"品牌\":\"无品牌\",\"原产国代码\":\"KOR\"}],\"单据类型\":\"进口发票\",\"总件数\":\"4\",\"总净重\":\"80.00\",\"总毛重\":\"83.00\",\"历史申报记录\":[{\"物料号\":\"8083539\",\"商品名称\":\"直线作用气缸\",\"商品编号\":\"8412310090999\",\"申报要素\":\"4|3|由压缩空气驱动气缸体内的活塞，并带动所连接的活塞杆作直线往复运动|工业自动化气动控制系统|费斯托FESTO\",\"品牌\":\"费斯托FESTO\",\"原产国\":\"韩国\",\"单价\":\"545.84\",\"币制\":\"EUR\"}],\"运抵国代码\":\"CHN\",\"指运港\":\"中国\",\"指运港代码\":\"CHN000\",\"监管方式\":\"一般贸易\",\"监管方式代码\":\"0110\",\"包装种类\":\"纸制或纤维板制盒/箱\",\"包装种类代码\":\"22\",\"验证结果\":[]}},\"result_logic\":{\"file_name\":\"2025 broker bidding-上海进口考题样本1.xlsx_1750761308718_8580468375531020492.xlsx\",\"content\":{\"境内收货人\":\"FESTO ( China ) Ltd.\",\"境外发货人\":\"Festo Didactic SE\",\"发票号\":\"2025-FK-202\",\"发票日期\":\"2025-06-03\",\"运抵国\":\"中国\",\"成交方式\":\"CPT\",\"商品信息\":[{\"物料号\":\"8083539\",\"商品名称\":\"Multi-pos. cyl. DNCP-100-PPV-A-70Z1-310Z2\",\"商品编号\":\"7307990000\",\"数量及单位\":\"4\",\"单价\":\"545.84\",\"总价\":\"2183.36\",\"币制\":\"EUR\",\"原产国\":\"韩国\",\"净重\":\"80.00KGS\",\"毛重\":\"83.00KGS\",\"品牌\":\"无品牌\",\"原产国代码\":\"KOR\"}],\"单据类型\":\"进口发票\",\"总件数\":\"4\",\"总净重\":\"80.00\",\"总毛重\":\"83.00\",\"历史申报记录\":[{\"物料号\":\"8083539\",\"商品名称\":\"直线作用气缸\",\"商品编号\":\"8412310090999\",\"申报要素\":\"4|3|由压缩空气驱动气缸体内的活塞，并带动所连接的活塞杆作直线往复运动|工业自动化气动控制系统|费斯托FESTO\",\"品牌\":\"费斯托FESTO\",\"原产国\":\"韩国\",\"单价\":\"545.84\",\"币制\":\"EUR\"}],\"运抵国代码\":\"CHN\",\"指运港\":\"中国\",\"指运港代码\":\"CHN000\",\"监管方式\":\"一般贸易\",\"监管方式代码\":\"0110\",\"包装种类\":\"纸制或纤维板制盒/箱\",\"包装种类代码\":\"22\"}}}";
        // TODO ..
        // ========== 开始解析和提取发票数据 ==========
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> rootMap = objectMapper.readValue(logContent, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> detailFormatList = (List<Map<String, Object>>) rootMap.get("detail_format");
        List<Map<String, Object>> invoiceDataList = new ArrayList<>();
        List<Map<String, Object>> packingListDataList = new ArrayList<>();
        if (detailFormatList != null) {
            for (Map<String, Object> document : detailFormatList) {
                Map<String, Object> content = (Map<String, Object>) document.get("content");
                if (content != null) {
                    String docType = (String) content.get("单据类型");
                    // 判断是否为 "出口发票" 或 "进口发票"
                    if ("出口发票".equals(docType) || "进口发票".equals(docType)) {
                        invoiceDataList.add(document);
                    }
                    // 提取箱单数据
                    if ("出口箱单".equals(docType) || "进口箱单".equals(docType)) {
                        packingListDataList.add(document);
                    }
                }
            }
        }
        if (invoiceDataList.isEmpty()) {
            log.info("大模型返回的数据中未找到发票信息，无法导出");
        }
        log.info("【exportInvoice】大模型返回的数据中有{}个发票数据：{}", invoiceDataList.size(), invoiceDataList);
        // ======================= 开始数据映射到ExportInvoiceExcel =======================
        // 创建物料号到重量的映射关系
        Map<String, Map<String, String>> materialWeightMap = new HashMap<>();
        for (Map<String, Object> packingDocument : packingListDataList) {
            Map<String, Object> content = (Map<String, Object>) packingDocument.get("content");
            if (content != null) {
                List<Map<String, Object>> goodsList = (List<Map<String, Object>>) content.get("商品信息");
                if (goodsList != null) {
                    for (Map<String, Object> goods : goodsList) {
                        String materialNo = (String) goods.get("物料号");
                        String netWeight = (String) goods.get("净重");
                        String grossWeight = (String) goods.get("毛重");
                        
                        Map<String, String> weightInfo = new HashMap<>();
                        weightInfo.put("净重", netWeight);
                        weightInfo.put("毛重", grossWeight);
                        materialWeightMap.put(materialNo, weightInfo);
                    }
                }
            }
        }
        // 最终要生成的Excel数据列表
        List<ExportInvoiceExcel> exportList = new ArrayList<>();
        // 正则表达式用于拆分 "数量及单位" (例如 "3720件" -> "3720", "件") 支持整数和浮点数
        java.util.regex.Pattern qtyPattern = java.util.regex.Pattern.compile("([\\d.]+)(.*)");
        for (Map<String, Object> invoiceDocument : invoiceDataList) {
            Map<String, Object> content = (Map<String, Object>) invoiceDocument.get("content");
            if (content == null) {
                continue;
            }
            String invoiceNo = (String) content.get("发票号");
            String invoiceDate = (String) content.get("日期");
            String billOfLadingNo = (String) content.get("提运单号");
            String originCountry = (String) content.get("原产国");
            String docType = (String) content.get("单据类型");
            List<Map<String, Object>> goodsList = (List<Map<String, Object>>) content.get("商品信息");
            if (goodsList == null || goodsList.isEmpty()) {
                continue;
            }
            for (Map<String, Object> goodsItem : goodsList) {
                ExportInvoiceExcel excelRow = new ExportInvoiceExcel();
                excelRow.setGoodsNo(invoiceNo); // 发票号
                excelRow.setGoodsName(invoiceDate); // 日期
                excelRow.setWaybillNo(billOfLadingNo); // 装运号 (提运单号)
                excelRow.setCountryOfOrigin(originCountry); // 原产国
                excelRow.setType(docType); // 类型 (单据类型)
                excelRow.setCopGno((String) goodsItem.get("物料号")); // 物料号
                excelRow.setPn((String) goodsItem.get("商品名称")); // 品名
                excelRow.setAmount(String.valueOf(goodsItem.get("总价"))); // 金额
                excelRow.setCurrency((String) goodsItem.get("币制")); // 币种
                // 拆分 "数量及单位"
                String qtyAndUnit = (String) goodsItem.get("数量及单位");
                if (qtyAndUnit != null && !qtyAndUnit.isEmpty()) {
                    java.util.regex.Matcher matcher = qtyPattern.matcher(qtyAndUnit.trim());
                    if (matcher.matches()) {
                        excelRow.setQty(matcher.group(1));  // 数量
                        excelRow.setQunit(matcher.group(2).trim()); // 单位
                    } else {
                        excelRow.setQty(qtyAndUnit);
                    }
                }
                // 处理重量
                String materialNo = (String) goodsItem.get("物料号");
                // 从箱单数据中获取重量信息
                String weight = "";
                if (materialWeightMap.containsKey(materialNo)) {
                    Map<String, String> weightInfo = materialWeightMap.get(materialNo);
                    String netWeight = weightInfo.get("净重");
                    String grossWeight = weightInfo.get("毛重");
                    // 优先使用净重，如果没有则使用毛重
                    if (netWeight != null && !netWeight.trim().isEmpty()) {
                        weight = netWeight;
                    } else if (grossWeight != null && !grossWeight.trim().isEmpty()) {
                        weight = grossWeight;
                    }
                    // 清理重量数据格式（去除单位等）
                    if (!weight.isEmpty()) {
                        weight = weight.replaceAll("[^\\d.]", ""); // 只保留数字和小数点
                    }
                }
                excelRow.setWeight(weight);
                
                exportList.add(excelRow);
            }
        }
    }
}