package org.jeecg.modules.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.EdiReceipt;
import org.jeecg.modules.business.entity.receipt.DTO.Resp;
import org.jeecg.modules.business.mapper.EdiReceiptMapper;
import org.jeecg.modules.business.service.IEdiReceiptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * <p>
 * 回执 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-16
 */
@Slf4j
@Service
public class EdiReceiptServiceImpl extends ServiceImpl<EdiReceiptMapper, EdiReceipt> implements IEdiReceiptService {

    /**
     * EDI状态历史相关服务
     *
     * @param id
     * @param seqNo
     * @return
     */
    @Override
    public Result<?> getEdiReceiptBySeq(String id, String seqNo) {
        LambdaQueryWrapper<EdiReceipt> queryWrapper = new LambdaQueryWrapper<EdiReceipt>();
        queryWrapper.and(i->i.and(a->a.eq(isNotBlank(id),EdiReceipt::getRelatedId,id)
                .or().eq(isNotBlank(seqNo),EdiReceipt::getSeqNo, seqNo)
                .notIn(isNotBlank(seqNo),EdiReceipt::getSeqNo, "")))
                .orderByAsc(EdiReceipt::getMessageId).orderByAsc(EdiReceipt::getSendTime)
                .orderByDesc(EdiReceipt::getReceiverTime).groupBy(EdiReceipt::getId);
        List<EdiReceipt> ediReceiptList = this.list(queryWrapper);
        // ZHANGCHAO@2021/7/8 16:48 追加/变更/完善：返回channel！！
        if (isNotEmpty(ediReceiptList)) {
            for (EdiReceipt ediReceipt : ediReceiptList) {
                if (isNotBlank(ediReceipt.getReceiptJson())) {
                    // 报关单
                    try {
                        if (ediReceipt.getReceiptJson().contains("CHANNEL")) {
                            if (ediReceipt.getReceiptJson().contains("DEC_DATA")
                                    && ediReceipt.getReceiptJson().contains("DEC_RESULT")) {
                                JSONObject jo = JSON.parseObject(ediReceipt.getReceiptJson()).getJSONObject("DEC_DATA").getJSONObject("DEC_RESULT");
                                if (isNotEmpty(jo)) {
                                    ediReceipt.setChannel(jo.get("CHANNEL") instanceof String ? (String) jo.get("CHANNEL") : String.valueOf(jo.get("CHANNEL")));
                                }
                            } else if (ediReceipt.getReceiptJson().contains("DEC_RESULT")) {
                                JSONObject jo = JSON.parseObject(ediReceipt.getReceiptJson()).getJSONObject("DEC_RESULT");
                                if (isNotEmpty(jo)) {
                                    ediReceipt.setChannel(jo.get("CHANNEL") instanceof String ? (String) jo.get("CHANNEL") : String.valueOf(jo.get("CHANNEL")));
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("[getEdiReceiptBySeq]取channel时出现异常！" + e);
                    }
                    // ZHANGCHAO@2021/7/15 10:04 追加/变更/完善：乱七八糟！！！各种情况，太乱了这状态！！！
                    if (isBlank(ediReceipt.getChannel())) {
                        if (ediReceipt.getReceiptJson().contains("暂存")) {
                            ediReceipt.setChannel("暂存");
                        }
                        if (ediReceipt.getReceiptJson().contains("直接申报")) {
                            ediReceipt.setChannel("已申报");
                        }
                        if (ediReceipt.getReceiptJson().contains("CHANNEL\":7")) {
                            ediReceipt.setChannel("已申报");
                        }
                        if (ediReceipt.getReceiptJson().contains("CHANNEL\":\"J") || ediReceipt.getReceiptJson().contains("CHANNEL\":\"G")) {
                            ediReceipt.setChannel("已审结");
                        }
                        if (ediReceipt.getReceiptJson().contains("CHANNEL\":\"K")
                                || ediReceipt.getReceiptJson().contains("CHANNEL\":\"P")
                                || ediReceipt.getReceiptJson().contains("CHANNEL\":\"F")) {
                            ediReceipt.setChannel("已放行");
                        }
                        if (ediReceipt.getReceiptJson().contains("CHANNEL\":\"R")) {
                            ediReceipt.setChannel("已结关");
                        }
                        if (ediReceipt.getReceiptJson().contains("CHANNEL\":\"L")) {
                            ediReceipt.setChannel("入库");
                        }

                    } else {
                        if ("P".equalsIgnoreCase(ediReceipt.getChannel())
                                || "K".equalsIgnoreCase(ediReceipt.getChannel())
                                || "F".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已放行");
                        }
                        if ("2".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已申报");
                        }
                        if ("L".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("入库"); // 原来L是2已申报  现在入库成功！
                        }
                        if ("7".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已申报");
                        }
                        if ("C".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("查验");
                        }
                        if ("J".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已审结");
                        }
                        if ("G".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已审结");
                        }
                        if ("R".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("已结关");
                        }
                        if ("E".equalsIgnoreCase(ediReceipt.getChannel())) {
                            ediReceipt.setChannel("退单");
                        }
                    }
                    // 核注单
                    if (ediReceipt.getReceiptJson().contains("CommonResponeMessage")
                            && ediReceipt.getReceiptJson().contains("调用")) {
                        ediReceipt.setChannel("调用");
                    }
                    if (ediReceipt.getReceiptJson().contains("ImportResponse")
                            && ediReceipt.getReceiptJson().contains("导入成功")) {
                        ediReceipt.setChannel("暂存");
                    }
                    if (ediReceipt.getReceiptJson().contains("INV201")
                            && ediReceipt.getReceiptJson().contains("入库")) {
                        ediReceipt.setChannel("入库");
                    }
                    if (ediReceipt.getReceiptJson().contains("INV201")
                            && ediReceipt.getReceiptJson().contains("manageResult\":4")
                            && ediReceipt.getReceiptJson().contains("vrfdedMarkcd\":1")) {
                        ediReceipt.setChannel("预核扣");
                    }
                    if (ediReceipt.getReceiptJson().contains("INV201")
                            && ediReceipt.getReceiptJson().contains("manageResult\":1")
                            && ediReceipt.getReceiptJson().contains("vrfdedMarkcd\":2")) {
                        ediReceipt.setChannel("通过（已核扣）");
                    }
                    if (ediReceipt.getReceiptJson().contains("INV202")
                            && ediReceipt.getReceiptJson().contains("manageResult\":1")) {
                        ediReceipt.setChannel("生成报关单");
                    }
                    if (ediReceipt.getReceiptJson().contains("INV211")) {
                        ediReceipt.setChannel("清单记账");
                    }
                    /*
                     * 出入库单、业务申报表、核放单、修撤单、减免税增加回执系统处理信息查看的功能
                     * 2023/1/28 15:35@ZHANGCHAO
                     */
                    if (isBlank(ediReceipt.getChannel())) {
                        if (ediReceipt.getReceiptJson().contains("SAS211")
                                && ediReceipt.getReceiptJson().contains("报文入库成功")) {
                            ediReceipt.setChannel("报文入库");
                        }
                        if (ediReceipt.getReceiptJson().contains("SAS211")
                                && ediReceipt.getReceiptJson().contains("电子审通过")) {
                            ediReceipt.setChannel("海关终审通过");
                        }
                        if (ediReceipt.getReceiptJson().contains("海关操作成功")) {
                            ediReceipt.setChannel("海关操作");
                        }
                    }
                    // 2023/2/3 8:40@ZHANGCHAO 追加/变更/完善：
                    if (isBlank(ediReceipt.getChannel())) {
                        if (ediReceipt.getReceiptJson().contains("manageResult\": \"Y") || ediReceipt.getReceiptJson().contains("manageResult\":\"Y")) {
                            ediReceipt.setChannel("入库成功");
                        }
                        if (ediReceipt.getReceiptJson().contains("manageResult\": 1") || ediReceipt.getReceiptJson().contains("manageResult\":1")) {
                            ediReceipt.setChannel("海关终审通过");
                        }
                        if (ediReceipt.getReceiptJson().contains("manageResult\": 2") || ediReceipt.getReceiptJson().contains("manageResult\":2")) {
                            ediReceipt.setChannel("转人工");
                        }
                        if (ediReceipt.getReceiptJson().contains("manageResult\": 3") || ediReceipt.getReceiptJson().contains("manageResult\":3")) {
                            ediReceipt.setChannel("退单");
                        }
                        if (ediReceipt.getReceiptJson().contains("manageResult\": \"Z") || ediReceipt.getReceiptJson().contains("manageResult\":\"Z")) {
                            ediReceipt.setChannel("入库失败");
                        }
                        // 核放单
                        if (ediReceipt.getReceiptJson().contains("SAS221")
                                && ediReceipt.getReceiptJson().contains("报文入库成功")) {
                            ediReceipt.setChannel("入库成功");
                        }
                        if (ediReceipt.getReceiptJson().contains("SAS221")
                                && ediReceipt.getReceiptJson().contains("manageResult\": 1")) {
                            ediReceipt.setChannel("海关终审通过");
                        }
                        if (ediReceipt.getReceiptJson().contains("SAS221")
                                && ediReceipt.getReceiptJson().contains("manageResult\": 2")) {
                            ediReceipt.setChannel("转人工");
                        }
                        if (ediReceipt.getReceiptJson().contains("SAS221")
                                && ediReceipt.getReceiptJson().contains("manageResult\": 3")) {
                            ediReceipt.setChannel("退单");
                        }
                        if (ediReceipt.getReceiptJson().contains("SAS221")
                                && (ediReceipt.getReceiptJson().contains("manageResult\": \"Z") || ediReceipt.getReceiptJson().contains("manageResult\":\"Z"))) {
                            ediReceipt.setChannel("入库失败");
                        }
                    }
                    if (isBlank(ediReceipt.getChannel())) {
                        if (ediReceipt.getReceiptJson().contains("FeedbackResults\":\"L") || ediReceipt.getReceiptJson().contains("FeedbackResults\": \"L")) {
                            ediReceipt.setChannel("海关接收入库成功");
                        }
                        if (ediReceipt.getReceiptJson().contains("FeedbackResults\":\"A") || ediReceipt.getReceiptJson().contains("FeedbackResults\": \"A")) {
                            ediReceipt.setChannel("海关受理");
                        }
                        if (ediReceipt.getReceiptJson().contains("FeedbackResults\":\"S") || ediReceipt.getReceiptJson().contains("FeedbackResults\": \"S")) {
                            ediReceipt.setChannel("海关操作成功");
                        }
                    }
                }
                if (isNotBlank(ediReceipt.getDealResult())) {
                    List<Resp> respList;
                    try {
                        respList = JSON.parseArray(ediReceipt.getDealResult(), Resp.class);
                    } catch (Exception e) {
                        log.error("不能转JSON=> " + ediReceipt.getDealResult() + " => " + e);
                        Resp resp = new Resp();
                        resp.setMsg(ediReceipt.getDealResult());
                        respList = new ArrayList<>(16);
                        respList.add(resp);
                    }
                    ediReceipt.setRespList(respList);
                }
            }
        }
        return Result.ok(ediReceiptList);
    }
}
