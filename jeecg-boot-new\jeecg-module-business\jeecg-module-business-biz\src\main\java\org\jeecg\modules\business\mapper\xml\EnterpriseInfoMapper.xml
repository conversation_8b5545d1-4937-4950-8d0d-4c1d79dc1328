<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.EnterpriseInfoMapper">
    <!--  -->
    <select id="getCollectionEnterpriseList" parameterType="org.jeecg.modules.business.entity.EnterpriseInfo"
            resultType="org.jeecg.modules.business.entity.EnterpriseInfo">

        SELECT
        e.id,
        e.enterprise_full_name,
        e.english_name,
        e.registered_address,
        e.registered_address_detail,
        e.unified_social_credit_code,
        e.enterprise_legal_person,
        e.cotact_ch_name,
        e.cotact_en_name,
        e.customs_declaration_code,
        e.sditds_userloginname,
        e.sditds_userpassowrd,
        e.private_key_certificate,
        e.message_transmission_number,
        e.web_site,
        e.english_adress,
        e.compay_telphone,
        e.compay_fax,
        e.suppliers_email,
        e.compay_picture,
        e.company_stamp,
        e.contract_stamp,
        e.document_stamp,
        e.purchasing_stamp,
        e.financial_stamp,
        e.del_flag,
        e.remarks,
        e.update_time,
               e.PROCUREMENT_PLATFORM_ACCOUNT,
               e.PROCUREMENT_PLATFORM_PASSWORD,
        e.PACK_LIST_TEMPL,
        e.INVOICE_TEMPL,
        e.SWID,
        e.SALESCONTRACT_TEMPL
        FROM
        enterprise_info e

        <where>
            e.del_flag = 0
            and e.tenant_id = #{tenantId,jdbcType=INTEGER}
            <!--            and b.info_code = #{infoCode,jdbcType=VARCHAR}-->

        </where>
    </select>

    <select id="getTenantNameById" parameterType="int" resultType="String">
        SELECT "NAME" AS tenantName FROM sys_tenant WHERE id = #{tenantId,jdbcType=INTEGER}
    </select>

    <update id="updateTenantName">
        UPDATE
        sys_tenant
        SET
        NAME = #{name,jdbcType=VARCHAR}
        <where>
            id = #{id,jdbcType=INTEGER}
        </where>
    </update>
</mapper>
