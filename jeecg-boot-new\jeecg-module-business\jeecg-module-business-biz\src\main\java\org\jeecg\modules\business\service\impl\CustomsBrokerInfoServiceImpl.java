package org.jeecg.modules.business.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.SysUser;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.CustomsBrokerInfo;
import org.jeecg.modules.business.mapper.CustomsBrokerInfoMapper;
import org.jeecg.modules.business.service.ICustomsBrokerInfoService;
import org.jeecg.modules.business.service.ISerialNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * @Description: 报关行信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-18
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomsBrokerInfoServiceImpl extends ServiceImpl<CustomsBrokerInfoMapper, CustomsBrokerInfo> implements ICustomsBrokerInfoService {
    @Autowired
    private ISerialNumberService serialNumberService;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;

    /**
     * 自动创建报关行用户且绑定角色
     * @param customsBrokerInfo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCustomsBrokerUserAndRole(CustomsBrokerInfo customsBrokerInfo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("realname",customsBrokerInfo.getCustomsBrokerName() + "_AUTO");
        jsonObject.put("username", serialNumberService.getSerialnumberByCustomerCode("bgh_user_", 2));
        jsonObject.put("password", "123456");
        jsonObject.put("relTenantIds", TenantContext.getTenant());
        jsonObject.put("tenantId", TenantContext.getTenant());
        jsonObject.put("selectedroles", "1801046544255541249"); // 默认报关行角色
        Result<SysUser> result = sysBaseApi.addUser(jsonObject);
        System.out.println(result);
        if (isNotEmpty(result.getResult())) {
            SysUser sysUser = result.getResult();
            baseMapper.update(null, new UpdateWrapper<CustomsBrokerInfo>().lambda()
                    .set(CustomsBrokerInfo::getAutoAddUserId, sysUser.getId())
                    .eq(CustomsBrokerInfo::getId, customsBrokerInfo.getId()));
        }
    }

    /**
     * 删除报关行用户且绑定角色
     * @param customsBrokerInfo
     */
    @Override
    public void delCustomsBrokerUserAndRole(CustomsBrokerInfo customsBrokerInfo) {
        try {
            Result result = sysBaseApi.deleteRecycleBin(customsBrokerInfo.getAutoAddUserId());
            System.out.println(result);
        } catch (Exception e) {
            log.error("删除报关行用户失败", e);
        }
    }

    /**
     * @param customsBrokerInfo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCustomsBrokerUserAndRole(CustomsBrokerInfo customsBrokerInfo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("newRealname",customsBrokerInfo.getCustomsBrokerName() + "_AUTO");
        jsonObject.put("userId", customsBrokerInfo.getAutoAddUserId());
        Result<SysUser> result = sysBaseApi.updateUser(jsonObject);
        System.out.println(result);
    }

    public static void main(String[] args) {
        String passwordEncode = PasswordUtil.encrypt("jmkcw", "Jmk123456+", "XYcFNyg0");
        System.out.println(passwordEncode);
    }
}
