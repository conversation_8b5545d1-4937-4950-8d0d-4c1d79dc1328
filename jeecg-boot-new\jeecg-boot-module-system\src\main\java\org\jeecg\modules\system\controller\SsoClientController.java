package org.jeecg.modules.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.CustomsBrokerInfo;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.entity.EnterpriseInfo;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.mapper.EnterpriseInfoMapper;
import org.jeecg.modules.system.mapper.SysTenantMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.mapper.SysUserRoleMapper;
import org.jeecg.modules.system.service.IEnterpriseInfoService;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysBaseApiImpl;
import org.jeecg.modules.system.util.AjaxJson;
import org.jeecg.modules.system.util.MyHttpSessionHolder;
import org.jeecg.modules.system.util.SsoRequestUtil;
import org.jeecg.modules.system.util.exception.ExceptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * SSO Client端 Controller
 */
@RestController
@RequestMapping("/sys")
@Slf4j
public class SsoClientController {

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysTenantService sysTenantService;
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    private SysBaseApiImpl sysBaseApi;
    @Autowired
    private SysTenantMapper sysTenantMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private RedisUtil redisUtil;

    /*TODO:
       1. 门户注册后，用户同步到平台sys_user
       2. 注销
       3. 一些异常处理（用户在平台端不存在）
    */

    // SSO-Client端：单点登录地址
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/sso/login")
    public Object ssoLogin(String ticket, @RequestParam("currUrl") String currUrl, @RequestParam(defaultValue = "/") String back,
                           HttpServletRequest request, HttpServletResponse response, HttpSession session) throws IOException {
        Result<JSONObject> result = new Result<>();

        // 如果已经登录，则直接返回
        if(session.getAttribute("userId") != null) {
            log.info("【/sso/login】已经登录，直接返回");
            response.sendRedirect(back);
            return null;
        }

        /*
         * 此时有两种情况:
         * 情况1：ticket无值，说明此请求是Client端访问，需要重定向至SSO认证中心
         * 情况2：ticket有值，说明此请求从SSO认证中心重定向而来，需要根据ticket进行登录
         */
        if (ticket == null) {
            String clientLoginUrl = currUrl + "?back=" + SsoRequestUtil.encodeUrl(back);
            String serverAuthUrl = SsoRequestUtil.getAuthUrl() + "?redirect=" + clientLoginUrl;
//            response.sendRedirect(serverAuthUrl);
            log.info("ticket is null:", serverAuthUrl);
            JSONObject obj = new JSONObject();
            obj.put("serverAuthUrl", serverAuthUrl);
            result.setResult(obj);
            result.success("ticket无值，说明此请求是Client端访问，需要重定向至SSO认证中心");
        } else {
            // 获取当前 client 端的单点注销回调地址
            String ssoLogoutCall = "";
            if (SsoRequestUtil.isSlo()) {
//                ssoLogoutCall = request.getRequestURL().toString().replace("/sso/login", "/sso/logoutCall");
                log.info("request.getRequestURL():{}", request.getRequestURL().toString());
                log.info("request.getRequestURI():{}", request.getRequestURI());
                ssoLogoutCall = currUrl.replace("/sso/login", "/sso/logoutCall");
                if (ssoLogoutCall.contains("/sys")) {
                    ssoLogoutCall = ssoLogoutCall.replace("/sys", "/jgsoft/sys");
                }
                log.info("返回给sso-server的单点注销回调地址是ssoLogoutCall:{}", ssoLogoutCall);
            }

            // 校验 ticket
            String timestamp = String.valueOf(System.currentTimeMillis());    // 时间戳
            String nonce = SsoRequestUtil.getRandomString(20);        // 随机字符串
            String sign = SsoRequestUtil.getSignByTicket(ticket, ssoLogoutCall, timestamp, nonce);    // 参数签名
            String checkUrl = SsoRequestUtil.getCheckTicketUrl() +
                    "?timestamp=" + timestamp +
                    "&nonce=" + nonce +
                    "&sign=" + sign +
                    "&ticket=" + ticket +
//                    "&client=" + SsoRequestUtil.getClient() +    // 添加client参数
                    "&ssoLogoutCall=" + ssoLogoutCall;
            log.info("check ticket url:{}", checkUrl);
            AjaxJson res = SsoRequestUtil.request(checkUrl);
            log.info("check ticket result:{}", res);

            if (res.getCode() == 200 && !SsoRequestUtil.isEmpty(res.getData())) {
                Object userId = res.getData();
//                session.setAttribute("userId", userId);
                // 设置忽略租户插件
                InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                // 校验用户是否有效
                SysUser sysUser = sysUserService.getById(String.valueOf(userId));
                result = sysUserService.checkUserIsEffective(sysUser);
                // 关闭忽略策略
                InterceptorIgnoreHelper.clearIgnoreStrategy();
                // TODO: 两种情况 1.有企业信息 2.没有企业信息
                if (!result.isSuccess()) {
                    log.info("user is not effective: {}", result);
                    log.info("user is not effective: {}", result.getMessage());
                    if (isNotBlank(result.getMessage()) && !result.getMessage().contains("注册")) {
                        return new HttpEntity<>(result.getMessage() + "|" + userId);
                    }
                    /*********************************************************/
                    // 从SSO获取企业信息
                    String timestamp_ = String.valueOf(System.currentTimeMillis());    // 时间戳
                    String nonce_ = SsoRequestUtil.getRandomString(20);        // 随机字符串
                    String sign_ = SsoRequestUtil.getSign(userId, timestamp_, nonce_);    // 参数签名

                    String urlData = SsoRequestUtil.getGetDataUrl() +
                            "?loginId=" + userId +
//                            "&apiType=userInfo" +
//                            "&client=" + SsoRequestUtil.getClient() +    // 添加client参数
                            "&timestamp=" + timestamp_ +
                            "&nonce=" + nonce_ +
                            "&sign=" + sign_;
                    log.info("get userInfo url: {}", urlData);
                    AjaxJson userInfoResult = SsoRequestUtil.request(urlData);
                    log.info("get userInfo data: {}", userInfoResult);
                    String entCodeScc;
                    String enterpriseName;
                    String phone;
                    String username;
                    String password;
                    String salt;
                    if (isNotEmpty(userInfoResult) && userInfoResult.getCode() == 200) {
                        // TODO 获取统一社会信用代码，SSO暂时还未提供！！！
                        try {
                            JSONObject data = (JSONObject) userInfoResult.getData();
                            enterpriseName = (String) data.get("businessName"); // 企业名称
                            entCodeScc = (String) data.get("creditCode"); // 统一社会信用代码
                            phone = (String) data.get("phone"); // 电话作为账号
                            username = (String) data.get("username"); // 电话作为账号
                            password = (String) data.get("password");
                            salt = (String) data.get("salt");
                        } catch (Exception e) {
                            log.error("从认证中心获取数据出现异常：{}", e.getMessage());
                            return new HttpEntity<>("从认证中心获取用户[loginId:" + userId + "]数据失败！");
                        }
                    } else {
                        return new HttpEntity<>("从认证中心获取用户[loginId:" + userId + "]数据失败！");
                    }
                    /*********************************************************/
                    // 设置忽略租户插件
                    InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                    // 注册企业?
                    EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                            .eq(EnterpriseInfo::getUnifiedSocialCreditCode, entCodeScc));
                    // 关闭忽略策略
                    InterceptorIgnoreHelper.clearIgnoreStrategy();
                    // 1.无企业也无用户
                    try {
                        if (isEmpty(enterpriseInfo)) {
                            // 判断新增的企业名称唯一
                            List<SysTenant> sysTenantList = sysTenantMapper.selectList(new LambdaQueryWrapper<SysTenant>()
                                    .eq(SysTenant::getName, enterpriseName));
                            SysTenant sysTenant = new SysTenant();
                            // 已经有租户了
                            if (isNotEmpty(sysTenantList)) {
                                BeanUtil.copyProperties(sysTenantList.get(0), sysTenant, CopyOptions.create().ignoreNullValue());
                                // 没有租户
                            } else {
                                // 1. 租户表
                                sysTenant.setName(enterpriseName);
                                sysTenant.setStatus(1); // 默认正常
                                sysTenant.setType("1801046544255541249"); // 默认报关行
                                sysTenant.setCreateBy("SSO");
                                sysTenant.setBeginDate(new Date());
                                int insertSysTenant = sysTenantMapper.insert(sysTenant);
                                if (insertSysTenant <= 0) {
                                    throw new RuntimeException("注册失败，请稍后再试！");
                                }
                            }
                            // 2. 企业信息表
                            EnterpriseInfo epi = new EnterpriseInfo();
                            epi.setTenantId(sysTenant.getId());
                            epi.setCreateBy("SSO");
                            epi.setCreateTime(new Date());
                            epi.setRemarks("SSO单点登录创建");
                            epi.setUnifiedSocialCreditCode(entCodeScc);
                            epi.setDelFlag(CommonConstant.DEL_FLAG_0);
                            epi.setCotactChName("管理员");
                            epi.setEnterpriseFullName(enterpriseName);
                            epi.setCustomerCode(generateTenantCode()); // 生成企业编号
                            epi.setIsThirdParts(true); // 三方注册企业标识
                            int insertEnterpriseInfo = enterpriseInfoMapper.insert(epi);
                            if (insertEnterpriseInfo <= 0) {
                                log.info("新增企业信息表失败，回滚...");
                                throw new RuntimeException("注册失败，请稍后再试！");
                            }
                            // 3.报关行表加上数据
                            CustomsBrokerInfo customsBrokerInfo = enterpriseInfoMapper.getBrokerInfo(enterpriseName);
                            if (isEmpty(customsBrokerInfo)) {
                                customsBrokerInfo = new CustomsBrokerInfo();
                                customsBrokerInfo.setId(IdWorker.getIdStr());
                                customsBrokerInfo.setTenantId(sysTenant.getId());
                                customsBrokerInfo.setCreateBy("admin");
                                customsBrokerInfo.setCreateTime(new Date());
                                customsBrokerInfo.setCustomsBrokerName(enterpriseName);
    //                        customsBrokerInfo.setCustomsBrokerAddress(apiCustomerDTO.getRegisteredAddressDetail());
                                customsBrokerInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
    //                        customsBrokerInfo.setDepartcd(departcd);
                                customsBrokerInfo.setUnifiedSocialCreditCode(entCodeScc);
                                customsBrokerInfo.setRemarks("SSO单点登录");
                                int insertBrokerInfo = enterpriseInfoMapper.insertBrokerInfo(customsBrokerInfo);
                                if (insertBrokerInfo <= 0) {
                                    log.info("新增报关行表失败，回滚...");
                                    throw new RuntimeException("注册失败，请稍后再试！");
                                }
                            }

                            SysUser user = new SysUser();
                            user.setId(String.valueOf(userId)); // 直接用SSO的用户id
    //                        user.setUsername("user_" + epi.getCustomerCode() + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN)); // 默认个username
                            user.setUsername(isNotBlank(username) ? username : phone);
    //                        user.setPassword("123456");
                            user.setPassword(password);
                            user.setRealname(epi.getEnterpriseFullName().trim() + "管理员");
                            user.setSalt(salt);
    //                        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
    //                        user.setPassword(passwordEncode);
                            user.setStatus(1);
                            user.setDelFlag(CommonConstant.DEL_FLAG_0);
                            user.setTenantId(String.valueOf(epi.getTenantId()));
                            user.setRelTenantIds(String.valueOf(epi.getTenantId()));
                            user.setLastPasswordChangeDate(new Date());
                            user.setCreateBy("admin");
                            user.setCreateTime(new Date());
                            int insertUser = sysUserMapper.insert(user);
                            if (insertUser <= 0) {
                                log.info("新增默认用户失败，回滚...");
                                throw new RuntimeException("注册失败，请稍后再试！");
                            }
                            sysUser = new SysUser();
                            BeanUtil.copyProperties(user, sysUser, CopyOptions.create().ignoreNullValue());
                            // 5. 保存角色
                            SysUserRole userRole = new SysUserRole(user.getId(), sysTenant.getType()); // 默认租户的角色
                            int insertUserRole = sysUserRoleMapper.insert(userRole);
                            if (insertUserRole <= 0) {
                                log.info("新增默认用户角色失败，回滚...");
                                throw new RuntimeException("注册失败，请稍后再试！");
                            }
                            // 2.有企业无用户
                        } else {
                            SysUser user = new SysUser();
                            user.setId(String.valueOf(userId)); // 直接用SSO的用户id
                            user.setUsername(isNotBlank(username) ? username : phone);
                            user.setPassword(password);
                            user.setRealname(enterpriseInfo.getEnterpriseFullName().trim() + "管理员");
                            user.setSalt(salt);
                            user.setStatus(1);
                            user.setDelFlag(CommonConstant.DEL_FLAG_0);
                            user.setTenantId(String.valueOf(enterpriseInfo.getTenantId()));
                            user.setRelTenantIds(String.valueOf(enterpriseInfo.getTenantId()));
                            user.setCreateBy("admin");
                            user.setCreateTime(new Date());
                            int insertUser = sysUserMapper.insert(user);
                            if (insertUser <= 0) {
                                log.info("新增默认用户角色失败，回滚...");
                                throw new RuntimeException("注册失败，请稍后再试！");
                            }
                            // 保存角色
                            SysTenant sysTenant = sysTenantMapper.selectById(enterpriseInfo.getTenantId());
                            SysUserRole userRole = new SysUserRole(user.getId(), sysTenant.getType()); // 默认租户的角色
                            int insertUserRole = sysUserRoleMapper.insert(userRole);
                            if (insertUserRole <= 0) {
                                log.info("新增默认用户角色失败，回滚...");
                                throw new RuntimeException("注册失败，请稍后再试！");
                            }
                            sysUser = new SysUser();
                            BeanUtil.copyProperties(user, sysUser, CopyOptions.create().ignoreNullValue());
                        }
                    } catch (Exception e) {
                        ExceptionUtil.getFullStackTrace(e);
                        log.error("企业和用户注册时出现异常：{}", e.getMessage());
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        if (e.getMessage().toLowerCase().contains(CommonConstant.SQL_INDEX_UNIQ_SYS_USER_USERNAME)) {
                            return new HttpEntity<>("用户名[" + username + "]已经存在，新增失败！" + "|" + userId);
                        } else if (e.getMessage().toLowerCase().contains(CommonConstant.SQL_INDEX_UNIQ_SYS_USER_WORK_NO)) {
                            return new HttpEntity<>("工号已经存在，新增失败！" + "|" + userId);
                        }
                        return new HttpEntity<>("注册企业和用户时出现异常，请联系管理员！" + "|" + userId);
                    }
                }

                String tenantIds = sysUser.getTenantId();
                List<Long> tenantIdList = new ArrayList<>();
                for(String id: tenantIds.split(",")){
                    tenantIdList.add(Long.valueOf(id));
                }
                if(tenantIdList == null){
                    tenantIdList.add(Long.valueOf(tenantIds));
                }
                // 该方法仅查询有效的租户，如果返回0个就说明所有的租户均无效。
                List<SysTenant> tenantList = sysTenantService.queryEffectiveTenant(tenantIdList);
                SysTenant sysTenant = tenantList.get(0);

                // 设置忽略租户插件
                InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                //登录的企业信息
                List<EnterpriseInfo> enterpriseInfoList=enterpriseInfoService.list(new LambdaQueryWrapper<EnterpriseInfo>()
                        .eq(EnterpriseInfo::getTenantId,sysTenant.getId()));
                // 关闭忽略策略
                InterceptorIgnoreHelper.clearIgnoreStrategy();

                String token = JwtUtil.sign(sysUser.getUsername(), sysUser.getPassword());
                // 为了单点注销使用！！
                String redisKey = CommonConstant.PREFIX_USER_TOKEN + userId;
                Set<String> tokenSet;
                if (redisUtil.hasKey(redisKey)) {
                    tokenSet = (Set<String>) redisUtil.get(redisKey);
                } else {
                    tokenSet = new HashSet<>();
                }
                tokenSet.add(token);
                redisUtil.set(redisKey, tokenSet);
//                redisUtil.expire(redisKey, 3600); // 过期时间，单位为秒

                redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
                redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
                // 2023/8/26 21:39@ZHANGCHAO 追加/变更/完善：设置用户信息到redis中！！
                redisUtil.set(CommonConstant.PREFIX_USER_TOKEN_INFO + token, sysUser, JwtUtil.EXPIRE_TIME * 2 / 1000);
                redisUtil.set(CommonConstant.PREFIX_TENANT_TOKEN_INFO + token, sysTenant, JwtUtil.EXPIRE_TIME * 2 / 1000);
                redisUtil.set(CommonConstant.PREFIX_TENANT_TOKEN_OBJ + token, JSONObject.toJSONString(sysTenant), JwtUtil.EXPIRE_TIME * 2 / 1000);

                JSONObject obj = new JSONObject();
                obj.put("multi_depart", 0);
                obj.put("token", token);
                obj.put("userInfo", sysUser);
                obj.put("tenantInfo", sysTenant);
                obj.put("enterpriseInfo", isNotEmpty(enterpriseInfoList) ? enterpriseInfoList.get(0) : "");
                result.setResult(obj);
                result.success("登录成功");
                session.setAttribute("userId", userId);
            } else {
                // 将 sso-server 回应的消息作为异常返回
                result.error500(res.getMsg());
            }
        }

        return new HttpEntity<>(result);
    }

    // SSO-Client端：单点注销地址
    @RequestMapping("/sso/logout")
    public Object ssoLogout(@RequestParam(defaultValue = "/") String back, HttpServletRequest request,
                            HttpServletResponse response, HttpSession session) throws IOException {

        // 如果未登录，则无需注销
        if (session.getAttribute("userId") == null && isBlank(request.getParameter("userId"))) {
//            response.sendRedirect(back);
//            return null;
            return new HttpEntity<>(Result.ok("未登录,无需注销!"));
        }

        // 调用 sso-server 认证中心单点注销API 
        Object loginId = session.getAttribute("userId");  // 账号id
        if (isEmpty(loginId)) {
            loginId = request.getParameter("userId");
        }
        String timestamp = String.valueOf(System.currentTimeMillis());    // 时间戳
        String nonce = SsoRequestUtil.getRandomString(20);        // 随机字符串
        String sign = SsoRequestUtil.getSignout(loginId, timestamp, nonce);    // 参数签名

        String url = SsoRequestUtil.getSloUrl() +
                "?loginId=" + loginId +
//                "&client=" + SsoRequestUtil.getClient() +    // 添加client参数
                "&nonce=" + nonce +
                "&timestamp=" + timestamp +
                "&sign=" + sign;
        log.info("sign out url:{}", url);
        AjaxJson result = SsoRequestUtil.request(url);
        log.info("sign out result:{}", result);

        // 校验响应状态码，200 代表成功
        if (result.getCode() == 200) {
            log.info("注销成功");

            // 极端场景下，sso-server 中心的单点注销可能并不会通知到此 client 端，所以这里需要再补一刀
            // 2025/5/19 14:05@ZHANGCHAO 追加/变更/完善：注意：此处由于脱离单点框架sa-token体系，所以粒度只能达到具体用户，而暂时不能定位同一用户的不同状态下。
            session.removeAttribute("userId");
            try {
                SysUser sysUser = sysUserService.getById(String.valueOf(loginId));
                if (sysUser != null) {
                    LoginUser user = sysBaseApi.getUserByName(sysUser.getUsername());
                    baseCommonService.addLog("用户名: "+sysUser.getRealname()+",退出成功！", CommonConstant.LOG_TYPE_1, null, user);

                    // 获取用户的token集合
                    Object tokenO = redisUtil.get(CommonConstant.PREFIX_USER_TOKEN + loginId);
                    // 判断是否为Set类型（新的多token机制）
                    if (isNotEmpty(tokenO) && tokenO instanceof Set) {
                        Set<String> tokenSet = (Set<String>) tokenO;
                        tokenSet.forEach(t -> {
                            //清空用户登录Token缓存
                            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + t);
                            //清空用户登录Shiro权限缓存
                            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
                            // 2023/8/29 14:33@ZHANGCHAO 追加/变更/完善：清空用户信息和租户信息！！！
                            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN_INFO + t);
                            redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_INFO + t);
                            redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_OBJ + t);
                            //调用shiro的logout
                            SecurityUtils.getSubject().logout();
                        });
                    } else if (isNotEmpty(tokenO) && tokenO instanceof String) {
                        String token = String.valueOf(tokenO);
                        //清空用户登录Token缓存
                        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
                        //清空用户登录Shiro权限缓存
                        redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
                        // 2023/8/29 14:33@ZHANGCHAO 追加/变更/完善：清空用户信息和租户信息！！！
                        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN_INFO + token);
                        redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_INFO + token);
                        redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_OBJ + token);
                        //调用shiro的logout
                        SecurityUtils.getSubject().logout();
                    }
                    //清空用户的缓存信息（包括部门信息），例如sys:cache:user::<username>
                    redisUtil.del(String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, sysUser.getUsername()));
                    log.info(" 用户名:  {},退出成功！ ", sysUser.getRealname());
                }
            } catch (Exception e) {
                log.error("用户退出异常！", e);
            }
            // 返回 back 地址
//            response.sendRedirect(back);
//            return null;
            return new HttpEntity<>(Result.ok("注销成功!"));

        } else {
            // 将 sso-server 回应的消息作为异常抛出
//            throw new RuntimeException(result.getMsg());
            log.error("无效签名，无需注销：{}", sign);
            return new HttpEntity<>(Result.ok("无效签名，无需注销!"));
        }
    }

    // SSO-Client端：单点注销回调地址
    @RequestMapping("/sso/logoutCall")
    public Object ssoLogoutCall(String loginId, String autoLogout, String timestamp, String nonce, String sign) {

        // 校验签名
        String calcSign = SsoRequestUtil.getSignByLogoutCall(loginId, autoLogout, timestamp, nonce);
        if (!calcSign.equals(sign)) {
            log.error("无效签名，拒绝应答：{}", sign);
            return AjaxJson.getError("无效签名，拒绝应答" + sign);
        }

        // 注销这个账号id
        for (HttpSession session : MyHttpSessionHolder.sessionList) {
            Object userId = session.getAttribute("userId");
            if (Objects.equals(String.valueOf(userId), loginId)) {
                session.removeAttribute("userId");
                try {
                    SysUser sysUser = sysUserService.getById(String.valueOf(loginId));
                    if (sysUser != null) {
                        LoginUser user = sysBaseApi.getUserByName(sysUser.getUsername());
                        baseCommonService.addLog("用户名: "+sysUser.getRealname()+",退出成功！", CommonConstant.LOG_TYPE_1, null, user);

                        // 获取用户的token集合
                        Object tokenO = redisUtil.get(CommonConstant.PREFIX_USER_TOKEN + loginId);
                        // 判断是否为Set类型（新的多token机制）
                        if (isNotEmpty(tokenO) && tokenO instanceof Set) {
                            Set<String> tokenSet = (Set<String>) tokenO;
                            tokenSet.forEach(t -> {
                                //清空用户登录Token缓存
                                redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + t);
                                //清空用户登录Shiro权限缓存
                                redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
                                // 2023/8/29 14:33@ZHANGCHAO 追加/变更/完善：清空用户信息和租户信息！！！
                                redisUtil.del(CommonConstant.PREFIX_USER_TOKEN_INFO + t);
                                redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_INFO + t);
                                redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_OBJ + t);
                                //调用shiro的logout
                                SecurityUtils.getSubject().logout();
                            });
                        } else if (isNotEmpty(tokenO) && tokenO instanceof String) {
                            String token = String.valueOf(tokenO);
                            //清空用户登录Token缓存
                            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
                            //清空用户登录Shiro权限缓存
                            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
                            // 2023/8/29 14:33@ZHANGCHAO 追加/变更/完善：清空用户信息和租户信息！！！
                            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN_INFO + token);
                            redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_INFO + token);
                            redisUtil.del(CommonConstant.PREFIX_TENANT_TOKEN_OBJ + token);
                            //调用shiro的logout
                            SecurityUtils.getSubject().logout();
                        }
                        //清空用户的缓存信息（包括部门信息），例如sys:cache:user::<username>
                        redisUtil.del(String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, sysUser.getUsername()));
                        log.info(" 用户名:  {},退出成功！ ", sysUser.getRealname());
                    }
                } catch (Exception e) {
                    log.error("用户退出异常！", e);
                }
            }
        }

        return AjaxJson.getSuccess("账号id=" + loginId + " 注销成功");
    }

    // 查询我的账号信息 (调用此接口的前提是 sso-server 端开放了 /sso/userinfo 路由)
    @RequestMapping("/sso/myInfo")
    public Object myInfo(HttpSession session) throws IOException {
        // 如果尚未登录
        if (session.getAttribute("userId") == null) {
            return "尚未登录，无法获取";
        }

        // 组织 url 参数 
        Object loginId = session.getAttribute("userId");  // 账号id 
        String timestamp = String.valueOf(System.currentTimeMillis());    // 时间戳
        String nonce = SsoRequestUtil.getRandomString(20);        // 随机字符串
        String sign = SsoRequestUtil.getSign(loginId, timestamp, nonce);    // 参数签名

        String url = SsoRequestUtil.getGetDataUrl() +
                "?loginId=" + loginId +
//                "&apiType=userInfo" +
//                "&client=" + SsoRequestUtil.getClient() +    // 添加client参数
                "&timestamp=" + timestamp +
                "&nonce=" + nonce+
                "&sign=" + sign;
        log.info("myInfo url:{}", url);
        AjaxJson result = SsoRequestUtil.request(url);

        // 返回给前端 
        return result;
    }

    /**
     * 查询企业余额
     *
     * @param tenantId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/5/20 14:09
     */
    @RequestMapping(value = "/sso/queryBalance")
    public Result<?> queryBalanceByBiz(@RequestParam("tenantId") String tenantId) {
        return enterpriseInfoService.queryBalanceByBiz(tenantId);
    }

    // 全局异常拦截
    @ExceptionHandler
    public AjaxJson handlerException(Exception e) {
        e.printStackTrace();
        return AjaxJson.getError(e.getMessage());
    }

    /**
     * 生成企业编码
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/12/3 10:32
     */
    private String generateTenantCode() {
        String customerCode = generateEnterpriseCode();
        long num = enterpriseInfoMapper.selectCount(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getCustomerCode, customerCode));
        if (num > 0) {
            customerCode = generateTenantCode();
        }
        return customerCode;
    }

    /**
     * 生成企业编码
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/12/3 10:32
     */
    private static String generateEnterpriseCode() {
        Random random = new Random();
        // 生成随机大写字母
        char letter = (char) (65 + random.nextInt(26));
        // 生成两位随机数字
        int number = 10 + random.nextInt(90);
        // 组合编码
        return String.format("%c%02d", letter, number);
    }

}
