package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.entity.excel.PassPortByE;
import org.jeecg.modules.business.entity.excel.PassPortByI;
import org.jeecg.modules.business.entity.paramVo.PassPortVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.IPassPortHeadService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.business.service.IPassPortListService;
import org.jeecg.modules.business.service.IPassPortRelService;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.FTPUtil;
import org.jeecg.modules.business.util.message.MessageFileUtil;
import org.jeecg.modules.business.util.message.PassPortMessageUtil;
import org.jeecg.modules.business.util.message.SFTPUtil;
import org.jeecg.modules.business.util.printing.createpdfs.CreateInvtPDF;
import org.jeecg.modules.business.util.printing.createpdfs.CreateInvtPDFEntity;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;
import static org.jeecg.common.constant.CommonConstant.HAS_OWN_FTP;
import static org.jeecg.common.constant.CommonConstant.SFTP;

/**
 * <p>
 * 核放单表头 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class PassPortHeadServiceImpl extends ServiceImpl<PassPortHeadMapper, PassPortHead> implements IPassPortHeadService {
    @Autowired
    private PassPortListMapper passportListMapper;
    @Autowired
    private StockHeadTypeMapper stockHeadTypeMapper;
    @Autowired
    private IPassPortListService passPortListService;
    @Autowired
    private IPassPortRelService passportRelService;
    @Autowired
    private NemsInvtHeadMapper nemsInvtHeadMapper;
    @Autowired
    private NemsInvtListMapper nemsInvtListMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private CommonService commonService;
    @Value("${jeecg.path.upload}")
    private String upLoadPath;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendSasPath}") // /ImpPath/Sas/OutBox
//    private String remoteSendSasPath;
@Autowired
private SysConfigMapper sysConfigMapper;
    @Autowired
    private FtpProperties ftpProperties;


    /**
     * 根据分页信息和查询条件查询页面列表
     *
     * @param page       分页信息
     * @param passPortVO 查询条件
     * @return 分页后的页面列表
     */
    @Override
    public IPage<PassPortHead> queryPageList(Page<PassPortHead> page, PassPortVO passPortVO) {
        passPortVO.setTenantId(TenantContext.getTenant());
        IPage<PassPortHead> passPortHeadIPage = baseMapper.queryPageList(page, passPortVO);
        return passPortHeadIPage;
    }

    /**
     * 根据ID获取PassPortHead对象
     *
     * @param id 要查询的ID
     * @return 对应的PassPortHead对象
     */
    @Override
    public PassPortHead getPassById(String id) {
        PassPortHead passPortHead = baseMapper.selectById(id);
        List<PassPortList> passPortLists = passportListMapper.selectList(new QueryWrapper<PassPortList>().lambda()
                .eq(PassPortList::getPassId, id));
        passPortHead.setPassPortLists(passPortLists);
        return passPortHead;
    }

    /**
     * 保存核放单
     *
     * @param passPortHead 登录头部信息
     * @return 返回存储结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> savePass(PassPortHead passPortHead) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isEmpty(passPortHead.getId())) {
            passPortHead.setAudited(false);
            passPortHead.setSend(false);
            passPortHead.setCreateBy(loginUser.getUsername());
            passPortHead.setCreateDate(new Date());
            passPortHead.setDecPushStatus("0");
            baseMapper.insert(passPortHead);
            // 编辑
        } else {
            passPortHead.setUpdateBy(loginUser.getUsername());
            passPortHead.setUpdateDate(new Date());
            baseMapper.updateById(passPortHead);
        }
        if (isNotEmpty(passPortHead.getPassPortLists())) {
            passPortHead.getPassPortLists().forEach(v -> v.setPassId(passPortHead.getId()));
            QueryWrapper<PassPortList> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PassPortList::getPassId, passPortHead.getId());
            // 批量更新
            passPortListService.remove(queryWrapper);
            passPortListService.saveBatch(passPortHead.getPassPortLists());
        }
        // 2024/3/18 15:22@ZHANGCHAO 追加/变更/完善：核放单和报关单关联关系表
        if (isNotEmpty(passPortHead.getPassPortRelList())) {
            List<PassPortRel> oldPassPortRelList = passportRelService.list(new LambdaQueryWrapper<PassPortRel>()
                    .eq(PassPortRel::getPassId, passPortHead.getId()));
            if (isNotEmpty(oldPassPortRelList)) {
                // 使用 Stream 进行过滤
                List<PassPortRel> dels = oldPassPortRelList.stream()
                        .filter(item -> passPortHead.getPassPortRelList().stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (PassPortRel passPortRel : dels) {
                        passportRelService.removeById(passPortRel.getId());
                    }
                }
            }
            for (PassPortRel passPortRel : passPortHead.getPassPortRelList()) {
                if (isEmpty(passPortRel.getId())) {
                    passPortRel.setPassId(passPortHead.getId());
                    passPortRel.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    passPortRel.setCreateDate(new Date());
                    passportRelService.save(passPortRel);
                } else {
                    passPortRel.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    passPortRel.setUpdateDate(new Date());
                    passportRelService.updateById(passPortRel);
                }
            }
        }
        PassPortHead returnPortHead = baseMapper.selectById(passPortHead.getId());
        returnPortHead.setPassPortLists(passportListMapper.selectList(new QueryWrapper<PassPortList>().lambda()
                .eq(PassPortList::getPassId, returnPortHead.getId())));
        returnPortHead.setPassPortRelList(passportRelService.list(new QueryWrapper<PassPortRel>().lambda()
                .eq(PassPortRel::getPassId, returnPortHead.getId())));
        return Result.ok(returnPortHead);
    }

    /**
     * 批量删除数据
     *
     * @param ids 要删除的数据ID集合
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> delBatch(String ids) {
        List<PassPortHead> passPortHeads = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        for (PassPortHead passPortHead : passPortHeads) {
            if (isNotEmpty(passPortHead.getAudited()) && passPortHead.getAudited()) {
                return Result.error("核放单[" + passPortHead.getId() + "]已审核不允许删除！");
            }
            if (isNotBlank(passPortHead.getRelationId())){
                List<String> relationIds = Arrays.asList(passPortHead.getRelationId().split(","));
                nemsInvtHeadMapper.update(null,new UpdateWrapper<NemsInvtHead>().lambda()
                        .set(NemsInvtHead::getCreatePassPort, "0")
                        .in(NemsInvtHead::getId, relationIds));
            }
        }
        baseMapper.deleteBatchIds(Arrays.asList(ids.split(",")));
        return Result.ok("删除成功！");
    }

    /**
     * 导出核放单日报表Excel
     *
     * @param request    HTTP请求对象
     * @param response   HTTP响应对象
     * @param passPortVO 过境港口信息对象
     */
    @Override
    public void exportPassDailyReport(HttpServletRequest request, HttpServletResponse response, PassPortVO passPortVO) {
        /*
         * 此方法逻辑移植自平台苏彪
         * 2024/1/16 14:02@ZHANGCHAO
         */
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<PassPortByE> page_E = new Page(1, 5000);
        Page<PassPortByI> page_I = new Page(1, 5000);
        List<PassPortByE> exportPassPortByES = new ArrayList<>();
        List<PassPortByI> exportPassPortByIS = new ArrayList<>();
        boolean choiseIoTypecd = isBlank(passPortVO.getIoTypecd());
        boolean choiseRltTbTypecd = isBlank(passPortVO.getRltTbTypecd());
        String rltTbTypecd = passPortVO.getRltTbTypecd();

        //出口
        LambdaQueryWrapper<PassPortHead> queryWrapper = new LambdaQueryWrapper<>();
        passPortVO.setRltTbTypecd("2");
        getList(passPortVO,queryWrapper, loginUser.getUsername());

        Map<Long,PassPortByE> passPortByEMap_E = new HashMap<>();

        if (choiseIoTypecd || "E".equals(passPortVO.getIoTypecd())){

            LambdaQueryWrapper<PassPortHead> queryWrapperNems = new LambdaQueryWrapper<>();
            passPortVO.setRltTbTypecd("1");
            passPortVO.setIoTypecd("E");
            getList(passPortVO,queryWrapperNems, loginUser.getUsername());
            // 条件为当前租户
            List<PassPortByI> passPortByIS = baseMapper.listPasPortHeadAndNemsInvt(page_I,queryWrapperNems);

            if (passPortByIS != null && !passPortByIS.isEmpty()){
                for (PassPortByI pass : passPortByIS){
                    if (!(passPortByEMap_E != null && passPortByEMap_E.containsKey(pass.getId()))){
                        PassPortByE passByE = new PassPortByE();
                        BeanUtils.copyProperties(pass,passByE);
//                        passByE.setDclUnitcd(cjdwMap.containsKey(passByE.getDclUnitcd()) && isNotEmpty(passByE.getDclUnitcd())
//                                ? cjdwMap.get(passByE.getDclUnitcd()).getItemName() : passByE.getDclUnitcd());
                        if (isNotEmpty(passByE.getReceiptDate())){
                            try {
                                Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(passByE.getReceiptDate());
                                passByE.setReceiptDate(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                        }
                        if (isNotEmpty(passByE.getDclTime())){
                            try {
                                Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(passByE.getDclTime());
                                passByE.setDclTime(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                        }
                        passByE.setDclQty(isNotEmpty(passByE.getDclQty()) ? passByE.getDclQty().stripTrailingZeros() : passByE.getDclQty());

                        exportPassPortByES.add(passByE);
                    }
                }
            }
        }
        if ((choiseIoTypecd || "I".equals(passPortVO.getIoTypecd()))  && (choiseRltTbTypecd || "1".equals(rltTbTypecd))){
            //进口
            passPortVO.setIoTypecd("I");
            passPortVO.setRltTbTypecd("1");
            LambdaQueryWrapper<PassPortHead> queryWrapperByNems = new LambdaQueryWrapper<>();
            getList(passPortVO,queryWrapperByNems, loginUser.getUsername());
            List<PassPortByI> passPortByIS = baseMapper.listPasPortHeadAndNemsInvt(page_I,queryWrapperByNems);
            if (passPortByIS != null){
                passPortByIS.forEach(v->{
//                    v.setDclUnitcd(cjdwMap.containsKey(v.getDclUnitcd()) && isNotEmpty(v.getDclUnitcd())
//                            ? cjdwMap.get(v.getDclUnitcd()).getItemName() : v.getDclUnitcd());
                    if (isNotEmpty(v.getReceiptDate())){
                        try {
                            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getReceiptDate());
                            v.setReceiptDate(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date));
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
                    if (isNotEmpty(v.getDclTime())){
                        try {
                            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getDclTime());
                            v.setDclTime(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date));
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
                    v.setDclQty(isNotEmpty(v.getDclQty()) ? v.getDclQty().stripTrailingZeros() : v.getDclQty());
                });
            }
            exportPassPortByIS.addAll(passPortByIS);
        }


        // 导出参数
        List<Map<String, Object>> paramsMapList = new ArrayList<>(16);

        if (!exportPassPortByES.isEmpty()) {
            Map<String, Object> map = new HashMap<>(16);
            ExportParams exportParams = new ExportParams();
            exportParams.setSheetName("出口");
            exportParams.setType(ExcelType.XSSF);
            map.put("title", exportParams);
            map.put("entity", PassPortByE.class);
            map.put("data", exportPassPortByES);
            paramsMapList.add(map);
        }
        if (!exportPassPortByIS.isEmpty()){
            Map<String, Object> map = new HashMap<>(16);
            ExportParams exportParams = new ExportParams();
            exportParams.setSheetName("进口");
            exportParams.setType(ExcelType.XSSF);
            map.put("title", exportParams);
            map.put("entity", PassPortByI.class);
            map.put("data", exportPassPortByIS);
            paramsMapList.add(map);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(paramsMapList, ExcelType.XSSF);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param request
     * @param response
     * @param id
     */
    @Override
    public void printNemsInvt(HttpServletRequest request, HttpServletResponse response, String id) {
        NemsInvtHead invtHead = nemsInvtHeadMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda()
                .eq(NemsInvtHead::getId, id));
        List<NemsInvtList> nemsInvtLists = nemsInvtListMapper.selectList(new QueryWrapper<NemsInvtList>()
                .lambda().eq(NemsInvtList::getInvId, id));

        DecHead decHead = decHeadMapper.selectOne(new QueryWrapper<DecHead>().lambda()
                .eq(DecHead::getInvId, invtHead.getId()));
        if (decHead != null) {
            List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
                    .eq(DecList::getDecId, decHead.getId()));
            decHead.setDecLists(decLists);
        }

        Map<String, Map<String, String>> dictMap = new HashMap<>();
        getItemName(dictMap, "QDLX");
        getItemName(dictMap, "GQDM");
        getItemName(dictMap, "GBDQ");
        getItemName(dictMap, "JGFS2");
        getItemName(dictMap, "YSFS2");
        getItemName(dictMap, "BGLX");
        getItemName(dictMap, "BZDM");
        getItemName(dictMap, "ZJMSFS");
        getItemName(dictMap, "CJDW");

        Map<String, String> invtHeadMap = CreateInvtPDFEntity.createInvtHeadMap(invtHead, dictMap);//设置表头key值对应
        List<String> invtHeadKeys = CreateInvtPDFEntity.createInvtHeadKeyList(invtHead.getImpexpMarkcd(),
                invtHead.getDclcusFlag(), invtHead.getDclcusTypecd());//设置表头key值顺序
        List<String> invtLists = CreateInvtPDFEntity.createInvtList(nemsInvtLists, dictMap);//设置表体打印
        List<String> decLists = CreateInvtPDFEntity.createDecList(decHead, dictMap);//设置报关单草稿
        List<String> stockInOrOutLists = CreateInvtPDFEntity.createStockInOrOutList();//出入库单信息
        List<String> listOfECommerces = CreateInvtPDFEntity.createListOfECommerce();//电商清单信息
        List<String> materialsOrFinishedProducts = CreateInvtPDFEntity.createMaterialsOrFinishedProductsList();//简单加工清单料件表体/一纳成品内销成品表体
        List<String> tariffCollectionMaterials = CreateInvtPDFEntity.createTariffCollectionMaterials();//选择性征关税料件表体
        String fileName = invtHead.getId() + ".pdf";
        String path = upLoadPath + File.separator + invtHead.getId() + File.separator;
        File file = new File(path);
        if (!file.exists()) {//如果文件夹不存在
            file.mkdir();//创建文件夹
        }
        String filePath = path + fileName;

        CreateInvtPDF.createInvtPDF(filePath, invtHeadMap, invtHeadKeys,
                invtLists, decLists, materialsOrFinishedProducts, stockInOrOutLists, listOfECommerces,
                invtHead.getImpexpMarkcd(), tariffCollectionMaterials, path,false);

        // 设置文件路径
        File downloadFile = new File(filePath);
        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + downloadFile.getName() + "\"");
        response.setContentLength((int) downloadFile.length());
        // 通过流将文件写入响应
        try (OutputStream out = response.getOutputStream(); FileInputStream in = new FileInputStream(downloadFile)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param id
     * @param etpsPreentNo
     * @param seqNo
     * @return
     */
    @Override
    public Result<PassPortHead> setSeqNoByEtpsNoOrId(String id, String etpsPreentNo, String seqNo) {
        if (null == etpsPreentNo) {
            return Result.error("核放单企业内部编号不能为空！");
        }
        if (isBlank(id)) {
            return Result.error("核放单流水号不能为空！");
        }
        if (isBlank(seqNo)){
            return Result.error("核放单将要变更的统一编号不能为空！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        LambdaQueryWrapper<PassPortHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(isNotNull(id), PassPortHead::getId, id);
        PassPortHead passPortHead = getOne(queryWrapper);
        if (passPortHead == null){
            return Result.error("核放单信息为空，id为："+id);
        }
        baseMapper.update(null,new UpdateWrapper<PassPortHead>().lambda().set(PassPortHead::getSeqNo,seqNo)
                .set(!"B".equals(passPortHead.getStatus()),PassPortHead::getStatus,"0").eq(PassPortHead::getId,id));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(passPortHead);
    }

    /**
     * 核放单推送报文
     *
     * @param ids
     * @param delcareFlag
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/16 11:00
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handlePush(String ids, String delcareFlag) {
        StringBuilder returnMsg = new StringBuilder();
        Set<String> successIds = new HashSet<>(16);
        Set<String> errorIds = new HashSet<>(16);
        for (String id : ids.split(",")) {
            try {
                // 防止事务失效！！
                PassPortHeadServiceImpl currentProxy = (PassPortHeadServiceImpl) AopContext.currentProxy(); // 获取代理对象
                Result<?> result = currentProxy.sendMessage(id, delcareFlag);
                if (result.isSuccess()) {
                    successIds.add(id);
                    returnMsg.append(result.getResult());
                } else {
                    errorIds.add(id);
                    returnMsg.append("核注单[").append(id).append("]推送报文出现异常：").append(result.getMessage()).append(";");
                }
            } catch (Exception e) {
                e.printStackTrace();
                ExceptionUtil.getFullStackTrace(e);
                returnMsg.append("核注单[").append(id).append("]推送报文出现异常：").append(e.getMessage()).append(";");
                errorIds.add(id);
            }
        }
        String msg = "共" + ids.split(",").length + "票核放单，成功数："
                + successIds.size() + (isNotEmpty(errorIds) ? ("，失败数："
                + errorIds.size() + "，原因：" + returnMsg) : "");
        return Result.ok(msg);
    }

    /**
     * 根据出入库单生成核放单信息
     *
     * @param ids
     * @return
     */
    @Override
    public Result<PassPortHead> generatePassPortByStockType(String ids) {
        if (isEmpty(ids)){
            return Result.error("传入出入库单流水号不允许为空");
        }
        List<StockHeadType> stockHeadTypes =stockHeadTypeMapper.selectList(new QueryWrapper<StockHeadType>().lambda()
                .in(StockHeadType::getId,Arrays.asList(ids.split(","))).eq(StockHeadType::getCreatePassPort,"0"));
        if (stockHeadTypes == null || stockHeadTypes.isEmpty()){
            return Result.error("未找到对应出入库单,请核实数据");
        }
        List<PassPortHead> passPortHeads = new ArrayList<>();
        stockHeadTypes.forEach(v->{
            PassPortHead passPortHead = new PassPortHead();
            createPassPortHeadByStockHead(passPortHead,v);
            passPortHeads.add(passPortHead);
        });
        boolean result = this.saveBatch(passPortHeads);
        if (result){
            stockHeadTypeMapper.update(null,new UpdateWrapper<StockHeadType>().lambda().set(StockHeadType::getCreatePassPort,"1")
                    .in(StockHeadType::getId,Arrays.asList(ids.split(","))));
        }
        return result ? Result.ok("生成成功") : Result.error("生成失败，请联系管理员");
    }

    /**
     * 变更核放单审批结果
     *
     * @param seqNo
     * @param manageResult
     * @return
     */
    @Override
    public Result<PassPortHead> setManageResultById(String seqNo, String manageResult) {
        if (null == seqNo) {
            return Result.error("核放单统一编号不能为空！");
        }

        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        LambdaQueryWrapper<PassPortHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(isNotNull(seqNo), PassPortHead::getSeqNo, seqNo);
        PassPortHead passPortHead = getOne(queryWrapper);
        passPortHead.setManageResult(manageResult);
        saveOrUpdate(passPortHead);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(passPortHead);
    }

    /**
     * 根据统一编号变更状态
     *
     * @param seqNo
     * @param status
     * @return
     */
    @Override
    public Result<PassPortHead> setStatusBySeqNo(String seqNo, String status) {
        if (isEmpty(seqNo)){
            return Result.error("统一编号不能为空");
        }
        if (isEmpty(status)){
            return Result.error("状态不能为空");
        }

// 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        PassPortHead passPortHead = baseMapper.selectOne(new QueryWrapper<PassPortHead>().lambda().eq(PassPortHead::getSeqNo,seqNo));
        //根据核放单统一编号查询关联的出入库单表体带取备案号，并修改推送状态为未推送
        if(status.equals("B") && passPortHead.getRltTbTypecd()!=null && "2".equals(passPortHead.getRltTbTypecd())){
            baseMapper.setEmsPushStatusBySeqNo(seqNo);
        }

        if (passPortHead != null && "B".equals(passPortHead.getStatus())){
            return Result.ok("核注单已终审通过，不允许变更状态");
        }
        int result = baseMapper.update(null,new UpdateWrapper<PassPortHead>().lambda().set(PassPortHead::getStatus,status)
                .eq(PassPortHead::getSeqNo,seqNo));
// 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return result>0 ? Result.ok("更新成功") : Result.error("更新失败");
    }

    /**
     * 回执更新核放单（暂时只有表头）
     *
     * @param passPortHead
     * @return
     */
    @Override
    public Result<PassPortHead> savePassTypeForReceipt(PassPortHead passPortHead) {
        if (passPortHead == null){
            return Result.error("回执更新核放单时传入信息为空");
        }
        if (isBlank(passPortHead.getSeqNo())){
            return Result.error("回执更新核放单时传入统一编号为空");
        }

// 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        PassPortHead headType = baseMapper.selectOne(new QueryWrapper<PassPortHead>().lambda()
                .eq(PassPortHead::getSeqNo,passPortHead.getSeqNo()));
        if (headType == null){
            return Result.error("回执更新核放单时未找到本地核放单");
        }
        passPortHead.setId(headType.getId());
        passPortHead.setSend(headType.getSend());
        passPortHead.setApplyNumber(headType.getApplyNumber());
        passPortHead.setCreateBy(headType.getCreateBy());
        passPortHead.setCreateDate(headType.getCreateDate());
        passPortHead.setTenantId(headType.getTenantId());
        passPortHead.setDclTenantId(headType.getDclTenantId());
        passPortHead.setAudited(headType.getAudited());
        passPortHead.setUpdateBy(headType.getUpdateBy());
        passPortHead.setUpdateDate(headType.getUpdateDate());
        passPortHead.setRelationId(headType.getRelationId());
        passPortHead.setInputDate(headType.getInputDate());
        passPortHead.setPassStatus(headType.getPassStatus());
        passPortHead.setInputId(headType.getInputId());
        passPortHead.setDecPushStatus("0");
        saveOrUpdate(passPortHead);

        // TODO 检查 出库时间异常
//        checkOutTimeError(passPortHead);
// 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("操作成功");
    }

    /**
     * @param rltNo
     * @return
     */
    @Override
    public Result<?> getInvtByRltNo(String rltNo) {
        List<NemsInvtHead> invts = nemsInvtHeadMapper.selectList(new LambdaQueryWrapper<NemsInvtHead>()
                .eq(NemsInvtHead::getBondInvtNo, rltNo));
        return Result.ok(isNotEmpty(invts) ? invts.get(0) : null);
    }

    /**
     *
     * @param pph
     */
//    private void checkOutTimeError(PassPortHead pph){
//        if(pph == null || pph.getRltNo()==null || pph.getDclTime()==null
//                || !"E".equals(pph.getIoTypecd()) || !"2".equals(pph.getRltTbTypecd())){
//            return;
//        }
//
//        long t0 = System.currentTimeMillis();
//        try {
//            Date dclTime = DateUtils.parseDate(pph.getDclTime(), "yyyy-MM-dd HH:mm:ss");
//            Date dclTime2 = DateUtils.addDays(dclTime, -1);
//
//            List<StockTypeImport> list = stockTypeImportService.
//                    list(new QueryWrapper<StockTypeImport>().eq("DELIVERY_NOTE_NUMBER", pph.getRltNo()));
//
//            list.forEach(e -> {
//                if (dclTime.after(e.getIssueDateTime()) && dclTime2.before(e.getIssueDateTime())) {
//                    //如果无异常，则无需标记，防止之前有别的异常，此处给覆盖了。
//                    // e.setOuttimeError(0);
//                    // e.setErrorInfo("");
//                }else{
//                    e.setOuttimeError(1);
//                    e.setErrorInfo((!e.getErrorInfo().isEmpty()? e.getErrorInfo()+";":"")+"出库时间异常[核放单申报时间校验]");
//                }
//                stockTypeImportMapper.updateById(e);
//            });
//
//            log.info("申报回执->校验出库时间: {}-{}; 耗时：{}", dclTime, dclTime2, System.currentTimeMillis() - t0);
//        }catch(Exception e){
//            e.printStackTrace();
//        }
//    }

    /**
     * 出入库单生成核放单
     * @param passPortHead
     * @param stockHeadType
     */
    private void createPassPortHeadByStockHead(PassPortHead passPortHead,StockHeadType stockHeadType){
        passPortHead.setId(IdWorker.getId());
        passPortHead.setPassportTypecd("4");//核放单类型代码
        passPortHead.setMasterCuscd("4301");//主管关区代码
        passPortHead.setDclTypecd("1");//申报类型代码
        passPortHead.setBindTypecd("2");//绑定类型代码
        passPortHead.setRltTbTypecd("2");//关联单证类型代码
        passPortHead.setVehicleNo("鲁A97GJ1");//承运车车牌号
        passPortHead.setVehicleIcNo("鲁A97GJ1");//IC卡号(电子车牌）
        passPortHead.setVehicleWt(new BigDecimal("1619"));//车自重
        passPortHead.setDclEtpsno("3701983939");//申报企业编号
        passPortHead.setDclEtpsNm("山东迅吉安国际物流有限公司");//申报企业名称
        passPortHead.setDclEtpsSccd("91370100560757589T");//申报企业社会信用代码
        passPortHead.setInputCode("3701983939");//录入单位代码
        passPortHead.setInputSccd("91370100560757589T");//录入单位社会信用代码
        passPortHead.setInputName("山东迅吉安国际物流有限公司");//录入单位名称
        passPortHead.setCol1(true);//到货确认标志
        passPortHead.setAreainOriactNo("");//区内账册编号
        passPortHead.setContainerNo("");//集装箱号
        passPortHead.setVehicleFrameNo("");//车架号
        passPortHead.setVehicleFrameWt(null);//车架重
        passPortHead.setContainerType("");//集装箱箱型
        passPortHead.setContainerWt(null);//集装箱重
//        passPortHead.setEtpsPreentNo("");//企业内部编号
        passPortHead.setRmk("");//备注


        passPortHead.setAudited(false);//是否审核
        passPortHead.setSend(false);//是否发送报文

//        String username = isNotEmpty(GetUserNameUtil.getUserName()) ? GetUserNameUtil.getUserName(): stockHeadType.getCreatePerson();
//        if ("systemRecievor".equals(username)){
//            passPortHead.setDclErConc("邵珊");//申请人及联系方式
//        }else {
//            //获取当前租户
//            String tenantId = RequestKit.getRequestIn().getHeader(TENANT_ID);
//            if (redisUtil.get(tenantId +"_"+ username) != null) {
//                String realname = ((SysUser) redisUtil.get(tenantId + "_" + username)).getRealname();
//                passPortHead.setDclErConc(realname);//申请人及联系方式
//            }else {
//                passPortHead.setDclErConc("邵珊");//申请人及联系方式
//            }
//        }

//        passPortHead.setCreateBy(username);//创建人
        passPortHead.setCreateDate(new Date());//创建时间
        passPortHead.setDecPushStatus("0");
        try{
            passPortHead.setInputDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));//录入日期
        }catch (Exception e){
            e.printStackTrace();
        }
//        if (isEmpty(RequestKit.getRequestIn().getHeader(CUSTOMER_ID))){
//            YmMsg<Tenant> tenantById = commonApi.getTenantById(stockHeadType.getTenantId());
//            if (tenantById.isSuccess() && tenantById.getData()!=null){
//                passPortHead.setInputId(Long.valueOf(tenantById.getData().getCustomerId()));//录入企业ID
//            }
//        }else {
//            String customerId = RequestKit.getRequestIn().getHeader(CUSTOMER_ID);
//            passPortHead.setInputId(Long.valueOf(customerId));//录入企业ID
//        }
//        String tenantId = isNotEmpty(RequestKit.getRequestIn().getHeader(TENANT_ID)) && !"1".equals(RequestKit.getRequestIn().getHeader(TENANT_ID))
//                ?RequestKit.getRequestIn().getHeader(TENANT_ID):stockHeadType.getTenantId().toString();
//        passPortHead.setTenantId(Long.valueOf(tenantId));

        passPortHead.setAreainEtpsNo(stockHeadType.getAreainEtpsno());//区内企业编码
        passPortHead.setAreainEtpsNm(stockHeadType.getAreainEtpsNm());//区内企业名称
        passPortHead.setAreainEtpsSccd(stockHeadType.getAreainEtpsSccd());//区内企业社会信用代码
        passPortHead.setIoTypecd(stockHeadType.getStockTypecd());//进出标志代码
        passPortHead.setRltNo(stockHeadType.getSasStockNo());//关联单证编号
        passPortHead.setTotalGrossWt(stockHeadType.getGrossWt());//货物总毛重
        passPortHead.setTotalNetWt(stockHeadType.getNetWt());//货物总净重
        BigDecimal totalWt = (stockHeadType.getGrossWt() != null ? stockHeadType.getGrossWt() : BigDecimal.ZERO)
                .add(passPortHead.getVehicleWt());
        passPortHead.setTotalWt(totalWt);//总重量
        passPortHead.setRelationId(stockHeadType.getId().toString());//关联id
        passPortHead.setAreainOriactNo(stockHeadType.getAreainOriactNo());//账册号
        passPortHead.setEtpsPreentNo(stockHeadType.getEtpsPreentNo()+"_HF");//企业内部编号
        passPortHead.setReceiptDate(stockHeadType.getDeclarationDate());//收货时间

    }

    /**
     * 处理单个核放单的推送
     *
     * @param id
     * @param delcareFlag
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/16 11:04
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<?> sendMessage(String id, String delcareFlag) {
        PassPortHead passPortHead = getPassById(id);
        if (isEmpty(passPortHead)) {
            return Result.error("未找到对应的核放单信息");
        }
        if (isEmpty(passPortHead.getRltNo())){
            return Result.error("关联单证编号不允许为空");
        }
        if (isNotEmpty(passPortHead.getCol1()) && isNotEmpty(passPortHead.getAreainOriactNo()) && !passPortHead.getAreainOriactNo().contains("L")){
            return Result.error("只有L账册的核放单可以录入到货确认标志字段");
        }
        // 文件上传
        String fileName = new StringBuffer(passPortHead.getId().toString()).append("-PASS001").toString();
//        MsgFtpConfig ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendSasPath);
        SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
        FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
        MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendSasPath());
        String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
        boolean uploadFlag = false;
        try {
            if (SFTP.equals(ftpType)) {
                uploadFlag = new SFTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(MessageFileUtil
                                .exportZip(PassPortMessageUtil.generateSignature(passPortHead,passPortHead.getPassPortLists(),fileName,delcareFlag),
                                        String.format("%s.xml", fileName)).toByteArray()));
            } else  {
                uploadFlag = new FTPUtil(ftpConfig).upload(String.format("%s.zip", fileName),
                        new ByteArrayInputStream(MessageFileUtil
                                .exportZip(PassPortMessageUtil.generateSignature(passPortHead,passPortHead.getPassPortLists(),fileName,delcareFlag),
                                        String.format("%s.xml", fileName)).toByteArray()));
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!uploadFlag) {
            return Result.error("发送失败！");
        }
        // TODO
        return Result.ok();
    }

    private void getItemName(Map<String, Map<String, String>> dictMap, String dictCode) {
        Map<String, String> itemNameMap = new HashMap<>();
        if ("CJDW".equals(dictCode)) {
            List<DictQuery> list = commonService.listDictQuery("erp_units", "code", "name", null, null);
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("QDLX".equals(dictCode)) {
            List<DictQuery> list = commonService.listDict("QDLX");
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("GQDM".equals(dictCode)) {
            List<DictQuery> list = commonService.listDictQuery("erp_customs_ports", "customs_port_code", "name", null, null);
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("GBDQ".equals(dictCode)) {
            List<DictQuery> list = commonService.listDictQuery("erp_countries", "code", "name", null, null);
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("JGFS2".equals(dictCode)) {
            List<DictQuery> list = commonService.listDict("JGFS");
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("YSFS2".equals(dictCode)) {
            List<DictQuery> list = commonService.listDict("trans_type");
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("BGLX".equals(dictCode)) {
            List<DictQuery> list = commonService.listDict("BGLX");
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("BZDM".equals(dictCode)) {
            List<DictQuery> list = commonService.listDictQuery("erp_currencies", "code", "name", "currency", null);
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        } else if ("ZJMSFS".equals(dictCode)) {
            List<DictQuery> list = commonService.listDict("ZJMSFS");
            list.forEach(dictQuery -> itemNameMap.put(dictQuery.getCode(), dictQuery.getName()));
            dictMap.put(dictCode, itemNameMap);
            list.clear();
        }
    }

    private void getList(PassPortVO passPortVO,LambdaQueryWrapper<PassPortHead> queryWrapper,String username){
        queryWrapper.eq(isNotEmpty(passPortVO.getAudited()), PassPortHead::getAudited, passPortVO.getAudited());
        queryWrapper.eq(isNotEmpty(passPortVO.getSend()), PassPortHead::getSend, passPortVO.getSend());
        queryWrapper.apply(isNotNull(passPortVO.getAll())&& ("1".equals(passPortVO.getAll())), "PASS_PORT_HEAD.CREATE_BY='"+username+"'");
        // 高级查询参数
        queryWrapper.apply(isNotEmpty(passPortVO.getStatus()),"PASS_PORT_HEAD.STATUS='"+passPortVO.getStatus()+"'");
        queryWrapper.eq(isNotEmpty(passPortVO.getBindTypecd()), PassPortHead::getBindTypecd, passPortVO.getBindTypecd());
        queryWrapper.eq(isNotEmpty(passPortVO.getPassportTypecd()), PassPortHead::getPassportTypecd, passPortVO.getPassportTypecd());
        queryWrapper.eq(isNotEmpty(passPortVO.getRltTbTypecd()), PassPortHead::getRltTbTypecd, passPortVO.getRltTbTypecd());
        queryWrapper.eq(isNotEmpty(passPortVO.getRltNo()), PassPortHead::getRltNo, passPortVO.getRltNo());
        queryWrapper.eq(isNotEmpty(passPortVO.getPassportNo()), PassPortHead::getPassportNo, passPortVO.getPassportNo());
        queryWrapper.eq(isNotEmpty(passPortVO.getVehicleNo()), PassPortHead::getVehicleNo, passPortVO.getVehicleNo());
        queryWrapper.eq(isNotEmpty(passPortVO.getIoTypecd()),PassPortHead::getIoTypecd,passPortVO.getIoTypecd());
        queryWrapper.eq(isNotEmpty(passPortVO.getPassStatus()),PassPortHead::getPassStatus,passPortVO.getPassStatus());
        queryWrapper.apply(isNotEmpty(passPortVO.getSeqNo()),"PASS_PORT_HEAD.SEQ_NO = '"+passPortVO.getSeqNo()+"'");
        queryWrapper.apply(isNotEmpty(passPortVO.getAreainEtpsNo()),"PASS_PORT_HEAD.AREAIN_ETPS_NO = '"+passPortVO.getAreainEtpsNo()+"'");
        queryWrapper.apply(isNotEmpty(passPortVO.getEtpsPreentNo()),"PASS_PORT_HEAD.ETPS_PREENT_NO = '"+passPortVO.getEtpsPreentNo()+"'");
        queryWrapper.apply(isNotEmpty(passPortVO.getAreainEtpsNm()),"PASS_PORT_HEAD.AREAIN_ETPS_NM LIKE '%"+passPortVO.getAreainEtpsNm()+"%'");

        queryWrapper.apply(isNotNull(passPortVO.getDclTimeStart()),
                "date_format (PASS_PORT_HEAD.DCL_TIME,'%Y-%m-%d') >= date_format('" + passPortVO.getDclTimeStart() + "','%Y-%m-%d')");
        queryWrapper.apply(isNotNull(passPortVO.getDclTimeEnd()),
                "date_format (PASS_PORT_HEAD.DCL_TIME,'%Y-%m-%d') <= date_format('" + passPortVO.getDclTimeEnd() + "','%Y-%m-%d')");

        queryWrapper.apply(isNotNull(passPortVO.getStarCreateDate()),
                "date_format (PASS_PORT_HEAD.CREATE_DATE,'%Y-%m-%d') >= date_format('" + passPortVO.getStarCreateDate() + "','%Y-%m-%d')");
        queryWrapper.apply(isNotNull(passPortVO.getLastCreateDate()),
                "date_format (PASS_PORT_HEAD.CREATE_DATE,'%Y-%m-%d') <= date_format('" + passPortVO.getLastCreateDate() + "','%Y-%m-%d')");

        queryWrapper.apply(isNotEmpty(passPortVO.getDecPushStatus()),"PASS_PORT_HEAD.DEC_PUSH_STATUS='"+passPortVO.getDecPushStatus()+"'");
    }

}
