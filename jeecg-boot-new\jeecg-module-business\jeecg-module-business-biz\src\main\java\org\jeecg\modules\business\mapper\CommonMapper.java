package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.system.vo.*;
import org.jeecg.modules.business.entity.AiLog;
import org.jeecg.modules.business.entity.EnterpriseInfo;
import org.jeecg.modules.business.entity.ErpPackagesTypes;
import org.jeecg.modules.business.entity.dto.DictQuery;

import java.util.List;

/**
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/3/5 11:40
 */
public interface CommonMapper {

    @InterceptorIgnore(tenantLine = "true")
    List<DictQuery> listDictQuery(String table, String code, String name, String title, String suffix);

    @InterceptorIgnore(tenantLine = "true")
    List<DictQuery> listDict(String dictCode);

    @InterceptorIgnore(tenantLine = "true")
    LoginUser getUserByName(String username);

    @InterceptorIgnore(tenantLine = "true")
    SysUser getUserByUsername(String username);

    @InterceptorIgnore(tenantLine = "true")
    EnterpriseInfo getEnterpriseInfoByCond(String searchText);

    @InterceptorIgnore(tenantLine = "true")
    EnterpriseInfo getEnterpriseInfoBySwid(String swid);

    @InterceptorIgnore(tenantLine = "true")
    String getDepartNameByTenantId(Long tenantId);
    @InterceptorIgnore(tenantLine = "true")
    DictQuery getUnitsQuery(String code);
    @InterceptorIgnore(tenantLine = "true")
    DictQuery getUnitsQueryByKey(String code);

    @InterceptorIgnore(tenantLine = "true")
    DictQuery getCityportsByEnName(String enName);

    @InterceptorIgnore(tenantLine = "true")
    List<SysTenant> queryEffectiveTenant(List<Long> idList);

    @InterceptorIgnore(tenantLine = "1")
    List<SysRole> getRolesByUserId(String id);
    @InterceptorIgnore(tenantLine = "1")
    SysRole getSysRoleById(String id);

    @InterceptorIgnore(tenantLine = "1")
    List<SysTenant> getTenantByName(String departName);
    @InterceptorIgnore(tenantLine = "1")
    int insertSysTenant(SysTenant sysTenant);
    @InterceptorIgnore(tenantLine = "1")
    int insertUser(SysUser user);
    @InterceptorIgnore(tenantLine = "1")
    int insertUserRole(SysUserRole userRole);
    @InterceptorIgnore(tenantLine = "1")
    List<SysUser> getByTenantIdAndCreateBy(Long tenantId, String createBy);
    @InterceptorIgnore(tenantLine = "1")
    int updateSysTenant(String name, Long tenantId);
    @InterceptorIgnore(tenantLine = "1")
    EnterpriseInfo getEnterpriseInfoById(String id);
    @InterceptorIgnore(tenantLine = "1")
    Tenant getTenantById(String customerId);
    @InterceptorIgnore(tenantLine = "1")
    List<AiLog> listLog();
    @InterceptorIgnore(tenantLine = "1")
    List<AiLog> getStatusRecords(String logType, String sourceId);
    @InterceptorIgnore(tenantLine = "1")
    DictQuery getDictItemByKey(String dictCode, String itemValue, String itemText);
    @InterceptorIgnore(tenantLine = "1")
    DictQuery getDictItemByKeyLike(String dictCode, String itemValue, String itemText);
    @InterceptorIgnore(tenantLine = "1")
    String getCustomerTypeByUserId(String id);
    @InterceptorIgnore(tenantLine = "1")
    List<DictQuery> getDictItemBySearchText(String dictCode, String searchText);
    @InterceptorIgnore(tenantLine = "1")
    EnterpriseInfo getEnterpriseInfoByTenantId(String tenantId);
    @InterceptorIgnore(tenantLine = "1")
    ErpPackagesTypes getErpPackagesTypesByName(String packsKinds);
    @InterceptorIgnore(tenantLine = "1")
    DictQuery getCurrencyCode(String searchText);
    @InterceptorIgnore(tenantLine = "1")
    LoginUser getUserByClientId(String clientId);
    @InterceptorIgnore(tenantLine = "1")
    AiLog getLogContent521(String id);
    @InterceptorIgnore(tenantLine = "1")
    DictQuery getErpUnitsCode(String searchText);
}
