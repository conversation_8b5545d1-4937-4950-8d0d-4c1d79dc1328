<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="物料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input placeholder="请输入物料号" v-model="queryParam.copGno" type='no'></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="批次号/报关单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input placeholder="请输入批次号/报关单号" v-model="queryParam.batchNo" type='no'></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="项号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<j-input placeholder="请输入项号" v-model="queryParam.itemNumber" type='no'></j-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
							<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
							<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							<a @click="handleToggleSearch" style="margin-left: 8px">
							{{ toggleSearchStatus ? '收起' : '展开' }}
							<a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
							</a>
						</span>
					</a-col>
					<template v-if="toggleSearchStatus">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="品名" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input placeholder="请输入品名" v-model="queryParam.pn" type='no'></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="商品编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input placeholder="请输入商品编码" v-model="queryParam.hscode" type='no'></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="仓库" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.storeCode"
									:dict="dictStoreCode"
									placeholder="请选择仓库"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="货主" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.customer"
									:dict-options="dictOwnerOption"
									placeholder="请选择货主"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="库区" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-search-select-tag
									v-model="queryParam.areaCode"
									:dict-options="areaCodeAll"
									placeholder="请选择库区"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="储位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入储位代码" v-model="queryParam.spaceCode"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="料件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-dict-select-tag
									v-model="queryParam.detailType"
									type="list"
									dictCode="STORE_DETAIL_TYPE"
									placeholder="请选择料件类型"
								/>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="创建日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-range-picker v-model="selectDate" format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']"
																@change="onQueryTimeChange" />
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="运单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入运单号" v-model="queryParam.billNo"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="清单编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入清单编号" v-model="queryParam.bondInvtNo"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="库存>0" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-checkbox  v-model="queryParam.hasBeginQty">

								</a-checkbox>
							</a-form-item>
						</a-col>
					</template>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div>
			<!--			新表格-->
			<query-vxe-grid
				class="xGrid-style"
				ref="xGrid"
				size="mini"
				height="500"
				:loading="loading"
				:gridOptions="gridOptions"
				:dataSource="dataSource"
				@cell-click="cellClick"
				@checkbox-change="checkboxChangeEvent"
				@checkbox-all="checkboxChangeEvent"
				@page-change="handlePageChange"
			>
				<template v-slot:toolbar_buttons>
					<!-- 操作按钮区域 -->
					<div class="table-operator">
						<a-button
							v-has="'storeInventory:down||storeInventory:downC'"
							type="primary"
							icon="export"
							@click="exportStocks"
							:loading="exportLoading"
						>导出</a-button>
					</div>
				</template>
				<template #action="{ row }">
					<a @click="handleFlow(row)">
						<a-icon type="eye"/>
						查看库存流水
					</a>
				</template>
				<template #occupyQty="{ row }">
					<div>
						<span style='vertical-align:middle;color:blue;font-weight:bold'>{{row.occupyQty}}</span>
					</div>
				</template>
				<template #availableQty="{ row }">
					<div v-if="row.availableQty>0">
						<span style='vertical-align:middle;color:green;font-weight:bold' >{{ row.availableQty ? row.availableQty : 0}}</span>
					</div>
					<div v-else-if="row.availableQty==0">
						<span style='vertical-align:middle;color:red;font-weight:bold' >{{row.availableQty}}</span>
					</div>
					<div v-else>
						<span style='vertical-align:middle;color:red;font-weight:bold' >0</span>
					</div>
				</template>
				<template #beginQty="{ row }">
					<div v-if="row.beginQty>0">
						<span style='vertical-align:middle;color:green;font-weight:bold' >{{ row.beginQty ? row.beginQty : 0 }}</span>
					</div>
					<div v-else-if="row.beginQty==0">
						<span style='vertical-align:middle;color:red;font-weight:bold' >{{ row.beginQty  }}</span>
					</div>
					<div v-else>
						<span style='vertical-align:middle;color:red;font-weight:bold' >0</span>
					</div>

				</template>
				<template #weight="{ row }">
					<div v-if="row.weight>0">
						<span style='vertical-align:middle;color:green;font-weight:bold' >{{ row.weight ? row.weight : 0 }}</span>
					</div>
					<div v-else-if="row.weight==0">
						<span style='vertical-align:middle;color:red;font-weight:bold' >{{ row.weight  }}</span>
					</div>
					<div v-else>
						<span style='vertical-align:middle;color:red;font-weight:bold' >0</span>
					</div>

				</template>



			</query-vxe-grid>

<!--			<a-table-->
<!--				ref="table"-->
<!--				size="small"-->
<!--				:scroll="{ x: true }"-->
<!--				bordered-->
<!--				rowKey="id"-->
<!--				:columns="columns"-->
<!--				:dataSource="dataSource"-->
<!--				:pagination="ipagination"-->
<!--				:loading="loading"-->
<!--				class="j-table-force-nowrap"-->
<!--				@change="handleTableChange"-->
<!--			>-->
<!--				<span slot="model" slot-scope="text, record" :title="record.model">-->
<!--          {{subStrForColumns(record.model, 15)}}-->
<!--				</span>-->
<!--				<span slot="billCode" slot-scope="text, record" :title="record.billCode">-->
<!--          {{subStrForColumns(record.billCode, 15)}}-->
<!--				</span>-->
<!--				<span slot="bondInvtNo" slot-scope="text, record" :title="record.bondInvtNo">-->
<!--          {{subStrForColumns(record.bondInvtNo, 15)}}-->
<!--				</span>-->
<!--				<span slot="action" slot-scope="text, record">-->
<!--          <a @click="handleFlow(record)">-->
<!--            <a-icon type="eye"/>-->
<!--            查看库存流水-->
<!--          </a>-->
<!--        </span>-->
<!--			</a-table>-->
		</div>
		<inventory-flow-modal ref='inventoryFlowRef' />
	</a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import { ajaxGetDictItems } from '@/api/api'
import Vue from 'vue'
import { TENANT_ID } from '@/store/mutation-types'
import {generateTransactionNo, subStrForColumns} from '@/utils/util'
import InventoryFlowModal from '@/views/storeManage/modules/InventoryFlowModal.vue'
import store from "@/store";
import {downloadFile, getAction} from "@/api/manage";
import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid.vue'
const DICT_erp_units = 'erp_units,name,code'
import { MyXGridMixin } from '@/mixins/MyXGridMixin'
export default {
	name: 'StoreInventory',
	mixins: [MyXGridMixin, mixinDevice],
	components: { QueryVxeGrid, InventoryFlowModal },
	data() {
		return {
			dataSource:[],
			/* 分页参数 */
			ipagination: {
				total: 0,
				currentPage: 1,
				pageSize: 15,
				pageSizes: [15, 30, 50, 100, 200],
				perfect: true
			},
			exportLoading: false,
			detailTypes: [],
			areaCodeAll: [],
			dictOwner: '',
			dictOwnerOption: [],
			selectDate: [],
			// 全部数据的统计信息
			totalSummary: {
				beginQty: 0,
				availableQty: 0,
				occupyQty: 0,
				weight: 0
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
			},
			dictStoreCode: '',
			storeInfoOptions: [],
			// 表头
			columns: [
				{
					title: '仓库',
					align: 'center',
					dataIndex: 'storeName'
				},
				{
					title: '货主',
					align: 'center',
					dataIndex: 'customerStr'
				},
				{
					title: '物料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '项号',
					align: 'center',
					dataIndex: 'itemNumber'
				},
				{
					title: '批次号/报关单号',
					align: 'center',
					dataIndex: 'batchNo'
				},
				{
					title: '清单编号',
					align: 'center',
					dataIndex: 'bondInvtNo',
				},
				{
					title: '商品编码',
					align: 'center',
					dataIndex: 'hscode',
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'pn',
				},
				{
					title: '生产日期',
					align: 'center',
					dataIndex: 'manufactureDate',
				},
				{
					title: '有效到期日',
					align: 'center',
					dataIndex: 'expirationDate',
				},

				// {
				// 	title: '运单号',
				// 	align: 'center',
				// 	dataIndex: 'billNo'
				// },
				{
					title: '库区',
					align: 'center',
					dataIndex: 'areaCode',
				},
				{
					title: '储位',
					align: 'center',
					dataIndex: 'spaceCode',
				},
				{
					title: '库存数量',
					align: 'center',
					dataIndex: 'beginQty',
					customRender: (value, row, index) => {
						if(value > 0){
							return (
								<div>
									<span style={'vertical-align:middle;color:green;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						} else if (value == 0) {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ value }</span>
								</div>
							)
						} else {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
								</div>
							)
						}
					}
				},
				{
					title: '可用库存数量',
					align: 'center',
					dataIndex: 'availableQty',
					customRender: (value, row, index) => {
						if(value > 0){
							return (
								<div>
									<span style={'vertical-align:middle;color:green;font-weight:bold' }>{ value ? value : 0 }</span>
								</div>
							)
						} else if (value == 0) {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ value }</span>
								</div>
							)
						} else {
							return (
								<div>
									<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
								</div>
							)
						}
					}
				},
				{
					title: '占用数量',
					align: 'center',
					dataIndex: 'occupyQty',
					customRender: (value, row, index) => {
						// if(row.occupyQty >= 0){
						return (
							<div>
								<span style={'vertical-align:middle;color:blue;font-weight:bold' }>{ value }</span>
							</div>
						)
						// } else {
						// 	return (
						// 		<div>
						// 			<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
						// 		</div>
						// 	)
						// }
					}
				},
				{
					title: '单位',
					align: 'center',
					dataIndex: 'qunit',
					customRender: this.showQunitText
				},

				{
					title: '料件类型',
					align: 'center',
					dataIndex: 'detailType',
					customRender: this.detailTypeText
				},
				{
					title: '规格型号',
					align: 'center',
					dataIndex: 'model',
					scopedSlots: { customRender: 'model' }
				},
				{
					title: '运单号',
					align: 'center',
					dataIndex: 'billNo',
				},
				{
					title: '期初单价',
					align: 'center',
					dataIndex: 'beginDcluprcamt',
				},
				{
					title: '期初金额',
					align: 'center',
					dataIndex: 'beginAmount',
				},
				{
					title: '创建日期',
					align: 'center',
					dataIndex: 'createDate',
				},
				// {
				// 	title: '备注',
				// 	align: 'center',
				// 	width: 250,
				// 	dataIndex: 'remark',
				// 	scopedSlots: { customRender: 'remark' }
				// },
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				},
			],
			url: {
				list: '/business/storeStocks/list',
				loadTotalSummary: '/business/storeStocks/loadTotalSummary',
				listArea: '/business/storeInfo/listAreaAll',
				exportStocks: '/business/storeStocks/exportStocks',
			},
			units: []
		}
	},
	computed: {
		gridOptions() {
			const gridOptions = {
				id: 'Table',
				showFooter: true,
				footerMethod: this.footerMethod,
				pagerConfig: {
					currentPage:this.ipagination.currentPage,
					pageSize:this.ipagination.pageSize,
					pageSizes: [15, 30, 50, 100, 200],
					total:this.ipagination.total
				},
				toolbarConfig: {
					perfect: true,
					refresh: {
						query: () => this.loadData(1)
					},
					zoom: true,
					custom: true,
					slots: {
						buttons: 'toolbar_buttons'
					}
				},
				columns: [
					{
						title: '仓库',
						align: 'center',
						width: 220,
						field: 'storeName'
					},
					{
						title: '货主',
						align: 'center',
						width: 220,
						field: 'customerStr'
					},
					{
						title: '物料号',
						align: 'center',
						width: 160,
						field: 'copGno'
					},
					{
						title: '项号',
						align: 'center',
						width: 100,
						field: 'itemNumber'
					},
					{
						title: '批次号/报关单号',
						align: 'center',
						width: 180,
						field: 'batchNo'
					},
					{
						title: '清单编号',
						align: 'center',
						width: 180,
						field: 'bondInvtNo',
					},
					{
						title: '商品编码',
						align: 'center',
						width: 100,
						field: 'hscode',
					},
					{
						title: '商品名称',
						align: 'center',
						width: 140,
						field: 'pn',
					},
					{
						title: '生产日期',
						align: 'center',
						width: 100,
						field: 'manufactureDate',
					},
					{
						title: '有效到期日',
						align: 'center',
						width: 100,
						field: 'expirationDate',
					},

					// {
					// 	title: '运单号',
					// 	align: 'center',
					// 	dataIndex: 'billNo'
					// },
					{
						title: '库区',
						align: 'center',
						width: 80,
						field: 'areaCode',
					},
					{
						title: '储位',
						align: 'center',
						width: 80,
						field: 'spaceCode',
					},
					{
						title: '库存数量',
						align: 'center',
						field: 'beginQty',
						width: 100,
						slots: {
							default: 'beginQty'
						}
						// formatter:function ({ cellValue, row, column }) {
						// 	if(cellValue > 0){
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:green;font-weight:bold' }>{ cellValue ? cellValue : 0 }</span>
						// 			</div>
						// 		)
						// 	} else if (cellValue == 0) {
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ cellValue }</span>
						// 			</div>
						// 		)
						// 	} else {
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
						// 			</div>
						// 		)
						// 	}
						// }
					},
					{
						title: '可用库存数量',
						align: 'center',
						field: 'availableQty',
						width: 100,
						slots: {
							default: 'availableQty'
						}
						// formatter:function ({ cellValue, row, column }) {
						// 	if(cellValue > 0){
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:green;font-weight:bold' }>{ cellValue ? cellValue : 0 }</span>
						// 			</div>
						// 		)
						// 	} else if (cellValue == 0) {
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:red;font-weight:bold' }>{ cellValue }</span>
						// 			</div>
						// 		)
						// 	} else {
						// 		return (
						// 			<div>
						// 				<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
						// 			</div>
						// 		)
						// 	}
						// }
					},
					{
						title: '占用数量',
						align: 'center',
						field: 'occupyQty',
						width: 100,
						slots: {
							default: 'occupyQty'
						}
						// formatter:function ({ cellValue, row, column }) {
						// 	// if(row.occupyQty >= 0){
						// 	return (
						// 		<div>
						// 			<span style={'vertical-align:middle;color:blue;font-weight:bold' }>{ cellValue }</span>
						// 		</div>
						// 	)
						// 	// } else {
						// 	// 	return (
						// 	// 		<div>
						// 	// 			<span style={'vertical-align:middle;color:red;font-weight:bold' }>0</span>
						// 	// 		</div>
						// 	// 	)
						// 	// }
						// }
					},
					{
						title: '重量',
						align: 'center',
						field: 'weight',
						width: 100,
						slots: {
							default: 'weight'
						}
					},
					{
						title: '单位',
						align: 'center',
						field: 'qunit',
						width: 80,
						formatter: this.showQunitText
					},

					{
						title: '料件类型',
						align: 'center',
						field: 'detailType',
						width: 100,
						formatter: this.detailTypeText
					},
					{
						title: '规格型号',
						align: 'center',
						field: 'model',
						width: 200,
					},
					{
						title: '运单号',
						align: 'center',
						field: 'billNo',
						width: 180,
					},
					{
						title: '期初单价',
						align: 'center',
						width: 100,
						field: 'beginDcluprcamt',
					},
					{
						title: '期初金额',
						align: 'center',
						width: 100,
						field: 'beginAmount',
					},
					{
						title: '创建日期',
						align: 'center',
						width: 160,
						field: 'createDate',
					},
					// {
					// 	title: '备注',
					// 	align: 'center',
					// 	width: 250,
					// 	dataIndex: 'remark',
					// 	scopedSlots: { customRender: 'remark' }
					// },
					{
						title: '操作',
						field: 'action',
						align: 'center',
						fixed: 'right',
						width: 100,
						slots: {
							default: 'action'
						}
					},
				],


			}
			return gridOptions
		}
	},
	created() {
		// 查询当前租户id
		let tenantId = Vue.ls.get(TENANT_ID)
		if (!tenantId) {
			tenantId = 0
		}
		this.dictOwner =
			'commissioner,COMMISSIONER_FULL_NAME,id,tenant_id=' + tenantId + 'and del_flag=0'
		this.dictStoreCode =
			'store_info,STORE_NAME,STORE_CODE,tenant_id=' + tenantId
		this.initDictData(this.dictStoreCode)
		this.initDictData('STORE_DETAIL_TYPE')
		this.initDictData(DICT_erp_units)
		this.loadDictOptions()
		this.getAreaCodeDict()
		this.loadData(1)
	},
	methods: {
		// 添加合计行计算方法 - 显示全部数据的统计
		footerMethod({ columns, data }) {
			return [
				columns.map((column, columnIndex) => {
					if (columnIndex === 0) {
						return '合计';
					}
					// 根据列字段名显示全部数据的合计
					if (column.property === 'beginQty') {
						return this.totalSummary.beginQty.toFixed(2);
					}
					if (column.property === 'availableQty') {
						return this.totalSummary.availableQty.toFixed(2);
					}
					if (column.property === 'occupyQty') {
						return this.totalSummary.occupyQty.toFixed(2);
					}
					if (column.property === 'weight') {
						return this.totalSummary.weight.toFixed(2);
					}
					return '';
				})
			];
		},
		// // 获取全部数据的统计信息
		// async loadTotalSummary() {
		// 	try {
		// 		// 构建查询参数
		// 		var params = Object.assign({}, this.queryParam);
		// 		// var params = {};
		// 		const res = await getAction(this.url.loadTotalSummary, params);
		// 		if (res.success && res.result) {
		// 			// 直接使用后端返回的统计数据
		// 			this.totalSummary = {
		// 				beginQty: parseFloat(res.result.beginQty) || 0,
		// 				availableQty: parseFloat(res.result.availableQty) || 0,
		// 				occupyQty: parseFloat(res.result.occupyQty) || 0,
		// 				weight: parseFloat(res.result.weight) || 0
		// 			};
		// 		} else {
		// 			// 如果后端返回失败，重置为0
		// 			this.totalSummary = {
		// 				beginQty: 0,
		// 				availableQty: 0,
		// 				occupyQty: 0,
		// 				weight: 0
		// 			};
		// 		}
		// 	} catch (error) {
		// 		console.error('获取统计信息失败:', error);
		// 		// 如果获取失败，重置为0
		// 		this.totalSummary = {
		// 			beginQty: 0,
		// 			availableQty: 0,
		// 			occupyQty: 0,
		// 			weight: 0
		// 		};
		// 	}
		// },
		// 重写 loadData 方法，在加载数据后获取统计信息
		loadData (arg,sortQuery) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.currentPage = 1
			}
			var params = this.getQueryParams() //查询条件
			if(sortQuery){
				params.fields=sortQuery.fields
				params.sortType=sortQuery.sortType
			}
			this.loading = true
			getAction(this.url.list, params)
				.then((res) => {
					if (res.success) {
						//列拖拽后需要清空，否则会重新刷新
						this.$refs.xGrid.loadColumn([])

						// 获取数据
						const records = res.result.records || res.result

						// 如果有数据且第一条数据包含summary属性，则使用它更新totalSummary
						if (records && records.length > 0 && records[0].summary) {
							this.totalSummary = {
								beginQty: parseFloat(records[0].summary.beginQty) || 0,
								availableQty: parseFloat(records[0].summary.availableQty) || 0,
								occupyQty: parseFloat(records[0].summary.occupyQty) || 0,
								weight: parseFloat(records[0].summary.weight) || 0
							}
						} else {
							// 如果没有summary数据，重置为0
							this.totalSummary = {
								beginQty: 0,
								availableQty: 0,
								occupyQty: 0,
								weight: 0
							}
						}

						//加载数据
						this.$refs.xGrid.loadData(records)

						//排序的小标标直接赋值刷新会消失回显，不知为何。
						// this.dataSource = res.result.records || res.result
						// this.$refs.xGrid.loadData(res.result.records || res.result)
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						this.initSelects()
					} else {
						this.$message.warning(res.message || res.result)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		/**
		 * 导出入库单
		 */
		exportStocks() {
			const that = this
			this.$confirm({
				title: '确认导出',
				content: '是否要导出仓库库存记录?',
				onOk: () => {
					that.exportLoading = true
					downloadFile(that.url.exportStocks, `仓库库存导出_${generateTransactionNo('')}.xlsx`, that.queryParam)
						.finally(() => {
							setTimeout(() => {
								that.exportLoading = false
							}, 500)
						})
				}
			})
		},
		// 查询委托方数据
		async loadDictOptions() {
			this.dictOwnerOption = []
			await getAction(`/sys/dict/getDictItems/${this.dictOwner}`,{}).then(res=>{
				if (res.success) {
					this.dictOwnerOption = res.result.map(item => ({value: item.value, text: item.text}))
				} else {
					console.error('getDictItems error: : ', res)
					this.dictOwnerOption = []
				}
				if (store.getters.tenantInfo) {
					this.dictOwnerOption.push({value: String(store.getters.tenantInfo.id), text: store.getters.tenantInfo.name})
				}
			})
		},
		async getAreaCodeDict() {
			const res = await getAction(this.url.listArea, {customer: null})
			if (res.success && res.result) {
				console.log(res.result)
				if (res.result.length > 0) {
					this.areaCodeAll = res.result.map(item => ({
						value: item.areaCode,
						label: item.areaName,
						text: item.areaName
					}))
				}
			}
		},
		handleFlow(record) {
			this.$refs.inventoryFlowRef.open(record)
			this.$refs.inventoryFlowRef.title = '[' + record.copGno + '-' + record.itemNumber + '-' + record.pn + '] 库存流水'
		},
		subStrForColumns,
		showQunitText({ cellValue, row, column }) {
			return this.getText(cellValue, this.units)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text
		},
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == this.dictStoreCode) {
					this.storeInfoOptions = dictOptions
				} else if (dictCode == DICT_erp_units) {
					this.units = dictOptions
				} else if (dictCode == 'STORE_DETAIL_TYPE') {
					this.detailTypes = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						// this.initDictData(dictCode)
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length>0) {
							if (dictCode == this.dictStoreCode) {
								this.storeInfoOptions = dictOptions
							} else if (dictCode == DICT_erp_units) {
								this.units = dictOptions
							} else if (dictCode == 'STORE_DETAIL_TYPE') {
								this.detailTypes = dictOptions
							}
						}
					}
				})
			}
		},
		onQueryTimeChange(value, dateString) {
			console.log(dateString[0], dateString[1])
			this.queryParam.createDateSatrt = dateString[0].toString()
			this.queryParam.createDateLast = dateString[1].toString()
		},
		detailTypeText({ cellValue, row, column }) {
			return this.getText(cellValue, this.detailTypes)
		},
		searchReset() {
			this.selectDate = []
			this.queryParam = {}
			this.loadData(1)
			this.onClearSelected()
		}
	}
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-card-body{
	padding-top: 4px;

}
/* 保持合计行与数据行样式一致 */
.xGrid-style /deep/ .vxe-footer--column {
	height: 18px;
	line-height: 18px;
	font-weight: bold;
}

.xGrid-style /deep/ .vxe-footer--column .vxe-cell {
	height: 18px;
	line-height: 18px;
	font-weight: bold;
	vertical-align: middle;
}

/* 合计行数字颜色样式 */
.xGrid-style /deep/ .vxe-footer--column .vxe-cell:nth-child(6),
.xGrid-style /deep/ .vxe-footer--column .vxe-cell:nth-child(7),
.xGrid-style /deep/ .vxe-footer--column .vxe-cell:nth-child(10) {
	color: #52c41a; /* 绿色 - 期初数量、可用数量、重量 */
}

.xGrid-style /deep/ .vxe-footer--column .vxe-cell:nth-child(8) {
	color: #1890ff; /* 蓝色 - 占用数量 */
}

.xGrid-style /deep/ .vxe-footer--column .col_51 .col--center {
	padding-top: 0;
	padding-bottom: 0;
}
/deep/ .ant-tabs-bar {
	margin: 0px 0px 4px 0px !important;
}
/deep/ .ant-tabs .ant-tabs-large-bar .ant-tabs-tab {
	padding: 12px;
}
/deep/.ant-card-head {
	min-height: 44px !important;
	margin-bottom: -1px;
	padding: 0 24px;
	color: rgba(0, 0, 0, 0.85);
	font-weight: 500;
	font-size: 13px;
	background: transparent;
	border-bottom: 0px solid #e8e8e8 !important;
	zoom: 1;
}
/deep/.ant-badge {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	margin: 0;
	padding: 0;
	color: rgba(0, 0, 0, 0.65);
	font-size: 13px;
	font-variant: tabular-nums;
	line-height: 1.5;
	list-style: none;
	-webkit-font-feature-settings: 'tnum';
	font-feature-settings: 'tnum';
	position: relative;
	display: inline-block;
	color: unset;
	line-height: 1;
}

/deep/ .ant-badge-count {
	min-width: 20px;
	height: 16px !important;
	padding: 0 6px;
	color: #fff;
	font-weight: normal;
	font-size: 12px;
	line-height: 16px !important;
	border-radius: 10px;
	-webkit-box-shadow: 0 0 0 1px #fff;
	box-shadow: 0 0 0 1px #fff;
}
</style>