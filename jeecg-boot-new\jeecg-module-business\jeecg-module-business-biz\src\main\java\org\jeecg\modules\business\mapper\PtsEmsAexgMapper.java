package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.business.entity.NemsInvtList;
import org.jeecg.modules.business.entity.PtsEmsAexg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.PtsEmsAimg;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.vo.PtsEmsAexgWeightStatisticsVO;
import org.jeecg.modules.business.vo.PtsEmsAimgWeightStatisticsVO;

import java.util.List;

/**
 * <p>
 * 手账册归并后成品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface PtsEmsAexgMapper extends BaseMapper<PtsEmsAexg> {

    /**
     * 查询Ems的详细信息
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAexg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAexg> listEmsDetail(Page page, EmsQueryDto emsQueryDto);

    List<PtsEmsAexg> listAexgList(String emsNo, List<?> gNoList);

    @InterceptorIgnore(tenantLine = "true")
    PtsEmsAexg getOneByCond(String copGno, String emsNo, String tenantId);
    /**
     * 查询Ems的详细信息
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAexg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAexg> listEmsAexgByReport(Page page, EmsQueryDto emsQueryDto);
    /**
     * 查询Ems的详细信息
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAexg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAexg> listEmsAexgByReportC(String emsNo,String tenantId,List<Integer> gNoList);
    @InterceptorIgnore(tenantLine = "true")
    List<PtsEmsAexgWeightStatisticsVO> listEmsDetailWeightStatisticsAexg(String emsNo, String tenantId);
    /**
     * 出口保税成品金额统计-报表中心显示
     *
     * @param page   分页信息
     * @param emsQueryDto Ems查询条件
     * @return PtsEmsAimg对象的分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PtsEmsAexg> listEmsDetailAexgAmountByReport(Page page, EmsQueryDto emsQueryDto);
    @InterceptorIgnore(tenantLine = "true")
    List<NemsInvtList> listInvtList2(String key, String emsNo);

}
