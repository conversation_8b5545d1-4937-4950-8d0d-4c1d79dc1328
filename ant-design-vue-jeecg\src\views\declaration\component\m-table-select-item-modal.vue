<template>
    <div v-if="selectType=='decgoods'" ref="decRef">
                <a-input :id="id" :ref="hsModelPPLX" v-model="listQuery.name" v-focus="vfocusNum" :disabled='readonly' :placeholder="placeholder" :size='size' :style="styleSrt"
                         class="ant-input-a" style="width: 100% ;" type="text" @blur="blur()"
                         @focus="focus($event)" @keyup="getCreated($event)"  @mouseout="tooltipLeave()"
                         @mouseover="tooltipHover()" @keyup.enter="searchEnter($event)"  @keydown.down="selectDown()" @keydown.up.prevent="selectUp()">

                </a-input>
<!--            </a-form-model-item>-->

        <!-- 这是一个小叉叉，点击它可清除输入框内容 -->
        <!--                <span class="search-reset" @click="clearInput()">&times;</span>-->
        <div v-if="show ==true" ref='searchSelectDom' class="search-select">
            <HappyScroll ref="nav" :hide-horizontal='true' class=".happy-scroll-container .happy-scroll-content"
                         color="#CDCDCD" resize size="8"
                         top>
                <div class="con" style="width: 100%">

                    <!--样式1-->
                    <div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='1'"
                         class="search-select-option-sum">
                        <li
                            :key="dataObj.value"
                            v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                            class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
                            {{dataObj.title.replace(/[/]/g, "-")}}
                        </li>
                    </div>

                    <!--样式2-->
                    <div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='2'"
                         class="search-select-option-sum">
                        <div class="search-select-option-div-one">
                            <li
                                :key="dataObj.value"
                                v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
                                {{dataObj.title.replace(/[0-9]+/g," ").replace(/[/]/g, " ")}}
                            </li>
                        </div>
                        <div class="search-select-option-dev-two">
                            <li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option-li search-select-list"
                                @mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
                            </li>
                            <!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
                        </div>
                    </div>

                    <!--样式4 类似运输方式那种-->
                    <div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='4'"
                         class="search-select-option-sum">
                        <div class="search-select-option-div-one">
                            <li
                                :key="dataObj.value"
                                v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
                                {{dataObj.title.replace(/[/]/g, " ")}}
                            </li>
                        </div>
                        <div class="search-select-option-dev-two">
                            <li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                                class="search-select-option-li search-select-list"
                                @mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
                            </li>
                            <!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
                        </div>
                    </div>
                    <!--样式3 手动传个 myData-->
                    <div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='3'"
                         class="search-select-option-sum">
                        <li
                            :key="dataObj.value"
                            v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
                            class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
                            {{dataObj.title}}
                        </li>
                        <!--                                </div>-->
                    </div>

                    <li v-if="myData.length==0" class="search-select-option search-select-list">
                        暂无数据
                    </li>
                </div>
            </HappyScroll>
        </div>
        </div>
	<div v-else ref="decRef" >
		<a-input :id="id" :ref="hsModelPPLX" v-model="listQuery.name" v-focus="vfocusNum" :disabled='readonly' :placeholder="placeholder" :size='size' :style="styleSrt"
						 class="ant-input-a" style="width: 100% ;" type="text" @blur="blur()"
						 @focus="focus" @keyup="getCreated($event)"  @mouseout="tooltipLeave()"
						 @mouseover="tooltipHover()" @keyup.enter="searchEnter($event)"  @keydown.down="selectDown()" @keydown.up.prevent="selectUp()">
			<a-tooltip slot="suffix" class=".ant-input-affix-wrapper .ant-input-suffix "
								 style="font-size:11px;">
				<div class="icons-list" @mouseout="tooltipLeave()" @mouseover="tooltipHover()">
					<!--                            <div></div>-->
					<a-icon v-if="aIconType==0" slot="addonAfter" style="color:#BFBFBF" type="close-circle" @click="clearInput()"/>
					<a-icon v-if="aIconType ==1" slot="addonAfter" style="color:#BFBFBF" type="down"/>
					<a-icon v-if="aIconType ==2" slot="addonAfter" style="color:#BFBFBF" type="up"/>
				</div>
			</a-tooltip>
		</a-input>
		<!--            </a-form-model-item>-->

		<!-- 这是一个小叉叉，点击它可清除输入框内容 -->
		<!--                <span class="search-reset" @click="clearInput()">&times;</span>-->
		<div v-if="show ==true" ref='searchSelectDom' class="search-select">
			<HappyScroll ref="nav" :hide-horizontal='true' class=".happy-scroll-container .happy-scroll-content"
									 color="#CDCDCD" resize size="8"
									 top>
				<div class="con" style="width: 100%">

					<!--样式1-->
					<div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='1'"
							 class="search-select-option-sum">
						<li
							:key="dataObj.value"
							v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
							class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
							{{dataObj.title.replace(/[/]/g, "-")}}
						</li>
					</div>

					<!--样式2-->
					<div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='2'"
							 class="search-select-option-sum">
						<div class="search-select-option-div-one">
							<li
								:key="dataObj.value"
								v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
								class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
								{{dataObj.title.replace(/[0-9]+/g," ").replace(/[/]/g, " ")}}
							</li>
						</div>
						<div class="search-select-option-dev-two">
							<li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
									class="search-select-option-li search-select-list"
									@mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
							</li>
							<!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
						</div>
					</div>

					<!--样式4 类似运输方式那种-->
					<div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='4'"
							 class="search-select-option-sum">
						<div class="search-select-option-div-one">
							<li
								:key="dataObj.value"
								v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
								class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
								{{dataObj.title.replace(/[/]/g, " ")}}
							</li>
						</div>
						<div class="search-select-option-dev-two">
							<li :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
									class="search-select-option-li search-select-list"
									@mouseover="selectHover(index)">{{dataObj.title.replace(/[^0-9]/ig,"")}}
							</li>
							<!--                                <li :class="{selectback:index==now}" class="search-select-option-li search-select-list"  @mouseover="selectHover(index)">1</li>-->
						</div>
					</div>
					<!--样式3 手动传个 myData-->
					<div v-for="(dataObj,index) in myData" v-if="show ==true&&styleType=='3'"
							 class="search-select-option-sum">
						<li
							:key="dataObj.value"
							v-model="val" :class="{selectback:(index==now||dataObj.value==listQuery.name),selectbackSize:dataObj.value==val}"
							class="search-select-option search-select-list" @click="selectClick(dataObj.value)" @mouseover="selectHover(index)">
							{{dataObj.title}}
						</li>
						<!--                                </div>-->
					</div>

					<li v-if="myData.length==0" class="search-select-option search-select-list">
						暂无数据
					</li>
				</div>
			</HappyScroll>
		</div>
	</div>

        <!--        </td>-->
<!--    </fragment>-->
</template>

<script>
    import {axios} from '@/utils/request'
    import {HappyScroll} from 'vue-happy-scroll'
    import 'vue-happy-scroll/docs/happy-scroll.css'
    import {Fragment} from 'vue-fragment'
		import {ajaxGetDictItems, getDictItemsFromCache} from "@/api/api";
		import {getAction} from "@/api/manage";

    /**
     * 所有的字典表
     * @type {{customsDict: string, units: string, sysDict: string}}
     */
    const dictTableNames = {
        /**
         * 系统字典表
         */
        sysDict: 'sys_dict',
        /**
         * 海关参数
         */
        customsDict: 'CUSTOMS_DICT',
        /**
         * 计量单位表
         */
        units: 'UNITS',
    }

    const configMap = [
        // 注释
        {
            /**
             * 表名
             */
            tableName: '',
            dictKey: '',
            /**
             * dictCode解析
             * 用于非系统字典库的数据生成使用
             * 参数说明:
             * key: 去字典库查询所需, 如: 国别地区: GBDQ
             * value: 保存时使用的字段
             * name: 显示的字段
             * table: 字典所在的表名
             * pCode: key所在的列名
             * 传参三种情况
             * 1个参数: key                                    从sys_dict查询
             * 3个参数: table, name, pCode                     从指定表查询
             * 5个参数: table, name, value, key, pCode         从指定表查询
             * 示例: dictCode=CUSTOMS_DICT,ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE
             * CUSTOMS_DICT : 表名
             * ITEM_CODE    : this.value
             * GBDQ         : dictCode
             * P_DICT_CODE  : GBDQ所属列名(分类列名)
             */
            dictCode: '',
            /**
             * splicing解析
             * 数据库返回信息的字段拼接
             * 示例: ITEM_NAME,ITEM_CODE,STANDARD_CODE,MEMO
             * 返回: ITEM_NAME/ITEM_CODE/STANDARD_CODE/MEMO
             */
            splicing: '',
            /**
             * 是否需要通过租户id查询
             */
            byTenant: ''
        },

        // 计量单位
        {
            tableName: dictTableNames.units,
            dictKey: 'UNITS',
            dictCode: `${dictTableNames.units},CN_UNIT_NAME,EN_UNIT_NAME`,
            splicing: 'EN_UNIT_NAME,CN_UNIT_NAME',
            byTenant: true
        },
        // 计量单位-value为数字的情况
        {
            tableName: dictTableNames.units,
            dictKey: 'UNITS_NUM',
            dictCode: `${dictTableNames.units},CN_UNIT_NAME,DECLARE_UNIT_NAME`,
            splicing: 'DECLARE_UNIT_NAME,CN_UNIT_NAME',
            byTenant: true
        },
        // 币制代码
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'BZDM',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,BZDM,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME,ITEM_KEY`
        },
        // 国别地区
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GBDQ',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE`,
            splicing: `ITEM_NAME`
        },
        // 关区代码
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GQDM',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GQDM,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 港口代码报关单
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GKDM-DEC',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GKDM,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 港口代码报关单
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GKDM',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GKDM,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 运输方式
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'YSFS2',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,YSFS2,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 集装箱号
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'JZXGG',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,JZXGG,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 成交方式
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'CJFS',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,CJFS,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 监管方式
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'JGFS2',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,JGFS2,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 包装种类
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'BZZL',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,BZZL,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 入境口岸
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GNKA',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GNKA,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 报关单类型
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'BGDLX',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,BGDLX,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 检验检疫受理机关
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'JYJG',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,JYJG,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 运杂代码
        {
            tableName: dictTableNames.customsDict,
            dictKey: "JGLX",
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,JGLX,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`,
        },
        // 关联号码及理由
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GLLY',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GLLY,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 征免性质
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'ZMXZ',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,ZMXZ,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 国别地区-报关单
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GBDQ-DEC',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GBDQ,P_DICT_CODE`,
            splicing: `ITEM_KEY,ITEM_CODE,ITEM_NAME`
        },
        // 目的地
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'GNDQ',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,GNDQ,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 境内目的地
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'XZQH',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,XZQH,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 征免方式
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'ZJMSFS',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,ZJMSFS,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 用途
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'YT',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,YT,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`
        },
        // 海关合规库计量单位
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'CJDW',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_KEY,CJDW,P_DICT_CODE`,
            splicing: `ITEM_KEY,ITEM_NAME`,
        },
        // 海关合规库计量单位数字
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'CJDW_NUM',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,CJDW,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`,
        },
        // 业务类型
        {
            tableName: dictTableNames.customsDict,
            dictKey: 'YWLX',
            dictCode: `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,CJDW,P_DICT_CODE`,
            splicing: `ITEM_CODE,ITEM_NAME`,
        },

    ]

    export default {
        name: "mtableselectitemfuben",
        components: {
            HappyScroll,
            Fragment,
        },

        data: function () {

            return {
                // 全部的
                allOptions: [],
                myData: [],
                //显示选中
                now: -1,
                //上下键滑动slideB滑动表slideK滑动快
                slideB: 0,
                slideK: 0,
                // styleType: '',
                show: false,
                dataSelectShow: false,
                //focusIndex副本
                focusIndexs: 0,
                //0删除1向下2向上
                aIconType :1,

                listQuery: {

                    page: 1,

                    limit: 20,

                    name: this.searchName

                },
                val: "",
            }

        },
        props: {
					selectType:{
						type: String
					},
            /**
             * 组件id
             */
            id: {
                type: Number,
            },
            hsModelPPLX:{
                type: String
            },

            searchUrl: {

                type: String

            },
            /*下拉框宽度*/
            selectWidth: {
                type: Number
            },
            /*input宽度*/
            inputWidth: {
                type: Number
            },
            /**
             * 提示内容
             */
            placeholder: {
                type: String,
            },
            searchName: {// 用户回显内容信息

                type: String

            },
            /**
             * 组件大小
             */
            size: {
                type: String,
                default: 'default'
            },

            /**
             * 使用v-model, value双向绑定未处理
             */
            value: {
                type: String,
            },
            /**
             * 异步加载的下拉选项
             */
            dictKey: {
                type: String,
                required: "",
            },
            /**
             * 下拉的样式
             * 1，两种代码 -分隔
             * 2，分行的那种
             */
            styleType: {
                type: Number,
                required: true,
            },
            /**
             * 表名(字典表表名)
             */
            tableName: {
                type: String,
                default: dictTableNames.customsDict
            },

            /*原来的*/
            /**
             * 组件id
             */
            id: {
                type: Number,
            },
            /**
             * 前端写死的部分下拉
             */
            options: {
                type: Array,
                default: () => {
                    return []
                }
            },
            blurV: {},
            /**
             * value所在td的class
             */
            valClass: {
                type: String,
            },
            iprop: {
                type: String,
            },
            required: {
                type: Boolean,
                default: false
            },
            vfocusNum: {
                type: Boolean,
            },
            focusIndex: {
                type: Number,
            },
            // /**
            //  * 指定当前选中的条目
            //  */
            // value: {
            //     type: String,
            // },
            /**
             * label提示内容
             */
            label: {
                type: String,
            },

            /**
             * 选择框占用的单元格, 默认为1
             */
            valueWidth: {
                type: Number,
                default: 1
            },

            /**
             * label占用的单元格, 默认为1
             */
            labelWidth: {
                type: Number,
                default: 1
            },
            /**
             * 为true则不显示label
             */
            noLabel: {
                type: Boolean,
            },

            /**
             * 禁用
             */
            readonly: {
                type: Boolean
            },
					styleSrt:{
						type: String,
					}
        },
        model: {
            prop: 'value',
            event: 'change',
        },
        created() {
            this.val = this.value
            //dictKey为空说明是穿list下拉
            if (this.dictKey == "" || this.dictKey == null) {
                this.allOptions = this.options
            }
            this.$nextTick(() => {
                //本地下拉不需要缓存数据
                if (!!this.dictKey) {
                    this.initSelect()
                }
                //提取显示中文
                this.extractName(1)
                this.$refs.decRef.style.width = this.inputWidth + "%"
            })
        },
        methods: {

            async getCreated(event) {
                if (event.keyCode == 38 || event.keyCode == 40 || event.keyCode == 13) {  //向上向下回车
                    return;
                }
                if (event == "" || event == null|| this.listQuery.name=="") {
                    this.val = ''
                    //获取数据
                    this.search(this.listQuery.name)
                    return
                }


                this.aIconType = 2
                //拼装查询url
                const surl = this.getSearchUrl()

                //手动传list
                if (this.options.length > 0) {
                    this.allOptions = this.options
                }
                let getLocalData = sessionStorage.getItem(this.dictKey);
                let jsonData = JSON.parse(getLocalData);
                if (!!jsonData && jsonData.length > 0) {
                    this.allOptions = jsonData
                }
                //传海关代码
                if (this.allOptions.length == 0) {
                    const res = await axios.get(surl)
                    this.allOptions = res.result
                    let str_jsonData = JSON.stringify(this.allOptions);
                    sessionStorage.setItem(this.dictKey, str_jsonData);
                }

                //获取数据
                this.search(this.listQuery.name)
                //样式调整  自定义宽度
                this.$nextTick(async () => {
                    this.$refs.searchSelectDom.style.width = this.selectWidth + "%"
                    this.$refs.nav.initSize.width = "100%"
                    if(this.myData.length>0){
                        this.now = 0;
                    }
                })
            },
            //初始化下拉吧所有数据放到缓存
            async initSelect() {
                //拼装查询url
                const surl = this.getSearchUrl()
                let getLocalData = sessionStorage.getItem(this.dictKey);
							if (getLocalData != 'undefined' && getLocalData) {
								let jsonData = JSON.parse(getLocalData);
								this.allOptions = jsonData
							}else if(this.dictKey == 'trading_type'){
								this.allOptions = getDictItemsFromCache(this.dictKey);

							}else if (this.dictKey.indexOf(',')>-1){
								if (this.dictKey.includes('erp_currencies,name,code,currency,1=1')) {
									//根据字典Code, 初始化字典数组
									await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
										if (res.success) {
											if (res.result) {
												let list = []
												for (let dict of res.result) {
													let obj = {
														value: dict.value,
														text: dict.name + ' | ' + dict.title + ' | ' + dict.value,
														title: dict.name + ' | ' + dict.title + ' | ' + dict.value
													}
													list.push(obj)
												}
												this.allOptions = list

												let str_jsonData = JSON.stringify(this.allOptions)
												await sessionStorage.setItem(this.dictKey, str_jsonData)
											}
										}
									})
								} else {
									await getAction(`/sys/dict/loadDict/${this.dictKey}`, {
										keyword: '',
										pageSize: ''
									}).then(async (res) => {
										if (res.success) {
											let list = []
											for (let dict of res.result) {
												let obj = {
													value: dict.value,
													text: dict.text,
													title: dict.text
												}
												list.push(obj)
											}
											this.allOptions = list

											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									})
								}
							} else if (this.dictKey.includes('GBDQ-DEC')) {
								//根据字典Code, 初始化字典数组
								await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
									if (res.success) {
										if (res.result) {
											let list = []
											for (let dict of res.result) {
												let obj = {
													value: dict.title.slice(0, 3),
													text: dict.title.slice(0, 3) + ' | '+ dict.value + ' | ' + dict.title.slice(-3),
													title: dict.title.slice(0, 3) + ' | '+ dict.value + ' | ' + dict.title.slice(-3),
													label: dict.title.slice(0, 3) + ' | '+ dict.value + ' | ' + dict.title.slice(-3)
												}
												list.push(obj)
											}
											this.allOptions = list
											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									}
								})
							}else if (this.dictKey.includes('PPLX')||this.dictKey.includes('XHQK')) {
								//根据字典Code, 初始化字典数组
								await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
									if (res.success) {
										if (res.result) {
											let list = []
											for (let dict of res.result) {
												let obj = {
													value: dict.value,
													text: dict.text,
													title:  dict.value+'-'+ dict.text,
												}
												list.push(obj)
											}
											this.allOptions = list
											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									}
								})
							} else if (this.dictKey.includes('JGFS')) {
								//根据字典Code, 初始化字典数组
								await ajaxGetDictItems(this.dictKey, null).then(async (res) => {
									if (res.success) {
										if (res.result) {
											let list = []
											for (let dict of res.result) {
												let obj = {
													value: dict.value,
													text: dict.value + ' | ' + dict.title,
													title: dict.value + ' | ' + dict.title
												}
												list.push(obj)
											}
											this.allOptions = list
											let str_jsonData = JSON.stringify(this.allOptions)
											await sessionStorage.setItem(this.dictKey, str_jsonData)
										}
									}
								})
							} else if (surl == ''){
								if (this.dictKey =='SFDZDM'){

								}
								//根据字典Code, 初始化字典数组
								let dictStr = ''
								if (this.dictKey) {
									let arr = this.dictKey.split(',')
									if (arr[0].indexOf('where') > 0) {
										let tbInfo = arr[0].split('where')
										dictStr = tbInfo[0].trim() + ',' + arr[1] + ',' + arr[2] + ',' + encodeURIComponent(tbInfo[1])
									} else {
										dictStr = this.dictKey
									}
									let list = [];
									if (this.dictKey.indexOf(',') == -1) {
										//优先从缓存中读取字典配置
										if (getDictItemsFromCache(this.dictKey)) {
											list = getDictItemsFromCache(this.dictKey)
											this.allOptions = list;
											return
										}
									}
									await ajaxGetDictItems(dictStr, null)
										.then((res) => {
											if (res.success) {
												for(let dict of res.result){
													let obj={
														value: dict.value,
														text: dict.text,
														title:dict.text,
													}
													list.push(obj);
												}
												this.allOptions = list;
											}
										})
								}
							} else {
                    const res = await axios.get(surl)
                    this.allOptions = res.result

                    let str_jsonData = JSON.stringify(this.allOptions);
                    sessionStorage.setItem(this.allOptions[0].byCode, str_jsonData);
                }

            },
            getSearchUrl() {
                let url = ""
                if (this.tableName == "sys_dict") {
                    url = `/sys/dict/getDictItems` +
                        `/${this.dictKey}`

                } else {
                    url = `/sys/dict/getDictItems` +
                        `?dictCode=${this.dictCodeGen()}` +
                        `&splicing=${this.splicingGen()}` +
                        `&byTenant=${this.byTenantGen()}`
                }
                return url
            },
            endStyle() {
                //解决样式最后一行不全
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByClassName('happy-scroll-container')//获取模块目的设置高度
                if (this.myData.length >= 50) {
                    // dd2[0].style.height = '220px'
                }
                //上下键的滚动
                // dd1[0].style.transform = "translateY(" + 0 + "px)"
            },
            /**
             * 生成url中的dictCode字段
             */
            dictCodeGen() {
                const config = configMap.filter(item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName))[0]
                if (this.tableName === dictTableNames.customsDict) {
                    return this.dictCodeCustomsDictGen(config)
                }
                return !!config && config.dictCode || ''
            },
            /**
             * 海关参数库中的代码, 查询条件统一处理格式
             * @param config
             * @returns {string}
             */
            dictCodeCustomsDictGen(config) {
                // config为空
                if (!config || !config.dictCode) {
                    return `${dictTableNames.customsDict},ITEM_NAME,ITEM_CODE,${this.dictKey},P_DICT_CODE`
                }
                return config.dictCode
            },
            // 对比字符串(忽略大小写)
            compareStr(a, b) {
                return a.toLowerCase() === b.toLowerCase()
            },
            /**
             * 生成url中的splicing字段
             */
            splicingGen() {
                const config = configMap.filter(item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName))[0]
                return !!config ? config.splicing : ''
            },
            /**
             * 生成url中的byTenant字段
             */
            byTenantGen() {
                const config = configMap.filter(item => item.dictKey === this.dictKey && this.compareStr(item.tableName, this.tableName))[0]
                return !!config ? !!config.byTenant : false
            },
            // 检索
            search(value) {
                if (!value || !value.trim()) {
                    value = ''
                    this.listQuery.name = ''
                }
                this.initSearch(value)
                // this.solveDefaultValue(value)
                // this.initDomt()
            },
            // 初步检索
            initSearch(value) {
							console.log('--------------------------------------')
							console.log(this.allOptions)
							console.log('--------------------------------------')
                if (value == "") {
                    this.myData = this.allOptions.slice(0, 200)
                    //选中的元素一直显示
                    this.pitchShow()
                    this.show = true;
                    return
                }
                if (this.allOptions.length <= 200) {
                    this.myData = this.allOptions.filter(option => this.optionContain(option, value))
                    //选中的元素一直显示
                    this.pitchShow()
                    this.show = true;
                    return
                }
                let options = []
                for (let option of this.allOptions) {
                    if (this.optionContain(option, value)) {
                        options.push(option)
                        if (options.length === 50) {
                            break
                        }
                    }
                }
                if (options.length == 1) {
                    this.now = 0
                }
                this.myData = options
                //选中的元素一直显示
                this.pitchShow()
                this.show = true;
            },
            //选中的元素一直显示
            pitchShow() {
                if (!!this.val) {
                    let option = this.allOptions.filter(el => {
                        return el.value == this.val
                    })
                    //提取中文
                    if (option.length > 0) {
                        if (this.myData.length > 1) {
                            for (var i = 0; i < this.myData.length; i++) {
                                if (this.myData[i].value == this.val) {
                                    this.myData.splice(i, 1);
                                }
                            }
                            this.myData.unshift(option[0])
                        }
                    }
                }
            },

            /**
             * 判断 option 是否包含value(忽略option大小写)
             * @param option   this.options.item
             * @param value    子串
             * @returns {boolean} 为 true 代表包含
             */
            optionContain(option, value) {
                return !!~option.title.toLowerCase().indexOf(value.toLowerCase()) || !!~option.value.toLowerCase().indexOf(value.toLowerCase())
            },
            //清除内容

            clearInput: function () {

                if (!this.readonly) {
                    this.show = false;
                    this.val = ''
                    this.listQuery.name = undefined;

                    this.myData = [];
                }


            },


            selectClick: function (index) {
                let option = this.myData.filter(el => {
                    return el.value == index
                })
                //提取代码
                let strVal =""
                //成交方式报关单类型
                if(option[0].byCode=="CJFS"||option[0].byCode=="BGDLX"){
                    strVal = option[0].title.split("|")[1]
                }else if(this.dictKey=='PPLX'||this.dictKey=='XHQK'){
									strVal=option[0].text
								}
                else {
                    //提取中文
                    strVal = this.extractStr(option[0].title)
                }

                this.listQuery.name = strVal;
                this.val = option[0].value;
                this.aIconType = 1
                this.show = false;
                this.slideB = 0 //清空
                this.slideK = 0 //清空


            },
            extractStr(str) {
                if(!!str){
                    let reg = new RegExp('[\u4e00-\u9fa5]+$', 'g');
                    let strVal = str.match(/[\u4e00-\u9fa5]/g).join("");
                    return strVal
                }
            },


            //光标移动到下拉当前下拉颜色更变
            selectHover: function (index) {
                this.now = index;

            },

            //向下

            selectDown: function () {
                if (this.now < this.myData.length) {
                    this.now++;
                }
                let dd = document.getElementsByClassName('happy-scroll-bar')
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByClassName('happy-scroll-container')//获取模块目的设置高度
                let dd3 = document.getElementsByTagName("li")
                let conHeight = dd1[0].clientHeight //模块总高度
                let liHeight = dd3[1].clientHeight //li模块高度

                if (this.now != 50 && this.now != 200 && this.myData.length > 7 && this.now > 3) {
                    this.slideB = this.slideB - 35
                    dd1[0].style.transform = "translateY(" + this.slideB + "px)"
                }

                this.slideK = this.slideK + 10

            },
            //选中
            tooltipHover(){
                this.aIconType = 0
            },
            tooltipLeave(){
                if(this.show ==true){
                    this.aIconType = 2
                }else{
                    this.aIconType = 1
                }


            },

            //向上
            selectUp: function () {
                if (this.now > 0) {
                    this.now--;
                }

                let dd = document.getElementsByClassName('happy-scroll-bar')
                let dd1 = document.getElementsByClassName('con')
                let dd2 = document.getElementsByTagName("li")
                let conHeight = dd1[0].clientHeight //模块总高度
                if (this.slideK > 30 && this.slideB != 0) {
                    this.slideB = this.slideB + 35
                }

                this.slideK = this.slideK + 10
                dd1[0].style.transform = "translateY(" + this.slideB + "px)"

                // dd[0].style.transform = "translateY("+this.slideK+"px)"
                this.$forceUpdate()
            },
            /**
             * 失焦下拉隐藏
             */
            blur() {
                setTimeout(() => {
                    this.show = false
                    this.extractName(0)
                    this.slideB = 0 //清空
                    this.slideK = 0 //清空
                }, 200)
            },
            focus(event) {
							//获取焦点全选
							event.currentTarget.select();
                if(!!event&&event!="undefined"){
                    if(!!event.currentTarget.id){
                        this.focusIndexs = parseInt(event.currentTarget.id)
                        this.$emit('focusIndexDate', this.focusIndexs)
                    }
                }
            },

            searchEnter(event) {
                if( this.show==true&&this.myData.length>0){
                    // let decmodel = this.myData[this.now]
                    //提取中文
                    let strVal
                    //成交方式报关单类型 提取的不是中文有可能是是数字或者其他单独处理
                    if(this.myData[this.now].byCode=="CJFS"||this.myData[this.now].byCode=="BGDLX"){
                        strVal = this.myData[this.now].title.split("|")[1]
                    }else if(this.dictKey=='PPLX'||this.dictKey=='XHQK'){
											strVal=this.myData[this.now].text
										}
										else{
                        strVal = this.extractStr(this.myData[this.now].title)
                    }
                    this.listQuery.name = strVal;
                    this.val = this.myData[this.now].value;

                }

                this.focusIndexs = parseInt(event.currentTarget.id) + 1
                this.$emit('focusIndexDate', this.focusIndexs)
                this.aIconType = 1
                this.show = false;
                this.slideB = 0 //清空
                this.slideK = 0 //清空
            },
            //0是失去焦点listQuery.name不作处理 1是表体新增的时候listQuery.name赋空 提取中文
            extractName(e) {
                if (!!this.val) {
                    let option = this.allOptions.filter(el => {
                        return el.value == this.val
                    })
                    //提取中文
                    if (option.length > 0) {
                        let strVal
                        //成交方式报关单类型
                        if(option[0].byCode=="CJFS"||option[0].byCode=="BGDLX"){
                            strVal = option[0].title.split("/")[1]
                        }else if(this.dictKey=='PPLX'||this.dictKey=='XHQK'){
													strVal=option[0].text
												}
												else{
                            strVal = this.extractStr(option[0].title)
                        }

                        if (!!strVal) {
                            this.listQuery.name = strVal;
                        }
                    } else  {
                        this.listQuery.name = this.val;
                    }
                }else if(e==1){
                    this.listQuery.name =""
                }
            },
            refMethod(){
                this.listQuery.name = "ddd"
            }

        },


        watch: {
            value: {
                async handler(e) {
                    console.log(e)
                    this.val = this.value
                    //本地下拉不需要缓存数据
                    if (!!this.dictKey) {
                        await  this.initSelect()
                    }
                    //提取显示中文
                    await  this.extractName(1)
                    // this.listQuery.name='d'
                },
            },
            val: {
                handler() {
                    this.$nextTick(() => {
                        this.$emit('change', this.val)
                    })

                },
            },
        }

    }
</script>

<style scoped>
    .search-input {

        height: 45px;

        width: 200px;

        margin: 0 auto;

        margin-top: 10px;

        position: relative;

    }

    .search-inputinput {

        border: 1px solid #e4e4e4;

        box-sizing: border-box;

        width: 500px;

        height: 45px;

        font-size: 18px;

        float: left;

        padding-left: 10px;

        padding-right: 10px;

        overflow: hidden;

    }

    .search-btn {

        height: 45px;

        width: 100px;

        border: 1px solid mediumseagreen;

        background-color: mediumseagreen;

        color: white;

        font-size: 16px;

        font-weight: bold;

        float: left;

    }

    .search-btn {

        cursor: pointer

    }

    .search-select {

        position: absolute;

        top: 35px;

        width: 36.5%;

        box-sizing: border-box;
        z-index: 999;
        height: 220px;
        max-height: 220px;
        /*float: left;*/
        background-color: #FFFFFF;
        border: 1px solid #d4d4d4;

    }

    .search-selectli {

        border: 1px solid #d4d4d4;

        border-top: none;

        border-bottom: none;

        background-color: #E6F7FF;

        width: 100%

    }

    .search-select-option {
        float: left;
        box-sizing: border-box;
        /*padding: 7px;*/
        width: 100%;
        padding: 7px 7px 7px 7px;
        /*padding-left: 7px;*/
    }

    .search-select-option-sum {
        display: -webkit-flex;
        display: flex;
    }

    .search-select-option-dev-two {
        width: 30%;
        display: block;
        float: left;
    }

    .search-select-option-div-one {
        -webkit-flex: 1; /* Safari */
        -ms-flex: 1; /* IE 10 */
        flex: 1; /* Standard syntax */
        width: 70%;
    }

    .search-select-option-li {

        box-sizing: border-box;
        float: left;
        padding: 7px;
        width: 100%;
        height: 100%;
        color: #ccc;
    }

    .selectback {

        background-color: #E6F7FF !important;

        cursor: pointer

    }

    .selectbackSize {
        background-color: #F8F8F8 !important;
        font-weight: bold;
        cursor: pointer
    }

    input::-ms-clear {

        display: none

    }

    .search-reset {
        z-index: 999;
        width: 21px;
        height: 21px;

        position: relative;

        /*display: block;*/

        line-height: 21px;

        /*text-align: center;*/

        cursor: pointer;

        font-size: 20px;

        right: 20px;

        /*top: 12px*/

    }

    /*.search-reset {*/

    /*    width: 21px;*/

    /*    height: 21px;*/

    /*    position: relative;*/

    /*    display: block;*/

    /*    line-height: 21px;*/

    /*    text-align: center;*/

    /*    cursor: pointer;*/

    /*    font-size: 20px;*/

    /*    right: 110px;*/

    /*    top: 12px*/

    /*}*/

    .search-select-list {

        transition: all0 .5s

    }

    .itemfade-enter,
    .itemfade-leave-active {

        opacity: 0;

    }

    .itemfade-leave-active {

        position: absolute;

    }

    .selectback {

        background-color: #E6F7FF !important;

        cursor: pointer

    }

    .search-selectul {
        margin: 0;
        text-align: left;
    }

    .fade-enter-active, .fade-leave-active {

        transition: opacity .5s;

    }

    .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
    {

        opacity: 0;

    }

    >>> .happy-scroll-container .happy-scroll-content {
        /*display: inline;*/
        width: 100%;
    }

    .icons-list >>> .anticon {
        margin-right: 2px;
        font-size: 11px;
    }


    >>> .ant-input-a input {
        font-size: 13px !important;
    }

    .ant-input-affix-wrapper .ant-input-suffix {
        margin-right: 2px;
    }

    .alone-input >>> div.ant-col.ant-form-item-control-wrapper {
        width: 100%;
    }

</style>