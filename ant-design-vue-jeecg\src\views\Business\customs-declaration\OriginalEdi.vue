<template>
    <a-modal
        :width="1200"
        :title="title"
        :visible="innerVisible"
        @cancel="handleCancel"
        cancelText="关闭"
        :okButtonProps="{ style: { display: 'none' } }"
    >
<!--        <a-table-->
<!--            ref="table"-->
<!--            rowKey="id"-->
<!--            size="small"-->
<!--            bordered-->
<!--            :columns="columns"-->
<!--            :loading="loading"-->
<!--            :dataSource="dataSource"-->
<!--            :pagination="false"-->
<!--            :scroll="{ x: 460, y: 450, scrollToFirstRowOnChange: true }"-->
<!--        >-->
        <vxe-grid
            style="z-index: 999"
            id="vxe-gridA"
            class="xGrid-style"
            border
            resizable
            highlightHoverRow
            ref="xGrid"
            size="mini"
            height="454px"
            rowKey="id"
            show-overflow="true"
            :loading="loading"
            :checkbox-config="{ trigger: 'row', highlight: 'true' }"
            :columns="defColumns"
            :toolbar-config="tableToolbar"
            :custom-config="{ storage: true }"
            :data="dataSource"
            :transfer="true"
            :expand-config="{}"
        >
            <textarea
                slot="expandedRowRender"
                slot-scope="{row}"
                style="width:100%;font-size: 12px;font-weight:500"
                :rows="10"
                v-html="JSONbig.stringify(JSONbig.parse(row.receiptJson), null, 2)"
                readOnly
            >
            </textarea>
            <span slot="sendTime" slot-scope="{row}">
                <span v-if="!!row.sendTime">{{ row.sendTime | dayjs }}</span>
            </span>
            <span slot="receiverTime" slot-scope="{row}">
                <span v-if="!!row.receiverTime">{{ row.receiverTime | dayjs }}</span>
            </span>

            <span slot="formatResult" slot-scope="{row}" class="span-modal">
                <div v-if="!row.respList" class="span-modal">
                        <span class="span-modal">无处理</span>
                </div>
                <div v-else v-for="(resp, index) in row.respList" :key="index" class="span-modal">
                        <span v-if="resp.status === 'Ok'" class="span-modal">{{ resp.msg }}{{
                                resp.status
                            }}</span>
                        <span v-else-if="resp.status === 'Failed'" class="span-modal">{{ resp.msg }}<a @click="failedClick(resp)">{{
                                resp.status
                            }}</a></span>
                        <span v-else-if="resp.status === 'No'" class="span-modal">{{ resp.msg }}{{ resp.status }}</span>
                        <span v-else class="span-modal">{{ resp.msg }}</span>
                </div>
            </span>
        </vxe-grid>
    </a-modal>
</template>

<script>
import { putAction, deleteAction, getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'

export default {
    name: 'OriginalEdi',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
					relId: '',
            tableToolbar: {
                perfect: true,

                zoom: true,
                custom: true,
            },
            title: 'EDI状态详情',
            loading: false,
            respList: [],
            selectedRowKeys: [],
            dataSource: [],
            defColumns: [
                {
                    type: 'expand',
                    title: '',
                    width: 38,
                    slots: {
                        content: 'expandedRowRender'
                    }
                },
                {
                    title: '回执文件名称',
                    align: 'center',
                    field: 'fileName',
                    key: 'fileName',
                    width: '180'
                },
                { title: '回执类型', align: 'center', field: 'receiptType', width: '150' },
                { title: '回执处理', align: 'center', field: 'channel', width: '150' },
                {
                    title: '处理结果',
                    align: 'center',
                    field: 'dealStatus',
                    key: 'dealStatus',
                    width: '100',
                    formatter: function ({cellValue}) {
                        if (cellValue == '0') {
                            return '失败'
                        } else if (cellValue == '1') {
                            return '成功'
                        } else if (cellValue == '2') {
                            return '未处理'
                        } else {
                            return cellValue
                        }
                    }
                },
                {
                    title: '时间',
                    align: 'center',
                    field: 'sendTime',
                    width: '150',
                    key: 'sendTime',
                    slots: {
                        default: 'sendTime'
                    }
                },
                {
                    title: '接收时间',
                    align: 'center',
                    field: 'receiverTime',
                    key: 'receiverTime',
                    width: '150',

                    slots: {
                        default: 'receiverTime'
                    }
                },
                {
                    title: '信息',
                    align: 'center',
                    field: 'respList',
                    key: 'respList',
                    width: '280',
                    slots: {
                        default: 'formatResult'
                    }
                }

            ],
            innerVisible: false,
            url: {
                getEDIBin: '/dcl/edi/getEdiReceiptBySeq'
            }
        }
    },
    watch: {
        // visible: {
        //   immediate: true,
        //   handler(val) {
        //     if (val) {
        //       this.loadData();
        //     }
        //     this.innerVisible = val;
        //   },
        // },
        // innerVisible(val) {
        //   this.$emit("update:visible", val);
        // },
    },
    methods: {
        show(visible, seqNo,consignNo,ediType) {
						this.relId = ''
            this.innerVisible = visible
            if (ediType=='eleApply') {
              this.loadData(consignNo)
            } else if (ediType=='relId') {
							this.relId = consignNo
							this.loadData('')
						} else {
            this.loadData(seqNo)
        }
        },
        loadData(seqNo) {
            //   superRequest.call(this, {
            //     loading: true,
            //     promise: this.$http.get(this.url.getEDIBin),
            //     success: (res) => (this.dataSource = res.result),
            //   });

            getAction(this.url.getEDIBin, {
							id: this.relId ? this.relId : '',
                seqNo: seqNo? seqNo : ''
            }).then(res => {
                if (res.success) {
                    this.dataSource = res.result
                } else {
                    this.dataSource = {}
                }
            })
        },
        handleOk() {
            this.loadData()
            this.$emit('ok')
        },
        handleCancel() {
            this.innerVisible = false
        },

        handleRevertBatch() {
            this.handleRevert(this.selectedRowKeys)
        },
        handleDeleteBatch() {
            this.handleDelete(this.selectedRowKeys)
        },
        handleClearSelection() {
            this.handleTableSelectChange([], [])
        },
        handleTableSelectChange(selectedRowKeys, selectionRows) {
            this.selectedRowKeys = selectedRowKeys
            this.selectionRows = selectionRows
        },
        failedClick(resp) {
            this.$warning({
                centered: true,
                title: '错误详情',
                content: resp.failureStr,
            })
        }
    }
}
</script>

<style lang="less" scoped>
.vxe-grid /deep/ .vxe-toolbar {
    height: 25px;
}
.xGrid-style /deep/ .vxe-table .vxe-header--column {
    height: 28px;
}
.xGrid-style /deep/ .vxe-table .vxe-header--column {
    height: 28px;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column {
    height: 28px;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column {
    height: 28px;
}
.span-modal {
    display: inline-block;
    text-align: left;
    width: 210px;
}
</style>
