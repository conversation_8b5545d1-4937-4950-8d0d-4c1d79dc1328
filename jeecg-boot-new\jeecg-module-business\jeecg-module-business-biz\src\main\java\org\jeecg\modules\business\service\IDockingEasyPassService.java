package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.JgVFlyBgDechead;
import org.jeecg.modules.business.entity.JgVFlyBgUnsafebusiness;
import org.jeecg.modules.business.entity.JgVFlyHzqdHead;
import org.jeecg.modules.business.entity.JgVFlySjZqHwsbCxdXxcx;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IDockingEasyPassService {

    IPage<JgVFlyBgDechead> listDec(Page<JgVFlyBgDechead> page, String customerName, String starDate, String lastDate);
    IPage<JgVFlySjZqHwsbCxdXxcx> listJgVFlySjZqHwsbCxdXxcx(Page<JgVFlySjZqHwsbCxdXxcx> page, String customerName, String starDate, String lastDate);
    IPage<JgVFlyHzqdHead> listInvt(Page<JgVFlyHzqdHead> page, String customerName, String starDate, String lastDate);
    IPage<JgVFlyBgUnsafebusiness> listTaxBill(Page<JgVFlyBgUnsafebusiness> page, String customerName, String starDate, String lastDate);

    Result<?> dockingDecSync(String customerName, String starDate, String lastDate);
    Result<?> dockingInvtSync(String customerName, String starDate, String lastDate);
    Result<?> dockingRepairCancellationOrdersSync(String customerName, String starDate, String lastDate);

    Result<?> syncHzqdForJob(String bizopEtpsNm, String supvModecd, String putrecNo, String startDate, String lastDate, String isAll);

    /**
     * 同步远程核注单通用 -- 根据经营单位编码
     *
     * @param bizopEtpsno
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/5 16:50
     */
    Result<?> syncHzqdCommonJob(String bizopEtpsno, String bondInvtNo, String startDate, String lastDate, String isAll);
    /**
     * 同步远程核注单通用 -- 根据经营单位编码
     *
     * @param bizopEtpsno
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/5 16:50
     */
    Result<?> syncHzqdCommonJob_new(String bizopEtpsno, String bondInvtNo, String startDate, String lastDate, String isAll);

    Result<?> dockingDecCommonSync(String ownerCode, String ownerName, String startDate, String lastDate, String isAll);

    Result<?> dockingDecSBCommonSync(String agentCode, String agentName, String startDate, String lastDate, String isAll);
}
