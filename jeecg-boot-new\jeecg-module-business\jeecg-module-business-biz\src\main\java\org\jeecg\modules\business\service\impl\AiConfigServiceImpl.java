package org.jeecg.modules.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.AiConfig;
import org.jeecg.modules.business.entity.StoreInfo;
import org.jeecg.modules.business.entity.StoreSpace;
import org.jeecg.modules.business.mapper.AiConfigMapper;
import org.jeecg.modules.business.service.IAiConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * <p>
 * AI配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Slf4j
@Service
public class AiConfigServiceImpl extends ServiceImpl<AiConfigMapper, AiConfig> implements IAiConfigService {

    /**
     * AI配置列表
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:14
     */
    @Override
    public Result<?> listAiSettings(AiConfig aiConfig) {
        aiConfig.setTenantId(Long.valueOf(TenantContext.getTenant()));
        List<AiConfig> aiConfigList = baseMapper.listAiSettings(aiConfig);
        if (isNotEmpty(aiConfigList) && aiConfigList.size() > 1) {
            for (int i = 0; i < aiConfigList.size(); i++) {
                if (aiConfigList.get(i).getIsDefault()) {
                    AiConfig aiConfigTwo = aiConfigList.get(i);
                    aiConfigList.remove(i);
                    aiConfigList.add(0, aiConfigTwo);
                    break;
                }
            }
        }
        return Result.ok(aiConfigList);
    }

    /**
     * 删除Ai配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:21
     */
    @Override
    public Result<?> deleteAiConfigBatch(String ids) {
        for (String id : ids.split(",")) {
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 保存AI配置
     *
     * @param aiConfig
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/18 23:59
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveAiSetting(AiConfig aiConfig) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isEmpty(aiConfig.getId())) {
//            Long count = baseMapper.selectCount(new LambdaQueryWrapper<AiConfig>()
//                    .eq(AiConfig::getIeFlag, aiConfig.getIeFlag())
//                    .eq(AiConfig::getTenantId, TenantContext.getTenant()));
//            if (count > 0) {
//                return Result.error("操作失败！已存在对应进出口类型的配置！");
//            }
            aiConfig.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            aiConfig.setCreateDate(new Date());
            aiConfig.setTenantId(Long.valueOf(TenantContext.getTenant()));
            baseMapper.insert(aiConfig);
            // 编辑
        } else {
//            Long count = baseMapper.selectCount(new LambdaQueryWrapper<AiConfig>()
//                    .eq(AiConfig::getIeFlag, aiConfig.getIeFlag())
//                    .ne(AiConfig::getId, aiConfig.getId())
//                    .eq(AiConfig::getTenantId, TenantContext.getTenant()));
//            if (count > 0) {
//                return Result.error("操作失败！已存在对应进出口类型的配置！");
//            }
            aiConfig.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            aiConfig.setUpdateDate(new Date());
            baseMapper.updateById(aiConfig);
        }
        return Result.ok(aiConfig);
    }

    /**
     * 设置默认AI配置
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/30 14:19
     */
    @Override
    public Result<?> setDefaultAiConfig(String ids) {
        for (String id : ids.split(",")) {
            AiConfig aiConfig = baseMapper.selectById(id);
            if (isEmpty(aiConfig)) {
                return Result.error("未找到ID为" + id + "的AI配置！");
            }
            if (aiConfig.getIsDefault()) {
                return Result.error("ID为" + id + "的AI配置已经是默认配置，无需重复设置！");
            }
            // 如果相同的进出口标识的配置已经是默认配置，则取消其他的配置，将此条设置默认
            baseMapper.update(null, new LambdaUpdateWrapper<AiConfig>()
                    .set(AiConfig::getIsDefault, false)
                    .eq(AiConfig::getIeFlag, aiConfig.getIeFlag())
                    .eq(AiConfig::getTenantId, aiConfig.getTenantId()));
            // 设置默认
            baseMapper.update(null, new LambdaUpdateWrapper<AiConfig>()
                    .set(AiConfig::getIsDefault, true)
                    .eq(AiConfig::getId, id));
        }
        return Result.ok("设置成功！");
    }

    /**
     * 根据ID查询AI配置
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/19 00:09
     */
    @Override
    public Result<?> getAiSettingById(String id) {
        AiConfig aiConfig = baseMapper.selectById(id);
        if (isEmpty(aiConfig)) {
            return Result.error("未找到ID为" + id + "的AI配置，请刷新页面重试！");
        }
        return Result.ok(aiConfig);
    }

}
