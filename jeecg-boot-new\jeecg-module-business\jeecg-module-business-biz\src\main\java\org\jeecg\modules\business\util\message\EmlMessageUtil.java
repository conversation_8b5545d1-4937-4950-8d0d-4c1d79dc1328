package org.jeecg.modules.business.util.message;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.messages.emlMessages.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * EmlMessageUtil
 * <pre>
 *   加贸手册发送报文用
 * </pre>
 *
 * <AUTHOR>  2025/6/23 16:34
 * @version 1.0
 */
public class EmlMessageUtil {

    public static EmlMessage generateMessage(PtsEmsHead ptsEmsHead, String fileName, EnterpriseInfo enterpriseInfo,String flag ) {
        EmlMessage emlMessage = new EmlMessage();
        emlMessage.setNptsEmlHead(generateNptsEmlHead(ptsEmsHead));
        emlMessage.setNptsEmlImg(generateNptsEmlImg(ptsEmsHead));
        emlMessage.setNptsEmlExg(generateNptsEmlExg(ptsEmsHead));
        emlMessage.setNptsEmlConsume(generateNptsEmlConsume(ptsEmsHead));
        emlMessage.setStatus("");
        emlMessage.setOperCusRegCode(isNotEmpty(enterpriseInfo)?enterpriseInfo.getCustomsDeclarationCode(): "");
        emlMessage.setImportInfo(generateImportInfo(flag));
        return emlMessage;
    }

    /**
     * 生成手册表头
     * @param ptsEmsHead
     * @return
     */
    private static NptsEmlHead generateNptsEmlHead(PtsEmsHead ptsEmsHead) {
        NptsEmlHead nptsEmlHead = new NptsEmlHead();
        nptsEmlHead.setSeqNo(isNotBlank(ptsEmsHead.getSeqNo()) ? ptsEmsHead.getSeqNo() : "");
        nptsEmlHead.setEmlNo(isNotBlank(ptsEmsHead.getEmsNo()) ? ptsEmsHead.getEmsNo() : "");
//        nptsEmlHead.setChgTmsCnt(isNotEmpty(ptsEmsHead.getChgTmsCnt()) ? ptsEmsHead.getChgTmsCnt() : 0);
        nptsEmlHead.setEtpsPreentNo(isNotBlank(ptsEmsHead.getCopEmsNo()) ? ptsEmsHead.getCopEmsNo() : "");
        nptsEmlHead.setBizopEtpsno(isNotBlank(ptsEmsHead.getTradeCode()) ? ptsEmsHead.getTradeCode() : "");
        nptsEmlHead.setBizopEtpsSccd(isNotBlank(ptsEmsHead.getTradeSccd()) ? ptsEmsHead.getTradeSccd() : "");
        nptsEmlHead.setBizopEtpsNm(isNotBlank(ptsEmsHead.getTradeName()) ? ptsEmsHead.getTradeName() : "");
        nptsEmlHead.setRcvgdEtpsno(isNotBlank(ptsEmsHead.getOwnerCode()) ? ptsEmsHead.getOwnerCode() : "");
        nptsEmlHead.setRvsngdEtpsSccd(isNotBlank(ptsEmsHead.getOwnerSccd()) ? ptsEmsHead.getOwnerSccd() : "");
        nptsEmlHead.setRcvgdEtpsNm(isNotBlank(ptsEmsHead.getOwnerName()) ? ptsEmsHead.getOwnerName() : "");
        nptsEmlHead.setRcvgdEtpsDtcd(isNotBlank(ptsEmsHead.getRegionCode())?ptsEmsHead.getRegionCode():"");
        nptsEmlHead.setDclEtpsno(isNotBlank(ptsEmsHead.getDeclareCode()) ? ptsEmsHead.getDeclareCode() : "");
        nptsEmlHead.setDclEtpsSccd(isNotBlank(ptsEmsHead.getDeclareSccd()) ? ptsEmsHead.getDeclareSccd() : "");
        nptsEmlHead.setDclEtpsNm(isNotBlank(ptsEmsHead.getDeclareName()) ? ptsEmsHead.getDeclareName() : "");
        nptsEmlHead.setDclEtpsTypecd(isNotBlank(ptsEmsHead.getDeclareType()) ? ptsEmsHead.getDeclareType() : "");
        nptsEmlHead.setInputEtpsTypecd(isNotBlank(ptsEmsHead.getInputCode()) ? ptsEmsHead.getInputCode() : "");
        nptsEmlHead.setInputEtpsSccd(isNotBlank(ptsEmsHead.getInputCreditCode()) ? ptsEmsHead.getInputCreditCode() : "");
        nptsEmlHead.setInputEtpsNm(isNotBlank(ptsEmsHead.getInputName()) ? ptsEmsHead.getInputName() : "");
        // 类型代码和日期
        // 手账册类型(加贸手册类型：B-来料加工 C-进料加工;加贸账册类型：1-E账册 2-H账册 3-耗料 4-工单;物流账册类型:TW-账册;L-账册)
        // 1-E账册 2-H账册 3-耗料 4-工单 5-企业为单元 6-区外工单账册
        nptsEmlHead.setEmlType(isNotBlank(ptsEmsHead.getEmsType()) ? ptsEmsHead.getEmsType() : "");
        nptsEmlHead.setValidDate(isNotEmpty(ptsEmsHead.getEndDate()) ? DateUtil.format(ptsEmsHead.getEndDate(), DatePattern.PURE_DATE_PATTERN) : "");
        nptsEmlHead.setDclTypecd(isNotBlank(ptsEmsHead.getDclTypeCd()) ? ptsEmsHead.getDclTypeCd() : ""); // 1-备案申请 2-变更申请 3-注销申请
        nptsEmlHead.setApcretNo("");
        nptsEmlHead.setDclTime(isNotEmpty(ptsEmsHead.getDeclareDate()) ? DateUtil.format(ptsEmsHead.getDeclareDate(), DatePattern.PURE_DATE_PATTERN) : DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
        nptsEmlHead.setInputTime(isNotEmpty(ptsEmsHead.getByDate()) ? DateUtil.format(ptsEmsHead.getByDate(), DatePattern.PURE_DATE_PATTERN) : DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
        nptsEmlHead.setUcnsDclSegcd("1");
        nptsEmlHead.setRmk(isNotBlank(ptsEmsHead.getNote()) ? ptsEmsHead.getNote() : "");
        nptsEmlHead.setMasterCuscd(isNotBlank(ptsEmsHead.getMasterCustoms()) ? ptsEmsHead.getMasterCustoms() : "");
        nptsEmlHead.setSupvModecd(isNotBlank(ptsEmsHead.getTradeTypeCode())? ptsEmsHead.getTradeTypeCode() : "");
        nptsEmlHead.setImpCtrtNo(isNotBlank(ptsEmsHead.getContractNoI()) ? ptsEmsHead.getContractNoI() : "");
        nptsEmlHead.setExpCtrtNo(isNotBlank(ptsEmsHead.getContractNoE()) ? ptsEmsHead.getContractNoE() : "");
        nptsEmlHead.setReduNatrcd(isNotBlank(ptsEmsHead.getTaxTypeCode())? ptsEmsHead.getTaxTypeCode() : "");
        nptsEmlHead.setProduceTypecd(isNotBlank(ptsEmsHead.getProcessingType())? ptsEmsHead.getProcessingType() : "");
        nptsEmlHead.setImpexpPortcd(isNotBlank(ptsEmsHead.getIePort())? ptsEmsHead.getIePort() : "");
        nptsEmlHead.setStndbkBankcd(isNotBlank(ptsEmsHead.getManualPurpose())? ptsEmsHead.getManualPurpose() : "");
        nptsEmlHead.setImpCurrcd(isNotBlank(ptsEmsHead.getImportCurrency())? ptsEmsHead.getImportCurrency() : "");
        nptsEmlHead.setExpCurrcd(isNotBlank(ptsEmsHead.getExportCurrency())? ptsEmsHead.getExportCurrency() : "");
        nptsEmlHead.setDclTypeMarkcd("1");
        nptsEmlHead.setPauseImpexpMarkcd(isNotBlank(ptsEmsHead.getSuspendIe())?ptsEmsHead.getSuspendIe():"1");
        nptsEmlHead.setLinkMan(isNotBlank(ptsEmsHead.getAssociates())?ptsEmsHead.getAssociates():"");
        nptsEmlHead.setLinkManTel(isNotBlank(ptsEmsHead.getTelephone())?ptsEmsHead.getTelephone():"");
        nptsEmlHead.setCol1(isNotBlank(ptsEmsHead.getCol1())?ptsEmsHead.getCol1():"");
        return nptsEmlHead;
    }

    /**
     * 生成手册料件
     * @param ptsEmsHead
     * @return
     */
    private static List<NptsEmlImg> generateNptsEmlImg(PtsEmsHead ptsEmsHead) {
        if (isNotEmpty(ptsEmsHead.getEmsAimgList())) {
            List<NptsEmlImg> nptsEmlImgs = new ArrayList<>();
            for (PtsEmsAimg ptsEmsAimg : ptsEmsHead.getEmsAimgList()) {
                NptsEmlImg nptsEmlImg = new NptsEmlImg();
                nptsEmlImg.setSeqNo(isNotBlank(ptsEmsHead.getSeqNo()) ? ptsEmsHead.getSeqNo() : "");
                nptsEmlImg.setGdsSeqno(isNotEmpty(ptsEmsAimg.getGNo()) ? ptsEmsAimg.getGNo() : 1);
                nptsEmlImg.setMtpckEndprdTypecd("I");
                nptsEmlImg.setGdecd(isNotBlank(ptsEmsAimg.getCodet()) ? ptsEmsAimg.getCodet() : "");
                nptsEmlImg.setGdsMtno(isNotBlank(ptsEmsAimg.getCopGno()) ? ptsEmsAimg.getCopGno() : "");
                nptsEmlImg.setGdsNm(isNotBlank(ptsEmsAimg.getGName()) ? ptsEmsAimg.getGName() : "");
                nptsEmlImg.setEndprdGdsSpcfModelDesc(isNotBlank(ptsEmsAimg.getGModel()) ? ptsEmsAimg.getGModel() : "");
                nptsEmlImg.setDclUnitcd(isNotBlank(ptsEmsAimg.getUnit()) ? ptsEmsAimg.getUnit() : "");
                nptsEmlImg.setLawfUnitcd(isNotBlank(ptsEmsAimg.getUnit1()) ? ptsEmsAimg.getUnit1() : "");
                nptsEmlImg.setSecdLawfUnitcd(isNotBlank(ptsEmsAimg.getUnit2()) ? ptsEmsAimg.getUnit2() : "");
                nptsEmlImg.setDclUprcAmt(isNotEmpty(ptsEmsAimg.getDecPrice()) ? ptsEmsAimg.getDecPrice() : BigDecimal.ZERO);
                nptsEmlImg.setDclTotalAmt(isNotEmpty(ptsEmsAimg.getDclTotalAmt()) ? ptsEmsAimg.getDclTotalAmt() : BigDecimal.ZERO);
                nptsEmlImg.setDclCurrcd(isNotBlank(ptsEmsAimg.getCurr()) ? ptsEmsAimg.getCurr() : "");
                nptsEmlImg.setDclQty(isNotEmpty(ptsEmsAimg.getQty()) ? ptsEmsAimg.getQty() : BigDecimal.ZERO);
                nptsEmlImg.setLvyrlfModecd(isNotBlank(ptsEmsAimg.getDutyMode())?ptsEmsAimg.getDutyMode():"");
                nptsEmlImg.setAdjmtrMarkcd("1");
                nptsEmlImg.setModfMarkcd(isNotBlank(ptsEmsAimg.getModifyFlag()) ? ptsEmsAimg.getModifyFlag() : "3");
                nptsEmlImg.setRmk(isNotBlank(ptsEmsAimg.getNote()) ? ptsEmsAimg.getNote() : "");
                nptsEmlImg.setNatcd(isNotBlank(ptsEmsAimg.getCountryCode()) ? ptsEmsAimg.getCountryCode() : "");
                nptsEmlImg.setGdsAtrcd(isNotBlank(ptsEmsAimg.getAttributes())?ptsEmsAimg.getAttributes():"");
                nptsEmlImg.setSourceMarkcd(isNotBlank(ptsEmsAimg.getCol1())?ptsEmsAimg.getCol1():"");
                nptsEmlImgs.add(nptsEmlImg);
            }
            return nptsEmlImgs;
        }
        return null;
    }
    /**
     * 生成手册成品
     *
     */
    private static List<NptsEmlExg> generateNptsEmlExg(PtsEmsHead ptsEmsHead) {
        if (isNotEmpty(ptsEmsHead.getEmsAexgList())) {
            List<NptsEmlExg> nptsEmlExgs = new ArrayList<>();
            for (PtsEmsAexg ptsEmsAexg : ptsEmsHead.getEmsAexgList()) {
                NptsEmlExg nptsEmlExg = new NptsEmlExg();
                nptsEmlExg.setSeqNo(isNotBlank(ptsEmsHead.getSeqNo()) ? ptsEmsHead.getSeqNo() : "");
                nptsEmlExg.setGdsSeqno(isNotEmpty(ptsEmsAexg.getGNo()) ? ptsEmsAexg.getGNo() : 1);
                nptsEmlExg.setMtpckEndprdTypecd("E");
                nptsEmlExg.setGdecd(isNotBlank(ptsEmsAexg.getCodet()) ? ptsEmsAexg.getCodet() : "");
                nptsEmlExg.setGdsMtno(isNotBlank(ptsEmsAexg.getCopGno()) ? ptsEmsAexg.getCopGno() : "");
                nptsEmlExg.setGdsNm(isNotBlank(ptsEmsAexg.getGName()) ? ptsEmsAexg.getGName() : "");
                nptsEmlExg.setEndprdGdsSpcfModelDesc(isNotBlank(ptsEmsAexg.getGModel()) ? ptsEmsAexg.getGModel() : "");
                nptsEmlExg.setDclUnitcd(isNotBlank(ptsEmsAexg.getUnit()) ? ptsEmsAexg.getUnit() : "");
                nptsEmlExg.setLawfUnitcd(isNotBlank(ptsEmsAexg.getUnit1()) ? ptsEmsAexg.getUnit1() : "");
                nptsEmlExg.setSecdLawfUnitcd(isNotBlank(ptsEmsAexg.getUnit2()) ? ptsEmsAexg.getUnit2() : "");
                nptsEmlExg.setDclUprcAmt(isNotEmpty(ptsEmsAexg.getDecPrice()) ? ptsEmsAexg.getDecPrice() : BigDecimal.ZERO);
                nptsEmlExg.setDclTotalAmt(isNotEmpty(ptsEmsAexg.getDclTotalAmt()) ? ptsEmsAexg.getDclTotalAmt() : BigDecimal.ZERO);
                nptsEmlExg.setDclCurrcd(isNotBlank(ptsEmsAexg.getCurr()) ? ptsEmsAexg.getCurr() : "");
                nptsEmlExg.setDclQty(isNotEmpty(ptsEmsAexg.getQty()) ? ptsEmsAexg.getQty() : BigDecimal.ZERO);
                nptsEmlExg.setLvyrlfModecd(isNotBlank(ptsEmsAexg.getDutyMode())?ptsEmsAexg.getDutyMode():"");
                nptsEmlExg.setModfMarkcd(isNotBlank(ptsEmsAexg.getModifyFlag()) ? ptsEmsAexg.getModifyFlag() : "3");
                nptsEmlExg.setRmk(isNotBlank(ptsEmsAexg.getNote()) ? ptsEmsAexg.getNote() : "");
                nptsEmlExg.setNatcd(isNotBlank(ptsEmsAexg.getCountryCode()) ? ptsEmsAexg.getCountryCode() : "");
                nptsEmlExg.setGdsAtrcd(isNotBlank(ptsEmsAexg.getAttributes())?ptsEmsAexg.getAttributes():"");
                nptsEmlExgs.add(nptsEmlExg);
            }
            return nptsEmlExgs;
        }
        return null;
    }
    /**
     * 生成手册单损耗
     */
    private static List<NptsEmlConsume> generateNptsEmlConsume(PtsEmsHead ptsEmsHead) {
        if (isNotEmpty(ptsEmsHead.getEmsCmList())) {
            List<NptsEmlConsume> nptsEmlConsumes = new ArrayList<>();
            for (PtsEmsCm ptsEmsCm : ptsEmsHead.getEmsCmList()) {
                NptsEmlConsume nptsEmlConsume = new NptsEmlConsume();
                nptsEmlConsume.setSeqNo(isNotBlank(ptsEmsHead.getSeqNo()) ? ptsEmsHead.getSeqNo() : "");
                nptsEmlConsume.setGseqNo(null!=ptsEmsCm.getGNo()?ptsEmsCm.getGNo():1);
                nptsEmlConsume.setEndprdSeqno(isNotEmpty(ptsEmsCm.getExgNo()) ? ptsEmsCm.getExgNo() : 1);
                nptsEmlConsume.setEndprdGdsMtno(isNotBlank(ptsEmsCm.getExgCopGno())?ptsEmsCm.getExgCopGno():"");
                nptsEmlConsume.setEndprdGdecd(isNotBlank(ptsEmsCm.getExgHscode())?ptsEmsCm.getExgHscode():"");
                nptsEmlConsume.setEndprdGdsNm(isNotBlank(ptsEmsCm.getExgGname())?ptsEmsCm.getExgGname():"");
                nptsEmlConsume.setMtpckSeqno(isNotEmpty(ptsEmsCm.getImgNo()) ? ptsEmsCm.getImgNo() : 1);
                nptsEmlConsume.setMtpckGdsMtno(isNotBlank(ptsEmsCm.getImgCopGno())?ptsEmsCm.getImgCopGno():"");
                nptsEmlConsume.setMtpckGdecd(isNotBlank(ptsEmsCm.getImgHscode())?ptsEmsCm.getImgHscode():"");
                nptsEmlConsume.setMtpckGdsNm(isNotBlank(ptsEmsCm.getImgGname())?ptsEmsCm.getImgGname():"");
                nptsEmlConsume.setUcnsVerno(isNotBlank(ptsEmsCm.getUcnsverno()) ? ptsEmsCm.getUcnsverno() : "");
                nptsEmlConsume.setUcnsQty("");
                nptsEmlConsume.setNetUseupQty(isNotEmpty(ptsEmsCm.getDecCm()) ? ptsEmsCm.getDecCm() : BigDecimal.ZERO);
                nptsEmlConsume.setTgblLossRate(isNotEmpty(ptsEmsCm.getDecDm()) ? ptsEmsCm.getDecDm() : BigDecimal.ZERO);
                nptsEmlConsume.setIntgbLossRate(isNotEmpty(ptsEmsCm.getDecDm()) ? ptsEmsCm.getDecDm() : BigDecimal.ZERO);
                nptsEmlConsume.setUcnsDclStucd(isNotBlank(ptsEmsHead.getSeqNo()) ? "2" : "1");
                nptsEmlConsume.setModfMarkcd(isNotBlank(ptsEmsCm.getModifyFlag()) ? ptsEmsCm.getModifyFlag() : "3");
                nptsEmlConsume.setBondMtpckPrpr(null!=ptsEmsCm.getProportionOfBondedMaterials()?ptsEmsCm.getProportionOfBondedMaterials():BigDecimal.ZERO);
                nptsEmlConsume.setRmk(isNotBlank(ptsEmsCm.getNote()) ? ptsEmsCm.getNote() : "");
                nptsEmlConsume.setEtpsExeMarkcd(isNotBlank(ptsEmsCm.getEnterpriseExecutionFlag()) ? ptsEmsCm.getEnterpriseExecutionFlag() : "1");
                nptsEmlConsumes.add(nptsEmlConsume);
            }
            return nptsEmlConsumes;
        }
        return null;
    }
    /**
     * 生成导入信息
     *
     */
    private static ImportInfo generateImportInfo(String flag) {
        ImportInfo importInfo = new ImportInfo();
        importInfo.setMessageType("NPTS001");
        importInfo.setOpType(flag); //导入操作类型：S(暂存)、D(申报)
        importInfo.setFileSize("0");
        return importInfo;
    }


}
