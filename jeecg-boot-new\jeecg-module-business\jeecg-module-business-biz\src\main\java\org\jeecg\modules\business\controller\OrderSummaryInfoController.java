package org.jeecg.modules.business.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.DomesticSuppliersInfoMapper;
import org.jeecg.modules.business.mapper.OrderInfoMapper;
import org.jeecg.modules.business.mapper.OrderSummaryInfoMapper;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.EmailUtil;
import org.jeecg.modules.business.util.PrintUtil;
import org.jeecg.modules.business.vo.*;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.StyledEditorKit;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isNotBlank;

/**
 * @Description: 订单信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-17
 * @Version: V1.0
 */
@Api(tags="订单信息表")
@RestController
@RequestMapping("/export/OrderSummaryInfo")
@Slf4j
public class OrderSummaryInfoController extends JeecgController<OrderSummaryInfoBiz, IOrderSummaryInfoService> {

	@Autowired
	private IOrderSummaryInfoService orderSummaryInfoService;
	@Autowired
	private IOverseasPayerInfoService overseasPayerInfoServicenfo;
	@Autowired
	private IErpCustomsPortsService erpCustomsPortsService;
	@Autowired
	private IErpCurrenciesService erpCurrenciesService;
	@Autowired
	private IErpCountriesService erpCountriesService;
	@Autowired
	private DomesticSuppliersInfoMapper domesticSuppliersInfoMapper;
	@Autowired
	private OrderSummaryInfoMapper orderSummaryInfoMapper;
	@Autowired
	private OrderInfoMapper orderInfoMapper;
	@Value("${jeecg.path.upload}")
	private String upLoadPath;
	/**
	 * 分页列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "订单信息表-分页列表查询")
	@ApiOperation(value="订单信息表-分页列表查询", notes="订单信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OrderSummaryInfoBiz orderSummaryInfoBiz,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) throws ParseException {
		int remindStatus = 0;
		int orderStatus = 0;
		String ieFlag=orderSummaryInfoBiz.getIeFlag();
		if(orderSummaryInfoBiz.getRemindStatus() !=null){
			remindStatus = orderSummaryInfoBiz.getRemindStatus();
			// 首页提醒用 提醒状态
			if(orderSummaryInfoBiz.getRemindStatus() == 1){
				orderSummaryInfoBiz = new OrderSummaryInfoBiz();
			}
		}

		if(orderSummaryInfoBiz.getOrderStatus() != null && orderSummaryInfoBiz.getOrderStatus() == 1){
			orderStatus = orderSummaryInfoBiz.getOrderStatus();
			orderSummaryInfoBiz.setOrderStatus(null);
		}
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		Page<OrderSummaryInfoBiz> page = new Page<OrderSummaryInfoBiz>(pageNo, pageSize);
		orderSummaryInfoBiz.setRemindStatus(null);
		QueryWrapper<OrderSummaryInfoBiz> queryWrapper = QueryGenerator.initQueryWrapper(orderSummaryInfoBiz, req.getParameterMap());
		LambdaQueryWrapper<OrderSummaryInfoBiz> query = queryWrapper.lambda();
		if(orderStatus == 1){
			query.in(OrderSummaryInfoBiz::getOrderStatus,1,2);
		}
		// 首页提醒用 提醒状态
		if(remindStatus == 1){
			query.ne(OrderSummaryInfoBiz::getRemindStatus,0);
		}else if(remindStatus == 2){
			//查正常的
			query.eq(OrderSummaryInfoBiz::getRemindStatus,0);
		}else if(remindStatus == 3){
            //查超期未收齐的
			query.and(tmp ->tmp.eq(OrderSummaryInfoBiz::getRemindStatus,1).or().eq(OrderSummaryInfoBiz::getRemindStatus,3));
		}else if(remindStatus == 4){
			//订单负利润
			query.eq(OrderSummaryInfoBiz::getRemindStatus,8);
		}else if(remindStatus == 5){
			//转款未完成
			query.eq(OrderSummaryInfoBiz::getRemindStatus,12);
		}
		// 印刷区间
		if(orderSummaryInfoBiz.getStartDate() != null && orderSummaryInfoBiz.getEndDate() != null){
			query.between(OrderSummaryInfoBiz::getCloseDate,orderSummaryInfoBiz.getStartDate(),orderSummaryInfoBiz.getEndDate());
			query.orderByDesc(OrderSummaryInfoBiz::getRemindStatus);
			query.orderByAsc(OrderSummaryInfoBiz::getCreateTime);
		}else{
			query.orderByDesc(OrderSummaryInfoBiz::getRemindStatus);
			query.orderByDesc(OrderSummaryInfoBiz::getCreateTime);
		}
		if(StringUtils.isBlank(orderSummaryInfoBiz.getIeFlag())&&StringUtils.isNotBlank(ieFlag)){
			query.eq(OrderSummaryInfoBiz::getIeFlag,ieFlag);
		}
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.getOrderList(page, query);
		return Result.OK(pageList);
	}

	/**
	 * 订单状态跟踪-分页列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "订单状态跟踪-分页列表查询")
	@ApiOperation(value="订单状态跟踪-分页列表查询", notes="订单状态跟踪-分页列表查询")
	@GetMapping(value = "/listForRisks")
	public Result<?> queryPageListForRisks(OrderSummaryInfoBiz orderSummaryInfoBiz,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) throws ParseException {
		if (isNotEmpty(orderSummaryInfoBiz.getOrderStatus()) && -1 == orderSummaryInfoBiz.getOrderStatus()) {
			orderSummaryInfoBiz.setOrderStatus(null);
		}
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		Page<OrderSummaryInfoBiz> page = new Page<>(pageNo, pageSize);
//		QueryWrapper<OrderSummaryInfoBiz> queryWrapper = QueryGenerator.initQueryWrapper(orderSummaryInfoBiz, req.getParameterMap());
//		LambdaQueryWrapper<OrderSummaryInfoBiz> query = queryWrapper.lambda();
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.queryPageListForRisks(page, orderSummaryInfoBiz);
		if ("I".equals(orderSummaryInfoBiz.getIeFlag())) {
			if (isNotEmpty(pageList.getRecords())) {
				for (OrderSummaryInfoBiz infoBiz : pageList.getRecords()) {
					DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoMapper.selectById(infoBiz.getOverseasPayerInfoId());
					if (isNotEmpty(domesticSuppliersInfo)) {
						infoBiz.setOverseasPayerInfoId(domesticSuppliersInfo.getSuppliersFullName());
					}
				}
			}
		}
		return Result.OK(pageList);
	}

	/**
	 * 风险预警-分页列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "风险预警-分页列表查询")
	@ApiOperation(value="风险预警-分页列表查询", notes="风险预警-分页列表查询")
	@GetMapping(value = "/listOrderRisksWarning")
	public Result<?> queryPageListOrderRisksWarning(OrderSummaryInfoBiz orderSummaryInfoBiz,
										   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
										   HttpServletRequest req) throws ParseException {
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		Page<OrderSummaryInfoBiz> page = new Page<>(pageNo, pageSize);
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.queryPageListOrderRisksWarning(page, orderSummaryInfoBiz);
		return Result.OK(pageList);
	}

	/**
	 * 高风险用户信息-分页列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "高风险用户信息-分页列表查询")
	@ApiOperation(value="高风险用户信息-分页列表查询", notes="高风险用户信息-分页列表查询")
	@GetMapping(value = "/listOrderHighRisks")
	public Result<?> queryPageListOrderHighRisks(OrderSummaryInfoBiz orderSummaryInfoBiz,
													@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													HttpServletRequest req) throws ParseException {
//		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		Page<OrderSummaryInfoBiz> page = new Page<>(pageNo, pageSize);
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.queryPageListOrderHighRisks(page, orderSummaryInfoBiz);
		return Result.OK(pageList);
	}

	/**
	 * 各个企业出口和进口订单数量
	 * @param request
	 * @return
	 */
	@AutoLog(value = "各个企业出口和进口订单数量")
	@ApiOperation(value="各个企业出口和进口订单数量", notes="各个企业出口和进口订单数量")
	@GetMapping(value = "/listCustomerOrderNums")
	public Result<?> listCustomerOrderNums(HttpServletRequest request) {
		List<OrderCharts> orderChartsList = orderSummaryInfoService.listCustomerOrderNums();
		return Result.OK(orderChartsList);
	}

	/**
	 * 各个企业出口和进口订单金额
	 * @param orderSummaryInfoBiz
	 * @return
	 */
	@AutoLog(value = "各个企业出口和进口订单金额")
	@ApiOperation(value="各个企业出口和进口订单金额", notes="各个企业出口和进口订单金额")
	@PostMapping(value = "/listCustomerOrderAmount")
	public Result<?> listCustomerOrderAmount(@RequestBody OrderSummaryInfoBiz orderSummaryInfoBiz) {
		List<OrderCharts> orderChartsList = orderSummaryInfoService.listCustomerOrderAmount(orderSummaryInfoBiz);
		return Result.OK(orderChartsList);
	}

	/**
	 * 各个企业出口和进口国别订单统计
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@AutoLog(value = "各个企业出口和进口国别订单统计")
	@ApiOperation(value="各个企业出口和进口国别订单统计", notes="各个企业出口和进口国别订单统计")
	@GetMapping(value = "/listCustomerOrderTradingCountry")
	public Result<?> listCustomerOrderTradingCountry(OrderSummaryInfoBiz orderSummaryInfoBiz,
													 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		Page<OrderSummaryInfoBiz> page = new Page<>(pageNo, pageSize);
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.listCustomerOrderTradingCountry(page, orderSummaryInfoBiz);
		return Result.OK(pageList);
	}

	/**
	 * 打印列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "订单信息表-打印列表查询")
	@ApiOperation(value="订单信息表-打印列表查询", notes="订单信息表-打印列表查询")
	@GetMapping(value = "/printList")
	public Result<?> queryPagePrintList(OrderSummaryInfoBiz orderSummaryInfoBiz,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) throws ParseException {
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
		orderSummaryInfoBiz.setReceiveMoneyFlag("E".equals(orderSummaryInfoBiz.getIeFlag())?1:null);
		orderSummaryInfoBiz.setPayMoneyFlag(1);
		orderSummaryInfoBiz.setOrderStatus(99);
		Page<OrderSummaryInfoBiz> page = new Page<OrderSummaryInfoBiz>(pageNo, pageSize);
		IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.getOrderPrintList(page, orderSummaryInfoBiz);
		return Result.OK(pageList);
	}

	/**
	 * 导出代理费列表查询
	 *
	 * @param orderSummaryInfoBiz
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "订单信息表-导出代理费列表查询")
	@ApiOperation(value="订单信息表-导出代理费列表查询", notes="订单信息表-导出代理费列表查询")
	@GetMapping(value = "/printDaiLiFeiList")
	public Result<?> printDaiLiFeiList(OrderDaiLiFeiExcelVO orderSummaryInfoBiz,
									   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
									   HttpServletRequest req) throws ParseException {
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));

		Page<OrderDaiLiFeiExcelVO> page = new Page<>(pageNo, pageSize);
		IPage<OrderDaiLiFeiExcelVO> pageList = orderSummaryInfoMapper.listPayMentDetailByQuery(page, orderSummaryInfoBiz);
		return Result.OK(pageList);
	}
	/**
	 * 导出excel
	 *
	 * @param request
	 * @param
	 */
	@RequestMapping(value = "/exportDaiLiFei")
	public ModelAndView exportDaiLiFei(HttpServletRequest request, OrderDaiLiFeiExcelVO orderSummaryInfoBiz) {
		// Step.1 组装查询条件
		orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// Step.2 获取导出数据
		Page<OrderDaiLiFeiExcelVO> page = new Page<>(1, 99999);
		IPage<OrderDaiLiFeiExcelVO> pageList = orderSummaryInfoMapper.listPayMentDetailByQuery(page, orderSummaryInfoBiz);

		// Step.3 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "月度代理费详情单");
		mv.addObject(NormalExcelConstants.CLASS, OrderDaiLiFeiExcelVO.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("月度代理费详情单", "导出人:" + sysUser.getRealname());
		exportParams.setImageBasePath(upLoadPath);
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, pageList.getRecords());
		return mv;
	}


	/**
	 * 打印列表查询(xls打印)
	 *
	 * @param inMap
	 * @return
	 */
	@RequestMapping(value = "/exportXls")
	public void queryPagePrintXlsList(@RequestParam Map<String,Object> inMap,HttpServletResponse response) throws IOException {
		String fileName = inMap.get("fileName") == null ? "" : (String)inMap.get("fileName");
		OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
		HashMap<String, Object> map = null;
		String fileXlsName = "";
		LambdaQueryWrapper<ErpCurrencies> queryWrapperCurrency = new LambdaQueryWrapper<>();
		queryWrapperCurrency.eq(ErpCurrencies::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list(queryWrapperCurrency);
		LambdaQueryWrapper<OverseasPayerInfo> queryWrapperOverseasPayer = new LambdaQueryWrapper<>();
		queryWrapperOverseasPayer.eq(OverseasPayerInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<OverseasPayerInfo> overseasPayerInfoList = overseasPayerInfoServicenfo.list(queryWrapperOverseasPayer);
		if("月度出口代理费结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfoFee";
			map = new PrintUtil().queryPagePrintPutMap(orderSummaryInfoBiz,list);
		}else if("月度结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("财务结算明细".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo2_E";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("财务运营报表".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo1_E";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("出口结算单".equals(fileName)){
			String out = inMap.get("orderSummaryInfoBiz") == null ? "" : (String)inMap.get("orderSummaryInfoBiz");
			Object succesResponse = JSON.parse(out);
			Map<String,Object> printMap = (Map)succesResponse;
			try {
				orderSummaryInfoBiz.setId((String)printMap.get("id"));
				orderSummaryInfoBiz.setTenantId((Long) printMap.get("tenantId"));
			} catch (Exception e) {
				e.printStackTrace();
				log.error("忽略！为了财务工作台！！" + e.getMessage());
			}
			fileXlsName = "SummaryInfo";
			List<HashMap> orderPrintInAmountXlsList = orderSummaryInfoService.getOrderPrintInAmountXlsList(orderSummaryInfoBiz);
			List<HashMap> orderPrintOutAmountXlsList = orderSummaryInfoService.getOrderPrintOutAmountXlsList(orderSummaryInfoBiz);
			map = new PrintUtil().queryPagePrint3PutMap(printMap,orderPrintInAmountXlsList,orderPrintOutAmountXlsList);
		}
		new PrintUtil().listPrintXLS(map,fileXlsName,fileName,false,null,response);
	}

	/**
	 * 打印列表查询(xls打印)
	 *
	 * @param inMap
	 * @return
	 */
	@RequestMapping(value = "/exportXls2")
	public void queryPagePrintXlsList2(@RequestParam Map<String,Object> inMap,HttpServletResponse response) throws IOException {
		String fileName = inMap.get("fileName") == null ? "" : (String)inMap.get("fileName");
		OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
		HashMap<String, Object> map = null;
		String fileXlsName = "";
		LambdaQueryWrapper<ErpCurrencies> queryWrapperCurrency = new LambdaQueryWrapper<>();
		queryWrapperCurrency.eq(ErpCurrencies::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list(queryWrapperCurrency);
		LambdaQueryWrapper<OverseasPayerInfo> queryWrapperOverseasPayer = new LambdaQueryWrapper<>();
		queryWrapperOverseasPayer.eq(OverseasPayerInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<OverseasPayerInfo> overseasPayerInfoList = overseasPayerInfoServicenfo.list(queryWrapperOverseasPayer);
		if("月度出口代理费结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfoFee";
			map = new PrintUtil().queryPagePrintPutMap(orderSummaryInfoBiz,list);
		}else if("月度结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("财务结算明细".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo2";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("财务运营报表".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			fileXlsName = "OrderSummaryInfo1";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("出口结算单".equals(fileName)){
			String out = inMap.get("orderSummaryInfoBiz") == null ? "" : (String)inMap.get("orderSummaryInfoBiz");
			Object succesResponse = JSON.parse(out);
			Map<String,Object> printMap = (Map)succesResponse;
			try {
				orderSummaryInfoBiz.setId((String)printMap.get("id"));
				orderSummaryInfoBiz.setTenantId((Long) printMap.get("tenantId"));
			} catch (Exception e) {
				e.printStackTrace();
				log.error("忽略！为了财务工作台！！" + e.getMessage());
			}
			fileXlsName = "SummaryInfo";
			List<HashMap> orderPrintInAmountXlsList = orderSummaryInfoService.getOrderPrintInAmountXlsList(orderSummaryInfoBiz);
			List<HashMap> orderPrintOutAmountXlsList = orderSummaryInfoService.getOrderPrintOutAmountXlsList(orderSummaryInfoBiz);
			map = new PrintUtil().queryPagePrint3PutMap(printMap,orderPrintInAmountXlsList,orderPrintOutAmountXlsList);
		}
		new PrintUtil().listPrintXLS(map,fileXlsName,fileName,false,null,response);
	}

	/**
	 * 运营管理-统计分析导出Excel报表
	 *
	 * @param inMap
	 * @param response
	 * @return void
	 * <AUTHOR>
	 * @date 2023/9/18 19:03
	 */
	@RequestMapping(value = "/exportStatisticalAnalysisXls")
	public void exportStatisticalAnalysisXls(@RequestParam Map<String,Object> inMap, HttpServletResponse response) throws IOException {
		String fileName = inMap.get("fileName") == null ? "" : (String)inMap.get("fileName");
		HashMap<String, Object> map = null;
		String fileXlsName = "";
		if("订单统计表".equals(fileName)){
			List<OrderCharts> orderChartsList = orderSummaryInfoService.listCustomerOrderNums();
			fileXlsName = "OrderStatistics";
			map = new PrintUtil().queryPagePrintPutMap(orderChartsList);
		}else if("贸易额统计表".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			String tenantId = inMap.get("tenantId") == null ? "" : (String)inMap.get("tenantId");
			OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(isNotBlank(tenantId)? Long.valueOf(tenantId) : null);
			List<OrderCharts> orderChartsList = orderSummaryInfoService.listCustomerOrderAmount(orderSummaryInfoBiz);
			fileXlsName = "TradeStatistics";
			map = new PrintUtil().queryTradeStatisticsPutMap(orderSummaryInfoBiz, orderChartsList);
		}else if("进出口国别统计表".equals(fileName)){
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			Map<String, String> countriesMap = new HashMap<>();
			for(ErpCountries erpCountries:erpCountriesList){
				countriesMap.put(erpCountries.getCode(), erpCountries.getName());
			}
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			String tenantId = inMap.get("tenantId") == null ? "" : (String)inMap.get("tenantId");
			OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(isNotBlank(tenantId)? Long.valueOf(tenantId) : null);
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			Page<OrderSummaryInfoBiz> page = new Page<>(1, 99999999);
			IPage<OrderSummaryInfoBiz> pageList = orderSummaryInfoService.listCustomerOrderTradingCountry(page, orderSummaryInfoBiz);
			fileXlsName = "ExportingCountries";
			map = new PrintUtil().queryExportingCountriesPutMap(orderSummaryInfoBiz, pageList, countriesMap);
		}
		new PrintUtil().listPrintXLS(map,fileXlsName,fileName,false,null,response);
	}

	/**
	 * 打印列表查询(pdf打印)
	 *
	 * @param inMap
	 * @return
	 */
	@RequestMapping(value = "/exportPdf")
	public void queryPagePrintPdfList(@RequestParam Map<String,Object> inMap, HttpServletResponse response)  {
		String fileName = inMap.get("fileName") == null ? "" : (String)inMap.get("fileName");
		OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
		HashMap<String, Object> map = null;
		String filePdfName = "";
		//最大行数
		int listMax = 0;
		LambdaQueryWrapper<ErpCurrencies> queryWrapperCurrency = new LambdaQueryWrapper<>();
		queryWrapperCurrency.eq(ErpCurrencies::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list(queryWrapperCurrency);
		LambdaQueryWrapper<OverseasPayerInfo> queryWrapperOverseasPayer = new LambdaQueryWrapper<>();
		queryWrapperOverseasPayer.eq(OverseasPayerInfo::getDelFlag, CommonConstant.DEL_FLAG_0);
		List<OverseasPayerInfo> overseasPayerInfoList = overseasPayerInfoServicenfo.list(queryWrapperOverseasPayer);
		if("月度出口代理费结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
			}
			listMax = 23;
			filePdfName = "OrderSummaryInfoFee";
			map = new PrintUtil().queryPagePrintPutMap(orderSummaryInfoBiz,list);
		}else if("月度结算单".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			listMax = 23;
			filePdfName = "OrderSummaryInfo";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("财务结算明细".equals(fileName)){
			String startDate = inMap.get("startDate") == null ? "" : (String)inMap.get("startDate");
			String endDate = inMap.get("endDate") == null ? "" : (String)inMap.get("endDate");
			orderSummaryInfoBiz.setStartDate(startDate);
			orderSummaryInfoBiz.setEndDate(endDate);
			orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
			orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
			String ieFlag = inMap.get("ieFlag") == null ? "" : (String)inMap.get("ieFlag");
			orderSummaryInfoBiz.setIeFlag(ieFlag);
//			orderSummaryInfoBiz.setReceiveMoneyFlag(1);
			orderSummaryInfoBiz.setPayMoneyFlag(1);
			List<OrderSummaryInfoBiz> list = orderSummaryInfoService.getOrderPrintXlsList(orderSummaryInfoBiz);
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ErpCustomsPorts::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list(queryWrapper);
			LambdaQueryWrapper<ErpCountries> queryWrapper2 = new LambdaQueryWrapper<>();
			queryWrapper2.eq(ErpCountries::getDelFlag, CommonConstant.DEL_FLAG_0);
			List<ErpCountries> erpCountriesList = erpCountriesService.list(queryWrapper2);
			for(OrderSummaryInfoBiz orderSummaryInfo:list){
				for(ErpCustomsPorts erpCustomsPorts:erpCustomsPortsList){
					if(orderSummaryInfo.getExitClearance() != null && orderSummaryInfo.getExitClearance().equals(erpCustomsPorts.getCustomsPortCode())){
						orderSummaryInfo.setExitClearance(erpCustomsPorts.getName());
					}
				}
				for(ErpCurrencies erpCurrencies:erpCurrenciesList){
					if(orderSummaryInfo.getCustomsDeclarationCurrency() != null && orderSummaryInfo.getCustomsDeclarationCurrency().equals(erpCurrencies.getCode())){
						orderSummaryInfo.setCustomsDeclarationCurrency(erpCurrencies.getCurrency());
					}
				}
				for(OverseasPayerInfo overseasPayerInfo:overseasPayerInfoList){
					if(orderSummaryInfo.getOverseasPayerInfoId() != null && orderSummaryInfo.getOverseasPayerInfoId().equals(overseasPayerInfo.getId())){
						orderSummaryInfo.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					}
				}
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderSummaryInfo.getTradingCountry() !=null && orderSummaryInfo.getTradingCountry().equals(erpCountries.getCode())){
						orderSummaryInfo.setTradingCountry(erpCountries.getName());
					}
				}
			}
			filePdfName = "OrderSummaryInfo2";
			map = new PrintUtil().queryPagePrint2PutMap(orderSummaryInfoBiz,list);
		}else if("出口结算单".equals(fileName)){
			String out = inMap.get("orderSummaryInfoBiz") == null ? "" : (String)inMap.get("orderSummaryInfoBiz");
			Object succesResponse = JSON.parse(out);
			Map<String,Object> printMap = (Map)succesResponse;
			orderSummaryInfoBiz.setId((String)printMap.get("id"));
			orderSummaryInfoBiz.setTenantId((Long) printMap.get("tenantId"));
			listMax = 23;
			filePdfName = "SummaryInfo";
			List<HashMap> orderPrintInAmountXlsList = orderSummaryInfoService.getOrderPrintInAmountXlsList(orderSummaryInfoBiz);
			List<HashMap> orderPrintOutAmountXlsList = orderSummaryInfoService.getOrderPrintOutAmountXlsList(orderSummaryInfoBiz);
			map = new PrintUtil().queryPagePrint3PutMap(printMap,orderPrintInAmountXlsList,orderPrintOutAmountXlsList);
		}
		new PrintUtil().listPrintPdf(map,filePdfName,fileName,false,listMax,null,response);
	}
    /**
     * 打印列表查询（收汇明细）
     *
     * @param orderSummaryInfoBiz
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "订单信息表-收汇明细")
    @ApiOperation(value="订单信息表-收汇明细", notes="订单信息表-收汇明细")
    @GetMapping(value = "/printInAmountList")
    public Result<?> queryPagePrintInAmountList(OrderSummaryInfoBiz orderSummaryInfoBiz,
                                            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                            @RequestParam(name="pageSize", defaultValue="500") Integer pageSize,
                                            HttpServletRequest req) throws ParseException {
        orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
        orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
        Page<HashMap> page  = new Page<>(pageNo, pageSize);
        IPage<HashMap> pageList = orderSummaryInfoService.getOrderPrintInAmountList(page, orderSummaryInfoBiz);
        return Result.OK(pageList);
    }

    /**
     * 打印列表查询（支出明细）
     *
     * @param orderSummaryInfoBiz
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "订单信息表-收汇明细")
    @ApiOperation(value="订单信息表-收汇明细", notes="订单信息表-收汇明细")
    @GetMapping(value = "/printOutAmountList")
    public Result<?> queryPagePrintOutAmountList(OrderSummaryInfoBiz orderSummaryInfoBiz,
                                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                @RequestParam(name="pageSize", defaultValue="500") Integer pageSize,
                                                HttpServletRequest req) throws ParseException {
        orderSummaryInfoBiz.setTenantId(Long.valueOf(TenantContext.getTenant()));
        orderSummaryInfoBiz.setDelFlag(CommonConstant.DEL_FLAG_0);
        Page<HashMap> page  = new Page<>(pageNo, pageSize);
        IPage<HashMap> pageList = orderSummaryInfoService.getOrderPrintOutAmountList(page, orderSummaryInfoBiz);
        return Result.OK(pageList);
    }

	/**
	 * 数据备份（财务工作台）
	 *
	 * @return
	 */
	@AutoLog(value = "数据备份（财务工作台）")
	@ApiOperation(value="数据备份（财务工作台）", notes="数据备份（财务工作台）")
	@GetMapping(value = "/dataBackup")
	public Result<?> dataBackup(HttpServletRequest req) {
		Date date = new Date();
		String time = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(date);
//		File folder = new File(upLoadPath + "/backup/");
		File folder = new File("/Users/<USER>/Downloads" + "/backup/");
		if (!folder.exists()) {// 如果文件夹不存在
			folder.mkdirs();// 创建文件夹
		}
		String backupFilePath = folder + "/db_backup_" + time + ".dmp"; // 备份的路径地址
		String jdbcUrl = "jdbc:oracle:thin:@***********:1521:orcl"; // 你的Oracle数据库连接URL
		String username = "c##trade";
		String password = "trade123";

		try {
			Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
			// 执行备份命令
			String expCommand = "exp " + username + "/" + password + "@" + jdbcUrl + " file=" + backupFilePath;
			log.info("备份oracle执行的命令是：" + expCommand);
			Process process = Runtime.getRuntime().exec(expCommand);
			int exitCode = process.waitFor();
			if (exitCode == 0) {
				System.out.println("数据库备份成功！");
			} else {
				System.err.println("数据库备份失败！");
			}
			// 关闭数据库连接
			connection.close();
		} catch (Exception e) {
			log.error("备份数据出现异常：" + e.getMessage());
			e.printStackTrace();
			return Result.error("备份数据出现异常！服务器缺少Oracle所需的依赖，请检查是否已安装Oracle客户端或环境变量是否配置正确！");
		}
		return Result.OK("备份成功");
	}

	@AutoLog(value = "订单信息表-收汇明细")
	@ApiOperation(value="订单信息表-收汇明细", notes="订单信息表-收汇明细")
	@GetMapping(value = "/queryOverseasOrderStatistics")
	public Result<?> queryOverseasOrderStatistics(OverseasOrderStatisticsVO overseasOrderStatisticsVO,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="500") Integer pageSize,
												  HttpServletRequest req) throws ParseException {
		overseasOrderStatisticsVO.setTenantId(Long.valueOf(TenantContext.getTenant()));
		Page<OverseasOrderStatisticsVO> page  = new Page<>(pageNo, pageSize);
		IPage<OverseasOrderStatisticsVO> pageList = orderSummaryInfoService.getOverseasOrderStatistics(page,overseasOrderStatisticsVO);
		return Result.OK(pageList);
	}

	@AutoLog(value = "订单分类状态统计")
	@ApiOperation(value="订单分类状态统计", notes="订单分类状态统计")
	@GetMapping(value = "/queryOrderStatistics")
	public Result<?> queryOrderStatistics(OrderStatisticsVO orderStatisticsVO,
										  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										  @RequestParam(name="pageSize", defaultValue="500") Integer pageSize,
										  HttpServletRequest req) throws ParseException {
		orderStatisticsVO.setTenantId(Long.valueOf(TenantContext.getTenant()));
		Page<OrderStatisticsVO> page  = new Page<>(pageNo, pageSize);
		IPage<OrderStatisticsVO> pageList = orderSummaryInfoMapper.getOrderStatistics(page,orderStatisticsVO);
		return Result.OK(pageList);
	}

	@AutoLog(value = "订单同环比分析")
	@ApiOperation(value="订单同环比分析", notes="订单同环比分析")
	@GetMapping(value = "/queryOrderTHBStatistics")
	public Result<?> queryOrderTHBStatistics(OrderStatisticsVO orderStatisticsVO,
										  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										  @RequestParam(name="pageSize", defaultValue="500") Integer pageSize,
										  HttpServletRequest req) throws ParseException {
		orderStatisticsVO.setTenantId(Long.valueOf(TenantContext.getTenant()));
		Page<OrderStatisticsVO> page  = new Page<>(pageNo, pageSize);
		IPage<OrderStatisticsVO> pageList = orderSummaryInfoMapper.queryOrderTHBStatistics(page,orderStatisticsVO);
		Map<String,Integer> dateCountMap=new HashMap<>();
		for(OrderStatisticsVO orderStatisticsVO1:pageList.getRecords()){
			dateCountMap.put(orderStatisticsVO1.getCreateTime(),orderStatisticsVO1.getOrderSum());
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		for(OrderStatisticsVO orderStatisticsVO1:pageList.getRecords()){
			//计算同比率 rate = (本年数据 - 前一年数据) / 前一年数据
			Calendar c = Calendar.getInstance();//获得一个日历的实例
			Date date = sdf.parse(orderStatisticsVO1.getCreateTime());
			c.setTime(date);
            c.add(Calendar.YEAR,-1);
			String tbDate = sdf.format(c.getTime());//对比去年的数据
			Integer tbOrderSum= dateCountMap.getOrDefault(tbDate, 0);
			BigDecimal tbOrderSumBd=BigDecimal.valueOf(tbOrderSum);
			BigDecimal orderSumBd=BigDecimal.valueOf(orderStatisticsVO1.getOrderSum());
			if(tbOrderSumBd.compareTo(BigDecimal.ZERO)==0){
				orderStatisticsVO1.setTbRate("");
			}else {
				String tbRate=(orderSumBd.subtract(tbOrderSumBd))
						.divide(tbOrderSumBd,
								2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
				orderStatisticsVO1.setTbRate(tbRate);
			}
			//计算同比率 rate = (本月数据 - 上个月数据) / 上个月数据
			Calendar c2 = Calendar.getInstance();//获得一个日历的实例
			c2.setTime(date);
			c2.add(Calendar.MONTH,-1);
			String HbDate = sdf.format(c2.getTime());//对比上个月的数据
			Integer hbOrderSum= dateCountMap.getOrDefault(HbDate, 0);
			BigDecimal hbOrderSumBd=BigDecimal.valueOf(hbOrderSum);
			if(hbOrderSumBd.compareTo(BigDecimal.ZERO)==0){
				orderStatisticsVO1.setHbRate("");
			}else {
				String hbRate=(orderSumBd.subtract(hbOrderSumBd))
						.divide(hbOrderSumBd,
								2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
				orderStatisticsVO1.setHbRate(hbRate);
			}
		}
		return Result.OK(pageList);
	}

	/**
	 * 导出excel
	 *
	 * @param orderStatisticsVO
	 */
	@AutoLog(value = "订单同环比分析导出")
	@ApiOperation(value="订单同环比分析导出", notes="订单同环比分析导出")
	@GetMapping(value = "/exportOrderTHBStatisticsXls")
	public ModelAndView exportOrderTHBStatisticsXls(OrderStatisticsVO orderStatisticsVO) throws ParseException{
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		orderStatisticsVO.setTenantId(Long.valueOf(TenantContext.getTenant()));
		Page<OrderStatisticsVO> page  = new Page<>(1, 99999);
		IPage<OrderStatisticsVO> pageList = orderSummaryInfoMapper.queryOrderTHBStatistics(page,orderStatisticsVO);
		Map<String,Integer> dateCountMap=new HashMap<>();
		for(OrderStatisticsVO orderStatisticsVO1:pageList.getRecords()){
			dateCountMap.put(orderStatisticsVO1.getCreateTime(),orderStatisticsVO1.getOrderSum());
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		for(OrderStatisticsVO orderStatisticsVO1:pageList.getRecords()){
			//计算同比率 rate = (本年数据 - 前一年数据) / 前一年数据
			Calendar c = Calendar.getInstance();//获得一个日历的实例
			Date date = sdf.parse(orderStatisticsVO1.getCreateTime());
			c.setTime(date);
			c.add(Calendar.YEAR,-1);
			String tbDate = sdf.format(c.getTime());//对比去年的数据
			Integer tbOrderSum= dateCountMap.getOrDefault(tbDate, 0);
			BigDecimal tbOrderSumBd=BigDecimal.valueOf(tbOrderSum);
			BigDecimal orderSumBd=BigDecimal.valueOf(orderStatisticsVO1.getOrderSum());
			String tbRate=(orderSumBd.subtract(tbOrderSumBd))
					.divide(tbOrderSumBd.compareTo(BigDecimal.ZERO)==0?BigDecimal.valueOf(1):tbOrderSumBd,
							2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
			orderStatisticsVO1.setTbRate(tbOrderSumBd.compareTo(BigDecimal.ZERO)==0?"":tbRate+"%");
			//计算同比率 rate = (本月数据 - 上个月数据) / 上个月数据
			Calendar c2 = Calendar.getInstance();//获得一个日历的实例
			c2.setTime(date);
			c2.add(Calendar.MONTH,-1);
			String HbDate = sdf.format(c2.getTime());//对比上个月的数据
			Integer hbOrderSum= dateCountMap.getOrDefault(HbDate, 0);
			BigDecimal hbOrderSumBd=BigDecimal.valueOf(hbOrderSum);
			String hbRate=(orderSumBd.subtract(hbOrderSumBd))
					.divide(hbOrderSumBd.compareTo(BigDecimal.ZERO)==0?BigDecimal.valueOf(1):hbOrderSumBd,
							2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
			orderStatisticsVO1.setHbRate(hbOrderSumBd.compareTo(BigDecimal.ZERO)==0?"":hbRate+"%");
		}
		// AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "订单同环比分析");
		mv.addObject(NormalExcelConstants.CLASS, OrderStatisticsVO.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("订单同环比分析", "导出人:" + sysUser.getRealname());
		exportParams.setImageBasePath(upLoadPath);
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, pageList.getRecords());
		return mv;
	}


	@AutoLog(value = "标记订单为危险")
	@ApiOperation(value="标记订单为危险", notes="标记订单为危险")
	@PostMapping(value = "/handleOrderDanger")
	public Result<String> handleOrderDanger(String ids,String dangerRemarks){
		List<String> idList=Arrays.asList(ids.split(","));
		if(idList.size()>0){
		for(String orderId:idList){
           List<DomesticSuppliersCotactInfo> domesticSuppliersCotactInfoList=
				   orderSummaryInfoMapper.listDomesticSuppliersCotactInfoByOrder(orderId)
				   .parallelStream().filter(Objects::nonNull).collect(Collectors.toList());;
           if(domesticSuppliersCotactInfoList!=null&&domesticSuppliersCotactInfoList.size()>0&&
				   StringUtils.isNotBlank(domesticSuppliersCotactInfoList.get(0).getCotactEmail())
			){
           	OrderInfo orderInfo=orderInfoMapper.selectById(orderId);
           	orderInfo.setDangerRemarks(dangerRemarks);
           	orderInfoMapper.updateById(orderInfo);
           	String title="货物海运风险预警";
            String emailTo=domesticSuppliersCotactInfoList.get(0).getCotactEmail();
            String content="订单号："+orderInfo.getOrderProtocolNo()+"。"+orderInfo.getDangerRemarks();
			Boolean sendFlag=EmailUtil.sendMail(emailTo,title,content);
			if(!sendFlag){
				return Result.error("订单号"+orderInfo.getOrderProtocolNo()+"发送失败");
			}
			orderInfoMapper.update(null,new LambdaUpdateWrapper<OrderInfo>()
			.set(OrderInfo::getHasDanger,1)
			.eq(OrderInfo::getId,orderId));
		   }
		}
		}
		return Result.ok("标记危险成功");
	}

}
