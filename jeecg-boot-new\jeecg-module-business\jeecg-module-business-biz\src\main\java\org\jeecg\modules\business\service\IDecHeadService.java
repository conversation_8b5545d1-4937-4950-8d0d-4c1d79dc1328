package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.DecHead;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.business.entity.DecLicenseDocus;
import org.jeecg.modules.business.entity.OrderInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
public interface IDecHeadService extends IService<DecHead> {

    /**
     * 根据统一编号生成随附单据信息
     * @param seqNo 报关单统一编号
     * @param docuCode  随附单证代码
     * @param certCode 随附单证编号
     * @return  com.yorma.entity.YmMsg<com.yorma.dcl.entity.DecLicenseDocus>
     * <AUTHOR> biao
     */
    Result<DecLicenseDocus> addDecLicenseDocus(String seqNo,String docuCode,String certCode);

    String saveDecHead(DecHead decHead);
    Result<?> saveDecHeadBatch(List<DecHead> decHeadList);

    DecHead getDecHeadById(String decHeadId);
    /**
     * 根据id获取完整报关单信息
     * @param id 表头id
     * @return 报关单信息
     */
    Result<DecHead> getDecById(Long id);
    void deleteDecHead(String id);

    boolean updatePushStatusById(String pushStatus,String id);

    DecHead queryPrintPdfList(String id);

    Result<?> handleInitialReview(String ids, String initialReviewStatus, String opinion);

    /**
     * 根据统一编号变更报关单状态
     * @param customsCode 客户端编号
     * @param seqNo 统一编号
     * @param decStatus 报关单状态
     * @return 报关单实体
     */
    Boolean updateDec(String customsCode, String seqNo, String decStatus, String entryId, Date dDate, Date releaseDate, Date finalDate);

    void customExportDec(DecHead decHead,
                         HttpServletRequest request, HttpServletResponse response);
    void exportDecComplete(String decId,String flag,
                         HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据报关单信息返回舱单信息
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    Result<?> getManifestInfo(String id, String pc, String tt, String wb,String swid);

    /**
     * 根据船名航次提单号返回海运舱单的信息
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    Result<?> getShipManifestInfo(String id, String iEFlag, String trafName, String cusVoyageNo, String billNo);

    /**
     * 企业报关差错记录
     * 返回企业报关差错记录和详细错误信息
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    Result<?> getCusError(String starDate, String lastDate);

    /**
     * 企业修撤单记录
     * 根据时间返回报关单修撤单的记录列表
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    Result<?> getDecModList(String starDate, String lastDate);

    Result<?> syncDecMod(String starDate, String lastDate);

    /**
     * 报关单税单信息
     * 返回指定报关单的税单信息（含税单货物信息）
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    Result<?> getCDTax(String entryId);

    /**
     * 返回税单核对单PDF文件
     * 根据报关单税单号或报关单号来获取税单核对单PDF格式文件内容。
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/27 下午3:23
     */
    Result<?> getCDTaxPdf(String entryId);

    /**
     * 报关单文件退税联(PDF)
     * 根据报关单号来获取报关单退税联 PDF 格式文件内容。如果成功，直接返回 pdf 文件。否则返回 json 格式错误。
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:36
     */
    Result<?> getCDPdfRtx(String entryId);

    /**
     * 报关单放行通知(PDF)
     * 根据报关单统一编号或报关单号来获取放行通知 PDF 格式文件内容。建议放行后再调取。
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:55
     */
    Result<?> getCDReleaseNote(String cusCiqNo);

    /**
     * 报关单查验通知(PDF)
     * 根据报关单统一编号或报关单号来获取查验通知 PDF 格式文件内容。如果没有查验通知，返回错误信息
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午2:04
     */
    Result<?> getCDCheckNote(String cusCiqNo);

    /**
     * 获取进出口状态
     * 返回指定报关单或提单号的进出口状态。
     *
     * @param entryId
     * @param billNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/4 下午1:09
     */
    Result<?> getSwIEStatus(String ieFlag, String entryId, String billNo);

    /**
     * 获取需要同步舱单的报关单数据
     * @return
     */
    Result<?> getDecHeadByManifestInfo(String tenandId);

    /**
     * 保存申报草单
     * @apiNote
     * <pre>
     *   保存申报草单,根据委托单进行核注单和报关单的添加,在保存报关单时会重新获取委托中的包装种类、其他包装、进出境关别、申报地海关等字段重新赋值
     * </pre>
     *
     * @param apply 委托单信息
     * @return com.yorma.entity.YmMsg<com.yorma.apply.entity.Apply> 成功为 true+委托信息 失败为 false+失败原因
     *
     * <AUTHOR> 2021/9/9 15:33
     * @version 1.0
     */
    Result<?> saveDecAndInvt(OrderInfo apply);

    Result<?> handleCreateInvtByDec(String id,String sysId);

    /**
     * 报关单列表
     * 根据最近操作时间，返回报关单列表。
     * 注意：最多只能查询 7 天的数据（使用 entryId 或 billNo 查询时，不受此限制）
     *
     * @param dclTrnRelFlag 报关单类型（空表示全部） 0 – 一般报关单 1 – 转关提前报关单 2 – 备案清单 3 – 转关提前备案清单 4 – 出口二次转关
     * @param etpsCategory 企业类别（空表示全部） A- 报关申报单位 B- 销售使用/生产销售单位 C - 报关收发货人 D – 报关录入单位
     * @param cusIEFlag 进出口标志（进口-I，出口-E， 全部-空串）
     * @param tableFlag 是否结关（未结关-0， 已结关-1， 全部-空串）
     * @param cnsnTradeCode 境内收发货人（18 位或 10 位）
     * @param entryId 报关单号或统一编号
     * @param billNo 提运单号
     * @param beginTime 起始时间(格式 yyyy-MM-dd)
     * @param endTime 结束时间(格式 yyyy-MM-dd)
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    Result<?> GetCDQuery(String dclTrnRelFlag, String etpsCategory, String cusIEFlag, String tableFlag,
                         String cnsnTradeCode, String entryId, String billNo, String beginTime, String endTime, String swid);

    /**
     * 报关单全量数据(JSON)
     * 根据报关单统一编号或报关单号来获取报关单 Json 格式全部内容。
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    Result<?> GetCDDetails(String cusCiqNo, String swid);

    /**
     * 读取JSON文件获取报关单全量数据(JSON)
     *
     * @param
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/25 17:39
     */
    Result<?> GetCDDetailsByJsonFiles(String path, String swid);

    /**
     * 报关单列表单独查询某一条数据
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    Result<?> syncDecClearance(String swid);

    /**
     * 报关单列表单独查询某一条数据
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    Result<?> Ydt_batchExtractReportKey(String goods);
    /**
     * 报关单列表单独查询某一条数据
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    Result<?> Ydt_batchQueryGoodsList(String goods);

    String Ydt_validate(String param);

    Result<?> listOverseasConsignorHistory(String ieFlag);
}
