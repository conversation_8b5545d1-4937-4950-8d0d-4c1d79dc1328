<template>
	<a-modal
		title="费用登记"
		:visible="visible"
		width="1300px"
		@ok="handleOk"
		@cancel="handleCancel"
		:destroyOnClose="true"
	>
		<template slot="footer">
			<a-button size="small" @click="handleCancel" icon="bulb">关闭</a-button>
			<a-button size="small" :loading="saveLoading" @click="handleOk" icon="check" type="primary">确认登记</a-button>
		</template>
		<a-tabs default-active-key="1" v-model="activeKey" @change="callbacktab">
			<!-- 根据集装箱登记 -->
			<a-tab-pane key="3" tab="根据集装箱登记">
				<div class="container-registration">
					<div class="left-panel">
						<a-form layout="inline" @keyup.enter.native="searchQuery">
							<a-row :gutter="24">
								<a-col :xl="12" :sm="24" :xxl="6" :md="12">
									<a-form-item label="箱号" :labelCol="labelCol" :wrapperCol="wrapperCol">
										<j-input :type="'no'" placeholder="请输入箱号" v-model="queryParam.containerId"></j-input>
									</a-form-item>
								</a-col>
								<a-col :xl="12" :sm="24" :xxl="6" :md="12" style="margin-top: 4px">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
								</a-col>


							</a-row>
						</a-form>

						<a-table
							:columns="containerColumns"
							:data-source="containerData"
							:pagination="containerPagination"
							rowKey="id"
							:loading="loading"
							:customRow="handleOverview"
							@change="handleTableChange"
							:row-selection="{ selectedRowKeys: selectedContainerKeys, onChange: onContainerSelectChange }"
						>
						</a-table>
					</div>
					<div class="right-panel">
						<div class="fee-input-section">
							<span v-if="this.selectedContainerKeys.length>0"
										style="font-size: 12px">登记费用的箱号:{{selectedContainerNos}}</span>
							<a-form :form="form" layout="vertical">
								<div v-for="(fee, index) in fees" :key="fee.key" class="fee-item">
									<a-row :gutter="16">
										<a-col :span="4">
											<a-form-item label="费用名称">
												<!-- <a-input
													v-decorator="[
                            `fees[${index}].feeName`,
                            { rules: [{ required: true, message: '请输入费用名称' }] }
                          ]"
													placeholder="请输入费用名称"
													v-model="fee.feeName"
												/> -->

												<j-search-select-tag :regularFlag='true' placeholder="请选择" v-model="fee.feeId"
                :dict="dictCodeFee" @change="(val,val2) => feeChange(val,val2, fee)" />
											</a-form-item>
										</a-col>
										<a-col :span="3">
											<a-form-item label="费用金额">
												<a-input-number
													v-decorator="[
                            `fees[${index}].feeAmount`,
                            { rules: [{ required: true, message: '请输入费用金额' }] }
                          ]"
													v-model="fee.feeAmount"
													style="width: 100%"
													:min="0"
													:precision="2"
													placeholder="请输入费用金额"
												/>
											</a-form-item>
										</a-col>
										<a-col :span="3">
											<a-form-item label="币制">
												<a-select
													v-decorator="[
                            `fees[${index}].currency`,
                            { rules: [{ required: true, message: '请选择币制' }] }
                          ]"
													v-model="fee.currency"
													placeholder="请选择币制"
													showSearch
													optionFilterProp="children"
													allowClear
												>
													<a-select-option v-for="d in currencies" :key="d.title" :value="d.title">
														{{ d.title }}
													</a-select-option>

												</a-select>
											</a-form-item>
										</a-col>
										<a-col :span="3">
											<a-form-item label="应收/应付">
							<a-select placeholder="请选择收支" v-model="fee.type" 	v-decorator="[
                            `fees[${index}].type`,
                            { rules: [{ required: true, message: '请选择应收/应付' }] }
                          ]">
								<a-select-option value="1">应收</a-select-option>
								<a-select-option value="2">应付</a-select-option>
							</a-select>

											</a-form-item>
										</a-col>
												<a-col :span="5">
											<a-form-item label="结算单位">
										
												<j-search-select-tag :regularFlag='true' placeholder="请选择" v-model="fee.settlementId"
											
												@change="(val,val2) => settlementChange(val,val2, fee)"
                :dict="dictCodeS" />


											</a-form-item>
										</a-col>
<!--										附件上传-->
										<a-col :span="4">
											<a-form-item label="附件">
												<attachments-info
													@change="(val) => attachmentsChange(val, fee)"

													style="margin-left: 10px" ref="attachments"/>
											</a-form-item>
										</a-col>

										<a-col :span="2" style="padding-top: 30px">
											<a-icon
												v-if="fees.length > 1"
												type="minus-circle"
												@click="removeFee(index)"
												style="color: #ff4d4f; font-size: 18px; cursor: pointer"
											/>
										</a-col>
									</a-row>
								</div>
								<a-button type="dashed" @click="addFee" style="width: 100%">
									<a-icon type="plus" /> 添加费用
								</a-button>
							</a-form>
						</div>

						<div class="registered-fees">
							<h4>本次已登记费用</h4>
							<a-list
								:data-source="registeredFees"
								:locale="{ emptyText: '本次暂无已登记费用' }"
							>
								<a-list-item slot="renderItem" slot-scope="item">
									<a-list-item-meta>
										<div slot="description">
											箱号 {{ item.documentNumber }} 登记费用 {{ item.feeName }}: {{ item.feeAmount }} ({{ item.currency }})
										</div>
									</a-list-item-meta>
								</a-list-item>
							</a-list>
						</div>
					</div>
				</div>
			</a-tab-pane>

		</a-tabs>
	</a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { filterObj } from '@/utils/util'
import { ajaxGetDictItems } from '@/api/api'
import Vue from 'vue'
import { TENANT_ID } from "@/store/mutation-types"
import AttachmentsInfo from '@/views/accountsReceivablePayable/component/AttachmentsInfo.vue'

let feeIndex = 0;

export default {
	components: { AttachmentsInfo },
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			dictCodeFee:'',
			dictCodeS:'',
			ieFlag: 'E',
			saveLoading: false,
			currencies: [],
			selectedContainerNos: '',
			selectedDecNos: '',
			loading: false,
			activeKey: "3",
			labelCol: {
				xs: { span: 6 },
				xxl: { span: 6 },
				xl: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 18 },
			},
			queryParam: {},
			queryParamDec: {},
			form: this.$form.createForm(this),
			containerColumns: [
				{
					title: '报关单号',
					dataIndex: 'clearanceNo',
					key: 'clearanceNo'
				},
				{
					title: '箱号',
					dataIndex: 'containerId',
					key: 'containerId'
				}
			],
			decColumns: [
				{
					title: '报关单号',
					dataIndex: 'clearanceNo',
					key: 'clearanceNo'
				},
				{
					title: '提运单号',
					dataIndex: 'billCode',
					key: 'billCode'
				}
			],
			containerData: [
				// { id: 1, declarationNo: 'BG20230001', containerNo: 'TGHU1234567' },
				// { id: 2, declarationNo: 'BG20230002', containerNo: 'TGHU7654321' },
				// { id: 3, declarationNo: 'BG20230003', containerNo: 'TGHU9876543' },
				// { id: 4, declarationNo: 'BG20230004', containerNo: 'TGHU4567890' },
				// { id: 5, declarationNo: 'BG20230005', containerNo: 'TGHU3456789' },
				// { id: 6, declarationNo: 'BG20230006', containerNo: 'TGHU2345678' },
				// { id: 7, declarationNo: 'BG20230007', containerNo: 'TGHU1234568' },
				// { id: 8, declarationNo: 'BG20230008', containerNo: 'TGHU8765432' },
				// { id: 9, declarationNo: 'BG20230009', containerNo: 'TGHU6543210' },
				// { id: 10, declarationNo: 'BG20230010', containerNo: 'TGHU5432109' }
			],
			decData: [],
			containerPagination: {
				total: 0,
				current: 1,
				pageSize: 10,
				showSizeChanger: true,
				size: "small",
				pageSizeOptions: ['10', '20', '50'],
			},
			decPagination: {
				total: 0,
				current: 1,
				pageSize: 10,
				showSizeChanger: true,
				size: "small",
				pageSizeOptions: ['10', '20', '50'],
			},
			selectedContainerKeys: [],
			selectedDecKeys: [],
			selectedContainer: null,
			selectedDec: null,
			fees: [],
			registeredFees: [],
			url: {
				listDecContainer: '/DecHead/dec-head/listDecContainer',
				listDecHead: '/DecHead/dec-head/listDecHead',
				listOrderInfo: '/export/orderInfo/listOrderInfo',
				save: '/business/accountsReceivablePayable/save',
			}

		};
	},
	created() {
		   // 查询当前租户id
        let tenantId = Vue.ls.get(TENANT_ID)
        if (!tenantId) {
          tenantId = 0;
        }
		this.dictCodeFee = 'fee_item,FEE_NAME,id,tenant_id='+ tenantId+' and del_flag=0 and DISABLE_FLAG=0';
		this.dictCodeS = 'settlement_info,SETTLEMENT_NAME,id,tenant_id='+ tenantId;
		this.addFee(); // 初始化一个费用输入框
		this.initDictData('erp_currencies,currency,code')
	},
	methods: {
		feeChange(e,e2,e3){
			e3.feeName = e2
			e3.feeId = e
		},
		settlementChange(e,e2,e3){
			e3.settlementName = e2
			e3.settlementId = e
		},
		callbacktab(key) {
			switch (key) {
				case '1':
					this.loadData(this.url.listDecContainer, 1)
					break;
				case '2':
					this.loadDataDec(this.url.listDecHead, 1)
					break;
				case '3':
					this.loadData(this.url.listOrderInfo, 1)
					break;
				default:
					break;
			}
		},
		attachmentsChange(val, fee) {
			fee.attachmentList = val
		},
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null) {
				this.currencies = dictOptions
				console.log('????')
				console.log(this.currencies)
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null) {
							this.currencies = dictOptions
						}
					}
				})
			}
		},
		searchQuery() {
			switch (this.activeKey) {
				case '1':
					this.loadData(this.url.listDecContainer, 1)
					break;
				case '2':
					this.loadDataDec(this.url.listDecHead, 1)
					break;
				case '3':
					this.loadData(this.url.listOrderInfo, 1)
					break;
				default:
					break;
			}
		},
		getQueryParams() {
			var param = Object.assign({}, this.queryParam)
			param.pageNo = this.containerPagination.current
			param.pageSize = this.containerPagination.pageSize
			return filterObj(param)
		},
		getQueryParamsDec() {
			var param = Object.assign({}, this.queryParamDec)
			param.pageNo = this.decPagination.current
			param.pageSize = this.decPagination.pageSize
			return filterObj(param)
		},
		searchReset() {
			this.queryParam = {}
			this.loadData(this.url.listDecContainer, 1)
		},
		loadData(url, arg) {
			let isEmpty = false
			if (!url) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.containerPagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(url, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.containerData = res.result.records || res.result
						if (res.result.total) {
							this.containerPagination.total = res.result.total
						} else {
							this.containerPagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},

		handleTableChange(pagination, filters, sorter) {
			// //分页、排序、筛选变化时触发
			// //TODO 筛选
			console.log(pagination)
			// if (!sorter.order) {
			// 	sorter.field = this.isorter.column
			// 	sorter.order = 'desc'
			// }
			// if (Object.keys(sorter).length > 0) {
			// 	this.isorter.column = sorter.field
			// 	this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
			// }
			this.containerPagination = pagination
			this.loadData(this.url.listDecContainer)

	},

	handleOk() {
		for(let f of this.fees){
					if(!f.feeId){
						this.$message.error('存在费用名称为空的费用，请检查。')
						return
					}
		}
		this.form.validateFields((err, values) => {
			if (!err && this.selectedContainer) {
				const selectedContainer = this.containerData.filter(
					item => this.selectedContainerKeys.includes(item.id)
				);

				this.fees.forEach(fee => {
					this.registeredFees.push({
						documentNumber: selectedContainer.length > 5 ?
							((selectedContainer.slice(0, 5)).map(item => item.containerId).join(',')) + '等' :
							selectedContainer.map(item => item.containerId).join(','),
						feeName: fee.feeName,
						feeAmount: fee.feeAmount,
						currency: fee.currency
					});
				});

				//执行请求
				let accountsReceivablePayableList = []
				for (let o of selectedContainer) {
					for (let f of this.fees) {
						accountsReceivablePayableList.push({
							documentNumber: o.containerId,
							relatedId: o.id,
							registrationType: 3,
							feeId:f.feeId,
							settlementId:f.settlementId,
							settlementName:f.settlementName,
							feeName: f.feeName,
							feeAmount: f.feeAmount,
							currency: f.currency,
							type:f.type,
							attachmentList: f.attachmentList,
							ieFlag: this.ieFlag
						})
					}
				}
				console.log(accountsReceivablePayableList)
				this.saveLoading = true
				httpAction(this.url.save, accountsReceivablePayableList, 'post')
					.then((res) => {
						if (res.success) {
							this.$emit('loadData')
							this.$message.success('费用登记成功');
							this.form.resetFields();
							this.fees = [];
							this.addFee(); // 重置后添加一个空的费用输入框
						} else {
							this.$message.error(res.message)
						}
					})
					.finally(() => {
						this.saveLoading = false
					})

			} else if (!this.selectedContainer) {
				this.$message.error('请选择要登记的集装箱');
			}
		});
	},
	handleCancel() {
		this.$emit('update:visible', false);
	},
	onContainerSelectChange(selectedRowKeys) {
		this.selectedContainerKeys = selectedRowKeys;
		this.selectedContainer = this.containerData.find(
			item => item.id === selectedRowKeys[0]
		);
		let data = []
		for (let o of this.containerData) {
			if (selectedRowKeys.includes(o.id)) {
				data.push(o)
			}
		}
		this.selectedContainerNos = data.length > 5 ?
			((data.slice(0, 5)).map(item => item.containerId).join(',')) + '等' : data.map(item => item.containerId).join(',')
	},
	onDecSelectChange(selectedRowKeys) {
		this.selectedDecKeys = selectedRowKeys;
		this.selectedDec = this.decData.find(
			item => item.id === selectedRowKeys[0]
		);
		let data = []
		for (let o of this.decData) {
			if (selectedRowKeys.includes(o.id)) {
				data.push(o)
			}
		}
		this.selectedDecNos = data.length > 3 ?
			((data.slice(0, 3)).map(item => item.clearanceNo).join(',')) + '等' : data.map(item => item.clearanceNo).join(',')
	},

	addFee() {
		this.fees.push({
			key: feeIndex++,
			feeId:null,
			feeName: null,
			settlementId:null,
			settlementName:null,
			feeAmount: null,
			currency: null,
			type:null,
			attachmentList: []
		});
	},
	removeFee(index) {
		this.fees.splice(index, 1);
	},
	handleOverview(record, index) {
		return {
			on: {
				click: () => {
					// this.$refs.modalOverview.overview(record)
					// this.$refs.modalOverview.title = '概览'

					let keys = []
					this.selectionRows = []
					keys.push(record.id)
					// this.selectedRowKeys = keys
					// this.selectionRows.push(record)

					this.selectedContainerKeys = keys;
					this.selectedContainer = this.containerData.find(
						item => item.id === keys[0]
					);
					this.selectedContainerNos = this.selectedContainer.containerId

				},
				dblclick: () => {

				}
			}
		}
	},

}
};
</script>

<style scoped>
.container-registration {
	display: flex;
	height: 600px;
}

.left-panel {
	width: 40%;
	padding-right: 16px;
	border-right: 1px solid #f0f0f0;
	overflow-y: auto;
}

.right-panel {
	width: 60%;
	padding-left: 16px;
	display: flex;
	flex-direction: column;
}

.fee-input-section {
	flex: 1;
	overflow-y: auto;
	margin-bottom: 16px;
}

.registered-fees {
	height: 200px;
	overflow-y: auto;
	border-top: 1px solid #f0f0f0;
	padding-top: 16px;
}

.fee-item {
	margin-bottom: 6px;
	padding: 8px;
	background: #fafafa;
	border-radius: 4px;
	height: auto;
}
/deep/ .ant-table-tbody .ant-table-row td {
	padding-top: 10px;
	padding-bottom: 10px;
	text-align: center
}
/deep/ .ant-table-thead > tr > th {
	padding: 8px;
}
/deep/ .ant-table-thead th {
	text-align: center !important;
}
</style>