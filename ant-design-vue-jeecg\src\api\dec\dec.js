/*dec----报关单*/


import {axios} from '@/utils/request'
import {downFile, getAction, getFile, postAction,_postAction,deleteAction} from '@/api/manage'

/**
 * 根据商品编码获取列表数据
 * @param hscode
 * @return8
 */
// const decCiqName = (params)=>getAction("/DecHead/decHead/listCiq",params);
export function decCiqName(hscode){
    // return axios.get(`/dictionary/erpCiq/listCiq?codeTs=${hscode}`)
    return axios.get(`/DecHead/dec-head/listCiq?codeTs=${hscode}`)
}
export function decCiqNameForPage(params){
  return getAction("/DecHead/dec-head/listCiq",params)
}
/**
 * 根据条件查询报关单表头
 * @param decHeadVO 条件参数
 * @param pageNum
 * @param pageSize
 */
export function listDecHead(decHeadVO, pageNum, pageSize) {
  const data = {
    decHeadVO: decHeadVO,
    pageNum  : pageNum,
    pageSize : pageSize
  }
  return axios.post(`/dcl/dec/listDecHead`, data)
}
/**
 * 导出核注单预览
 */
export  function  exportNemsList(queryParam,ids,fields) {
  return postAction('/dcl/tool/exportInvtHeadByFieldsBatch',{decHeadVO:queryParam,ids:ids,fields:fields})
}
/**
 * 导出报关单预览
 */
export  function  exportDecList(queryParam,ids,fields) {
  return postAction('/dcl/tool/exportDecHeadByFieldsBatch',{decHeadVO:queryParam,ids:ids,fields:fields})
}
/**
 * 审核
 * @param decHead 包含表体装箱等信息
 */
export function isAuditDec(id,isAuditedType) {
  return axios.post(`/dcl/dec/setAuditedBatch`, {
    ids: id,
    audited:true,
  })
}
export  function  getFilePrint(listKey,that,type,URLType,attachmentName) {

  let urlName = ''
  if(listKey.indexOf("http")>-1){
    urlName = listKey
  }else{
    if(URLType=='staticDomainURL'){
      urlName = `${window._CONFIG['staticDomainURL']+listKey}`
    }else if(URLType=='fileServerURL'){
      urlName = `${window._CONFIG['fileServerURL']+listKey}`
    }

  }
  getFile(urlName).then((data) => {
    if (!data) {
      this.$message.warning("文件下载失败")
      return
    }
    console.log( urlName.split("/"))
    let fileName = ''
    if(!!attachmentName){
      fileName = attachmentName
    }else{
      fileName = urlName.split("/")[urlName.split("/").length-1]
    }
    //

    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data],{type: 'application/pdf;chartset=UTF-8'}), fileName)
    }else{

      let url = window.URL.createObjectURL(new Blob([data],{type: 'application/pdf'}))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      if(type==1){
        that.exportLoading = false
      }else if(type==2){
        that.printDecLoading=false
        that.showModal = !that.showModal
      }else if(type==3){
        that.localPrintDecLoading=false
      }else if(type == 4){
        that.printDecLoading=false
      }else if(type==6){
        that.localPrintDecLoading=false
      }else if(type == 5){
        that.printLoading = false
      }else if(type == 7){
        that.exportExcelLoading = false
      }
      link.click()
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    }
  })
}
/**
 * 获取包装种类
 */
export function getBZZL() {
  return axios.get(`dictionary/erpPackagesTypes/listAll`,{})
}

export function decTeriff(hscode){
  return axios.get(`/dictionary/erpHscodes/listTariffByHscode?codeTs=${hscode}`)
}

export function listDecListByTenant(optUnitSocialCode,optUnitId,optUnitName,
	createPerson,hscode,hsname,pageNo,pageSize,declareUnitSocialCode,IE_FLAG,HSMODEL,deliverUnitName){
return axios.get(`/DecHead/dec-head/listDecListByTenant?createPerson=${createPerson}&hscode=${hscode}
&hsname=${encodeURIComponent(hsname)}&pageNo=${pageNo}&pageSize=${pageSize}&optUnitSocialCode=${optUnitSocialCode}
&optUnitId=${optUnitId}&optUnitName=${optUnitName}&declareUnitSocialCode=${declareUnitSocialCode}&IE_FLAG=${IE_FLAG}&HSMODEL=${HSMODEL}&deliverUnitName=${deliverUnitName}`)
}


export function checkDeclarationStandards(hscode,historyDeclarationSpecification){
	return postAction(`/DecHead/dec-head/checkDeclarationStandards`,{hscode:hscode,
		historyDeclarationSpecification:historyDeclarationSpecification})
}


export function deleteDecDocument(id){
    return axios.get(`/DecAttachment/dec-attachment/deleteDecAttachment?id=${id}`)
}
/**
 * 保存随附单据
 * @param hscode
 * @return
 */
export function saveDecDocument(obj){
    // if (obj && obj.length>0){
    //     for (let att of obj ){
            return axios.post(`/DecAttachment/dec-attachment/addDecAttachment`, obj)
        // }
    // }

}
/**
 * 查看随附单据
 * @param hscode
 * @return
 */
export function getDecDocument(id){
    return axios.get(`/DecAttachment/dec-attachment/getDecAttachmentByDclId?dclId=${id}`)
}
export function saveDec(decHead) {
    return axios.post(`/DecHead/dec-head/saveDecHead`, decHead)
}
export function Ydt_validate(param) {
	return axios.post(`/DecHead/dec-head/Ydt_validate`, param)
}
export function getDecById(decHeadId) {
  return axios.get(`/DecHead/dec-head/getDecHeadById?decHeadId=${decHeadId}`)
}

export function batchdecFxtzsPrint(ids,signature) {
  return ''
}
/**
 * 根据申报税号获取申报规范
 * @param hscode
 * @return
 */
export function decHsCode(hscode){
	return axios.get(`/DecHead/dec-head/listSpecByHscodes?hscode=${hscode}`)

}

/**
 * 电子委托保存
 * @param record
 */
export  function  elecSave(elecProtocol) {
	return postAction('/business/elecProtocol/saveElecProtocol',{elecProtocol:elecProtocol})
}
/**
 * 电子委托查看
 * @param record
 */
export  function  getElecById(id) {
	return getAction('/business/elecProtocol/getElecProtocolById',{id:id})
}

/**
 * 不显示商品历史库
 */
export function hideDecList(id) {
  return axios.post(`/DecHead/dec-head/hideDecList?id=${id}`)
}

/**
 * 不显示商品历史库
 */
export function Ydt_batchQueryGoodsList(goodsHsCodes) {
  return axios.post(`/DecHead/dec-head/Ydt_batchQueryGoodsList?goodsHsCodes=${goodsHsCodes}`)
}

/**
 * OCR识别报关单
 * @param {*} links
 * @returns
 */
export  function  getYdt_OcrRecognition(links) {
	return getAction('/business/bgapi/Ydt_OcrRecognition',{links:links})
}
/**
 * 通过序号获取手册备案信息 （远程接口）
 * @param {*} tradeCode
 * @param {*} manualNo
 * @param {*} gdsSeqNo
 * @returns
 */
export function GetManualItem(tradeCode,manualNo,gdsSeqNo){
  return axios.post(`/business/bgapi/GetManualItem?tradeCode=${tradeCode}&manualNo=${manualNo}&gdsSeqNo=${gdsSeqNo}`)
}

/**
 * 通过序号获取手册备案信息 （本地）
 * @param {*} links
 * @returns
 */
export function GetManualItemLocal(emsNo,gNo,type) {
	return getAction('/business/ems/listEmsDetail',{pageNum: '1', pageSize: '1',
		emsNo: emsNo,
		gNo: gNo,
		type: type,
	isFromDec:'1'})
}






