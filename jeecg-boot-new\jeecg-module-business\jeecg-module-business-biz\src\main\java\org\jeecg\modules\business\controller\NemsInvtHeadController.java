package org.jeecg.modules.business.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.MonthlyListEntity;
import org.jeecg.modules.business.entity.dto.NemsInvtHeadDTO;
import org.jeecg.modules.business.entity.dto.ReportManagerDTO;
import org.jeecg.modules.business.entity.excel.ExportConsumptionEntity;
import org.jeecg.modules.business.entity.excel.ExportDecExcel;
import org.jeecg.modules.business.entity.paramVo.DefColumnsVO;
import org.jeecg.modules.business.entity.paramVo.NemsListByWriteOffBalanceVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.excel.ExcelExportStylerBorderImpl;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isEmpty;

/**
 * <p>
 * 核注清单表头 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Slf4j
@RestController
@RequestMapping("/dcl/invt")
public class NemsInvtHeadController {

    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;
    @Autowired
    private INemsInvtListService invtListService;
    @Autowired
    private RateInfoMapper rateInfoMapper;
    @Autowired
    private ErpCurrenciesMapper erpCurrenciesMapper;
    @Autowired
    private NemsInvtHeadMapper nemsInvtHeadMapper;
    @Autowired
    private IErpUnitsService erpUnitsService;
    @Autowired
    private IStoreInfoService storeInfoService;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private IPtsEmsHeadService ptsEmsHeadService;
    @Autowired
    private ReportHeadMapper reportHeadMapper;
    @Autowired
    private ReportListMapper reportListMapper;
    @Autowired
    private IPtsEmsAimgService ptsEmsAimgService;
    @Autowired
    private IPtsEmsAexgService ptsEmsAexgService;

    /**
     * 分页列表查询
     *
     * @param nemsInvtHeadDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "核注清单列表-分页列表查询", notes = "核注清单列表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(NemsInvtHeadDTO nemsInvtHeadDTO, HttpServletRequest request,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<NemsInvtHead> page = new Page<>(pageNo, pageSize);
        IPage<NemsInvtHead> pageList = nemsInvtHeadService.queryPageList(page, nemsInvtHeadDTO, request);
        return Result.OK(pageList);
    }

    /**
     * 根据ID获取核注单
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID获取核注单", notes = "根据ID获取核注单")
    @GetMapping(value = "/getInvtById")
    public Result<?> getInvtById(@RequestParam("id") String id) {
        NemsInvtHead nemsInvtHead = nemsInvtHeadService.getInvtById(id);
        return Result.OK(nemsInvtHead);
    }

    /**
     * 保存核注单
     *
     * @param nemsInvtHead
     * @return
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存核注单")
    @ApiOperation(value = "保存核注单", notes = "保存核注单")
    @PostMapping(value = "/saveInvt")
    public Result<?> saveInvt(@RequestBody NemsInvtHead nemsInvtHead) {
        return nemsInvtHeadService.saveInvt(nemsInvtHead);
    }

    /**
     * 表体分页列表查询
     *
     * @param invId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "表体分页列表查询", notes = "表体分页列表查询")
    @GetMapping(value = "/listInvt")
    public Result<?> listInvt(@RequestParam("invId") String invId,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<NemsInvtList> page = new Page<>(pageNo, pageSize);
        IPage<NemsInvtList> pageList = nemsInvtHeadService.listInvt(page, invId);
        return Result.OK(pageList);
    }

    /**
     * 根据企业内部编号和报关单商品序号获取报关单标体信息
     *
     * @param invId 核注单ID
     * @param item  报关单商品序号
     * @return
     */
    @ApiOperation(value = "表体分页列表查询", notes = "表体分页列表查询")
    @GetMapping(value = "/getDecListByInvtIdAndItem")
    public Result<?> getDecListByInvtIdAndItem(@RequestParam(value = "invId", required = false) String invId,
                                               @RequestParam(value = "entryNo", required = false) String entryNo,
                                               @RequestParam(value = "item", required = false) String item) {
        List<DecList> decList = nemsInvtHeadService.getDecListByInvtIdAndItem(invId, item,entryNo);
        return Result.OK(decList);
    }

    /**
     * 根据核注单id获取报关单数据
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据核注单id获取报关单数据", notes = "根据核注单id获取报关单数据")
    @GetMapping(value = "/getIdDecHead")
    public Result<?> getInvtIdDec(@RequestParam("id") String id) {
        DecHead decHead = nemsInvtHeadService.getInvtIdDec(id);
        return Result.OK(decHead);
    }

    /**
     * 批量删除核注单
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "批量删除核注单")
    @ApiOperation(value = "批量删除核注单", notes = "批量删除核注单")
    @DeleteMapping(value = "/delBatch")
    public Result<?> delBatch(@RequestParam("ids") String ids) {
        return nemsInvtHeadService.delBatch(ids);
    }

    /**
     * 核注单初复审
     *
     * @param ids
     * @param initialReviewStatus
     * @param opinion
     * @return
     */
    @AutoLog(value = "核注单初复审")
    @ApiOperation(value = "核注单初复审", notes = "核注单初复审")
    @PostMapping(value = "/handleInitialReview")
    public Result<?> handleInitialReview(@RequestParam("ids") String ids,
                                         @RequestParam("initialReviewStatus") String initialReviewStatus,
                                         @RequestParam("opinion") String opinion) {
        if (!"1".equals(initialReviewStatus) && !"2".equals(initialReviewStatus)) {
            return Result.error("未知的审核类型！");
        }
        return nemsInvtHeadService.handleInitialReview(ids, initialReviewStatus, opinion);
    }


    /**
     * 获取清单记录列表
     *
     * @param putrecNo 入库条码
     * @param putrecSeqno 入库顺序号
     * @param mtpckEndprdMarkcd 包装结束标记
     * @param vrfdedMarkcd 核对标记
     * @param pageNum 当前页码
     * @param pageSize 每页数量
     * @return 结果对象
     */
    @GetMapping(value = "/listDeclarationRecord")
    public Result<?> listDeclarationRecord(
            @RequestParam("putrecNo") String putrecNo,
            @RequestParam("putrecSeqno") String putrecSeqno,
            @RequestParam("mtpckEndprdMarkcd") String mtpckEndprdMarkcd,
            @RequestParam(value = "vrfdedMarkcd", required = false) String vrfdedMarkcd,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize) {
        return nemsInvtHeadService.listDeclarationRecord(putrecNo, putrecSeqno, mtpckEndprdMarkcd, vrfdedMarkcd, pageNum, pageSize);
    }

    /**
     * 修改申报记录的申报数量
     * @apiNote
     * <pre>
     *   修改申报记录的申报数量
     * </pre>
     *
     * @param invtListId, emsNo, gNo, dclQty
     * @return com.yorma.entity.YmMsg<java.lang.String>
     *
     * @version 1.0
     */
    @RequestMapping(value = "/editDclQty")
    public Result<?> editDclQty(@RequestParam("invtListId") String invtListId,
                                @RequestParam("emsNo") String emsNo,
                                @RequestParam("gNo")String gNo,
                                @RequestParam("dclQty")String dclQty,
                                @RequestParam("stockQty")String stockQty,
                                @RequestParam("type") String type) {
        return nemsInvtHeadService.editDclQty(invtListId,emsNo,gNo,dclQty,stockQty,type);
    }

    /**
     * 修改申报记录的申报数量
     * @apiNote
     * <pre>
     *   修改申报记录的申报数量
     * </pre>
     *
     * @param invtListId, emsNo, gNo, dclQty
     * @return com.yorma.entity.YmMsg<java.lang.String>
     *
     * @version 1.0
     */
    @RequestMapping(value = "/delInvtList")
    public Result<?> delInvtList(@RequestParam("invtListId") String invtListId,
                                 @RequestParam("emsNo") String emsNo,
                                 @RequestParam("gNo")String gNo,
                                 @RequestParam("dclQty")String dclQty,
                                 @RequestParam("type") String type) {
        return nemsInvtHeadService.delInvtList(invtListId,emsNo,gNo,dclQty,type);
    }

    /**
     * 修改申报记录的申报数量
     * @apiNote
     * <pre>
     *   修改申报记录的申报数量
     * </pre>
     *
     * @return com.yorma.entity.YmMsg<java.lang.String>
     *
     * @version 1.0
     */
    @RequestMapping(value = "/checkNemDel")
    public Result<?> checkNemDel(@RequestParam("invtId") String invtId) {
        List<NemsInvtList> nemsInvtListList=invtListService.list(new LambdaQueryWrapper<NemsInvtList>()
                .eq(NemsInvtList::getInvId,invtId));
        if(nemsInvtListList.size()==0){
            nemsInvtHeadService.removeById(invtId);
        }
        return null;
    }

    /**
     * 核注单手动核扣
     *
     * @param ids
     * @param warehousingDate
     * @return
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "核注单手动核扣")
    @ApiOperation(value = "核注单手动核扣", notes = "核注单手动核扣")
    @PostMapping(value = "/handleManualDeduction")
    public Result<?> handleManualDeduction(@RequestParam("ids") String ids,
                                           @RequestParam(value = "warehousingDate", required = false) String warehousingDate,
                                           @RequestParam("type") String type) {
        return nemsInvtHeadService.handleManualDeduction(ids, warehousingDate, type);
    }

    /**
     * 核注单生成报关单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "核注单生成报关单")
    @ApiOperation(value = "核注单生成报关单", notes = "核注单生成报关单")
    @PostMapping(value = "/handleCreateDecByInvt")
    public Result<?> handleCreateDecByInvt(@RequestParam("id") String id) {
        return nemsInvtHeadService.handleCreateDecByInvt(id);
    }

    /**
     * 核注单生成核放单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "核注单生成核放单")
    @ApiOperation(value = "核注单生成核放单", notes = "核注单生成核放单")
    @PostMapping(value = "/handleCreatePassByInvt")
    public Result<?> handleCreatePassByInvt(@RequestParam("id") String id) {
        return nemsInvtHeadService.handleCreatePassByInvt(id);
    }

    /**
     * 核注单推送报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/30 9:15
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "核注单推送报文")
    @ApiOperation(value = "核注单推送报文", notes = "核注单推送报文")
    @PostMapping(value = "/handlePush")
    public Result<?> handlePush(@RequestParam("ids") String ids,
                                @RequestParam("passageway") String passageway) {
        return nemsInvtHeadService.handlePush(ids, passageway);
    }

    /**
     * 根据海关编码或信用代码或企业名称获取企业信息
     *
     * @param searchText
     * @param flag 参数范围（0=海关编号 1=统一社会信用代码 2=组织机构代码 3=公司名称 4=检疫编号,""=不调用第三方接口）
     * @return 结果对象
     */
    @GetMapping(value = "/getEnterpriseByDepartcdOrSocialCodeOrDepartName")
    public Result<?> getEnterpriseByDepartcdOrSocialCodeOrDepartName(@RequestParam("flag") String flag,
                                                                     @RequestParam("searchText") String searchText) {
        return nemsInvtHeadService.getEnterpriseByDepartcdOrSocialCodeOrDepartName(flag, searchText);
    }

    /**
     * 首页统计四个方块的数据
     *
     * @return 结果对象
     */
    @GetMapping(value = "/getFourStatisticalSquares")
    public Result<?> getFourStatisticalSquares() {
        return nemsInvtHeadService.getFourStatisticalSquares();
    }

    /**
     * 根据账册号获取清单数据
     *
     * @param emsNo
     * @param pageNo
     * @param pageSize
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    @ApiOperation(value = "表体分页列表查询", notes = "表体分页列表查询")
    @GetMapping(value = "/listInvtByEmsNo")
    public Result<?> listInvtByEmsNo(@RequestParam("emsNo") String emsNo,
                                     @RequestParam("putrecSeqno") String putrecSeqno,
                                     @RequestParam("impexpMarkcd") String impexpMarkcd,
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<NemsInvtHead> page = new Page<>(pageNo, pageSize);
        IPage<NemsInvtHead> pageList = nemsInvtHeadService.listInvtByEmsNo(page, emsNo, putrecSeqno, impexpMarkcd);
        return Result.OK(pageList);
    }

    /**
     * 根据账册号获取报关数据
     *
     * @param emsNo
     * @param pageNo
     * @param pageSize
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    @ApiOperation(value = "表体分页列表查询", notes = "表体分页列表查询")
    @GetMapping(value = "/listDecByEmsNo")
    public Result<?> listDecByEmsNo(@RequestParam("emsNo") String emsNo,
                                    @RequestParam("putrecSeqno") String putrecSeqno,
                                    @RequestParam("impexpMarkcd") String impexpMarkcd,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<DecHead> page = new Page<>(pageNo, pageSize);
        IPage<DecHead> pageList = nemsInvtHeadService.listDecByEmsNo(page, emsNo, putrecSeqno, impexpMarkcd);
        return Result.OK(pageList);
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/4 10:52
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return nemsInvtHeadService.importExcel(request, response);
    }

    @RequestMapping("/setSeqNoByEtpsNoOrId")
    public Result<NemsInvtHead> setSeqNoByEtpsNoOrId(@RequestParam("id") String id, @RequestParam("etpsNo") String etpsNo,@RequestParam("seqNo") String seqNo) {
        return nemsInvtHeadService.setSeqNoByEtpsNoOrId(id, etpsNo, seqNo);
    }

    @RequestMapping("/updateInvtPartBySeqNo")
    public Result<NemsInvtHead> updateInvtPartBySeqNo(@RequestParam("seqNo") String seqNo, @RequestParam("invtDclTime") Date invtDclTime,
                                                     @RequestParam("entryDclTime") Date entryDclTime,@RequestParam("vrfdedMarkcd") String vrfdedMarkcd,
                                                     @RequestParam("businessId") String businessId, @RequestParam("invtIochkptStucd") String invtIochkptStucd,
                                                     @RequestParam("invtStatus") String invtStatus) {
        return nemsInvtHeadService.updateInvtPartBySeqNo(seqNo, invtDclTime, entryDclTime, vrfdedMarkcd,businessId,invtIochkptStucd,invtStatus);
    }

    @RequestMapping("/setInvtBySeqNo")
    public Result<NemsInvtHead> setInvtBySeqNo(@RequestBody NemsInvtHead nemsInvtHead) {
        return nemsInvtHeadService.setInvtBySeqNo(nemsInvtHead);
    }


    /**
     * 获取核注清单列表
     * 获取符合条件的核注清单列表
     * <p>
     * 参数说明：
     * etpsCategory：要查询企业类型
     * A – 经营单位 B-加工单位 C-申报单位 D-录入单位
     * 为空时，如果查询企业为收发货人，则默认为 A，否则默认为 C。
     * status(数据状态)代码表如下：
     * 0：暂存，1：申报成功 4：成功发送海关 5：海关接收成功 6：海关接收失败
     * B：海关终审通过 C：海关退单 E：删除 P：预审批通过
     * 空代表全部
     * vrfdedMarkcd(核扣标志)代码表如下：
     * 0：未核扣 1：预核扣 2：已核扣 3：已核销 4：反核扣
     * 空代表全部
     * startDate、endDate 为一组日期范围，dclStartDate、dclEndDate 为一组日期范围，形式都为
     * yyyyMMdd，只有当输入 invtNo 或 seqNo 进行查询时，才能两组日期都为空，否则至少输
     * 入一组日期。当使用时间段查询时，查询区间不能大于 90 天。
     *
     * @param swid         操作员卡号
     * @param systemId     子系统代码（必填）
     * @param status       数据状态（可选，默认全部）
     * @param etpsCategory 查询企业类型（选填，默认为 A 或 C 的一种）
     * @param tradeCode    查询企业 10 位海关代码（必填）
     * @param ieFlag       进出口类型（I/E）（可选）
     * @param dclTypecd    申报类型（可选， 1=备案 2=修改 3=删除）
     * @param invtNo       清单编号（可选）
     * @param seqNo        预录入统一编号（可选）
     * @param etpsNo       经营单位编码（可选）
     * @param putrecNo     账册编号（可选，12 位）
     * @param vrfdedMarkcd 核扣标志（可选，代码含义见参数说明）
     * @param startDate    录入日期范围起始（条件可选）
     * @param endDate      录入日期范围截止（条件可选）
     * @param dclStartDate 申报日期范围起始（条件可选）
     * @param dclEndDate   申报日期范围截止（条件可选）
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @GetMapping(value = "/GetInvtList")
    public Result<?> GetInvtList(@RequestParam("swid") String swid,
                                 @RequestParam("systemId") String systemId,
                                 @RequestParam(value = "status", required = false) String status,
                                 @RequestParam(value = "etpsCategory", required = false) String etpsCategory,
                                 @RequestParam("tradeCode") String tradeCode,
                                 @RequestParam(value = "ieFlag", required = false) String ieFlag,
                                 @RequestParam(value = "dclTypecd", required = false) String dclTypecd,
                                 @RequestParam(value = "invtNo", required = false) String invtNo,
                                 @RequestParam(value = "seqNo", required = false) String seqNo,
                                 @RequestParam(value = "etpsNo", required = false) String etpsNo,
                                 @RequestParam(value = "putrecNo", required = false) String putrecNo,
                                 @RequestParam(value = "vrfdedMarkcd", required = false) String vrfdedMarkcd,
                                 @RequestParam(value = "startDate", required = false) String startDate,
                                 @RequestParam(value = "endDate", required = false) String endDate,
                                 @RequestParam(value = "dclStartDate", required = false) String dclStartDate,
                                 @RequestParam(value = "dclEndDate", required = false) String dclEndDate) {
        return nemsInvtHeadService.GetInvtList(swid, systemId, status, etpsCategory, tradeCode, ieFlag, dclTypecd,
                invtNo, seqNo, etpsNo, putrecNo, vrfdedMarkcd, startDate, endDate, dclStartDate, dclEndDate);
    }

    /**
     * 获取核注清单电子数据
     * 获取核注清单的详细信息
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    @GetMapping(value = "/GetInvtData")
    public Result<?> GetInvtData(@RequestParam("swid") String swid,
                                 @RequestParam("systemId") String systemId,
                                 @RequestParam(value = "seqNo", required = false) String seqNo) {
        return nemsInvtHeadService.GetInvtData(swid, systemId, seqNo);
    }

    /**
     * 再次同步未核扣和预核扣的核注单
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    @GetMapping(value = "/syncInvtVrfdedMarkcd")
    public Result<?> syncInvtVrfdedMarkcd(@RequestParam("swid") String swid,
                                          @RequestParam("systemId") String systemId,
                                          @RequestParam("tradeCode") String tradeCode) {
        return nemsInvtHeadService.syncInvtVrfdedMarkcd(swid, systemId, tradeCode, null, null, null);
    }

    /**
     * 再次同步未核扣和预核扣的核注单
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    @GetMapping(value = "/handleSync")
    public Result<?> handleSync() {
        return nemsInvtHeadService.handleSync();
    }

    @GetMapping(value = "/listMonthlyReport")
    public Result<?> listMonthlyReport(ReportManagerDTO reportManagerDTO){
        if(isBlank(reportManagerDTO.getStartInvtDclMonth())||isBlank(reportManagerDTO.getEndInvtDclMonth())){
            return Result.error("请选择起始月份进行查询");
        }
        //获取查询月份的所有汇率
        List<String> monthList=getBetweenMonths(
                reportManagerDTO.getStartInvtDclMonth(),reportManagerDTO.getEndInvtDclMonth());
        List<RateInfo> rateInfoList = rateInfoMapper.listExchangeRateByMonth(monthList);
        if (null==rateInfoList || rateInfoList.isEmpty()) {
            return Result.error("选择的月份未查询到海关汇率");
        }
        //获取全部字母币制和数字币制，需要转换，查询出是数字，汇率表为字母
        List<ErpCurrencies> erpCurrenciesList = erpCurrenciesMapper.selectList(null);
        //分组获取数据
        List<ReportManagerDTO> list = nemsInvtHeadMapper.listMonthlyReport(reportManagerDTO);
        //获取总条数
        Long dclNum = nemsInvtHeadMapper.listMonthlyListEntitySize(reportManagerDTO);
        //表体数量
        Long dclItemNum = Long.valueOf(0);
        //总重量
        BigDecimal netWeightSum = new BigDecimal(0);
        //币值总和
        BigDecimal monetaryTotal = new BigDecimal(0);
        for (ReportManagerDTO reportManagerDTO1 : list) {
            if (isNotEmpty(reportManagerDTO1.getDclItemNum())) {
                dclItemNum = dclItemNum + reportManagerDTO1.getDclItemNum();
            }
            if (isNotEmpty(reportManagerDTO1.getNetWeightSum())) {
                netWeightSum = netWeightSum.add(reportManagerDTO1.getNetWeightSum());
            }
            //处理币值总和统一美元
            if(!"502".equals(reportManagerDTO1.getDclCurrcd())){
                //获取对应月份的海关汇率
                List<RateInfo> exchangeRates=rateInfoList.stream().filter(
                                i-> DateUtil.format(i.getRateDate(),"yyyy-MM").
                                        equals(lastYearMonth(reportManagerDTO1.getInvtDclTime())))
                        .collect(Collectors.toList());
                if(!exchangeRates.isEmpty()){
                    //转化为字母币制
                    List<ErpCurrencies> dictItems=erpCurrenciesList.stream().filter(i->i.getCode().
                                    equals(reportManagerDTO1.getDclCurrcd()))
                            .collect(Collectors.toList());
                    if(!dictItems.isEmpty()){
                        String curr=dictItems.get(0).getCurrency();
                        List<RateInfo> exchangeRateList2=exchangeRates.stream().filter(i->
                                i.getCurrency().equals(curr)).collect(Collectors.toList());
                        RateInfo exchangeRateObj= exchangeRateList2.isEmpty()? exchangeRates.stream().filter(i->
                                i.getCurrency().equals("USD")).collect(Collectors.toList()).get(0) :
                                exchangeRateList2.get(0);

                        //获取对应币制汇率
                        try {
                            BigDecimal currValueUsd = new BigDecimal(1);
                            if("CNY".equals(curr)){
                                Field currFieldUsd2 = exchangeRateObj.getClass().getDeclaredField("rmb");
                                currFieldUsd2.setAccessible(true);
                                //人民币转美元
                                BigDecimal currValueUsd2 = (BigDecimal) currFieldUsd2.get(exchangeRateObj);
                                currValueUsd = new BigDecimal(1).divide(
                                        currValueUsd2,4, BigDecimal.ROUND_HALF_UP);
                            }else {
                                Field currFieldUsd = exchangeRateObj.getClass().getDeclaredField("usd");
                                currFieldUsd.setAccessible(true);
                                //获取美元汇率需要统一转
                                 currValueUsd = (BigDecimal) currFieldUsd.get(exchangeRateObj);
                            }

                            if(isNotEmpty(currValueUsd)){
                                monetaryTotal=monetaryTotal.add(reportManagerDTO1.getMonetaryTotal()
                                        .multiply(currValueUsd));
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

            }else {
                monetaryTotal=monetaryTotal.add(reportManagerDTO1.getMonetaryTotal());
            }
        }
        //返回的list
        List<ReportManagerDTO> monthListRateDclEndList = new ArrayList<ReportManagerDTO>();
        ReportManagerDTO reportManagerDTO12=new ReportManagerDTO();
        reportManagerDTO12.setDclNum(dclNum);
        reportManagerDTO12.setDclItemNum(dclItemNum);
        reportManagerDTO12.setNetWeightSum(netWeightSum.setScale(2, RoundingMode.HALF_UP));
        reportManagerDTO12.setMonetaryTotal(monetaryTotal.setScale(2, RoundingMode.HALF_UP));
        monthListRateDclEndList.add(reportManagerDTO12);
        return Result.OK(monthListRateDclEndList);

    }

    /**
     * 导出出入库月报表
     * @param start
     * @param end
     * @return
     */
    @RequestMapping(value = "/handleExportMonthlyReport")
    public void handleExportMonthlyReport(ReportManagerDTO reportManagerDTO,
                                HttpServletRequest request, HttpServletResponse response) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> dataMap = new HashMap<>(16);

        TemplateExportParams params = new TemplateExportParams();

        String startDateString = "yyyy年mm月dd日";
        String endDateString = "yyyy年mm月dd日";
        String newsheetname = "出/入库表";
        String startYMD=reportManagerDTO.getStartInvtDclMonth()+"-01";
        String endYMD=reportManagerDTO.getEndInvtDclMonth()+"-01";
        DateTime startDate = DateUtil.parseDate(startYMD);
        DateTime endDate = DateUtil.parseDate(endYMD);
        startDateString = DateUtil.format(startDate, "yyyy年MM月dd日");

        DateTime ofMonth = DateUtil.endOfMonth(endDate);//获取月份最后一天（带时分秒）
        DateTime truncate = DateUtil.truncate(ofMonth, DateField.DAY_OF_MONTH);
        endDateString = DateUtil.format(endDate, "yyyy年MM月dd日");
        Boolean note = false;
        long betweenDay = 0;
        //获取当前租户的仓库名称
        List<StoreInfo> storeInfos = storeInfoService.list();
        //获取当前租户的账册
        List<PtsEmsHead> ptsEmsHeads = ptsEmsHeadService.list();

        // 设置忽略租户插件
        //当前登录的企业信息
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        if (isEmpty(enterpriseInfo)) {
            enterpriseInfo = new EnterpriseInfo();
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();

        SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy年MM月",Locale.CHINESE);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        try {
             date = sdf2.parse(reportManagerDTO.getStartInvtDclMonth());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if ("I".equals(reportManagerDTO.getType())) {
            dataMap.put("title", (!storeInfos.isEmpty()?storeInfos.get(0).getStoreName():"")+
                    sdf3.format(date)+"货物入库月报表");
            dataMap.put("explain", "（※本数据统计周期为" + startDateString + "—" + endDateString + "※）");
            dataMap.put("explainTwo", "保税仓库经营单位：  "+enterpriseInfo.getEnterpriseFullName()+"                海关编码："+enterpriseInfo.getCustomsDeclarationCode()+"                       账册编号："+
                    (!ptsEmsHeads.isEmpty()?ptsEmsHeads.get(0).getEmsNo():""));
            dataMap.put("tableName", "入库表:");
            dataMap.put("dateSource", "数据来源:悦通关关务系统申报数据");
            dataMap.put("bondInvtNoTitle", "进口清单编号");
            dataMap.put("entryDclTimeTitleTh", "实际入库时间");
            dataMap.put("invtDclTimeTitle", "清单申报日期");
            dataMap.put("dclQtyTitle", "入库数量");
            newsheetname = "入库表";
            //获取模板文件路径
            Workbook templateWorkbook = null;
            try {
                templateWorkbook = WorkbookFactory.create(
                        this.getClass().getResourceAsStream("/templates/xls/月度表关务系统导出模板入库.xls"));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            params.setTemplateWb(templateWorkbook);


        } else if ("E".equals(reportManagerDTO.getType())) {
            dataMap.put("title", (!storeInfos.isEmpty()?storeInfos.get(0).getStoreName():"")+
                    sdf3.format(date)+"货物出库月报表");
            dataMap.put("explain", "（※本数据统计周期为" + startDateString + "—" + endDateString + "※）");
            dataMap.put("explainTwo", "保税仓库经营单位：  "+enterpriseInfo.getEnterpriseFullName()+"                海关编码："+enterpriseInfo.getCustomsDeclarationCode()+"                       账册编号："+
                    (!ptsEmsHeads.isEmpty()?ptsEmsHeads.get(0).getEmsNo():""));
            dataMap.put("tableName", "出库表:");
            dataMap.put("dateSourceE", "数据来源:悦通关关务系统申报数据");
            dataMap.put("bondInvtNoTitle", "出口清单编号");
            dataMap.put("entryDclTimeTitleTh", "实际出库时间");
            dataMap.put("invtDclTimeTitle", "清单申报日期");
            dataMap.put("dclQtyTitle", "出库数量");
            newsheetname = "出库表";
            note = true;
            //获取模板文件路径
            Workbook templateWorkbook = null;
            try {
                templateWorkbook = WorkbookFactory.create(
                        this.getClass().getResourceAsStream("/templates/xls/月度表关务系统导出模板出库.xls"));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            params.setTemplateWb(templateWorkbook);
        }
        //存放表体数据
        List<Map<String, Object>> listMapList = new ArrayList<>();
        //查询表体数据
        List<MonthlyListEntity> list=nemsInvtHeadMapper.listMonthlyListEntity
                (reportManagerDTO.getStartInvtDclMonth(), reportManagerDTO.getEndInvtDclMonth(),
                        reportManagerDTO.getType());
        if(!list.isEmpty()){
            //币值字典
            List<ErpCurrencies> erpCurrenciesList = erpCurrenciesMapper.selectList(null);
            List<ErpUnits> erpUnits = erpUnitsService.list();
            for (MonthlyListEntity invtList : list) {
                Map<String, Object> lm = new HashMap<>();
                // 账册项号
                lm.put("putrecSeqno", isNotBlank(invtList.getPutrecSeqno()) ? invtList.getPutrecSeqno() : "");
                // 清单作业编号
                lm.put("bondInvtNo", isNotBlank(invtList.getBondInvtNo()) ? invtList.getBondInvtNo() : "");
                //规格型号
                lm.put("gdsMtno", isNotBlank(invtList.getGdsMtno()) ? invtList.getGdsMtno() : "");
                //报关单号
                lm.put("dclEndNo", isNotBlank(invtList.getDclEndNo()) ? invtList.getDclEndNo() : "保税间结转货物，非报关");
                //清单申报日期
                lm.put("invtDclTime", ObjectUtil.isNotEmpty(invtList.getInvtDclTime()) ? sdf.format(invtList.getInvtDclTime()) : "");
                //报关单申报日期
                lm.put("entryDclTime", ObjectUtil.isNotEmpty(invtList.getDeclarationDate()) ? sdf.format(invtList.getDeclarationDate()) : "");
                // 数量               BJ invtList.getDclQty().setScale(2, RoundingMode.HALF_UP)
//                  if(isNotEmpty(invtList.getDclQty())){
//                      BigDecimal decimal = new BigDecimal(BigDecimal.valueOf(Double.parseDouble(String.valueOf(invtList.getDclQty().setScale(4, RoundingMode.HALF_UP)))).stripTrailingZeros().toPlainString());
//                      lm.put("dclQty", decimal.doubleValue());
//                  }
//                  else{
//                      lm.put("dclQty", "");
//                  }
                lm.put("dclQty",invtList.getDclQty() != null ? invtList.getDclQty() : "");
                // 单位
                List<ErpUnits> erpUnits1=erpUnits.stream()
                        .filter(i->i.getCode().equals(invtList.getDclUnitcd()))
                        .collect(Collectors.toList());
                lm.put("dclUnitcd", ObjectUtil.isNotEmpty(erpUnits1) ? erpUnits1.get(0).getName() : "");

                if (note) {
                    lm.put("importBillNo", ObjectUtil.isNotEmpty(invtList.getImportBillNo()) ? invtList.getImportBillNo() : "");
                }
                if("2".equals(invtList.getDclcusFlag())){
                    lm.put("remark", "非报关");
                }
//                if("I".equals(reportManagerDTO.getType())){
                    //企业内编
                    lm.put("etpsInnerInvtNo",isNotBlank(invtList.getEtpsInnerInvtNo()) ? invtList.getEtpsInnerInvtNo() : "");
//                }else {
//                    //出库的显示类别
//                    //“带有PT字样的，类型为转税；带有RT字样的，类型为新件退运，其他为安装件，”这里会将那种担保不足起征点的，也算作安装件
//                    if(isNotBlank(invtList.getEtpsInnerInvtNo())){
//                        if(invtList.getEtpsInnerInvtNo().contains("PT")){
//                            lm.put("etpsInnerInvtNo","转税");
//                        }else if(invtList.getEtpsInnerInvtNo().contains("RT")){
//                            lm.put("etpsInnerInvtNo","新件退运");
//                        }else {
//                            lm.put("etpsInnerInvtNo","安装件");
//                        }
//                    }
//                }
                lm.put("dclUprcamt",invtList.getDclUprcamt() != null ? invtList.getDclUprcamt() : "");
                lm.put("dclTotalamt",invtList.getDclTotalamt() != null ? invtList.getDclTotalamt() : "");

                List<ErpCurrencies> dictItems=erpCurrenciesList.stream()
                        .filter(i->i.getCode().equals(invtList.getDclCurrcd()))
                        .collect(Collectors.toList());
                lm.put("dclCurrcd", ObjectUtil.isNotEmpty(dictItems) ? dictItems.get(0).getName() : "");

                lm.put("netWeight",invtList.getNetWeight() != null ? invtList.getNetWeight() : "");
                listMapList.add(lm);

            }
        }
        dataMap.put("listMapList", listMapList); // 核注单表体数据
        Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    //获取两个月份之间的月份字符串
    public List<String> getBetweenMonths(String start, String end) {
        List<String> months = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        Calendar startSign = Calendar.getInstance();
        try {
            startSign.setTime(sdf.parse(start));
            startSign.add(Calendar.MONTH, -1);
            Calendar endSign = Calendar.getInstance();
            endSign.setTime(sdf.parse(end));
            while (startSign.before(endSign)) {
                months.add(sdf.format(startSign.getTime()));
                startSign.add(Calendar.MONTH, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return months;
    }
    //获取上个月的年月
    private String lastYearMonth(String dateString){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar startSign = Calendar.getInstance();
        try {
            startSign.setTime(sdf.parse(dateString));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        startSign.add(Calendar.MONTH, -1);
        return sdf.format(startSign.getTime());
    }

    @GetMapping(value = "/handleWriteOffBalance")
    public Result<List<NemsListByWriteOffBalanceVO>> handleWriteOffBalance(@RequestParam(value = "startDate",required = false) String startDate,
                                           @RequestParam(value = "lastDate",required = false) String lastDate){
        List<NemsListByWriteOffBalanceVO> nemsListByWriteOffBalanceVOS =
                nemsInvtHeadMapper.listByWriteOffBalance(startDate, lastDate, TenantContext.getTenant());
        //查询符合日期段的导入表数据的表体数据
        List<ReportList> reportLists = reportListMapper.selectList(new LambdaQueryWrapper<ReportList>()
                .ge(isNotBlank(startDate), ReportList::getStartDate, startDate)
                .le(isNotBlank(lastDate), ReportList::getEndDate, lastDate));
        if(reportLists.isEmpty()){
            return Result.error("未查询到该周期的报核数据，暂时无法进行核销平衡。");
        }
        //计量单位字典
        List<ErpUnits> erpUnits = erpUnitsService.list();
        //依次循环查询出核注单表体数据，进行消耗总量的计算
        for (NemsListByWriteOffBalanceVO nemsListByWriteOffBalanceVO : nemsListByWriteOffBalanceVOS) {
            //获取当前物料号的表体数据
            List<ReportList> reportListList = reportLists.stream().filter(i->
                    i.getInPnBig().equals(nemsListByWriteOffBalanceVO.getCopGno())).collect(Collectors.toList());
            //该物料号的单耗==总实耗/出口数量
            //总实际耗用
            BigDecimal actualConsumption=reportListList.stream().map(ReportList::getInQty)
                    .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //总出口数量
            BigDecimal exportQty=reportListList.stream().map(ReportList::getOutQty)
                    .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算单耗
            BigDecimal unitConsumption=exportQty.compareTo(BigDecimal.ZERO)==0? (BigDecimal.ZERO):
                    (actualConsumption.divide(exportQty,9,BigDecimal.ROUND_HALF_UP));
            //计算消耗总量
            nemsListByWriteOffBalanceVO.setConsumeQty(nemsListByWriteOffBalanceVO.getImportQty().multiply(unitConsumption));
            //计算剩余数量=进口总量-消耗总量-内销数量-余料转出数量
            nemsListByWriteOffBalanceVO.setRemainingQty(nemsListByWriteOffBalanceVO.getImportQty()
                   .subtract(nemsListByWriteOffBalanceVO.getConsumeQty())
                   .subtract(nemsListByWriteOffBalanceVO.getMarketQty())
                   .subtract(nemsListByWriteOffBalanceVO.getRollOutQty()));
            //转换币制
            if(isNotBlank(nemsListByWriteOffBalanceVO.getUnit())){
                List<ErpUnits> erpUnits1=erpUnits.stream().
                       filter(i->i.getCode().equals(nemsListByWriteOffBalanceVO.getUnit())).collect(Collectors.toList());
                nemsListByWriteOffBalanceVO.setUnit(erpUnits1.isEmpty()?"":erpUnits1.get(0).getName());
            }
        }


        return Result.OK(nemsListByWriteOffBalanceVOS);

    }

    /**
     * 核销平衡表导出
     * @param startDate
     * @param lastDate
     * @param request
     * @param response
     */
    @RequestMapping(value = "/exportWriteOffBalance")
    public void exportWriteOffBalance(@RequestParam(value = "startDate",required = false) String startDate,
                                         @RequestParam(value = "lastDate",required = false) String lastDate,
                                         HttpServletRequest request, HttpServletResponse response) {
        Result<List<NemsListByWriteOffBalanceVO>> result = this.handleWriteOffBalance(startDate, lastDate);
        if(result.getResult().size()>0){
            List<NemsListByWriteOffBalanceVO> nemsListByWriteOffBalanceVOS=result.getResult();
            cn.afterturn.easypoi.excel.entity.ExportParams params = new ExportParams();
            params.setSheetName("账册核销平衡表");
            params.setType(ExcelType.XSSF);
            params.setStyle(ExcelExportStylerBorderImpl.class);
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> mapInv = new HashMap<>();
            mapInv.put("title", params);
            mapInv.put("entity", NemsListByWriteOffBalanceVO.class);
            mapInv.put("data", nemsListByWriteOffBalanceVOS);
            list.add(mapInv);
            Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);
            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            // 下载文件能正常显示中文
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream fos = null;
            FileOutputStream FileFos = null;
            try {
                //普通下载
                fos = response.getOutputStream();
                workbook.write(fos);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (FileFos != null) {
                        FileFos.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 核销平衡后的单耗表导出
     * @param startDate
     * @param lastDate
     * @param request
     * @param response
     */
    @RequestMapping(value = "/exportConsumption")
    public void exportConsumption(@RequestParam(value = "startDate",required = false) String startDate,
                                      @RequestParam(value = "lastDate",required = false) String lastDate,
                                      HttpServletRequest request, HttpServletResponse response) {
        //查询符合日期段的导入表数据的表体数据
        List<ReportList> reportLists = reportListMapper.selectList(new LambdaQueryWrapper<ReportList>()
                .ge(isNotBlank(startDate), ReportList::getStartDate, startDate)
                .le(isNotBlank(lastDate), ReportList::getEndDate, lastDate)
                .eq(ReportList::getTenantId, TenantContext.getTenant()));
        if(!reportLists.isEmpty()){
            //查询出全部料件，成品，用于根据导入的历史报核数据获取成品料件的序号
            List<PtsEmsAimg> ptsEmsAimgs = nemsInvtHeadMapper.listAimgByTenantId(TenantContext.getTenant());
            // 将 List 转换为 Map，键为 id，值为 name
            Map<String, Integer> aimgsMap = ptsEmsAimgs.stream()
                    .collect(Collectors.toMap(PtsEmsAimg::getCopGno, PtsEmsAimg::getGNo));
            List<PtsEmsAexg> ptsEmsAexgs = nemsInvtHeadMapper.listAexgByTenantId(TenantContext.getTenant());;
            Map<String, Integer> aexgMap = ptsEmsAexgs.stream()
                    .collect(Collectors.toMap(PtsEmsAexg::getCopGno, PtsEmsAexg::getGNo));
            //以成品料号为主键
            Map<String, List<ReportList>> groupedReportList =
                    reportLists.stream().filter(i-> StringUtils.isNotBlank(i.getOutPnBig()))
                    .collect(Collectors.groupingBy(ReportList::getOutPnBig));
            //要导出的数据集合
            List<ExportConsumptionEntity> exportConsumptionEntityList=new ArrayList<>();
            groupedReportList.forEach((outPnBigItem, reportListList) -> {
                //存在多个相同的投入物料，需要进行汇总
                //以投入物料号为主键
                Map<String, List<ReportList>> groupedReportListByInPnBig = reportListList.stream()
                        .collect(Collectors.groupingBy(ReportList::getInPnBig));
                groupedReportListByInPnBig.forEach((inPnBigItem, reportListListIn) -> {
                    ExportConsumptionEntity exportConsumptionEntity = new ExportConsumptionEntity();
                    exportConsumptionEntity.setAexgGno(aexgMap.getOrDefault(outPnBigItem, 0));//成品序号
                    exportConsumptionEntity.setAimgGno(aimgsMap.getOrDefault(inPnBigItem,0));//料件序号
                    exportConsumptionEntity.setUcnsverno(
                            String.valueOf(exportConsumptionEntity.getAexgGno()));//单耗版本号(暂定和成品序号一致)
                    //实耗
                    BigDecimal actualConsumption=reportListListIn.stream().map(ReportList::getInQty)
                            .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //出口数量
                    BigDecimal exportQty=reportListList.stream().map(ReportList::getOutQty)
                            .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    exportConsumptionEntity.setDecCm(exportQty.compareTo(BigDecimal.ZERO)==0? ("0"):
                            (actualConsumption.divide(exportQty,9, RoundingMode.HALF_UP)
                                    .stripTrailingZeros().toPlainString()));//单耗/净耗=实耗/出口数量
                    exportConsumptionEntity.setDecInlossrate(BigDecimal.ZERO);//无形损耗率
                    exportConsumptionEntity.setUcnsstcd("2");//单耗申报状态代码
                    exportConsumptionEntity.setDecBondrate(new BigDecimal(100));//保税料件比例(%)
                    exportConsumptionEntity.setUcnsmodflg("0");//修改标志
                    exportConsumptionEntityList.add(exportConsumptionEntity);

                });
            });

            List<ExportConsumptionEntity> sortedExportConsumptionEntityList = exportConsumptionEntityList.stream()
                    .sorted(Comparator.comparing(ExportConsumptionEntity::getAexgGno)
                            .thenComparing(ExportConsumptionEntity::getAimgGno))
                    .collect(Collectors.toList());
            AtomicReference<Integer> index= new AtomicReference<>(1);//序号
            sortedExportConsumptionEntityList.forEach(i->{
                i.setIndex(index.get());
                index.getAndSet(index.get() + 1);
            });

            //生成excel
            cn.afterturn.easypoi.excel.entity.ExportParams params = new ExportParams();
            params.setSheetName("单耗表");
            params.setType(ExcelType.XSSF);
            params.setStyle(ExcelExportStylerBorderImpl.class);
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> mapInv = new HashMap<>();
            mapInv.put("title", params);
            mapInv.put("entity", ExportConsumptionEntity.class);
            mapInv.put("data", sortedExportConsumptionEntityList);
            list.add(mapInv);
            Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);
            //个别单元格转为数字格式
            // 创建数字格式
            DataFormat format = workbook.createDataFormat();
            CellStyle style = workbook.createCellStyle();
            style.setDataFormat(format.getFormat("#,##0.000000000"));
            style.setBorderBottom(BorderStyle.THIN);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setAlignment(HorizontalAlignment.CENTER);

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            for(int i =1;i<=sheet.getLastRowNum();i++){
                Row row = sheet.getRow(i); // 创建行
                Cell cell = row.getCell(4); // 创建单元格
                cell.setCellValue(Double.parseDouble(cell.getStringCellValue()));
                // 应用之前创建的样式到单元格
                cell.setCellStyle(style);
            }

            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            // 下载文件能正常显示中文
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream fos = null;
            FileOutputStream FileFos = null;
            try {
                //普通下载
                fos = response.getOutputStream();
                workbook.write(fos);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (FileFos != null) {
                        FileFos.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 给前端返回需要的所有字段
     * <p>
     * type: 1-报关单 2-核注单
     *
     * @param type
     * @return com.yorma.entity.YmMsg<java.lang.String>
     * <AUTHOR>
     * @date 2021/3/29 11:20
     */
    @GetMapping("/listInvtFields")
    public Result<List<DefColumnsVO>> listDecFields(String type) {
        type = isBlank(type) ? "1" : type; // 默认报关单
        List<DefColumnsVO> defColumnsVOS = new ArrayList<>();
        Field[] fields;
        // 报关单
        if ("1".equals(type)) {
            fields = DecHead.class.getDeclaredFields();
        } else if ("2".equals(type)) {
            fields = NemsInvtHead.class.getDeclaredFields();
        } else {
            return Result.error("未知的类型！");
        }
            for (Field field : fields) {
                if (null != field.getAnnotation(Excel.class)) {
                    DefColumnsVO columnsVO = new DefColumnsVO();
                    columnsVO.setFieldName(field.getAnnotation(Excel.class).name());
                    columnsVO.setField(field.getName());
                    defColumnsVOS.add(columnsVO);
                }
            }
        return Result.ok(defColumnsVOS);
    }
    /**
     * 导出核注清单列表
     * @param request
     * @param response
     */
    @RequestMapping(value = "/exportInvtHeadByFieldsBatch")
    public void exportInvtHeadByFieldsBatch(NemsInvtHeadDTO nemsInvtHeadVO,
                                            HttpServletRequest request, HttpServletResponse response){
        List<NemsInvtHead> invtHeads;
        if(!isEmpty(nemsInvtHeadVO.getIds())){
            invtHeads = nemsInvtHeadMapper.selectBatchIds(Arrays.asList(nemsInvtHeadVO.getIds().split(",")));
        }else{
            invtHeads = nemsInvtHeadService.queryPageList(new Page<>(1,9999999), nemsInvtHeadVO,request).getRecords();
        }
        //生成excel
        org.jeecgframework.poi.excel.entity.ExportParams exportParams = new org.jeecgframework.poi.excel.entity.ExportParams();
        exportParams.setSheetName("核注单列表");
        exportParams.setType(org.jeecgframework.poi.excel.entity.enmus.ExcelType.XSSF);
        exportParams.setAddIndex(true);
        //处理导出的数据
        Workbook workbook = org.jeecgframework.poi.excel.ExcelExportUtil.exportExcel(exportParams, NemsInvtHead.class,
                invtHeads,nemsInvtHeadVO.getColumnList());
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
