<template>
	<a-modal
		:maskClosable="false"
		:title="title"
		:visible="visible"
		:width="width"
		cancelText="关闭"
		@cancel="handleCancel"
	>
		<template slot="footer">
			<a-button icon="bulb" size="small" @click="handleCancel">关闭</a-button>
		</template>

		<!-- table区域-begin -->
		<div>
			<a-spin :spinning="loading">
				<template>
					<vxe-grid
						id="vxe-gridA"
						ref="xGrid"
						:columns="defColumns"
						:custom-config="{ storage: true }"
						:data="dataSource"
						:loading="loading"
						:row-config="{keyField: 'id', isCurrent: true, isHover: true}"
						:show-overflow=true
						:toolbar-config="tableToolbar"
						:transfer="true"
						:empty-text="loading ? '正在加载数据...' : '暂无船舶计划数据'"
						border
						class="xGrid-style"
						highlightHoverRow
						keep-source
						resizable
						row-id="id"
						show-overflow="tooltip"
						size="mini"
					>
						<template v-slot:toolbar_buttons>
						</template>
					</vxe-grid>
				</template>
			</a-spin>
		</div>
	</a-modal>
</template>
<script>
import {getAction} from "@/api/manage";
import {XGridMixin} from "@/mixins/XGridMixin";

export default {
	name: "ContainerCargoNodeModal",
	mixins: [XGridMixin],
	data() {
		return {
			tableToolbar: {
				perfect: true,
				refresh: {
					query: ()=> this.loadData(1)
				},
				zoom: true,
				custom: true,
				slots: {
					buttons: 'toolbar_buttons'
				}
			},
			/* 数据源 */
			dataSource: [],
			defColumns: [
				{
					title: '序号',
					field: 'serialNumber',
					width: 80,
					align: 'center'
				},
				{
					title: '靠泊单位',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '中文船名',
					field: 'zwcm',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '英文船名',
					field: 'ywcm',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '进口航次',
					field: 'jkhc',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '出口航次',
					field: 'ckhc',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '预计到港时间',
					field: 'eta',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '预计离港时间',
					field: 'etd',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '实际到港时间',
					field: 'sjdgsj',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '实际离港时间',
					field: 'sjlgsj',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '收箱开始时间',
					field: 'sxkssj',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '收箱结束时间',
					field: 'sxjssj',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '航线代码',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '船名代码',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '航线',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '抵港状态',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '进口代理',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '承运人',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '内外贸',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '位置',
					field: 'dlwz',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '泊位',
					field: '',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					title: 'IMO号',
					field: 'cimo',
					width: 150,
					ellipsis: true,
					align: 'center'
				}
			],
			title: "集装箱船舶计划",
			width: 1000,
			visible: false,
			disableMixinCreated: true,
			disabled: false,
			labelCol: {
				xs: {span: 24},
				sm: {span: 10},
			},
			wrapperCol: {
				xs: {span: 24},
				sm: {span: 14},
			},
			model: {
				id: '',
				shipName: '',
				voyage: ''
			},
			url: {
				listContainerCargoNodeByDecId: '/business/shipPlan/listContainerCargoNodeByDecId',
			}
		}
	},
	computed: {
		formDisabled() {
			return this.disabled
		}
	},
	methods: {
		async open(record) {
			if (this.isEmpty(record)) {
				this.$message.warning('报关数据未找到!')
				return false
			}

			// 立即显示模态框，提升用户体验
			this.model = Object.assign({}, record)
			this.visible = true

			// 加载数据
			await this.loadDataInternal(false) // false表示不显示刷新成功消息
		},
		/**
		 * 加载数据方法（供工具栏刷新按钮调用）
		 * @param pageNo 页码（保留参数以兼容工具栏调用）
		 */
		async loadData(pageNo) {
			await this.loadDataInternal(true) // true表示显示刷新成功消息
		},
		/**
		 * 内部数据加载方法
		 * @param showSuccessMessage 是否显示刷新成功消息
		 */
		async loadDataInternal(showSuccessMessage = false) {
			if (!this.model || !this.model.id) {
				console.warn('没有可用的记录ID，无法加载数据')
				return
			}

			this.loading = true
			this.dataSource = [] // 清空当前数据

			try {
				const res = await getAction(this.url.listContainerCargoNodeByDecId, {
					decId: this.model.id,
				})

				if (res.success) {
					// 后端只返回一条数据，直接使用结果
					this.dataSource = res.result.records || res.result || []
					if (showSuccessMessage) {
						this.$message.success('数据刷新成功')
					}
				} else {
					this.dataSource = []
					// this.$message.warning(res.message || '查询船舶计划数据失败')
				}
			} catch (error) {
				console.error('查询船舶计划数据异常：', error)
				this.dataSource = []
				this.$message.error('查询船舶计划数据异常，请稍后重试')
			} finally {
				this.loading = false
			}
		},
		close() {
			this.dataSource = []
			this.visible = false
		},
		handleCancel() {
			this.close()
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-modal-body {
	padding: 12px;
}
</style>