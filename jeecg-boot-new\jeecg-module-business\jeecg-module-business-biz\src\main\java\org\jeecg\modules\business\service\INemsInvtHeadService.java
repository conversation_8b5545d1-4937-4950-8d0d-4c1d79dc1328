package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.DecHead;
import org.jeecg.modules.business.entity.DecList;
import org.jeecg.modules.business.entity.NemsInvtHead;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.business.entity.NemsInvtList;
import org.jeecg.modules.business.entity.dto.NemsInvtHeadDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 核注清单表头 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface INemsInvtHeadService extends IService<NemsInvtHead> {

    /**
     * 分页列表查询
     *
     * @return
     */
    IPage<NemsInvtHead> queryPageList(Page<NemsInvtHead> page, NemsInvtHeadDTO nemsInvtHeadDTO, HttpServletRequest request);

        /**
     * 根据核注单id获取库存数据
     *
     * @param id 核注单id
     * @return NemsInvtHead对象，表示库存数据头部信息
     */
    NemsInvtHead getInvtById(String id);

    /**
     * 根据分页和库存id获取库存数据列表
     *
     * @param page 分页对象
     * @param invId 库存id
     * @return IPage<NemsInvtList>对象，表示库存数据列表页面
     */
    IPage<NemsInvtList> listInvt(Page<NemsInvtList> page, String invId);

    /**
     * 根据库存id和项目获取盘亏列表
     *
     * @param invId 库存id
     * @param item 项目
     * @return DecList对象，表示盘亏列表
     */
    List<DecList> getDecListByInvtIdAndItem(String invId, String item,String entryNo);

    /**
     * 根据核注单id获取报关单数据
     *
     * @param id 核注单id
     * @return DecHead对象，表示报关单数据
     */
    DecHead getInvtIdDec(String id);

    /**
     * 保存库存数据
     *
     * @param nemsInvtHead NemsInvtHead对象，表示库存数据头部信息
     * @return Result<?>对象，表示保存结果
     */
    Result<?> saveInvt(NemsInvtHead nemsInvtHead);

    /**
     * 删除一批库存数据
     *
     * @param ids id字符串，多个id用逗号分隔
     * @return Result<?>对象，表示删除结果
     */
    Result<?> delBatch(String ids);

    /**
     * 处理初步审核
     *
     * @param ids 要处理的文档ID
     * @param initialReviewStatus 初步审核状态
     * @param opinion 意见
     * @return 处理结果
     */
    Result<?> handleInitialReview(String ids, String initialReviewStatus, String opinion);

    Result<?> saveNemsInvtHeadBatch(List<NemsInvtHead> nemsInvtHeadList);

    /**
     * 根据统一编号查询核注单信息
     *
     * @param seqNo 统一编号
     * @return 核注单信息
     */
    Result<NemsInvtHead> getInvtBySeqNo(String seqNo);
    /**
     * 查询清单记录列表
     *
     * @param putrecNo 入库条码
     * @param putrecSeqno 入库顺序号
     * @param mtpckEndprdMarkcd 包装结束标记
     * @param vrfdedMarkcd 核对标记
     * @param pageNum 当前页码
     * @param pageSize 每页数量
     * @return 符合条件的清单记录结果
     */
    Result<?> listDeclarationRecord(String putrecNo, String putrecSeqno, String mtpckEndprdMarkcd, String vrfdedMarkcd, Integer pageNum, Integer pageSize);

    /**
     * 修改DCL数量
     *
     * @param invtListId  发票列表ID
     * @param emsNo       仓库编码
     * @param gNo         标签号
     * @param dclQty      DCL数量
     * @param stockQty    库存数量
     * @param type        类型
     * @return 结果
     */
    Result<?> editDclQty(String invtListId, String emsNo, String gNo, String dclQty, String stockQty, String type);
    /**
     * 预核扣130：核注单预核扣回执，检测此委托下的所有核注单是否都大于等于预核扣（即：预核扣或者已核扣）
     * @apiNote
     * <pre>
     *   预核扣130：核注单预核扣回执，检测此委托下的所有核注单是否都大于等于预核扣（即：预核扣或者已核扣）
     * </pre>
     *
     * @param nemsInvtHead
     * @return com.yorma.entity.YmMsg<com.yorma.dcl.entity.NemsInvtHead>
     *
     * <AUTHOR> 2022/6/2 9:46
     * @version 1.0
     */
    Result<NemsInvtHead> handleWithholdingUpdateApplyNodeStatus(NemsInvtHead nemsInvtHead);

    /**
     * 根据核注单统一编号回填报关单统一编号
     *
     * @param invtSeqNo 核注单统一编号
     * @param seqNo     报关单统一编号
     * @return 是否更新数据
     */
    Result<DecHead> setSeqNoByInvtSeqNo(String invtSeqNo, String seqNo);

    /**
     * 变更核注清单表体备案序号
     * @param seqNo 统一编号
     * @param putrecSeqnos 表体序号，备案序号
     * @return 核注单信息
     */
    Result<Boolean> updateInvtLists(String seqNo, List<String > putrecSeqnos);
    /**
     * 删除库存列表
     *
     * @param invtListId   库存列表ID
     * @param emsNo        物流公司单号
     * @param gNo          商品编号
     * @param dclQty       商品数量
     * @param type         类型
     * @return Result<?>   结果
     */
    Result<?> delInvtList(String invtListId, String emsNo, String gNo, String dclQty, String type);

    /**
     * 手动扣除处理方法
     *
     * @param ids           待处理的ID集合
     * @param warehousingDate 正式生效时间
     * @return 处理结果
     */
    Result<?> handleManualDeduction(String ids, String warehousingDate, String type);

    /**
     * 处理账册库存数量
     *
     * @param nemsInvtHeads
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/25 10:29
     */
    Result<?> processingEmsStockQuantities(List<NemsInvtHead> nemsInvtHeads);

    /**
     * 核注单生成报关单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */
    Result<?> handleCreateDecByInvt(String id);

    /**
     * 核注单生成核放单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/18 10:15
     */
    Result<?> handleCreatePassByInvt(String id);

    /**
     * 核注单推送报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/1/30 9:15
     */
    Result<?> handlePush(String ids, String passageway);

    /**
     * 根据部门编码、社会编码或部门名称获取企业信息
     * @param flag 标志位，取值为"deptCode"、"socialCode"或"deptName"
     * @param searchText 搜索关键字
     * @return 企业信息
     */
    Result<?> getEnterpriseByDepartcdOrSocialCodeOrDepartName(String flag, String searchText);

    /**
     * 获取四个统计平方数的结果。
     *
     * @return 四个统计平方数的结果
     */
    Result<?> getFourStatisticalSquares();

    /**
     * 根据账册号获取清单数据
     *
     * @param page
     * @param emsNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    IPage<NemsInvtHead> listInvtByEmsNo(Page<NemsInvtHead> page, String emsNo, String putrecSeqno, String impexpMarkcd);

    /**
     * 根据账册号获取报关数据
     *
     * @param page
     * @param emsNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/17 上午10:25
     */
    IPage<DecHead> listDecByEmsNo(Page<DecHead> page, String emsNo, String putrecSeqno, String impexpMarkcd);

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/4 10:52
     */
    Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 获取核注清单列表
     * 获取符合条件的核注清单列表
     * <p>
     * 参数说明：
     * etpsCategory：要查询企业类型
     * A – 经营单位 B-加工单位 C-申报单位 D-录入单位
     * 为空时，如果查询企业为收发货人，则默认为 A，否则默认为 C。
     * status(数据状态)代码表如下：
     * 0：暂存，1：申报成功 4：成功发送海关 5：海关接收成功 6：海关接收失败
     * B：海关终审通过 C：海关退单 E：删除 P：预审批通过
     * 空代表全部
     * vrfdedMarkcd(核扣标志)代码表如下：
     * 0：未核扣 1：预核扣 2：已核扣 3：已核销 4：反核扣
     * 空代表全部
     * startDate、endDate 为一组日期范围，dclStartDate、dclEndDate 为一组日期范围，形式都为
     * yyyyMMdd，只有当输入 invtNo 或 seqNo 进行查询时，才能两组日期都为空，否则至少输
     * 入一组日期。当使用时间段查询时，查询区间不能大于 90 天。
     *
     * @param swid         操作员卡号
     * @param systemId     子系统代码（必填）
     * @param status       数据状态（可选，默认全部）
     * @param etpsCategory 查询企业类型（选填，默认为 A 或 C 的一种）
     * @param tradeCode    查询企业 10 位海关代码（必填）
     * @param ieFlag       进出口类型（I/E）（可选）
     * @param dclTypecd    申报类型（可选， 1=备案 2=修改 3=删除）
     * @param invtNo       清单编号（可选）
     * @param seqNo        预录入统一编号（可选）
     * @param etpsNo       经营单位编码（可选）
     * @param putrecNo     账册编号（可选，12 位）
     * @param vrfdedMarkcd 核扣标志（可选，代码含义见参数说明）
     * @param startDate    录入日期范围起始（条件可选）
     * @param endDate      录入日期范围截止（条件可选）
     * @param dclStartDate 申报日期范围起始（条件可选）
     * @param dclEndDate   申报日期范围截止（条件可选）
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    Result<?> GetInvtList(String swid, String systemId, String status, String etpsCategory, String tradeCode, String ieFlag, String dclTypecd, String invtNo,
                          String seqNo, String etpsNo, String putrecNo, String vrfdedMarkcd, String startDate, String endDate, String dclStartDate, String dclEndDate);

    /**
     * 获取核注清单电子数据
     * 获取核注清单的详细信息
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    Result<?> GetInvtData(String swid, String systemId, String seqNo);

    /**
     * 再次同步未核扣和预核扣的核注单
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    Result<?> syncInvtVrfdedMarkcd(String swid, String systemId, String tradeCode, String startTime, String endTime, String isAll);

    Result<NemsInvtHead> setSeqNoByEtpsNoOrId(String id, String etpsNo, String seqNo);
    Result<NemsInvtHead> setSeqNoByEtpsNoOrIdForRecepit(String id, String etpsNo, String seqNo);
    /**
     * :根据清单统一编号变更核注清单的关键数据
     *
     * @param seqNo        统一编号
     * @param invtDclTime  核注单申报日期
     * @param entryDclTime 报关单申报日期
     * @param vrfdedMarkcd 核扣状态
     * @param businessId   清单编号
     * @param invtIochkptStucd 卡口状态
     * @return 核注单信息
     */
    Result<NemsInvtHead> updateInvtPartBySeqNo(String seqNo, Date invtDclTime, Date entryDclTime, String vrfdedMarkcd,
                                              String businessId, String invtIochkptStucd, String invtStatus);

    Result<NemsInvtHead> setInvtBySeqNo(NemsInvtHead nemsInvtHead);

    Result<?> handleSync();

}
