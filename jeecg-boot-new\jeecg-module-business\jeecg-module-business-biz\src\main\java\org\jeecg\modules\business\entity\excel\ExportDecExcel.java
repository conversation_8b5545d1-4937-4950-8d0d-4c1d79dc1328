package org.jeecg.modules.business.entity.excel;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.DecContainerFields;
import org.jeecg.common.aspect.annotation.DecHeadFields;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * ExportDecExcel
 * <pre>
 *   报关单详情导出实体
 * </pre>
 *
 * <AUTHOR>  2024/2/22 14:32
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
public class ExportDecExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 统一编号
     */
    @Excel(name = "统一编号",width = 16)
    @DecHeadFields
    private String seqNo;
    /**
     * 报关单号
     */
    @Excel(name = "报关单号",width = 22)
    @DecHeadFields
    private String clearanceNo;
    /**
     * 申报日期
     */
    @Excel(name = "申报日期",width = 26)
    @DecHeadFields
    private String appDate;
    /**
     * 申报状态
     */
    @Excel(name = "申报状态",width = 16,replace = {"保存_1","结关_10","查验通知_11","已申报_2","海关入库成功_4","退单_6","审结_7",
            "删单_8","放行_9","公自用物品核准通过_S","公自用物品退单_T","公自用物品待核准_U"})
    @DecHeadFields
    private String decStatus;
    /**
     * 进出口
     */
    @Excel(name = "进出口",width = 16,replace = {"进口_I","出口_E"})
    @DecHeadFields
    private String ieFlag;
    /**
     * 申报地海关
     */
    @Excel(name = "申报地海关",width = 16)
    @DecHeadFields
    private String declarePlace;
    /**
     * 出入境关别
     */
    @Excel(name = "出入境关别",width = 16)
    @DecHeadFields
    private String outPortCode;
    /**
     * 境内收发货人
     */
    @Excel(name = "境内收发货人",width = 26)
    @DecHeadFields
    private String optUnitName;
    /**
     * 境外收发货人
     */
    @Excel(name = "境外收发货人",width = 26)
    @DecHeadFields
    private String overseasConsigneeEname;
    /**
     * 消费使用/生产销售单位
     */
    @Excel(name = "消费使用/生产销售单位",width = 26)
    @DecHeadFields
    private String deliverUnitName;
    /**
     * 申报单位
     */
    @Excel(name = "申报单位",width = 26)
    @DecHeadFields
    private String declareUnitName;
    /**
     * 进出口日期
     */
    @Excel(name = "进出口日期",width = 18)
    @DecHeadFields
    private String outDate;

    /**
     * 创建人
     */
    @Excel(name = "创建人",width = 16)
    @DecHeadFields
    private String createPerson;
    /**
     * 合同号
     */
    @Excel(name = "合同号",width = 26)
    @DecHeadFields
    private String contract;
    /**
     * 运输方式
     */
    @Excel(name = "运输方式",width = 15)
    @DecHeadFields
    private String shipTypeCode;
    /**
     * 备案号
     */
    @Excel(name = "备案号",width = 15)
    @DecHeadFields
    private String recordNumber;
    /**
     * 运输工具名称
     */
    @Excel(name = "运输工具名称",width = 26)
    @DecHeadFields
    private String shipName;
    /**
     * 航次号
     */
    @Excel(name = "航次号",width = 26)
    @DecHeadFields
    private String voyage;
    /**
     * 提单号
     */
    @Excel(name = "提单号",width = 26)
    @DecHeadFields
    private String billCode;
    /**
     * 监管方式
     */
    @Excel(name = "监管方式",width = 18)
    @DecHeadFields
    private String tradeTypeCode;
    /**
     * 征免性质
     */
    @Excel(name = "征免性质",width = 18)
    @DecHeadFields
    private String taxTypeCode;
    /**
     * 贸易国
     */
    @Excel(name = "贸易国",width = 14)
    @DecHeadFields
    private String tradeCountry;
    /**
     * 成交方式
     */
    @Excel(name = "成交方式",width = 14)
    @DecHeadFields
    private String termsTypeCode;
    /**
     * 核注清单编号
     */
    @Excel(name = "核注清单编号",width = 14)
    @DecHeadFields
    private String bondInvtNo;
    /**
     * 件数
     */
    @Excel(name = "件数",width = 10)
    @DecHeadFields
    private String packs;
    /**
     * 包装种类
     */
    @Excel(name = "包装种类",width = 18)
    @DecHeadFields
    private String packsKinds;
    /**
     * 毛重
     */
    @Excel(name = "毛重",width = 16)
    @DecHeadFields
    private String grossWeight;
    /**
     * 净重
     */
    @Excel(name = "净重",width = 16)
    @DecHeadFields
    private String netWeight;
    /**
     * 入境/离境口岸
     */
    @Excel(name = "入境/离境口岸",width = 26)
    @DecHeadFields
    private String entyPortCode;
    /**
     * 商品序号
     */
    @Excel(name = "商品序号",width = 16)
    private String item;
    /**
     * 备案序号
     */
    @Excel(name = "备案序号",width = 16)
    private String recordItem;
    /**
     * 商品编码
     */
    @Excel(name = "商品编码",width = 16)
    private String hscode;
    /**
     * 商品名称
     */
    @Excel(name = "商品名称",width = 26)
    private String hsname;
    /**
     * 规格型号
     */
    @Excel(name = "规格型号",width = 36)
    private String goodsHsmodel;
    /**
     * 成交数量
     */
    @Excel(name = "成交数量",width = 16)
    private String goodsCount;
    /**
     * 成交单位
     */
    @Excel(name = "成交单位",width = 16)
    private String unitCode;
    /**
     * 成交单价
     */
    @Excel(name = "成交单价",width = 16)
    private String price;
    /**
     * 成交总价
     */
    @Excel(name = "成交总价",width = 16)
    private String goodsTotal;
    /**
     * 币制
     */
    @Excel(name = "币制",width = 12)
    private String currencyCode;
    /**
     * 法定第一数量
     */
    @Excel(name = "法定第一数量",width = 20)
    private String count1;
    /**
     * 法定第一单位
     */
    @Excel(name = "法定第一单位",width = 20)
    private String unit1;
    /**
     * 法定第二数量
     */
    @Excel(name = "法定第二数量",width = 20)
    private String count2;
    /**
     * 法定第二单位
     */
    @Excel(name = "法定第二单位",width = 20)
    private String unit2;
    /**
     * 原产国
     */
    @Excel(name = "原产国",width = 16)
    private String desCountry;
    /**
     * 征免方式
     */
    @Excel(name = "征免方式",width = 16)
    private String faxTypeCode;
    /**
     * 境内目的地/货源地
     */
    @Excel(name = "境内目的地/货源地",width = 26)
    private String districtCode;
    /**
     * 运费代码
     */
    @Excel(name = "运费代码",width = 16,replace = {"率_1","单价_2","总价_3"})
    @DecHeadFields
    private String shipFeeCode;
    /**
     * 运费
     */
    @Excel(name = "运费",width = 16)
    @DecHeadFields
    private String shipFee;
    /**
     * 运费币制
     */
    @Excel(name = "运费币制",width = 16)
    @DecHeadFields
    private String shipCurrencyCode;
    /**
     * 杂费代码
     */
    @Excel(name = "杂费代码",width = 16,replace = {"率_1","单价_2","总价_3"})
    @DecHeadFields
    private String extrasCode;
    /**
     * 杂费值
     */
    @Excel(name = "杂费",width = 16)
    @DecHeadFields
    private String extras;

    /**
     * 杂费币制
     */
    @Excel(name = "杂费币制",width = 16)
    @DecHeadFields
    private String otherCurr;
    /**
     * 保费代码
     */
    @Excel(name = "保费代码",width = 16,replace = {"率_1","单价_2","总价_3"})
    @DecHeadFields
    private String insuranceCode;
    /**
     * 保费
     */
    @Excel(name = "保费",width = 16)
    @DecHeadFields
    private String insurance;
    /**
     * 保费币制
     */
    @Excel(name = "保费币制",width = 16)
    @DecHeadFields
    private String insuranceCurr;
    /**
     * 标记唛码
     */
    @Excel(name = "标记唛码",width = 16)
    @DecHeadFields
    private String markNo;
    /**
     * 启运国/运抵国
     */
    @Excel(name = "启运国/运抵国",width = 16)
    @DecHeadFields
    private String arrivalArea;
    /**
     * 启运港
     */
    @Excel(name = "启运港",width = 26)
    @DecHeadFields
    private String despPortCode;
    /**
     * 经停港
     */
    @Excel(name = "经停港",width = 26)
    @DecHeadFields
    private String desPort;
    /**
     * 许可证号
     */
    @Excel(name = "许可证号",width = 26)
    @DecHeadFields
    private String licenceNumber;
    /**
     * 其他包装
     */
    @Excel(name = "其他包装",width = 20)
    @DecHeadFields
    private String packType;
    /**
     * 货物存放地点
     */
    @Excel(name = "货物存放地点",width = 26)
    @DecHeadFields
    private String goodsPlace;
    /**
     * 备注
     */
    @Excel(name = "备注",width = 26)
    @DecHeadFields
    private String markNumber;
    /**
     * 检验检疫受理机关
     */
    @Excel(name = "检验检疫受理机关",width = 26)
    @DecHeadFields
    private String orgCode;
    /**
     * 口岸检验检疫机关
     */
    @Excel(name = "口岸检验检疫机关",width = 26)
    @DecHeadFields
    private String inspOrgCode;
    /**
     * 目的地检验检疫机关
     */
    @Excel(name = "目的地检验检疫机关",width = 26)
    @DecHeadFields
    private String purpOrgCode;
    /**
     * 集装箱号
     */
    @Excel(name = "集装箱号",width = 20)
    @DecContainerFields
    private String containerId;
    /**
     * 检验检疫名称
     */
    @Excel(name = "检验检疫名称",width = 26)
    @DecHeadFields
    private String ciqName;
    /**
     * 货物属性
     */
    @Excel(name = "货物属性",width = 20)
    @DecHeadFields
    private String goodsAttr;
    /**
     * 危险货物信息
     */
    @Excel(name = "危险货物信息",width = 26)
    @DecHeadFields
    private String dangName;
    /**
     * 检验检疫货物规格
     */
    @Excel(name = "检验检疫货物规格",width = 26)
    @DecHeadFields
    private String goodsSpec;
    /**
     * 用途
     */
    @Excel(name = "用途",width = 16)
    @DecHeadFields
    private String purpose;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间",width = 16)
    @DecHeadFields
    private String createTime;



}
