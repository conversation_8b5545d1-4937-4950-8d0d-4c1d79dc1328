<template>
	<j-modal :disableSubmit="disableSubmit" :title="title" :visible="visible" :width="width" cancelText="关闭"
		selfCloseAction="closePop" @cancel="handleCancel">
		<template slot="footer">
			<a-button :disabled="confirmLoading" type="primary" @click="handleReset">
				<j-ai-check-icon :size="16" style="margin-right: 5px;margin-bottom: 3px" />
				重新校验
			</a-button>
			<a-button type="default" @click="handleCancel">关闭</a-button>
		</template>
		<!-- 加载状态 -->
		<div v-if="confirmLoading" class="loading-container">
			<div class="loading-animation">
				<div class="ai-tech-animation">
					<div class="brain-container">
						<div class="circle-ripple"></div>
						<div class="brain">
							<div class="circuit-container">
								<div class="circuit c1"></div>
								<div class="circuit c2"></div>
								<div class="circuit c3"></div>
								<div class="circuit c4"></div>
								<div class="circuit c5"></div>
								<div class="circuit c6"></div>
								<div class="pulse-dot d1"></div>
								<div class="pulse-dot d2"></div>
								<div class="pulse-dot d3"></div>
								<div class="pulse-dot d4"></div>
							</div>
							<div class="data-stream">
								<div class="data-particle p1"></div>
								<div class="data-particle p2"></div>
								<div class="data-particle p3"></div>
								<div class="data-particle p4"></div>
								<div class="data-particle p5"></div>
								<div class="data-particle p6"></div>
							</div>
						</div>
						<div class="scanning-beam"></div>
					</div>
				</div>
			</div>
			<div class="loading-text">
				<p class="ai-analyzing">AI智能分析中...</p>
				<p class="loading-subtext">深度学习模型正在审核文档数据</p>
				<div class="tech-dots">
					<span class="tech-dot"></span>
					<span class="tech-dot"></span>
					<span class="tech-dot"></span>
				</div>
			</div>
		</div>

		<!-- 内容区域 -->
		<div v-else class="check-result-container">
			<a-empty v-if="!checkResults || checkResults.length === 0" description="暂无审核结果" />

			<div v-else>
				<a-alert :type="hasErrors ? 'warning' : 'success'" banner class="mb-10">
					<template slot="message">
						<div class="result-status">
							<span>AI审单校验结果：</span>
							<span v-if="hasErrors" class="status-text error-status">异常</span>
							<span v-else class="status-text success-status">正常</span>
						</div>
					</template>
				</a-alert>

				<a-collapse :bordered="false" :defaultActiveKey="getDefaultActiveKeys()" class="result-collapse">
					<!-- 单个文件校验折叠面板 -->
					<a-collapse-panel key="file-checks" :class="{ 'has-error': hasFileErrors }">
						<template slot="header">
							<div class="collapse-header">
								<span class="file-name">
									<a-icon class="mr-5 file-icon" type="folder" />
									<span class="file-text">单个文件校验</span>
								</span>
								<a-tag v-if="hasFileErrors" color="red">异常</a-tag>
								<a-tag v-else color="green">正常</a-tag>
							</div>
						</template>

						<!-- 单个文件子列表 -->
						<a-collapse :bordered="false" :defaultActiveKey="[]" class="sub-collapse">
							<a-collapse-panel v-for="(item, index) in fileCheckResults" :key="index"
								:class="{ 'error-panel': item.error }">
								<template slot="header">
									<div class="collapse-header">
										<span class="file-name">
											<a-icon class="mr-5 file-icon" type="file" />
											<a-tooltip :title="item.file_name">
												<span class="file-text">{{ displayFileName(item.file_name) }}</span>
											</a-tooltip>
										</span>
										<a-tag v-if="item.error" color="red">异常</a-tag>
										<a-tag v-else color="green">正常</a-tag>
									</div>
								</template>

								<div v-if="item.error" class="error-content">
									<div v-for="(error, errorIndex) in formatErrors(item.error)" :key="errorIndex" class="error-item">
										<a-icon class="error-icon" theme="filled" type="warning" />
										<span>{{ error }}</span>
									</div>
								</div>
								<div v-else class="success-content">
									<a-icon class="success-icon" theme="filled" type="check-circle" />
									<span>校验通过</span>
								</div>
							</a-collapse-panel>
						</a-collapse>
					</a-collapse-panel>

					<!-- 校验结果汇总面板 -->
					<a-collapse-panel v-if="summaryResult" key="summary" :class="{ 'has-error': summaryResult.error }">
						<template slot="header">
							<div class="collapse-header">
								<span class="file-name">
									<a-icon class="mr-5 file-icon" type="audit" />
									<span class="file-text">单单比对校验</span>
								</span>
								<a-tag v-if="summaryResult.error" color="red">异常</a-tag>
								<a-tag v-else color="green">正常</a-tag>
							</div>
						</template>

						<div v-if="summaryResult.error" class="error-content">
							<div v-for="(error, errorIndex) in formatErrors(summaryResult.error)" :key="errorIndex"
								class="error-item">
								<a-icon class="error-icon" theme="filled" type="warning" />
								<span>{{ error }}</span>
							</div>
						</div>
						<div v-else class="success-content">
							<a-icon class="success-icon" theme="filled" type="check-circle" />
							<span>校验通过</span>
						</div>
					</a-collapse-panel>
				</a-collapse>
			</div>
		</div>
	</j-modal>
</template>

<script>
import { getAction } from "@/api/manage";
import JAiCheckIcon from "@/components/jeecg/JAiCheckIcon.vue";

export default {
	name: "AiCheckModal",
	components: {JAiCheckIcon},
	data() {
		return {
			title: "AI审单",
			width: 900,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			decId: '',
			checkResults: [],
			url: {
				verify: '/common/verify',
			}
		}
	},
	computed: {
		hasErrors() {
			return this.checkResults.some(item => item.error);
		},
		summaryResult() {
			return this.checkResults.find(item => item.file_name === '校验结果');
		},
		fileCheckResults() {
			// 过滤并排序文件，异常的排在上面，正常的排在下面
			return this.checkResults
				.filter(item => item.file_name !== '校验结果')
				.sort((a, b) => {
					// 如果a有错误而b没有，a排在前面
					if (a.error && !b.error) return -1;
					// 如果b有错误而a没有，b排在前面
					if (!a.error && b.error) return 1;
					// 两者都有错误或都没有错误，保持原顺序
					return 0;
				});
		},
		hasFileErrors() {
			return this.fileCheckResults.some(item => item.error);
		}
	},
	methods: {
		/**
		 * 重新校验
		 */
		handleReset() {
			this.confirmLoading = true;
			getAction(this.url.verify, {
				decId: this.decId,
				isQz: "1"
			})
				.then((res) => {
					if (res.success) {
						this.checkResults = res.result.records || res.result;
						console.log(this.checkResults);
					} else {
						this.$message.warning(res.message || res);
					}
				})
				.finally(() => {
					this.confirmLoading = false;
				});
		},
		async show(decId) {
			this.visible = true;
			this.confirmLoading = true;
			this.decId = decId;

			await getAction(this.url.verify, {
				decId: this.decId,
				isQz: null
			})
				.then((res) => {
					if (res.success) {
						this.checkResults = res.result.records || res.result;
						console.log(this.checkResults);
					} else {
						this.$message.warning(res.message || res);
					}
				})
				.finally(() => {
					this.confirmLoading = false;
				});
		},
		close() {
			this.visible = false;
			this.checkResults = [];
		},
		handleCancel() {
			this.close();
		},
		displayFileName(fileName) {
			if (fileName && fileName.length > 40) {
				return fileName.substring(0, 37) + '...';
			}
			return fileName;
		},
		formatErrors(errorText) {
			if (!errorText) return [];

			// 处理分号分割的错误信息
			if (errorText.includes(';')) {
				return errorText.split(';')
					.map(e => e.trim())
					.filter(e => e.length > 0);
			}

			// 处理中文分号分割的错误信息
			if (errorText.includes('；')) {
				return errorText.split('；')
					.map(e => e.trim())
					.filter(e => e.length > 0);
			}

			// 处理序号格式的错误信息
			const errors = [];
			const regex = /\d+\.\s/g;
			const matches = [...errorText.matchAll(regex)];

			if (matches.length > 1) {
				for (let i = 0; i < matches.length; i++) {
					const start = matches[i].index;
					const end = i < matches.length - 1 ? matches[i + 1].index : errorText.length;
					const errorItem = errorText.substring(start, end).trim();
					if (errorItem.length > 0) {
						errors.push(errorItem);
					}
				}
			} else {
				// 如果没有找到序号格式，尝试按句号分割
				if (errorText.includes('。')) {
					return errorText.split('。')
						.map(e => e.trim())
						.filter(e => e.length > 0)
						.map(e => e.endsWith('。') ? e : e + '。');
				}
				errors.push(errorText);
			}

			return errors;
		},
		getDefaultActiveKeys() {
			const keys = ['file-checks'];
			if (this.summaryResult) {
				keys.push('summary');
			}
			return keys;
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';

.check-result-container {
	padding: 10px;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300px;
	padding: 20px;
	background: linear-gradient(to bottom, rgba(240, 242, 245, 0.8), rgba(240, 242, 245, 0.4));
	border-radius: 8px;
}

.loading-animation {
	margin-bottom: 20px;
}

.loading-text {
	text-align: center;

	.ai-analyzing {
		margin: 0;
		font-size: 18px;
		color: #1890ff;
		font-weight: 600;
		letter-spacing: 0.5px;
	}

	.loading-subtext {
		margin-top: 8px;
		font-size: 14px;
		color: #666;
	}
}

/* 高科技动画效果 */
.ai-tech-animation {
	position: relative;
	width: 160px;
	height: 160px;
	margin: 0 auto;
}

.brain-container {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.circle-ripple {
	position: absolute;
	width: 120px;
	height: 120px;
	border-radius: 50%;
	border: 2px solid rgba(24, 144, 255, 0.2);
	animation: ripple 2s infinite ease-out;
}

.brain {
	position: relative;
	width: 100px;
	height: 100px;
	background: rgba(24, 144, 255, 0.1);
	border-radius: 50%;
	overflow: hidden;
	box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
	z-index: 2;
}

.circuit-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.circuit {
	position: absolute;
	background: rgba(24, 144, 255, 0.3);

	&.c1 {
		top: 20%;
		left: 10%;
		width: 30%;
		height: 2px;
		animation: pulse 1.5s infinite alternate;
	}

	&.c2 {
		top: 40%;
		right: 10%;
		width: 40%;
		height: 2px;
		animation: pulse 2s infinite alternate-reverse;
	}

	&.c3 {
		top: 60%;
		left: 20%;
		width: 50%;
		height: 2px;
		animation: pulse 1.8s infinite alternate;
	}

	&.c4 {
		top: 30%;
		left: 50%;
		width: 2px;
		height: 30%;
		animation: pulse 2.2s infinite alternate-reverse;
	}

	&.c5 {
		top: 50%;
		left: 30%;
		width: 2px;
		height: 30%;
		animation: pulse 1.7s infinite alternate;
	}

	&.c6 {
		top: 20%;
		right: 30%;
		width: 2px;
		height: 40%;
		animation: pulse 2.3s infinite alternate-reverse;
	}
}

.pulse-dot {
	position: absolute;
	width: 6px;
	height: 6px;
	background: #1890ff;
	border-radius: 50%;

	&.d1 {
		top: 20%;
		left: 30%;
		animation: dot-pulse 1.5s infinite;
	}

	&.d2 {
		top: 40%;
		right: 20%;
		animation: dot-pulse 1.8s infinite 0.2s;
	}

	&.d3 {
		bottom: 30%;
		left: 40%;
		animation: dot-pulse 1.6s infinite 0.4s;
	}

	&.d4 {
		top: 60%;
		right: 35%;
		animation: dot-pulse 1.7s infinite 0.6s;
	}
}

.data-stream {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.data-particle {
	position: absolute;
	width: 4px;
	height: 4px;
	background: #1890ff;
	opacity: 0.8;
	border-radius: 50%;

	&.p1 {
		top: 25%;
		left: 10%;
		animation: data-move-1 3s infinite linear;
	}

	&.p2 {
		top: 45%;
		left: 15%;
		animation: data-move-2 3.5s infinite linear;
	}

	&.p3 {
		top: 65%;
		left: 20%;
		animation: data-move-3 2.8s infinite linear;
	}

	&.p4 {
		top: 35%;
		right: 15%;
		animation: data-move-4 3.2s infinite linear;
	}

	&.p5 {
		top: 55%;
		right: 20%;
		animation: data-move-5 2.9s infinite linear;
	}

	&.p6 {
		top: 75%;
		right: 25%;
		animation: data-move-6 3.1s infinite linear;
	}
}

.scanning-beam {
	position: absolute;
	width: 100%;
	height: 4px;
	background: linear-gradient(to right, rgba(24, 144, 255, 0), rgba(24, 144, 255, 0.7), rgba(24, 144, 255, 0));
	animation: scan 2s infinite ease-in-out;
	z-index: 3;
}

.tech-dots {
	margin-top: 12px;

	.tech-dot {
		display: inline-block;
		width: 8px;
		height: 8px;
		margin: 0 3px;
		background-color: #1890ff;
		border-radius: 50%;
		opacity: 0.6;

		&:nth-child(1) {
			animation: dot-fade 1.5s infinite;
		}

		&:nth-child(2) {
			animation: dot-fade 1.5s infinite 0.5s;
		}

		&:nth-child(3) {
			animation: dot-fade 1.5s infinite 1s;
		}
	}
}

@keyframes ripple {
	0% {
		transform: scale(0.8);
		opacity: 1;
	}

	100% {
		transform: scale(1.5);
		opacity: 0;
	}
}

@keyframes pulse {
	0% {
		opacity: 0.2;
	}

	100% {
		opacity: 0.8;
	}
}

@keyframes dot-pulse {
	0% {
		transform: scale(1);
		opacity: 0.5;
	}

	50% {
		transform: scale(1.5);
		opacity: 1;
	}

	100% {
		transform: scale(1);
		opacity: 0.5;
	}
}

@keyframes data-move-1 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(35px, 25px);
	}

	100% {
		transform: translate(80px, 0);
	}
}

@keyframes data-move-2 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(40px, -15px);
	}

	100% {
		transform: translate(70px, 10px);
	}
}

@keyframes data-move-3 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(30px, -25px);
	}

	100% {
		transform: translate(60px, -10px);
	}
}

@keyframes data-move-4 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(-35px, 15px);
	}

	100% {
		transform: translate(-70px, 0);
	}
}

@keyframes data-move-5 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(-40px, -15px);
	}

	100% {
		transform: translate(-65px, 5px);
	}
}

@keyframes data-move-6 {
	0% {
		transform: translate(0, 0);
	}

	50% {
		transform: translate(-30px, -25px);
	}

	100% {
		transform: translate(-60px, -5px);
	}
}

@keyframes scan {
	0% {
		top: 0;
		opacity: 0.7;
	}

	50% {
		top: 96px;
		opacity: 1;
	}

	100% {
		top: 0;
		opacity: 0.7;
	}
}

@keyframes dot-fade {

	0%,
	100% {
		opacity: 0.3;
	}

	50% {
		opacity: 1;
	}
}

.mb-10 {
	margin-bottom: 10px;
}

.mr-5 {
	margin-right: 5px;
}

.result-status {
	display: flex;
	align-items: center;

	.status-text {
		font-size: 16px;
		font-weight: 600;
		margin-left: 5px;
	}

	.error-status {
		color: #f5222d;
	}

	.success-status {
		color: #52c41a;
	}
}

.result-collapse {
	background-color: #fff;

	/deep/ .ant-collapse-item {
		margin-bottom: 10px;
		border-radius: 4px;
		overflow: hidden;
		border: 1px solid #f0f0f0;

		&.has-error {
			.ant-collapse-header {
				background-color: #fff1f0;
			}
		}

		&.error-panel {
			.ant-collapse-header {
				background-color: #fff1f0;
			}
		}

		.ant-collapse-header {
			padding: 12px 16px 12px 40px;
			position: relative;

			.ant-collapse-arrow {
				left: 16px;
			}

			&:hover {
				background-color: #fafafa;
			}
		}

		.ant-collapse-content-box {
			padding: 16px;
		}
	}
}

.sub-collapse {
	/deep/ .ant-collapse-item {
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}

		.ant-collapse-content-box {
			padding: 12px;
		}

		&.error-panel {
			.ant-collapse-header {
				background-color: #fff1f0;
			}
		}

		/* 确保正常面板显示白色背景 */
		.ant-collapse-header {
			background-color: #ffffff;
		}
	}
}

/* 覆盖父面板对子面板的背景色影响 */
/deep/ .ant-collapse-item.has-error {
	.sub-collapse {
		.ant-collapse-item:not(.error-panel) {
			.ant-collapse-header {
				background-color: #ffffff;
			}
		}
	}
}

.collapse-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding-left: 24px;
}

.file-name {
	font-weight: 500;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 700px;
	position: relative;
	display: flex;
	align-items: center;
}

.file-text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.file-icon {
	position: relative;
	z-index: 2;
}

.error-content {
	padding: 5px 0;

	.error-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 8px;
		word-wrap: break-word;
		word-break: break-all;
		white-space: pre-wrap;

		&:last-child {
			margin-bottom: 0;
		}

		.error-icon {
			color: #f5222d;
			margin-right: 8px;
			margin-top: 3px;
			flex-shrink: 0;
		}

		span {
			flex: 1;
			word-wrap: break-word;
			word-break: break-all;
			white-space: pre-wrap;
			line-height: 1.5;
		}
	}
}

.success-content {
	padding: 5px 0;
	display: flex;
	align-items: center;

	.success-icon {
		color: #52c41a;
		margin-right: 8px;
		font-size: 16px;
	}
}
</style>