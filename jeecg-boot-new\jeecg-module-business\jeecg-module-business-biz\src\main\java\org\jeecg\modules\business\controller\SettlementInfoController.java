package org.jeecg.modules.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.FeeItem;
import org.jeecg.modules.business.entity.SettlementInfo;
import org.jeecg.modules.business.service.ISettlementInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.sql.Array;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * SettlementInfoController
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/4/23 13:27
 * @version 1.0
 */
@RestController
@RequestMapping("/settlement/settlementInfo")
public class SettlementInfoController {

    @Autowired
    private ISettlementInfoService settlementInfoService;
    /**
     * 分页列表查询
     *
     * @param feeItem
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "结算单位-分页列表查询")
    @ApiOperation(value = "结算单位-分页列表查询", notes = "结算单位-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SettlementInfo settlementInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<SettlementInfo> queryWrapper = QueryGenerator.initQueryWrapper(settlementInfo, req.getParameterMap());
        Page<SettlementInfo> page = new Page<SettlementInfo>(pageNo, pageSize);
        IPage<SettlementInfo> pageList = settlementInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    /**
     * 添加
     *
     * @param feeItem
     * @return
     */
    @AutoLog(value = "结算单位-添加")
    @ApiOperation(value = "结算单位-添加", notes = "结算单位-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SettlementInfo settlementInfo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        settlementInfo.setTenantId(TenantContext.getTenant());
        settlementInfo.setCreator(isNotEmpty(loginUser) ? loginUser.getRealname() : "");
        settlementInfoService.save(settlementInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param feeItem
     * @return
     */
    @AutoLog(value = "结算单位-编辑")
    @ApiOperation(value = "结算单位-编辑", notes = "结算单位-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody SettlementInfo settlementInfo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        settlementInfo.setUpdater(isNotEmpty(loginUser) ? loginUser.getRealname() : "");
        settlementInfoService.updateById(settlementInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过ids删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "结算单位-通过id删除")
    @ApiOperation(value = "结算单位-通过id删除", notes = "结算单位-通过id删除")
    @RequestMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        List<String> idList = Arrays.asList(id.split(","));
        settlementInfoService.removeBatchByIds(idList);
        return Result.OK("删除成功!");
    }
    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "结算单位-通过id查询")
    @ApiOperation(value = "结算单位-通过id查询", notes = "结算单位-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        SettlementInfo settlementInfo = settlementInfoService.getById(id);
        if (settlementInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(settlementInfo);
    }
}
