package org.jeecg.modules.business.entity.messages.emlMessages;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * NptsEmlImg
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/6/24 10:25
 * @version 1.0
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class NptsEmlImg {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "GdsSeqno")
    private Integer gdsSeqno;

    @XmlElement(name = "MtpckEndprdTypecd")
    private String mtpckEndprdTypecd;

    @XmlElement(name = "GdsMtno")
    private String gdsMtno;

    @XmlElement(name = "Gdecd")
    private String gdecd;

    @XmlElement(name = "GdsNm")
    private String gdsNm;

    @XmlElement(name = "EndprdGdsSpcfModelDesc")
    private String endprdGdsSpcfModelDesc;

    @XmlElement(name = "DclUnitcd")
    private String dclUnitcd;

    @XmlElement(name = "LawfUnitcd")
    private String lawfUnitcd;

    @XmlElement(name = "SecdLawfUnitcd")
    private String secdLawfUnitcd;

    @XmlElement(name = "DclQty")
    private BigDecimal dclQty;

    @XmlElement(name = "DclUprcAmt")
    private BigDecimal dclUprcAmt;

    @XmlElement(name = "DclTotalAmt")
    private BigDecimal dclTotalAmt;

    @XmlElement(name = "DclCurrcd")
    private String dclCurrcd;

    @XmlElement(name = "Natcd")
    private String natcd;

    @XmlElement(name = "GdsSceneUrdf")
    private String gdsSceneUrdf;

    @XmlElement(name = "LvyrlfModecd")
    private String lvyrlfModecd;

    @XmlElement(name = "AdjmtrMarkcd")
    private String adjmtrMarkcd;

    @XmlElement(name = "GdsAtrcd")
    private String gdsAtrcd;

    @XmlElement(name = "ModfMarkcd")
    private String modfMarkcd;

    @XmlElement(name = "Rmk")
    private String rmk;

    @XmlElement(name = "SourceMarkcd")
    private String sourceMarkcd;

    // Getters and Setters
    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public Integer getGdsSeqno() {
        return gdsSeqno;
    }

    public void setGdsSeqno(Integer gdsSeqno) {
        this.gdsSeqno = gdsSeqno;
    }

    public String getMtpckEndprdTypecd() {
        return mtpckEndprdTypecd;
    }

    public void setMtpckEndprdTypecd(String mtpckEndprdTypecd) {
        this.mtpckEndprdTypecd = mtpckEndprdTypecd;
    }

    public String getGdsMtno() {
        return gdsMtno;
    }

    public void setGdsMtno(String gdsMtno) {
        this.gdsMtno = gdsMtno;
    }

    public String getGdecd() {
        return gdecd;
    }

    public void setGdecd(String gdecd) {
        this.gdecd = gdecd;
    }

    public String getGdsNm() {
        return gdsNm;
    }

    public void setGdsNm(String gdsNm) {
        this.gdsNm = gdsNm;
    }

    public String getEndprdGdsSpcfModelDesc() {
        return endprdGdsSpcfModelDesc;
    }

    public void setEndprdGdsSpcfModelDesc(String endprdGdsSpcfModelDesc) {
        this.endprdGdsSpcfModelDesc = endprdGdsSpcfModelDesc;
    }

    public String getDclUnitcd() {
        return dclUnitcd;
    }

    public void setDclUnitcd(String dclUnitcd) {
        this.dclUnitcd = dclUnitcd;
    }

    public String getLawfUnitcd() {
        return lawfUnitcd;
    }

    public void setLawfUnitcd(String lawfUnitcd) {
        this.lawfUnitcd = lawfUnitcd;
    }

    public String getSecdLawfUnitcd() {
        return secdLawfUnitcd;
    }

    public void setSecdLawfUnitcd(String secdLawfUnitcd) {
        this.secdLawfUnitcd = secdLawfUnitcd;
    }

    public BigDecimal getDclQty() {
        return dclQty;
    }

    public void setDclQty(BigDecimal dclQty) {
        this.dclQty = dclQty;
    }

    public BigDecimal getDclUprcAmt() {
        return dclUprcAmt;
    }

    public void setDclUprcAmt(BigDecimal dclUprcAmt) {
        this.dclUprcAmt = dclUprcAmt;
    }

    public BigDecimal getDclTotalAmt() {
        return dclTotalAmt;
    }

    public void setDclTotalAmt(BigDecimal dclTotalAmt) {
        this.dclTotalAmt = dclTotalAmt;
    }

    public String getDclCurrcd() {
        return dclCurrcd;
    }

    public void setDclCurrcd(String dclCurrcd) {
        this.dclCurrcd = dclCurrcd;
    }

    public String getNatcd() {
        return natcd;
    }

    public void setNatcd(String natcd) {
        this.natcd = natcd;
    }

    public String getGdsSceneUrdf() {
        return gdsSceneUrdf;
    }

    public void setGdsSceneUrdf(String gdsSceneUrdf) {
        this.gdsSceneUrdf = gdsSceneUrdf;
    }

    public String getLvyrlfModecd() {
        return lvyrlfModecd;
    }

    public void setLvyrlfModecd(String lvyrlfModecd) {
        this.lvyrlfModecd = lvyrlfModecd;
    }

    public String getAdjmtrMarkcd() {
        return adjmtrMarkcd;
    }

    public void setAdjmtrMarkcd(String adjmtrMarkcd) {
        this.adjmtrMarkcd = adjmtrMarkcd;
    }

    public String getGdsAtrcd() {
        return gdsAtrcd;
    }

    public void setGdsAtrcd(String gdsAtrcd) {
        this.gdsAtrcd = gdsAtrcd;
    }

    public String getModfMarkcd() {
        return modfMarkcd;
    }

    public void setModfMarkcd(String modfMarkcd) {
        this.modfMarkcd = modfMarkcd;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getSourceMarkcd() {
        return sourceMarkcd;
    }

    public void setSourceMarkcd(String sourceMarkcd) {
        this.sourceMarkcd = sourceMarkcd;
    }
}
