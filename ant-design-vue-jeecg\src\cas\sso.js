import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import store from '@/store'
/**
 * 单点登录
 */
const init = (callback) => {
	// 如果当前是错误页面，直接执行回调函数，不进行SSO检查
	if (window.location.pathname === '/login-error') {
		callback && callback()
		return
	}

	if (process.env.VUE_APP_SSO === 'true') {
		console.log('-------单点登录开始-------')
		let service = 'http://' + window.location.host
		// let serviceUrl = encodeURIComponent(service)
		let serviceUrl = service
		console.log('serviceUrl:' + serviceUrl)
		let back = window.location.href
		let backUrl = encodeURIComponent(back)
		let token = Vue.ls.get(ACCESS_TOKEN)
		if (token) {
			loginSuccess(callback)
		} else {
			let st = getUrlParam('ticket')
			// if (st) {
			  console.log('st: ' + st)
				validateSt(st, serviceUrl + '/sys/sso/login', backUrl, callback)
			// } else {
			// 	window.location.href = window._CONFIG['casPrefixUrl'] + '?redirect=' + serviceUrl + '/sys/sso/login?back=' + backUrl
			// }
		}
		console.log('-------单点登录结束-------')
	} else {
		callback && callback()
	}
}

/**
 * 获取url参数
 * @param name
 * @returns {string}
 */
function getUrlParam(name) {
	return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || ['', ''])[1].replace(/\+/g, '%20')) || null
}

/**
 * 校验ST
 * @param ticket
 * @param serviceUrl
 * @param callback
 */
function validateSt(ticket, serviceUrl, backUrl, callback) {
	let params = {
		ticket: ticket,
		currUrl: serviceUrl,
		back: backUrl
	}
	store.dispatch('ValidateLogin', params).then(res => {
		console.log(res)
		debugger
		if (res.success) {
			// 从URL参数中获取back地址，如果存在则跳转到back地址
			let urlParams = new URLSearchParams(window.location.search)
			let backParam = urlParams.get('back')
			if (backParam) {
				// 解码并跳转到back参数指定的地址
				backParam = backParam.replace('/login-error', '')
				let decodedBack = decodeURIComponent(backParam)
				console.log('跳转到back地址:', decodedBack)
				window.location.href = decodedBack
			} else {
				// 如果没有back参数，则正常执行callback
				loginSuccess(callback)
			}
		} else {
			debugger
			console.log('-------单点登录失败-------')
			console.log(res)
			let errorMsg = '系统异常，请联系管理员！'
			let loginId = ''
			// 判断res是字符串，并且包含|
			if (typeof res === 'string' && res.includes('|')) {
				// 登录失败，重定向到错误页面并传递错误信息
				errorMsg = res.split('|')[0]
				loginId = res.split('|')[1]
			}
			// 登录失败，重定向到错误页面并传递错误信息
			errorMsg = encodeURIComponent(errorMsg || '系统异常，请联系管理员！')
			window.location.href = '/login-error?message=' + errorMsg + '&loginId=' + loginId + '&redirect=' + serviceUrl + '&back=' + backUrl
		}
	}).catch((err) => {
		console.log(err)
		// 捕获到异常也需要重定向到错误页面
		let errorMsg = encodeURIComponent(err || '系统异常，请联系管理员！')
		window.location.href = '/login-error?message=' + errorMsg + '&redirect=' + serviceUrl + '&back=' + backUrl
	})
}

function loginSuccess (callback) {
	callback()
}

// 恢复为默认导出，保持与原代码兼容
const SSO = {
	init
}

export default SSO