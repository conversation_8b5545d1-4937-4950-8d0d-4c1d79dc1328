package org.jeecg.modules.business.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.business.service.IReceiptHandleService;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.jeecg.common.constant.CommonConstant.DEFAULT;

/**
 * 回执配置
 * 
 * <AUTHOR>
 *
 * @date 2025年6月28日 下午2:32:21
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "receipt")
@RefreshScope
public class ReceiptConfig {

	/**
	 * 回执路由配置集合
	 */
	private List<Map<String, String>> receiptRouteConfigList;

    /**
     * 企业配置集合
     */
    private Map<String, EnterpriseConfig> enterprises;

	/**
	 * 回执处理Service实例集合[key:报文根节点名称;value:回执处理Service实例]
	 */
	private Map<String, IReceiptHandleService> receiptHandleServiceMap;

    /**
     * 回执处理Service实例名称映射
     */
    private Map<String, String> handleServices;

	/**
	 * 根据报文根节点名称获取回执处理Service实例
	 * 
	 * @param key 报文根节点名称
	 * @return 回执处理Service实例
	 */
	public IReceiptHandleService getReceiptHandleService(String key) {
		return receiptHandleServiceMap.get(key);
	}

    /**
     * 根据企业名称获取企业配置
     *
     * @param enterpriseName 企业名称
     * @return 企业配置
     */
    public EnterpriseConfig getEnterpriseConfig(String enterpriseName) {
        return enterprises != null ? enterprises.get(enterpriseName) : null;
    }

    /**
     * 获取默认企业配置
     *
     * @return 默认企业配置
     */
    public EnterpriseConfig getDefaultEnterpriseConfig() {
        return getEnterpriseConfig(DEFAULT);
    }

    /**
     * 初始化Service实例映射和路由配置合并
     */
    @PostConstruct
    public void init() {
        // 初始化Service实例映射
        initReceiptHandleServiceMap();

        // 合并所有企业的路由配置
        mergeEnterpriseRouteConfigs();
    }

    /**
     * 初始化回执处理Service实例映射
     */
    private void initReceiptHandleServiceMap() {
        receiptHandleServiceMap = new HashMap<>();
        if (handleServices != null) {
            handleServices.forEach((key, beanName) -> {
                try {
                    IReceiptHandleService service = SpringContextUtils.getBean(beanName, IReceiptHandleService.class);
                    receiptHandleServiceMap.put(key, service);
                } catch (Exception e) {
                    log.error("初始化回执处理Service实例失败，beanName: {}, error: {}", beanName, e.getMessage());
                }
            });
            log.info("初始化回执处理Service实例映射：{}", receiptHandleServiceMap.keySet());
        }
    }

    /**
     * 合并所有企业的路由配置到全局列表中
     */
    private void mergeEnterpriseRouteConfigs() {
        receiptRouteConfigList = new ArrayList<>();

        if (enterprises != null) {
            enterprises.forEach((enterpriseName, enterpriseConfig) -> {
                if (enterpriseConfig.getRouteConfigList() != null) {
                    enterpriseConfig.getRouteConfigList().forEach(routeConfig -> {
                        Map<String, String> routeMap = new HashMap<>();

                        // 添加企业标识
                        routeMap.put("enterpriseName", enterpriseName);
                        // 为了确保路由ID唯一性，可以加上企业前缀
                        String uniqueRouteId = enterpriseName + "_" + routeConfig.getRouteId();
                        routeMap.put("routeId", uniqueRouteId);
                        routeMap.put("sourceType", routeConfig.getSourceType());

                        // 处理地址模板，替换占位符
                        String sourceAddress = routeConfig.getSourceAddressTemplate()
                                .replace("{server}", enterpriseConfig.getServer())
                                .replace("{param}", enterpriseConfig.getParam());

                        String targetAddress = routeConfig.getTargetAddressTemplate()
                                .replace("{backups}", enterpriseConfig.getBackups());

                        routeMap.put("sourceAddress", sourceAddress);
                        routeMap.put("targetAddress", targetAddress);

                        receiptRouteConfigList.add(routeMap);
                    });
                }
            });
        }

        log.info("合并企业路由配置完成，总计路由数量: {}", receiptRouteConfigList.size());
        receiptRouteConfigList.forEach(route ->
                log.debug("路由配置: {}", route)
        );
    }

    @Data
    public static class EnterpriseConfig {
        private String server;
        private String param;
        private String backups;
        private List<RouteConfig> routeConfigList;

        /**
         * 根据routeId获取路由配置
         *
         * @param routeId 路由ID
         * @return 路由配置
         */
        public RouteConfig getRouteConfig(String routeId) {
            if (routeConfigList != null) {
                return routeConfigList.stream()
                        .filter(config -> routeId.equals(config.getRouteId()))
                        .findFirst()
                        .orElse(null);
            }
            return null;
        }
    }

    @Data
    public static class RouteConfig {
        private String routeId;
        private String sourceType;
        private String sourceAddressTemplate;
        private String targetAddressTemplate;

        /**
         * 获取完整的源地址
         *
         * @param enterpriseConfig 企业配置
         * @return 完整的源地址
         */
        public String getFullSourceAddress(EnterpriseConfig enterpriseConfig) {
            return sourceAddressTemplate
                    .replace("{server}", enterpriseConfig.getServer())
                    .replace("{param}", enterpriseConfig.getParam());
        }

        /**
         * 获取完整的目标地址
         *
         * @param enterpriseConfig 企业配置
         * @return 完整的目标地址
         */
        public String getFullTargetAddress(EnterpriseConfig enterpriseConfig) {
            return targetAddressTemplate
                    .replace("{backups}", enterpriseConfig.getBackups());
        }
    }

}
