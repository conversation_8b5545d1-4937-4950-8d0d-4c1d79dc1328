<template>
	<div class="login-error-container">
		<a-card :class="{ 'animate-card': showCard }" class="error-card">
			<a-result :sub-title="errorMessage || '认证服务器返回错误'" status="error" title="登录失败">
				<template #extra>
					<div class="button-container">
						<a-button class="action-button bounce-hover" type="primary" @click="tryAgain">
							<a-icon type="reload" />
							重新登录
						</a-button>
<!--						<a-button @click="handleLogout" class="action-button bounce-hover">-->
<!--							<a-icon type="logout" />-->
<!--							注销-->
<!--						</a-button>-->
					</div>
				</template>
			</a-result>
			<div v-if="ticket" class="error-ticket">
				<div class="ticket-label">无效ticket:</div>
				<div class="ticket-value">{{ ticket }}</div>
			</div>
			<div class="error-tips">
				<a-alert show-icon type="info">
					<template #message>
						<span>若多次尝试无法登录，请联系系统管理员</span>
					</template>
				</a-alert>
			</div>
		</a-card>
	</div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
	name: 'LoginError',
	data() {
		return {
			errorMessage: '',
			loginId: '',
			redirect: '',
			back: '',
			showCard: false,
			ticket: ''
		}
	},
	created() {
		// 从URL参数中获取错误信息
		const urlParams = new URLSearchParams(window.location.search)
		this.errorMessage = decodeURIComponent(urlParams.get('message') || '')
		this.loginId = decodeURIComponent(urlParams.get('loginId') || '')
		this.redirect = decodeURIComponent(urlParams.get('redirect') || '')
		this.back = decodeURIComponent(urlParams.get('back') || '')
		this.ticket = decodeURIComponent(urlParams.get('ticket') || '')
    if  (this.ticket) {
			this.tryAgain()
		}
	},
	mounted() {
		// 页面加载时展示动画
		setTimeout(() => {
			this.showCard = true
		}, 100)
	},
	methods: {
		...mapActions(['Logout']),
		async tryAgain() {
			let params = {
				loginId: this.loginId
			}
			// 调用store中的退出登录接口
			await this.Logout(params).then(() => {
				console.log('注销成功')
			}).catch((err) => {
				console.log('注销失败：' + err)
			})
			// 重定向到CAS登录页
			// window.location.href = window._CONFIG['casPrefixUrl'] + '?redirect=' + this.redirect + '&back=' + this.back
		},
		handleLogout() {
			// 调用store中的退出登录接口
			this.Logout().then(() => {
				console.log('注销成功')
			})
		}
	}
}
</script>

<style scoped>
.login-error-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	overflow: hidden;
}

.error-card {
	width: 450px;
	border-radius: 10px;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
	background-color: #fff;
	opacity: 0;
	transform: translateY(50px);
	transition: all 0.5s ease-out;
	overflow: hidden;
	padding: 10px;
}

.animate-card {
	opacity: 1;
	transform: translateY(0);
}

.button-container {
	display: flex;
	justify-content: center;
	gap: 20px;
	margin-top: 10px;
}

.action-button {
	min-width: 120px;
	height: 40px;
	border-radius: 20px;
	font-weight: 500;
	transition: all 0.3s;
}

.bounce-hover:hover {
	transform: translateY(-3px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error-tips {
	margin-top: 20px;
	padding: 0 20px 10px;
}

/* 票据信息样式 */
.error-ticket {
	margin: 0 20px;
	padding: 10px;
	background-color: #f9f9f9;
	border-radius: 4px;
	text-align: center;
}

.ticket-label {
	font-weight: bold;
	margin-bottom: 5px;
	color: #666;
}

.ticket-value {
	word-break: break-all;
	font-family: monospace;
	font-size: 12px;
	color: #999;
	max-height: 60px;
	overflow-y: auto;
	padding: 5px;
	background: #f0f0f0;
	border-radius: 3px;
}
</style>