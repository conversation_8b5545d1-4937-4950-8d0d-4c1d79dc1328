package org.jeecg.modules.business.entity.messages.emlMessages;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * EmlMessage
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/6/24 9:49
 * @version 1.0
 */
@XmlRootElement(name = "EmlMessage", namespace = "http://www.chinaport.gov.cn/npts")
@XmlAccessorType(XmlAccessType.FIELD)
public class EmlMessage {
    @XmlElement(name = "NptsEmlHead", namespace = "http://www.chinaport.gov.cn/npts")
    private NptsEmlHead nptsEmlHead;

    @XmlElement(name = "NptsEmlImg", namespace = "http://www.chinaport.gov.cn/npts")
    private List<NptsEmlImg> nptsEmlImg;

    @XmlElement(name = "NptsEmlExg", namespace = "http://www.chinaport.gov.cn/npts")
    private List<NptsEmlExg> nptsEmlExg;

    @XmlElement(name = "NptsEmlConsume", namespace = "http://www.chinaport.gov.cn/npts")
    private List<NptsEmlConsume> nptsEmlConsume;

    @XmlElement(name = "status")
    private String status;

    @XmlElement(name = "OperCusRegCode")
    private String operCusRegCode;

    @XmlElement(name = "ImportInfo")
    private ImportInfo importInfo;

    // Getters and Setters
    public NptsEmlHead getNptsEmlHead() {
        return nptsEmlHead;
    }

    public void setNptsEmlHead(NptsEmlHead nptsEmlHead) {
        this.nptsEmlHead = nptsEmlHead;
    }

    public List<NptsEmlImg> getNptsEmlImg() {
        return nptsEmlImg;
    }

    public void setNptsEmlImg(List<NptsEmlImg> nptsEmlImg) {
        this.nptsEmlImg = nptsEmlImg;
    }

    public List<NptsEmlExg> getNptsEmlExg() {
        return nptsEmlExg;
    }

    public void setNptsEmlExg(List<NptsEmlExg> nptsEmlExg) {
        this.nptsEmlExg = nptsEmlExg;
    }

    public List<NptsEmlConsume> getNptsEmlConsume() {
        return nptsEmlConsume;
    }

    public void setNptsEmlConsume(List<NptsEmlConsume> nptsEmlConsume) {
        this.nptsEmlConsume = nptsEmlConsume;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperCusRegCode() {
        return operCusRegCode;
    }

    public void setOperCusRegCode(String operCusRegCode) {
        this.operCusRegCode = operCusRegCode;
    }

    public ImportInfo getImportInfo() {
        return importInfo;
    }

    public void setImportInfo(ImportInfo importInfo) {
        this.importInfo = importInfo;
    }
}
