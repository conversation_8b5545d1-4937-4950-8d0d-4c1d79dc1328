package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.business.entity.StorageDetail;
import org.jeecg.modules.business.entity.StoreStocks;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.dto.StoreStocksDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓库库存建账表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public interface StoreStocksMapper extends BaseMapper<StoreStocks> {

    int updateQtySafe(StorageDetail storageDetail);

    int updateStockQtySafe(StorageDetail storageDetail);

    int updateOccQtySafe(StorageDetail storageDetail);

    int addOccQtySafe(StorageDetail storageDetail);

    int addOccAndStockQtySafe(StorageDetail storageDetail);

    /**
     * 根据给定的分页对象和店铺库存对象查询指定店铺的库存列表
     *
     * @param page       分页对象，用于指定查询的页码和每页显示的数量
     * @param storeStocksDTO 店铺库存对象，用于指定查询的条件，如店铺ID、商品ID等
     * @return 符合查询条件的店铺库存列表的分页结果
     */
    IPage<StoreStocks> queryPageList(Page<StoreStocks> page, StoreStocksDTO storeStocksDTO);
    IPage<StoreStocks> queryPageList_(Page<StoreStocks> page, StoreStocksDTO storeStocksDTO);

    // 增加占用数量 - 一对多
    int addOccQtySafeOneToMany(StorageDetail storageDetail);

    /**
     * 仓库库存冲正
     *
     * @param storageDetail
     * @return int
     * <AUTHOR>
     * @date 2024/12/10 13:20
     */
    int updateQtySafeRectify(StorageDetail storageDetail);
}
