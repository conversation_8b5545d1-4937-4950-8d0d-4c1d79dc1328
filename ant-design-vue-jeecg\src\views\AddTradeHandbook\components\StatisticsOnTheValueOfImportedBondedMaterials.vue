<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件序号" v-model="queryParam.gNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件料号" v-model="queryParam.copGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入商品名称" v-model="queryParam.gName"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div style="margin-top: -15px;">
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="gNo"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				class="j-table-force-nowrap"
				@change="handleTableChange"
			>
				<span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
          {{subStrForColumns(record.gModel, 25)}}
        </span>
<!--				<template slot="footer" slot-scope="currentPageData">-->
<!--					<a-table-->
<!--						rowKey="id"-->
<!--						:columns="columns"-->
<!--						:bordered=false-->
<!--						:pagination=false-->
<!--						:dataSource=[]-->
<!--						:showHeader=false-->
<!--					></a-table>-->
<!--				</template>-->
			</a-table>
		</div>
	</a-card>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import {getAction} from "@/api/manage";
import { floatAdd } from '@/utils/util'
import {subStrForColumns} from "@/utils/util";
import {filterDictTextByCache} from "@/components/dict/JDictSelectUtil";
import {ajaxGetDictItems} from "@/api/api";
const DICT_erp_units = 'erp_units,name,code'
export default {
	name: "StatisticsOnTheValueOfImportedBondedMaterials",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			disableMixinCreated: true,
			units: [],
			queryParam: {
				type: '1', // 1料件 2成品 3损耗
				emsId: '999999999'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			emsHead: {},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '料件序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '料件料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '计量单位',
					align: 'center',
					dataIndex: 'unit',
					customRender: this.showQunitText
				},
				{
					title: '备案金额',
					align: 'center',
					dataIndex: 'decPrice'
				},
				{
					title: '直接进口金额合计(A)',
					align: 'center',
					dataIndex: 'zjjksl'
				},
				{
					title: '深加工结转进口金额合计(B)',
					align: 'center',
					dataIndex: 'sjgjzjksl'
				},
				{
					title: '余料转入金额合计(C)',
					align: 'center',
					dataIndex: 'yljzjksl'
				},
				{
					title: '料件退换进口金额合计(D)',
					align: 'center',
					dataIndex: 'ljthjksl'
				},
				{
					title: '料件退换出口金额合计(E)',
					align: 'center',
					dataIndex: 'ljthcksl'
				},
				{
					title: '进口金额合计(F)',
					align: 'center',
					dataIndex: 'jkjehj'
				},
				{
					title: '内销征税金额合计(G)',
					align: 'center',
					dataIndex: 'nxzssl'
				},
				{
					title: '转复出金额合计(H)',
					align: 'center',
					dataIndex: 'zfcsl'
				},
				{
					title: '销毁金额合计(I)',
					align: 'center',
					dataIndex: 'xhsl'
				},
				{
					title: '余料转出金额合计(J)',
					align: 'center',
					dataIndex: 'ylckjzsl'
				},
				{
					title: '实际金额进口合计(K=F-G-H-I-J)',
					align: 'center',
					dataIndex: 'sjjkjehj'
				},
				{
					title: '已出口成品折料金额合计',
					align: 'center',
					dataIndex: 'yckcpzljehj'
				},
				{
					title: '加权平均单价',
					align: 'center',
					dataIndex: 'jqpjdj'
				},
				{
					title: '边角料征税金额合计',
					align: 'center',
					dataIndex: 'bjlybsl'
				},
				{
					title: '边角料复出金额合计',
					align: 'center',
					dataIndex: 'bjlfcsl'
				},
			],
			url: {
				list: '/business/ems/listEmsDetailAmountByReport',
			}
		}
	},
	created() {
		this.initDictData(DICT_erp_units)
	},
	methods: {
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == DICT_erp_units) {
					this.units = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						this.initDictData(dictCode)
					}
				})
			}
		},
		subStrForColumns,
		init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						let data = res.result.records || res.result
						let newDataSource = data
						if (data.length > 0) {
							let item = {}
							item.unit = 'Total:'
							let amount = 0
							let zjjksl = 0
							let sjgjzjksl = 0
							let yljzjksl = 0
							let ljthjksl = 0
							let ljthcksl = 0
							let jkjehj = 0
							let nxzssl =0
							let zfcsl = 0
							let xhsl = 0
							let ylckjzsl = 0
							let sjjkjehj = 0
							let yckcpzljehj = 0
							let jqpjdj = 0
							let bjlybsl = 0
							let bjlfcsl = 0
							for (let i = 0; i < data.length; i++) {
								amount = floatAdd(amount,data[i].decPrice ? Number(data[i].decPrice) : 0)
								zjjksl = floatAdd(zjjksl,data[i].zjjksl ? Number(data[i].zjjksl) : 0)
								sjgjzjksl = floatAdd(sjgjzjksl,data[i].sjgjzjksl ? Number(data[i].sjgjzjksl) : 0)
								yljzjksl = floatAdd(yljzjksl,data[i].yljzjksl ? Number(data[i].yljzjksl) : 0)
								ljthcksl = floatAdd(ljthcksl,data[i].ljthcksl ? Number(data[i].ljthcksl) : 0)
								// ljthcksl += data[i].ljthcksl ? Number(data[i].ljthcksl) : 0
								jkjehj = floatAdd(jkjehj,data[i].jkjehj ? Number(data[i].jkjehj) : 0)
								nxzssl = floatAdd(nxzssl,data[i].nxzssl ? Number(data[i].nxzssl) : 0)
								zfcsl = floatAdd(zfcsl,data[i].zfcsl ? Number(data[i].zfcsl) : 0)
								ylckjzsl = floatAdd(ylckjzsl,data[i].ylckjzsl ? Number(data[i].ylckjzsl) : 0)
								sjjkjehj = floatAdd(sjjkjehj,data[i].sjjkjehj ? Number(data[i].sjjkjehj) : 0)
								yckcpzljehj = floatAdd(yckcpzljehj,data[i].yckcpzljehj ? Number(data[i].yckcpzljehj) : 0)
								jqpjdj = floatAdd(jqpjdj,data[i].jqpjdj ? Number(data[i].jqpjdj) : 0)
								bjlybsl = floatAdd(bjlybsl,data[i].bjlybsl ? Number(data[i].bjlybsl) : 0)
								bjlfcsl = floatAdd(bjlfcsl,data[i].bjlfcsl ? Number(data[i].bjlfcsl) : 0)
							}
							item.decPrice = amount
							item.zjjksl = zjjksl
							item.sjgjzjksl = sjgjzjksl
							item.yljzjksl = yljzjksl
							item.ljthjksl = ljthjksl
							item.ljthcksl = ljthcksl
							item.jkjehj = jkjehj
							item.nxzssl = nxzssl
							item.zfcsl = zfcsl
							item.xhsl = xhsl
							item.ylckjzsl = ylckjzsl
							item.sjjkjehj = sjjkjehj
							item.yckcpzljehj = yckcpzljehj
							item.jqpjdj = jqpjdj
							item.bjlybsl = bjlybsl
							item.bjlfcsl = bjlfcsl
							newDataSource.push(item)
						}
						this.dataSource = newDataSource
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				type: '1', // 1料件 2成品 3损耗
				emsId: this.emsHead.id ? this.emsHead.id : '999999999',
				emsNo : this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			}
			this.loadData(1)
			this.onClearSelected()
		},
		showQunitText(text, record, index) {
			return this.getText(text, this.units)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}else {
						text = value
					}
				}
			}
			return text
		},
	}
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.table-page-search-wrapper {
	margin-top: -16px;
	margin-bottom: 16px;
}
.textGreen {
	color: darkgreen;
	font-weight: bold;
}
/deep/  .ant-table-expand-icon-th {
	background-color: #eaebed !important;
	border: 1px solid #d7dbe4 !important;
}
</style>