<template>
	<j-modal
		:title="'加贸手册成品 ' + title"
		:width="width"
		:visible="visible"
		@ok="handleSave"
		:okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
		@cancel="handleCancel"
		cancelText="关闭"
		selfCloseAction="closePop"
		:disableSubmit="disableSubmit"
	>
		<template slot="footer">
			<span class="tipsText" style="float: left">手册编号：{{model.emsNo}}</span>
			<a-button type="default" @click="handleCancel">关闭</a-button>
			<a-button v-show="!disableSubmit" type="primary" @click="handleSave">保存</a-button>
		</template>

		<a-spin :spinning="confirmLoading">
			<a-collapse v-model="activeKeys" :bordered="false" style="margin-top: -10px">
				<!-- 基本信息 -->
				<a-collapse-panel key="1" header="基本信息" >
					<j-form-container :disabled="disableSubmit">
						<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
							<a-row :gutter="24" type="flex" justify="start">
								<a-col :span="8">
									<a-form-model-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="gNo">
										<a-input-number placeholder='请输入序号' v-model="model.gNo" :min='0' :maxLength="10" style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="料号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="copGno">
										<a-input placeholder='请输入料号' v-model="model.copGno" :maxLength="10"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="gName">
										<a-input placeholder='请输入商品名称' v-model="model.gName"></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="商品编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="codet">
										<a-input v-model="model.codet" placeholder='请输入商品编码'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="规格型号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="gModel">
										<a-input v-model="model.gModel" placeholder='请输入规格型号'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="币制" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="curr">
										<j-dict-select-tag
											v-model="model.curr"
											type="node-limit"
											dictCode="erp_currencies,name,code,currency,1=1"
											placeholder="请选择币制"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="计量单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unit">
										<j-dict-select-tag
											v-model="model.unit"
											type="join"
											dictCode="erp_units,name,code,1=1 order by code asc"
											placeholder="请选择计量单位"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="法定计量单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unit1">
										<j-dict-select-tag
											v-model="model.unit1"
											type="join"
											dictCode="erp_units,name,code,1=1 order by code asc"
											placeholder="请选择法定计量单位"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="法定第二计量单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unit2">
										<j-dict-select-tag
											v-model="model.unit2"
											type="join"
											dictCode="erp_units,name,code,1=1 order by code asc"
											placeholder="请选择法定第二计量单位"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报单价" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="decPrice">
										<a-input-number placeholder='请输入申报单价' v-model="model.decPrice" :min='0' style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="征免方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dutyMode">
										<j-dict-select-tag
											v-model="model.dutyMode"
											type="node-limit"
											dictCode="ZJMSFS"
											placeholder="请选择征免方式"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="申报数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="qty">
										<a-input-number placeholder='请输入申报数量' v-model="model.qty" :min='0' style='width: 100%;'></a-input-number>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="产销国" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="countryCode">
										<j-dict-select-tag
											v-model="model.countryCode"
											type="node-limit"
											dictCode="erp_countries,name,code,isenabled=0"
											placeholder="请选择产销国"
										/>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="重点商品标识" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="keyProductIdentification">
										<a-input v-model="model.keyProductIdentification" placeholder='请输入重点商品标识'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="商品属性" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="attributes">
										<a-input v-model="model.attributes" placeholder='请输入商品属性'></a-input>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="修改标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="modifyFlag">
										<a-select v-model="model.modifyFlag" allowClear showSearch placeholder="请选择修改标志">
											<a-select-option value="1">修改</a-select-option>
											<!-- <a-select-option value="2">报核前</a-select-option> -->
											<a-select-option value="3">增加</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="海关执行标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="customsEnforcementMark">
									<a-select v-model="model.customsEnforcementMark" allowClear showSearch placeholder="请选择海关执行标志">
											<a-select-option value="1">正常执行</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="单耗质疑标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unitConsumptionQueryFlag">
											<a-select v-model="model.unitConsumptionQueryFlag" allowClear showSearch placeholder="请选择单耗质疑标志">
													<a-select-option value="0">不质疑</a-select-option>
											<a-select-option value="1">质疑</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>
								<a-col :span="8">
									<a-form-model-item label="磋商标志" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="negotiationSymbol">
												<a-select v-model="model.negotiationSymbol" allowClear showSearch placeholder="请选择磋商标志">
													<a-select-option value="0">未磋商</a-select-option>
											<a-select-option value="1">磋商</a-select-option>
										</a-select>
									</a-form-model-item>
								</a-col>

								<a-col :span="8">
									<a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="note">
										<j-remarks-component
											v-model="model.note"
											placeholder="请输入备注"
											:maxLength="500"
											:readOnly="disableSubmit"
										/>
									</a-form-model-item>
								</a-col>
							</a-row>
						</a-form-model>
					</j-form-container>
				</a-collapse-panel>
			</a-collapse>
		</a-spin>

	</j-modal>
</template>
<script>
import { getAction, httpAction } from '@/api/manage'

export default {
	name: 'AimgEditModal',
	data() {
		return {
			model: {
				emsId: '',
				emsNo: ''
			},
			emsHead: {},
			activeKeys: ['1', '2'],
			labelCol: {
				xs: { span: 24 },
				sm: { span: 9 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 14 },
			},
			title: '',
			width: 980,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			validatorRules: {
				gNo: [{ required: true, message: '请输入序号!', max: 10, checkName: '序号', validator: this.checkNo}],
				copGno: [{ required: true, max: 50, checkName: '料号', validator: this.checkNo }],
				gName: [{ required: true, message: '请填写商品名称!'}],
				unit: [{ required: true, message: '请选择计量单位!' }],
				codet: [{ required: true, checkName: '商品编码', validator: this.checkNo, max: 10 }],
				decPrice: [{
					max: 18,
					validateParam: [
						{
							pattern: /^[0-9]+([.]{1}[0-9]+){0,1}$/,
							message: '请填写正确的申报单价!'
						},
						{
							pattern: /^[0-9]{0,21}$|^[0-9]{0,21}([.]{1})([0-9]{0,5})$/,
							message: '最多五位小数!'
						}
					],
					validator: this.checkNo,
					checkName: '申报单价'}],
				qty: [
					{
						required: true,
						max: 18,
						validateParam: [
							{
								pattern: /^(([^0][0-9]+|0).([0-9]{1,200})$)|^(([^0][0-9]+|0)$)|^(([1-9]+).([0-9]{1,200})$)|^(([1-9]+)$)/,
								message: '请填写正确的申报数量!'
							},
							{
								pattern: /^(([^0][0-9]+|0).([0-9]{1,5})$)|^(([^0][0-9]+|0)$)|^(([1-9]+).([0-9]{1,5})$)|^(([1-9]+)$)/,
								message: '最多五位小数!'
							}
						],
						validator: this.checkNo,
						checkName: '申报数量'
					}
				],
				gModel: [{ required: true, message: '请填写规格型号!' }],
			},
			url: {
				save: '/business/ems/saveEmsAexg',
				getById: '/business/ems/getEmsAexgById',
			}
		}
	},
	methods : {
		add(record) {
			this.emsHead = Object.assign({}, record)
			this.model.emsNo = this.emsHead.emsNo
			this.model.emsId = this.emsHead.id
			this.visible = true
		},
		edit(record) {
			this.initModel(record)
			this.visible = true
		},
		initModel(value) {
			this.confirmLoading = true
			let val = value
			if (val == undefined) {
				val = this.model
			}
			let params = {
				id: val.id,
			}
			if (val.id != null) {
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.model = record
						} else {
							// 失败
							this.$message.warning(res.message || res)
							this.close()
						}
					})
					.finally(() => {
						this.confirmLoading = false
					})
			} else {
				// 新增
				this.confirmLoading = false
			}
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的加贸账册成品数据：', this.model)
					httpAction(this.url.save, this.model, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								this.close()
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		close() {
			this.$emit('close')
			this.model = {}
			this.emsHead = {}
			this.visible = false
		},
		handleCancel () {
			this.close()
		},
		/**
		 ** 小数验证
		 ** rule入参
      {
        required: true,
        max: 18,
        validateParam: [
          {
            pattern: this.$regex.POSITIVE_DECIMAL,
            message: "请填写正确的申报单价!"
          },
          {
            pattern: this.$regex.POSITIVE_DECIMAL_5,
            message: "最多五位小数!"
          }
        ],
        validator: this.checkNo,
        checkName: '申报单价'
      }
		 ** 返回值：callback
		 **/
		checkNo (rule, value, callback) {
			if (rule.required && this.isEmpty(value)) {
				if (rule.inputType === 'select') {
					callback(`请选择${rule.checkName}!`)
				} else {
					callback(`请填写${rule.checkName}!`)
				}
			}
			if (!this.isEmpty(value)) {
				if (rule.validateParam) {
					rule.validateParam.forEach(element => {
						if (element.pattern && !element.pattern.test(value)) {
							callback(element.message)
						}
					})
				}

				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(`长度不能大于${rule.max}位!`)
				}
				if (!this.isEmpty(rule.max) && value > Math.pow(10, rule.max)) {
					callback(`长度不能大于${rule.max}位!`)
				} else {
					callback()
				}
			} else {
				callback()
			}
		}
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
</style>