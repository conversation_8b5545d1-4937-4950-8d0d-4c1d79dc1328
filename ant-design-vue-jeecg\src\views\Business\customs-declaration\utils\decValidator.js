/**
 * 报关单数据校验工具
 * 用于集中管理所有关于报关单数据的校验规则
 */
import { getAction } from '@/api/manage'

/**
 * 校验报关单数据并返回错误消息数组
 * @param {Object} record 报关单记录对象
 * @returns {Array} 错误消息数组，如果没有错误则为空数组
 */
export async function validateDecRecord(record) {
	if (!record) return []

	const errorMsgs = []

	// 基础必填项校验
	if (!record.declarePlace || record.declarePlace.trim() === '') {
		errorMsgs.push('【申报地海关】不能为空')
	}

	if (!record.outPortCode || record.outPortCode.trim() === '') {
		errorMsgs.push('【进出境关别】不能为空')
	}

	// 监管方式校验
	if (!record.tradeTypeCode || record.tradeTypeCode.trim() === '') {
		errorMsgs.push('【监管方式】不能为空')
	}

	// 境内收发货人校验
	if (!record.optUnitName || record.optUnitName.trim() === '') {
		errorMsgs.push('【境内收发货人】不能为空')
	}

	// 申报单位校验
	if (!record.declareUnitName || record.declareUnitName.trim() === '') {
		errorMsgs.push('【申报单位】不能为空')
	}

	// 表体数据校验
	if (!record.decLists || record.decLists.length === 0) {
		errorMsgs.push('【表体数据】至少需要一条商品信息')
	} else {
		// 创建一个Promise数组来收集所有异步校验结果
		const validationPromises = []
		// 遍历校验每条表体数据
		record.decLists.forEach((item, index) => {
			// 商品编码校验
			if (!item.hscode || item.hscode.trim() === '') {
				errorMsgs.push(`第${index + 1}项商品【商品编码】不能为空`)
			} else {
				// 检查商品编码是否存在于税则表中，并验证申报要素必填项
				const hscodePromise = checkHscodeAndValidateElements(item.hscode, item.hsmodel, index + 1).then(result => {
					if (!result.exists) {
						errorMsgs.push(`第${index + 1}项商品【商品编码】${item.hscode}已经被海关作废`)
					}
					if (result.elementErrors && result.elementErrors.length > 0) {
						errorMsgs.push(...result.elementErrors)
					}
				})
				validationPromises.push(hscodePromise)
			}
			// 商品名称校验
			if (!item.hsname || item.hsname.trim() === '') {
				errorMsgs.push(`第${index + 1}项商品【商品名称】不能为空`)
			}
			// 商品名称校验
			if (!item.hsmodel || item.hsmodel.trim() === '') {
				errorMsgs.push(`第${index + 1}项商品【申报要素】不能为空`)
			}
			// 成交数量校验
			if (!item.goodsCount || isNaN(parseFloat(item.goodsCount))) {
				errorMsgs.push(`第${index + 1}项商品【成交数量】必须为有效数字`)
			}
			// 价格合理性校验
			if (parseFloat(item.goodsCount) <= 0) {
				errorMsgs.push(`第${index + 1}项商品【成交数量】必须大于0`)
			}
			// 单价校验
			if (!item.price || isNaN(parseFloat(item.price))) {
				errorMsgs.push(`第${index + 1}项商品【单价】必须为有效数字`)
			}
			// 价格合理性校验
			if (parseFloat(item.price) <= 0) {
				errorMsgs.push(`第${index + 1}项商品【单价】必须大于0`)
			}
			// 总价校验
			if (!item.total || isNaN(parseFloat(item.total))) {
				errorMsgs.push(`第${index + 1}项商品【总价】必须为有效数字`)
			}
			// 价格合理性校验
			if (parseFloat(item.total) <= 0) {
				errorMsgs.push(`第${index + 1}项商品【总价】必须大于0`)
			}
		})
		// 等待所有异步校验结果
		await Promise.all(validationPromises)
	}

	// 对错误消息进行排序，确保按照商品序号顺序显示
	errorMsgs.sort((a, b) => {
		// 提取商品序号的正则表达式
		const itemRegex = /第(\d+)项商品/
		const matchA = a.match(itemRegex)
		const matchB = b.match(itemRegex)

		// 如果两个消息都包含商品序号，按序号排序
		if (matchA && matchB) {
			return parseInt(matchA[1]) - parseInt(matchB[1])
		}
		// 如果只有一个包含商品序号，将包含序号的排在后面
		if (matchA) return 1
		if (matchB) return -1
		// 其他情况保持原顺序
		return 0
	})

	return errorMsgs
}

/**
 * 检查商品编码是否存在于税则表中，并验证申报要素必填项
 * @param {String} hscode 商品编码
 * @param {String} hsmodel 申报要素
 * @param {Number} itemIndex 商品项序号
 * @returns {Promise<Object>} 包含存在性和申报要素验证结果的对象
 */
async function checkHscodeAndValidateElements(hscode, hsmodel, itemIndex) {
	try {
		// 调用后端API查询税则表
		const response = await getAction(`/dictionary/erpHscodes/listTariffByHscodeNew?codeTs=${hscode}`)
		// 检查税号是否存在
		const exists = response && response.result && response.result.length > 0
		const result = {
			exists: exists,
			elementErrors: []
		}
		// 如果税号存在，进行申报要素必填项验证
		if (exists && response.result[0]) {
			const tariffData = response.result[0]
			const sbysRequired = tariffData.sbysRequired
			const sbys = tariffData.sbys // 新增：获取申报要素项名

			// 验证申报要素必填项
			if (sbysRequired && hsmodel && sbys) {
				const elementErrors = validateDeclarationElements(hsmodel, sbysRequired, sbys, itemIndex)
				result.elementErrors = elementErrors
			}
		}
		return result
	} catch (error) {
		console.error('查询税则表失败:', error)
		// 查询失败时默认返回存在，避免误报
		return {
			exists: true,
			elementErrors: []
		}
	}
}

/**
 * 验证申报要素必填项
 * @param {String} hsmodel 申报要素字符串，用竖线分割
 * @param {String} sbysRequired 必填标识字符串，1表示必填，0表示非必填
 * @param {String} sbys 申报要素项名字符串，格式如"0:品牌类型;1:出口享惠情况;2:用途;3:品牌（中文或外文名称）;4:型号;5:GTIN;6:CAS;7:其他;"
 * @param {Number} itemIndex 商品项序号
 * @returns {Array} 错误消息数组
 */
function validateDeclarationElements(hsmodel, sbysRequired, sbys, itemIndex) {
	const errors = []
	if (!hsmodel || !sbysRequired || !sbys) {
		return errors
	}
	console.log('hsmodel:', hsmodel, 'sbysRequired:', sbysRequired, 'sbys:', sbys)
	// 按竖线分割申报要素
	const elements = hsmodel.split('|')
	// 将sbysRequired转换为数组
	const requiredFlags = sbysRequired.split('')

	// 解析sbys字符串，构建项名映射
	const itemNameMap = {}
	const sbysItems = sbys.split(';')
	sbysItems.forEach(item => {
		if (item.trim()) {
			const [index, name] = item.split(':')
			if (index !== undefined && name !== undefined) {
				itemNameMap[parseInt(index)] = name.trim()
			}
		}
	})

	// 收集所有缺失的必填项信息
	const missingRequiredItems = []

	// 遍历检查每个位置的必填项
	for (let i = 0; i < requiredFlags.length; i++) {
		const isRequired = requiredFlags[i] === '1'
		// 获取对应位置的元素值，如果超出范围则为空字符串
		const elementValue = (i < elements.length && elements[i]) ? elements[i].trim() : ''
		// 如果该位置是必填的，但值为空或无效
		if (isRequired && (!elementValue || elementValue === '')) {
			const itemName = itemNameMap[i] || `第${i + 1}项`
			// 在项名前加上序号，格式如"3类型（如吊扇、落地扇、壁扇等）"
			const itemWithIndex = `${i + 1}${itemName}`
			missingRequiredItems.push(itemWithIndex)
		}
	}

	// 如果有缺失的必填项，生成合并的错误消息
	if (missingRequiredItems.length > 0) {
		let itemText = ''
		if (missingRequiredItems.length === 1) {
			itemText = missingRequiredItems[0]
		} else {
			// 将项名数组转换为"3类型（如吊扇、落地扇、壁扇等）、4输出功率项"的格式
			itemText = missingRequiredItems.join('、')
		}
		errors.push(`第${itemIndex}项商品【申报要素】${itemText}为必填项，不能为空`)
	}
	return errors
}

/**
 * 检查商品编码是否存在于税则表中
 * @param {String} hscode 商品编码
 * @returns {Promise<boolean>} 是否存在
 */
async function checkHscodeExists(hscode) {
	try {
		// 调用后端API查询税则表
		const response = await getAction(`/dictionary/erpHscodes/listTariffByHscodeNew?codeTs=${hscode}`)
		// 如果返回的数据为空数组，则表示税号不存在或已作废
		return response && response.result && response.result.length > 0
	} catch (error) {
		console.error('查询税则表失败:', error)
		// 查询失败时默认返回true，避免误报
		return true
	}
}

/**
 * 显示AI校验提示框
 * @param {Array} errorMsgs 错误消息数组
 * @param id 报关单ID
 * @param {Function} showModal 显示弹窗的函数
 */
export function showAiValidateModal(errorMsgs, id, showModal) {
	// 无论是否有错误，都调用showModal函数
	let errorHtml = '';
	if (errorMsgs && errorMsgs.length > 0) {
		errorMsgs.forEach((msg, index) => {
			errorHtml += `${index + 1}. ${msg}；<br/>`
		})
	}

	// 调用传入的显示弹窗函数
	if (typeof showModal === 'function') {
		showModal(errorHtml)
	}
}

/**
 * 完整校验流程：校验并显示结果
 * @param {Object} record 报关单记录对象
 * @param {Function} showModal 显示弹窗的函数
 * @param {Boolean} isUserAction 是否为用户主动点击检查
 * @returns {boolean} 校验是否通过
 */
export async function validateAndShowResult(record, showModal, isUserAction = false) {
	const errorMsgs = await validateDecRecord(record)

	// 只有在有错误或用户主动点击检查时才显示弹窗
	if (errorMsgs.length > 0 || isUserAction) {
		showAiValidateModal(errorMsgs, record.id, showModal)
	}

	return errorMsgs.length === 0
}
