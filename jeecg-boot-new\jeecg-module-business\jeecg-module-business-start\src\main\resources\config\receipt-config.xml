<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.3.xsd">

    <!-- 回执配置 -->
    <bean id="receiptConfig"
          class="org.jeecg.modules.business.config.ReceiptConfig">
        <!-- 回执路由配置 -->
        <property name="receiptRouteConfigList">
            <list>
                <!-- 报关单回执(单一窗口)-->
                <map>
                    <!-- 来源地址 -->
                    <entry key="sourceAddress"
                           value="${receipt.single_server}/ImpPath/DecCus001/InBox?${receipt.single_param}"></entry>
                    <!-- 目标地址 -->
                    <entry key="targetAddress"
                           value="file:${receipt.single_backups}" />
                    <!-- 来源类型(通道类型):0:单一窗口 -->
                    <entry key="sourceType" value="0" />
                    <!-- 路由ID -->
                    <entry key="routeId" value="dec-single" />
                </map>
                <!-- 核注单回执 -->
                <map>
                    <!-- 来源地址 -->
                    <entry key="sourceAddress"
                           value="${receipt.single_server}/ImpPath/Nems/InBox?${receipt.single_param}"/>
                    <!-- 目标地址 -->
                    <entry key="targetAddress" value="file:${receipt.single_backups}Nems/InBox"/><!-- file:/home/<USER>/receive/Redsw-->
                    <!-- 来源类型(通道类型):1:海关直连 -->
                    <entry key="sourceType" value="1"/>
                    <!-- 路由ID -->
                    <entry key="routeId" value="merger_Nems"/>
                </map>
                <!-- 核放单、太古保税仓.集中申报 -->
                <map>
                    <!-- 来源地址 -->
                    <entry key="sourceAddress"
                           value="${receipt.single_server}/ImpPath/Sas/InBox?${receipt.single_param}"/>
                    <!-- 目标地址 -->
                    <entry key="targetAddress" value="file:${receipt.single_backups}Sas/InBox"/><!-- file:/home/<USER>/receive/Redsw-->
                    <!-- 来源类型(通道类型):1:海关直连 -->
                    <entry key="sourceType" value="1"/>
                    <!-- 路由ID -->
                    <entry key="routeId" value="merger_declare"/>
                </map>
                <!-- 电子委托协议报文 -->
                <map>
                    <entry key="sourceAddress"
                           value="${receipt.single_server}/ImpPath/Acd/InBox?${receipt.single_param}"/>
                    <entry key="targetAddress" value="file:${receipt.single_backups}/Acd/InBox"/>
                    <!-- 来源类型(通道类型):1:海关直连 -->
                    <entry key="sourceType" value="1"/>
                    <!-- 路由ID -->
                    <entry key="routeId" value="elec-protocol"/>
                </map>
                <!-- 加工贸易手册报文 -->
                <map>
                    <entry key="sourceAddress"
                           value="${receipt.single_server}/ImpPath/Npts/InBox?${receipt.single_param}"/>
                    <entry key="targetAddress" value="file:${receipt.single_backups}/Npts/InBox"/>
                    <!-- 来源类型(通道类型):1:海关直连 -->
                    <entry key="sourceType" value="1"/>
                    <!-- 路由ID -->
                    <entry key="routeId" value="trade-npts"/>
                </map>
            </list>
        </property>
        <!-- 回执处理ServiceMap集合 -->
        <property name="receiptHandleServiceMap">
            <map>
                <!-- DecImportResponse节点  基础数据-->
                <entry key="DecImportResponse"
                       value-ref="decImportResponseReceiptHandleServiceImpl"/>
                <!-- DEC_RESULT节点 -->
                <entry key="DEC_RESULT"
                       value-ref="decResultReceiptHandleServiceImpl"/>
                <!-- DEC_DATA节点 -->
                <entry key="DEC_DATA"
                       value-ref="decDataReceiptHandleServiceImpl"/>
                <!-- Package节点 -->
                <entry key="Package"
                       value-ref="packageReceiptHandleServiceImpl"/>
                <!-- CommonResponeMessage节点 -->
                <entry key="CommonResponeMessage"
                       value-ref="commonResponeMessageReceiptHandleServiceImpl"/>
                <!-- ImportResponse节点 -->
                <entry key="ImportResponse"
                       value-ref="commonResponeMessageReceiptHandleServiceImpl"/>
                <!-- ImportAgrResponse节点 -->
                <entry key="ImportAgrResponse"
                       value-ref="importAgrResponseHandleServiceImpl"/>
                <!-- Signature节点 -->
                <entry key="Signature"
                        value-ref="signatureReceiptHandleServiceImpl"/>
                <!--修撤单-->
<!--                <entry key="DecModSaveResponse"-->
<!--                       value-ref="decModSaveResponseHandleServiceImpl"/>-->
                <!-- DataInfo节点 -->
<!--                <entry key="DataInfo"-->
<!--                       value-ref="dataInfoReceiptHandleServiceImpl"/>-->
                <!-- Signature节点 -->
<!--                <entry key="Signature"-->
<!--                       value-ref="signatureReceiptForDocdHandleServiceImpl"/>-->
                <!-- 集报服务 Sas211/Sas221 -->
                <!--
                                <entry key="SasMessageHandler"
                                       value-ref="sasHandleMessageTypeServiceImpl"/>
                -->
                <!-- Signature节点 -->
<!--                <entry key="TRN_RESULT"-->
<!--                       value-ref="trnResultHandleServiceImpl"/>-->
            </map>
        </property>
    </bean>

</beans>
