<template>
	<a-card :bordered="false" :body-style="{padding: '0 24px 24px 24px'}" :loading= 'loading'>
		<a-descriptions
			title="进口保税科件重量统计"
			bordered
			:column="2"
			size="small"
			class="label"
		>
			<a-descriptions-item label="直接进口重量(A)">
				{{aimgDate.zjjkWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="余料结转进口重量(B)">
				{{aimgDate.yljzjkWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="深加工结转进口重量(C)">
				{{aimgDate.sjgjzjkWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="料件退换进口重量(D)">
				{{aimgDate.ljthjkWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="料件内销重量(E)">
				{{aimgDate.ljnxWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="余量结转出口重量(F)">
				{{aimgDate.yljzckWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="料件复出重量(G)">
				{{aimgDate.ljfcWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="料件退换出口重量(H)">
				{{aimgDate.ljthckWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="边角料内销重量(I)">
				{{aimgDate.bjlnxWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="边角料复出重量(J)">
				{{aimgDate.bjlfcWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="边角料销毁重量(K)">
				{{aimgDate.bjlxhWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="料件销毁重量(L)">
				{{aimgDate.ljxhWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="进口保税料件重量合计(M)">
				{{aimgDate.jkbsljWeightSum}}
			</a-descriptions-item>
		</a-descriptions>

		<a-descriptions style="margin-top: 20px"
			title="出口保税成品重量统计"
			bordered
			:column="2"
			size="small"
			class="label"
		>
			<a-descriptions-item label="直接出口重量(A)">
				{{aexgDate.zjckWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="深加工结转出口重量(B)">
				{{aexgDate.sjgjzckWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="成品退换出口重量(C)">
				{{aexgDate.cpthckWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="成品退换进口重量(D)">
				{{aexgDate.cpthjkWeight}}
			</a-descriptions-item>
			<a-descriptions-item label="出口保税成品重量合计(M)">
				{{aexgDate.ckbscpWeightSum}}
			</a-descriptions-item>
		</a-descriptions>
	</a-card>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import {filterDictTextByCache} from "@/components/dict/JDictSelectUtil";
import {getAction} from "@/api/manage";
import {subStrForColumns} from "@/utils/util";
export default {
	name: "WeightStatistics",
	mixins: [JeecgListMixin, mixinDevice],
		data() {
		return {
			loading:false,
	disableMixinCreated: true,
		queryParam:{},
				aimgDate:{},
				aexgDate:{},
			url: {

				list: '/business/ems/listEmsDetailWeightStatistics'
			}

		}
		},
		methods: {
	init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
		
			var params = this.queryParam
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						this.aimgDate = res.result.aimg[0]
						this.aexgDate = res.result.aexg[0]
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		}

}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
/deep/ .label .ant-descriptions-item-label{
	width: 250px;
}
</style>