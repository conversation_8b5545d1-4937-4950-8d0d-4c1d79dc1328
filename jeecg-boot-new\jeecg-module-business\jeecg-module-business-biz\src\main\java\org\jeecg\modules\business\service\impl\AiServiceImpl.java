package org.jeecg.modules.business.service.impl;

import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.config.mqtoken.UserTokenContext;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.entity.excel.ExportInvoiceExcel;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.IAiLogService;
import org.jeecg.modules.business.service.IAiService;
import org.jeecg.modules.business.service.IDecHeadService;
import org.jeecg.modules.business.service.IEnterpriseInfoService;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.modules.business.controller.OrderInfoController.containsCharacter;
import static org.jeecg.modules.business.util.ApiUtil.*;
import static org.jeecg.modules.business.util.UnitChecker.isWeightUnit;

/**
 * AI相关 服务实现类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/4/27 09:21
 */
@Slf4j
@Service
public class AiServiceImpl implements IAiService {
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Autowired
    private IDecHeadService decHeadService;
    @Autowired
    private AiRelTableMapper aiRelTableMapper;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private PtsEmsAexgMapper emsAexgMapper;
    @Autowired
    private ErpHscodeDataMapper erpHscodeDataMapper;
    @Autowired
    private DecHeadMapper decHeadMapper;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private AiGenRecordMapper aiGenRecordMapper;
    @Autowired
    private ErpDistrictsMapper erpDistrictsMapper;
    @Autowired
    private IAiLogService aiLogService;
    @Autowired
    private AiServiceImpl self;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Value("${sso.api.ai-maker-cost}")
    private String aiMakerCostUrl;
    @Value("${ai.config.url.prod}")
    private String aiBaseUrl;

    /**
     * 判断国家是否在优惠关税国家列表中
     *
     * @param countryCode 国家代码
     * @return 是否在优惠关税国家列表中
     */
    private static boolean isPreferentialTariffCountry(String countryCode) {
        // 享受优惠关税的国家列表
        Set<String> preferentialCountries = new HashSet<>(Arrays.asList(
                "韩国", "印度", "孟加拉", "斯里兰卡", "老挝", "文莱", "柬埔寨",
                "印度尼西亚", "马来西亚", "缅甸", "菲律宾", "新加坡", "泰国",
                "越南", "巴基斯坦", "智利", "新西兰", "秘鲁", "台湾",
                "哥斯达黎加", "冰岛", "瑞士", "列士敦士登", "澳大利亚",
                "格鲁吉亚", "日本", "尼加拉瓜", "厄瓜多尔", "塞尔维亚",
                "洪都拉斯", "马尔代夫"
        ));
        // 享受优惠关税的国家列表（英文代码）
        Set<String> preferentialCountriesEn = new HashSet<>(Arrays.asList(
                "KOR", "IND", "BGD", "LKA", "LAO", "BRN", "KHM",
                "IDN", "MYS", "MMR", "PHL", "SGP", "THA",
                "VNM", "PAK", "CHL", "NZL", "PER", "TWN",
                "CRI", "ISL", "CHE", "LIE", "AUS",
                "GEO", "JPN", "NIC", "ECU", "SRB",
                "HND", "MDV"
        ));
        return preferentialCountries.contains(countryCode) || preferentialCountriesEn.contains(countryCode);
    }

    /**
     * 判断字符串是否为纯英文字符
     *
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2025/4/18 09:24
     */
    public static boolean isNotChinese(String str) {
        if (str == null) return true;
        return !str.matches(".*[\\u4e00-\\u9fa5].*");
    }

    /**
     * 拆分运输工具及航次信息
     *
     * @param transportInfo 完整的运输工具及航次字符串，如"EVER FAR 1208-025E"
     * @return String数组，第一个元素是运输工具名称，第二个元素是航次
     */
    public static String[] splitTransportAndVoyage(String transportInfo) {
        if (transportInfo == null || transportInfo.trim().isEmpty()) {
            return new String[]{"", ""};
        }
        // 首先检查是否有斜杠
        int slashIndex = transportInfo.lastIndexOf('/');
        if (slashIndex != -1) {
            String vesselName = transportInfo.substring(0, slashIndex).trim();
            String voyageNumber = transportInfo.substring(slashIndex + 1).trim();
            return new String[]{vesselName, voyageNumber};
        }
        // 然后检查空格
        int lastSpaceIndex = transportInfo.lastIndexOf(' ');
        if (lastSpaceIndex != -1) {
            String vesselName = transportInfo.substring(0, lastSpaceIndex).trim();
            String voyageNumber = transportInfo.substring(lastSpaceIndex + 1).trim();
            return new String[]{vesselName, voyageNumber};
        }
        return new String[]{transportInfo, ""};
    }

    /**
     * 将数量和单位拆分并返回字符串数组
     *
     * @param quantityAndUnit 包含数量和单位的字符串
     * @return 字符串数组，索引0是数量，索引1是单位，解析失败则返回null
     */
    public static String[] extractQuantityAndUnitAsArray(String quantityAndUnit) {
        Pattern pattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*(.*)");
        Matcher matcher = pattern.matcher(quantityAndUnit);
        if (matcher.find()) {
            String quantity = matcher.group(1);
            String unit = matcher.group(2).trim();
            return new String[]{quantity, unit};
        } else {
            return new String[]{"", ""};
        }
    }

    /**
     * 提取币制和价格信息，并以竖线分隔返回
     *
     * @param priceInfo 包含币制和价格的字符串
     * @return 格式为"币制|价格"的字符串，解析失败则返回null
     */
    public static String extractPriceInfo(String priceInfo) {
        if (priceInfo == null || priceInfo.trim().isEmpty()) {
            return null;
        }
        Pattern pattern = Pattern.compile("([A-Z]{3,4})(\\d+(?:\\.\\d+)?)");
        Matcher matcher = pattern.matcher(priceInfo);
        if (matcher.find()) {
            String currency = matcher.group(1);
            String price = matcher.group(2);
            return currency + "|" + price;
        } else {
            return null;
        }
    }

    /**
     * 判断是否是数字
     *
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2025/4/27 09:31
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            new BigDecimal(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 处理申报要素
     *
     * @param jsonString JSON格式的字符串
     * @return 处理后的结果字符串
     */
    public static String processHsmodel(String jsonString, DecList decList) {
        jsonString = isNotBlank(jsonString) ? jsonString : "||";
        // 先判断是否为 "数字|数字|" 格式
        if (jsonString.matches("^\\d\\|\\d\\|.*$")) {
            return jsonString;
        }
        try {
            String[] parts = jsonString.split("\\|", -1);
            if (parts.length < 2) {
                log.info("申报要素字段数不足: {}", jsonString);
                return jsonString;
            }

            // 处理品牌类型 - 如果已经是数字就不替换
            if (!parts[0].matches("^\\d+$")) {
                parts[0] = (jsonString.contains("无品牌") || jsonString.contains("无中文品牌") || jsonString.contains("无外文品牌")) ? "0" : "1";
            }

            // 处理出口享惠 - 如果已经是数字就不替换
            if (!parts[1].matches("^\\d+$")) {
                if (parts[1].contains("不")) {
                    parts[1] = "0";
                } else {
                    String destCountry = decList != null ? decList.getDestinationCountry() : null;
                    parts[1] = (destCountry != null && isPreferentialTariffCountry(destCountry)) ? "1" : "0";
                }
            }

            return String.join("|", parts);
        } catch (Exception e) {
            log.error("处理JSON出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * AI制单
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @param priceReference
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/14 15:58
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> aiMaker(MultipartFile[] files, String ieFlag, String customerName, String declarePlace,
                             String outPortCode, String shipTypeCode, String modelProvider, String priceReference, String decIdParam) {
        if (files == null || files.length == 0) {
            return Result.error("请上传文件");
        }
//        long startTime = System.currentTimeMillis();
        log.info("AI制单开始处理，客户名称：{}，进出口标识：{}", customerName, ieFlag);
        String decId;
        // 如果是重新制单
        boolean isReMaker = isNotBlank(decIdParam);
        if (isReMaker) {
            decId = decIdParam;
            DecHead decHead = decHeadService.getById(decIdParam);
            if (isEmpty(decHead)) {
                return Result.error("未找到对应的报关单");
            }
            // 设置忽略租户插件
            InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
            aiLogService.remove(new LambdaQueryWrapper<AiLog>()
                    .like(AiLog::getSourceId, decId));
            decHeadService.update(null, new LambdaUpdateWrapper<DecHead>()
                    .set(DecHead::getStatus, 1)
                    .eq(DecHead::getId, decId));
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
            // 保存日志先
            AiLog logDTO = new AiLog();
            logDTO.setLogType(LOG_TYPE_521);
            logDTO.setOperateType(OPERATE_TYPE_2);
            logDTO.setUserid("AI");
            logDTO.setRequestType("1");
            logDTO.setRequestParam("AI制单中");
            logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
            logDTO.setSourceId(decId);
            aiLogService.addLog(logDTO);
            // 2025/4/30 13:12@ZHANGCHAO 追加/变更/完善：再保存下企业单量
            // 获取当前登录用户
            LoginUser sysUser;
            try {
                sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            } catch (Exception e) {
                sysUser = new LoginUser();
                sysUser.setUsername("AI");
            }
            AiGenRecord aiGenRecord = new AiGenRecord();
            aiGenRecord.setType("1"); // 报关单
            aiGenRecord.setRelId(decId);
            aiGenRecord.setOptUnitName(customerName);
            aiGenRecord.setTenantId(isNotBlank(TenantContext.getTenant()) ? Long.parseLong(TenantContext.getTenant()) : 0L);
            aiGenRecord.setCreateBy(sysUser.getUsername());
            aiGenRecord.setCreateDate(new Date());
            aiGenRecord.setIsReprint(true);
            aiGenRecordMapper.insert(aiGenRecord);
        } else {
            // 1. 先生成一票基本的报关单
            DecHead decHead = new DecHead();
            decHead.setIeFlag(ieFlag);
            decHead.setOptUnitName(customerName);
            decHead.setDeclarePlace(declarePlace);
            decHead.setOutPortCode(outPortCode);
            decHead.setShipTypeCode(shipTypeCode);
            decHead.setClearanceType("M");
            decHead.setMarkNo("N/M");
            decHead.setStatus(1);
            decHead.setIsAi(true);
            decHead.setPriceReference(priceReference);
            decId = decHeadService.saveDecHead(decHead);
            if (isBlank(decId)) {
                return Result.error("AI制单失败");
            } else {
                // 保存日志先
                AiLog logDTO = new AiLog();
                logDTO.setLogType(LOG_TYPE_521);
                logDTO.setOperateType(OPERATE_TYPE_2);
                logDTO.setUserid("AI");
                logDTO.setRequestType("1");
                logDTO.setRequestParam("AI制单中");
                logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO.setSourceId(decId);
                aiLogService.addLog(logDTO);
                // 2025/4/30 13:12@ZHANGCHAO 追加/变更/完善：再保存下企业单量
                // 获取当前登录用户
                LoginUser sysUser;
                try {
                    sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                } catch (Exception e) {
                    sysUser = new LoginUser();
                    sysUser.setUsername("AI");
                }
                AiGenRecord aiGenRecord = new AiGenRecord();
                aiGenRecord.setType("1"); // 报关单
                aiGenRecord.setRelId(decId);
                aiGenRecord.setOptUnitName(customerName);
                aiGenRecord.setTenantId(isNotBlank(TenantContext.getTenant()) ? Long.parseLong(TenantContext.getTenant()) : 0L);
                aiGenRecord.setCreateBy(sysUser.getUsername());
                aiGenRecord.setCreateDate(new Date());
                aiGenRecordMapper.insert(aiGenRecord);
            }
        }


        List<Path> tempFiles = new ArrayList<>();
        try {
            for (MultipartFile file : files) {
                Path tempFile = saveToTempFile(file);
                tempFiles.add(tempFile);
            }
        } catch (IOException e) {
            log.error("保存临时文件失败", e);
            return Result.error("文件处理失败");
        }
        // 获取当前线程的租户ID
        String currentTenantId = TenantContext.getTenant();
        log.info("此时的租户Id是：{}", currentTenantId);
        CompletableFuture.runAsync(() -> {
            try {
                // 在异步线程中设置租户ID
                TenantContext.setTenant(currentTenantId);
                log.info("异步线程中的租户Id是：{}", TenantContext.getTenant());
                processAiModelAsync(tempFiles, modelProvider, decId, ieFlag);
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("AI异步处理异常", e);
            } finally {
                // 清理租户ID
                TenantContext.clear();
                deleteTempFiles(tempFiles);
            }
        }, Executors.newFixedThreadPool(10));

        return Result.ok("提交AI制单成功！请稍后刷新查看数据！");
    }

    /**
     * 智能填写
     *
     * @param hscode
     * @param text
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/22 12:24
     */
    @Override
    public Result<?> smartFill(String hscode, String text) {
        ErpHscodeData erpHscodeData = erpHscodeDataMapper.selectOne(new LambdaQueryWrapper<ErpHscodeData>()
                .eq(ErpHscodeData::getHscode, hscode)
                .last("limit 1"));
        String sbys = "";
        String sbysRequired = "";
        if (isNotEmpty(erpHscodeData)) {
            sbys = erpHscodeData.getSbys();
        }
        if (isNotEmpty(erpHscodeData)) {
            sbysRequired = erpHscodeData.getSbysRequired();
        }
        String pythonServiceUrl = aiBaseUrl + "/api/smart-fill";
        HttpRequest request = HttpUtil.createPost(pythonServiceUrl);
        request.header("Content-Type", "application/json");
        // 2025/4/24 16:43@ZHANGCHAO 追加/变更/完善：加鉴权了！！
        request.header("timestamp", getHeadersForAi().get("timestamp"));
        request.header("sign", getHeadersForAi().get("sign"));
        JSONObject paramJson = new JSONObject();
        paramJson.put("text", text);
        paramJson.put("split_rules", sbys);
        paramJson.put("sbysRequired", sbysRequired);
        request.body(paramJson.toString());
        int readTimeout = 180000;
        request.timeout(readTimeout);
        HttpResponse response = request.execute();
        String responseContent = response.body();
        log.info("[申报要素智能填写]服务调用结果：{}", responseContent);
        return Result.ok(isNotBlank(responseContent) ? JSONObject.parseObject(responseContent) : null);
    }

    /**
     * AI制单 -- 对外
     *
     * @param files
     * @param ieFlag
     * @param customerName
     * @param declarePlace
     * @param outPortCode
     * @param shipTypeCode
     * @param modelProvider
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/6/9 15:58
     */
//    @Transactional(rollbackFor = Exception.class)  // 移除顶层的事务，不让spring开启一个长事务。
    @Override
    public Result<?> aiMakerOut(MultipartFile[] files, String ieFlag, String customerName, String declarePlace,
                                String outPortCode, String shipTypeCode, String modelProvider, String clientId, String decIdParam) {
        if (files == null || files.length == 0) {
            return Result.error("请上传文件");
        }
        if (isBlank(clientId)) {
            return Result.error("请传入clientId");
        }
        LoginUser sysUser = commonMapper.getUserByClientId(clientId);
        if (isEmpty(sysUser)) {
            return Result.error("未知的clientId");
        }
        // 检查企业余额
        Result<?> balanceCheckResult = checkBalanceBeforeProcess(clientId);
        if (!balanceCheckResult.isSuccess()) {
            return balanceCheckResult;
        }

        List<Path> tempFiles = new ArrayList<>();
        try {
            for (MultipartFile file : files) {
                Path tempFile = saveToTempFile(file);
                tempFiles.add(tempFile);
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("保存临时文件失败", e);
            return Result.error("处理失败，请检查您的文档");
        }
        log.info("AI制单开始处理...>>>>>>");

        DecHead decHead = createInitialDecHead(ieFlag, customerName, declarePlace, outPortCode, shipTypeCode, sysUser, decIdParam);
        String decId;
        try {
            decId = self.createInitialRecordsInTransaction(decHead, customerName, sysUser, decIdParam);
            // 保存日志先
            AiLog logDTO = new AiLog();
            logDTO.setLogType(LOG_TYPE_521);
            logDTO.setOperateType(OPERATE_TYPE_2);
            logDTO.setUserid("AI");
            logDTO.setRequestType("1");
            logDTO.setRequestParam("AI制单中");
            logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
            logDTO.setSourceId(decId);
            aiLogService.addLog(logDTO);
        } catch (Exception e) {
            log.error("创建初始报关单记录失败", e);
            return Result.error("创建初始任务失败，请重试");
        }

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        try {
            String pythonServiceUrl = aiBaseUrl + "/upload";
            HttpRequest request = HttpUtil.createPost(pythonServiceUrl);
            request.form("ie_flag", ieFlag);
            request.form("model_provider", StrUtil.blankToDefault(modelProvider, "bailian"));
            for (Path tempFile : tempFiles) {
                request.form("files", tempFile.toFile());
            }
            request.header("timestamp", getHeadersForAi().get("timestamp"));
            request.header("sign", getHeadersForAi().get("sign"));
            // 设置24小时超时时间
            int readTimeout = 86400000;
            request.timeout(readTimeout);
            HttpResponse response = request.execute();
            int status = response.getStatus();
            String responseContent = response.body();
            log.info("Python服务调用结果，状态码：{}，响应内容：{}", status, responseContent);
            if (status == 200) {
                // 保存日志
                AiLog logDTO = new AiLog();
                logDTO.setLogType(LOG_TYPE_521);
                logDTO.setOperateType(OPERATE_TYPE_2);
                logDTO.setUserid("AI");
                logDTO.setRequestUrl(pythonServiceUrl);
                logDTO.setRequestType("2");
                logDTO.setRequestParam("数据清洗处理中");
                logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO.setLogContent(responseContent);
                logDTO.setSourceId(decId);
                aiLogService.addLog(logDTO);

                log.info("Python服务调用成功，开始处理响应数据");
                JSONObject jsonResult = JSONObject.parseObject(responseContent);
                JSONArray detailOcr = jsonResult.getJSONArray("detail_ocr");
                JSONArray detailFormat = jsonResult.getJSONArray("detail_format");
                JSONObject result = jsonResult.getJSONObject("result");
                if (isEmpty(result) || isEmpty(result.getJSONObject("content"))) {
                    log.error("Python服务调用成功，但响应数据为空");
                    return Result.error("AI制单失败，大模型调用成功，但响应数据为空");
                }
                // 设置租户ID
                TenantContext.setTenant(sysUser.getTenantId());
                // 回填数据到报关单中
                updateDecHeadWithAiResult(decId, clientId, detailOcr, detailFormat, result);

                long processTime = System.currentTimeMillis() - startTime;
                log.info("AI制单处理完成，耗时：{}ms", processTime);
                // 保存日志
                AiLog logDTO3 = new AiLog();
                logDTO3.setLogType(LOG_TYPE_521);
                logDTO3.setOperateType(OPERATE_TYPE_2);
                logDTO3.setUserid("AI");
                logDTO3.setRequestType("3");
                logDTO3.setRequestParam("待逻辑复审");
                logDTO3.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO3.setSourceId(decId);
                aiLogService.addLog(logDTO3);
            } else {
                log.error("Python服务调用失败，状态码：{}，响应内容：{}", status, responseContent);
                // 保存日志
                AiLog logDTO3 = new AiLog();
                logDTO3.setLogType(LOG_TYPE_521);
                logDTO3.setOperateType(OPERATE_TYPE_2);
                logDTO3.setUserid("AI");
                logDTO3.setRequestType("-1");
                logDTO3.setRequestParam("大模型调用失败");
                logDTO3.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO3.setLogContent(responseContent);
                logDTO3.setSourceId(decId);
                aiLogService.addLog(logDTO3);
                decHeadMapper.update(null, new LambdaUpdateWrapper<DecHead>()
                        .set(DecHead::getStatus, -1)
                        .eq(DecHead::getId, decId));
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("AI制单处理异常", e);
        } finally {
            // 清理租户ID
            TenantContext.clear();
        }
        log.info("-----------------AI制单处理完成，准备返回结果-----------------");
        DecHead returnDecHead = decHeadService.getDecHeadById(decId);
        log.info("AI制单处理完成，返回结果：{}", returnDecHead);
        return Result.OK(returnDecHead);
    }

    /**
     * 辅助方法，用于创建DecHead对象
     */
    private DecHead createInitialDecHead(String ieFlag, String customerName, String declarePlace, String outPortCode,
                                         String shipTypeCode, LoginUser sysUser, String decIdParam) {
        DecHead decHead = null;
        if (isNotBlank(decIdParam)) {
            decHead = decHeadMapper.selectById(decIdParam);
        } else {
            decHead = new DecHead();
            decHead.setTenantId(Long.valueOf(sysUser.getTenantId()));
            decHead.setIeFlag(ieFlag);
            decHead.setOptUnitName(customerName);
            decHead.setDeclarePlace(declarePlace);
            decHead.setOutPortCode(outPortCode);
            decHead.setShipTypeCode(shipTypeCode);
            decHead.setClearanceType("M");
            decHead.setMarkNo("N/M");
            decHead.setStatus(1); // 1 代表 "处理中"
            decHead.setIsAi(true);
            decHead.setIsAiOut(true);

            decHead.setId(IdWorker.getIdStr());
//            decHead.setSeqNo("BD"+ decHead.getId());
            decHead.setTenantId(isNotEmpty(decHead.getTenantId()) ? decHead.getTenantId() : Long.valueOf(TenantContext.getTenant()));
            decHead.setDclTenantId(String.valueOf(decHead.getTenantId()));
            decHead.setCustomsCode("D" + decHead.getId());
            decHead.setCreatePerson(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            decHead.setCreateTime(new Date());
        }
        return decHead;
    }

    /**
     * [事务方法] 创建初始记录。
     * 这个方法在一个独立的短事务中执行。
     */
    @Transactional(rollbackFor = Exception.class)
    public String createInitialRecordsInTransaction(DecHead decHead, String customerName, LoginUser sysUser, String decIdParam) {
        String decId = decIdParam;
        if (isNotBlank(decIdParam)) {
            AiGenRecord aiGenRecord = new AiGenRecord();
            aiGenRecord.setType("1"); // 报关单
            aiGenRecord.setRelId(decId);
            aiGenRecord.setOptUnitName(customerName);
            aiGenRecord.setTenantId(isNotBlank(sysUser.getTenantId()) ? Long.parseLong(sysUser.getTenantId()) : 0L);
            aiGenRecord.setCreateBy(sysUser.getUsername());
            aiGenRecord.setCreateDate(new Date());
            aiGenRecord.setIsReprint(true);
            aiGenRecordMapper.insert(aiGenRecord);
        } else {
            decHeadMapper.insert(decHead);
            decId = decHead.getId();
            AiGenRecord aiGenRecord = new AiGenRecord();
            aiGenRecord.setType("1"); // 报关单
            aiGenRecord.setRelId(decId);
            aiGenRecord.setOptUnitName(customerName);
            aiGenRecord.setTenantId(Long.valueOf(sysUser.getTenantId()));
            aiGenRecord.setCreateBy(sysUser.getUsername());
            aiGenRecord.setCreateDate(new Date());
            aiGenRecord.setIsOut(true); // 外部单子
            aiGenRecordMapper.insert(aiGenRecord);
        }
        return decId;
    }

    /**
     * AI制单导出发票
     *
     * @param id
     * @param request
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2025/6/20 11:50
     */
    @Override
    public void exportInvoice(String id, HttpServletRequest request, HttpServletResponse response) {
        // 返回前端提示
        if(isBlank(id)){
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"参数[报关单Id]不能为空\"}");
                return;
            } catch (Exception e) {
                log.error("返回错误信息失败", e);
                return;
            }
        }

        try {
            // 1. 根据DecHead ID查询报关单信息
            DecHead decHead = decHeadService.getById(id);
            if (decHead == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"报关单不存在\"}");
                return;
            }
            AiLog logDTO = commonMapper.getLogContent521(id);
            if (logDTO == null || isBlank(logDTO.getLogContent())) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"大模型未返回发票数据，无法导出\"}");
                return;
            }
            String logContent = logDTO.getLogContent();
            // TODO ..
            // ========== 开始解析和提取发票数据 ==========
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> rootMap = objectMapper.readValue(logContent, new TypeReference<Map<String, Object>>() {});
            List<Map<String, Object>> detailFormatList = (List<Map<String, Object>>) rootMap.get("detail_format");
            List<Map<String, Object>> invoiceDataList = new ArrayList<>();
            List<Map<String, Object>> packingListDataList = new ArrayList<>();
            if (detailFormatList != null) {
                for (Map<String, Object> document : detailFormatList) {
                    Map<String, Object> content = (Map<String, Object>) document.get("content");
                    if (content != null) {
                        String docType = (String) content.get("单据类型");
                        // 判断是否为 "出口发票" 或 "进口发票"
                        if ("出口发票".equals(docType) || "进口发票".equals(docType)) {
                            invoiceDataList.add(document);
                        }
                        // 提取箱单数据
                        if ("出口箱单".equals(docType) || "进口箱单".equals(docType)) {
                            packingListDataList.add(document);
                        }
                    }
                }
            }
            if (invoiceDataList.isEmpty()) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"大模型返回的数据中未找到发票信息，无法导出\"}");
                return;
            }
            log.info("【exportInvoice】大模型返回的数据中有{}个发票数据：{}", invoiceDataList.size(), invoiceDataList);
            // ======================= 开始数据映射到ExportInvoiceExcel =======================
            // 创建物料号到重量的映射关系
            Map<String, Map<String, String>> materialWeightMap = new HashMap<>();
            if (isNotEmpty(packingListDataList)) {
                for (Map<String, Object> packingDocument : packingListDataList) {
                    Map<String, Object> content = (Map<String, Object>) packingDocument.get("content");
                    if (content != null) {
                        List<Map<String, Object>> goodsList = (List<Map<String, Object>>) content.get("商品信息");
                        if (goodsList != null) {
                            for (Map<String, Object> goods : goodsList) {
                                String materialNo = (String) goods.get("物料号");
                                String netWeight = (String) goods.get("净重");
                                String grossWeight = (String) goods.get("毛重");

                                Map<String, String> weightInfo = new HashMap<>();
                                weightInfo.put("净重", netWeight);
                                weightInfo.put("毛重", grossWeight);
                                materialWeightMap.put(materialNo, weightInfo);
                            }
                        }
                    }
                }
            }
            // 最终要生成的Excel数据列表
            List<ExportInvoiceExcel> exportList = new ArrayList<>();
            // 正则表达式用于拆分 "数量及单位" (例如 "3720件" -> "3720", "件") 支持整数和浮点数
            java.util.regex.Pattern qtyPattern = java.util.regex.Pattern.compile("([\\d.]+)(.*)");
            for (Map<String, Object> invoiceDocument : invoiceDataList) {
                Map<String, Object> content = (Map<String, Object>) invoiceDocument.get("content");
                if (content == null) {
                    continue;
                }
                String invoiceNo = (String) content.get("发票号");
                String invoiceDate = (String) content.get("发票日期");
                String billOfLadingNo = (String) content.get("提运单号");
                String docType = (String) content.get("单据类型");
                List<Map<String, Object>> goodsList = (List<Map<String, Object>>) content.get("商品信息");
                if (goodsList == null || goodsList.isEmpty()) {
                    continue;
                }
                for (Map<String, Object> goodsItem : goodsList) {
                    ExportInvoiceExcel excelRow = new ExportInvoiceExcel();
                    excelRow.setGoodsNo(invoiceNo); // 发票号
                    excelRow.setGoodsName(invoiceDate); // 日期
                    excelRow.setWaybillNo(billOfLadingNo); // 装运号 (提运单号)
                    excelRow.setCountryOfOrigin((String) goodsItem.get("原产国")); // 原产国
                    excelRow.setType(docType); // 类型 (单据类型)
                    excelRow.setCopGno((String) goodsItem.get("物料号")); // 物料号
                    excelRow.setPn((String) goodsItem.get("商品名称")); // 品名
                    excelRow.setAmount(String.valueOf(goodsItem.get("总价"))); // 金额
                    excelRow.setCurrency((String) goodsItem.get("币制")); // 币种
                    // 拆分 "数量及单位"
                    String qtyAndUnit = (String) goodsItem.get("数量及单位");
                    if (qtyAndUnit != null && !qtyAndUnit.isEmpty()) {
                        java.util.regex.Matcher matcher = qtyPattern.matcher(qtyAndUnit.trim());
                        if (matcher.matches()) {
                            excelRow.setQty(matcher.group(1));  // 数量
                            excelRow.setQunit(matcher.group(2).trim()); // 单位
                        } else {
                            excelRow.setQty(qtyAndUnit);
                        }
                    }
                    // 处理重量
                    String materialNo = (String) goodsItem.get("物料号");
                    // 从箱单数据中获取重量信息
                    String weight = "";
                    if (materialWeightMap.containsKey(materialNo)) {
                        Map<String, String> weightInfo = materialWeightMap.get(materialNo);
                        String netWeight = weightInfo.get("净重");
                        String grossWeight = weightInfo.get("毛重");
                        // 优先使用净重，如果没有则使用毛重
                        if (netWeight != null && !netWeight.trim().isEmpty()) {
                            weight = netWeight;
                        } else if (grossWeight != null && !grossWeight.trim().isEmpty()) {
                            weight = grossWeight;
                        }
                        // 清理重量数据格式（去除单位等）
                        if (!weight.isEmpty()) {
                            weight = weight.replaceAll("[^\\d.]", ""); // 只保留数字和小数点
                        }
                    }
                    excelRow.setWeight(weight);
                    exportList.add(excelRow);
                }
            }

            //获取模板文件路径
//            Workbook templateWorkbook = WorkbookFactory.create(this.getClass().getResourceAsStream("/templates/xls/AI_marker_invoice.xlsx"));
            org.jeecgframework.poi.excel.entity.ExportParams exportParams = new org.jeecgframework.poi.excel.entity.ExportParams();
            exportParams.setSheetName("导出发票");
//            exportParams.setType(org.jeecgframework.poi.excel.entity.enmus.ExcelType.XSSF);
//            exportParams.setAddIndex(true);


            // 5. 导出Excel
            String fileName = "发票明细_" + (decHead.getClearanceNo() != null ? decHead.getClearanceNo() : "未知") + "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

            // 导出Excel
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ExportInvoiceExcel.class, exportList);
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出发票失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出发票失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("返回错误信息失败", ex);
            }
        }
    }

    /**
     * 异步处理AI模型调用
     *
     * @param tempFiles
     * @param modelProvider
     * @param decId
     * @param ieFlag
     * @return void
     * <AUTHOR>
     * @date 2025/4/27 09:34
     */
    private void processAiModelAsync(List<Path> tempFiles, String modelProvider, String decId, String ieFlag) {
        log.info("此时的租户Id是：{}", TenantContext.getTenant());
        long startTime = System.currentTimeMillis();
        try {
//            String pythonServiceUrl = "http://10.10.70.110:8000/upload";
            String pythonServiceUrl = aiBaseUrl + "/upload";
            log.info("请求地址是: {}", pythonServiceUrl);
            HttpRequest request = HttpUtil.createPost(pythonServiceUrl);
            request.form("ie_flag", ieFlag);
            request.form("model_provider", StrUtil.blankToDefault(modelProvider, "bailian"));
            for (Path tempFile : tempFiles) {
                request.form("files", tempFile.toFile());
            }
            // 2025/4/24 16:43@ZHANGCHAO 追加/变更/完善：加鉴权了！！
            request.header("timestamp", getHeadersForAi().get("timestamp"));
            request.header("sign", getHeadersForAi().get("sign"));
            // 设置24小时超时时间
            int readTimeout = 86400000;
            request.timeout(readTimeout);
            HttpResponse response = request.execute();
            int status = response.getStatus();
            String responseContent = response.body();
            log.info("Python服务调用结果，状态码：{}，响应内容：{}", status, responseContent);
            if (status == 200) {
                // 保存日志
                AiLog logDTO = new AiLog();
                logDTO.setLogType(LOG_TYPE_521);
                logDTO.setOperateType(OPERATE_TYPE_2);
                logDTO.setUserid("AI");
                logDTO.setRequestUrl(pythonServiceUrl);
                logDTO.setRequestType("2");
                logDTO.setRequestParam("数据清洗处理中");
                logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO.setLogContent(responseContent);
                logDTO.setSourceId(decId);
                aiLogService.addLog(logDTO);

                log.info("Python服务调用成功，开始处理响应数据");
                JSONObject jsonResult = JSONObject.parseObject(responseContent);
                JSONArray detailOcr = jsonResult.getJSONArray("detail_ocr");
                JSONArray detailFormat = jsonResult.getJSONArray("detail_format");
                JSONObject result = jsonResult.getJSONObject("result");
                if (isEmpty(result) || isEmpty(result.getJSONObject("content"))) {
                    log.error("Python服务调用成功，但响应数据为空");
                    return;
                }
                JSONObject content = result.getJSONObject("content");
                JSONArray validationResults = content.getJSONArray("验证结果");
                if (validationResults == null || validationResults.isEmpty()) {
                    log.info("数据验证通过，没有发现任何错误。");
                } else {
                    // 验证失败，遍历并打印详细错误
                    log.info("数据验证失败，详情如下：");
                    try {
                        for (int i = 0; i < validationResults.size(); i++) {
                            JSONObject errorItem = validationResults.getJSONObject(i);
                            String materialNo = errorItem.getString("物料号");
                            String productName = errorItem.getString("商品名称");
                            JSONArray errors = errorItem.getJSONArray("错误");
                            log.info("物料 [{} - {}] 存在问题：", materialNo, productName);
                            // 遍历具体的错误描述
                            for (Object errorMsg : errors) {
                                log.info("  - {}", errorMsg.toString());
                            }
                        }
                    } catch (Exception e) {
                        log.error("数据验证失败，请检查数据格式是否正确！", e);
                    }
                }

                // 回填数据到报关单中
                updateDecHeadWithAiResult(decId, null, detailOcr, detailFormat, result);

                // 2025/6/25 17:32@ZHANGCHAO 追加/变更/完善：获取单证校验结果
                JSONArray dzValidationResults = null;
                try {
                    dzValidationResults = content.getJSONArray("单证校验");
                    if (dzValidationResults != null && !dzValidationResults.isEmpty()) {
                        log.info("单证校验结果：{}", dzValidationResults);
                        // 获取当前登录用户
                        LoginUser sysUser;
                        try {
                            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                        } catch (Exception e) {
                            sysUser = new LoginUser();
                            sysUser.setUsername("AI");
                        }
                        aiRelTableMapper.delete(new LambdaQueryWrapper<AiRelTable>()
                                .eq(AiRelTable::getType, "4")
                                .eq(AiRelTable::getDecId, decId));
                        AiRelTable aiRelTable;
                        aiRelTable = new AiRelTable();
                        aiRelTable.setType("4");
                        aiRelTable.setCheckContent(dzValidationResults.isEmpty() ? null : JSONObject.toJSONString(dzValidationResults));
                        aiRelTable.setDecId(decId);
                        aiRelTable.setCreateBy(sysUser.getUsername());
                        aiRelTable.setCreateDate(new Date());
                        aiRelTableMapper.insert(aiRelTable);
                    }
                } catch (Exception e) {
                    log.error("单证校验结果解析异常：{}", e.getMessage());
                }
                // 保存日志
                AiLog logDTO4 = new AiLog();
                logDTO4.setLogType(LOG_TYPE_521);
                logDTO4.setOperateType(OPERATE_TYPE_2);
                logDTO4.setUserid("AI");
                logDTO4.setRequestType("4");
                logDTO4.setRequestParam("原始单证校验");
                logDTO4.setLogContent(dzValidationResults == null || dzValidationResults.isEmpty() ? null : JSONObject.toJSONString(dzValidationResults));
                logDTO4.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO4.setSourceId(decId);
                aiLogService.addLog(logDTO4);

                // 暂停1秒
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("线程暂停被中断", e);
                }
                long processTime = System.currentTimeMillis() - startTime;
                log.info("AI制单处理完成，耗时：{}ms", processTime);
                // 保存日志
                AiLog logDTO3 = new AiLog();
                logDTO3.setLogType(LOG_TYPE_521);
                logDTO3.setOperateType(OPERATE_TYPE_2);
                logDTO3.setUserid("AI");
                logDTO3.setRequestType("3");
                logDTO3.setRequestParam("待逻辑复审");
                logDTO3.setLogContent(validationResults == null || validationResults.isEmpty() ? null : JSONObject.toJSONString(validationResults));
                logDTO3.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO3.setSourceId(decId);
                aiLogService.addLog(logDTO3);

            } else {
                log.error("Python服务调用失败，状态码：{}，响应内容：{}", status, responseContent);
                // 保存日志
                AiLog logDTO3 = new AiLog();
                logDTO3.setLogType(LOG_TYPE_521);
                logDTO3.setOperateType(OPERATE_TYPE_2);
                logDTO3.setUserid("AI");
                logDTO3.setRequestType("-1");
                logDTO3.setRequestParam("大模型调用失败");
                logDTO3.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO3.setLogContent(responseContent);
                logDTO3.setSourceId(decId);
                aiLogService.addLog(logDTO3);
                decHeadMapper.update(null, new LambdaUpdateWrapper<DecHead>()
                        .set(DecHead::getStatus, -1)
                        .eq(DecHead::getId, decId));
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("AI制单处理异常", e);
            // 保存日志
            AiLog logDTO3 = new AiLog();
            logDTO3.setLogType(LOG_TYPE_521);
            logDTO3.setOperateType(OPERATE_TYPE_2);
            logDTO3.setUserid("AI");
            logDTO3.setRequestType("-1");
            logDTO3.setRequestParam("大模型调用失败");
            logDTO3.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
            logDTO3.setLogContent(ExceptionUtil.ResExHandle(e.toString()));
            logDTO3.setSourceId(decId);
            aiLogService.addLog(logDTO3);
            decHeadMapper.update(null, new LambdaUpdateWrapper<DecHead>()
                    .set(DecHead::getStatus, -1)
                    .eq(DecHead::getId, decId));
        }
    }

    /**
     * 使用AI结果更新报关单
     *
     * @param decId
     * @param detailOcr
     * @param detailFormat
     * @param result
     * @return void
     * <AUTHOR>
     * @date 2025/4/27 09:34
     */
    private void updateDecHeadWithAiResult(String decId, String clientId, JSONArray detailOcr, JSONArray detailFormat, JSONObject result) {
        // TODO: 实现具体的报关单更新逻辑
        // 根据decId获取报关单，然后使用AI返回的数据更新报关单字段
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        DecHead decHead = decHeadMapper.selectById(decId);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (decHead == null || isEmpty(result) || isEmpty(result.getJSONObject("content"))) {
            log.error("报关单[{}]不存在", decId);
            return;
        }
        try {
            Map<String, String> customsPortMap = getDictMap("erp_customs_ports,name,customs_port_code");
//            Map<String, String> transportMap = getDictMap("trans_type");
//            Map<String, String> chinaPortMap = getDictMap("erp_china_ports,name,china_port_code");
            Map<String, String> unitMap = getDictMap("erp_units,name,code");
            Map<String, String> unitItemKeyMap = getDictMap("erp_units,name,ITEM_KEY");
            Map<String, String> unitCodeMap = getDictMap("erp_units,ITEM_KEY,code");
            Map<String, String> tradeModeMap = getDictMap("trading_type");
//            Map<String, String> packageTypeMap = getDictMap("erp_packages_types,name,code,isenabled=1");
//            Map<String, String> zmxzMap = getDictMap("ZMXZ");
            Map<String, String> ZJMSFSMap = getDictMap("ZJMSFS");
//            Map<String, String> jgfsMap = getDictMap("JGFS");
//            Map<String, String> gkdmMap = getDictMap("GKDM");
            Map<String, String> countriesMap = getDictMap("erp_countries,name,code,isenabled=1");
            Map<String, String> districtCodeMap = getDictMap("erp_districts,name,code,del_Flag=0");
            Map<String, String> currMap = getDictMap("erp_currencies,name,currency");
            // 回填数据！！！
            JSONObject content = result.getJSONObject("content");
            String ieFlag = decHead.getIeFlag();
            String historyOptUnitName = decHead.getOptUnitName();
            // 境外收货人/境内发货人
            String optUnitName = I.equals(ieFlag) ? content.getString("境外收货人") : content.getString("境内发货人");
            // 消费使用单位/生产销售单位
            // 生产销售单位：同上境内收发货人，一般和境内收发货人一致，若不一致，单证上同时体现：出口代理人和卖方，则境内收发货人填制：出口代理人；将卖方填制到生产销售单位字段
            String productionSalesUnit = I.equals(ieFlag) ? content.getString("消费使用单位") : content.getString("生产销售单位");
            productionSalesUnit = isBlank(productionSalesUnit) ? optUnitName : productionSalesUnit;
            if (isBlank(productionSalesUnit) || !productionSalesUnit.equals(optUnitName)) {
                if (isNotBlank(content.getString("出口代理人"))) {
                    optUnitName = content.getString("出口代理人");
                }
                if (isNotBlank(content.getString("卖方"))) {
                    productionSalesUnit = content.getString("卖方");
                }
            }
            if (isNotBlank(optUnitName)) {
                try {
                    if (isNotChinese(optUnitName)) {
                        // 设置忽略租户插件
                        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                        List<AiRelTable> aiRelTables = aiRelTableMapper.selectList(new LambdaQueryWrapper<AiRelTable>()
                                .eq(AiRelTable::getType, "1")
                                .eq(AiRelTable::getEnName, optUnitName));
                        // 关闭忽略策略
                        InterceptorIgnoreHelper.clearIgnoreStrategy();
                        if (isNotEmpty(aiRelTables)) {
                            optUnitName = aiRelTables.get(0).getCnName();
                        }
                    }
                    CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, optUnitName);
                    if (isCheckEmpty(customerEnterprise)) {
                        if (optUnitName.contains("(") || optUnitName.contains(")")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, optUnitName.replace("(", "（").replace(")", "）"));
                        } else if (optUnitName.contains("（") || optUnitName.contains("）")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, optUnitName.replace("（", "(").replace("）", ")"));
                        }
                    }
                    decHead.setOptUnitName(isNotEmpty(customerEnterprise) && isNotBlank(customerEnterprise.getDepartName()) ? customerEnterprise.getDepartName() : optUnitName);
                    decHead.setOptUnitSocialCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null);
                    decHead.setTradeCiqCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getCiqCode() : null);
                    decHead.setOptUnitId(isNotEmpty(customerEnterprise) ? customerEnterprise.getDepartcd() : null);
                } catch (Exception e) {
                    log.error("根据企业名称{}获取备案信息出现异常：{}", optUnitName, e.getMessage());
                }
            }
            if (isBlank(decHead.getOptUnitName()) && isNotBlank(historyOptUnitName)) {
                try {
                    CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, historyOptUnitName);
                    if (isCheckEmpty(customerEnterprise)) {
                        if (historyOptUnitName.contains("(") || historyOptUnitName.contains(")")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, historyOptUnitName.replace("(", "（").replace(")", "）"));
                        } else if (historyOptUnitName.contains("（") || historyOptUnitName.contains("）")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, historyOptUnitName.replace("（", "(").replace("）", ")"));
                        }
                    }
                    decHead.setOptUnitName(isNotEmpty(customerEnterprise) && isNotBlank(customerEnterprise.getDepartName()) ? customerEnterprise.getDepartName() : historyOptUnitName);
                    decHead.setOptUnitSocialCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null);
                    decHead.setTradeCiqCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getCiqCode() : null);
                    decHead.setOptUnitId(isNotEmpty(customerEnterprise) ? customerEnterprise.getDepartcd() : null);
                } catch (Exception e) {
                    log.error("根据企业名称{}获取备案信息出现异常：{}", historyOptUnitName, e.getMessage());
                }
            }
            if (isNotBlank(productionSalesUnit)) {
                try {
                    if (isNotChinese(productionSalesUnit)) {
                        // 设置忽略租户插件
                        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                        List<AiRelTable> aiRelTables = aiRelTableMapper.selectList(new LambdaQueryWrapper<AiRelTable>()
                                .eq(AiRelTable::getType, "1")
                                .eq(AiRelTable::getEnName, productionSalesUnit));
                        // 关闭忽略策略
                        InterceptorIgnoreHelper.clearIgnoreStrategy();
                        if (isNotEmpty(aiRelTables)) {
                            productionSalesUnit = aiRelTables.get(0).getCnName();
                        }
                    }
                    CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, productionSalesUnit);
                    if (isCheckEmpty(customerEnterprise)) {
                        if (productionSalesUnit.contains("(") || productionSalesUnit.contains(")")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, productionSalesUnit.replace("(", "（").replace(")", "）"));
                        } else if (productionSalesUnit.contains("（") || productionSalesUnit.contains("）")) {
                            customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, productionSalesUnit.replace("（", "(").replace("）", ")"));
                        }
                    }
                    decHead.setDeliverUnitName(isNotEmpty(customerEnterprise) && isNotBlank(customerEnterprise.getDepartName()) ? customerEnterprise.getDepartName() : productionSalesUnit);
                    decHead.setDeliverUnitSocialCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null);
                    decHead.setOwnerCiqCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getCiqCode() : null);
                    decHead.setDeliverUnit(isNotEmpty(customerEnterprise) ? customerEnterprise.getDepartcd() : null);
                } catch (Exception e) {
                    log.error("根据企业名称{}获取备案信息出现异常：{}", productionSalesUnit, e.getMessage());
                }
            }
            // 境外发货人/境外收货人
            if (I.equals(ieFlag)) {
                String jwfhr = content.getString("境外发货人");
                decHead.setOverseasConsignorEname(jwfhr); // 境外发货人英文名称
                if (isNotBlank(jwfhr)) {
                    try {
                        CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwfhr);
                        if (isCheckEmpty(customerEnterprise)) {
                            if (jwfhr.contains("(") || jwfhr.contains(")")) {
                                customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwfhr.replace("(", "（").replace(")", "）"));
                            } else if (jwfhr.contains("（") || jwfhr.contains("）")) {
                                customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwfhr.replace("（", "(").replace("）", ")"));
                            }
                        }
                        decHead.setOverseasConsignorCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null); // 境外发货人代码
                    } catch (Exception e) {
                        log.error("根据企业名称{}获取备案信息出现异常：{}", jwfhr, e.getMessage());
                    }
                }
            } else {
                String jwshr = content.getString("境外收货人");
                decHead.setOverseasConsigneeEname(jwshr); // 境外收货人名称
                if (isNotBlank(jwshr)) {
                    try {
                        CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwshr);
                        if (isCheckEmpty(customerEnterprise)) {
                            if (jwshr.contains("(") || jwshr.contains(")")) {
                                customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwshr.replace("(", "（").replace(")", "）"));
                            } else if (jwshr.contains("（") || jwshr.contains("）")) {
                                customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, jwshr.replace("（", "(").replace("）", ")"));
                            }
                        }
                        decHead.setOverseasConsigneeCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null); // 境外收货人代码
                    } catch (Exception e) {
                        log.error("根据企业名称{}获取备案信息出现异常：{}", jwshr, e.getMessage());
                    }
                }
            }
            // 申报单位
            String declareUnitName = content.getString("申报单位");
            if (isBlank(declareUnitName)) {
                try {
                    LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                    declareUnitName = commonMapper.getCustomerTypeByUserId(isNotEmpty(sysUser) && isNotBlank(sysUser.getId()) ? sysUser.getId() : clientId);
                } catch (Exception e) {
                    log.error("获取当前登录用户信息出现异常：{}", e.getMessage());
                }
            }
            if (isNotBlank(declareUnitName)) {
                if (isNotChinese(declareUnitName)) {
                    // 设置忽略租户插件
                    InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                    List<AiRelTable> aiRelTables = aiRelTableMapper.selectList(new LambdaQueryWrapper<AiRelTable>()
                            .eq(AiRelTable::getType, "1")
                            .eq(AiRelTable::getEnName, declareUnitName));
                    // 关闭忽略策略
                    InterceptorIgnoreHelper.clearIgnoreStrategy();
                    if (isNotEmpty(aiRelTables)) {
                        declareUnitName = aiRelTables.get(0).getCnName();
                    }
                }
                CustomerEnterprise customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, declareUnitName);
                if (isCheckEmpty(customerEnterprise)) {
                    if (declareUnitName.contains("(") || declareUnitName.contains(")")) {
                        customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, declareUnitName.replace("(", "（").replace(")", "）"));
                    } else if (declareUnitName.contains("（") || declareUnitName.contains("）")) {
                        customerEnterprise = enterpriseInfoService.getCustomerEnterpriseByCond(null, null, null, declareUnitName.replace("（", "(").replace("）", ")"));
                    }
                }
                decHead.setDeclareUnitName(isNotEmpty(customerEnterprise) ? customerEnterprise.getDepartName() : declareUnitName);
                decHead.setDeclareUnitSocialCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getSocialCode() : null); // 申报单位社会统一信用代码
                decHead.setDeclareUnit(isNotEmpty(customerEnterprise) ? customerEnterprise.getDepartcd() : null); // 申报单位海关代码
                decHead.setDeclCiqCode(isNotEmpty(customerEnterprise) ? customerEnterprise.getCiqCode() : null); // 申报单位检验检疫编码
            }
            // 再走一遍，默认迅吉安
            if (isBlank(decHead.getDeclareUnitName())) {
                decHead.setDeclareUnitName("山东迅吉安国际物流有限公司");
                decHead.setDeclareUnitSocialCode("91370100560757589T");
                decHead.setDeclareUnit("3701983939");
                decHead.setDeclCiqCode("3700910305");
            }

            // 进出日期
            String inOutDate = I.equals(ieFlag) ? content.getString("进口日期") : content.getString("出口日期");
//            decHead.setOutDate(inOutDate);
            // 申报日期
//            decHead.setAppDate(isNotBlank(content.getString("申报日期")) ? DateUtil.parseDate(content.getString("申报日期")) : null);
            // 进境关别/出境关别
            String fileOutPortCode = I.equals(ieFlag) ? content.getString("进境关别") : content.getString("出境关别");
            if (isNotBlank(fileOutPortCode)) {
                if (customsPortMap.containsKey(fileOutPortCode)) {
                    decHead.setOutPortCode(customsPortMap.get(fileOutPortCode));
                } else if (isNumeric(fileOutPortCode)) {
                    decHead.setOutPortCode(fileOutPortCode);
                }
            }
            // 入境口岸/离境口岸
            decHead.setEntyPortCode(I.equals(ieFlag) ? content.getString("入境口岸代码") : content.getString("离境口岸代码"));
//            String entyPortCode = I.equals(ieFlag) ? content.getString("入境口岸") : content.getString("离境口岸");
//            if (isNotBlank(entyPortCode)) {
//                if (chinaPortMap.containsKey(entyPortCode)) {
//                    decHead.setEntyPortCode(chinaPortMap.get(entyPortCode));
//                } else if (isNumeric(entyPortCode)) {
//                    decHead.setEntyPortCode(entyPortCode);
//                }
//            }
            // 2025/4/17 10:28@ZHANGCHAO 追加/变更/完善：处理关别口岸对应关系！！
            if (isNotBlank(decHead.getOutPortCode()) && isBlank(decHead.getEntyPortCode())) {
                DictQuery dictQuery = commonMapper.getDictItemByKey("ENTRY_AND_PORT_DICT", decHead.getOutPortCode(), null);
                if (isNotEmpty(dictQuery)) {
                    decHead.setEntyPortCode(dictQuery.getText());
                }
            } else if (isBlank(decHead.getOutPortCode()) && isNotBlank(decHead.getEntyPortCode())) {
                DictQuery dictQuery = commonMapper.getDictItemByKey("ENTRY_AND_PORT_DICT", null, decHead.getEntyPortCode());
                if (isNotEmpty(dictQuery)) {
                    decHead.setOutPortCode(dictQuery.getCode());
                }
            }
            // 例如：QINGDAO,CHINA/青岛：青开发区4218，对应的离境口岸为黄岛，RIZHAO,日照为日照海关4202，对应的日照，北京-京机场关0101对应口岸首都国际机场。
            if (isNotBlank(fileOutPortCode)) {
                if (fileOutPortCode.toUpperCase().contains("QINGDAO,CHINA")) {
                    decHead.setOutPortCode("4218");
                    decHead.setEntyPortCode("370201");
                } else if (fileOutPortCode.toUpperCase().contains("RIZHAO") || fileOutPortCode.toUpperCase().contains("Ri Zhao")) {
                    decHead.setOutPortCode("4202");
                    decHead.setEntyPortCode("370002");
                } else if (fileOutPortCode.toUpperCase().contains("BEIJING")) {
                    decHead.setOutPortCode("0101");
                    decHead.setEntyPortCode("110101");
                }
            }
            // 运输方式
            decHead.setShipTypeCode(content.getString("运输方式代码"));
//            if (isNotBlank(content.getString("运输方式"))) {
//                if (transportMap.containsKey(content.getString("运输方式"))) {
//                    decHead.setShipTypeCode(transportMap.get(content.getString("运输方式")));
//                } else if (isNumeric(content.getString("运输方式"))) {
//                    decHead.setShipTypeCode(content.getString("运输方式"));
//                }
//            }
            // 2025/4/17 10:21@ZHANGCHAO 追加/变更/完善：处理申报地海关
            String declarePlace = content.getString("申报地海关");
            if (isNotBlank(declarePlace)) {
                if (isNumeric(declarePlace)) {
                    decHead.setDeclarePlace(declarePlace);
                } else {
                    if (customsPortMap.containsKey(declarePlace)) {
                        decHead.setDeclarePlace(customsPortMap.get(declarePlace));
                    }
                }
            }
            // 迅吉安的规则为海运基本为4301泉城海关，空运为4302，济机场关；
            if (isNotBlank(decHead.getDeclareUnitName()) && decHead.getDeclareUnitName().contains("迅吉安")) {
                if (isNotBlank(decHead.getShipTypeCode()) && isBlank(decHead.getDeclarePlace())) {
                    if ("2".equals(decHead.getShipTypeCode())) {
                        decHead.setDeclarePlace("4301");
                    } else if ("5".equals(decHead.getShipTypeCode())) {
                        decHead.setDeclarePlace("4302");
                    }
                }
            }
            // 提运单号
            decHead.setBillCode(content.getString("提运单号"));
            if (isNotBlank(decHead.getBillCode()) && "5".equals(decHead.getShipTypeCode())) {
//                decHead.setBillCode(decHead.getBillCode().replaceAll("\\s*", "").replace("-", "").replace("/", "_"));
                decHead.setBillCode(decHead.getBillCode().replace("-", "").replace("/", "_"));
            }
            // 许可证号
            decHead.setLicenceNumber(content.getString("许可证号"));
            // 运输工具及航次
            String cmhc = isNotBlank(content.getString("运输工具及航次")) ? content.getString("运输工具及航次") : content.getString("船名/航次");
            String[] vesselAndVoyage = splitTransportAndVoyage(cmhc);
            decHead.setShipName(vesselAndVoyage[0]);
            decHead.setVoyage(vesselAndVoyage[1]);
            // 2025/6/11 15:04@ZHANGCHAO 追加/变更/完善：从新字段里取值！！
            if (isNotBlank(content.getString("运输工具名称"))) {
                decHead.setShipName(content.getString("运输工具名称"));
            }
            if (isNotBlank(content.getString("航次号"))) {
                decHead.setVoyage(content.getString("航次号"));
            }
            if (isNotBlank(decHead.getShipName()) || isNotBlank(decHead.getVoyage())) {
                decHead.setShipTypeCode(isNotBlank(decHead.getShipTypeCode()) ? decHead.getShipTypeCode() : "2");
            }
            // 贸易国
            decHead.setTradeCountry(content.getString("贸易国代码"));
//            String tradeCountry = content.getString("贸易国");
//            if (isNotBlank(tradeCountry)) {
//                decHead.setTradeCountry(countriesMap.getOrDefault(tradeCountry, tradeCountry));
//            }
            // 启运国/运抵国
            decHead.setArrivalArea(I.equals(ieFlag) ? content.getString("启运国代码") : content.getString("运抵国代码"));
//            String arrivalArea = I.equals(ieFlag) ? content.getString("启运国") : content.getString("运抵国");
//            if (isNotBlank(arrivalArea)) {
//                decHead.setArrivalArea(countriesMap.getOrDefault(arrivalArea, arrivalArea));
//            }
            // 指运港
            decHead.setDesPort(isNotBlank(content.getString("指运港代码")) ? content.getString("指运港代码") : content.getString("经停港代码"));
//            String desPort = isNotBlank(content.getString("指运港")) ? content.getString("指运港") : content.getString("经停港");
//            if (isNotBlank(desPort)) {
//                if (gkdmMap.containsKey(desPort)) {
//                    decHead.setDesPort(gkdmMap.get(desPort));
//                }
//                // 还是空的？
//                if (isBlank(decHead.getDesPort())) {
//                    DictQuery dictQuery = commonMapper.getDictItemByKeyLike("GKDM", null, desPort);
//                    decHead.setDesPort(isEmpty(dictQuery) ? desPort : dictQuery.getCode());
//                }
//            }
            // 启运港
            decHead.setDespPortCode(content.getString("启运港代码"));
            // 件数
            String packs = isNotBlank(content.getString("总件数")) ? content.getString("总件数") : content.getString("总数量");
            if (isNotBlank(packs)) {
                try {
                    Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                    Matcher matcher = pattern.matcher(packs);
                    if (matcher.find()) {
                        // decHead.setPacks(Integer.parseInt(matcher.group()));
                        // 先转为 double，再转为 int
                        double value = Double.parseDouble(matcher.group());
                        decHead.setPacks((int) Math.round(value));
                    }
                } catch (Exception e) {
                    log.error("解析总件数出错：{}", e.getMessage());
                }
            }
            // 毛重
            String grossWeight = content.getString("总毛重");
            if (isNotBlank(grossWeight)) {
                try {
                    Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                    Matcher matcher = pattern.matcher(grossWeight);
                    if (matcher.find()) {
                        decHead.setGrossWeight(new BigDecimal(matcher.group()));
                    }
                } catch (Exception e) {
                    log.error("解析总毛重出错：{}", e.getMessage());
                }
            }
            // 净重
            String netWeightObj = content.getString("总净重");
            if (isNotBlank(netWeightObj)) {
                try {
                    Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                    Matcher matcher = pattern.matcher(netWeightObj);
                    if (matcher.find()) {
                        decHead.setNetWeight(new BigDecimal(matcher.group()));
                    }
                } catch (Exception e) {
                    log.error("解析总净重\"出错：{}", e.getMessage());
                }
            }
            // 成交方式
            String termsTypeCode = content.getString("成交方式");
//            if (isNotBlank(termsTypeCode) && tradeModeMap.containsKey(termsTypeCode)) {
//                decHead.setTermsTypeCode(tradeModeMap.get(termsTypeCode));
//            }
            // 2025/5/22 10:09@ZHANGCHAO 追加/变更/完善：完善成交方式
//            if (isBlank(decHead.getTermsTypeCode())) {
                termsTypeCode = isNotBlank(termsTypeCode) ? termsTypeCode.trim() : "";
                if ("CIF".equalsIgnoreCase(termsTypeCode) || "CIP".equalsIgnoreCase(termsTypeCode)
                        || "DAP".equalsIgnoreCase(termsTypeCode) || "DPU".equalsIgnoreCase(termsTypeCode)
                        || "DDP".equalsIgnoreCase(termsTypeCode) || "DAP DPU DDP".equalsIgnoreCase(termsTypeCode)) {
                    decHead.setTermsTypeCode("1");
                } else if ("C&F".equalsIgnoreCase(termsTypeCode) || "CPT".equalsIgnoreCase(termsTypeCode)
                        || "CFR".equalsIgnoreCase(termsTypeCode) || "CFR CPT".equalsIgnoreCase(termsTypeCode) || "CNF".equalsIgnoreCase(termsTypeCode)) {
                    decHead.setTermsTypeCode("2");
                } else if ("EXW".equalsIgnoreCase(termsTypeCode)) {
                    decHead.setTermsTypeCode("7");
                } else if ("FOB".equalsIgnoreCase(termsTypeCode)
                        || "FCA".equalsIgnoreCase(termsTypeCode)
                        || "FAS".equalsIgnoreCase(termsTypeCode) || "FCA FOB FAS".equalsIgnoreCase(termsTypeCode)) {
                    decHead.setTermsTypeCode("3");
                } else {
                    if (isNotBlank(termsTypeCode) && tradeModeMap.containsKey(termsTypeCode)) {
                        decHead.setTermsTypeCode(tradeModeMap.get(termsTypeCode));
                    } else {
                        // 如果是纯数字，则认为是成交方式代码
                        if (isNumeric(termsTypeCode)) {
                            decHead.setTermsTypeCode(termsTypeCode);
                        }
                    }
                }
//            }
            // 包装种类
            // 包装种类：取自箱单，CTN/CARTON-纸箱，PAL/PALLET-再生托盘等，若没有默认纸箱
            decHead.setPacksKinds(content.getString("包装种类代码"));
//            String packsKinds = content.getString("包装种类");
//            if (isNotBlank(packsKinds)) {
//                if (packageTypeMap.containsKey(packsKinds)) {
//                    decHead.setPacksKinds(packageTypeMap.get(packsKinds));
//                } else {
//                    ErpPackagesTypes erpPackagesTypes = commonMapper.getErpPackagesTypesByName(packsKinds.replace("其它", "其他"));
//                    if (isNotEmpty(erpPackagesTypes)) {
//                        decHead.setPacksKinds(erpPackagesTypes.getCode());
//                    }
//                    if  ("其它".equals(packsKinds) || "其他".equals(packsKinds)) {
//                        decHead.setPacksKinds("99"); // 其他包装
//                    }
//                }
//                if (isBlank(decHead.getPacksKinds())) {
//                    if (packsKinds.toLowerCase().contains("ctn")
//                            || packsKinds.toLowerCase().contains("carton")
//                            || packsKinds.toLowerCase().contains("ctns")
//                            || packsKinds.toLowerCase().contains("cartons")) {
//                        decHead.setPacksKinds("22");
//                    } else if (packsKinds.toLowerCase().contains("pal")) {
//                        decHead.setPacksKinds("92"); // 再生木托
//                    }
//                }
//                // 继续来一遍
//                if (isBlank(decHead.getPacksKinds()) && packsKinds.contains("箱")) {
//                    decHead.setPacksKinds("22"); // 纸制或纤维板制盒/箱
//                }
//            } else {
//                decHead.setPacksKinds("22"); // 纸制或纤维板制盒/箱
//            }
            // 征免性质
//            String freeNature = content.getString("征免性质");
//            if (isNotBlank(freeNature) && zmxzMap.containsKey(freeNature)) {
//                decHead.setTaxTypeCode(zmxzMap.get(freeNature));
//            }
            // 2025/7/1 09:55@ZHANGCHAO 追加/变更/完善：直接取代码
            decHead.setTaxTypeCode(content.getString("征免性质代码"));
            // 备案号
            String recordNumber = content.getString("备案号");
            decHead.setRecordNumber(isNotBlank(recordNumber) ? recordNumber : null);
            // 备注
            String markNumber = content.getString("备注");
            decHead.setMarkNumber(isNotBlank(markNumber) ? markNumber : null);
            // 发票号
            String invoiceNumber = content.getString("发票号");
            // 合同协议号
            String contract = isNotBlank(content.getString("合同号")) ? content.getString("合同号") : content.getString("合同协议号");
            contract = isNotBlank(contract) ? contract : invoiceNumber;
            String processedContract = contract;
            String remark = "";
            if (isNotBlank(contract) && contract.length() > 32) {
                remark = "完整合同号：" + contract;
                String[] contractArray = contract.split("[,，;；/|\\s]+");
                if (contractArray.length > 1) {
                    String firstContract = contractArray[0].trim();
                    processedContract = firstContract + "等";
                    if (processedContract.length() > 32) {
                        processedContract = processedContract.substring(0, 30) + "等";
                    }
                } else {
                    // 单个合同号超字符的情况
                    processedContract = contract.substring(0, 32);
                }
            }
            decHead.setContract(processedContract);
            if (isNotBlank(remark)) {
                String existingRemark = decHead.getMarkNumber(); // 获取现有备注
                String finalRemark = isNotBlank(existingRemark) ? existingRemark + "；" + remark : remark;
                decHead.setMarkNumber(finalRemark);
            }

            // 监管方式
            decHead.setTradeTypeCode(content.getString("监管方式代码"));
//            String supervisionMode = content.getString("监管方式");
//            if (isNotBlank(supervisionMode) && jgfsMap.containsKey(supervisionMode)) {
//                decHead.setTradeTypeCode(jgfsMap.get(supervisionMode));
//            }
//            // 2025/4/28 11:04@ZHANGCHAO 追加/变更/完善：特殊处理！
//            if (isNotBlank(decHead.getRecordNumber()) && isBlank(decHead.getTradeTypeCode())) {
//                // 手册号C开头的几乎都是进料对口
//                if (decHead.getRecordNumber().startsWith("C")) {
//                    decHead.setTradeTypeCode("0615");
//                }
//            }
            // 征免方式
            String faxTypeCode = "";
            if (isNotBlank(decHead.getTradeTypeCode())) {
                // 设置忽略租户插件
                InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                List<AiRelTable> aiRelTables = aiRelTableMapper.selectList(new LambdaQueryWrapper<AiRelTable>()
                        .eq(AiRelTable::getType, "2")
                        .eq(AiRelTable::getTradeTypeCode, decHead.getTradeTypeCode())
                        .eq(isNotBlank(decHead.getTaxTypeCode()), AiRelTable::getTaxTypeCode, decHead.getTaxTypeCode())
                        .orderByAsc(AiRelTable::getId));
                // 关闭忽略策略
                InterceptorIgnoreHelper.clearIgnoreStrategy();
                if (isNotEmpty(aiRelTables)) {
                    decHead.setTradeTypeCode(aiRelTables.get(0).getTradeTypeCode());
                    decHead.setTaxTypeCode(aiRelTables.get(0).getTaxTypeCode());
                    faxTypeCode = aiRelTables.get(0).getFaxTypeCode();
                }
            } else {
                // 备案号为空，一般贸易
                if (isBlank(decHead.getRecordNumber())) {
                    decHead.setTradeTypeCode("0110");
                    decHead.setTaxTypeCode(isNotBlank(decHead.getTaxTypeCode()) ? decHead.getTaxTypeCode() : "101");
                    faxTypeCode = "1";
                }
            }
            // 运费
            String fee = content.getString("运费");
            if (isNotBlank(fee)) {
                fee = fee.trim(); // 清理前后空格
                if (isNumeric(fee)) {
                    decHead.setShipFee(new BigDecimal(fee));
                } else if (fee.contains("/")) {
                    String[] parts = fee.split("/");
                    if (parts.length >= 2) {
                        String currency = parts[0].trim();
                        String feeStr = parts[1].trim();
                        try {
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setShipCurrencyCode(currency);  // 币制
                            decHead.setShipFee(feeVal);             // 运费值
                            if (parts.length >= 3) {
                                decHead.setShipFeeCode(parts[2].trim()); // 运费代码（可选）
                            }
                        } catch (NumberFormatException e) {
                            log.warn("运费格式错误，不能转换为数字: {}", fee);
                        }
                    } else {
                        log.warn("运费字段格式不正确: {}", fee);
                    }
                } else {
                    // 处理货币代码和金额连在一起的格式，如 "CNY35515.3392"
                    String currencyFirstPatternStr = "^([A-Z]{3})([0-9]+\\.?[0-9]*)$";
                    // 格式2: 金额在前，如 "200USD"
                    String amountFirstPatternStr = "^([0-9]+\\.?[0-9]*)([A-Z]{3})$";
                    Pattern currencyFirstPattern = Pattern.compile(currencyFirstPatternStr);
                    Matcher currencyFirstMatcher = currencyFirstPattern.matcher(fee);
                    Pattern amountFirstPattern = Pattern.compile(amountFirstPatternStr);
                    Matcher amountFirstMatcher = amountFirstPattern.matcher(fee);
                    if (currencyFirstMatcher.matches()) {
                        try {
                            String currency = currencyFirstMatcher.group(1);
                            String feeStr = currencyFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setShipCurrencyCode(currency);
                            decHead.setShipFee(feeVal);
                        } catch (Exception e) {
                            log.warn("运费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else if (amountFirstMatcher.matches()) {
                        try {
                            String feeStr = amountFirstMatcher.group(1);
                            String currency = amountFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setShipCurrencyCode(currency);
                            decHead.setShipFee(feeVal);
                        } catch (Exception e) {
                            log.warn("运费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else {
                        log.warn("无法解析的运费格式: {}", fee);
                    }
                }
            }
            // 运费币制
            String feeCurrencyCode = content.getString("运费币制");
            if (isNotBlank(feeCurrencyCode) && currMap.containsKey(feeCurrencyCode)) {
                decHead.setShipCurrencyCode(currMap.get(feeCurrencyCode));
            } else {
                decHead.setShipCurrencyCode(isNotBlank(decHead.getShipCurrencyCode()) ? decHead.getShipCurrencyCode() : feeCurrencyCode);
            }
            if (isNotEmpty(decHead.getShipFee())) {
                decHead.setShipFeeCode(isNotBlank(decHead.getShipFeeCode()) ? decHead.getShipFeeCode() : "3"); // 默认总价
            }

            // 保费
            String insurance = content.getString("保费");
            if (isNotBlank(insurance)) {
                insurance = insurance.trim(); // 清理前后空格
                if (isNumeric(insurance)) {
                    decHead.setInsurance(new BigDecimal(insurance));
                } else if (insurance.contains("/")) {
                    String[] parts = insurance.split("/");
                    if (parts.length >= 2) {
                        String currency = parts[0].trim();
                        String feeStr = parts[1].trim();
                        try {
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setInsuranceCurr(currency);  // 币制
                            decHead.setInsurance(feeVal);        // 保费值
                            if (parts.length >= 3) {
                                decHead.setInsuranceCode(parts[2].trim()); // 保费代码（可选）
                            }
                        } catch (NumberFormatException e) {
                            log.warn("保费格式错误，不能转换为数字: {}", insurance);
                        }
                    } else {
                        log.warn("保费字段格式不正确: {}", insurance);
                    }
                } else {
                    // 处理货币代码和金额连在一起的格式，如 "CNY35515.3392"
                    String currencyFirstPatternStr = "^([A-Z]{3})([0-9]+\\.?[0-9]*)$";
                    // 格式2: 金额在前，如 "200USD"
                    String amountFirstPatternStr = "^([0-9]+\\.?[0-9]*)([A-Z]{3})$";
                    Pattern currencyFirstPattern = Pattern.compile(currencyFirstPatternStr);
                    Matcher currencyFirstMatcher = currencyFirstPattern.matcher(fee);
                    Pattern amountFirstPattern = Pattern.compile(amountFirstPatternStr);
                    Matcher amountFirstMatcher = amountFirstPattern.matcher(fee);
                    if (currencyFirstMatcher.matches()) {
                        try {
                            String currency = currencyFirstMatcher.group(1);
                            String feeStr = currencyFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setInsuranceCurr(currency);
                            decHead.setInsurance(feeVal);
                        } catch (Exception e) {
                            log.warn("保费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else if (amountFirstMatcher.matches()) {
                        try {
                            String feeStr = amountFirstMatcher.group(1);
                            String currency = amountFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setInsuranceCurr(currency);
                            decHead.setInsurance(feeVal);
                        } catch (Exception e) {
                            log.warn("保费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else {
                        log.warn("无法解析的保费格式: {}", fee);
                    }
                }
            }
            // 保费币制
            String insuranceCurr = content.getString("保费币制");
            if (isNotBlank(insuranceCurr) && currMap.containsKey(insuranceCurr)) {
                decHead.setInsuranceCurr(currMap.get(insuranceCurr));
            } else {
                decHead.setInsuranceCurr(isNotBlank(decHead.getInsuranceCurr()) ? decHead.getInsuranceCurr() : insuranceCurr);
            }
            // 设置默认保费代码
            if (isNotEmpty(decHead.getInsurance())) {
                decHead.setInsuranceCode(isNotBlank(decHead.getInsuranceCode()) ? decHead.getInsuranceCode() : "3"); // 默认总价
            }

            // 杂费
            String extras = content.getString("杂费");
            if (isNotBlank(extras)) {
                extras = extras.trim(); // 清理前后空格
                if (isNumeric(extras)) {
                    decHead.setExtras(new BigDecimal(extras));
                } else if (extras.contains("/")) {
                    String[] parts = extras.split("/");
                    if (parts.length >= 2) {
                        String currency = parts[0].trim();
                        String feeStr = parts[1].trim();
                        try {
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setOtherCurr(currency);  // 币制
                            decHead.setExtras(feeVal);       // 杂费值
                            if (parts.length >= 3) {
                                decHead.setExtrasCode(parts[2].trim()); // 杂费代码（可选）
                            }
                        } catch (NumberFormatException e) {
                            log.warn("杂费格式错误，不能转换为数字: {}", extras);
                        }
                    } else {
                        log.warn("杂费字段格式不正确: {}", extras);
                    }
                } else {
                    // 处理货币代码和金额连在一起的格式，如 "CNY35515.3392"
                    String currencyFirstPatternStr = "^([A-Z]{3})([0-9]+\\.?[0-9]*)$";
                    // 格式2: 金额在前，如 "200USD"
                    String amountFirstPatternStr = "^([0-9]+\\.?[0-9]*)([A-Z]{3})$";
                    Pattern currencyFirstPattern = Pattern.compile(currencyFirstPatternStr);
                    Matcher currencyFirstMatcher = currencyFirstPattern.matcher(fee);
                    Pattern amountFirstPattern = Pattern.compile(amountFirstPatternStr);
                    Matcher amountFirstMatcher = amountFirstPattern.matcher(fee);
                    if (currencyFirstMatcher.matches()) {
                        try {
                            String currency = currencyFirstMatcher.group(1);
                            String feeStr = currencyFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setOtherCurr(currency);
                            decHead.setExtras(feeVal);
                        } catch (Exception e) {
                            log.warn("运费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else if (amountFirstMatcher.matches()) {
                        try {
                            String feeStr = amountFirstMatcher.group(1);
                            String currency = amountFirstMatcher.group(2);
                            BigDecimal feeVal = new BigDecimal(feeStr);
                            decHead.setOtherCurr(currency);
                            decHead.setExtras(feeVal);
                        } catch (Exception e) {
                            log.warn("运费格式错误，解析时发生异常: {}", fee, e);
                        }
                    } else {
                        log.warn("无法解析的运费格式: {}", fee);
                    }
                }
            }
            // 杂费币制
            String otherCurr = content.getString("杂费币制");
            if (isNotBlank(otherCurr) && currMap.containsKey(otherCurr)) {
                decHead.setOtherCurr(currMap.get(otherCurr));
            } else {
                decHead.setOtherCurr(isNotBlank(decHead.getOtherCurr()) ? decHead.getOtherCurr() : otherCurr);
            }
            // 设置默认杂费代码
            if (isNotEmpty(decHead.getExtras())) {
                decHead.setExtrasCode(isNotBlank(decHead.getExtrasCode()) ? decHead.getExtrasCode() : "3"); // 默认总价
            }

            // 2025/5/8 13:26@ZHANGCHAO 追加/变更/完善：处理价格说明：是1 否0 空9
            // 2025/5/8 13:26@ZHANGCHAO 追加/变更/完善：处理价格说明
            String promiseItmes = processPromiseItems(content);
            decHead.setPromiseItmes(promiseItmes);

            /*
             * 报关单表体
             * 2025/4/16 10:06@ZHANGCHAO
             */
            JSONArray jsonArray = content.getJSONArray("商品信息");
            processDecList(decId, faxTypeCode, countriesMap, unitMap, unitItemKeyMap, unitCodeMap, districtCodeMap, currMap, ZJMSFSMap, decHead, jsonArray);

            /*
             * 处理集装箱信息
             * 2025/4/21 14:48@ZHANGCHAO
             */
            JSONArray jsonArrayContainer = content.getJSONArray("集装箱信息");
            processDecContainer(decHead, jsonArrayContainer, content);

            /*
             * 处理随附单证
             * 2025/5/27 14:13@ZHANGCHAO
             */
            String electronicDataNumber = content.getString("电子底账数据号");
            if  (isNotBlank(electronicDataNumber)) {
                List<DecLicenseDocus> decLicenseDocusList = new ArrayList<>();
                DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
                decLicenseDocus.setDecId(decId);
                decLicenseDocus.setDocuCode("B");
                decLicenseDocus.setCertCode(electronicDataNumber);
                decLicenseDocusList.add(decLicenseDocus);
                decHead.setDecLicenseDocuses(decLicenseDocusList);
            }

            if (isBlank(decHead.getPromiseItmes()) || "9|9|9|9|9".equals(decHead.getPromiseItmes())) {
                decHead.setPromiseItmes(null); // 又改了。。
            }

            log.info("此时的租户Id是：{}", TenantContext.getTenant());
            decHead.setStatus(3);
            decHeadService.saveDecHead(decHead);
            // 2025/4/30 13:23@ZHANGCHAO 追加/变更/完善：再保存下单量统计表
            saveAiBillingCostStatistics(decHead, clientId);
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("回填报关单出现异常：", e.getMessage());
            decHeadMapper.update(null, new LambdaUpdateWrapper<DecHead>()
                    .set(DecHead::getStatus, 3)
                    .eq(DecHead::getId, decId));
        }
        log.info("开始更新报关单[{}]的AI处理结果", decId);
    }

    /**
     * 保存AI制单费用统计前检查余额
     *
     * @param clientId 客户端ID
     * @return Result<?> 检查结果
     * <AUTHOR>
     * @date 2025/5/20 16:22
     */
    private Result<?> checkBalanceBeforeProcess(String clientId) {
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (isEmpty(sysUser) && isNotBlank(clientId)) {
                sysUser = commonMapper.getUserByClientId(clientId);
                if (isEmpty(sysUser)) {
                    return Result.error("未知的clientId");
                }
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return Result.error("获取用户信息异常");
        }
        // 查询企业余额
        String tenantId = sysUser.getTenantId();
        Result<?> balanceResult = enterpriseInfoService.queryBalanceByBiz(tenantId);
        if (balanceResult.getCode() != CommonConstant.SC_OK_200) {
            return Result.error("查询企业余额失败：" + balanceResult.getMessage());
        }
        // 获取余额并判断是否足够
        BigDecimal balance = null;
        Object balanceObj = balanceResult.getResult();
        if (balanceObj != null) {
            try {
                if (balanceObj instanceof BigDecimal) {
                    balance = (BigDecimal) balanceObj;
                } else if (balanceObj instanceof String) {
                    balance = new BigDecimal((String) balanceObj);
                } else if (balanceObj instanceof Number) {
                    balance = new BigDecimal(balanceObj.toString());
                } else {
                    log.warn("余额数据类型异常: {}", balanceObj.getClass().getName());
                }
            } catch (Exception e) {
                log.error("余额数据转换异常", e);
            }
        }
        // 计算本次操作所需费用
        BigDecimal requiredAmount = new BigDecimal("10.00");
        if (balance == null || balance.compareTo(requiredAmount) < 0) {
            log.info("企业余额不足，当前余额：{}，需要：{}", balance, requiredAmount);
            return Result.error("企业余额不足，当前余额：" + balance + "，请充值后再试");
        }
        return Result.OK();
    }

    /**
     * 判断是否为空
     *
     * @param customerEnterprise
     * @return boolean
     * <AUTHOR>
     * @date 2025/5/30 11:38
     */
    private boolean isCheckEmpty(CustomerEnterprise customerEnterprise) {
        if (isEmpty(customerEnterprise)) {
            return true;
        }
        return isBlank(customerEnterprise.getDepartName())
                && isBlank(customerEnterprise.getSocialCode())
                && isBlank(customerEnterprise.getDepartcd());
    }

    /**
     * 保存AI制单费用统计
     *
     * @param decHead
     * @return void
     * <AUTHOR>
     * @date 2025/5/20 16:22
     */
    private void saveAiBillingCostStatistics(DecHead decHead, String clientId) {
        if  (decHead == null) {
            return;
        }
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
        }
        if (isEmpty(sysUser) && isNotBlank(clientId)) {
            sysUser = commonMapper.getUserByClientId(clientId);
            if (isEmpty(sysUser)) {
                return;
            }
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        int update = aiGenRecordMapper.update(null, new LambdaUpdateWrapper<AiGenRecord>()
                .set(AiGenRecord::getOptUnitName, decHead.getOptUnitName())
                .set(AiGenRecord::getDeliverUnitName, decHead.getDeliverUnitName())
                .set(AiGenRecord::getDeliverUnitName, decHead.getDeliverUnitName())
                .set(AiGenRecord::getIeFlag, decHead.getIeFlag())
                .set(AiGenRecord::getInvoiceNo, decHead.getContract())
                .set(AiGenRecord::getContract, decHead.getContract())
                .set(AiGenRecord::getBillCode, decHead.getBillCode())
                .eq(AiGenRecord::getRelId, decHead.getId()));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (update > 0) {
            log.info("[saveAiBillingCostStatistics]更新AI制单费用统计成功");
            // 去记录费用统计
            JSONObject paramJson = new JSONObject();
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByTenantId(isNotEmpty(decHead.getTenantId()) ? String.valueOf(decHead.getTenantId()) : TenantContext.getTenant());
            if (isNotEmpty(enterpriseInfo)) {
                paramJson.put("creditCode", enterpriseInfo.getUnifiedSocialCreditCode());
            }
            String userId = isNotEmpty(sysUser) && isNotBlank(sysUser.getId()) ? sysUser.getId() : clientId;
            paramJson.put("userId", userId);
            // 服务示例Id规则：Id流水号+合同号，如果没有合同号则取发票号，没有发票号则取提运单号
            String serviceInstanceId = String.join("_",
                    decHead.getId(),
                    isNotBlank(decHead.getContract()) ? decHead.getContract() : decHead.getBillCode()
            );
            paramJson.put("serviceInstanceId", serviceInstanceId);
            paramJson.put("serviceCode", "jg-api-01");
            paramJson.put("uuid", UUID.randomUUID().toString());
            log.info("[saveAiBillingCostStatistics]推送到SSO认证中心AI制单费用统计参数：{}", paramJson);
            String result = requestPost(aiMakerCostUrl, paramJson.toString());
            log.info("[saveAiBillingCostStatistics]推送到SSO认证中心AI制单费用统计返回结果：{}", result);
            try {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (isNotBlank(jsonObject.getString("code")) && "1".equals(jsonObject.getString("code"))) {
                    aiGenRecordMapper.update(null, new LambdaUpdateWrapper<AiGenRecord>()
                            .set(AiGenRecord::getIsSyNC, true)
                            .eq(AiGenRecord::getRelId, decHead.getId()));
                }
            } catch (Exception e) {
                log.error("查询余额异常：{}", e.getMessage());
            }
        } else {
            log.info("[saveAiBillingCostStatistics]更新AI制单费用统计失败");
        }
    }

    /**
     * 处理报关单表体
     *
     * @param decId
     * @param faxTypeCode
     * @param countriesMap
     * @param decHead
     * @param jsonArray
     * @return void
     * <AUTHOR>
     * @date 2025/4/28 10:21
     */
    private void processDecList(String decId, String faxTypeCode, Map<String, String> countriesMap, Map<String, String> unitMap,
                                Map<String, String> unitItemKeyMap, Map<String, String> unitCodeMap, Map<String, String> districtCodeMap, Map<String, String> currMap, Map<String, String> ZJMSFSMap,
                                DecHead decHead, JSONArray jsonArray) {
        if (isNotEmpty(jsonArray)) {
            List<CompletableFuture<Pair<Integer, JSONObject>>> futures = new ArrayList<>();
            List<DecList> decLists = new ArrayList<>();
            Map<String, ErpHscodeData> hscodeMap = new HashMap<>();
            Map<String, String> sbysMap = new HashMap<>();
            int i = 1;
            for (Object ob : jsonArray) {
                JSONObject o = (JSONObject) ob;
                final int itemNo = i++;
                DecList decList = new DecList();
                decList.setDecId(decHead.getId());
                decList.setItem(itemNo);
                decList.setFaxTypeCode(isNotBlank(o.getString("征免方式代码")) ? o.getString("征免方式代码") : faxTypeCode);
                decList.setHsname(o.getString("商品名称"));
                // 备案序号：加工贸易手账册中的序号
                if (isNotBlank(decHead.getRecordNumber())) {
                    if (I.equals(decHead.getIeFlag())) {
                        PtsEmsAimg ptsEmsAimg = emsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                                .eq(PtsEmsAimg::getCopGno, decList.getHsname())
                                .eq(PtsEmsAimg::getEmsNo, decHead.getRecordNumber())
                                .last("limit 1"));
                        decList.setRecordItem(isNotEmpty(ptsEmsAimg) ? ptsEmsAimg.getGNo() : null);
                    } else {
                        PtsEmsAexg ptsEmsAexg = emsAexgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAexg>()
                                .eq(PtsEmsAexg::getCopGno, decList.getHsname())
                                .eq(PtsEmsAexg::getEmsNo, decHead.getRecordNumber())
                                .last("limit 1"));
                        decList.setRecordItem(isNotEmpty(ptsEmsAexg) ? ptsEmsAexg.getGNo() : null);
                    }
                }
                String recordItem = o.getString("备案序号");
                if (isNotBlank(recordItem) && isEmpty(decList.getRecordItem())) {
                    try {
                        decList.setRecordItem(Integer.parseInt(recordItem));
                    } catch (Exception e) {
                        try {
                            if (isNotBlank(recordItem) && recordItem.contains(".")) {
                                decList.setRecordItem(Integer.parseInt(recordItem.split("\\.")[0]));
                            }
                        } catch (NumberFormatException ex) {
                            log.error("备案序号转换异常：{}", recordItem, e);
                        }
                        log.error("备案序号转换异常：{}", recordItem, e);
                    }
                }
                // 最终目的国
                decList.setDestinationCountry(o.getString("最终目的国代码"));
//                String destinationCountry = o.getString("最终目的国");
//                if (isNotBlank(destinationCountry)) {
//                    decList.setDestinationCountry(countriesMap.getOrDefault(destinationCountry, destinationCountry));
//                }
//                // 运输方式为保税港区/保税仓库/保税区,最终目的国:中国
//                if (isBlank(decList.getDestinationCountry())) {
//                    if ("Y".equals(decHead.getShipTypeCode()) || "8".equals(decHead.getShipTypeCode()) || "7".equals(decHead.getShipTypeCode())) {
//                        decList.setDestinationCountry("CHN");
//                    }
//                }
                // 确定哪个字段有值
                String determinedCountry = null;
                if (isNotBlank(decHead.getTradeCountry())) {
                    determinedCountry = decHead.getTradeCountry();
                } else if (isNotBlank(decHead.getArrivalArea())) {
                    determinedCountry = decHead.getArrivalArea();
                } else if (isNotBlank(decList.getDestinationCountry())) {
                    determinedCountry = decList.getDestinationCountry();
                }
                if (isNotBlank(determinedCountry)) {
                    if (isBlank(decHead.getTradeCountry())) {
                        decHead.setTradeCountry(determinedCountry);
                    }
                    if (isBlank(decHead.getArrivalArea())) {
                        decHead.setArrivalArea(determinedCountry);
                    }
                    if (isBlank(decList.getDestinationCountry())) {
                        decList.setDestinationCountry(determinedCountry);
                    }
                }
                // 没有指运港则用所在国家
                if (isBlank(decHead.getDesPort())) {
                    decHead.setDesPort(isNotBlank(determinedCountry) ? (determinedCountry + "000") : null);
                }
                // 2025/5/8 13:37@ZHANGCHAO 追加/变更/完善：兼容商品编号和商品编码
                String hs_code = isNotBlank(o.getString("商品编号")) ? o.getString("商品编号") : o.getString("商品编码");
                String processedCode = processCommodityCode(hs_code, decList);
                decList.setHscode(processedCode);
                // 处理申报要素
                // 调用第二个接口
                String unit1 = ""; // 法定第一单位
                String unit2 = ""; // 法定第二单位
                String sbys = "";
                String sbysRequired = "";
                ErpHscodeData erpHscodeData = erpHscodeDataMapper.selectOne(new LambdaQueryWrapper<ErpHscodeData>()
                        .eq(ErpHscodeData::getHscode, decList.getHscode())
                        .last("limit 1"));
                if (isNotEmpty(erpHscodeData)) {
                    unit1 = erpHscodeData.getUnit1();
                    unit2 = erpHscodeData.getUnit2();
                    sbys = erpHscodeData.getSbys();
                    sbysRequired = erpHscodeData.getSbysRequired();
                    sbysMap.put(decList.getHscode(), sbys);
                    hscodeMap.put(decList.getHscode(), erpHscodeData);
                }

                String pplx = o.getString("品牌类型");
                String ckxhqk = o.getString("出口享惠情况");
                String sbysu = o.getString("申报要素"); // 2025/4/28 10:13@ZHANGCHAO 追加/变更/完善：新加！！
                String hsmodel = o.getString("规格型号");
                String pp = o.getString("品牌"); // 2025/5/8 16:14@ZHANGCHAO 追加/变更/完善：新加！！
                if (isNotBlank(decList.getHscode())) {
                    // 创建异步任务
                    CompletableFuture<Pair<Integer, JSONObject>> future = getSmartFillAsync(decId, hsmodel, sbysu, pplx, ckxhqk, sbys, sbysRequired, pp)
                            .thenApply(res -> new Pair<>(itemNo, res));
                    futures.add(future);
                }
                // 数量及单位
                String qty = o.getString("数量");
                if (isNotBlank(qty)) {
                    try {
                        Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                        Matcher matcher = pattern.matcher(qty);
                        if (matcher.find()) {
                            decList.setGoodsCount(new BigDecimal(matcher.group()));
                        }
                    } catch (Exception e) {
                        log.error("解析数量出错：{}", e.getMessage());
                    }
                }
                String qtyAndUnit = o.getString("数量及单位");
                if (isNotBlank(qtyAndUnit)) {
                    log.info("数量及单位：{}", qtyAndUnit);
                    String[] qtyAndUnitArr = extractQuantityAndUnitAsArray(qtyAndUnit);
                    decList.setGoodsCount(isNotBlank(qtyAndUnitArr[0]) ? new BigDecimal(qtyAndUnitArr[0]) : decList.getGoodsCount());
                    if (isNotBlank(qtyAndUnitArr[1])) {
                        log.info("[转换前]数量及单位中的单位：{}", qtyAndUnitArr[1]);
                        if (unitMap.containsKey(qtyAndUnitArr[1].toUpperCase())) {
                            log.info("[转换后]数量及单位中的单位：{}", unitMap.get(qtyAndUnitArr[1].toUpperCase()));
                            decList.setUnitCode(unitMap.get(qtyAndUnitArr[1].toUpperCase()));
                        }
                        if (isBlank(decList.getUnitCode())) {
                            if (unitItemKeyMap.containsKey(qtyAndUnitArr[1].toUpperCase())) {
                                log.info("[转换后]数量及单位中的单位：{}", unitItemKeyMap.get(qtyAndUnitArr[1].toUpperCase()));
                                decList.setUnitCode(unitItemKeyMap.get(qtyAndUnitArr[1].toUpperCase()));
                            }
                        }
                        if (isBlank(decList.getUnitCode())) {
                            if (unitCodeMap.containsKey(qtyAndUnitArr[1].toUpperCase())) {
                                log.info("[转换后]数量及单位中的单位：{}", unitCodeMap.get(qtyAndUnitArr[1].toUpperCase()));
                                decList.setUnitCode(unitCodeMap.get(qtyAndUnitArr[1].toUpperCase()));
                            }
                        }
                        if (isBlank(decList.getUnitCode())) {
                            DictQuery dictQuery = commonMapper.getErpUnitsCode(qtyAndUnitArr[1]);
                            if (isNotEmpty(dictQuery)) {
                                decList.setUnitCode(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : "");
                            }
                        }
                        log.info("[转换后]最终的数量及单位中的单位：{}", decList.getUnitCode());
                    }
                }
                if (isBlank(decList.getUnitCode())) {
                    String unit = o.getString("单位");
                    if (isNotBlank(unit)) {
                        if (unitMap.containsKey(unit.toUpperCase())) {
                            decList.setUnitCode(unitMap.get(unit.toUpperCase()));
                        }
                        if (isBlank(decList.getUnitCode())) {
                            if (unitItemKeyMap.containsKey(unit.toUpperCase())) {
                                decList.setUnitCode(unitItemKeyMap.get(unit.toUpperCase()));
                            }
                        }
                        if (isBlank(decList.getUnitCode())) {
                            if (unitCodeMap.containsKey(unit.toUpperCase())) {
                                decList.setUnitCode(unitCodeMap.get(unit.toUpperCase()));
                            }
                        }
                        if (isBlank(decList.getUnitCode())) {
                            DictQuery dictQuery = commonMapper.getErpUnitsCode(unit);
                            if (isNotEmpty(dictQuery)) {
                                decList.setUnitCode(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : "");
                            }
                        }
                    }
                }
                if (isBlank(decList.getUnitCode())) {
                    log.info("兜底的单位：{}", decList.getUnitCode());
                    decList.setUnitCode("011"); // 默认单位为件
                }
                // 净重毛重
                String nw = o.getString("净重");
                 if (isNotBlank(nw)) {
                    if (isNumeric(nw)) {
                        decList.setNetWeight(new BigDecimal(nw));
                    } else {
                        try {
                            Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                            Matcher matcher = pattern.matcher(nw);
                            if (matcher.find()) {
                                decList.setNetWeight(new BigDecimal(matcher.group()));
                            }
                        } catch (Exception e) {
                            log.error("解析净重出错：{}", e.getMessage());
                        }
                    }
                }
                String gw = o.getString("毛重");
                if (isNotBlank(gw)) {
                    if (isNumeric(gw)) {
                        decList.setGrossWeight(new BigDecimal(gw));
                    } else {
                        try {
                            Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                            Matcher matcher = pattern.matcher(gw);
                            if (matcher.find()) {
                                decList.setGrossWeight(new BigDecimal(matcher.group()));
                            }
                        } catch (Exception e) {
                            log.error("解析毛重出错：{}", e.getMessage());
                        }
                    }
                }
                // 法定第一数量及单位
                String qty1AndUnit1 = o.getString("法定第一数量及单位");
                BigDecimal ai_qty1 = null; // 模型返的法一数量
                String ai_unit1 = ""; // 模型返的法一单位
                if (isNotBlank(qty1AndUnit1)) {
                    String[] qty1AndUnit1Arr = extractQuantityAndUnitAsArray(qty1AndUnit1);
                    ai_qty1 = isNotBlank(qty1AndUnit1Arr[0]) ? new BigDecimal(qty1AndUnit1Arr[0]) : null;
                    if (isNotBlank(qty1AndUnit1Arr[1])) {
                        if (unitMap.containsKey(qty1AndUnit1Arr[1].toUpperCase())) {
                            ai_unit1 = unitMap.get(qty1AndUnit1Arr[1].toUpperCase());
                        }
                        if (isBlank(ai_unit1)) {
                            if (unitItemKeyMap.containsKey(qty1AndUnit1Arr[1].toUpperCase())) {
                               ai_unit1 = unitItemKeyMap.get(qty1AndUnit1Arr[1].toUpperCase());
                            }
                        }
                        if (isBlank(ai_unit1)) {
                            if (unitCodeMap.containsKey(qty1AndUnit1Arr[1].toUpperCase())) {
                                ai_unit1 = unitCodeMap.get(qty1AndUnit1Arr[1].toUpperCase());
                            }
                        }
                        if (isBlank(ai_unit1)) {
                            DictQuery dictQuery = commonMapper.getErpUnitsCode(qty1AndUnit1Arr[1]);
                            if (isNotEmpty(dictQuery)) {
                                ai_unit1 = isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : null;
                            }
                        }
                    }
                }
                if (isNotBlank(ai_unit1) && ai_unit1.equals(unit1)) {
                    decList.setUnit1(ai_unit1);
                    if (isNotEmpty(ai_qty1)) {
                        decList.setCount1(ai_qty1);
                    } else {
                        // 如果是重量级单位，则取重量
                        BigDecimal goodsCount = decList.getGoodsCount();
                        if (isWeightUnit(decList.getUnit1())) {
                            goodsCount = decList.getNetWeight();
                            // 再进一步，如果重量是空，取成交数量来转换
                            if (isEmpty(decList.getNetWeight())) {
                                // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                if (isWeightUnit(decList.getUnitCode())) {
                                    goodsCount = decList.getGoodsCount();
                                }
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                                decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            } else {
                                decList.setCount1(goodsCount);
                            }
                        } else {
                            BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                            decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                        }
                    }
                } else {
                    if (isNotBlank(unit1)) {
                        decList.setUnit1(unit1);
                        // 如果是重量级单位，则取重量
                        BigDecimal goodsCount = decList.getGoodsCount();
                        if (isWeightUnit(decList.getUnit1())) {
                            goodsCount = decList.getNetWeight();
                            // 再进一步，如果重量是空，取成交数量来转换
                            if (isEmpty(decList.getNetWeight())) {
                                // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                if (isWeightUnit(decList.getUnitCode())) {
                                    goodsCount = decList.getGoodsCount();
                                }
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                                decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            } else {
                                decList.setCount1(goodsCount);
                            }
                        } else {
                            BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                            decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                        }
                    } else if (isNotBlank(ai_unit1)) {
                        decList.setUnit1(ai_unit1);
                        if (isNotEmpty(ai_qty1)) {
                            decList.setCount1(ai_qty1);
                        } else {
                            // 如果是重量级单位，则取重量
                            BigDecimal goodsCount = decList.getGoodsCount();
                            if (isWeightUnit(decList.getUnit1())) {
                                goodsCount = decList.getNetWeight();
                                // 再进一步，如果重量是空，取成交数量来转换
                                if (isEmpty(decList.getNetWeight())) {
                                    // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                    if (isWeightUnit(decList.getUnitCode())) {
                                        goodsCount = decList.getGoodsCount();
                                    }
                                    BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                                    decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                                } else {
                                    decList.setCount1(goodsCount);
                                }
                            } else {
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit1(), goodsCount);
                                decList.setCount1(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            }
                        }
                    }
                }
                // 法定第二数量及单位
                String qty2AndUnit2 = o.getString("法定第二数量及单位");
                BigDecimal ai_qty2 = null; // 模型返的法二数量
                String ai_unit2 = ""; // 模型返的法二单位
                if (isNotBlank(qty2AndUnit2)) {
                    String[] qty2AndUnit2Arr = extractQuantityAndUnitAsArray(qty2AndUnit2);
                    ai_qty2 = isNotBlank(qty2AndUnit2Arr[0]) ? new BigDecimal(qty2AndUnit2Arr[0]) : null;
                    if (isNotBlank(qty2AndUnit2Arr[1])) {
                        if (unitMap.containsKey(qty2AndUnit2Arr[1].toUpperCase())) {
                            ai_unit2 = unitMap.get(qty2AndUnit2Arr[1].toUpperCase());
                        }
                        if (isBlank(ai_unit2)) {
                            if (unitItemKeyMap.containsKey(qty2AndUnit2Arr[1].toUpperCase())) {
                                ai_unit2 = unitItemKeyMap.get(qty2AndUnit2Arr[1].toUpperCase());
                            }
                        }
                        if (isBlank(ai_unit2)) {
                            if (unitCodeMap.containsKey(qty2AndUnit2Arr[1].toUpperCase())) {
                                ai_unit2 = unitCodeMap.get(qty2AndUnit2Arr[1].toUpperCase());
                            }
                        }
                        if (isBlank(ai_unit2)) {
                            DictQuery dictQuery = commonMapper.getErpUnitsCode(qty2AndUnit2Arr[1]);
                            if (isNotEmpty(dictQuery)) {
                                ai_unit2 = isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : null;
                            }
                        }
                    }
                }
                if (isNotBlank(ai_unit2) && ai_unit2.equals(unit2)) {
                    decList.setUnit2(ai_unit2);
                    if (isNotEmpty(ai_qty2)) {
                        decList.setCount2(ai_qty2);
                    } else {
                        // 如果是重量级单位，则取重量
                        BigDecimal goodsCount = decList.getGoodsCount();
                        if (isWeightUnit(decList.getUnit2())) {
                            goodsCount = decList.getNetWeight();
                            // 再进一步，如果重量是空，取成交数量来转换
                            if (isEmpty(decList.getNetWeight())) {
                                // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                if (isWeightUnit(decList.getUnitCode())) {
                                    goodsCount = decList.getGoodsCount();
                                }
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                                decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            } else {
                                decList.setCount2(goodsCount);
                            }
                        } else {
                            BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                            decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                        }
                    }
                } else {
                    if (isNotBlank(unit2)) {
                        decList.setUnit2(unit2);
                        // 如果是重量级单位，则取重量
                        BigDecimal goodsCount = decList.getGoodsCount();
                        if (isWeightUnit(decList.getUnit2())) {
                            goodsCount = decList.getNetWeight();
                            // 再进一步，如果重量是空，取成交数量来转换
                            if (isEmpty(decList.getNetWeight())) {
                                // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                if (isWeightUnit(decList.getUnitCode())) {
                                    goodsCount = decList.getGoodsCount();
                                }
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                                decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            } else {
                                decList.setCount2(goodsCount);
                            }
                        } else {
                            BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                            decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                        }
                    } else if (isNotBlank(ai_unit2)) {
                        decList.setUnit2(ai_unit2);
                        if (isNotEmpty(ai_qty2)) {
                            decList.setCount2(ai_qty2);
                        } else {
                            // 如果是重量级单位，则取重量
                            BigDecimal goodsCount = decList.getGoodsCount();
                            if (isWeightUnit(decList.getUnit2())) {
                                goodsCount = decList.getNetWeight();
                                // 再进一步，如果重量是空，取成交数量来转换
                                if (isEmpty(decList.getNetWeight())) {
                                    // 如果成交单位和法一单位都是重量级，则取成交数量来转换
                                    if (isWeightUnit(decList.getUnitCode())) {
                                        goodsCount = decList.getGoodsCount();
                                    }
                                    BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                                    decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                                } else {
                                    decList.setCount2(goodsCount);
                                }
                            } else {
                                BigDecimal legaQty = handleUnitConversion(decList.getUnitCode(), decList.getUnit2(), goodsCount);
                                decList.setCount2(isNotEmpty(legaQty) && legaQty.compareTo(BigDecimal.ZERO) > 0 ? legaQty : null);
                            }
                        }
                    }
                }
                // 单价/总价/币制
                String priceAndTotalAndCurrency = o.getString("单价/总价/币制");
                if (isNotBlank(priceAndTotalAndCurrency)) {
                    String priceAndTotalAndCurrencyArr = extractPriceInfo(priceAndTotalAndCurrency);
                    if (isNotBlank(priceAndTotalAndCurrencyArr) && priceAndTotalAndCurrencyArr.contains("|")) {
                        String[] arr = priceAndTotalAndCurrencyArr.split("\\|");
                        if (arr.length > 1) {
                            decList.setCurrencyCode(isNotBlank(arr[0]) ? arr[0] : null);
                            decList.setPrice(isNotBlank(arr[1]) ? new BigDecimal(arr[1]) : null);
                        }
                    }
                }
                if (isBlank(decList.getCurrencyCode())) {
                    String currencyCode = o.getString("币制");
                    if (isNotBlank(currencyCode)) {
                        if (currMap.containsKey(currencyCode)) {
                            decList.setCurrencyCode(currMap.get(currencyCode));
                        }
                        if (isBlank(decList.getCurrencyCode())) {
                            DictQuery dictQuery = commonMapper.getCurrencyCode(currencyCode);
                            if (isNotEmpty(dictQuery)) {
                                decList.setCurrencyCode(isNotBlank(dictQuery.getText()) ? dictQuery.getText() : "");
                            }
                        }
                        if ("RMB".equals(currencyCode)) {
                            decList.setCurrencyCode("CNY");
                        }
                        if ("美金".equals(currencyCode)) {
                            decList.setCurrencyCode("USD");
                        }
                    }
                }
                // 单价
                if (isEmpty(decList.getPrice())) {
                    try {
                        String price = o.getString("单价");
                        if (isNotBlank(price)) {
                            if (isNumeric(price)) {
                                decList.setPrice(new BigDecimal(price));
                            } else {
                                String priceAndCurrencyArr = extractPriceInfo(price);
                                if (isNotBlank(priceAndCurrencyArr) && priceAndCurrencyArr.contains("|")) {
                                    String[] arr = priceAndCurrencyArr.split("\\|");
                                    if (arr.length > 1) {
                                        decList.setCurrencyCode(isNotBlank(arr[0]) ? arr[0] : null);
                                        decList.setPrice(isNotBlank(arr[1]) ? new BigDecimal(arr[1]) : null);
                                    }
                                }
                            }
                            // 最后直接正则取吧
                            if (isEmpty(decList.getPrice())) {
                                try {
                                    Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                                    Matcher matcher = pattern.matcher(price);
                                    if (matcher.find()) {
                                        decList.setPrice(new BigDecimal(matcher.group()));
                                    }
                                } catch (Exception e) {
                                    log.error("解析单价出错：{}", e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析单价出错：{}", e.getMessage());
                    }
                }
                // 总价
                if (isEmpty(decList.getTotal())) {
                    try {
                        String total = o.getString("总价");
                        if (isNotBlank(total)) {
                            if (isNumeric(total)) {
                                decList.setTotal(new BigDecimal(total));
                            } else {
                                String totalAndCurrencyArr = extractPriceInfo(total);
                                if (isNotBlank(totalAndCurrencyArr) && totalAndCurrencyArr.contains("|")) {
                                    String[] arr = totalAndCurrencyArr.split("\\|");
                                    if (arr.length > 1) {
                                        decList.setCurrencyCode(isNotBlank(arr[0]) ? arr[0] : null);
                                        decList.setTotal(isNotBlank(arr[1]) ? new BigDecimal(arr[1]) : null);
                                    }
                                }
                            }
                            // 最后直接正则取吧
                            if (isEmpty(decList.getTotal())) {
                                try {
                                    Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                                    Matcher matcher = pattern.matcher(total);
                                    if (matcher.find()) {
                                        decList.setTotal(new BigDecimal(matcher.group()));
                                    }
                                } catch (Exception e) {
                                    log.error("解析总价出错：{}", e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析总价出错：{}", e.getMessage());
                    }
                }
                // 2025/4/30 15:47@ZHANGCHAO 追加/变更/完善：单独处理单价总价数量！
                // 根据价格基准(priceReference)处理单价、总价和数量的计算关系
//                if ("2".equals(decHead.getPriceReference())) {
//                    // 总价为基准：如果有总价和数量，则重新计算单价
//                    if (isNotEmpty(decList.getTotal()) && isNotEmpty(decList.getGoodsCount())) {
//                        if (decList.getGoodsCount().compareTo(BigDecimal.ZERO) != 0) {
//                            decList.setPrice(decList.getTotal().divide(decList.getGoodsCount(), 5, RoundingMode.HALF_UP));
//                        }
//                    } else if (isNotEmpty(decList.getPrice()) && isNotEmpty(decList.getTotal()) && isEmpty(decList.getGoodsCount())) {
//                        // 已知单价和总价，计算数量
//                        if (decList.getPrice().compareTo(BigDecimal.ZERO) != 0) {
//                            decList.setGoodsCount(decList.getTotal().divide(decList.getPrice(), 5, RoundingMode.HALF_UP));
//                        }
//                    }
//                } else {
//                    // 没有指定价格基准，采用默认逻辑
//                    if (isNotEmpty(decList.getGoodsCount()) && isNotEmpty(decList.getPrice()) && isEmpty(decList.getTotal())) {
//                        // 已知数量和单价，计算总价
//                        decList.setTotal(decList.getGoodsCount().multiply(decList.getPrice()).setScale(5, RoundingMode.HALF_UP));
//                    } else if (isNotEmpty(decList.getGoodsCount()) && isNotEmpty(decList.getTotal()) && isEmpty(decList.getPrice())) {
//                        // 已知数量和总价，计算单价
//                        if (decList.getGoodsCount().compareTo(BigDecimal.ZERO) != 0) {
//                            decList.setPrice(decList.getTotal().divide(decList.getGoodsCount(), RoundingMode.HALF_UP).setScale(5, RoundingMode.HALF_UP));
//                        }
//                    } else if (isNotEmpty(decList.getPrice()) && isNotEmpty(decList.getTotal()) && isEmpty(decList.getGoodsCount())) {
//                        // 已知单价和总价，计算数量
//                        if (decList.getPrice().compareTo(BigDecimal.ZERO) != 0) {
//                            decList.setGoodsCount(decList.getTotal().divide(decList.getPrice(), 5, RoundingMode.HALF_UP));
//                        }
//                    }
//                }
                // 2025/5/22 15:33@ZHANGCHAO 追加/变更/完善：5. 单价总价，都按总价来，计算单价，四舍五入保留4位小数
                if (isNotEmpty(decList.getGoodsCount()) && isNotEmpty(decList.getTotal())) {
                    // 已知数量和总价，计算单价
                    if (decList.getGoodsCount().compareTo(BigDecimal.ZERO) != 0) {
                        decList.setPrice(decList.getTotal().divide(decList.getGoodsCount(), 4, RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP));
                    }
                }

                // 原产国
                decList.setDesCountry(o.getString("原产国代码"));
//                String originCountry = o.getString("原产国");
//                if (isNotBlank(originCountry)) {
//                    decList.setDesCountry(countriesMap.getOrDefault(originCountry, originCountry));
//                }
//                if (isBlank(decList.getDesCountry())) {
//                    if (E.equals(decHead.getIeFlag())) {
//                        decList.setDesCountry("CHN");
//                    }
//                }

                // 境内货源地
                decList.setDistrictCode(I.equals(decHead.getIeFlag()) ? o.getString("境内目的地代码") : o.getString("境内货源地代码"));
//                String districtCode = I.equals(decHead.getIeFlag()) ? o.getString("境内目的地") : o.getString("境内货源地");
//                if (isNotBlank(districtCode)) {
//                    // 格式化境内货源地
////                    districtCode = formatSourceLocation(districtCode);
//                    try {
//                        Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
//                        Matcher matcher = pattern.matcher(districtCode);
//                        if (matcher.find()) {
//                            districtCode = matcher.group();
//                        }
//                    } catch (Exception e) {
//                        log.error("解析出错：{}", e.getMessage());
//                    }
//                    if (isNumeric(districtCode)) {
//                        decList.setDistrictCode(districtCode);
//                    } else {
//                        if (districtCodeMap.containsKey(districtCode)) {
//                            decList.setDistrictCode(districtCodeMap.get(districtCode));
//                        }
//                    }
//                    // 怎么还是空，那就取历史
//                    if (isBlank(decList.getDistrictCode())) {
//                        DecList d = decHeadMapper.queryListForDistrictCode(decHead.getOptUnitName(), decHead.getDeliverUnitName(), decList.getHscode(), decList.getHsname(), districtCode);
//                        if (isNotEmpty(d)) {
//                            decList.setDistrictCode(d.getDistrictCode());
//                            decList.setDistrictName(d.getDistrictName());
//                            decList.setDestCode(d.getDestCode());
//                            decList.setDestName(d.getDestName());
//                        } else {
//                            // 怎么历史还是空啊，那就再模糊取字典
//                            ErpDistricts erpDistricts = erpDistrictsMapper.selectOne(new LambdaQueryWrapper<ErpDistricts>()
//                                    .like(ErpDistricts::getName, (isNotBlank(districtCode) ? districtCode : "999") + "其他").last("LIMIT 1"));
//                            if (isNotEmpty(erpDistricts)) {
//                                decList.setDistrictCode(erpDistricts.getCode());
//                                decList.setDistrictName(erpDistricts.getName());
//                            } else {
//                                ErpDistricts erd = erpDistrictsMapper.selectOne(new LambdaQueryWrapper<ErpDistricts>()
//                                        .like(ErpDistricts::getName, districtCode).last("LIMIT 1"));
//                                if (isNotEmpty(erd)) {
//                                    decList.setDistrictCode(erd.getCode());
//                                    decList.setDistrictName(erd.getName());
//                                }
//                            }
//                        }
//                    }
//                }
                // 最终还是空？默认前五位收发货人海关编码兜底
                if (isBlank(decList.getDistrictCode())) {
                    if (isNotBlank(decHead.getDeliverUnit()) && decHead.getDeliverUnit().length() >= 5) {
                        decList.setDistrictCode(decHead.getDeliverUnit().substring(0, 5));
                    }
                }

                // 境内货源地辅助字段-行政区划
//                if (isBlank(decList.getDestCode())) {
//                    DecList decListForDestCode = decHeadMapper.queryListForDestCode(decHead.getOptUnitName(), decHead.getDeliverUnitName(), decList.getHscode(), decList.getHsname(), decList.getDistrictCode());
//                    if (isNotEmpty(decListForDestCode)) {
//                        decList.setDestCode(decListForDestCode.getDestCode());
//                        if (isBlank(decList.getDistrictCode())) {
//                            decList.setDistrictCode(decListForDestCode.getDistrictCode());
//                        }
//                    }
//                }
                // 2025/5/13 09:01@ZHANGCHAO 追加/变更/完善：最后的兜底
//                if (isNotBlank(decList.getDestCode())) {
//                    List<DictQuery> dictQueryList = commonMapper.getDictItemBySearchText("XZQH", decList.getDestCode());
//                    if (isEmpty(dictQueryList)) {
//                        decList.setDestCode(null);
//                    }
//                }

                // 2025/5/25 21:22@ZHANGCHAO 追加/变更/完善：如果是出口，行政区划就空就行
                if (E.equals(decHead.getIeFlag())) {
                    decList.setDestCode(null);
                }

                // 2025/5/12 11:10@ZHANGCHAO 追加/变更/完善：征免
//                String zmfs = o.getString("征免");
//                if (isNotBlank(zmfs) && ZJMSFSMap.containsKey(zmfs)) {
//                    decList.setFaxTypeCode(isNotBlank(ZJMSFSMap.get(zmfs)) ? ZJMSFSMap.get(zmfs) : decList.getFaxTypeCode());
//                }

                decLists.add(decList);
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 处理结果
            for (CompletableFuture<Pair<Integer, JSONObject>> future : futures) {
                try {
                    Pair<Integer, JSONObject> ress = future.get();
                    if (ress != null && ress.getValue() != null) {
                        int itemNo = ress.getKey();
                        JSONObject jsonResult = ress.getValue();
                        String result_hsmodel = jsonResult.getString("result");
                        // 找到对应的商品并更新申报要素
                        decLists.stream()
                                .filter(item -> item.getItem() == itemNo)
                                .findFirst()
//                                    .ifPresent(item -> item.setHsmodel(processHsmodel(result_hsmodel, item)));
                                .ifPresent(item -> item.setHsmodel(result_hsmodel)); // 2025/4/23 11:46@ZHANGCHAO 追加/变更/完善：直接用模型返回的数据
                    }
                } catch (Exception e) {
                    log.error("获取申报要素结果出错：{}", e.getMessage());
                }
            }
            // 2025/4/29 16:50@ZHANGCHAO 追加/变更/完善：最后处理一遍申报要素！！
            for (DecList decList : decLists) {
                if (isNotBlank(decList.getHsmodel())) {
                    // 分割 hsmodel 并过滤掉一位纯数字的项
                    String[] hsmodelParts = decList.getHsmodel().split("\\|");
                    List<String> filteredHsmodelParts = Arrays.stream(hsmodelParts)
//                            .filter(part -> !part.matches("^\\d$"))
                            .collect(Collectors.toList());
                    // 获取历史报关数据
                    DecList decList1 = decHeadMapper.queryListForCheckHsmodel(decHead.getOptUnitName(), decHead.getDeliverUnitName(),
                            decList.getHscode(), decList.getHsname(), filteredHsmodelParts.isEmpty() ? null : filteredHsmodelParts);
                    if (isEmpty(decList1)) {
                        decList1 = decHeadMapper.queryListForCheckHsmodel_(decHead.getDeclareUnitName(),
                                decList.getHscode(), decList.getHsname(), filteredHsmodelParts.isEmpty() ? null : filteredHsmodelParts);
                    }

                    if (isNotEmpty(decList1)) {
                        // historyHsmodel示例：0|0|滑移装载机用|非破碎锤|无品牌|无型号
                        String historyHsmodel = decList1.getHsmodel();
                        // nowHsmodel示例：|||TTR88|||||
                        String nowHsmodel = decList.getHsmodel();
                        String combinedHsmodel = combineHsmodels(historyHsmodel, nowHsmodel);
                        decList.setHsmodel(processHsmodel(combinedHsmodel, decList));
                    } else {
                        decList.setHsmodel(processHsmodel(decList.getHsmodel(), decList));
                    }
                } else {
                    // 如果当前hsmodel为空但有历史数据，直接使用历史hsmodel
                    DecList decList1 = decHeadMapper.queryListForCheckHsmodel(decHead.getOptUnitName(), decHead.getDeliverUnitName(), decList.getHscode(), decList.getHsname(), null);
                    if (isEmpty(decList1)) {
                        decList1 = decHeadMapper.queryListForCheckHsmodel_(decHead.getDeclareUnitName(),
                                decList.getHscode(), decList.getHsname(), null);
                    }

                    if (isNotEmpty(decList1) && isNotBlank(decList1.getHsmodel())) {
                        decList.setHsmodel(processHsmodel(decList1.getHsmodel(), decList));
                    } else {
                        if (isNotBlank(decList.getHscode())) {
                            // sbys示例：0:品牌类型;1:出口享惠情况;2:用途（适用于XX品牌XX机或通用于XX机等）;3:如为破碎锤注明是否带有钎杆;4:品牌（中文或外文名称）;5:型号;6:GTIN;7:CAS;8:其他;
                            String sbys = sbysMap.getOrDefault(decList.getHscode(), "");
                            String hsmodel = processHsmodelWith(sbys);
                            decList.setHsmodel(processHsmodel(hsmodel, decList));
                        } else {
                            decList.setHsmodel(processHsmodel(decList.getHsmodel(), decList));
                        }
                    }
                }
            }

            // 涉法检排在前面
            List<DecList> decListsAll = new ArrayList<>();
            List<DecList> decLists1 = new ArrayList<>();
            List<DecList> decLists2 = new ArrayList<>();
            if (isNotEmpty(decLists)) {
                for (DecList decList : decLists) {
                    ErpHscodeData erpHscodeData = hscodeMap.get(decList.getHscode());
                    if (isNotEmpty(erpHscodeData) && containsCharacter(erpHscodeData.getControLmark(), decHead.getIeFlag())) {
                        decLists1.add(decList);
                    } else {
                        decLists2.add(decList);
                    }
                }
                if (isNotEmpty(decLists1)) {
                    decListsAll.addAll(decLists1);
                }
                if (isNotEmpty(decLists2)) {
                    decListsAll.addAll(decLists2);
                }
                decHead.setDecLists(decListsAll);
            }
        }
    }

    /**
     * 格式化境内货源地
     *
     * 河南省郑州市 -> 郑州
     * 广西壮族自治区南宁市 -> 南宁
     * 北京市 -> 北京
     * 香港特别行政区 -> 香港
     * 浙江省宁波市 -> 宁波
     * 河南许昌 -> 许昌
     * 新疆维吾尔自治区乌鲁木齐市 -> 乌鲁木齐
     *
     * @param sourceLocation
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/12 14:06
     */
    public String formatSourceLocation(String sourceLocation) {
        log.info("原始的境内货源地是：{}", sourceLocation);
        if (sourceLocation == null || sourceLocation.isEmpty()) {
            return sourceLocation;
        }
        String[] suffixes = {
                "省", "市", "自治区", "特别行政区", "壮族自治区", "回族自治区", "维吾尔自治区"
        };
        String[] provinces = {
                "河南", "浙江", "广西", "新疆", "北京", "上海", "天津", "重庆",
                "广东", "江苏", "山东", "山西", "四川", "云南", "贵州", "湖南",
                "湖北", "黑龙江", "吉林", "辽宁", "陕西", "甘肃", "青海", "宁夏",
                "河北", "安徽", "福建", "江西", "海南", "内蒙古", "西藏"
        };
        for (String suffix : suffixes) {
            if (sourceLocation.contains(suffix)) {
                int index = sourceLocation.indexOf(suffix) + suffix.length();
                String result = sourceLocation.substring(index).trim();
                if (result.endsWith("市")) {
                    result = result.substring(0, result.length() - 1);
                }
                return result;
            }
        }
        if (sourceLocation.endsWith("市")) {
            return sourceLocation.substring(0, sourceLocation.length() - 1);
        }
        for (String province : provinces) {
            if (sourceLocation.startsWith(province)) {
                String result = sourceLocation.substring(province.length()).trim();
                if (result.endsWith("市")) {
                    result = result.substring(0, result.length() - 1);
                }
                return result;
            }
        }
        log.info("格式化后的境内货源地是：{}", sourceLocation);
        return sourceLocation;
    }

    /**
     * 处理价格说明字段并组合为promise_itmes
     * @param content 包含表单数据的对象
     * @return 处理后的promise_itmes字符串，格式为：值1|值2|值3|值4|值5
     */
    private String processPromiseItems(JSONObject content) {
        // 需要处理的字段名数组
        String[] fieldNames = {
                "特殊关系确认",
                "价格影响确认",
                "与货物有关的特许权使用费支付确认",
                "公式定价确认",
                "暂定价格确认"
        };

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < fieldNames.length; i++) {
            String fieldValue = null;
            try {
                fieldValue = content.getString(fieldNames[i]);
            } catch (Exception e) {
                log.error("处理价格说明字段出错：{}", e.getMessage());
            }
            String processedValue = processFieldValue(fieldValue);

            result.append(processedValue);
            if (i < fieldNames.length - 1) {
                result.append("|");
            }
        }

        return result.toString();
    }

    /**
     * 处理单个字段值，将"是"转为1，"否"转为0，"空"转为9
     * @param fieldValue 字段值
     * @return 处理后的值：1、0或9
     */
    private String processFieldValue(String fieldValue) {
        if (isNotBlank(fieldValue)) {
            if (isNumeric(fieldValue)) {
                return fieldValue.equals("1") || fieldValue.equals("0") || fieldValue.equals("9") ? fieldValue : "9";
            } else {
                if ("是".equals(fieldValue)) {
                    return "1";
                } else if ("否".equals(fieldValue)) {
                    return "0";
                } else if ("空".equals(fieldValue)) {
                    return "9";
                } else {
                    return "9";
                }
            }
        } else {
            return "9";
        }
    }

    /**
     * 制作空白数组拼接串
     *
     * @param sbys
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/4/29 18:42
     */
    private String processHsmodelWith(String sbys) {
        if (isBlank(sbys)) {
            return "";
        }
//        String str = "0:品牌类型;1:出口享惠情况;2:用途;3:加工工艺;4:是否配定剂量;5:是否零售包装;6:成分;7:来源;8:品牌（中文或外文名称）;9:型号;10:包装规格;11:GTIN;12:CAS;13:其他;";
        String[] strs = sbys.split(";");
        String[] strs1 = new String[strs.length];
        Arrays.fill(strs1, "");
        String result = String.join("|", strs1);
        return result;
    }

    /**
     * 组合历史hsmodel和当前hsmodel
     * 以竖线分割，优先使用当前值，当前值为空时使用历史值
     *
     * @param historyHsmodel 历史hsmodel，如：0|0|滑移装载机用|非破碎锤|无品牌|无型号
     * @param nowHsmodel 当前hsmodel，如：|||TTR88|||||
     * @return 组合后的hsmodel
     */
    private String combineHsmodels(String historyHsmodel, String nowHsmodel) {
        if (isBlank(historyHsmodel)) {
            return nowHsmodel;
        }
        if (isBlank(nowHsmodel)) {
            return historyHsmodel;
        }
        String[] historyItems = historyHsmodel.split("\\|");
        String[] nowItems = nowHsmodel.split("\\|");
        int maxLength = Math.max(historyItems.length, nowItems.length);
        StringBuilder combinedBuilder = new StringBuilder();
        for (int i = 0; i < maxLength; i++) {
            String nowValue = (i < nowItems.length) ? nowItems[i] : "";
            String historyValue = (i < historyItems.length) ? historyItems[i] : "";
            String valueToUse = isNotBlank(nowValue) ? nowValue : historyValue;
            combinedBuilder.append(valueToUse);
            if (i < maxLength - 1) {
                combinedBuilder.append("|");
            }
        }
        return combinedBuilder.toString();
    }

    /**
     * 处理集装箱
     *
     * @param decHead
     * @param jsonArrayContainer
     * @param content
     * @return void
     * <AUTHOR>
     * @date 2025/4/28 10:32
     */
    private void processDecContainer(DecHead decHead, JSONArray jsonArrayContainer, JSONObject content) {
        if (isNotEmpty(jsonArrayContainer)) {
            List<DecContainer> decContainers = new ArrayList<>();
            for (Object ob : jsonArrayContainer) {
                JSONObject o = (JSONObject) ob;
                DecContainer decContainer = new DecContainer();
                decContainer.setDecId(decHead.getId());
                decContainer.setContainerId(o.getString("集装箱号"));
                String containerMd = o.getString("集装箱规格");
                if (isNotBlank(containerMd)) {
                    if (containerMd.contains("大柜") || containerMd.contains("40")) {
                        decContainer.setContainerMd("11");
                    } else if (containerMd.contains("小柜") || containerMd.contains("20")) {
                        decContainer.setContainerMd("21");
                    }
                }
                String goodsContaWt = o.getString("自重");
                if (isNotBlank(goodsContaWt)) {
                    if (isNumeric(goodsContaWt)) {
                        decContainer.setGoodsContaWt(new BigDecimal(goodsContaWt));
                    } else {
                        try {
                            Pattern pattern = Pattern.compile("\\d+\\.?\\d*");
                            Matcher matcher = pattern.matcher(goodsContaWt);
                            if (matcher.find()) {
                                decContainer.setGoodsContaWt(new BigDecimal(matcher.group()));
                            }
                        } catch (Exception e) {
                            log.error("解析自重出错：{}", e.getMessage());
                        }
                    }
                }
                decContainers.add(decContainer);
            }
            if (isNotEmpty(decContainers)) {
                // 如果集装箱数量为1，直接关联所有商品项号
                if (decContainers.size() == 1) {
                    DecContainer singleContainer = decContainers.get(0);
                    StringBuilder itemNos = new StringBuilder();
                    if (isNotEmpty(decHead.getDecLists())) {
                        for (DecList goods : decHead.getDecLists()) {
                            if (itemNos.length() > 0) {
                                itemNos.append(",");
                            }
                            itemNos.append(goods.getItem());
                        }
                    }
                    singleContainer.setGoodsNo(itemNos.toString());
                } else {
                    // 若集装箱数量大于1，则需要根据装箱明细来判断具体哪些项号商品装在哪个集装箱内。
                    // TODO
                    JSONArray jsonArray_zxinfo = content.getJSONArray("分箱明细");
                    if (isNotEmpty(jsonArray_zxinfo)) {
                        Map<String, String> containerMap = new HashMap<>();
                        jsonArray_zxinfo.forEach(obj -> {
                            JSONObject o = (JSONObject) obj;
                            String containerId = o.getString("集装箱号");
                            if (isNotBlank(containerId) && containerId.contains("/")) {
                                containerId = containerId.split("/")[0];
                            }
                            // 2025/4/29 09:33@ZHANGCHAO 追加/变更/完善：商品编码
                            String processedCode = processCommodityCode(o.getString("商品编码"), new DecList());
                            List<Integer> items = new ArrayList<>();
                            if (isNotEmpty(decHead.getDecLists())) {
                                decHead.getDecLists().forEach(goods -> {
                                    if (goods.getHscode().equals(processedCode) || goods.getHsname().equals(o.getString("商品名称"))) {
                                        items.add(goods.getItem());
                                    }
                                });
                                if (isNotEmpty(items)) {
                                    containerMap.put(containerId, CollUtil.join(CollUtil.distinct(items), ","));
                                }
                            }
                        });
                        if (isNotEmpty(containerMap)) {
                            decContainers.forEach(container -> {
                                String goodsNo = containerMap.get(container.getContainerId());
                                if (isNotBlank(goodsNo)) {
                                    container.setGoodsNo(goodsNo);
                                }
                            });
                        }
                    }
                }
            }
            if (isNotEmpty(decContainers)) {
                decHead.setDecContainers(decContainers);
            }
        }
    }

    /**
     * 创建并发调用
     *
     * @param decId
     * @param hsmodel
     * @param sbysu
     * @param pplx
     * @param ckxhqk
     * @param sbys
     * @return java.util.concurrent.CompletableFuture<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2025/4/27 09:34
     */
    private CompletableFuture<JSONObject> getSmartFillAsync(String decId, String hsmodel, String sbysu, String pplx, String ckxhqk, String sbys, String sbysRequired, String pp) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String pythonServiceUrl = aiBaseUrl + "/api/smart-fill";
                HttpRequest request = HttpUtil.createPost(pythonServiceUrl);
                request.header("Content-Type", "application/json");
                // 2025/4/24 16:43@ZHANGCHAO 追加/变更/完善：加鉴权了！！
                request.header("timestamp", getHeadersForAi().get("timestamp"));
                request.header("sign", getHeadersForAi().get("sign"));
                JSONObject paramJson = new JSONObject();
                // 组装text
//                String text = (isNotBlank(sbysu) ? ("申报要素：" + sbysu + "，") : "") + (isNotBlank(hsmodel) ? ("规格型号：" + hsmodel) : "") + (isNotBlank(pplx) ? ("，品牌类型：" + pplx) : "")
//                        + (isNotBlank(ckxhqk) ? ("，出口享惠情况：" + ckxhqk) : "") + (isNotBlank(pp) ? ("，品牌：" + pp) : "");
                // -- 修改开始: 使用 StringJoiner 以句号拼接 --
                StringJoiner textJoiner = new StringJoiner("。");
                if (isNotBlank(sbysu)) {
                    textJoiner.add("申报要素：" + sbysu);
                }
                if (isNotBlank(hsmodel)) {
                    textJoiner.add("规格型号：" + hsmodel);
                }
                if (isNotBlank(pplx)) {
                    textJoiner.add("品牌类型：" + pplx);
                }
                if (isNotBlank(ckxhqk)) {
                    textJoiner.add("出口享惠情况：" + ckxhqk);
                }
                if (isNotBlank(pp)) {
                    textJoiner.add("品牌：" + pp);
                }
                String text = textJoiner.toString();
                // -- 修改结束 --
                paramJson.put("text", text);
                paramJson.put("split_rules", sbys);
                paramJson.put("sbysRequired", sbysRequired);
                request.body(paramJson.toString());
                int readTimeout = 180000;
                request.timeout(readTimeout);
                // 2025/5/27 10:30@ZHANGCHAO 追加/变更/完善：完善一下，如果是空的就不去调用了！！
                int status;
                String responseContent;
                if (isBlank(sbys) || isBlank(text)) {
                    status = 200;
                    JSONObject responseJson = new JSONObject();
                    responseJson.put("error", "申报规范或所有条件为空，不需要去请求查询");
                    responseContent = responseJson.toJSONString();
                } else {
                    HttpResponse response = request.execute();
                    status = response.getStatus();
                    responseContent = response.body();
                }

                // 记录日志
                log.info("[申报要素智能填写]服务调用结果：{}", responseContent);

                // 保存日志
                AiLog logDTO = new AiLog();
                logDTO.setLogType(LOG_TYPE_521);
                logDTO.setOperateType(OPERATE_TYPE_2);
                logDTO.setUserid("AI");
                logDTO.setRequestUrl(pythonServiceUrl);
                logDTO.setRequestType("2");
                logDTO.setRequestParam("数据清洗处理中");
                logDTO.setMethod(Thread.currentThread().getStackTrace()[1].getClassName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName() + "()");
                logDTO.setLogContent(responseContent);
                logDTO.setRemark(paramJson.toString());
                logDTO.setSourceId(decId + "_hsmodel");
                aiLogService.addLog(logDTO);

                if (status == 200) {
                    try {
                        return JSONObject.parseObject(responseContent);
                    } catch (Exception e) {
                        log.error("解析申报要素出错：{}", e.getMessage());
                    }
                }
                return null;
            } catch (Exception e) {
                log.error("调用申报要素接口出错：{}", e.getMessage());
                return null;
            }
        });
    }

    /**
     * 处理单位转换
     *
     * @param dclUnitcd 成交单位
     * @param legalUnit 法定第一单位
     * @param dclQty    成交数量
     * @return
     */
    private BigDecimal handleUnitConversion(String dclUnitcd, String legalUnit, BigDecimal dclQty) {
        dclQty = isNotEmpty(dclQty) ? dclQty : BigDecimal.ZERO;
        BigDecimal legaQty = dclQty;
        if ("007".equals(dclUnitcd) && "054".equals(legalUnit)) {//个→千个
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        } else if ("054".equals(dclUnitcd) && "007".equals(legalUnit)) {//千个→个
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("007".equals(dclUnitcd) && "044".equals(legalUnit)) {//个→百片
            legaQty = dclQty.multiply(new BigDecimal("0.01"));
        } else if ("044".equals(dclUnitcd) && "007".equals(legalUnit)) {//百片→个
            legaQty = dclQty.multiply(new BigDecimal("100"));
        } else if ("011".equals(dclUnitcd) && "054".equals(legalUnit)) {//件→千个
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        } else if ("054".equals(dclUnitcd) && "011".equals(legalUnit)) {//千个→件
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("035".equals(dclUnitcd) && "036".equals(legalUnit)) {//千克→克
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("036".equals(dclUnitcd) && "035".equals(legalUnit)) {//克→千克
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        } else if ("070".equals(dclUnitcd) && "035".equals(legalUnit)) {//吨→千克
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("035".equals(dclUnitcd) && "070".equals(legalUnit)) {//千克→吨
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        } else if ("147".equals(dclUnitcd) && "030".equals(legalUnit)) {//千米→米
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("030".equals(dclUnitcd) && "147".equals(legalUnit)) {//米→千米
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        } else if ("076".equals(dclUnitcd) && "083".equals(legalUnit)) {//磅→盎司
            legaQty = dclQty.multiply(new BigDecimal("16"));
        } else if ("083".equals(dclUnitcd) && "076".equals(legalUnit)) {//盎司→磅
            legaQty = dclQty.multiply(new BigDecimal("0.0625")).setScale(4, RoundingMode.HALF_UP);
        } else if ("076".equals(dclUnitcd) && "035".equals(legalUnit)) { // 磅→千克
            legaQty = dclQty.multiply(new BigDecimal("0.454")).setScale(4, RoundingMode.HALF_UP);
        } else if ("035".equals(dclUnitcd) && "076".equals(legalUnit)) { // 千克→磅
            legaQty = dclQty.multiply(new BigDecimal("2.20462")).setScale(4, RoundingMode.HALF_UP);
        } else if ("083".equals(dclUnitcd) && "035".equals(legalUnit)) { // 盎司→千克
            legaQty = dclQty.multiply(new BigDecimal("0.0283495")).setScale(4, RoundingMode.HALF_UP);
        } else if ("035".equals(dclUnitcd) && "083".equals(legalUnit)) { // 千克→盎司
            legaQty = dclQty.multiply(new BigDecimal("35.274")).setScale(4, RoundingMode.HALF_UP);
        } else if ("095".equals(dclUnitcd) && "035".equals(legalUnit)) { // 升→千克
            legaQty = dclQty.multiply(new BigDecimal("0.888")).setScale(4, RoundingMode.HALF_UP); // 1/1.126 ≈ 0.888
        } else if ("035".equals(dclUnitcd) && "095".equals(legalUnit)) { // 千克→升
            legaQty = dclQty.multiply(new BigDecimal("1.126")).setScale(4, RoundingMode.HALF_UP);
        } else if ("095".equals(dclUnitcd) && "096".equals(legalUnit)) { // 升→毫升
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("096".equals(dclUnitcd) && "095".equals(legalUnit)) { // 毫升→升
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        }
        return legaQty;
    }

    /**
     * 获取境内货源地
     *
     * @param decHead
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/4/20 10:54
     */
    private String getDistrictCodeByCond(DecHead decHead) {
        String districtCode = decHeadMapper.getDistrictCodeByDeliverUnitName(decHead.getDeliverUnitName());
        if (!isValidDistrictCode(districtCode)) {
            districtCode = decHeadMapper.getDistrictCodeByOptUnitName(decHead.getOptUnitName());
        }
        if (!isValidDistrictCode(districtCode)) {
            if (isNotBlank(decHead.getDeliverUnit()) && decHead.getDeliverUnit().length() >= 5) {
                districtCode = decHead.getDeliverUnit().substring(0, 5);
            }
            if (!isValidDistrictCode(districtCode) && isNotBlank(decHead.getOptUnitId())
                    && decHead.getOptUnitId().length() >= 5) {
                districtCode = decHead.getOptUnitId().substring(0, 5);
            }
        }
        return isValidDistrictCode(districtCode) ? districtCode : null;
    }

    // 验证代码有效性
    private boolean isValidDistrictCode(String code) {
        return isNotBlank(code) && code.length() >= 5
                && !"909".equals(code.substring(2, 5));
    }

    /**
     * 字典处理
     *
     * @param dictCode
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2025/4/17 09:57
     */
    private Map<String, String> getDictMap(String dictCode) {
        //1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        List<DictModel> dictList = sysBaseApi.getDictItems(dictCode);
        //2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        return Optional.ofNullable(dictList).orElse(Collections.emptyList())
                .stream()
                .filter(dict -> dict.getValue() != null)
                .collect(Collectors.toMap(
                        DictModel::getText,
                        DictModel::getValue,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 处理商品编号
     *
     * @param originalCode 原始商品编号，如"39241000.0"
     * @return 处理后的商品编号，确保长度为10位，不足补0
     */
    public String processCommodityCode(String originalCode, DecList decList) {
        if (originalCode == null || originalCode.isEmpty()) {
            // 去历史记录查询
            if (isNotBlank(decList.getHsname())) {
//                originalCode = decHeadMapper.getHscodeByHistory(decList.getHsname());
//                if (isBlank(originalCode)) {
                    return originalCode;
//                }
            } else {
                return originalCode;
            }
        }
        String codeWithoutDot = originalCode.replace(".", "");
        if (codeWithoutDot.length() > 10) {
            return codeWithoutDot.substring(0, 10);
        } else if (codeWithoutDot.length() < 10) {
            // 不足10位，后面补0
            StringBuilder sb = new StringBuilder(codeWithoutDot);
            while (sb.length() < 10) {
                sb.append("0");
            }
            return sb.toString();
        }
        return codeWithoutDot;
    }

    /**
     * 保存MultipartFile到临时文件
     */
    private Path saveToTempFile(MultipartFile file) throws IOException {
//        String prefix = "ai_maker_" + System.currentTimeMillis() + "_";
        String prefix = file.getOriginalFilename() + "_" + System.currentTimeMillis() + "_";
        String suffix = file.getOriginalFilename() != null ?
                file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('.')) : "";
        Path tempFile = Files.createTempFile(prefix, suffix);
        file.transferTo(tempFile);
        return tempFile;
    }

    /**
     * 删除临时文件
     */
    private void deleteTempFiles(List<Path> tempFiles) {
        for (Path tempFile : tempFiles) {
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException e) {
                log.warn("删除临时文件失败: {}", tempFile, e);
            }
        }
    }

    /**
     * 获取临时令牌
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/19 上午11:16
     */
    public String getTemporaryToken() {
        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        LoginUser sysUser = commonMapper.getUserByName("admin");
        // 模拟登录生成Token
        String token = JwtUtil.sign(sysUser.getUsername(), sysUser.getPassword());
        // 设置Token缓存有效时间为 5 分钟
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, 5 * 60 * 1000);
        return token;
    }

    /**
     * 获取鉴权请求头
     *
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2025/4/24 16:56
     */
    private Map<String, String> getHeadersForAi() {
        String timestamp = getTime();
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        Map<String, String> param = new HashMap<>();
        param.put("timestamp", timestamp);
        param.put("sign", md5(secretKey + timestamp).toUpperCase());
        return param;
    }
}
