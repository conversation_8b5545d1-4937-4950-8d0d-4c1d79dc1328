package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.base.BaseMap;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.modules.redis.client.JeecgRedisClient;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.entity.SysAnnouncement;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.ICustomerEnterpriseService;
import org.jeecg.modules.business.service.IEnterpriseCustomsRecordService;
import org.jeecg.modules.business.service.IEnterpriseInfoService;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.MSG_TYPE_TENANT;
import static org.jeecg.common.constant.CommonConstant.REDIS_BUSINESS_HANDLER;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date: 2022-02-18
 * @Version: V1.0
 */
@Service
@Slf4j
public class EnterpriseInfoServiceImpl extends ServiceImpl<EnterpriseInfoMapper, EnterpriseInfo> implements IEnterpriseInfoService {
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private CustomsBrokerInfoMapper customsBrokerInfoMapper;
    @Autowired
    private LogisticsInfoMapper logisticsInfoMapper;
    @Autowired
    private EnterpriseCustomsRecordMapper enterpriseCustomsRecordMapper;
    @Autowired
    private ICustomerEnterpriseService customerEnterpriseService;
    @Autowired
    private CommissionerMapper commissionerMapper;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Resource
    private JeecgRedisClient jeecgRedisClient;
    @Value("${sso.api.query-balance}")
    private String queryBalanceUrl;

    private static final String URL = "https://api.jgsoft.com.cn:15555/open-api/sm/GetEnterInfoEx";
    private static final String URL_ = "https://api.jgsoft.com.cn:15555/open-api/sm/GetEnterInfo"; // 基础接口
//    private static final String URL = "http://10.10.100.136:15555/open-api/sm/GetEnterInfoEx";
//    private static final String SWID = "8930000026341"; // 迅吉安的卡
    private static final String SWID = "2100090037794";

    @Override
    public List<EnterpriseInfo> getCollectionEnterpriseList(EnterpriseInfo enterpriseInfo) {
        List<EnterpriseInfo> list = enterpriseInfoMapper.getCollectionEnterpriseList(enterpriseInfo);
        if (list.size() == 0 && enterpriseInfo.getTenantId() != 0) {
            list.add(new EnterpriseInfo());
            String tenantName = enterpriseInfoMapper.getTenantNameById(enterpriseInfo.getTenantId());
            if (tenantName != null) {
                list.get(0).setEnterpriseFullName(tenantName);
            }
        }
        // 2024/6/20 下午2:40@ZHANGCHAO 追加/变更/完善：返回信用等级及行政处罚！
        list.forEach(i -> {
            i.setEnterpriseCustomsRecordList(enterpriseCustomsRecordMapper.selectList(new LambdaQueryWrapper<EnterpriseCustomsRecord>()
                    .eq(EnterpriseCustomsRecord::getEnterpriseId, i.getId())
                    .eq(EnterpriseCustomsRecord::getType, "0")));
        });
        return list;
    }

    @Override
    public void updateTenantName(Long id, String name) {
        enterpriseInfoMapper.updateTenantName(id, name);
    }

    // 设置个限流的令牌桶
    private static final RateLimiter rateLimiter = RateLimiter.create(1.0); // 每秒1个令牌

    /**
     * 海关备案企业信息查询
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/19 下午1:15
     */
    @Override
    public Result<?> customsRecordEnterpriseInformationInquiry() {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        try {
            List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoMapper.selectList(null);
            if (isNotEmpty(enterpriseInfoList)) {
                for (EnterpriseInfo enterpriseInfo : enterpriseInfoList) {
                    Map<String, Object> jsonMap = new LinkedHashMap<>();
                    jsonMap.put("swid", SWID);
                    jsonMap.put("code", enterpriseInfo.getUnifiedSocialCreditCode());
                    jsonMap.put("flag", "1");
                    // 等待直到获取到令牌
                    rateLimiter.acquire();
                    log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
                    String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
                    log.info("请求结果：{}", result);
                    JSONObject jsonObject = JSON.parseObject(result);
                    if (jsonObject.getBoolean("ok")) {
                        dealWithJson(jsonObject, enterpriseInfo);

                    } else {
                        log.info("{};{}", jsonObject.getString("errors"), enterpriseInfo.getEnterpriseFullName());
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取企业备案信息出现异常：{}", e.getMessage());
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
        return Result.ok("执行成功");
    }

    /**
     * 报关行企业信息查询
     * @return
     */
    @Override
    public Result<?> customsRecordEnterpriseInformationInquiry_() {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        try {
            List<CustomsBrokerInfo> customsBrokerInfoList = customsBrokerInfoMapper.selectList(new LambdaQueryWrapper<CustomsBrokerInfo>()
                    .eq(CustomsBrokerInfo::getDelFlag, 0));
            if (isNotEmpty(customsBrokerInfoList)) {
                for (CustomsBrokerInfo customsBrokerInfo : customsBrokerInfoList) {
                    Map<String, Object> jsonMap = new LinkedHashMap<>();
                    jsonMap.put("swid", SWID);
                    jsonMap.put("code", customsBrokerInfo.getCustomsBrokerName());
                    jsonMap.put("flag", "3");
                    // 等待直到获取到令牌
                    rateLimiter.acquire();
                    log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
                    String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
                    log.info("请求结果：{}", result);
                    JSONObject jsonObject = JSON.parseObject(result);
                    if (jsonObject.getBoolean("ok")) {
                        dealWithJson(jsonObject, customsBrokerInfo);
                    } else {
                        log.info("{};{}", jsonObject.getString("errors"), customsBrokerInfo.getCustomsBrokerName());
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取企业备案信息出现异常：{}", e.getMessage());
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
        return Result.ok("执行成功");
    }

    /**
     * 货代企业信息查询
     * @return
     */
    @Override
    public Result<?> customsRecordEnterpriseInformationInquiry__() {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        try {
            List<LogisticsInfo> logisticsInfoList = logisticsInfoMapper.selectList(new LambdaQueryWrapper<LogisticsInfo>()
                    .eq(LogisticsInfo::getDelFlag, 0));
            if (isNotEmpty(logisticsInfoList)) {
                for (LogisticsInfo logisticsInfo : logisticsInfoList) {
                    Map<String, Object> jsonMap = new LinkedHashMap<>();
                    jsonMap.put("swid", SWID);
                    jsonMap.put("code", logisticsInfo.getLogisticsName());
                    jsonMap.put("flag", "3");
                    // 等待直到获取到令牌
                    rateLimiter.acquire();
                    log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
                    String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
                    log.info("请求结果：{}", result);
                    JSONObject jsonObject = JSON.parseObject(result);
                    if (jsonObject.getBoolean("ok")) {
                        dealWithJson(jsonObject, logisticsInfo);
                    } else {
                        log.info("{};{}", jsonObject.getString("errors"), logisticsInfo.getLogisticsName());
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取企业备案信息出现异常：{}", e.getMessage());
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
        return Result.ok("执行成功");
    }

    /**
     * @param templ
     * @return
     */
    @Override
    public Result<?> updateFileTempl(String templ, String type) {
        // 箱单
        if ("receipt".equals(type)) {
            baseMapper.update(null, new UpdateWrapper<EnterpriseInfo>().lambda()
                    .set(EnterpriseInfo::getPackListTempl, templ)
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
            // 发票
        } else if ("release".equals(type)) {
            baseMapper.update(null, new UpdateWrapper<EnterpriseInfo>().lambda()
                    .set(EnterpriseInfo::getInvoiceTempl, templ)
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        }else if("salescontract".equals(type)){
            baseMapper.update(null, new UpdateWrapper<EnterpriseInfo>().lambda()
                   .set(EnterpriseInfo::getSalescontractTempl, templ)
                   .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        }
        return Result.ok("更新成功！");
    }

    /**
     * @param templ
     * @param type
     * @return
     */
    @Override
    public Result<?> removeFileTempl(String templ, String type) {
        // 箱单
        if ("receipt".equals(type)) {
            baseMapper.update(null, new UpdateWrapper<EnterpriseInfo>().lambda()
                    .set(EnterpriseInfo::getPackListTempl, null)
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
            // 发票
        } else if ("release".equals(type)) {
            baseMapper.update(null, new UpdateWrapper<EnterpriseInfo>().lambda()
                    .set(EnterpriseInfo::getInvoiceTempl, null)
                    .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        }
        return Result.ok("清空成功！");
    }

    /**
     * 获取树毛的企业备案信息
     *
     * @param socialCode
     * @param customerName
     * @return org.jeecg.modules.business.entity.CustomerEnterprise
     * <AUTHOR>
     * @date 2024/11/25 11:23
     */
    @Override
    public Result<?> getCustomerEnterpriseBySm(String socialCode, String departcd, String tradeCiqCode, String customerName) {
        if (isBlank(socialCode) && isBlank(departcd) && isBlank(tradeCiqCode) && isBlank(customerName)) {
            return Result.error("参数都不能都为空！");
        }
        String code = "";
        String flag = "";
        if (isNotBlank(socialCode)) {
            code = socialCode;
            flag = "1";
        } else if (isNotBlank(departcd)) {
            code = departcd;
            flag = "0";
        } else if (isNotBlank(tradeCiqCode)) {
            code = tradeCiqCode;
            flag = "4";
        } else if (isNotBlank(customerName)) {
            code = customerName;
            flag = "3";
        }
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID);
        jsonMap.put("code", code);
        jsonMap.put("flag", flag); // 0=海关编号 1=统一社会信用代码 2=组织机构代码 3=公司名称 4=检疫编号
        // 等待直到获取到令牌
        rateLimiter.acquire();
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        CustomerEnterprise customerEnterprise = null;
        if (jsonObject.getBoolean("ok")) {
            customerEnterprise = new CustomerEnterprise();
            dealEnterpriseWithJson(jsonObject, customerEnterprise);
        } else {
            log.info("{};{};{}", jsonObject.getString("errors"), socialCode, customerName);
        }
        return Result.ok(customerEnterprise);
    }

    @Override
    public CustomerEnterprise getCustomerEnterpriseByCond(String socialCode, String departcd, String tradeCiqCode, String customerName) {
        // 检查是否所有参数都为空
        if (StringUtils.isBlank(socialCode) && StringUtils.isBlank(departcd)
                && StringUtils.isBlank(tradeCiqCode) && StringUtils.isBlank(customerName)) {
            return new CustomerEnterprise();
        }

        // 如果是英文，先获取中文名称
        if (isNotBlank(customerName) && !customerName.matches("^[\\u4e00-\\u9fa5]+$")) {
            List<Commissioner> commissioners = commissionerMapper.selectList(new LambdaQueryWrapper<Commissioner>()
                    .eq(Commissioner::getCommissionerEnName, customerName));
            if (isNotEmpty(commissioners)) {
                customerName = commissioners.get(0).getCommissionerFullName();
            }
        }

        String finalCustomerName = customerName;
        List<CustomerEnterprise> customerEnterprises = customerEnterpriseService.list(new LambdaQueryWrapper<CustomerEnterprise>()
                .and(i -> i.eq(isNotBlank(socialCode), CustomerEnterprise::getSocialCode, socialCode)
                        .eq(isNotBlank(departcd),CustomerEnterprise::getDepartcd, departcd)
                        .eq(isNotBlank(tradeCiqCode),CustomerEnterprise::getCiqCode, tradeCiqCode)
                        .eq(isNotBlank(finalCustomerName),CustomerEnterprise::getDepartName, finalCustomerName)));
        if (isNotEmpty(customerEnterprises)) {
            return customerEnterprises.get(0);
        }
        CustomerEnterprise customerEnterprise = isNotEmpty(customerEnterprises) ? customerEnterprises.get(0) : new CustomerEnterprise();

        // 调用远程接口获取数据
        Result<?> remoteResult = this.getCustomerEnterpriseBySm(socialCode, departcd, tradeCiqCode, customerName);
        if (remoteResult.isSuccess() && remoteResult.getResult() != null) {
            CustomerEnterprise remoteEnterprise = (CustomerEnterprise) remoteResult.getResult();
            // 检查远程返回的数据是否有效
            if (isNotEmpty(remoteEnterprise) &&
                    !(isBlank(remoteEnterprise.getDepartcd()) &&
                            isBlank(remoteEnterprise.getDepartName()) &&
                            isBlank(remoteEnterprise.getCiqCode()) &&
                            isBlank(remoteEnterprise.getSocialCode()))) {
                if (isNotEmpty(customerEnterprise.getId())) {
                    // 本地更新
                    BeanUtil.copyProperties(remoteEnterprise, customerEnterprise, CopyOptions.create().ignoreNullValue());
                    customerEnterpriseService.updateById(customerEnterprise);
                } else {
                    // 本地保存
                    customerEnterpriseService.save(remoteEnterprise);
                }
                return remoteEnterprise;
            }
        } else {
            return customerEnterprise;
        }
        return customerEnterprise;
    }

    /**
     * 获取到备案信息后存入企业备案信息表
     *
     * @param jsonObject
     * @param customerEnterprise
     * @return void
     * <AUTHOR>
     * @date 2024/11/25 11:39
     */
    private void dealEnterpriseWithJson(JSONObject jsonObject, CustomerEnterprise customerEnterprise) {
        try {
            JSONObject jsonData = jsonObject.getJSONObject("data");
            if (isNotEmpty(jsonData)) {
                JSONObject jo = jsonData.getJSONArray("copInfo").getJSONObject(0);
                String regCiqCode = jo.getJSONArray("ciqInfoList")
                        .getJSONObject(0)
                        .getString("regCiqCode");
                String ciqNameSaic = jo.getJSONArray("ciqInfoList")
                        .getJSONObject(0)
                        .getString("ciqNameSaic");
                String regCusCode = jo.getJSONArray("cusInfoList")
                        .getJSONObject(0)
                        .getString("regCusCode");
                customerEnterprise.setSocialCode(jo.getString("scCode")); // 统一社会信用代码
                customerEnterprise.setDepartName(ciqNameSaic); // 海关注册名称
                customerEnterprise.setDepartcd(regCusCode); // 海关注册编码
                customerEnterprise.setCiqCode(regCiqCode); // 商检注册编码
            }
        } catch (Exception e) {
            log.error("解析json出错：{}", e.getMessage());
        }
    }

    /**
     * 处理备案信息
     *
     * @param jsonObject
     * @param t
     * @return void
     * <AUTHOR>
     * @date 2024/6/20 上午10:58
     */
    private <T> void dealWithJson(JSONObject jsonObject, T t) {
        String id;
        String tenantId;
        String name;
        String type;
        boolean send;
        if (t instanceof EnterpriseInfo) {
            send = false;
            EnterpriseInfo enterpriseInfo = (EnterpriseInfo) t;
            id = enterpriseInfo.getId();
            tenantId = enterpriseInfo.getTenantId().toString();
            name = enterpriseInfo.getEnterpriseFullName();
            type = "0";
        } else if (t instanceof CustomsBrokerInfo) {
            CustomsBrokerInfo customsBrokerInfo = (CustomsBrokerInfo) t;
            id = customsBrokerInfo.getId();
            tenantId = customsBrokerInfo.getTenantId().toString();
            name = customsBrokerInfo.getCustomsBrokerName();
            send = true;
            type = "1";
        } else if (t instanceof LogisticsInfo) {
            LogisticsInfo logisticsInfo = (LogisticsInfo) t;
            id = logisticsInfo.getId();
            tenantId = logisticsInfo.getTenantId().toString();
            name = logisticsInfo.getLogisticsName();
            send = true;
            type = "2";
        } else {
            return;
        }
        List<EnterpriseCustomsRecord> list = new ArrayList<>();
        // 处理信用等级信息
        if (isNotEmpty(jsonObject.getJSONObject("data").get("creditInfos"))) {
            if (jsonObject.getJSONObject("data").get("creditInfos") instanceof JSONObject) {
                JSONObject jo = jsonObject.getJSONObject("data").getJSONObject("creditInfos");
                list.add(setCustomsRecordByJson(id, tenantId, name, jo, type, true));
            } else {
                for (Object o : jsonObject.getJSONObject("data").getJSONArray("creditInfos")) {
                    JSONObject jo = (JSONObject) o;
                    list.add(setCustomsRecordByJson(id, tenantId, name, jo, type, true));
                }
            }
        }
        // 处理行政处罚信息
        if (isNotEmpty(jsonObject.getJSONObject("data").get("caseInfos"))) {
            if (jsonObject.getJSONObject("data").get("caseInfos") instanceof JSONObject) {
                JSONObject jo = jsonObject.getJSONObject("data").getJSONObject("caseInfos");
                list.add(setCustomsRecordByJson(id, tenantId, name, jo, type, false));
            } else {
                for (Object o : jsonObject.getJSONObject("data").getJSONArray("caseInfos")) {
                    JSONObject jo = (JSONObject) o;
                    list.add(setCustomsRecordByJson(id, tenantId, name, jo, type, false));
                }
            }
        }
        if (isNotEmpty(list)) {
//                        enterpriseCustomsRecordService.saveOrUpdateBatch(list);
            list.forEach(i -> {
                if (isNotEmpty(i.getIsCredit()) && i.getIsCredit()) {
                    Long count = enterpriseCustomsRecordMapper.selectCount(new LambdaQueryWrapper<EnterpriseCustomsRecord>()
                            .eq(EnterpriseCustomsRecord::getEnterpriseId, i.getEnterpriseId())
                            .eq(EnterpriseCustomsRecord::getEnterpriseName, i.getEnterpriseName())
                            .eq(EnterpriseCustomsRecord::getNewCoClassName, i.getNewCoClassName())
                            .eq(EnterpriseCustomsRecord::getOrgCoClassName, i.getOrgCoClassName())
                            .eq(EnterpriseCustomsRecord::getCoClassReviseDate, i.getCoClassReviseDate()));
                    if (count == 0) {
                        int insert = enterpriseCustomsRecordMapper.insert(i);
                        if (insert > 0 && send) {
                            sendMessage(i);
                        }
                    }
                } else {
                    Long count = enterpriseCustomsRecordMapper.selectCount(new LambdaQueryWrapper<EnterpriseCustomsRecord>()
                            .eq(EnterpriseCustomsRecord::getEnterpriseId, i.getEnterpriseId())
                            .eq(EnterpriseCustomsRecord::getEnterpriseName, i.getEnterpriseName())
                            .eq(EnterpriseCustomsRecord::getEtpsNm, i.getEtpsNm())
                            .eq(EnterpriseCustomsRecord::getCaseNatureNm, i.getCaseNatureNm())
                            .eq(EnterpriseCustomsRecord::getDspsTime, i.getDspsTime())
                            .eq(EnterpriseCustomsRecord::getCaseNo, i.getCaseNo()));
                    if (count == 0) {
                        int insert = enterpriseCustomsRecordMapper.insert(i);
                        if (insert > 0 && send) {
                            sendMessage(i);
                        }
                    }
                }
            });
        }
    }

    /**
     * 转换企业信息
     *
     * @param jo
     * @param isCredit
     * @return org.jeecg.modules.business.entity.EnterpriseCustomsRecord
     * <AUTHOR>
     * @date 2024/6/19 下午6:11
     */
    public static EnterpriseCustomsRecord setCustomsRecordByJson(String id, String tenantId, String name, JSONObject jo, String type, boolean isCredit) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        EnterpriseCustomsRecord enterpriseCustomsRecord = new EnterpriseCustomsRecord();
        enterpriseCustomsRecord.setEnterpriseId(id);
        enterpriseCustomsRecord.setTenantId(Long.valueOf(tenantId));
        enterpriseCustomsRecord.setEnterpriseName(name);
        enterpriseCustomsRecord.setCreateBy(loginUser.getUsername());
        enterpriseCustomsRecord.setCreateDate(new Date());
        enterpriseCustomsRecord.setIsCredit(isCredit);
        enterpriseCustomsRecord.setType(type);
        if (isCredit) {
            enterpriseCustomsRecord.setNewCoClassName(jo.getString("newCoClassName"));
            enterpriseCustomsRecord.setOrgCoClassName(jo.getString("orgCoClassName"));
            enterpriseCustomsRecord.setCoClassReviseDate(jo.getString("coClassReviseDate"));
        } else {
            enterpriseCustomsRecord.setEtpsNm(jo.getString("etpsNm"));
            enterpriseCustomsRecord.setCaseNatureNm(jo.getString("caseNatureNm"));
            enterpriseCustomsRecord.setDspsTime(jo.getString("dspsTime"));
            enterpriseCustomsRecord.setCaseNo(jo.getString("caseNo"));
        }
        return enterpriseCustomsRecord;
    }

    /**
     * 发送消息
     *
     * @param enterpriseCustomsRecord
     * @return void
     * <AUTHOR>
     * @date 2024/6/20 上午9:02
     */
    private void sendMessage(EnterpriseCustomsRecord enterpriseCustomsRecord) {
        String title;
        String msgContent;
        if (isNotEmpty(enterpriseCustomsRecord.getIsCredit()) && enterpriseCustomsRecord.getIsCredit()) {
            title = "信用等级信息变更";
            msgContent = "企业[" + enterpriseCustomsRecord.getEnterpriseName() + "]信用等级发生变更！原信用等级[" + enterpriseCustomsRecord.getOrgCoClassName()
                    + "]，调整后信用等级[" + enterpriseCustomsRecord.getNewCoClassName() + "]" + "，调整日期[" + enterpriseCustomsRecord.getCoClassReviseDate()
                    + "]，所属租户[" + enterpriseCustomsRecord.getTenantId() + "]";
        } else {
            title = "行政处罚信息变更";
            msgContent = "企业[" + enterpriseCustomsRecord.getEnterpriseName() + "]行政处罚信息发生变更！行政处罚决定书编号[" + enterpriseCustomsRecord.getCaseNo()
                    + "]，处罚时间[" + enterpriseCustomsRecord.getDspsTime() + "]，所属租户[" + enterpriseCustomsRecord.getTenantId() + "]";
        }
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        sysAnnouncement.setTitile(title);
        //消息类型为业务消息
        sysAnnouncement.setMsgCategory("3");
//        sysAnnouncement.setMsgType(MSG_TYPE_ALL);
        sysAnnouncement.setMsgType(MSG_TYPE_TENANT);
//        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        sysAnnouncement.setMsgContent(msgContent);
        sysAnnouncement.setBusType("EnterpriseCustomsRecord");
//        List<String> idList = new ArrayList<>(16);
        // TODO: 2023/8/27 获取接收消息的用户ID，逻辑待定！！
//        idList.add("e9ca23d68d884d4ebb19d07889727dae");
//        if (isEmpty(idList)) {
//            log.info("[sendMessage]用户ID列表是空的，无法发送消息！");
//            return;
//        }
//        sysAnnouncement.setUserIds(CollUtil.join(idList, ","));
//        sysAnnouncement.setOpenType("component");
//        sysAnnouncement.setOpenPage("I".equals(contract.getImSign()) ? "/contractManage/ContractImportIndex" : "/contractManage/ContractExportIndex");
        sysAnnouncement.setCreateBy("TASK");
        sysAnnouncement.setSender("TASK");
        sysAnnouncement.setTenantId(String.valueOf(enterpriseCustomsRecord.getTenantId()));
        BaseMap baseMap = new BaseMap();
        baseMap.put("message", JSON.toJSONString(sysAnnouncement));
        jeecgRedisClient.sendMessage(REDIS_BUSINESS_HANDLER, baseMap);
    }

    /**
     * 请求三方接口
     *
     * @param url
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/18 下午4:08
     */
    public static String requestGet(String url) {
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 系统参数
        Map<String, Object> param = new HashMap<>();
        param.put("timeStamp", timestamp);
        String sign = null;
        try {
            sign = buildSign(param, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HttpRequest request = HttpUtil.createGet(url);
        request.header("Content-Type", "application/json");
        request.header("timestamp", timestamp);
        request.header("sign", sign);
        int readTimeout = 180000;
        request.timeout(readTimeout);
        HttpResponse response = request.execute();
        String responseContent = response.body();
        log.info("[requestGet]服务调用结果：{}", responseContent);
        return responseContent;
    }

    /**
     * 构建签名
     *
     * @param paramsMap 参数
     * @param secret    密钥
     * @return
     */
    public static String buildSign(Map<String, ?> paramsMap, String secret) {
        Set<String> keySet = paramsMap.keySet();
        List<String> paramNames = new ArrayList<String>(keySet);
        Collections.sort(paramNames);
        StringBuilder paramNameValue = new StringBuilder();
        for (String paramName : paramNames) {
            paramNameValue.append(paramName).append(paramsMap.get(paramName));
        }
        String source = secret + paramNameValue + secret;
        log.info("source: {}", source);
        return md5_(source);
    }

    /**
     * 生成md5,全部大写
     *
     * @param message
     * @return
     */
    public static String md5_(String message) {
        try {
            // 1 创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 2 将消息变成byte数组
            byte[] input = message.getBytes();
            // 3 计算后获得字节数组,这就是那128位了
            byte[] buff = md.digest(input);
            // 4 把数组每一字节（一个字节占八位）换成16进制连成md5字符串
            return byte2hex(buff);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 二进制转十六进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    /**
     * 查询企业余额
     *
     * @param tenantId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/5/20 14:09
     */
    @Override
    public Result<?> queryBalanceByBiz(String tenantId) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        EnterpriseInfo enterpriseInfo = baseMapper.selectOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, tenantId)
                .last("LIMIT 1"));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        BigDecimal balance = null;
        if (isNotEmpty(enterpriseInfo)) {
            String creditCode = enterpriseInfo.getUnifiedSocialCreditCode();
            String completeUrl = queryBalanceUrl;
            if (isNotBlank(creditCode)) {
                // 动态构建URL
                completeUrl = queryBalanceUrl.replace("{creditCode}", creditCode);
            }
            log.info("[queryBalanceByBiz]查询余额URL：{}", completeUrl);
            String result = requestGet(completeUrl);
            try {
                JSONObject jsonObject = JSONObject.parseObject(result);
                balance = isNotBlank(jsonObject.getString("data")) ? new BigDecimal(jsonObject.getString("data")) : null;
            } catch (Exception e) {
                log.error("查询余额异常：{}", e.getMessage());
                return Result.error("未获取到企业[" + enterpriseInfo.getEnterpriseFullName() + "]的账户余额信息！");
            }
        }
        return Result.OK(balance);
    }
}
