package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.base.BaseMap;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.modules.redis.client.JeecgRedisClient;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.entity.SysAnnouncement;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.ImportExcelUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.entity.dto.LoveUBaby;
import org.jeecg.modules.business.entity.excel.ImportBusinessExcelEntity;
import org.jeecg.modules.business.entity.excel.ImportBusinessExcelEntityE;
import org.jeecg.modules.business.entity.excel.OrderExcelEntity;
import org.jeecg.modules.business.entity.paramVo.DecHeadTempleteVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.result.ExcelImportResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.SimpleFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static org.jeecg.common.constant.CommonConstant.MSG_TYPE_TENANT;
import static org.jeecg.common.constant.CommonConstant.REDIS_BUSINESS_HANDLER;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;

/**
 * @Description: 订单信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements IOrderInfoService {

    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private IOrderProductInfoService orderProductInfoService;
    @Autowired
    private IAttachmentsInfoService attachmentsInfoService;
    @Autowired
    private IOrderTransportationInfoService orderTransportationInfoService;
    @Autowired
    private IOrderReceiptNoticeInfoService orderReceiptNoticeInfoService;
    @Autowired
    private IOrderShippingBookingInfoService orderShippingBookingInfoService;
    @Autowired
    private IOrderCustomsDecExportInfoService orderCustomsDecExportInfoService;
    @Autowired
    private OrderSummaryInfoMapper orderSummaryInfoMapper;
    @Autowired
    private IOperationLogInfoService operationLogInfoService;
    @Autowired
    private OrderPackingInfoMapper packingInfoMapper;
    @Autowired
    private IOrderPackingInfoService packingInfoService;
    @Autowired
    private IErpCurrenciesService erpCurrenciesService;
    @Autowired
    private IErpUnitsService erpUnitsService;
    @Autowired
    private IOverseasPayerInfoService overseasPayerInfoService;
    @Autowired
    private OverseasPayerInfoMapper overseasPayerInfoMapper;
    @Autowired
    private IDecHeadService decHeadService;
    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;
    @Autowired
    private IDomesticSuppliersInfoService domesticSuppliersInfoService;
    @Autowired
    private DomesticSuppliersInfoMapper domesticSuppliersInfoMapper;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private IContractGoodsService contractGoodsService;
    @Autowired
    private CustomerEnterpriseMapper customerEnterpriseMapper;
    @Autowired
    private CommissionerMapper commissionerMapper;
    @Autowired
    private CustomsBrokerInfoMapper customsBrokerInfoMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ErpCiqMapper erpCiqMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private IDecHeadTempleteService decHeadTempleteService;
    @Autowired
    private CustomsExemptMapper exemptMapper;
    @Autowired
    private PtsEmsHeadMapper emsHeadMapper;
    @Autowired
    private PtsEmsAimgMapper emsAimgMapper;
    @Autowired
    private PtsEmsAexgMapper emsAexgMapper;
    @Autowired
    private ErpHscodesMapper erpHscodesMapper;
    @Autowired
    private ProductInfoMapper productInfoMapper;
    @Autowired
    private IApplyConfigService applyConfigService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private ProductMergeMapper productMergeMapper;
    @Autowired
    private OrderInfoTemplateMapper orderInfoTemplateMapper;
    @Autowired
    private OrderProductInfoTemplateMapper orderProductInfoTemplateMapper;
    @Resource
    private JeecgRedisClient jeecgRedisClient;
    @Autowired
    private IProductInfoService productInfoService;

    /**
     * 通过id查询
     * 查询订单信息
     * @param orderInfoId
     * @return
     */
    @Override
    public OrderInfoBiz getOrderById(String orderInfoId) {
        String tenantId = TenantContext.getTenant();
//        获取订单全部信息
        OrderInfoBiz orderInfo = orderInfoMapper.getOrderById(orderInfoId, tenantId);
        if (orderInfo == null) {
            return null;
        }
//        获取订单商品列表
        Map<String, Object> mapP = new HashMap<>();
        mapP.put("order_info_id",orderInfoId);
        mapP.put("tenant_id",tenantId);
        mapP.put("del_flag",CommonConstant.DEL_FLAG_0);
        List<OrderProductInfo> productList = orderProductInfoService.listByMap(mapP);
        orderInfo.setProductList(productList);
//        获取订单附件列表
        Map<String, Object> mapA = new HashMap<>();
        mapA.put("relation_id",orderInfoId);
        mapA.put("tenant_id",tenantId);
        mapA.put("del_flag", CommonConstant.DEL_FLAG_0);
        List<AttachmentsInfo> attachmentList = attachmentsInfoService.listByMap(mapA);
        orderInfo.setAttachmentList(attachmentList);
//        获取订单装箱信息列表
        Map<String, Object> mapC = new HashMap<>();
        mapC.put("order_info_id",orderInfoId);
        mapP.put("tenant_id",tenantId);
        List<OrderPackingInfo> packingInfoList = packingInfoService.listByMap(mapC);
        orderInfo.setPackingList(packingInfoList);
//        匹配金额
        BigDecimal inAmount = orderSummaryInfoMapper.getOrderIncome(orderInfo.getId());
        orderInfo.setMatchingAmount(inAmount);
        if (inAmount.compareTo(BigDecimal.ZERO) == 0) {
            orderInfo.setHasMatchingFlag(false);
        } else {
            orderInfo.setHasMatchingFlag(true);
        }

        return orderInfo;
    }

    /**
     * 保存订单信息
     * @param orderInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrder(OrderInfoBiz orderInfo) {
        orderInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
        String tenantId = TenantContext.getTenant();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isBlank(orderInfo.getCreateBy())) {
            orderInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
        }
        if (isEmpty(orderInfo.getCreateTime())) {
            orderInfo.setCreateTime(new Date());
        }
        boolean isCG = "1".equals(orderInfo.getOrderType());
//        设置默认值
        if (orderInfo.getOrderStatus() == null) {
            orderInfo.setOrderStatus(0);
        }
        if (orderInfo.getReceiveMoneyFlag() == null) {
            orderInfo.setReceiveMoneyFlag(0);
        }
        if (orderInfo.getPayMoneyFlag() == null) {
            orderInfo.setPayMoneyFlag(0);
        }
        if (orderInfo.getHasPayMoneyFlag() == null) {
            orderInfo.setHasPayMoneyFlag(0);
        }
//        清空传入的非当前监管模式下（其它监管模式下）的相关值
        if (!"9710".equals(orderInfo.getSupervisionMode())) {
            // 非9710模式
            orderInfo.setOrderNoNineSeven(null);
            orderInfo.setOverseasWarehousePlatform(null);
        }
        if (!"9810".equals(orderInfo.getSupervisionMode())) {
            // 非9810模式
            orderInfo.setOrderStockNo(null);
            orderInfo.setOverseasWarehouseName(null);
            orderInfo.setOverseasWarehouseCountry(null);
            orderInfo.setOverseasWarehouseAddress(null);
        }

        // 2024/9/23 13:27@ZHANGCHAO 追加/变更/完善：业务保存时默认个业务类型
        if (isBlank(orderInfo.getOrderType())) {
            if ("I".equals(orderInfo.getIeFlag())) {
                orderInfo.setOrderType("2"); // 默认2进口采购
            } else if ("E".equals(orderInfo.getIeFlag())) {
                orderInfo.setOrderType("3"); // 默认3外销订单
            }
        }
        //20241204前端传的运抵国启运国存在数字 需要转化为字母
        if(isNotBlank(orderInfo.getCountryArrival())&&isNumeric(orderInfo.getCountryArrival())){
            List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_countries,name,code");
            List<DictModel> dictModelList = dictModels2.stream().filter(i->i.getValue().equals(orderInfo.getCountryArrival()))
                    .collect(Collectors.toList());
            if(!dictModelList.isEmpty()){
                List<DictModel> dictModelList4 =  dictModels2.stream().filter(i->i.getText()
                        .equals(dictModelList.get(0).getText())).collect(Collectors.toList());
                List<DictModel> dictModelList3 = dictModelList4.stream().filter(i->!isNumeric(i.getValue()))
                                .collect(Collectors.toList());

                orderInfo.setCountryArrival(!dictModelList3.isEmpty() ?dictModelList3.get(0).getValue():null);

            }

        }

//        保存订单信息表
        String orderInfoId = orderInfo.getId();
        if (isBlank(orderInfoId)) {
//            新建订单信息
            OrderInfo order = new OrderInfo();
            BeanUtils.copyProperties(orderInfo, order);
            String orderNo = serialNumberService.getSerialnumberByCustomerCode("I".equals(orderInfo.getIeFlag()) ? "IB" : "EB", 4);
            order.setOrderProtocolNo(orderNo);
            order.setTenantId(Long.valueOf(tenantId));
            order.setRemindStatus(0);
            order.setIncomingNoticeFlag(0);
            order.setUpdateTime(new Date());
            order.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
            order.setCreateTime(new Date());
            // 处理委托方
            if (isBlank(orderInfo.getBuyer()) && isNotBlank(orderInfo.getBuyerName())) {
                List<Commissioner> commissioners = commissionerMapper.selectList(new LambdaQueryWrapper<Commissioner>()
                        .eq(Commissioner::getCommissionerFullName, orderInfo.getBuyerName()));
                if (isNotEmpty(commissioners)) {
                    order.setBuyer(commissioners.get(0).getId());
                }
            } else {
                if (isBlank(orderInfo.getBuyerName())) {
                    Commissioner commissioner = commissionerMapper.selectById(orderInfo.getBuyer());
                    if (isNotEmpty(commissioner)) {
                        order.setBuyerName(commissioner.getCommissionerFullName());
                    }
                    if (isBlank(orderInfo.getBuyerName())) {
                        String tenantName = null;
                        try {
                            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                            Result<Tenant> tenant = sysBaseApi.getTenantById(orderInfo.getBuyer());
                            if (isNotEmpty(tenant.getResult())) {
                                tenantName = tenant.getResult().getName();
                            }
                        } catch (Exception e) {
                            log.error("获取租户名出现异常：{}", e.getMessage());
                        }
                        order.setBuyerName(tenantName);
                    }
                }
            }
            // 处理供应商-进口 或 买方/付款方-出口
            if (isBlank(orderInfo.getOverseasPayerInfoId()) && isNotBlank(orderInfo.getOverseasPayerInfoName())) {
                if ("I".equals(orderInfo.getIeFlag())) {
                    List<DomesticSuppliersInfo> domesticSuppliersInfos = domesticSuppliersInfoService.list(new LambdaQueryWrapper<DomesticSuppliersInfo>()
                            .eq(DomesticSuppliersInfo::getSuppliersFullName, orderInfo.getOverseasPayerInfoName()));
                    if (isNotEmpty(domesticSuppliersInfos)) {
                        order.setOverseasPayerInfoId(domesticSuppliersInfos.get(0).getId());
                    }
                } else if ("E".equals(orderInfo.getIeFlag())) {
                    List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
                            .eq(OverseasPayerInfo::getOverseasPayerName, orderInfo.getOverseasPayerInfoName()));
                    if (isNotEmpty(overseasPayerInfos)) {
                        order.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
                        order.setOverseasPayerInfoName(overseasPayerInfos.get(0).getOverseasPayerName());
                    }
                }
            } else {
                if (isBlank(order.getOverseasPayerInfoName())) {
                    if ("I".equals(orderInfo.getIeFlag())) {
                        DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(orderInfo.getOverseasPayerInfoId());
                        if (isNotEmpty(domesticSuppliersInfo)) {
                            order.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                        }
                    } else if ("E".equals(orderInfo.getIeFlag())) {
                        OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
                        if (isNotEmpty(overseasPayerInfo)) {
                            order.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
                        }
                    }
                }
            }
            // 2024/9/30 11:23@ZHANGCHAO 追加/变更/完善：报关行完善些数据！
            if (isNotBlank(orderInfo.getCustomsBrokerId())) {
                CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoMapper.selectById(orderInfo.getCustomsBrokerId());
                if (isNotEmpty(customsBrokerInfo)) {
                    order.setDeclareUnit(customsBrokerInfo.getDepartcd()); // 申报单位海关代码
                    order.setDeclareUnitSocialCode(customsBrokerInfo.getUnifiedSocialCreditCode()); // 申报单位社会统一信用代码
                    order.setDeclareUnitName(customsBrokerInfo.getCustomsBrokerName()); // 申报单位名称
                }
            }
            // 2024/10/17 15:24@ZHANGCHAO 追加/变更/完善：
            if (isBlank(order.getReceiver())) {
                order.setReceiver(order.getBuyerName());
                order.setReceiverId(order.getBuyer());
            }
            if (isBlank(order.getDomesticSuppliersInfoId())) {
                order.setDomesticSuppliersInfoId(order.getOverseasPayerInfoId());
                order.setDomesticSuppliersInfoName(order.getOverseasPayerInfoName());
            }
            BigDecimal pcs = BigDecimal.ZERO;
            BigDecimal gw = BigDecimal.ZERO;
            BigDecimal nw = BigDecimal.ZERO;
            if (isNotEmpty(orderInfo.getProductList())) {
                for (OrderProductInfo orderProductInfo : orderInfo.getProductList()) {
                    if (isNotEmpty(orderProductInfo.getShipmentQuantity())) {
                        pcs = pcs.add(orderProductInfo.getShipmentQuantity());
                    }
                    if (isNotEmpty(orderProductInfo.getGrossWeight())) {
                        gw = gw.add(orderProductInfo.getGrossWeight());
                    }
                    if (isNotEmpty(orderProductInfo.getNetWeight())) {
                        nw = nw.add(orderProductInfo.getNetWeight());
                    }
                }
                order.setPcs(pcs);
                order.setGw(gw);
                order.setNw(nw);
            }
            boolean oResult = super.save(order);
            if(!oResult){
                throw new JeecgBootException("保存失败，请重新录入！！！");
            } else {
                orderInfoId = order.getId();
                if(orderInfo.getLogFlag() != null && orderInfo.getLogFlag()) {
                    // 订单状态
                    String orderStatus = operationLogInfoService.queryDictTextByKey("order_status",orderInfo.getOrderStatus().toString());
                    // 添加业务日志
                    operationLogInfoService.addLog("创建出口订单，订单协议号：" + orderInfo.getOrderProtocolNo() + "，订单状态："+ orderStatus, CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_2, null);
                }
            }
        } else {
            HashMap<String, Object> mapO = new HashMap<>();
            mapO.put("tenantId", tenantId);
            mapO.put("updateBy", isNotEmpty(loginUser) ? loginUser.getUsername() : "openApi");
            mapO.put("updateTime", orderInfo.getUpdateTime());
            mapO.put("supervisionMode", orderInfo.getSupervisionMode());
            if(orderInfo.getLogFlag() != null && orderInfo.getLogFlag()){
                mapO.put("orderProtocolNo", orderInfo.getOrderProtocolNo());
            }
            // 处理委托方
            if (isBlank(orderInfo.getBuyer()) && isNotBlank(orderInfo.getBuyerName())) {
                List<Commissioner> commissioners = commissionerMapper.selectList(new LambdaQueryWrapper<Commissioner>()
                        .eq(Commissioner::getCommissionerFullName, orderInfo.getBuyerName()));
                if (isNotEmpty(commissioners)) {
                    orderInfo.setBuyer(commissioners.get(0).getId());
                }
            } else {
                if (isBlank(orderInfo.getBuyerName())) {
                    Commissioner commissioner = commissionerMapper.selectById(orderInfo.getBuyer());
                    if (isNotEmpty(commissioner)) {
                        orderInfo.setBuyerName(commissioner.getCommissionerFullName());
                    }
                    if (isBlank(orderInfo.getBuyerName())) {
                        String tenantName = null;
                        try {
                            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                            Result<Tenant> tenant = sysBaseApi.getTenantById(orderInfo.getBuyer());
                            if (isNotEmpty(tenant.getResult())) {
                                tenantName = tenant.getResult().getName();
                            }
                        } catch (Exception e) {
                            log.error("获取租户名出现异常：{}", e.getMessage());
                        }
                        orderInfo.setBuyerName(tenantName);
                    }
                }
            }
            // 处理供应商-进口 或 买方/付款方-出口
            if (isBlank(orderInfo.getOverseasPayerInfoId()) && isNotBlank(orderInfo.getOverseasPayerInfoName())) {
                if ("I".equals(orderInfo.getIeFlag())) {
                    List<DomesticSuppliersInfo> domesticSuppliersInfos = domesticSuppliersInfoService.list(new LambdaQueryWrapper<DomesticSuppliersInfo>()
                            .eq(DomesticSuppliersInfo::getSuppliersFullName, orderInfo.getOverseasPayerInfoName()));
                    if (isNotEmpty(domesticSuppliersInfos)) {
                        orderInfo.setOverseasPayerInfoId(domesticSuppliersInfos.get(0).getId());
                    }
                } else if ("E".equals(orderInfo.getIeFlag())) {
                    List<OverseasPayerInfo> overseasPayerInfos = overseasPayerInfoService.list(new LambdaQueryWrapper<OverseasPayerInfo>()
                            .eq(OverseasPayerInfo::getOverseasPayerName, orderInfo.getOverseasPayerInfoName()));
                    if (isNotEmpty(overseasPayerInfos)) {
                        orderInfo.setOverseasPayerInfoId(overseasPayerInfos.get(0).getId());
                    }
                }
            } else {
                if (isBlank(orderInfo.getOverseasPayerInfoName())) {
                    if ("I".equals(orderInfo.getIeFlag())) {
                        DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(orderInfo.getOverseasPayerInfoId());
                        if (isNotEmpty(domesticSuppliersInfo)) {
                            orderInfo.setOverseasPayerInfoName(domesticSuppliersInfo.getSuppliersFullName());
                        }
                    } else if ("E".equals(orderInfo.getIeFlag())) {
                        OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
                        if (isNotEmpty(overseasPayerInfo)) {
                            orderInfo.setOverseasPayerInfoName(overseasPayerInfo.getOverseasPayerName());
                        }
                    }
                }
            }
            // 2024/9/30 11:23@ZHANGCHAO 追加/变更/完善：报关行完善些数据！
            if (isNotBlank(orderInfo.getCustomsBrokerId())) {
                CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoMapper.selectById(orderInfo.getCustomsBrokerId());
                if (isNotEmpty(customsBrokerInfo)) {
                    orderInfo.setDeclareUnit(customsBrokerInfo.getDepartcd()); // 申报单位海关代码
                    orderInfo.setDeclareUnitSocialCode(customsBrokerInfo.getUnifiedSocialCreditCode()); // 申报单位社会统一信用代码
                    orderInfo.setDeclareUnitName(customsBrokerInfo.getCustomsBrokerName()); // 申报单位名称
                }
            }
            // 2024/10/17 15:24@ZHANGCHAO 追加/变更/完善：
            if (isBlank(orderInfo.getReceiver())) {
                orderInfo.setReceiver(orderInfo.getBuyerName());
                orderInfo.setReceiverId(orderInfo.getBuyer());
            }
            if (isBlank(orderInfo.getDomesticSuppliersInfoId())) {
                orderInfo.setDomesticSuppliersInfoId(orderInfo.getOverseasPayerInfoId());
                orderInfo.setDomesticSuppliersInfoName(orderInfo.getOverseasPayerInfoName());
            }
            BigDecimal pcs = BigDecimal.ZERO;
            BigDecimal gw = BigDecimal.ZERO;
            BigDecimal nw = BigDecimal.ZERO;
            if (isNotEmpty(orderInfo.getProductList())) {
                for (OrderProductInfo orderProductInfo : orderInfo.getProductList()) {
                    if (isNotEmpty(orderProductInfo.getShipmentQuantity())) {
                        pcs = pcs.add(orderProductInfo.getShipmentQuantity());
                    }
                    if (isNotEmpty(orderProductInfo.getGrossWeight())) {
                        gw = gw.add(orderProductInfo.getGrossWeight());
                    }
                    if (isNotEmpty(orderProductInfo.getNetWeight())) {
                        nw = nw.add(orderProductInfo.getNetWeight());
                    }
                }
                orderInfo.setPcs(pcs);
                orderInfo.setGw(gw);
                orderInfo.setNw(nw);
            }
            saveOrderInfo(orderInfo, mapO);
        }

//        保存订单运输信息表
        HashMap<String, Object> mapT = new HashMap<>();
        mapT.put("orderInfoId", orderInfoId);
        mapT.put("tenantId", tenantId);
        mapT.put("updateTime", orderInfo.getTUpdateTime());
        saveTransportationInfo(orderInfo, mapT);

        if (orderInfo.getOrderStatus() >= 1) {
//            保存订舱信息表
            HashMap<String, Object> mapS = new HashMap<>();
            mapS.put("orderInfoId", orderInfoId);
            mapS.put("tenantId", tenantId);
            mapS.put("updateTime", orderInfo.getSUpdateTime());
            saveShippingBookingInfo(orderInfo, mapS);

//            保存入货通知信息表
            HashMap<String, Object> mapR = new HashMap<>();
            mapR.put("orderInfoId", orderInfoId);
            mapR.put("tenantId", tenantId);
            mapR.put("updateTime", orderInfo.getRUpdateTime());
            saveReceiptNoticeInfo(orderInfo, mapR);
            if (orderInfo.getOrderStatus()>=2) {
//                保存报关信息表
                HashMap<String, Object> mapC = new HashMap<>();
                mapC.put("orderInfoId", orderInfoId);
                mapC.put("tenantId", tenantId);
                mapC.put("updateTime", orderInfo.getCUpdateTime());
                saveCustomsDecExportInfo(orderInfo, mapC);
            }
        }

//        保存订单商品列表
        saveProductInfo(orderInfo, orderInfoId, Long.valueOf(tenantId));

//        保存附件列表
        saveAttachmentsInfo(orderInfo.getAttachmentList(), orderInfoId, Long.valueOf(tenantId));

        // 保存装箱信息表
        saveOrderPackingInfo(orderInfo.getPackingList(), orderInfoId);

        // 2023/10/17 15:42@ZHANGCHAO 追加/变更/完善：回填订单数据！！
//        if (isNotBlank(orderInfo.getContractId())) {
//            contractService.update(null, new UpdateWrapper<Contract>().lambda()
//                    .set(Contract::getAddOrderFlag, "1")
//                    .eq(Contract::getId, orderInfo.getContractId()));
//        }

        return orderInfoId;
    }

    private void saveOrderPackingInfo(List<OrderPackingInfo> packingList, String orderInfoId) {
        if (isNotEmpty(packingList)) {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            List<OrderPackingInfo> oldPackingList = packingInfoMapper.selectList(new LambdaQueryWrapper<OrderPackingInfo>()
                    .eq(OrderPackingInfo::getOrderInfoId, orderInfoId));
            if (isNotEmpty(oldPackingList)) {
                // 使用 Stream 进行过滤
                List<OrderPackingInfo> dels = oldPackingList.stream()
                        .filter(item -> packingList.stream().filter(i -> isNotEmpty(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (OrderPackingInfo orderPackingInfo : dels) {
                        packingInfoMapper.deleteById(orderPackingInfo.getId());
                    }
                }
            }
            for (OrderPackingInfo orderPackingInfo : packingList) {
                if (isBlank(orderPackingInfo.getId())) {
                    orderPackingInfo.setOrderInfoId(orderInfoId);
                    orderPackingInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    orderPackingInfo.setCreateTime(new Date());
                    packingInfoMapper.insert(orderPackingInfo);
                } else {
                    orderPackingInfo.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                    orderPackingInfo.setUpdateTime(new Date());
                    packingInfoMapper.updateById(orderPackingInfo);
                }
            }
        }
    }

    /**
     * 通过id查询
     * 查询订单概览信息
     * @param orderInfoId
     * @return
     */
    @Override
    public OrderOverviewInfoBiz getOverviewInfoByOrderInfoId(String orderInfoId) {
        OrderOverviewInfoBiz overviewInfo;
        String tenantId = TenantContext.getTenant();
//        获取订单概览信息
        overviewInfo = orderInfoMapper.getOverviewInfoByOrderInfoId(orderInfoId, Long.valueOf(tenantId));
//        获取订单商品信息
        Map<String, Object> mapP = new HashMap<>();
        mapP.put("order_info_id",orderInfoId);
        mapP.put("tenant_id",tenantId);
        mapP.put("del_flag",CommonConstant.DEL_FLAG_0);
        List<OrderProductInfo> productList = orderProductInfoService.listByMap(mapP);
        overviewInfo.setProductList(productList);
        if (productList != null && productList.size()>0) {
            Integer totalQuantity = 0;
            BigDecimal totalGrossWeight = BigDecimal.valueOf(0);
            BigDecimal totalNetWeight = BigDecimal.valueOf(0);
            String firstShipmentPackingType = productList.get(0).getShipmentPackingType();
            for (OrderProductInfo product : productList) {
                if (product.getShipmentPackagesNumbers() != null) {
                    totalQuantity += product.getShipmentPackagesNumbers();
                }
                if (product.getGrossWeight() != null) {
                    totalGrossWeight = totalGrossWeight.add(product.getGrossWeight());
                }
                if (product.getNetWeight() != null) {
                    totalNetWeight = totalNetWeight.add(product.getNetWeight());
                }
            }
            overviewInfo.setFirstShipmentPackingType(firstShipmentPackingType);
            overviewInfo.setTotalQuantity(totalQuantity);
            if (overviewInfo.getTotalGrossWeight() == null) {
                overviewInfo.setTotalGrossWeight(totalGrossWeight);
            }
            if (overviewInfo.getTotalNetWeight() == null) {
                overviewInfo.setTotalNetWeight(totalNetWeight);
            }
        }

        return overviewInfo;
    }

    /**
     * 拷贝新增
     * @param orderBaseInfo
     * @param orderInfoIdOld
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveCopyOrderInfo(OrderBaseInfoBiz orderBaseInfo, String orderInfoIdOld) {
        orderBaseInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
//        设置默认值
        if (orderBaseInfo.getOrderStatus() == null) {
            orderBaseInfo.setOrderStatus(0);
        }
        if (orderBaseInfo.getReceiveMoneyFlag() == null) {
            orderBaseInfo.setReceiveMoneyFlag(0);
        }
        if (orderBaseInfo.getPayMoneyFlag() == null) {
            orderBaseInfo.setPayMoneyFlag(0);
        }
        if (orderBaseInfo.getHasPayMoneyFlag() == null) {
            orderBaseInfo.setHasPayMoneyFlag(0);
        }
        String tenantId = TenantContext.getTenant();

        boolean oResult;
        OrderInfo orderInfo = new OrderInfo();
        BeanUtils.copyProperties(orderBaseInfo, orderInfo);
        orderInfo.setId(null);
        boolean isCG = "1".equals(orderInfo.getOrderType());
        String orderNo = serialNumberService.getSerialnumberByCustomerCode(!isCG ? "PO" : "CO", 4);
        orderInfo.setOrderProtocolNo(orderNo);
//        新建订单信息
        orderInfo.setTenantId(Long.valueOf(tenantId));
        orderInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
        orderInfo.setUpdateTime(new Date());
        orderInfo.setRemindStatus(0);
        orderInfo.setIncomingNoticeFlag(0);
        oResult = super.save(orderInfo);
        if(!oResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }else {
            // 订单状态
            String orderStatus = operationLogInfoService.queryDictTextByKey("order_status",orderInfo.getOrderStatus().toString());
            // 添加业务日志
            operationLogInfoService.addLog( "创建出口订单，订单协议号：" + orderInfo.getOrderProtocolNo() + "，订单状态：" + orderStatus,CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_2,null);
        }

        OrderTransportationInfo orderTransportation = orderTransportationInfoService.getOne(new LambdaQueryWrapper<OrderTransportationInfo>()
                .eq(OrderTransportationInfo::getOrderInfoId, orderInfoIdOld)
                .eq(OrderTransportationInfo::getTenantId, tenantId)
                .eq(OrderTransportationInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
//        新建订单运输信息
        if (orderTransportation == null) {
            orderTransportation = new OrderTransportationInfo();
        }
        boolean tResult;
        orderTransportation.setId(null);
        orderTransportation.setCreateBy(null);
        orderTransportation.setCreateTime(null);
        orderTransportation.setUpdateTime(new Date());
        orderTransportation.setTenantId(Long.valueOf(tenantId));
        orderTransportation.setOrderInfoId(orderInfo.getId());
        orderTransportation.setShippingType(orderBaseInfo.getShippingType());
        orderTransportation.setDeparturePort(orderBaseInfo.getDeparturePort());
        orderTransportation.setOverflowRate(orderBaseInfo.getOverflowRate());
        orderTransportation.setCountryArrival(orderBaseInfo.getCountryArrival());
        orderTransportation.setPortDestination(orderBaseInfo.getPortDestination());
        orderTransportation.setTotalNameGoods(orderBaseInfo.getTotalNameGoods());
        orderTransportation.setLogisticsServiceMode(orderBaseInfo.getLogisticsServiceMode());
        orderTransportation.setShippingMark(orderBaseInfo.getShippingMark());
        orderTransportation.setInsuranceCoefficient(orderBaseInfo.getInsuranceCoefficient());
        orderTransportation.setInsuranceRate(orderBaseInfo.getInsuranceRate());
        tResult = orderTransportationInfoService.save(orderTransportation);
        if(!tResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }

        OrderShippingBookingInfo orderShippingBooking = orderShippingBookingInfoService.getOne(new LambdaQueryWrapper<OrderShippingBookingInfo>()
                .eq(OrderShippingBookingInfo::getOrderInfoId, orderInfoIdOld)
                .eq(OrderShippingBookingInfo::getTenantId, tenantId)
                .eq(OrderShippingBookingInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderShippingBooking != null) {
//            新建订单订舱委托书
            boolean sResult;
            orderShippingBooking.setId(null);
            orderShippingBooking.setCreateBy(null);
            orderShippingBooking.setCreateTime(null);
            orderShippingBooking.setUpdateTime(new Date());
            orderShippingBooking.setOrderInfoId(orderInfo.getId());
            sResult = orderShippingBookingInfoService.save(orderShippingBooking);
            if(!sResult){
                throw new JeecgBootException("保存失败，请重新录入！！！");
            }
        }

        OrderReceiptNoticeInfo orderReceiptNotice = orderReceiptNoticeInfoService.getOne(new LambdaQueryWrapper<OrderReceiptNoticeInfo>()
                .eq(OrderReceiptNoticeInfo::getOrderInfoId, orderInfoIdOld)
                .eq(OrderReceiptNoticeInfo::getTenantId, tenantId)
                .eq(OrderReceiptNoticeInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderReceiptNotice != null) {
            boolean rResult;
//            新建订单入货通知单
            orderReceiptNotice.setId(null);
            orderReceiptNotice.setCreateBy(null);
            orderReceiptNotice.setCreateTime(null);
            orderReceiptNotice.setUpdateTime(new Date());
            orderReceiptNotice.setOrderInfoId(orderInfo.getId());
            orderReceiptNotice.setTotalRoughWeight(null);
            orderReceiptNotice.setTotalSpecifications(null);
            rResult = orderReceiptNoticeInfoService.save(orderReceiptNotice);
            if(!rResult){
                throw new JeecgBootException("保存失败，请重新录入！！！");
            }
        }

        OrderCustomsDecExportInfo orderCustomsDecExport = orderCustomsDecExportInfoService.getOne(new LambdaQueryWrapper<OrderCustomsDecExportInfo>()
                .eq(OrderCustomsDecExportInfo::getOrderInfoId, orderInfoIdOld)
                .eq(OrderCustomsDecExportInfo::getTenantId, tenantId)
                .eq(OrderCustomsDecExportInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderCustomsDecExport != null) {
//            新建订单报关单
            boolean cResult;
            orderCustomsDecExport.setId(null);
            orderCustomsDecExport.setCreateBy(null);
            orderCustomsDecExport.setCreateTime(null);
            orderCustomsDecExport.setUpdateTime(new Date());
            orderCustomsDecExport.setOrderInfoId(orderInfo.getId());
            orderCustomsDecExport.setTotalGrossWeight(null);
            orderCustomsDecExport.setTotalNetWeight(null);
            cResult = orderCustomsDecExportInfoService.save(orderCustomsDecExport);
            if(!cResult){
                throw new JeecgBootException("保存失败，请重新录入！！！");
            }
        }

        if (orderBaseInfo.getProductList() != null && !orderBaseInfo.getProductList().isEmpty()) {
            boolean pResult = true;
            List<OrderProductInfo> addList = new ArrayList<>();
            for (OrderProductInfo orderProductInfo : orderBaseInfo.getProductList()) {
                if (orderProductInfo.getId() == null || orderProductInfo.getId().equals(orderProductInfo.getProductId())) {
//                    新建订单商品信息
                    orderProductInfo.setId(null);
                    orderProductInfo.setCreateBy(null);
                    orderProductInfo.setCreateTime(null);
                    orderProductInfo.setOrderInfoId(orderInfo.getId());
                    orderProductInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
                    orderProductInfo.setUpdateTime(new Date());
                    addList.add(orderProductInfo);
                }
            }
            if (addList.size() > 0) {
                pResult = orderProductInfoService.saveBatch(addList);
            }
            if (!pResult) {
                throw new JeecgBootException("保存失败，请重新录入！！！");
            }
        }

        saveAttachmentsInfo(orderBaseInfo.getAttachmentList(), orderInfo.getId(), Long.valueOf(tenantId));

        return orderInfo.getId();
    }

    /**
     * 保存订单信息表
     * @param entity
     * @param map
     * @param <T>
     */
    private <T> void saveOrderInfo(T entity, HashMap<String, Object> map) {
        Date updateTime = (Date) map.get("updateTime");
        String updateBy = (String) map.get("updateBy");
        String supervisionMode = (String) map.get("supervisionMode");
        String orderProtocolNo = map.get("orderProtocolNo") == null?"":(String) map.get("orderProtocolNo");
        boolean oResult = false;
        OrderInfo orderInfo = new OrderInfo();
        BeanUtils.copyProperties(entity, orderInfo);
        orderInfo.setUpdateBy(updateBy);
        if (isBlank(orderInfo.getOrderType())) {
            if ("I".equals(orderInfo.getIeFlag())) {
                orderInfo.setOrderType("2"); // 默认2进口采购
            } else if ("E".equals(orderInfo.getIeFlag())) {
                orderInfo.setOrderType("3"); // 默认3外销订单
            }
        }
        if (orderInfo.getId() != null) {
//            编辑
            if (!"9710".equals(supervisionMode)) {
                // 非9710模式
                orderInfo.setOrderNoNineSeven(null);
                orderInfo.setOverseasWarehousePlatform(null);
            }
            if (!"9810".equals(supervisionMode)) {
                // 非9810模式
                orderInfo.setOrderStockNo(null);
                orderInfo.setOverseasWarehouseName(null);
                orderInfo.setOverseasWarehouseCountry(null);
                orderInfo.setOverseasWarehouseAddress(null);
            }
            LambdaUpdateWrapper<OrderInfo> updateWrapper = new UpdateWrapper().lambda();
//            updateWrapper.last(CommonUtils.SetUpdateSqlCondition(updateTime, orderInfo.getId()));
            updateWrapper.eq(OrderInfo::getId, orderInfo.getId());
            oResult = super.update(orderInfo, updateWrapper);
        }
        if(!oResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }else {
            if(isNotBlank(orderProtocolNo)){
                // 订单状态
                String orderStatus = operationLogInfoService.queryDictTextByKey("order_status",orderInfo.getOrderStatus().toString());
                // 添加业务日志
                operationLogInfoService.addLog( "更新出口订单，订单协议号：" + orderInfo.getOrderProtocolNo() + "，订单状态：" + orderStatus,CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3,null);
            }
        }
    }

    /**
     * 保存订单运输信息表
     * @param entity
     * @param map
     * @param <T>
     */
    private <T> void saveTransportationInfo(T entity, HashMap<String, Object> map) {
        String orderInfoId = (String) map.get("orderInfoId");
        String tenantId = (String) map.get("tenantId");
        Date updateTime = (Date) map.get("updateTime");
        boolean tResult;
        OrderTransportationInfo orderTransportationInfo = new OrderTransportationInfo();
        BeanUtils.copyProperties(entity, orderTransportationInfo);
        OrderTransportationInfo orderTransportation = orderTransportationInfoService.getOne(new LambdaQueryWrapper<OrderTransportationInfo>()
                .eq(OrderTransportationInfo::getOrderInfoId, orderInfoId)
                .eq(OrderTransportationInfo::getTenantId, tenantId)
                .eq(OrderTransportationInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        orderTransportationInfo.setOrderInfoId(orderInfoId);

        if (orderTransportation == null) {
//            新建订单运输信息
            orderTransportationInfo.setId(null);
            orderTransportationInfo.setTenantId(Long.valueOf(tenantId));
            orderTransportationInfo.setCreateBy(null);
            orderTransportationInfo.setCreateTime(null);
            orderTransportationInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            orderTransportationInfo.setUpdateTime(new Date());
            tResult = orderTransportationInfoService.save(orderTransportationInfo);
        } else {
//            编辑
            orderTransportationInfo.setId(orderTransportation.getId());
            orderTransportationInfo.setCreateBy(orderTransportation.getCreateBy());
            orderTransportationInfo.setCreateTime(orderTransportation.getCreateTime());
            orderTransportationInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            LambdaUpdateWrapper<OrderTransportationInfo> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.last(CommonUtils.SetUpdateSqlCondition(updateTime, orderTransportationInfo.getId()));
            tResult = orderTransportationInfoService.update(orderTransportationInfo, updateWrapper);
        }
        if(!tResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }
    }

    /**
     * 保存订单出货订舱委托书信息表
     * @param entity
     * @param map
     * @param <T>
     */
    private <T> void saveShippingBookingInfo(T entity, HashMap<String, Object> map) {
        String orderInfoId = (String) map.get("orderInfoId");
        String tenantId = (String) map.get("tenantId");
        Date updateTime = (Date) map.get("updateTime");
        boolean sResult;
        OrderShippingBookingInfo orderShippingBookingInfo = new OrderShippingBookingInfo();
        BeanUtils.copyProperties(entity, orderShippingBookingInfo);
        OrderShippingBookingInfo orderShippingBooking = orderShippingBookingInfoService.getOne(new LambdaQueryWrapper<OrderShippingBookingInfo>()
                .eq(OrderShippingBookingInfo::getOrderInfoId, orderInfoId)
                .eq(OrderShippingBookingInfo::getTenantId, tenantId)
                .eq(OrderShippingBookingInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        orderShippingBookingInfo.setOrderInfoId(orderInfoId);
        if (orderShippingBooking == null) {
//            新建订单订舱委托书
            orderShippingBookingInfo.setId(null);
            orderShippingBookingInfo.setTenantId(Long.valueOf(tenantId));
            orderShippingBookingInfo.setCreateBy(null);
            orderShippingBookingInfo.setCreateTime(null);
            orderShippingBookingInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            orderShippingBookingInfo.setUpdateTime(new Date());
            sResult = orderShippingBookingInfoService.save(orderShippingBookingInfo);
        } else {
//            编辑
            orderShippingBookingInfo.setId(orderShippingBooking.getId());
            orderShippingBookingInfo.setCreateBy(orderShippingBooking.getCreateBy());
            orderShippingBookingInfo.setCreateTime(orderShippingBooking.getCreateTime());
            orderShippingBookingInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            LambdaUpdateWrapper<OrderShippingBookingInfo> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.last(CommonUtils.SetUpdateSqlCondition(updateTime, orderShippingBookingInfo.getId()));
            sResult = orderShippingBookingInfoService.update(orderShippingBookingInfo, updateWrapper);
        }
        if(!sResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }
    }

    /**
     * 保存订单入货通知单信息表
     * @param entity
     * @param map
     * @param <T>
     */
    private <T> void saveReceiptNoticeInfo(T entity, HashMap<String, Object> map) {
        String orderInfoId = (String) map.get("orderInfoId");
        String tenantId = (String) map.get("tenantId");
        Date updateTime = (Date) map.get("updateTime");
        boolean rResult;
        OrderReceiptNoticeInfo orderReceiptNoticeInfo = new OrderReceiptNoticeInfo();
        BeanUtils.copyProperties(entity, orderReceiptNoticeInfo);
        OrderReceiptNoticeInfo orderReceiptNotice = orderReceiptNoticeInfoService.getOne(new LambdaQueryWrapper<OrderReceiptNoticeInfo>()
                .eq(OrderReceiptNoticeInfo::getOrderInfoId, orderInfoId)
                .eq(OrderReceiptNoticeInfo::getTenantId, tenantId)
                .eq(OrderReceiptNoticeInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        orderReceiptNoticeInfo.setOrderInfoId(orderInfoId);
        if (orderReceiptNotice == null) {
//            新建订单入货通知单
            orderReceiptNoticeInfo.setId(null);
            orderReceiptNoticeInfo.setTenantId(Long.valueOf(tenantId));
            orderReceiptNoticeInfo.setCreateBy(null);
            orderReceiptNoticeInfo.setCreateTime(null);
            orderReceiptNoticeInfo.setUpdateTime(new Date());
            orderReceiptNoticeInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            rResult = orderReceiptNoticeInfoService.save(orderReceiptNoticeInfo);
        } else {
//            编辑
            orderReceiptNoticeInfo.setId(orderReceiptNotice.getId());
            orderReceiptNoticeInfo.setCreateBy(orderReceiptNotice.getCreateBy());
            orderReceiptNoticeInfo.setCreateTime(orderReceiptNotice.getCreateTime());
            orderReceiptNoticeInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            LambdaUpdateWrapper<OrderReceiptNoticeInfo> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.last(CommonUtils.SetUpdateSqlCondition(updateTime, orderReceiptNoticeInfo.getId()));
            rResult = orderReceiptNoticeInfoService.update(orderReceiptNoticeInfo, updateWrapper);
        }
        if(!rResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }
    }

    /**
     * 保存订单出口货物报关信息表
     * @param entity
     * @param map
     * @param <T>
     */
    private <T> void saveCustomsDecExportInfo(T entity, HashMap<String, Object> map) {
        String orderInfoId = (String) map.get("orderInfoId");
        String tenantId = (String) map.get("tenantId");
        Date updateTime = (Date) map.get("updateTime");
        boolean cResult;
        OrderCustomsDecExportInfo orderCustomsDecExportInfo = new OrderCustomsDecExportInfo();
        BeanUtils.copyProperties(entity, orderCustomsDecExportInfo);
        OrderCustomsDecExportInfo orderCustomsDecExport = orderCustomsDecExportInfoService.getOne(new LambdaQueryWrapper<OrderCustomsDecExportInfo>()
                .eq(OrderCustomsDecExportInfo::getOrderInfoId, orderInfoId)
                .eq(OrderCustomsDecExportInfo::getTenantId, tenantId)
                .eq(OrderCustomsDecExportInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        orderCustomsDecExportInfo.setOrderInfoId(orderInfoId);
        if (orderCustomsDecExport == null) {
//            新建订单报关单
            orderCustomsDecExportInfo.setId(null);
            orderCustomsDecExportInfo.setTenantId(Long.valueOf(tenantId));
            orderCustomsDecExportInfo.setCreateBy(null);
            orderCustomsDecExportInfo.setCreateTime(null);
            orderCustomsDecExportInfo.setUpdateTime(new Date());
            orderCustomsDecExportInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            cResult = orderCustomsDecExportInfoService.save(orderCustomsDecExportInfo);
        } else {
//            编辑
            orderCustomsDecExportInfo.setId(orderCustomsDecExport.getId());
            orderCustomsDecExportInfo.setCreateBy(orderCustomsDecExport.getCreateBy());
            orderCustomsDecExportInfo.setCreateTime(orderCustomsDecExport.getCreateTime());
            orderCustomsDecExportInfo.setRemarks(null); // 传入的remarks为订单表的remarks，避免被覆盖
            LambdaUpdateWrapper<OrderCustomsDecExportInfo> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.last(CommonUtils.SetUpdateSqlCondition(updateTime, orderCustomsDecExportInfo.getId()));
            cResult = orderCustomsDecExportInfoService.update(orderCustomsDecExportInfo, updateWrapper);
        }
        if(!cResult){
            throw new JeecgBootException("保存失败，请重新录入！！！");
        }
    }

    /**
     * 保存订单商品信息表
     * @param orderInfo
     * @param orderInfoId
     */
    public void saveProductInfo(OrderInfoBiz orderInfo, String orderInfoId, Long tenantId) {
        List<OrderProductInfo> productInfoList = orderInfo.getProductList();
        boolean pResult = true;
        Map<String, Object> mapP = new HashMap<>();
        mapP.put("order_info_id",orderInfoId);
//        mapP.put("tenant_id",tenantId);
//        mapP.put("del_flag",CommonConstant.DEL_FLAG_0);
        List<OrderProductInfo> existList = orderProductInfoService.listByMap(mapP);
        if (existList != null && existList.size()>0) {
//            List<OrderProductInfo> deleteList = existList.stream()
//                    .filter(delProduct->!productInfoList.stream().map(all->all.getId()).collect(Collectors.toList()).contains(delProduct.getId()))
//                    .collect(Collectors.toList());
            // 使用 Stream 进行过滤
            List<OrderProductInfo> deleteList = existList.stream()
                    .filter(item -> productInfoList.stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                    .collect(Collectors.toList());
            if (deleteList.size()>0) {
                for (OrderProductInfo orderProductInfo : deleteList) {
                    //   删除
//                    orderProductInfo.setDelFlag(CommonConstant.DEL_FLAG_1);
//                    LambdaUpdateWrapper<OrderProductInfo> updateWrapper = new UpdateWrapper().lambda();
//                    updateWrapper.last(CommonUtils.SetUpdateSqlCondition(orderProductInfo.getUpdateTime(), orderProductInfo.getId()));
//                    updateWrapper.eq(OrderProductInfo::getId, orderProductInfo.getId());
//                    pResult = orderProductInfoService.update(orderProductInfo, updateWrapper);
                    // 2024/4/11 下午1:01@ZHANGCHAO 追加/变更/完善：彻底删除！！
                    pResult = orderProductInfoService.removeById(orderProductInfo.getId());
                }
//                if (!pResult) {
//                    throw new JeecgBootException("保存失败，请重新录入！！！");
//                }
            }
        }
        if (productInfoList != null && !productInfoList.isEmpty()) {
            List<OrderProductInfo> addList = new ArrayList<>();
//            List<OrderProductInfo> editList = new ArrayList<>();
            for(OrderProductInfo orderProductInfo : productInfoList) {
                if (isBlank(orderProductInfo.getId())) {
//                    新建订单商品信息
                    orderProductInfo.setId(null);
                    orderProductInfo.setTenantId(tenantId);
                    orderProductInfo.setCreateBy(null);
                    orderProductInfo.setCreateTime(null);
                    orderProductInfo.setOrderInfoId(orderInfoId);
                    orderProductInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
                    orderProductInfo.setUpdateTime(new Date());
                    orderProductInfo.setHscode(isNotBlank(orderProductInfo.getHscode()) ? orderProductInfo.getHscode() : orderProductInfo.getCustomsCodeInfoCode());
                    orderProductInfo.setHawb(orderInfo.getDeliveryNumbers()); // 赋值提运单号
                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册？？
//                    if (isNotBlank(orderInfo.getRecordNumber())) {
//                        OrderInfo orderInfo1 = new OrderInfo();
//                        BeanUtil.copyProperties(orderInfo, orderInfo1, CopyOptions.create().ignoreNullValue());
//                        takingDataFromTheAccountBook(orderProductInfo, orderInfo1);
//                    }
                    addList.add(orderProductInfo);
                } else {
//                    编辑
                    LambdaUpdateWrapper<OrderProductInfo> updateWrapper = new UpdateWrapper().lambda();
                    orderProductInfo.setUpdateTime(new Date());
                    orderProductInfo.setHscode(isNotBlank(orderProductInfo.getHscode()) ? orderProductInfo.getHscode() : orderProductInfo.getCustomsCodeInfoCode());
                    orderProductInfo.setHawb(orderInfo.getDeliveryNumbers()); // 赋值提运单号
//                    updateWrapper.last(CommonUtils.SetUpdateSqlCondition(orderProductInfo.getUpdateTime(), orderProductInfo.getId()));
                    updateWrapper.eq(OrderProductInfo::getId, orderProductInfo.getId());
                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册？？
//                    if (isNotBlank(orderInfo.getRecordNumber())) {
//                        OrderInfo orderInfo1 = new OrderInfo();
//                        BeanUtil.copyProperties(orderInfo, orderInfo1, CopyOptions.create().ignoreNullValue());
//                        takingDataFromTheAccountBook(orderProductInfo, orderInfo1);
//                    }
                    pResult = orderProductInfoService.update(orderProductInfo, updateWrapper);
                }
                if (!pResult) {
//                    throw new JeecgBootException("保存失败，请重新录入！！！");
                }
                // 回填订单商品信息执行数量
                if (isNotBlank(orderInfo.getFromBusiness())) {
                    if (isNotEmpty(orderProductInfo.getOrderDetailId())) {
                        OrderDetail orderDetail = orderDetailMapper.selectById(orderProductInfo.getOrderDetailId());
                        if (isNotEmpty(orderDetail)) {
                            // 原已发货+此订单已发货=现合同已发货
                            BigDecimal shippedQty = (isNotEmpty(orderDetail.getShippedQty()) ? orderDetail.getShippedQty() : BigDecimal.ZERO).add(orderProductInfo.getShipmentQuantity());
                            // 合同商品总 - 现合同已发货 = 现可执行
                            BigDecimal execQty = orderDetail.getQty().subtract(shippedQty);
                            orderDetailMapper.update(null, new UpdateWrapper<OrderDetail>().lambda()
                                    .set(OrderDetail::getShippedQty, shippedQty)
                                    .set(OrderDetail::getExecQty, execQty)
                                    .eq(OrderDetail::getId, orderDetail.getId()));
                        }
                    }
                }
            }
            if (addList.size() > 0) {
                pResult = orderProductInfoService.saveBatch(addList);
            }

            BigDecimal pcs = BigDecimal.ZERO;
            BigDecimal gw = BigDecimal.ZERO;
            BigDecimal nw = BigDecimal.ZERO;
            // 2023/10/17 15:48@ZHANGCHAO 追加/变更/完善：回填合同商品数据！！
            for (OrderProductInfo orderProductInfo : productInfoList) {
                if (isNotBlank(orderInfo.getFromOrder())) {
                    if (isNotEmpty(orderProductInfo.getContractGoodsId())) {
                        ContractGoods contractGoods = contractGoodsService.getById(orderProductInfo.getContractGoodsId());
                        if (isNotEmpty(contractGoods)) {
                            // 原已发货+此订单已发货=现合同已发货
                            BigDecimal shippedQty = (isNotEmpty(contractGoods.getShippedQty()) ? contractGoods.getShippedQty() : BigDecimal.ZERO).add(orderProductInfo.getShipmentQuantity());
                            // 合同商品总 - 现合同已发货 = 现可执行
                            BigDecimal execQty = contractGoods.getShipmentQuantity().subtract(shippedQty);
                            contractGoodsService.update(null, new UpdateWrapper<ContractGoods>().lambda()
                                    .set(ContractGoods::getShippedQty, shippedQty)
                                    .set(ContractGoods::getExecQty, execQty)
                                    .eq(ContractGoods::getId, contractGoods.getId()));
                        }
                    }
                }
                if (isNotEmpty(orderProductInfo.getShipmentQuantity())) {
                    pcs = pcs.add(orderProductInfo.getShipmentQuantity());
                }
                if (isNotEmpty(orderProductInfo.getGrossWeight())) {
                    gw = gw.add(orderProductInfo.getGrossWeight());
                }
                if (isNotEmpty(orderProductInfo.getNetWeight())) {
                    nw = nw.add(orderProductInfo.getNetWeight());
                }
            }
            if (!(isNotEmpty(orderInfo.getPcs()) && isNotEmpty(orderInfo.getGw()) && isNotEmpty(orderInfo.getNw()))) {
                orderInfoMapper.update(null, new UpdateWrapper<OrderInfo>().lambda()
                        .set(isEmpty(orderInfo.getPcs()), OrderInfo::getPcs, pcs)
                        .set(isEmpty(orderInfo.getGw()), OrderInfo::getGw, gw)
                        .set(isEmpty(orderInfo.getNw()), OrderInfo::getNw, nw)
                        .eq(OrderInfo::getId, orderInfoId));
            }
//            if (editList.size() > 0) {
//                pResult = orderProductInfoService.updateBatchById(editList);
//            }
            if (!pResult) {
//                throw new JeecgBootException("保存失败，请重新录入！！！");
            }
        }
    }

    /**
     * 保存附件信息表
     * @param attachmentsInfoList
     * @param orderInfoId
     * @param tenantId
     */
    private void saveAttachmentsInfo(List<AttachmentsInfo> attachmentsInfoList, String orderInfoId, Long tenantId) {
        if (attachmentsInfoList != null && !attachmentsInfoList.isEmpty()) {
            for(AttachmentsInfo attachmentsInfo : attachmentsInfoList) {
                boolean aResult;
                AttachmentsInfo atta = attachmentsInfoService.getOne(new LambdaQueryWrapper<AttachmentsInfo>()
                        .eq(AttachmentsInfo::getRelationId, orderInfoId)
                        .eq(AttachmentsInfo::getTenantId, tenantId)
                        .eq(AttachmentsInfo::getAttachmentsFileType, attachmentsInfo.getAttachmentsFileType())
                        .eq(AttachmentsInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
                if (atta == null) {
//                    新建附件信息
                    attachmentsInfo.setId(null);
                    attachmentsInfo.setTenantId(tenantId);
                    attachmentsInfo.setRelationId(orderInfoId);
                    attachmentsInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
                    attachmentsInfo.setUpdateTime(new Date());
                    aResult = attachmentsInfoService.save(attachmentsInfo);
                } else {
//                    编辑
                    attachmentsInfo.setId(atta.getId());
                    attachmentsInfo.setCreateBy(atta.getCreateBy());
                    attachmentsInfo.setCreateTime(atta.getCreateTime());
                    LambdaUpdateWrapper<AttachmentsInfo> updateWrapper = new UpdateWrapper().lambda();
                    updateWrapper.last(CommonUtils.SetUpdateSqlCondition(attachmentsInfo.getUpdateTime(), attachmentsInfo.getId()));
                    aResult = attachmentsInfoService.update(attachmentsInfo, updateWrapper);
                }
                if (!aResult) {
                    throw new JeecgBootException("保存失败！！！");
                }
            }
        }
    }

    /**
     * 通过id删除
     * 删除订单信息
     * @param orderInfoId
     * @param updateTime
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrder(String orderInfoId, String updateTime) {
        String tenantId = TenantContext.getTenant();
        OrderInfo orderInfo = super.getOne(new LambdaQueryWrapper<OrderInfo>()
                .eq(OrderInfo::getId, orderInfoId)
                .eq(OrderInfo::getTenantId, tenantId)
                .eq(OrderInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderInfo == null) {
            throw new JeecgBootException("订单不存在！！！");
        }
//        订单信息表删除
        boolean oResult;

//        LambdaUpdateWrapper<OrderInfo> updateWrapper = new UpdateWrapper().lambda();
//        updateWrapper.last(CommonUtils.SetUpdateSqlCondition(DateUtils.str2Date(updateTime, DateUtils.datetimeFormat.get()), orderInfoId));
//        orderInfo.setDelFlag(CommonConstant.DEL_FLAG_1);

//        oResult = super.update(orderInfo, updateWrapper);
//        if(!oResult){
//            throw new JeecgBootException("删除失败！！！");
//        }else {
            // 订单状态
//            String orderStatus = operationLogInfoService.queryDictTextByKey("order_status",orderInfo.getOrderStatus().toString());
            // 添加业务日志
//            operationLogInfoService.addLog( "删除出口订单，订单协议号：" + orderInfo.getOrderProtocolNo() + "，订单状态：" + orderStatus,CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_4,null);
//        }

//        订单运输信息表删除
        boolean tResult;
        OrderTransportationInfo orderTransportationInfo = orderTransportationInfoService.getOne(new LambdaQueryWrapper<OrderTransportationInfo>()
                .eq(OrderTransportationInfo::getOrderInfoId, orderInfoId)
                .eq(OrderTransportationInfo::getTenantId, tenantId)
                .eq(OrderTransportationInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderTransportationInfo != null) {
//            orderTransportationInfo.setDelFlag(CommonConstant.DEL_FLAG_1);
            tResult = orderTransportationInfoService.removeById(orderTransportationInfo);
//            if(!tResult){
//                throw new JeecgBootException("删除失败！！！");
//            }
        }

//        附件信息表删除
        List<AttachmentsInfo> attachmentsInfoList = attachmentsInfoService.list(new LambdaQueryWrapper<AttachmentsInfo>()
                .eq(AttachmentsInfo::getRelationId, orderInfoId)
                .eq(AttachmentsInfo::getTenantId, tenantId)
                .eq(AttachmentsInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (attachmentsInfoList != null && !attachmentsInfoList.isEmpty()) {
            boolean aResult;
            for(AttachmentsInfo attachmentsInfo : attachmentsInfoList) {
//                attachmentsInfo.setDelFlag(CommonConstant.DEL_FLAG_1);
                attachmentsInfoService.removeById(attachmentsInfo.getId());
            }
//            aResult = attachmentsInfoService.updateBatchById(attachmentsInfoList);
//            if (!aResult) {
//                throw new JeecgBootException("删除失败！！！");
//            }
        }

//        订单出货订舱委托书信息表删除
        boolean sResult;
        OrderShippingBookingInfo orderShippingBookingInfo = orderShippingBookingInfoService.getOne(new LambdaQueryWrapper<OrderShippingBookingInfo>()
                .eq(OrderShippingBookingInfo::getOrderInfoId, orderInfoId)
                .eq(OrderShippingBookingInfo::getTenantId, tenantId)
                .eq(OrderShippingBookingInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderShippingBookingInfo != null) {
            orderShippingBookingInfo.setDelFlag(CommonConstant.DEL_FLAG_1);
            sResult = orderShippingBookingInfoService.removeById(orderShippingBookingInfo.getId());
//            if(!sResult){
//                throw new JeecgBootException("删除失败！！！");
//            }
        }

//        订单入货通知单信息表删除
        boolean rResult;
        OrderReceiptNoticeInfo orderReceiptNotice = orderReceiptNoticeInfoService.getOne(new LambdaQueryWrapper<OrderReceiptNoticeInfo>()
                .eq(OrderReceiptNoticeInfo::getOrderInfoId, orderInfoId)
                .eq(OrderReceiptNoticeInfo::getTenantId, tenantId)
                .eq(OrderReceiptNoticeInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderReceiptNotice != null) {
//            orderReceiptNotice.setDelFlag(CommonConstant.DEL_FLAG_1);
            rResult = orderReceiptNoticeInfoService.removeById(orderReceiptNotice.getId());
//            if(!rResult){
//                throw new JeecgBootException("删除失败！！！");
//            }
        }

//        订单出口货物报关信息表删除
        boolean cResult;
        OrderCustomsDecExportInfo orderCustomsDecExport = orderCustomsDecExportInfoService.getOne(new LambdaQueryWrapper<OrderCustomsDecExportInfo>()
                .eq(OrderCustomsDecExportInfo::getOrderInfoId, orderInfoId)
                .eq(OrderCustomsDecExportInfo::getTenantId, tenantId)
                .eq(OrderCustomsDecExportInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderCustomsDecExport != null) {
//            orderCustomsDecExport.setDelFlag(CommonConstant.DEL_FLAG_1);
            cResult = orderCustomsDecExportInfoService.removeById(orderCustomsDecExport.getId());
//            if(!cResult){
//                throw new JeecgBootException("删除失败！！！");
//            }
        }

        // 2023/11/27 13:24@ZHANGCHAO 追加/变更/完善：回填订单数据
        List<OrderProductInfo> productInfoList = orderProductInfoService.list(new LambdaQueryWrapper<OrderProductInfo>()
                .eq(OrderProductInfo::getOrderInfoId, orderInfoId));

        if (isNotBlank(orderInfo.getContractId())) {
            List<OrderInfo> orderInfoList = this.list(new LambdaQueryWrapper<OrderInfo>()
                   .eq(OrderInfo::getContractId, orderInfo.getContractId()));
            if (isNotEmpty(orderInfoList)) {
                List<ContractGoods> contractGoodsList = contractGoodsService.list(new LambdaQueryWrapper<ContractGoods>()
                        .eq(ContractGoods::getContractId, orderInfo.getContractId()));
                if (isNotEmpty(contractGoodsList) && isNotEmpty(productInfoList)) {
                    Map<String, List<OrderProductInfo>> orderProductMap = productInfoList.stream().filter(i -> isNotBlank(i.getContractGoodsId())).collect(Collectors.groupingBy(OrderProductInfo::getContractGoodsId));
                    for (ContractGoods contractGoods : contractGoodsList) {
                        List<OrderProductInfo> orderProductInfoList = orderProductMap.get(contractGoods.getId());
                        if (isNotEmpty(orderProductInfoList)) {
                            BigDecimal execQty = orderProductInfoList.stream().map(OrderProductInfo::getShipmentQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal newExecQty = (isNotEmpty(contractGoods.getExecQty()) ? contractGoods.getExecQty() : BigDecimal.ZERO).add(execQty);
                            BigDecimal newShippedQty = (isNotEmpty(contractGoods.getShippedQty()) ? contractGoods.getShippedQty() : BigDecimal.ZERO).subtract(execQty);
                            contractGoodsService.update(null, new UpdateWrapper<ContractGoods>().lambda()
                                   .set(ContractGoods::getExecQty, newExecQty)
                                   .set(ContractGoods::getShippedQty, newShippedQty)
                                   .eq(ContractGoods::getId, contractGoods.getId()));
                        }
                    }
                }
//                if (orderInfoList.size() == 1 && orderInfoList.get(0).getId().equals(orderInfoId)) {
//                    contractService.update(null, new UpdateWrapper<Contract>().lambda()
//                            .set(Contract::getAddOrderFlag, "0")
//                            .eq(Contract::getId, orderInfo.getContractId()));
//                }
            }
        }

        // 回滚订单可执行数量
        if (isNotEmpty(productInfoList)) {
            for(OrderProductInfo orderProductInfo : productInfoList) {
                if (isNotBlank(orderProductInfo.getOrderDetailId())) {
                    OrderDetail orderDetail = orderDetailMapper.selectById(orderProductInfo.getOrderDetailId());
                    if (isNotEmpty(orderDetail)) {
                        // 可执行
                        BigDecimal newExecQty = (isNotEmpty(orderDetail.getExecQty()) ? orderDetail.getExecQty() : BigDecimal.ZERO).add(isNotEmpty(orderProductInfo.getShipmentQuantity()) ? orderProductInfo.getShipmentQuantity() : BigDecimal.ZERO);
                        // 已执行
                        BigDecimal newShippedQty = (isNotEmpty(orderDetail.getShippedQty()) ? orderDetail.getShippedQty() : BigDecimal.ZERO).subtract(isNotEmpty(orderProductInfo.getShipmentQuantity()) ? orderProductInfo.getShipmentQuantity() : BigDecimal.ZERO);
                        orderDetailMapper.update(null, new UpdateWrapper<OrderDetail>().lambda()
                                .set(OrderDetail::getShippedQty, newShippedQty)
                                .set(OrderDetail::getExecQty, newExecQty)
                                .eq(OrderDetail::getId, orderDetail.getId()));
                    }
                }
            }
        }

        // 订单商品信息表删除
        if (isNotEmpty(productInfoList)) {
            for(OrderProductInfo orderProductInfo : productInfoList) {
                orderProductInfoService.removeById(orderProductInfo.getId());
            }
        }

        super.removeById(orderInfoId);
    }

    @Override
    public EnterpriseInfo getEnterpriseInfoInfoId(Long tenantId) {
        EnterpriseInfo result = orderInfoMapper.getEnterpriseInfoInfoId(tenantId);
        return result;
    }

    @Override
    public String generateDec(String orderInfoId, HttpServletRequest request) throws Exception {
        OrderInfo orderInfo = baseMapper.selectById(orderInfoId);
        if (orderInfo == null){
            throw new Exception("订单信息为空,请刷新列表数据");
        }
        if (orderInfo.getDelFlag() == 1){
            throw new Exception("该订单已删除,请刷新列表数据");
        }
        DecHead decHead = new DecHead();
        generateDecHeadByOrder(orderInfo,decHead,request);
        // 获取登录用户信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        decHead.setCreatePerson(isNotEmpty(sysUser) ? sysUser.getUsername() : "");
        decHead.setCreateTime(new Date());
        String decId = decHeadService.saveDecHead(decHead);

        return decId;
    }

    @Override
    public DecHead generateDecHeadByOrderNo(String orderInfoNo, HttpServletRequest request) throws Exception {
        OrderInfo orderInfo = baseMapper.selectOne(new QueryWrapper<OrderInfo>().lambda()
                .eq(OrderInfo::getOrderProtocolNo, orderInfoNo));
        if (orderInfo == null){
            throw new Exception("订单信息为空,请刷新列表数据");
        }
        if (orderInfo.getDelFlag() == 1){
            throw new Exception("该订单已删除,请刷新列表数据");
        }
        DecHead decHead = new DecHead();
        generateDecHeadByOrder(orderInfo, decHead, request);
        return decHead;
    }

    /**
     * 获取令牌
     *
     * @param account  用户名
     * @param password 密码
     * @return 令牌结果
     */
    @Override
    public Result<?> getToken(String account, String password, boolean isCache) {
        String returnToken = (String) redisUtil.get(account + password);
        if (isNotBlank(returnToken) && isCache) {
            return Result.ok(returnToken);
        }
        JSONObject paramJson = new JSONObject();
        paramJson.put("Account", account);
        paramJson.put("PassWord", password);
        String bodyString = paramJson.toJSONString();
        log.info("bodyString==> " + bodyString);
        String httpResponse = HttpUtil.post("http://auth.feiliankeji.cn/Fly_BgApi/GetToken", paramJson);
        String token = "";
        if (isNotBlank(httpResponse)) {
            JSONObject jsonObject = JSONObject.parseObject(httpResponse);
            if (jsonObject.containsKey("code")) {
                String code = jsonObject.getString("code");
                if (code.equals("200")) {
                    token = jsonObject.getString("data");
                } else {
                    return Result.error("token获取失败！");
                }
            }
        }
        if (isNotBlank(token)) {
            redisUtil.set(account + password, token, 3600 * 24 * 7);
        }
        return Result.ok(token);
    }

    /**
     * 根据给定的mblno和yard获取Manifest的结果。
     *
     * @param mblno Manifest的编号
     * @param yard  货场的编号
     * @return 与给定的mblno和yard匹配的Manifest的结果
     */
    @Override
    public Result<?> getManifestResult(String mblno, String yard, String account, String password, String token) {
        String paraToken = token;
        if (isNotBlank(account) && isNotBlank(password)) {
            Result<?> result = getToken(account, password, false);
            if (result.isSuccess()) {
                paraToken = (String) result.getResult();
            }
        }
        JSONObject paramJson = new JSONObject();
        paramJson.put("MBLNO", mblno);
        paramJson.put("YARD", yard);
        String bodyString = paramJson.toJSONString();
        log.info("bodyString：" + bodyString);
        log.info("Token：" + paraToken);
        String response = HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/GetManifestResult")
                .header("Token", paraToken)//头信息，多个头信息多次调用此方法即可
                .form(paramJson)//表单内容
                .timeout(30000)//超时，毫秒
                .execute().body();
        log.info("===> " + response);
        JSONObject returnJson = new JSONObject();
        if (isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.containsKey("code")) {
                String code = jsonObject.getString("code");
                if (code.equals("200")) {
                    String data = jsonObject.getString("data");
                    returnJson = JSONObject.parseObject(data);
                } else {
                    return Result.error("未获取到场站信息！");
                }
            }
        }
        return Result.ok(returnJson);
    }

    /**
     * 根据订单信息编号生成CIQ数据。
     *
     * @param orderInfoNo 订单信息的编号，用于标识特定的订单。
     * @return 返回操作结果，其中包含生成CIQ数据的成功与否等信息。
     */
    @Override
    public Result<?> generateCiqByOrderNo(String orderInfoNo) {
        OrderInfo orderInfo = baseMapper.selectOne(new QueryWrapper<OrderInfo>().lambda()
                .eq(OrderInfo::getOrderProtocolNo, orderInfoNo));
        if (orderInfo == null){
            return Result.error("订单信息为空,请刷新列表数据");
        }
        if (orderInfo.getDelFlag() == 1){
            return Result.error("该订单已删除,请刷新列表数据");
        }
        OrderTransportationInfo orderTransportation;
        OrderTransportationInfo ot = orderTransportationInfoService.getOne(new LambdaQueryWrapper<OrderTransportationInfo>()
                .eq(OrderTransportationInfo::getOrderInfoId, orderInfo.getId()));
        orderTransportation = isNotEmpty(ot) ? ot : new OrderTransportationInfo();
        ItfDclIoDecl itfDclIoDecl = new ItfDclIoDecl();
        itfDclIoDecl.setOrderId(orderInfo.getId());
        itfDclIoDecl.setOrderProtocolNo(orderInfo.getOrderProtocolNo());
        itfDclIoDecl.setCiqIeFlag("24"); // 类别默认出境检验检疫
        String buyerName = "";
        String buyerNameEn = "";
        Commissioner commissioner = commissionerMapper.selectById(orderInfo.getBuyer());
        if (isNotEmpty(commissioner)) {
            buyerName = commissioner.getCommissionerFullName();
            buyerNameEn = commissioner.getCommissionerEnName();
        }
        List<CustomerEnterprise> customerEnterpriseList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, buyerName));
        if (isNotEmpty(customerEnterpriseList)) {
            itfDclIoDecl.setConsignorCode(customerEnterpriseList.get(0).getDepartcd()); // 境内收发货人 - 10位海关代码
        }
        itfDclIoDecl.setConsignorCname(buyerName); // 发货人名称（中文）
        itfDclIoDecl.setConsignorEname(buyerNameEn); // 发货人名称（外文）

        String customsBrokerId = "";
        if (isNotBlank(orderInfo.getCustomsBrokerId())) {
            CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoMapper.selectById(orderInfo.getCustomsBrokerId());
            if (isNotEmpty(customsBrokerInfo)) {
                customsBrokerId = customsBrokerInfo.getCustomsBrokerName();
            } else {
                customsBrokerId = orderInfo.getCustomsBrokerId();
            }
        }
        List<CustomerEnterprise> customsBrokerList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, customsBrokerId));
        if (isNotEmpty(customsBrokerList)) {
            itfDclIoDecl.setDeclRegNo(customsBrokerList.get(0).getDepartcd()); // 申请单位代码
        }
        itfDclIoDecl.setDeclRegName(customsBrokerId); // 申请单位名称
        itfDclIoDecl.setApplicationAcceptanceAuthority(orderInfo.getExitClearance()); // 申请受理机关
        itfDclIoDecl.setVsaOrgCode(orderInfo.getExitClearance()); // 领证机关
        itfDclIoDecl.setCiqDestCode(orderInfo.getExitClearance()); // 目的机关
        itfDclIoDecl.setInspOrgCode(orderInfo.getExitClearance()); // 口岸机关
        itfDclIoDecl.setDespPortCode(orderInfo.getPortDestination()); // 离境口岸
        itfDclIoDecl.setCiqTrafMode(orderTransportation.getShippingType()); // 运输方式
        itfDclIoDecl.setTrafName(orderTransportation.getTransportName()); // 运输工具名称
        itfDclIoDecl.setTradeModeCode("0110"); // 贸易方式
        itfDclIoDecl.setContractNo(orderInfo.getExportContractNo()); // 合同号
        itfDclIoDecl.setArrivPortCode(orderTransportation.getDeparturePort()); // 到达口岸
        itfDclIoDecl.setCiqTradeCountryCode(orderTransportation.getCountryArrival()); // 输往国家地区
        itfDclIoDecl.setGoodsPlace(buyerName); // 存放地 = 生产销售单位
        // 转换表体
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.list(new QueryWrapper<OrderProductInfo>().lambda()
                .eq(OrderProductInfo::getOrderInfoId, orderInfo.getId()));
        if (isNotEmpty(orderProductInfos)) {
//            ErpCurrencies erpCurrencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
//                    .eq(ErpCurrencies::getCode, orderInfo.getCustomsDeclarationCurrency()));
            List<ItfDclIoDeclGoods> itfDclIoDeclGoodsList = new ArrayList<>();
            int i = 1;
            for (OrderProductInfo orderProductInfo : orderProductInfos) {
                ItfDclIoDeclGoods itfDclIoDeclGoods = new ItfDclIoDeclGoods();
                itfDclIoDeclGoods.setItem(i);
                itfDclIoDeclGoods.setCodeTs(orderProductInfo.getCustomsCodeInfoCode()); // HS编码
                try {
                    if (isNotBlank(orderProductInfo.getCustomsCodeInfoCode())) {
                        ErpCiq erpCiq = erpCiqMapper.selectOne(new QueryWrapper<ErpCiq>().lambda()
                                .eq(ErpCiq::getCodeTs, orderProductInfo.getCustomsCodeInfoCode()));
                        if (isNotEmpty(erpCiq)) {
                            itfDclIoDeclGoods.setCiqName(erpCiq.getCiqName());
                        }
                    }
                } catch (Exception e) {
                    log.error("erpCiqMapper.selectOne error", e);
                }
                itfDclIoDeclGoods.setSupvDmd("P/Q"); // 监管状态
                itfDclIoDeclGoods.setGName(orderProductInfo.getChineseName()); // 货物名称
                itfDclIoDeclGoods.setEnName(orderProductInfo.getEnglishName()); // 商品英文名称
                itfDclIoDeclGoods.setGoodsSpec(orderProductInfo.getSpecificationModel()); // 货物规格
                itfDclIoDeclGoods.setGoodsModel(orderProductInfo.getSpecificationModel()); // 货物型号
                itfDclIoDeclGoods.setQty1(orderProductInfo.getLegalQuantity()); // HS标准量
                itfDclIoDeclGoods.setUnit1(orderProductInfo.getLegalUnitCode()); // HS标准量单位
                itfDclIoDeclGoods.setCiqQty(orderProductInfo.getShipmentQuantity()); // 申请数量
                itfDclIoDeclGoods.setCiqQtyMeasUnit(orderProductInfo.getShipmentUnit()); // 申请数量单位
                itfDclIoDeclGoods.setCiqWeight(orderProductInfo.getLegalQuantity()); // 申请重量
                itfDclIoDeclGoods.setCiqWtMeasUnit(orderProductInfo.getLegalUnitCode()); // 申请重量单位
                itfDclIoDeclGoods.setPricePerUnit(orderProductInfo.getShipmentUnitPrice()); // 单价
                itfDclIoDeclGoods.setGoodsTotalVal(orderProductInfo.getShipmentGoodsValue()); // 货物总值
//                itfDclIoDeclGoods.setCiqCurr(isNotEmpty(erpCurrencies) ? erpCurrencies.getCurrency() : null); // 货物总值币制代码
                itfDclIoDeclGoods.setCiqCurr(orderInfo.getCustomsDeclarationCurrency()); // 货物总值币制代码
                itfDclIoDeclGoods.setCiqDomeOriginCode(orderProductInfo.getDistrictCode()); // 产地代码
                itfDclIoDeclGoods.setMnufctrRegName(buyerName); // 生产单位名称
                itfDclIoDeclGoods.setPackCatgName(orderProductInfo.getShipmentPackingType()); // 包装种类
                itfDclIoDeclGoods.setPackQty(isNotEmpty(orderProductInfo.getShipmentPackagesNumbers()) ? BigDecimal.valueOf(orderProductInfo.getShipmentPackagesNumbers()) : null); // 包装件数
                itfDclIoDeclGoods.setMnufctrRegNo(isNotEmpty(customerEnterpriseList) ? customerEnterpriseList.get(0).getDepartcd() : null); // 生产单位注册号
                itfDclIoDeclGoodsList.add(itfDclIoDeclGoods);
                i++;
            }
            itfDclIoDecl.setItfDclIoDeclGoodsList(itfDclIoDeclGoodsList);
        }
        return Result.ok(itfDclIoDecl);
    }

    /**
     * 导入业务
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/23 09:58
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> importBusiness(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String ieFlag = request.getParameter("ieFlag");
        if (isBlank(ieFlag)) {
            return Result.error("未知的进出口类型！");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        AtomicInteger successLines = new AtomicInteger();
        AtomicInteger errorLines = new AtomicInteger();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            try {
                ExcelImportResult<ImportBusinessExcelEntity> excelImportResult = ExcelImportUtil.importExcelVerify(file.getInputStream(), ImportBusinessExcelEntity.class, params);
                if (null == excelImportResult || isEmpty(excelImportResult.getList())) {
                    return Result.error("文件格式错误，请严格按模版填写！");
                }
                List<ImportBusinessExcelEntity> importBusinessExcelEntityList = excelImportResult.getList();
                List<String> noMatchPns=new ArrayList<>();

                //根据配置表确认当前租户是否需要小物料号替换大物料号
                List<SysConfig> import_merge = sysConfigMapper.selectList(
                        new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, "IMPORT_MERGE"));
                if(!import_merge.isEmpty() &&isNotBlank(import_merge.get(0).getConfigValue())){
                    List<String> configValueList = Arrays.asList(import_merge.get(0).getConfigValue().split(","));
                    if(configValueList.contains(TenantContext.getTenant())){
                        handleMergePnImportI(importBusinessExcelEntityList,noMatchPns);
                        if(!noMatchPns.isEmpty()){
                            String errorInfo = "请注意，以下物料号未找到对应大物料号，替换失败。"+String.join(",",noMatchPns);
                            errorMessage.add(errorInfo);
                        }
                    }
                }

                Map<String, List<ImportBusinessExcelEntity>> importBusinessExcelEntityMap = importBusinessExcelEntityList.stream().collect(Collectors.groupingBy(ImportBusinessExcelEntity::getRelNoteNo));
                // 币制
                List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,code,currency,1=1");
                Map<String, String> dictMap3 = new HashMap<>();
                if (isNotEmpty(dictModels3)) {
                    dictModels3.forEach(dictModel -> {
                        dictMap3.put(dictModel.getValue(), dictModel.getText());
                    });
                }
                String tenantName = null;
                try {
                    // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                    Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
                    if (isNotEmpty(tenant.getResult())) {
                        tenantName = tenant.getResult().getName();
                    }
                } catch (Exception e) {
                    log.error("获取租户名出现异常：{}", e.getMessage());
                }
                String finalTenantName = tenantName;
                importBusinessExcelEntityMap.forEach((key, value) -> {
                    try {
                        OrderInfo orderInfo = baseMapper.selectOne(new LambdaQueryWrapper<OrderInfo>()
                                .eq(OrderInfo::getRelNoteNo, key)
                                .eq(OrderInfo::getIeFlag, ieFlag));
                        // 新增
                        if (isEmpty(orderInfo)) {
                            OrderInfo insertOrderInfo = new OrderInfo();
                            BeanUtil.copyProperties(value.get(0), insertOrderInfo, CopyOptions.create().ignoreNullValue());
                            insertOrderInfo.setId(null);
                            insertOrderInfo.setOrderStatus(1);
//                            String orderNo = serialNumberService.getSerialnumber("I".equals(ieFlag) ? "IB" : "EB", TenantContext.getTenant(), 4);
//                            insertOrderInfo.setOrderProtocolNo(orderNo);
                            insertOrderInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            insertOrderInfo.setCreateTime(new Date());
                            insertOrderInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
                            insertOrderInfo.setBuyer(TenantContext.getTenant());
                            insertOrderInfo.setBuyerName(finalTenantName);
                            insertOrderInfo.setIeFlag(ieFlag);
                            if ("I".equals(ieFlag)) {
                                insertOrderInfo.setOrderType("2"); // 默认2进口采购
                            } else if ("E".equals(ieFlag)) {
                                insertOrderInfo.setOrderType("3"); // 默认3外销订单
                            }
                            // 处理币制
                            if (isNotBlank(insertOrderInfo.getCustomsDeclarationCurrency())) {
                                if(dictMap3.containsKey(insertOrderInfo.getCustomsDeclarationCurrency())) {
                                    insertOrderInfo.setCustomsDeclarationCurrency(dictMap3.get(insertOrderInfo.getCustomsDeclarationCurrency()));
                                }
                                // 2024/9/23 11:46@ZHANGCHAO 追加/变更/完善：
                                if ("RMB".equalsIgnoreCase(insertOrderInfo.getCustomsDeclarationCurrency())) {
                                    insertOrderInfo.setCustomsDeclarationCurrency("142");
                                }
                            }
                            // 处理总金额
                            BigDecimal totalContractAmount = BigDecimal.ZERO;
                            for (ImportBusinessExcelEntity importBusinessExcelEntity : value) {
                                totalContractAmount = totalContractAmount.add(isNotEmpty(importBusinessExcelEntity.getShipmentGoodsValue()) ? importBusinessExcelEntity.getShipmentGoodsValue() : BigDecimal.ZERO);
                            }
                            insertOrderInfo.setTotalContractAmount(totalContractAmount.setScale(2, RoundingMode.HALF_UP));
                            insertOrderInfo.setSupervisionMode("0110"); // 默认一般贸易
//                            baseMapper.insert(insertOrderInfo);
                            List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
                            int i = 1;
                            for (ImportBusinessExcelEntity importBusinessExcelEntity : value) {
                                OrderProductInfo orderProductInfo = new OrderProductInfo();
                                BeanUtil.copyProperties(importBusinessExcelEntity, orderProductInfo, CopyOptions.create().ignoreNullValue());
                                orderProductInfo.setId(null);
                                orderProductInfo.setOrderInfoId(insertOrderInfo.getId());
                                orderProductInfo.setCustomsDeclarationCurrency(insertOrderInfo.getCustomsDeclarationCurrency());
                                orderProductInfo.setHawb(insertOrderInfo.getDeliveryNumbers());
                                orderProductInfo.setDecQty(importBusinessExcelEntity.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setLegalQuantity(importBusinessExcelEntity.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                                orderProductInfo.setCreateTime(new Date());
                                orderProductInfo.setSequence(i++);
                                // 2024/10/10 21:38@ZHANGCHAO 追加/变更/完善：带取品名库逻辑+++
                                if (!(isNotBlank(orderProductInfo.getBondedMark()) && (orderProductInfo.getBondedMark().contains("保税") || orderProductInfo.getBondedMark().contains("B")))) {
                                    // 2024/10/15 16:39@ZHANGCHAO 追加/变更/完善：  - 增加账册号、并自动带账册申报信息，根据"保税完税标志"大概判定监管方式--先写死。（保税-来料加工，非保税-一般贸易）
                                    insertOrderInfo.setSupervisionMode("0110");
                                    orderProductInfo.setSupervisionMode("0110");
                                    takingDataFromTheProductLibrary(orderProductInfo);
                                } else {
                                    insertOrderInfo.setSupervisionMode("0214");
                                    orderProductInfo.setSupervisionMode("0214");
                                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册？？
                                    if (isNotBlank(insertOrderInfo.getRecordNumber())) {
                                        takingDataFromTheAccountBook(orderProductInfo, insertOrderInfo);
                                    }
                                }
                                orderProductInfoList.add(orderProductInfo);
                            }
//                            orderProductInfoService.saveBatch(orderProductInfoList);
                            insertOrderInfo.setProductList(orderProductInfoList);
                            OrderInfoBiz orderInfoBiz = new OrderInfoBiz();
                            BeanUtil.copyProperties(insertOrderInfo, orderInfoBiz, CopyOptions.create().ignoreNullValue());
                            this.saveOrder(orderInfoBiz);
                            successLines.getAndIncrement();
                            // 编辑
                        } else {
                            BeanUtil.copyProperties(value.get(0), orderInfo, CopyOptions.create().ignoreNullValue());
                            orderInfo.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            orderInfo.setUpdateTime(new Date());
                            orderInfo.setTenantId(isNotEmpty(orderInfo.getTenantId()) ? orderInfo.getTenantId() : Long.valueOf(TenantContext.getTenant()));
                            // 处理币制
                            if (isNotBlank(orderInfo.getCustomsDeclarationCurrency())) {
                                if(dictMap3.containsKey(orderInfo.getCustomsDeclarationCurrency())) {
                                    orderInfo.setCustomsDeclarationCurrency(dictMap3.get(orderInfo.getCustomsDeclarationCurrency()));
                                }
                                // 2024/9/23 11:46@ZHANGCHAO 追加/变更/完善：
                                if ("RMB".equalsIgnoreCase(orderInfo.getCustomsDeclarationCurrency())) {
                                    orderInfo.setCustomsDeclarationCurrency("142");
                                }
                            }
                            // 处理总金额
                            BigDecimal totalContractAmount = BigDecimal.ZERO;
                            for (ImportBusinessExcelEntity importBusinessExcelEntity : value) {
                                totalContractAmount = totalContractAmount.add(isNotEmpty(importBusinessExcelEntity.getShipmentGoodsValue()) ? importBusinessExcelEntity.getShipmentGoodsValue() : BigDecimal.ZERO);
                            }
                            orderInfo.setTotalContractAmount(totalContractAmount.setScale(2, RoundingMode.HALF_UP));
                            orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0110"); // 默认一般贸易
//                            baseMapper.updateById(orderInfo);
                            orderProductInfoService.remove(new LambdaQueryWrapper<OrderProductInfo>()
                                    .eq(OrderProductInfo::getOrderInfoId, orderInfo.getId()));
                            List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
                            int i = 1;
                            for (ImportBusinessExcelEntity importBusinessExcelEntity : value) {
                                OrderProductInfo orderProductInfo = new OrderProductInfo();
                                BeanUtil.copyProperties(importBusinessExcelEntity, orderProductInfo, CopyOptions.create().ignoreNullValue());
                                orderProductInfo.setId(null);
                                orderProductInfo.setOrderInfoId(orderInfo.getId());
                                orderProductInfo.setCustomsDeclarationCurrency(orderInfo.getCustomsDeclarationCurrency());
                                orderProductInfo.setHawb(orderInfo.getDeliveryNumbers());
                                orderProductInfo.setDecQty(importBusinessExcelEntity.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setLegalQuantity(importBusinessExcelEntity.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                                orderProductInfo.setUpdateTime(new Date());
                                orderProductInfo.setSequence(i++);
                                // 2024/10/10 21:38@ZHANGCHAO 追加/变更/完善：带取品名库逻辑+++
                                if (!(isNotBlank(orderProductInfo.getBondedMark()) && orderProductInfo.getBondedMark().contains("保税"))) {
                                    orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0110");
                                    orderProductInfo.setSupervisionMode(isNotBlank(orderProductInfo.getSupervisionMode()) ? orderProductInfo.getSupervisionMode() : "0110");
                                    takingDataFromTheProductLibrary(orderProductInfo);
                                } else {
                                    orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0214");
                                    orderProductInfo.setSupervisionMode(isNotBlank(orderProductInfo.getSupervisionMode()) ? orderProductInfo.getSupervisionMode() : "0214");
                                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册？？
                                    if (isNotBlank(orderInfo.getRecordNumber())) {
                                        takingDataFromTheAccountBook(orderProductInfo, orderInfo);
                                    }
                                }
                                orderProductInfoList.add(orderProductInfo);
                            }
//                            orderProductInfoService.saveBatch(orderProductInfoList);
                            orderInfo.setProductList(orderProductInfoList);
                            OrderInfoBiz orderInfoBiz = new OrderInfoBiz();
                            BeanUtil.copyProperties(orderInfo, orderInfoBiz, CopyOptions.create().ignoreNullValue());
                            this.saveOrder(orderInfoBiz);
                            successLines.getAndIncrement();
                        }
                    } catch (Exception e) {
                        errorLines.getAndIncrement();
                        log.error(e.getMessage(), e);
                        ExceptionUtil.getFullStackTrace(e);
                        errorMessage.add(e.getMessage().length() > 50 ? e.getMessage().substring(0, 50) : e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                ExceptionUtil.getFullStackTrace(e);
//                return Result.error("文件导入失败：" + e.getMessage());
                return Result.error("文件导入失败，请检查模版文件！");
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                    ExceptionUtil.getFullStackTrace(e);
                    log.error(e.getMessage(), e);
                }
            }
        }
        if (isNotEmpty(errorMessage)) {
            return Result.error("成功导入行数："+ successLines.get() + "；失败行数：" + errorLines.get() + "；" + CollUtil.join(errorMessage, ","));
        }
        return ImportExcelUtil.imporReturnRes(errorLines.get(), successLines.get(), errorMessage);
    }

    /**
     * 导入出口业务
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/23 13:46
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> importBusinessE(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String ieFlag = request.getParameter("ieFlag");
        if (isBlank(ieFlag)) {
            return Result.error("未知的进出口类型！");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        AtomicInteger successLines = new AtomicInteger();
        AtomicInteger errorLines = new AtomicInteger();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            try {
                ExcelImportResult<ImportBusinessExcelEntityE> excelImportResult = ExcelImportUtil.importExcelVerify(file.getInputStream(), ImportBusinessExcelEntityE.class, params);
                if (null == excelImportResult || isEmpty(excelImportResult.getList())) {
                    return Result.error("文件格式错误，请严格按模版填写！");
                }
                List<ImportBusinessExcelEntityE> importBusinessExcelEntityEList = excelImportResult.getList();
                List<String> noMatchPns = new ArrayList<>();
                //根据配置表确认当前租户是否需要小物料号替换大物料号
                List<SysConfig> import_merge = sysConfigMapper.selectList(
                        new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, "IMPORT_MERGE"));
                if(!import_merge.isEmpty() &&isNotBlank(import_merge.get(0).getConfigValue())){
                    List<String> configValueList = Arrays.asList(import_merge.get(0).getConfigValue().split(","));
                    if(configValueList.contains(TenantContext.getTenant())){
                      handleMergePnImportE(importBusinessExcelEntityEList,noMatchPns);
                      if(!noMatchPns.isEmpty()){
                          String errorInfo = "请注意，以下物料号未找到对应大物料号，替换失败。"+String.join(",",noMatchPns);
                          errorMessage.add(errorInfo);
                      }
                    }
                }

                Map<String, List<ImportBusinessExcelEntityE>> importBusinessExcelEntityEMap = importBusinessExcelEntityEList.stream().collect(Collectors.groupingBy(ImportBusinessExcelEntityE::getLinkedBillOfLadingNo));
                String tenantName = null;
                try {
                    // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
                    Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
                    if (isNotEmpty(tenant.getResult())) {
                        tenantName = tenant.getResult().getName();
                    }
                } catch (Exception e) {
                    log.error("获取租户名出现异常：{}", e.getMessage());
                }
                //模板配置处理
                List<OrderInfoTemplate> orderInfoTemplates = orderInfoTemplateMapper.selectList(new LambdaQueryWrapper<OrderInfoTemplate>()
                        .eq(OrderInfoTemplate::getIeFlag, "E"));
                if(!orderInfoTemplates.isEmpty()){
                    List<OrderProductInfoTemplate> orderProductInfoTemplates = orderProductInfoTemplateMapper.
                            selectList(new LambdaQueryWrapper<OrderProductInfoTemplate>()
                                    .eq(OrderProductInfoTemplate::getOrderInfoTemplateId, orderInfoTemplates.get(0).getId()));
                    orderInfoTemplates.get(0).setOrderProductInfoTemplateList(orderProductInfoTemplates);
                    handleTemplateImportE(importBusinessExcelEntityEList,orderInfoTemplates.get(0),tenantName);

                }

                // 包装种类
                List<DictModel> dictModels = sysBaseApi.getDictItems("erp_packages_types,name,code,isenabled=1");
                Map<String, String> dictMap = new HashMap<>();
                if (isNotEmpty(dictModels)) {
                    dictModels.forEach(dictModel -> {
                        dictMap.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                // 币制
                List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,code,currency,1=1");
                Map<String, String> dictMap3 = new HashMap<>();
                if (isNotEmpty(dictModels3)) {
                    dictModels3.forEach(dictModel -> {
                        dictMap3.put(dictModel.getValue(), dictModel.getText());
                    });
                }
                // 运输方式
                List<DictModel> dictModels1 = sysBaseApi.getDictItems("YSFS");
                Map<String, String> dictMap1 = new HashMap<>();
                if (isNotEmpty(dictModels1)) {
                    dictModels1.forEach(dictModel -> {
                        dictMap1.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
                Map<String, String> dictMap2 = new HashMap<>();
                if (isNotEmpty(dictModels2)) {
                    dictModels2.forEach(dictModel -> {
                        dictMap2.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels22 = sysBaseApi.getDictItems("erp_countries,code,standard_code,isenabled=1");
                Map<String, String> dictMap22 = new HashMap<>();
                if (isNotEmpty(dictModels22)) {
                    dictModels22.forEach(dictModel -> {
                        if(isNotBlank(dictModel.getValue())){
                            dictMap22.put(dictModel.getValue(), dictModel.getText());
                        }
                    });
                }

                List<DictModel> dictModelsPort = sysBaseApi.getDictItems("erp_cityports,cnname,cityport_code,isenabled=1");
                Map<String, String> dictMapPort = new HashMap<>();
                if (isNotEmpty(dictModelsPort)) {
                    dictModelsPort.forEach(dictModel -> {
                        dictMapPort.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels4 = sysBaseApi.getDictItems("erp_customs_ports,name,customs_port_code");
                Map<String, String> dictMap4 = new HashMap<>();
                if (isNotEmpty(dictModels4)) {
                    dictModels4.forEach(dictModel -> {
                        dictMap4.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels5 = sysBaseApi.getDictItems("erp_china_ports,name,china_port_code");
                Map<String, String> dictMap5 = new HashMap<>();
                if (isNotEmpty(dictModels5)) {
                    dictModels5.forEach(dictModel -> {
                        dictMap5.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels6 = sysBaseApi.getDictItems("JGFS");
                Map<String, String> dictMap6 = new HashMap<>();
                if (isNotEmpty(dictModels6)) {
                    dictModels6.forEach(dictModel -> {
                        dictMap6.put(dictModel.getText(), dictModel.getValue());
                    });
                }
                List<DictModel> dictModels7 = sysBaseApi.getDictItems("ZMXZ");
                Map<String, String> dictMap7 = new HashMap<>();
                if (isNotEmpty(dictModels7)) {
                    dictModels7.forEach(dictModel -> dictMap7.put(dictModel.getText(), dictModel.getValue()));
                }
                List<DictModel> dictModels8 = sysBaseApi.getDictItems("trading_type");
                Map<String, String> dictMap8 = new HashMap<>();
                if (isNotEmpty(dictModels8)) {
                    dictModels8.forEach(dictModel -> dictMap8.put(dictModel.getText(), dictModel.getValue()));
                }
                // 运费种类
                List<DictModel> dictModels9 = sysBaseApi.getDictItems("freight_amount_type");
                Map<String, String> dictMap9 = new HashMap<>();
                if (isNotEmpty(dictModels9)) {
                    dictModels9.forEach(dictModel -> dictMap9.put(dictModel.getText(), dictModel.getValue()));
                }
                //货源地
                List<DictModel> dictModels10 = sysBaseApi.getDictItems("erp_districts,name,code");
                Map<String, String> dictMap10 = new HashMap<>();
                if (isNotEmpty(dictModels10)) {
                    dictModels10.forEach(dictModel -> dictMap10.put(dictModel.getText(), dictModel.getValue()));
                }


                String finalTenantName = tenantName;
                importBusinessExcelEntityEMap.forEach((key, value) -> {
                    try {
                        OrderInfo orderInfo = baseMapper.selectOne(new LambdaQueryWrapper<OrderInfo>()
                                .eq(OrderInfo::getLinkedBillOfLadingNo, key)
                                .eq(OrderInfo::getIeFlag, ieFlag));
                        // 新增
                        if (isEmpty(orderInfo)) {
                            OrderInfo insertOrderInfo = new OrderInfo();
                            BeanUtil.copyProperties(value.get(0), insertOrderInfo, CopyOptions.create().ignoreNullValue());
                            insertOrderInfo.setId(null);
                            insertOrderInfo.setOrderStatus(1);
//                            String orderNo = serialNumberService.getSerialnumber("I".equals(ieFlag) ? "IB" : "EB", TenantContext.getTenant(), 4);
//                            insertOrderInfo.setOrderProtocolNo(orderNo);
                            insertOrderInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            insertOrderInfo.setCreateTime(new Date());
                            insertOrderInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
                            insertOrderInfo.setBuyer(TenantContext.getTenant());
                            insertOrderInfo.setBuyerName(finalTenantName);
                            insertOrderInfo.setIeFlag(ieFlag);
                            insertOrderInfo.setDomesticSourceOfGoods(value.get(0).getDistrictCode());
                            if ("I".equals(ieFlag)) {
                                insertOrderInfo.setOrderType("2"); // 默认2进口采购
                            } else if ("E".equals(ieFlag)) {
                                insertOrderInfo.setOrderType("3"); // 默认3外销订单
                            }
                            // 处理币制
                            if (isNotBlank(insertOrderInfo.getCustomsDeclarationCurrency())) {
                                if(dictMap3.containsKey(insertOrderInfo.getCustomsDeclarationCurrency())) {
                                    insertOrderInfo.setCustomsDeclarationCurrency(dictMap3.get(insertOrderInfo.getCustomsDeclarationCurrency()));
                                }
                            }
                            // 加工费币制
                            if (isNotBlank(insertOrderInfo.getProcessingFeeCurr())) {
                                if(dictMap3.containsKey(insertOrderInfo.getProcessingFeeCurr())) {
                                    insertOrderInfo.setProcessingFeeCurr(dictMap3.get(insertOrderInfo.getProcessingFeeCurr()));
                                }
                            }
                            // 材料费币制
                            if (isNotBlank(insertOrderInfo.getMaterialFeeCurr())) {
                                if(dictMap3.containsKey(insertOrderInfo.getMaterialFeeCurr())) {
                                    insertOrderInfo.setMaterialFeeCurr(dictMap3.get(insertOrderInfo.getMaterialFeeCurr()));
                                }
                            }
                            if (isNotBlank(insertOrderInfo.getShipCurrencyCode())) {
                                if(dictMap3.containsKey(insertOrderInfo.getShipCurrencyCode())) {
                                    insertOrderInfo.setShipCurrencyCode(dictMap3.get(insertOrderInfo.getShipCurrencyCode()));
                                }
                            }
                            if (isNotBlank(insertOrderInfo.getInsuranceCurr())) {
                                if(dictMap3.containsKey(insertOrderInfo.getInsuranceCurr())) {
                                    insertOrderInfo.setInsuranceCurr(dictMap3.get(insertOrderInfo.getInsuranceCurr()));
                                }
                            }
                            if (isNotBlank(insertOrderInfo.getOtherCurr())) {
                                if(dictMap3.containsKey(insertOrderInfo.getOtherCurr())) {
                                    insertOrderInfo.setOtherCurr(dictMap3.get(insertOrderInfo.getOtherCurr()));
                                }
                            }
                            // 包装种类
                            if (isNotBlank(insertOrderInfo.getPacksKinds())) {
                                if(dictMap.containsKey(insertOrderInfo.getPacksKinds())) {
                                    insertOrderInfo.setPacksKinds(dictMap.get(insertOrderInfo.getPacksKinds()));
                                }
                            }
                            // 运输方式
//                            if (isNotBlank(insertOrderInfo.getShippingType())) {
//                                if(dictMap1.containsKey(insertOrderInfo.getShippingType())) {
//                                    insertOrderInfo.setShippingType(dictMap1.get(insertOrderInfo.getShippingType()));
//                                }
//                            }
                            // 贸易国别
                            if (isNotBlank(insertOrderInfo.getTradingCountry())) {
                                if(dictMap2.containsKey(insertOrderInfo.getTradingCountry())) {
                                    insertOrderInfo.setTradingCountry(dictMap2.get(insertOrderInfo.getTradingCountry()));
                                }
                            }
                            // 运抵国
                            if (isNotBlank(insertOrderInfo.getCountryArrival())) {
                                if(dictMap22.containsKey(insertOrderInfo.getCountryArrival())) {
                                    insertOrderInfo.setCountryArrival(dictMap22.get(insertOrderInfo.getCountryArrival()));
                                }
                            }
                            // 指运港
                            //博汇的指运港按照运抵国赋值！！！
                            if("山东博汇纸业股份有限公司".equals(finalTenantName)){
                                if (isNotBlank(insertOrderInfo.getCountryArrival()
                                )) {
                                    List<DictModel> dictModels11 =dictModels2.stream()
                                            .filter(i->i.getValue().equals(insertOrderInfo.getCountryArrival()))
                                            .collect(Collectors.toList());
                                    String ydgName = !dictModels11.isEmpty() ?
                                            dictModels11.get(0).getText():null;
                                    if(isNotBlank(ydgName)){
                                      List<DictModel>  dictModelsPort2 = dictModelsPort.stream().filter(i->
                                              ydgName.equals(i.getText())
                                      ).collect(Collectors.toList());
                                      if(dictModelsPort2.size()>0){
                                          insertOrderInfo.setPortDestination(dictModelsPort2.get(0).getValue());
                                      }
                                    }
                                }
                            }else {
                                if (isNotBlank(insertOrderInfo.getPortDestination())) {
                                    if(dictMapPort.containsKey(insertOrderInfo.getPortDestination())) {
                                        insertOrderInfo.setPortDestination(dictMapPort.get(insertOrderInfo.getPortDestination()));
                                    }
                                }
                            }

                            // 出境关别
                            if (isNotBlank(insertOrderInfo.getExitClearance())) {
                                if(dictMap4.containsKey(insertOrderInfo.getExitClearance())) {
                                    insertOrderInfo.setExitClearance(dictMap4.get(insertOrderInfo.getExitClearance()));
                                } else {
                                    insertOrderInfo.setExitClearance(null);
                                }
                            }
                            // 申报地海关
                            if (isNotBlank(insertOrderInfo.getDeclarePlace())) {
                                if(dictMap4.containsKey(insertOrderInfo.getDeclarePlace())) {
                                    insertOrderInfo.setDeclarePlace(dictMap4.get(insertOrderInfo.getDeclarePlace()));
                                }
                            }
                            // 离境口岸
                            if (isNotBlank(insertOrderInfo.getDeparturePort())) {
                                if(dictMap5.containsKey(insertOrderInfo.getDeparturePort())) {
                                    insertOrderInfo.setDeparturePort(dictMap5.get(insertOrderInfo.getDeparturePort()));
                                }
                            }
                            // 最终目的国
                            if (isNotBlank(insertOrderInfo.getFinalContry())) {
                                if(dictMap22.containsKey(insertOrderInfo.getFinalContry())) {
                                    insertOrderInfo.setFinalContry(dictMap22.get(insertOrderInfo.getFinalContry()));
                                }
                            }
                            // 监管方式
                            if (isNotBlank(insertOrderInfo.getSupervisionMode())) {
                                if(dictMap6.containsKey(insertOrderInfo.getSupervisionMode())) {
                                    insertOrderInfo.setSupervisionMode(dictMap6.get(insertOrderInfo.getSupervisionMode()));
                                }
                            }
                            // 征免性质
                            if (isNotBlank(insertOrderInfo.getTaxTypeCode())) {
                                if(dictMap7.containsKey(insertOrderInfo.getTaxTypeCode())) {
                                    insertOrderInfo.setTaxTypeCode(dictMap7.get(insertOrderInfo.getTaxTypeCode()));
                                }
                            }
                            // 成交方式
                            if (isNotBlank(insertOrderInfo.getTransMode())) {
                                if(dictMap8.containsKey(insertOrderInfo.getTransMode())) {
                                    insertOrderInfo.setTransMode(dictMap8.get(insertOrderInfo.getTransMode()));
                                    insertOrderInfo.setTradingType(isNotBlank(insertOrderInfo.getTransMode()) ? Integer.valueOf(insertOrderInfo.getTransMode()) : null);
                                }
                            }
                            // 运费种类
                            if (isNotBlank(insertOrderInfo.getShipFeeCode())) {
                                if(dictMap9.containsKey(insertOrderInfo.getShipFeeCode())) {
                                    insertOrderInfo.setShipFeeCode(dictMap9.get(insertOrderInfo.getShipFeeCode()));
                                }
                            }
                            // 保费种类
                            if (isNotBlank(insertOrderInfo.getInsuranceCode())) {
                                if(dictMap9.containsKey(insertOrderInfo.getInsuranceCode())) {
                                    insertOrderInfo.setInsuranceCode(dictMap9.get(insertOrderInfo.getInsuranceCode()));
                                }
                            }
                            // 杂费种类
                            if (isNotBlank(insertOrderInfo.getExtrasCode())) {
                                if(dictMap9.containsKey(insertOrderInfo.getExtrasCode())) {
                                    insertOrderInfo.setExtrasCode(dictMap9.get(insertOrderInfo.getExtrasCode()));
                                }
                            }
                            //货源地
                            if(isNotBlank(insertOrderInfo.getDomesticSourceOfGoods())){
                                if(dictMap10.containsKey(insertOrderInfo.getDomesticSourceOfGoods())) {
                                    insertOrderInfo.setDomesticSourceOfGoods(dictMap10.get(
                                            value.get(0).getDistrictCode()));
                                }
                            }
                            // 处理总金额
                            BigDecimal totalContractAmount = BigDecimal.ZERO;
                            for (ImportBusinessExcelEntityE importBusinessExcelEntityE : value) {
                                totalContractAmount = totalContractAmount.add(isNotEmpty(importBusinessExcelEntityE.getShipmentGoodsValue()) ? importBusinessExcelEntityE.getShipmentGoodsValue() : BigDecimal.ZERO);
                            }
                            insertOrderInfo.setTotalContractAmount(totalContractAmount.setScale(2, RoundingMode.HALF_UP));
                            insertOrderInfo.setSupervisionMode(isNotBlank(insertOrderInfo.getSupervisionMode()) ? insertOrderInfo.getSupervisionMode() : "0110"); // 默认一般贸易
                            // 2024/10/28 09:32@ZHANGCHAO 追加/变更/完善：重新处理下运输方式！
                            if (isNotBlank(insertOrderInfo.getShippingType())) {
                                if (insertOrderInfo.getShippingType().contains("水路") || insertOrderInfo.getShippingType().contains("海运")) {
                                    insertOrderInfo.setShippingType("SEA");
                                } else if (insertOrderInfo.getShippingType().contains("航空") || insertOrderInfo.getShippingType().contains("空运")) {
                                    insertOrderInfo.setShippingType("AIR");
                                } else if (insertOrderInfo.getShippingType().equals("公路")) {
                                    insertOrderInfo.setShippingType("TRUCK");
                                } else if (insertOrderInfo.getShippingType().equals("铁路")) {
                                    insertOrderInfo.setShippingType("RAILWAY");
                                } else if (insertOrderInfo.getShippingType().equals("管道")) {
                                    insertOrderInfo.setShippingType("TRANSPORT");
                                } else {
                                    insertOrderInfo.setShippingType("OTHERS");
                                }
                            }
                            //如果租户是博汇，默认境外客户
                            if("山东博汇纸业股份有限公司".equals(finalTenantName)){
                                List<OverseasPayerInfo> listed = overseasPayerInfoService.
                                        list(new LambdaQueryWrapper<OverseasPayerInfo>()
                                        .eq(OverseasPayerInfo::getOverseasPayerName, "BOFENG GROUP HOLDINGS (HK) LIMITED"));
                                insertOrderInfo.setOverseasPayerInfoId(!listed.isEmpty() ?listed.get(0).getId():null);
                                insertOrderInfo.setOverseasPayerInfoName("BOFENG GROUP HOLDINGS (HK) LIMITED");
                            }
//                            baseMapper.insert(insertOrderInfo);
                            List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
                            int i = 1;
                            List<String> deliveryNoteNumbers = new ArrayList<>();
                            for (ImportBusinessExcelEntityE importBusinessExcelEntityE : value) {
                                OrderProductInfo orderProductInfo = new OrderProductInfo();
                                BeanUtil.copyProperties(importBusinessExcelEntityE, orderProductInfo, CopyOptions.create().ignoreNullValue());
                                orderProductInfo.setId(null);
//                                orderProductInfo.setOrderInfoId(insertOrderInfo.getId());
                                //如果租户是博汇，导入原产国空，默认中国
                                if("山东博汇纸业股份有限公司".equals(finalTenantName)&&
                                        isBlank(importBusinessExcelEntityE.getOriginCountry())){
                                    orderProductInfo.setOriginCountry("CHN");
                                }else {
                                    orderProductInfo.setOriginCountry(dictMap2.get(importBusinessExcelEntityE.getOriginCountry()));
                                }
                                orderProductInfo.setCustomsDeclarationCurrency(insertOrderInfo.getCustomsDeclarationCurrency());
                                orderProductInfo.setHawb(insertOrderInfo.getDeliveryNumbers());
                                orderProductInfo.setDecQty(importBusinessExcelEntityE.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setLegalQuantity(importBusinessExcelEntityE.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                                orderProductInfo.setCreateTime(new Date());
                                orderProductInfo.setSequence(i++);
                                // 2024/10/10 21:38@ZHANGCHAO 追加/变更/完善：带取品名库逻辑+++
                                if (!(isNotBlank(orderProductInfo.getBondedMark()) && (orderProductInfo.getBondedMark().contains("保税") || orderProductInfo.getBondedMark().contains("B")))) {
                                    // 2024/10/15 16:39@ZHANGCHAO 追加/变更/完善：  - 增加账册号、并自动带账册申报信息，根据"保税完税标志"大概判定监管方式--先写死。（保税-来料加工，非保税-一般贸易）
                                    insertOrderInfo.setSupervisionMode("0110");
                                    orderProductInfo.setSupervisionMode("0110");
                                    takingDataFromTheProductLibrary(orderProductInfo);
                                } else {
                                    insertOrderInfo.setSupervisionMode("0214");
                                    orderProductInfo.setSupervisionMode("0214");
                                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册
                                    if (isNotBlank(insertOrderInfo.getRecordNumber())) {
                                        takingDataFromTheAccountBook(orderProductInfo, insertOrderInfo);
                                    }
                                }
                                orderProductInfoList.add(orderProductInfo);
                                if (isNotBlank(importBusinessExcelEntityE.getDeliveryNoteNumber())) {
                                    deliveryNoteNumbers.add(importBusinessExcelEntityE.getDeliveryNoteNumber());
                                }
                            }
                            if (isNotEmpty(deliveryNoteNumbers)) {
                                insertOrderInfo.setDeliveryNoteNumber(CollUtil.join(CollUtil.distinct(deliveryNoteNumbers), "/"));
                            }
//                            orderProductInfoService.saveBatch(orderProductInfoList);
                            insertOrderInfo.setProductList(orderProductInfoList);
                            OrderInfoBiz orderInfoBiz = new OrderInfoBiz();
                            BeanUtil.copyProperties(insertOrderInfo, orderInfoBiz, CopyOptions.create().ignoreNullValue());
                            this.saveOrder(orderInfoBiz);
                            successLines.getAndIncrement();
                            // 编辑
                        } else {
                            BeanUtil.copyProperties(value.get(0), orderInfo, CopyOptions.create().ignoreNullValue());
                            orderInfo.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                            orderInfo.setUpdateTime(new Date());
                            orderInfo.setTenantId(isNotEmpty(orderInfo.getTenantId()) ? orderInfo.getTenantId() : Long.valueOf(TenantContext.getTenant()));
                            // 处理币制
                            if (isNotBlank(value.get(0).getCustomsDeclarationCurrency())) {
                                if(dictMap3.containsKey(value.get(0).getCustomsDeclarationCurrency())) {
                                    orderInfo.setCustomsDeclarationCurrency(dictMap3.get(value.get(0).getCustomsDeclarationCurrency()));
                                }
                            }
                            // 加工费币制
                            if (isNotBlank(orderInfo.getProcessingFeeCurr())) {
                                if(dictMap3.containsKey(orderInfo.getProcessingFeeCurr())) {
                                    orderInfo.setProcessingFeeCurr(dictMap3.get(orderInfo.getProcessingFeeCurr()));
                                }
                            }
                            // 材料费币制
                            if (isNotBlank(orderInfo.getMaterialFeeCurr())) {
                                if(dictMap3.containsKey(orderInfo.getMaterialFeeCurr())) {
                                    orderInfo.setMaterialFeeCurr(dictMap3.get(orderInfo.getMaterialFeeCurr()));
                                }
                            }
                            if (isNotBlank(orderInfo.getShipCurrencyCode())) {
                                if(dictMap3.containsKey(orderInfo.getShipCurrencyCode())) {
                                    orderInfo.setShipCurrencyCode(dictMap3.get(orderInfo.getShipCurrencyCode()));
                                }
                            }
                            if (isNotBlank(orderInfo.getInsuranceCurr())) {
                                if(dictMap3.containsKey(orderInfo.getInsuranceCurr())) {
                                    orderInfo.setInsuranceCurr(dictMap3.get(orderInfo.getInsuranceCurr()));
                                }
                            }
                            if (isNotBlank(orderInfo.getOtherCurr())) {
                                if(dictMap3.containsKey(orderInfo.getOtherCurr())) {
                                    orderInfo.setOtherCurr(dictMap3.get(orderInfo.getOtherCurr()));
                                }
                            }
                            // 包装种类
                            if (isNotBlank(orderInfo.getPacksKinds())) {
                                if(dictMap.containsKey(orderInfo.getPacksKinds())) {
                                    orderInfo.setPacksKinds(dictMap.get(orderInfo.getPacksKinds()));
                                }
                            }
                            // 运输方式
//                            if (isNotBlank(orderInfo.getShippingType())) {
//                                if(dictMap1.containsKey(orderInfo.getShippingType())) {
//                                    orderInfo.setShippingType(dictMap1.get(orderInfo.getShippingType()));
//                                }
//                            }
                            // 贸易国别
                            if (isNotBlank(orderInfo.getTradingCountry())) {
                                if(dictMap2.containsKey(orderInfo.getTradingCountry())) {
                                    orderInfo.setTradingCountry(dictMap2.get(orderInfo.getTradingCountry()));
                                }
                            }
                            // 运抵国
                            if (isNotBlank(orderInfo.getCountryArrival())) {
                                if(dictMap22.containsKey(orderInfo.getCountryArrival())) {
                                    orderInfo.setCountryArrival(dictMap22.get(orderInfo.getCountryArrival()));
                                }
                            }
                            // 指运港
                            //博汇的指运港按照运抵国赋值！！！
                            if("山东博汇纸业股份有限公司".equals(finalTenantName)){
                                if (isNotBlank(orderInfo.getPortDestination())&&isNotBlank(
                                        orderInfo.getCountryArrival()
                                )) {
                                    String ydgName = dictMap2.get(orderInfo.getCountryArrival());
                                    if(isNotBlank(ydgName)){
                                        List<DictModel>  dictModelsPort2 = dictModelsPort.stream().filter(i->
                                                ydgName.equals(i.getValue())
                                        ).collect(Collectors.toList());
                                        if(dictModelsPort2.size()>0){
                                            orderInfo.setPortDestination(dictModelsPort2.get(0).getText());
                                        }
                                    }
                                }
                            }else {
                                if (isNotBlank(orderInfo.getPortDestination())) {
                                    if (dictMapPort.containsKey(orderInfo.getPortDestination())) {
                                        orderInfo.setPortDestination(dictMapPort.get(orderInfo.getPortDestination()));
                                    }
                                }
                            }
                            // 出境关别
                            if (isNotBlank(orderInfo.getExitClearance())) {
                                if(dictMap4.containsKey(orderInfo.getExitClearance())) {
                                    orderInfo.setExitClearance(dictMap4.get(orderInfo.getExitClearance()));
                                } else {
                                    orderInfo.setExitClearance(null);
                                }
                            }
                            // 申报地海关
                            if (isNotBlank(orderInfo.getDeclarePlace())) {
                                if(dictMap4.containsKey(orderInfo.getDeclarePlace())) {
                                    orderInfo.setDeclarePlace(dictMap4.get(orderInfo.getDeclarePlace()));
                                }
                            }
                            // 离境口岸
                            if (isNotBlank(orderInfo.getDeparturePort())) {
                                if(dictMap5.containsKey(orderInfo.getDeparturePort())) {
                                    orderInfo.setDeparturePort(dictMap5.get(orderInfo.getDeparturePort()));
                                }
                            }
                            // 最终目的国
                            if (isNotBlank(orderInfo.getFinalContry())) {
                                if(dictMap22.containsKey(orderInfo.getFinalContry())) {
                                    orderInfo.setFinalContry(dictMap22.get(orderInfo.getFinalContry()));
                                }
                            }
                            // 监管方式
                            if (isNotBlank(orderInfo.getSupervisionMode())) {
                                if(dictMap6.containsKey(orderInfo.getSupervisionMode())) {
                                    orderInfo.setSupervisionMode(dictMap6.get(orderInfo.getSupervisionMode()));
                                }
                            }
                            // 征免性质
                            if (isNotBlank(value.get(0).getTaxTypeCode())) {
                                if(dictMap7.containsKey(value.get(0).getTaxTypeCode())) {
                                    orderInfo.setTaxTypeCode(dictMap7.get(value.get(0).getTaxTypeCode()));
                                }
                            }
                            // 成交方式
                            if (isNotBlank(orderInfo.getTransMode())) {
                                if(dictMap8.containsKey(orderInfo.getTransMode())) {
                                    orderInfo.setTransMode(dictMap8.get(orderInfo.getTransMode()));
                                    orderInfo.setTradingType(isNotBlank(orderInfo.getTransMode()) ? Integer.valueOf(orderInfo.getTransMode()) : null);
                                }
                            }
                            // 运费种类
                            if (isNotBlank(orderInfo.getShipFeeCode())) {
                                if(dictMap9.containsKey(orderInfo.getShipFeeCode())) {
                                    orderInfo.setShipFeeCode(dictMap9.get(orderInfo.getShipFeeCode()));
                                }
                            }
                            // 保费种类
                            if (isNotBlank(orderInfo.getInsuranceCode())) {
                                if(dictMap9.containsKey(orderInfo.getInsuranceCode())) {
                                    orderInfo.setInsuranceCode(dictMap9.get(orderInfo.getInsuranceCode()));
                                }
                            }
                            // 杂费种类
                            if (isNotBlank(orderInfo.getExtrasCode())) {
                                if(dictMap9.containsKey(orderInfo.getExtrasCode())) {
                                    orderInfo.setExtrasCode(dictMap9.get(orderInfo.getExtrasCode()));
                                }
                            }
                            //货源地
                            if(isNotBlank(orderInfo.getDomesticSourceOfGoods())){
                                if(dictMap10.containsKey(orderInfo.getDomesticSourceOfGoods())) {
                                    orderInfo.setDomesticSourceOfGoods(dictMap10.get(
                                            value.get(0).getDistrictCode()));
                                }
                            }
                            // 处理总金额
                            BigDecimal totalContractAmount = BigDecimal.ZERO;
                            for (ImportBusinessExcelEntityE importBusinessExcelEntityE : value) {
                                totalContractAmount = totalContractAmount.add(isNotEmpty(importBusinessExcelEntityE.getShipmentGoodsValue()) ? importBusinessExcelEntityE.getShipmentGoodsValue() : BigDecimal.ZERO);
                            }
                            orderInfo.setTotalContractAmount(totalContractAmount.setScale(2, RoundingMode.HALF_UP));
                            orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0110"); // 默认一般贸易
                            // 2024/10/28 09:32@ZHANGCHAO 追加/变更/完善：重新处理下运输方式！
                            if (isNotBlank(orderInfo.getShippingType())) {
                                if (orderInfo.getShippingType().contains("水路") || orderInfo.getShippingType().contains("海运")) {
                                    orderInfo.setShippingType("SEA");
                                } else if (orderInfo.getShippingType().contains("航空") || orderInfo.getShippingType().contains("空运")) {
                                    orderInfo.setShippingType("AIR");
                                } else if (orderInfo.getShippingType().equals("公路")) {
                                    orderInfo.setShippingType("TRUCK");
                                } else if (orderInfo.getShippingType().equals("铁路")) {
                                    orderInfo.setShippingType("RAILWAY");
                                } else if (orderInfo.getShippingType().equals("管道")) {
                                    orderInfo.setShippingType("TRANSPORT");
                                } else {
                                    orderInfo.setShippingType("OTHERS");
                                }
                            }
//                            baseMapper.updateById(orderInfo);
                            orderProductInfoService.remove(new LambdaQueryWrapper<OrderProductInfo>()
                                    .eq(OrderProductInfo::getOrderInfoId, orderInfo.getId()));
                            List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
                            int i = 1;
                            List<String> deliveryNoteNumbers = new ArrayList<>();
                            for (ImportBusinessExcelEntityE importBusinessExcelEntityE : value) {
                                OrderProductInfo orderProductInfo = new OrderProductInfo();
                                BeanUtil.copyProperties(importBusinessExcelEntityE, orderProductInfo, CopyOptions.create().ignoreNullValue());
                                orderProductInfo.setId(null);
                                orderProductInfo.setOrderInfoId(orderInfo.getId());
                                orderProductInfo.setOriginCountry(dictMap2.get(importBusinessExcelEntityE.getOriginCountry()));
                                orderProductInfo.setCustomsDeclarationCurrency(orderInfo.getCustomsDeclarationCurrency());
                                orderProductInfo.setHawb(orderInfo.getDeliveryNumbers());
                                orderProductInfo.setDecQty(importBusinessExcelEntityE.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setLegalQuantity(importBusinessExcelEntityE.getShipmentQuantity()); // 申报数量 20241010
                                orderProductInfo.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                                orderProductInfo.setUpdateTime(new Date());
                                orderProductInfo.setSequence(i++);
                                // 2024/10/10 21:38@ZHANGCHAO 追加/变更/完善：带取品名库逻辑+++
                                if (!(isNotBlank(orderProductInfo.getBondedMark()) && (orderProductInfo.getBondedMark().contains("保税") || orderProductInfo.getBondedMark().contains("B")))) {
                                    orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0110");
                                    orderProductInfo.setSupervisionMode(isNotBlank(orderProductInfo.getSupervisionMode()) ? orderProductInfo.getSupervisionMode() : "0110");
                                    takingDataFromTheProductLibrary(orderProductInfo);
                                } else {
                                    orderInfo.setSupervisionMode(isNotBlank(orderInfo.getSupervisionMode()) ? orderInfo.getSupervisionMode() : "0214");
                                    orderProductInfo.setSupervisionMode(isNotBlank(orderProductInfo.getSupervisionMode()) ? orderProductInfo.getSupervisionMode() : "0214");
                                    // 2024/10/11 14:34@ZHANGCHAO 追加/变更/完善：带取账册
                                    if (isNotBlank(orderInfo.getRecordNumber())) {
                                        takingDataFromTheAccountBook(orderProductInfo, orderInfo);
                                    }
                                }
                                orderProductInfoList.add(orderProductInfo);
                                if (isNotBlank(importBusinessExcelEntityE.getDeliveryNoteNumber())) {
                                    deliveryNoteNumbers.add(importBusinessExcelEntityE.getDeliveryNoteNumber());
                                }
                            }
                            if (isNotEmpty(deliveryNoteNumbers)) {
                                orderInfo.setDeliveryNoteNumber(CollUtil.join(CollUtil.distinct(deliveryNoteNumbers), "/"));
                            }
//                            orderProductInfoService.saveBatch(orderProductInfoList);
                            orderInfo.setProductList(orderProductInfoList);
                            OrderInfoBiz orderInfoBiz = new OrderInfoBiz();
                            BeanUtil.copyProperties(orderInfo, orderInfoBiz, CopyOptions.create().ignoreNullValue());
                            this.saveOrder(orderInfoBiz);
                            successLines.getAndIncrement();
                        }
                    } catch (Exception e) {
                        errorLines.getAndIncrement();
                        log.error(e.getMessage(), e);
                        ExceptionUtil.getFullStackTrace(e);
                        errorMessage.add(e.getMessage().length() > 50 ? e.getMessage().substring(0, 50) : e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                ExceptionUtil.getFullStackTrace(e);
//                return Result.error("文件导入失败：" + e.getMessage());
                return Result.error("文件导入失败，请检查模版文件！");
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                    ExceptionUtil.getFullStackTrace(e);
                    log.error(e.getMessage(), e);
                }
            }
        }
        if (isNotEmpty(errorMessage)) {
            return Result.error("归并后成功导入行数："+ successLines.get() + "；失败行数：" + errorLines.get() + "；"
                    + CollUtil.join(errorMessage, ","));
        }
        return ImportExcelUtil.imporReturnRes(errorLines.get(), successLines.get(), errorMessage);
    }

    /**
     * 将小物料替换为大物料 -出口
     * @param importBusinessExcelEntityEList
     */
    private void handleMergePnImportE(List<ImportBusinessExcelEntityE> importBusinessExcelEntityEList,List<String> noMatchPns){
        List<String> pns = importBusinessExcelEntityEList.stream().map(ImportBusinessExcelEntityE::getPn)
                .filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<ProductMerge> productMergeList=productMergeMapper.selectList(new LambdaQueryWrapper<ProductMerge>()
                .in(ProductMerge::getMergePn,pns));
        Map<String,String> stringMap2 =
                productMergeList.stream().collect(Collectors.toMap
                        (ProductMerge::getMergePn,ProductMerge::getProductPn,(value1, value2) -> value1));

        for(ImportBusinessExcelEntityE importBusinessExcelEntityE:importBusinessExcelEntityEList){
            if(isNotBlank(importBusinessExcelEntityE.getPn())){
                if(stringMap2.containsKey(importBusinessExcelEntityE.getPn())){
                    importBusinessExcelEntityE.setPn(stringMap2.get(importBusinessExcelEntityE.getPn()));
                }else {
                    noMatchPns.add(importBusinessExcelEntityE.getPn());
                }
            }
        }
    }
    /**
     * 将小物料替换为大物料-进口
     * @param importBusinessExcelEntityEList
     */
    private void handleMergePnImportI(List<ImportBusinessExcelEntity> importBusinessExcelEntityEList,List<String> noMatchPns){
        List<String> pns = importBusinessExcelEntityEList.stream().map(ImportBusinessExcelEntity::getPn)
                .filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<ProductMerge> productMergeList=productMergeMapper.selectList(new LambdaQueryWrapper<ProductMerge>()
                .in(ProductMerge::getMergePn,pns));
        Map<String,String> stringMap2 =
                productMergeList.stream().collect(Collectors.toMap
                        (ProductMerge::getMergePn,ProductMerge::getProductPn,(value1, value2) -> value1));

        for(ImportBusinessExcelEntity importBusinessExcelEntity:importBusinessExcelEntityEList){
            if(isNotBlank(importBusinessExcelEntity.getPn())){
                if(stringMap2.containsKey(importBusinessExcelEntity.getPn())){
                    importBusinessExcelEntity.setPn(stringMap2.get(importBusinessExcelEntity.getPn()));
                }else {
                    noMatchPns.add(importBusinessExcelEntity.getPn());
                }
            }
        }
    }

    /**
     * 处理模板数据
     */
    private void handleTemplateImportE(List<ImportBusinessExcelEntityE> importBusinessExcelEntityEList,
                                       OrderInfoTemplate orderInfoTemplate,String tenantName){
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap2 = new HashMap<>();
        //租户为博汇的出境关别需要转换 从字典取
        if("山东博汇纸业股份有限公司".equals(tenantName)){
            //博汇业务导入进出境关别
            List<DictModel> dictModels1 = sysBaseApi.getDictItems("BHJCJGB");
            if (isNotEmpty(dictModels1)) {
                dictModels1.forEach(dictModel -> {
                    dictMap1.put(dictModel.getText(), dictModel.getValue());
                });
            }
            //博汇备案号对应账册号
            List<DictModel> dictModels2 = sysBaseApi.getDictItems("BHBAZC");
            if (isNotEmpty(dictModels2)) {
                dictModels2.forEach(dictModel -> {
                    dictMap2.put(dictModel.getText(), dictModel.getValue());
                });
            }
        }

        //存在模板 两种情况，1：完全以模板为主 2 ：表格中有值按表格，无值按照模板
        if("1".equals(orderInfoTemplate.getIsFirst())){
//            表头部分
            for(ImportBusinessExcelEntityE importBusinessExcelEntityE:importBusinessExcelEntityEList){
                String exitClearance = importBusinessExcelEntityE.getExitClearance();
                String recordNumber = importBusinessExcelEntityE.getRecordNumber();
                //先拷贝相同字段的值
                BeanUtil.copyProperties(orderInfoTemplate,importBusinessExcelEntityE,
                        "id","createBy","createTime","updateBy","updateTime");
                importBusinessExcelEntityE.setBuyer(orderInfoTemplate.getReceiverId());
                importBusinessExcelEntityE.setBuyerName(orderInfoTemplate.getReceiver());
                importBusinessExcelEntityE.setTransMode(orderInfoTemplate.getTradingType());
                importBusinessExcelEntityE.setDistrictCode(orderInfoTemplate.getDomesticSourceOfGoods());
                //处理出境关别
                if(isNotBlank(exitClearance)){
                    if(!dictMap1.isEmpty()){
                        String value = dictMap1.get(exitClearance);
                        if(isNotBlank(value)){
                            List<String> valueList = Arrays.asList(value.split(","));
                            if(valueList.size()>1){
                                importBusinessExcelEntityE.setExitClearance(valueList.get(0));
                                importBusinessExcelEntityE.setDeparturePort(valueList.get(1));
                            }
                        }
                    }
                }
                if(isNotBlank(recordNumber)){
                    if(!dictMap2.isEmpty()){
                        String value = dictMap2.get(recordNumber);
                        if(isNotBlank(value)){
                            importBusinessExcelEntityE.setRecordNumber(value);
                        }
                    }
                }
                //处理表体
                if(!orderInfoTemplate.getOrderProductInfoTemplateList().isEmpty()){
                    importBusinessExcelEntityE.setOriginCountry(
                            orderInfoTemplate.getOrderProductInfoTemplateList().get(0).getOriginCountry());
                    importBusinessExcelEntityE.setFaxTypeCode(
                            orderInfoTemplate.getOrderProductInfoTemplateList().get(0).getFaxTypeCode());
                }

            }
        }else {
            //            表头部分
            for(ImportBusinessExcelEntityE importBusinessExcelEntityE:importBusinessExcelEntityEList){
                String exitClearance = importBusinessExcelEntityE.getExitClearance();
                String recordNumber = importBusinessExcelEntityE.getRecordNumber();
                //先拷贝相同字段的值
                //表格中有值按表格，无值按照模板
                CopyOptions copyOption =
                        CopyOptions.create(null, true,
                                "id","createBy","createTime","updateBy","updateTime");
                BeanUtil.copyProperties(orderInfoTemplate,importBusinessExcelEntityE,
                        copyOption);
                importBusinessExcelEntityE.setBuyer(isBlank(importBusinessExcelEntityE.getBuyer())?
                        orderInfoTemplate.getReceiverId():importBusinessExcelEntityE.getBuyer());
                importBusinessExcelEntityE.setBuyerName(isBlank(importBusinessExcelEntityE.getBuyerName())?
                        orderInfoTemplate.getReceiver():importBusinessExcelEntityE.getBuyerName());
                importBusinessExcelEntityE.setTransMode(isBlank(importBusinessExcelEntityE.getTransMode())?
                        orderInfoTemplate.getTradingType():importBusinessExcelEntityE.getTransMode());
                importBusinessExcelEntityE.setDistrictCode(isBlank(importBusinessExcelEntityE.getDistrictCode())?
                        orderInfoTemplate.getDomesticSourceOfGoods():importBusinessExcelEntityE.getDistrictCode());
                //处理出境关别
                if(isNotBlank(exitClearance)){
                    if(!dictMap1.isEmpty()){
                        String value = dictMap1.get(importBusinessExcelEntityE.getExitClearance());
                        if(isNotBlank(value)){
                            List<String> valueList = Arrays.asList(value.split(","));
                            if(valueList.size()>1){
                                importBusinessExcelEntityE.setExitClearance(valueList.get(0));
                                importBusinessExcelEntityE.setDeparturePort(valueList.get(1));
                            }
                        }
                    }
                }
                if(isNotBlank(recordNumber)){
                    if(!dictMap2.isEmpty()){
                        String value = dictMap2.get(importBusinessExcelEntityE.getRecordNumber());
                        if(isNotBlank(value)){
                            importBusinessExcelEntityE.setRecordNumber(value);
                        }
                    }
                }
                //处理表体
                if(!orderInfoTemplate.getOrderProductInfoTemplateList().isEmpty()){
                    importBusinessExcelEntityE.setOriginCountry(isBlank(importBusinessExcelEntityE.getOriginCountry())?
                            orderInfoTemplate.getOrderProductInfoTemplateList().get(0).getOriginCountry():
                            importBusinessExcelEntityE.getOriginCountry());
                    importBusinessExcelEntityE.setFaxTypeCode(isBlank(importBusinessExcelEntityE.getFaxTypeCode())?
                            orderInfoTemplate.getOrderProductInfoTemplateList().get(0).getFaxTypeCode()
                            :importBusinessExcelEntityE.getFaxTypeCode());
                }
            }
        }
    }

    /**
     * 从账册带取数据
     *
     * @param orderProductInfo
     * @param orderInfo
     * @return void
     * <AUTHOR>
     * @date 2024/10/11 14:35
     */
    private void takingDataFromTheAccountBook(OrderProductInfo orderProductInfo, OrderInfo orderInfo) {
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_units,name,code");
        Map<String, String> dictMap1 = new HashMap<>();
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> {
                dictMap1.put(dictModel.getValue(), dictModel.getText());
            });
        }
        if ("I".equals(orderInfo.getIeFlag())) {
            PtsEmsAimg ptsEmsAimg = emsAimgMapper.getOneByCond(orderProductInfo.getPn(), orderInfo.getRecordNumber(), String.valueOf(orderInfo.getTenantId()));
            if (isNotEmpty(ptsEmsAimg)) {
                orderProductInfo.setPn(isNotBlank(orderProductInfo.getPn()) ? orderProductInfo.getPn() : ptsEmsAimg.getCopGno());
                orderProductInfo.setChineseName(isNotBlank(orderProductInfo.getChineseName()) ? orderProductInfo.getChineseName() : ptsEmsAimg.getGName());
                orderProductInfo.setInvoicesRecordItem(isNotEmpty(orderProductInfo.getInvoicesRecordItem()) ? orderProductInfo.getInvoicesRecordItem() : ptsEmsAimg.getGNo());
                orderProductInfo.setHscode(isNotBlank(orderProductInfo.getHscode()) ? orderProductInfo.getHscode() : ptsEmsAimg.getCodet());
                orderProductInfo.setCustomsCodeInfoCode(isNotBlank(orderProductInfo.getCustomsCodeInfoCode()) ? orderProductInfo.getCustomsCodeInfoCode() : ptsEmsAimg.getCodet());
                orderProductInfo.setHsname(isNotBlank(orderProductInfo.getHsname()) ? orderProductInfo.getHsname() : ptsEmsAimg.getGName());
                orderProductInfo.setHsmodel(isNotBlank(orderProductInfo.getHsmodel()) ? orderProductInfo.getHsmodel() : ptsEmsAimg.getGModel());
                orderProductInfo.setCustomsDeclarationElements(isNotBlank(orderProductInfo.getCustomsDeclarationElements()) ? orderProductInfo.getCustomsDeclarationElements() : ptsEmsAimg.getGModel());
                orderProductInfo.setSpecificationModel(isNotBlank(orderProductInfo.getSpecificationModel()) ? orderProductInfo.getSpecificationModel() : ptsEmsAimg.getGModel());
                orderProductInfo.setShipmentUnit(isNotBlank(orderProductInfo.getShipmentUnit()) ? orderProductInfo.getShipmentUnit() : ptsEmsAimg.getUnit());
                orderProductInfo.setDecQunit(isNotBlank(orderProductInfo.getDecQunit()) ? orderProductInfo.getDecQunit() : ptsEmsAimg.getUnit()); // 申报单位
                orderProductInfo.setLegalUnitCode(isNotBlank(orderProductInfo.getLegalUnitCode()) ? orderProductInfo.getLegalUnitCode() : ptsEmsAimg.getUnit1());
                orderProductInfo.setLegalUnit(isNotBlank(orderProductInfo.getLegalUnit()) ? orderProductInfo.getLegalUnit() : dictMap1.get(ptsEmsAimg.getUnit1()));
                orderProductInfo.setSecondUnitCode(isNotBlank(orderProductInfo.getSecondUnitCode()) ? orderProductInfo.getSecondUnitCode() : ptsEmsAimg.getUnit2());
                orderProductInfo.setSecondUnit(isNotBlank(orderProductInfo.getSecondUnit()) ? orderProductInfo.getSecondUnit() : dictMap1.get(ptsEmsAimg.getUnit2()));
                orderProductInfo.setLegalQuantity(isNotEmpty(orderProductInfo.getLegalQuantity()) ? orderProductInfo.getLegalQuantity() : ptsEmsAimg.getCount1());
                orderProductInfo.setSecondNumbers(isNotEmpty(orderProductInfo.getSecondNumbers()) ? orderProductInfo.getSecondNumbers() : ptsEmsAimg.getCount2());
                orderProductInfo.setNetWeight(isNotEmpty(orderProductInfo.getNetWeight()) ? orderProductInfo.getNetWeight() : ptsEmsAimg.getNetWeight());
                orderProductInfo.setCustomsDeclarationCurrency(isNotBlank(orderProductInfo.getCustomsDeclarationCurrency()) ? orderProductInfo.getCustomsDeclarationCurrency() : ptsEmsAimg.getCurr());
                orderProductInfo.setOriginCountry(isNotBlank(orderProductInfo.getOriginCountry()) ? orderProductInfo.getOriginCountry() : ptsEmsAimg.getCountryCode());
            }
        } else if ("E".equals(orderInfo.getIeFlag())) {
//            List<PtsEmsAexg> ptsEmsAexgList = emsAexgMapper.selectList(new LambdaQueryWrapper<PtsEmsAexg>()
//                    .eq(PtsEmsAexg::getCopGno, orderProductInfo.getPn())
//                    .eq(PtsEmsAexg::getEmsNo, orderInfo.getRecordNumber()));
            PtsEmsAexg ptsEmsAexg = emsAexgMapper.getOneByCond(orderProductInfo.getPn(), orderInfo.getRecordNumber(), String.valueOf(orderInfo.getTenantId()));
            if (isNotEmpty(ptsEmsAexg)) {
                orderProductInfo.setPn(isNotBlank(orderProductInfo.getPn()) ? orderProductInfo.getPn() : ptsEmsAexg.getCopGno());
                orderProductInfo.setChineseName(isNotBlank(orderProductInfo.getChineseName()) ? orderProductInfo.getChineseName() : ptsEmsAexg.getGName());
                orderProductInfo.setInvoicesRecordItem(isNotEmpty(orderProductInfo.getInvoicesRecordItem()) ? orderProductInfo.getInvoicesRecordItem() : ptsEmsAexg.getGNo());
                orderProductInfo.setHscode(isNotBlank(orderProductInfo.getHscode()) ? orderProductInfo.getHscode() : ptsEmsAexg.getCodet());
                orderProductInfo.setCustomsCodeInfoCode(isNotBlank(orderProductInfo.getCustomsCodeInfoCode()) ? orderProductInfo.getCustomsCodeInfoCode() : ptsEmsAexg.getCodet());
                orderProductInfo.setHsname(isNotBlank(orderProductInfo.getHsname()) ? orderProductInfo.getHsname() : ptsEmsAexg.getGName());
                orderProductInfo.setHsmodel(isNotBlank(orderProductInfo.getHsmodel()) ? orderProductInfo.getHsmodel() : ptsEmsAexg.getGModel());
                orderProductInfo.setCustomsDeclarationElements(isNotBlank(orderProductInfo.getCustomsDeclarationElements()) ? orderProductInfo.getCustomsDeclarationElements() : ptsEmsAexg.getGModel());
                orderProductInfo.setSpecificationModel(isNotBlank(orderProductInfo.getSpecificationModel()) ? orderProductInfo.getSpecificationModel() : ptsEmsAexg.getGModel());
                orderProductInfo.setShipmentUnit(isNotBlank(orderProductInfo.getShipmentUnit()) ? orderProductInfo.getShipmentUnit() : ptsEmsAexg.getUnit());
                orderProductInfo.setDecQunit(isNotBlank(orderProductInfo.getDecQunit()) ? orderProductInfo.getDecQunit() : ptsEmsAexg.getUnit()); // 申报单位
                orderProductInfo.setLegalUnitCode(isNotBlank(orderProductInfo.getLegalUnitCode()) ? orderProductInfo.getLegalUnitCode() : ptsEmsAexg.getUnit1());
                orderProductInfo.setLegalUnit(isNotBlank(orderProductInfo.getLegalUnit()) ? orderProductInfo.getLegalUnit() : dictMap1.get(ptsEmsAexg.getUnit1()));
                orderProductInfo.setSecondUnitCode(isNotBlank(orderProductInfo.getSecondUnitCode()) ? orderProductInfo.getSecondUnitCode() : ptsEmsAexg.getUnit2());
                orderProductInfo.setSecondUnit(isNotBlank(orderProductInfo.getSecondUnit()) ? orderProductInfo.getSecondUnit() : dictMap1.get(ptsEmsAexg.getUnit2()));
//                orderProductInfo.setLegalQuantity(isNotEmpty(orderProductInfo.getLegalQuantity()) ? orderProductInfo.getLegalQuantity() : ptsEmsAexg.getCount1());
//                orderProductInfo.setSecondNumbers(isNotEmpty(orderProductInfo.getSecondNumbers()) ? orderProductInfo.getSecondNumbers() : ptsEmsAexg.getCount2());
               //    1. 出口业务导入，如果带取的账册单位是千克（035），则将数量赋值到净重字段。 20241126 by 张连良提出
                if("035".equals(ptsEmsAexg.getUnit())){
                    orderProductInfo.setNetWeight(orderProductInfo.getDecQty());
                    orderProductInfo.setShipmentUnit("KG");
                }else {
                    orderProductInfo.setNetWeight(isNotEmpty(orderProductInfo.getNetWeight()) ? orderProductInfo.getNetWeight() : ptsEmsAexg.getNetWeight());
                }
                orderProductInfo.setCustomsDeclarationCurrency(isNotBlank(orderProductInfo.getCustomsDeclarationCurrency()) ? orderProductInfo.getCustomsDeclarationCurrency() : ptsEmsAexg.getCurr());
                orderProductInfo.setOriginCountry(isNotBlank(orderProductInfo.getOriginCountry()) ? orderProductInfo.getOriginCountry() : ptsEmsAexg.getCountryCode());
            }
        }

    }

    /**
     * 从品名库带取数据
     *
     * @param orderProductInfo
     * @return void
     * <AUTHOR>
     * @date 2024/10/10 21:49
     */
    private void takingDataFromTheProductLibrary(OrderProductInfo orderProductInfo) {
        ProductInfo productInfo = productInfoMapper.selectOne(new LambdaQueryWrapper<ProductInfo>()
                .eq(ProductInfo::getPn, orderProductInfo.getPn()));
        if (isNotEmpty(productInfo)) {
            orderProductInfo.setPn(productInfo.getPn());
            orderProductInfo.setChineseName(productInfo.getChineseName());
            orderProductInfo.setEnglishName(productInfo.getEnglishName());
            orderProductInfo.setOriginCountry(productInfo.getOriginCountry());
            orderProductInfo.setHscode(productInfo.getCustomsCodeInfoCode());
            orderProductInfo.setHsname(productInfo.getHsname());
            orderProductInfo.setHsmodel(productInfo.getCustomsDeclarationElements());
            orderProductInfo.setCustomsCodeInfoCode(productInfo.getCustomsCodeInfoCode());
            orderProductInfo.setMonitorcondition(productInfo.getMonitorcondition());
            orderProductInfo.setIaqcategory(productInfo.getIaqcategory());
            orderProductInfo.setTaxRebateRate(productInfo.getTaxRebateRate());
            orderProductInfo.setAddedTaxRate(productInfo.getAddedTaxRate());
            //净重和单价浮动预警
//            if(null!=productInfo.getNetWeight()&&null!=productInfo.getNetWeightFluctuationRatio()){
//                //净重校验
//                BigDecimal netWeightMin = productInfo.getNetWeight().multiply(new BigDecimal(1)
//                        .subtract(productInfo.getNetWeightFluctuationRatio().divide(new BigDecimal(100))));
//
//            }

        }else {
            //20241210 如果物料号不存在，直接入商品库，未审核状态，并添加预警
            ProductInfo productInfoAdd = new ProductInfo();
            productInfoAdd.setPn(orderProductInfo.getPn());
            productInfoAdd.setChineseName(orderProductInfo.getChineseName());
            productInfoAdd.setEnglishName(orderProductInfo.getEnglishName());
            productInfoAdd.setOriginCountry(orderProductInfo.getOriginCountry());
            productInfoAdd.setCustomsCodeInfoCode(orderProductInfo.getHscode());
            productInfoAdd.setHsname(orderProductInfo.getHsname());
            productInfoAdd.setCustomsDeclarationElements(orderProductInfo.getHsmodel());
            productInfoAdd.setCustomsCodeInfoCode(orderProductInfo.getCustomsCodeInfoCode());
            productInfoAdd.setMonitorcondition(orderProductInfo.getMonitorcondition());
            productInfoAdd.setIaqcategory(orderProductInfo.getIaqcategory());
            productInfoAdd.setTaxRebateRate(orderProductInfo.getTaxRebateRate());
            productInfoAdd.setAddedTaxRate(orderProductInfo.getAddedTaxRate());
            productInfoAdd.setTenantId(Long.valueOf(TenantContext.getTenant()));
            productInfoAdd.setCreateBy(orderProductInfo.getCreateBy());
            productInfoAdd.setCreateTime(orderProductInfo.getCreateTime());
            productInfoMapper.insert(productInfoAdd);
            handleAddRiskWarning("新物料预警:"+productInfoAdd.getPn(),"请注意，物料号："+productInfoAdd.getPn()+
                    "为新物料，请及时进行商品备案审核。");
        }
    }
    /**
     * 新物料预警
     */
    private void handleAddRiskWarning(String title,String msgContent){
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        sysAnnouncement.setTitile(title);
        //消息类型为预警消息
        sysAnnouncement.setMsgCategory("4");
        sysAnnouncement.setMsgType(MSG_TYPE_TENANT);
        sysAnnouncement.setMsgContent(msgContent);
        sysAnnouncement.setBusType("warning");

        sysAnnouncement.setCreateBy("TASK");
        sysAnnouncement.setSender("TASK");
        sysAnnouncement.setTenantId(TenantContext.getTenant());
        BaseMap baseMap = new BaseMap();
        baseMap.put("message", JSON.toJSONString(sysAnnouncement));
        jeecgRedisClient.sendMessage(REDIS_BUSINESS_HANDLER, baseMap);

    }

    /**
     * 业务快捷生成草单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/26 13:45
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> generateSaveDecAndInvt(String id, HttpServletRequest request) {
        //20250312支持批量生成草单，传入值可能为多个id，逗号分隔
        List<String> idList = Arrays.asList(id.split(","));
        List<OrderInfo> orderInfos = orderInfoMapper.selectBatchIds(idList);
        if (isEmpty(orderInfos)) {
            return Result.error("未获取到业务信息");
        }
//        if (!ApplyType.DEC.equals(apply.getApplyType())){
//            return  YmMsg.error("快捷生成申报单只允许普货类型的委托");
//        }
        try {
            for(OrderInfo orderInfo:orderInfos) {
                List<OrderProductInfo> productInfoList = orderProductInfoService.list(new LambdaQueryWrapper<OrderProductInfo>()
                        .eq(OrderProductInfo::getOrderInfoId, orderInfo.getId()));
                if (isEmpty(productInfoList)) {
                    return Result.error("未获取到业务商品信息");
                }
                // 2025/4/4 08:29@ZHANGCHAO 追加/变更/完善：重新带取品名库
                try {
                    for (OrderProductInfo orderProductInfo : productInfoList) {
                        if (isBlank(orderProductInfo.getHscode()) || isBlank(orderProductInfo.getHsname()) || isBlank(orderProductInfo.getHsmodel())) {
                            List<ProductInfo> productInfos = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                                    .eq(ProductInfo::getPn, orderProductInfo.getPn())
                                    .eq(isNotBlank(orderProductInfo.getChineseName()), ProductInfo::getChineseName, orderProductInfo.getChineseName())
                                    .eq(isNotBlank(orderProductInfo.getEnglishName()), ProductInfo::getEnglishName, orderProductInfo.getEnglishName()));
                            if (isNotEmpty(productInfos)) {
                                orderProductInfo.setProductId(productInfos.get(0).getId());
                                orderProductInfo.setChineseName(productInfos.get(0).getChineseName());
                                orderProductInfo.setEnglishName(productInfos.get(0).getEnglishName());
                                orderProductInfo.setProductSpecificationModel(productInfos.get(0).getProductSpecificationModel());
                                orderProductInfo.setCustomsCodeInfoCode(productInfos.get(0).getCustomsCodeInfoCode());
                                orderProductInfo.setHscode(productInfos.get(0).getCustomsCodeInfoCode());
                                orderProductInfo.setHsname(productInfos.get(0).getHsname());
                                // 2025/2/28 18:01@ZHANGCHAO 追加/变更/完善：针对品名库中有变量的！！
                                String hsmodel = productInfos.get(0).getCustomsDeclarationElements();
                                if (isNotBlank(hsmodel) && hsmodel.contains("{}")) {
                                    if (isNotBlank(orderInfo.getExportContractNo())) {
                                        Pattern pattern = Pattern.compile("-(\\d{8})-");
                                        Matcher matcher = pattern.matcher(orderInfo.getExportContractNo());
                                        if (matcher.find()) {
                                            String dateStr = matcher.group(1);
                                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                                            LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                                            String formattedDate = date.format(outputFormatter);
                                            log.info("提取的日期: {}", dateStr);
                                            log.info("格式化后的日期: {}", formattedDate);
                                            hsmodel = hsmodel.replace("{}", formattedDate);
                                        } else {
                                            log.info("无法从合同编号提取日期: {}", orderInfo.getExportContractNo());
                                        }
                                    }
                                }
                                orderProductInfo.setHsmodel(hsmodel);
                                orderProductInfo.setCustomsDeclarationElements(hsmodel);
                                orderProductInfoService.updateById(orderProductInfo);
                            } else {
                                log.info("【生成草单重新带取商品库】未找到匹配的品名信息：pn={}, chineseName={}, englishName={}", orderProductInfo.getPn(), orderProductInfo.getChineseName(), orderProductInfo.getEnglishName());
                            }
                        }
                    }
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error("重新带取品名库出现异常：", e.getMessage());
                }
                orderInfo.setProductList(productInfoList);

                // 2024/10/15 08:27@ZHANGCHAO 追加/变更/完善：判断是否已经生成了报关单核注单！！
                try {
                    judgeBeDecOrInvt(orderInfo);
                } catch (RuntimeException e) {
                    log.error(e.getMessage());
                    return Result.error(e.getMessage());
                }


                //检测税号和申报规范
                List<OrderInfo> applyList = new ArrayList<>();
                applyList.add(orderInfo);
                judgeBeHsCodes(applyList);

                // 2024/10/24 08:45@ZHANGCHAO 追加/变更/完善：处理运输方式
                if ("SEA".equals(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("2");
                } else if ("AIR".equals(orderInfo.getShippingType())
                        || "DHL".equals(orderInfo.getShippingType())
                        || "UPS".equals(orderInfo.getShippingType())
                        || "FedEx".equals(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("5");
                } else if ("RAILWAY".equals(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("3");
                } else if ("TRUCK".equals(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("4");
                } else if ("OTHERS".equals(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("9");
                }
                if (isBlank(orderInfo.getShippingType())) {
                    orderInfo.setShippingType("2"); // 最终默认个海运吧
                }
                // 2024/10/24 08:50@ZHANGCHAO 追加/变更/完善：提前处理单位！
                for (OrderProductInfo orderProductInfo : orderInfo.getProductList()) {
                    if (isNotBlank(orderProductInfo.getShipmentUnit()) && !isNumeric(orderProductInfo.getShipmentUnit())) {
                        DictQuery dictQuery = commonMapper.getUnitsQueryByKey(orderProductInfo.getShipmentUnit());
                        if (isNotEmpty(dictQuery)) {
                            orderProductInfo.setShipmentUnit(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : orderProductInfo.getShipmentUnit());
                        }
                    }
                    if (isNotBlank(orderProductInfo.getDecQunit()) && !isNumeric(orderProductInfo.getDecQunit())) {
                        DictQuery dictQuery = commonMapper.getUnitsQueryByKey(orderProductInfo.getDecQunit());
                        if (isNotEmpty(dictQuery)) {
                            orderProductInfo.setDecQunit(isNotBlank(dictQuery.getCode()) ? dictQuery.getCode() : orderProductInfo.getDecQunit());
                        }
                    }
                }
//        if (productInfoList.size() > 50) {
//            return Result.error("业务商品项信息不允许超过50项");
//        }

                /**********************获取模版和申报前配置可以放后面去！！！***********************/
                // 设置忽略租户插件
                InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                //获取报关单模板信息
                DecHeadTempleteVO decHeadTempleteVO = new DecHeadTempleteVO();
                decHeadTempleteVO.setIeFlag(orderInfo.getIeFlag());
                decHeadTempleteVO.setShipTypeCode(orderInfo.getShippingType());
                decHeadTempleteVO.setTenantId(TenantContext.getTenant());
                Result<?> decHeadTempleteResult = decHeadTempleteService.listDecTemp(decHeadTempleteVO, 1, Integer.MAX_VALUE);
                // 关闭忽略策略
                InterceptorIgnoreHelper.clearIgnoreStrategy();
                List<DecHeadTemplete> decHeadTempletes = isNotEmpty(decHeadTempleteResult.getResult()) ? (List<DecHeadTemplete>) decHeadTempleteResult.getResult() : new ArrayList<>();
                DecHeadTemplete decHeadTemplete = isNotEmpty(decHeadTempletes) ? decHeadTempletes.get(0) : null;
                log.info("获取{}条报关单模板信息：{}", decHeadTempletes.size(), decHeadTemplete);
//        ApplyConfig config = new ApplyConfig();
//        config.setVotes(50);
//        config.setDecTemplate(decHeadTemplete != null ? decHeadTemplete.getId().toString() : null);
                // 2024/10/15 13:17@ZHANGCHAO 追加/变更/完善：申报前配置，已维护到库里！！
                ApplyConfig config;
                List<ApplyConfig> applyConfigList = applyConfigService.list(new LambdaQueryWrapper<ApplyConfig>()
                        .eq(ApplyConfig::getImSign, orderInfo.getIeFlag())
                        .eq(ApplyConfig::getTradeType, orderInfo.getSupervisionMode())
                        .eq(ApplyConfig::getConsignorId, TenantContext.getTenant()));
                if (isNotEmpty(applyConfigList)) {
                    config = applyConfigList.get(0);
                } else {
                    config = new ApplyConfig();
                    config.setVotes(50);
                }
                config.setDecTemplate(isNotEmpty(decHeadTemplete) ? decHeadTemplete.getId().toString() : null);

                Map<String, ApplyConfig> applyConfigMap = new HashMap<>();
                String identifing;
                if (isNotEmpty(orderInfo.getBuyer())) {
                    identifing = orderInfo.getBuyer() + "|" + orderInfo.getSupervisionMode();
                } else {
                    identifing = "|" + orderInfo.getSupervisionMode();
                }
                applyConfigMap.put(identifing, config);
                //模拟一条申报前配置信息
                orderInfo.setApplyConfigMap(applyConfigMap);
                /**********************获取模版和申报前配置可以放后面去！！！***********************/
                //设置快捷生辰申报单的标记
//        orderInfo.setQuick(true);
//        apply.setOutTradeType(apply.getTradeType());

                //生成草单
                generalApplyDraft(orderInfo);

                //汇总生成的数据
                List<NemsInvtHead> nemsInvtHeads = new ArrayList<>();
                List<DecHead> decHeads = new ArrayList<>();
                if (isNotEmpty(orderInfo.getNemsInvtHeads())) {
                    nemsInvtHeads.addAll(orderInfo.getNemsInvtHeads());
                }
                if (isNotEmpty(orderInfo.getDecHeads())) {
                    decHeads.addAll(orderInfo.getDecHeads());
                }
                //处理提运单号
                handleBillCode(decHeads, orderInfo);
                //处理企业内部编号
                handleEtpsInnerNo(orderInfo, nemsInvtHeads, decHeads);
                //处理虚拟id
//        handleVid(nemsInvtHeads,decHeads);
                //TODO：报关单监管为出料加工时征免方式进口时全免，出口是照章征税
                if (isNotEmpty(decHeads)) {
                    decHeads.forEach(v -> {
                        if ("1427".equals(v.getTradeTypeCode())) {
                            v.getDecLists().forEach(l -> {
                                if ("I".equals(v.getIeFlag())) {
                                    l.setFaxTypeCode("3");
                                }
                                if ("E".equals(v.getIeFlag())) {
                                    l.setFaxTypeCode("1");
                                }
                            });
                        }
                    });
                }
                orderInfo.setNemsInvtHeads(nemsInvtHeads);
                orderInfo.setDecHeads(decHeads);
            }
        } catch (RuntimeException e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("生成草单出现异常：{}", e.getMessage());
            return Result.error("生成草单出现异常:[" + (e.getMessage().length() > 30 ? (e.getMessage().substring(0, 30)+"...") : e.getMessage()) + "]，请检查数据或联系管理员！");
        }
        //保存申报单并变更委托单状态
        Result<?> result = this.saveApplyAndDeclaration(orderInfos);
//        DecHead decHead = new DecHead();
//        generateDecHeadByOrder(orderInfo, decHead, request);
//        String saveDecHead = decHeadService.saveDecHead(decHead);
//        decHead.setId(saveDecHead);
        return result;
    }

    /**
     * 处理企业内部编号
     * @param apply
     * @param invtHeads
     * @throws RuntimeException
     */
    private void handleEtpsInnerNo(OrderInfo apply,List<NemsInvtHead> invtHeads,List<DecHead> decHeads) throws RuntimeException {
        int number = 1;
        for (NemsInvtHead v : invtHeads){
            /*
             * 生成草单，核注单企内取业务的提单号，业务没有就自动生成一个，注意判断重复自动增加-1 2。
             * 2024/12/13 14:07@ZHANGCHAO
             */
            if (isNotBlank(apply.getDeliveryNumbers())) {
                if (invtHeads.size() > 1) {
                    v.setEtpsInnerInvtNo(apply.getDeliveryNumbers() + "_" + number);
                } else {
                    v.setEtpsInnerInvtNo(apply.getDeliveryNumbers());
                }
                number++;
            } else {
                v.setEtpsInnerInvtNo("H" + v.getId());
            }
        }
        for (NemsInvtHead v : invtHeads){
            String originalInvtNo = v.getEtpsInnerInvtNo();
            String finalInvtNo = originalInvtNo;
            try {
                int suffix = 1;
                while (isInventoryNumberExists(finalInvtNo)) {
                    if (originalInvtNo.matches(".*_\\d+$")) {
                        String basePart = originalInvtNo.replaceAll("_\\d+$", "");
                        suffix = Integer.parseInt(originalInvtNo.replaceAll(".*_", "")) + 1;
                        finalInvtNo = basePart + "_" + suffix;
                    } else {
                        finalInvtNo = originalInvtNo + "_" + suffix;
                    }
                    suffix++;
                }
            } catch (NumberFormatException e) {
                log.error("处理提运单号时发生错误：{}", e.getMessage());
            }
            // 更新库存编号
            v.setEtpsInnerInvtNo(finalInvtNo);
        }
    }

    // 辅助方法：检查库存编号是否已存在
    private boolean isInventoryNumberExists(String invtNo) {
        List<NemsInvtHead> nemsInvtHeads = baseMapper.isexistInvtNo(invtNo);
        if (isNotEmpty(nemsInvtHeads)) {
            return true;
        }
        return false;
    }

    /**
     * 处理提运单号
     * @param decHeads
     */
    private void handleBillCode(List<DecHead> decHeads,OrderInfo apply){
        Integer number = 1;
        String string  = "A";

        char [] tempChar = string.toCharArray();
        for (DecHead v : decHeads) {
            if (isBlank(v.getBillCode())){
                continue;
            }
            v.setBillCode(v.getBillCode().replace("-", ""));
            //提单号拼接
            char letter[]= string.toCharArray();
            letter[0]=  (char)(tempChar[0]-1+number);
            String _billCode = "";
            if (letter[0] <= 'Z'){
                _billCode = "_" + String.valueOf(letter);
            }
//			String _billCode = "_" + billCodeMap.get(number.toString());

            if (isNotEmpty(v.getBillCode()) && decHeads.size() > 1) {
                v.setBillCode(v.getBillCode() + _billCode);
                number++;
            }
        }

    }

    /**
     * 判断是否已经生成报关单或核注单
     * @param apply
     * @throws RuntimeException
     */
    private void judgeBeDecOrInvt(OrderInfo apply) throws RuntimeException {
        // 0.检查是否存在申报数据
//        List<NemsInvtHead> invtHeads =
//                RpcKit.getList(nemsInvtApi, NemsInvtApi::listInvtByApplyNumber, apply.getId().toString());
        List<NemsInvtHead> invtHeads = nemsInvtHeadService.list(new LambdaQueryWrapper<NemsInvtHead>().eq(NemsInvtHead::getApplyNumber, apply.getId()));
        if (invtHeads != null && invtHeads.size() > 0) {
            throw new RuntimeException("已生成申报单，刷新一下，查看最新状态");
        }
        List<String> ids = new ArrayList<>(16);
        ids.add(apply.getId().toString());
        List<DecHead> decHeadList = decHeadService.list(new LambdaQueryWrapper<DecHead>().in(DecHead::getApplyNumber, ids));
        if (decHeadList != null && decHeadList.size() > 0) {
            throw new RuntimeException("已生成申报单，刷新一下，查看最新状态");
        }
    }

    /**
     * 检测税号和申报规范
     * @param applyList
     * @throws RuntimeException
     */
    private void judgeBeHsCodes(List<OrderInfo> applyList) throws  RuntimeException {
        List<String> errorPns = new ArrayList<>();
        List<String> hscodeList = new ArrayList<>();
        for(OrderInfo v : applyList) {//增加获取配置信息
            for (OrderProductInfo ai : v.getProductList()) {
                if (isEmpty(ai.getHscode()) || ai.getHscode().length() < 10) {
                    if (!errorPns.contains(ai.getPn())){
                        errorPns.add(ai.getPn());
                    }
//					throw new YmException("数据存在不符合规范的税号");
                }else if (!hscodeList.contains(ai.getHscode())){
                    hscodeList.add(ai.getHscode());
                }
            }
        }
        if (errorPns != null && !errorPns.isEmpty()){
            throw new RuntimeException("数据存在不符合规范的税号,物料号为："+JSONObject.toJSONString(errorPns));
        }
        Map<String, ErpHscodes> specificationMap = getSpecifications(hscodeList);
        String resultError = "";
        for (String hscode: hscodeList) {
            if (!specificationMap.containsKey(hscode)){
                if (isEmpty(resultError)){
                    resultError = hscode;
                }else if (!resultError.contains(hscode)){
                    resultError = new StringBuilder(resultError).append(",").append(hscode).toString();
                }
            }
        }
        if (isNotBlank(resultError)){
            throw new RuntimeException(String.format("数据存在无申报规范的税号:%s",resultError));
        }

    }

    /**
     * 保存委托单和申报单信息
     *
     * @param apply 委托（内含申报单信息）
     * @return com.yorma.entity.YmMsg<com.yorma.apply.entity.Apply>
     */
    @Override
    public Result<?> saveApplyAndDeclaration(List<OrderInfo> orderInfos) {

        for(OrderInfo apply : orderInfos) {
            // FIXME: 2021/7/14 !!!
            if (apply == null) {
                return Result.error("业务信息不允许为空");
            }
            //变更委托单状态为预录中
//        if ("3".equals(apply.getStatus())) {
//            apply.setStatus("4");
//        }
            // 2022/6/2 10:13@ZHANGCHAO 追加/变更/完善：预录中120：生成草单时。委托下所有申报单删除时回退到110（可在现有删除回退委托状态接口处调整）
//        YmMsg<ApplyNode> applyNodeYmMsg = applyNodeStatusApi.updateApplyNodeStatus(apply.getId().toString(), 1L, 120L, 2, null);
//        if (!applyNodeYmMsg.isSuccess()) {
//            log.info("[updateApplyInvoices]更新报关节点放行状态失败：" + applyNodeYmMsg.getMessage());
//        }
//        String token = RequestKit.getRequestIn().getHeader(HEADER_TOKEN);
            // 从redis获取当前登录用户名
//        String username = (String) ((HashMap<String, Object>) redisUtil.get(PREFIX_USER_TOKEN_INFO + token)).get(USERNAME);
//        apply.setReceiveName(username);//制单人
//        apply.setReceiveDate(new Date());//制单时间

            if ((apply.getNemsInvtHeads() != null && !apply.getNemsInvtHeads().isEmpty())
                    || (apply.getDecHeads() != null && !apply.getDecHeads().isEmpty())) {
//            RequestKit.copyHeaders(false, XID, TENANT_ID, HEADER_TOKEN, CUSTOMER_ID);
                Result<?> declarationYmMsg = decHeadService.saveDecAndInvt(apply);

                if (!declarationYmMsg.isSuccess()) {
                    throw new RuntimeException("新增申报单失败，失败原因为：" + declarationYmMsg.getMessage());
                }

                apply.setNemsInvtHeads(isNotEmpty(declarationYmMsg.getResult()) ? ((OrderInfo) declarationYmMsg.getResult()).getNemsInvtHeads() : null);
                apply.setDecHeads(isNotEmpty(declarationYmMsg.getResult()) ? ((OrderInfo) declarationYmMsg.getResult()).getDecHeads() : null);

                //更新报关单/核注单的表头表体流水号
                if (isNotEmpty(apply.getNemsInvtHeads())) {
                    Map<Integer, NemsInvtList> invtListMap = new HashMap<>();
                    apply.getNemsInvtHeads().forEach(vh -> {
                        vh.getNemsInvtLists().forEach(vl -> {
                            if (isNotEmpty(vl.getGoodsId())) {
                                for (String goodsId : vl.getGoodsId().split(",")) {
                                    invtListMap.put(Integer.valueOf(goodsId), vl);
                                }
                            }
                        });
                    });
                    if (invtListMap.size() > 0) {
                        invtListMap.forEach((k, v) -> {
                            orderProductInfoService.update(new UpdateWrapper<OrderProductInfo>().lambda()
                                    .set(OrderProductInfo::getInvId, v.getInvId())
                                    .set(OrderProductInfo::getInvListId, v.getId())
                                    .eq(OrderProductInfo::getSequence, k)
                                    .eq(OrderProductInfo::getOrderInfoId, apply.getId()));
                        });
                    }
                }
                if (isNotEmpty(apply.getDecHeads())) {
                    Map<Integer, DecList> decListMap = new HashMap<>();
                    apply.getDecHeads().forEach(vh -> {
                        vh.getDecLists().forEach(vl -> {
                            if (isNotEmpty(vl.getGoodsId())) {
                                for (String goodsId : vl.getGoodsId().split(",")) {
                                    decListMap.put(Integer.valueOf(goodsId), vl);
                                }
                            }
                        });
                    });
                    if (decListMap.size() > 0) {
                        decListMap.forEach((k, v) -> {
                            orderProductInfoService.update(new UpdateWrapper<OrderProductInfo>().lambda()
                                    .set(OrderProductInfo::getDecId, v.getDecId())
                                    .set(OrderProductInfo::getDecListId, v.getId())
                                    .eq(OrderProductInfo::getSequence, k)
                                    .eq(OrderProductInfo::getOrderInfoId, apply.getId()));
                        });
                    }
                }
            }
 /*       int listSize = 0;
        if (apply.getDecHeads() != null){
            for (DecHead decHead : apply.getDecHeads()){
                listSize = listSize+decHead.getDecLists().size();
            }
        }
        if (apply.getNemsInvtHeads() != null){
            for (NemsInvtHead invtHead : apply.getNemsInvtHeads()){
                if ("2".equals(invtHead.getDclcusFlag())){
                    listSize = listSize+invtHead.getNemsInvtLists().size();
                }

            }
        }
        if (listSize != 0){
//            baseMapper.setDecItemsById(listSize,apply.getId());
            apply.setDecItems(listSize);
        }*/
            baseMapper.updateById(apply);// 2021-07-19 su 不去更新发票信息，只更新委托

        }
        return Result.ok(orderInfos);
    }

    /**
     * 从品名库重新带取数据
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/3 19:05
     */
    @Override
    public Result<?> getRebringData(String ids) {
        if (isBlank(ids)) {
            return Result.error("未知的商品！");
        }
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.listByIds(Arrays.asList(ids.split(",")));
        if (isEmpty(orderProductInfos)) {
            return Result.error("未知的商品！");
        }
        List<OrderProductInfo> orderProductInfoList = new ArrayList<>();
        Map<String, OrderInfo> orderMap = new HashMap<>();
        for (OrderProductInfo orderProductInfo : orderProductInfos) {
            OrderInfo orderInfo;
            if (orderMap.containsKey(orderProductInfo.getOrderInfoId())) {
                orderInfo = orderMap.get(orderProductInfo.getOrderInfoId());
            } else {
                orderInfo = this.getById(orderProductInfo.getOrderInfoId());
                orderMap.put(orderProductInfo.getOrderInfoId(), isNotEmpty(orderInfo) ? orderInfo : new OrderInfo());
            }

            List<ProductInfo> productInfos = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                    .eq(ProductInfo::getPn, orderProductInfo.getPn())
                    .eq(isNotBlank(orderProductInfo.getChineseName()), ProductInfo::getChineseName, orderProductInfo.getChineseName())
                    .eq(isNotBlank(orderProductInfo.getEnglishName()), ProductInfo::getEnglishName, orderProductInfo.getEnglishName()));
            if (isNotEmpty(productInfos)) {
                orderProductInfo.setProductId(productInfos.get(0).getId());
                orderProductInfo.setChineseName(productInfos.get(0).getChineseName());
                orderProductInfo.setEnglishName(productInfos.get(0).getEnglishName());
                orderProductInfo.setProductSpecificationModel(productInfos.get(0).getProductSpecificationModel());
                orderProductInfo.setCustomsCodeInfoCode(productInfos.get(0).getCustomsCodeInfoCode());
                orderProductInfo.setHscode(productInfos.get(0).getCustomsCodeInfoCode());
                orderProductInfo.setHsname(productInfos.get(0).getHsname());
                // 2025/2/28 18:01@ZHANGCHAO 追加/变更/完善：针对品名库中有变量的！！
                String hsmodel = productInfos.get(0).getCustomsDeclarationElements();
                if (isNotBlank(hsmodel) && hsmodel.contains("{}")) {
                    if (isNotBlank(orderInfo.getExportContractNo())) {
                        Pattern pattern = Pattern.compile("-(\\d{8})-");
                        Matcher matcher = pattern.matcher(orderInfo.getExportContractNo());
                        if (matcher.find()) {
                            String dateStr = matcher.group(1);
                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                            LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                            String formattedDate = date.format(outputFormatter);
                            log.info("提取的日期: {}", dateStr);
                            log.info("格式化后的日期: {}", formattedDate);
                            hsmodel = hsmodel.replace("{}", formattedDate);
                        } else {
                            log.info("无法从合同编号提取日期: {}", orderInfo.getExportContractNo());
                        }
                    }
                }
                orderProductInfo.setHsmodel(hsmodel);
                orderProductInfo.setCustomsDeclarationElements(hsmodel);
                orderProductInfoService.updateById(orderProductInfo);
            } else {
                orderProductInfo.setProductId(null);
            }
            orderProductInfoList.add(orderProductInfo);
        }
        return Result.ok(orderProductInfoList);
    }

    /**
     * 生成报关单和核注单
     * @param orderInfo
     * @return
     * @throws
     */
    private OrderInfo generalApplyDraft(OrderInfo orderInfo) {

        if (isBlank(orderInfo.getDeclareUnit()) || isBlank(orderInfo.getDeclareUnitName()) || isBlank(orderInfo.getDeclareUnitSocialCode())) {
            if (isNotBlank(orderInfo.getCustomsBrokerId())) {
                CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoMapper.selectById(orderInfo.getCustomsBrokerId());
                if (isNotEmpty(customsBrokerInfo)) {
                    orderInfo.setDeclareUnit(customsBrokerInfo.getDepartcd()); // 申报单位海关代码
                    orderInfo.setDeclareUnitSocialCode(customsBrokerInfo.getUnifiedSocialCreditCode()); // 申报单位社会统一信用代码
                    orderInfo.setDeclareUnitName(customsBrokerInfo.getCustomsBrokerName()); // 申报单位名称
                } else {
//                    throw new RuntimeException("未找到ID为["+orderInfo.getCustomsBrokerId()+"]的报关行信息！"); // 2025/3/4 14:06@ZHANGCHAO 追加/变更/完善：华熙可以为空！！！
                }
            } else {
//                throw new RuntimeException("报关行不能为空！"); // 2025/3/4 14:06@ZHANGCHAO 追加/变更/完善：华熙可以为空！！！
            }
        }

        // 草单配置
        CustomsExempt cIm = null; //区外、不入区进口
        CustomsExempt cEx = null; //区外、不入区出口

        if ("I".equals(orderInfo.getIeFlag())) {
            cIm = exemptMapper.getCarrierByTradeTypeAndEmsType(orderInfo.getSupervisionMode(), isNotEmpty(orderInfo.getRecordNumber()) ? orderInfo.getRecordNumber().substring(0,1) : null);
            error(cIm, orderInfo.getSupervisionMode(), orderInfo.getRecordNumber());
        }
        if ("E".equals(orderInfo.getIeFlag())) {
            cEx = exemptMapper.getCarrierByTradeTypeAndEmsType(orderInfo.getSupervisionMode(), isNotEmpty(orderInfo.getRecordNumber()) ? orderInfo.getRecordNumber().substring(0,1) : null);
            error(cEx, orderInfo.getSupervisionMode(), orderInfo.getRecordNumber());
        }

        OrderInfo dIm = null;//区外进
        OrderInfo dEx = null;//区外出

        String applyKind = null;
        List<NemsInvtHead> nemsInvtHeads = new ArrayList<>();
        List<DecHead> decHeads = new ArrayList<>();

        if(cIm!=null){
            cIm.setSource("Im");
            if (cIm.getHasInvt()){
                cIm.setDclcusFlag("1");//报关标志  1.报关
                cIm.setDclcusTypecd("2");//报关类型 2.对应报关
            }
            cIm.setDecType("1");//进口报关单
            cIm.setHasDec(true);
            //拆分进出口标识
            orderInfo.setIeFlag("I");
            orderInfo.setNemsInvtHeads(null);
            orderInfo.setDecHeads(null);

            if (isEmpty(applyKind) && isNotEmpty(orderInfo.getRecordNumber()) && "L".equals(orderInfo.getRecordNumber().substring(0,1))
                    && "I".equals(orderInfo.getIeFlag())){//保税仓库进口处理
                List<PtsEmsHead> emsHeads = emsHeadMapper.selectList(new LambdaQueryWrapper<PtsEmsHead>()
                        .eq(PtsEmsHead::getEmsNo, orderInfo.getRecordNumber()));
                PtsEmsHead emsHead = isNotEmpty(emsHeads) ? emsHeads.get(0) : null;
                if (emsHead != null){
                    orderInfo.setDeliverUnitName(emsHead.getOwnerName());
                }

            }

            dIm = generalDraft_Im(orderInfo, cIm);
            if (isNotEmpty(dIm.getNemsInvtHeads())){
                nemsInvtHeads.addAll(dIm.getNemsInvtHeads());
            }
            if (isNotEmpty(dIm.getDecHeads())){
                decHeads.addAll(dIm.getDecHeads());
            }
        }
        if(cEx!=null){
            cEx.setSource("Ex");
            if (cEx.getHasInvt()){
                cEx.setDclcusFlag("1");//报关标志  1.报关
                cEx.setDclcusTypecd("2");//报关类型 2.对应报关
            }
            cEx.setDecType("2");//出口报关单
            cEx.setHasDec(true);

            //拆分进出口标识
            orderInfo.setIeFlag("E");
            orderInfo.setNemsInvtHeads(null);
            orderInfo.setDecHeads(null);

            dEx = generalDraft_Ex(orderInfo, cEx);
            if (isNotEmpty(dEx.getNemsInvtHeads())){
                nemsInvtHeads.addAll(dEx.getNemsInvtHeads());
            }
            if (isNotEmpty(dEx.getDecHeads())){
                decHeads.addAll(dEx.getDecHeads());
            }
        }
        if (isNotEmpty(nemsInvtHeads)){
            orderInfo.setNemsInvtHeads(nemsInvtHeads);
        }
        if (isNotEmpty(decHeads)){
            orderInfo.setDecHeads(decHeads);
        }
        return orderInfo;
    }

    /**
     * 区外出委托处理
     * @param apply
     * @param exempt
     * @return
     */
    private OrderInfo generalDraft_Ex(OrderInfo apply, CustomsExempt exempt) throws RuntimeException{

        // 国别地区
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap0 = new HashMap<>();
        // 将 dictModel 列表转换为字典 map
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
        }
        if (isNotEmpty(dictModels0)) {
            dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
        }
        // 使用 dictMap1 中的 value 替换 dictMap0 的 key
        Map<String, String> replacedDictMap0 = new HashMap<>();
        dictMap0.forEach((key, value) ->
                replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
        );

        // 1.查询手/账册信息 并清单检查
        List<String> hscodeList  = new ArrayList<>();			//暂存申报税号
        checkInvoiceLists(apply, hscodeList);	//  判断数据完整性
        Map<String, List<OrderProductInfo>> splitMap = generalDraft(apply,exempt,hscodeList);
        List<NemsInvtHead> invtHeads = new ArrayList<>();
        List<DecHead> decHeads = new ArrayList<>();
        for (String key : splitMap.keySet()) {
            NemsInvtHead invtHead = null;
            if (exempt.getHasInvt()){
                invtHead = toNemsInvtHead(splitMap.get(key), apply, exempt,null);
                invtHead.setNonBusiness(false);
                invtHead.getNemsInvtLists().forEach(l -> {
                    if (isEmpty(l.getNatcd())){
                        if (isNotEmpty(apply.getApplyKind())){
                            l.setNatcd("142");
                        }else {
                            l.setNatcd(replacedDictMap0.get(apply.getCountryArrival()) == null ? "" : replacedDictMap0.get(apply.getCountryArrival()));
                        }
                    }
                });

                invtHead.setStshipTrsarvNatcd(isNotEmpty(apply.getCountryArrival()) && replacedDictMap0.get(apply.getCountryArrival()) != null
                        ? replacedDictMap0.get(apply.getCountryArrival())
                        : apply.getCountryArrival());// 起运运抵国别代码
                invtHead.setTrspModecd(apply.getShippingType());//运输方式

                setMtpckEndprdMarkcd(invtHead,apply,apply.getRecordNumber());
                invtHeads.add(invtHead);

                if (exempt.getHasDec()){
                    ApplyConfig ac = apply.getApplyConfigMap().get(apply.getConfigKey(apply.getSupervisionMode()));

                    DecHeadTemplete decHeadTemplete = null;
                    if (ac != null && isNotEmpty(ac.getDecTemplate())) {
                        decHeadTemplete = decHeadTempleteService.getDecTemplete(ac.getDecTemplate());
                    }
                    String profileType = "";

                    DecHead decHead = createByNemsInvt(invtHead,apply,splitMap.get(key),decHeadTemplete, exempt,profileType);

                    decHeads.add(decHead);
                }
            } else if (exempt.getHasDec()){
                ApplyConfig ac = apply.getApplyConfigMap().get(apply.getConfigKey(apply.getSupervisionMode()));

                DecHeadTemplete decHeadTemplete = null;
                if (ac != null && isNotEmpty(ac.getDecTemplate())) {
                    decHeadTemplete = decHeadTempleteService.getDecTemplete(ac.getDecTemplate());
                }
                String profileType = "";

                DecHead decHead = toDecHead(splitMap.get(key),apply,decHeadTemplete,exempt,profileType);
                createDec_Ex(decHead,apply);

                decHeads.add(decHead);
            }

        }
        if (isNotEmpty(invtHeads)){
            apply.setNemsInvtHeads(invtHeads);
        }
        if (isNotEmpty(decHeads)){
            apply.setDecHeads(decHeads);
        }
        return apply;
    }

    /**
     * 生成出口报关单单独配置
     * @param decHead
     * @param apply
     */
    private void createDec_Ex(DecHead decHead,OrderInfo apply){
        if (isNotEmpty(apply.getRelationId())){
            decHead.setNetWeight(apply.getNw());
        }

        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap0 = new HashMap<>();
        // 将 dictModel 列表转换为字典 map
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
        }
        if (isNotEmpty(dictModels0)) {
            dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
        }
        // 使用 dictMap1 中的 value 替换 dictMap0 的 key
        Map<String, String> replacedDictMap0 = new HashMap<>();
        dictMap0.forEach((key, value) ->
                replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
        );

        if (isNotEmpty(apply.getGoodsType()) && "111".contains(apply.getGoodsType())){
            decHead.setNetWeight(decHead.getGrossWeight());
            String tradeCountry = isNotBlank(decHead.getTradeCountry()) && replacedDictMap0.get(decHead.getTradeCountry()) != null
                    ? replacedDictMap0.get(decHead.getTradeCountry()) : decHead.getTradeCountry();
            String markNumber = new StringBuilder("修理物品,整机价:").append(isNotEmpty(decHead.getDecLists().get(0).getCurrencyCode()) ? decHead.getDecLists().get(0).getCurrencyCode() : "").append(isNotEmpty(apply.getTotalContractAmount()) ? apply.getTotalContractAmount() : "")
                    .append(",飞机注册号:").append(isNotEmpty(apply.getProductList().get(0).getBatch()) ? apply.getProductList().get(0).getBatch() : "").append(",")
                    .append(tradeCountry).append(isNotEmpty(decHead.getOverseasConsigneeEname()) ? decHead.getOverseasConsigneeEname() : "")
                    .append("航空公司所属,飞机进境修理,复运出境.发动机序列号:").append(isNotEmpty(apply.getProductList().get(0).getGrnNo()) ? apply.getProductList().get(0).getGrnNo() : "").append(".").toString();
            decHead.setMarkNumber(markNumber);
            decHead.setTermsTypeCode("3");
            decHead.getDecLists().forEach(v->{
                v.setFaxTypeCode("3");
            });
            decHead.setClearanceType("M");//报关单类型
            decHead.setShipFeeCode(null);//运费代码
            decHead.setShipFee(null);//运费
            decHead.setShipCurrencyCode(null);//运费币制
            decHead.setInsuranceCode(null);//保费代码
            decHead.setInsurance(null);//保费
            decHead.setInsuranceCurr(null);//保费
            decHead.setOtherCurr(null);//杂费币制
            decHead.setExtras(null);//杂费
            decHead.setExtrasCode(null);//杂费代码
        }

    }

    /**
     * 区外入委托处理
     * @param orderInfo
     * @param exempt
     * @return
     */
    private OrderInfo generalDraft_Im(OrderInfo orderInfo, CustomsExempt exempt) throws RuntimeException {
//        orderInfo.setOuter(true);
//
//        if (isNotEmpty(orderInfo.getRecordNumber())){
//            getCommodityByOut(orderInfo,0);//带取区外手账册信息
//        }

        List<String> hscodeList  = new ArrayList<>();			//暂存申报税号
        //查询手/账册信息 并清单检查
        checkInvoiceLists(orderInfo, hscodeList);    //  判断数据完整性

        Map<String, List<OrderProductInfo>> splitMap = generalDraft(orderInfo, exempt, hscodeList);
        List<NemsInvtHead> invtHeads = new ArrayList<>();

        List<DecHead> decHeads = new ArrayList<>();

        // 国别地区
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap0 = new HashMap<>();
        // 将 dictModel 列表转换为字典 map
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
        }
        if (isNotEmpty(dictModels0)) {
            dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
        }
        // 使用 dictMap1 中的 value 替换 dictMap0 的 key
        Map<String, String> replacedDictMap0 = new HashMap<>();
        dictMap0.forEach((key, value) ->
                replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
        );

        for (String key : splitMap.keySet()) {
            NemsInvtHead invtHead = null;
            if (exempt.getHasInvt()){

                invtHead = toNemsInvtHead(splitMap.get(key), orderInfo, exempt,null);

                //新件且监管方式为保税间货物流转类型默认区间料件结转(E)
//                if ("101".equals(orderInfo.getGoodsType()) && "1200".equals(invtHead.getSupvModecd())){
//                    invtHead.setListType("E");
//                }
                invtHead.setNonBusiness(false);
                invtHead.getNemsInvtLists().forEach(l -> {
                    if (isEmpty(l.getNatcd())){
                        l.setNatcd("142");
                    }

                });
//                if (isEmpty(orderInfo.getApplyKind()) && isNotEmpty(orderInfo.getRecordNumber())
//                        && "B|C|E|L".contains(orderInfo.getRecordNumber().substring(0,1))){//TODO:增加太古业务的账册
                    invtHead.setStshipTrsarvNatcd(isNotEmpty(orderInfo.getCountryArrival()) && replacedDictMap0.containsKey(orderInfo.getCountryArrival())
                            ? replacedDictMap0.get(orderInfo.getCountryArrival())
                            : orderInfo.getCountryArrival());// 起运运抵国别代码
                    invtHead.setTrspModecd(orderInfo.getShippingType());//运输方式
//                }else {
//                    invtHead.setStshipTrsarvNatcd(isNotEmpty(orderInfo.getCountryArrival()) ? (replacedDictMap0.containsKey(orderInfo.getCountryArrival()) ? replacedDictMap0.get(orderInfo.getCountryArrival()) : orderInfo.getCountryArrival()) : "142");// 起运运抵国别代码
//                    invtHead.setTrspModecd("Y");
//                }

                setMtpckEndprdMarkcd(invtHead, orderInfo, orderInfo.getRecordNumber());
                invtHeads.add(invtHead);

                if (exempt.getHasDec()){

                    ApplyConfig ac = orderInfo.getApplyConfigMap().get(orderInfo.getConfigKey(orderInfo.getSupervisionMode()));

                    DecHeadTemplete decHeadTemplete = null;
                    if (ac != null && isNotEmpty(ac.getDecTemplate())) {
//                        decHeadTemplete =
//                                RpcKit.get(decTempleteApi, DecTempleteApi::getDecTemplete, ac.getDecTemplate()).getData();
                        decHeadTemplete = decHeadTempleteService.getDecTemplete(ac.getDecTemplate());
                    }

                    String profileType = "";
//                    if (isNotEmpty(ac.getTwoStepDeclaration()) && "1".equals(ac.getTwoStepDeclaration().substring(2,3))){
//                        profileType = "1";
//                    }
                    DecHead decHead = createByNemsInvt(invtHead, orderInfo, splitMap.get(key), decHeadTemplete, exempt, profileType);


//					createDecByInvt_two(decHead,invtHead,apply,splitMap.get(key));
//                    decHead.setOuter(true);//是否是区外单据
//                    createDecByInvt_out(decHead,invtHead,orderInfo,splitMap.get(key),decHeadTemplete);
                    //处理二线区外的入区赋值
//                    if (!ApplyType.BWS.equals(apply.getApplyType())){
//                        assignmentDecHeadByEnter(apply, decHead, invtHead);
//                    }

                    if (isNotEmpty(ac.getTwoStepDeclaration())){
                        decHead.setDecType(isNotEmpty(decHead.getDecType())
                                ? (decHead.getDecType().length() <2 ? decHead.getDecType()+" " : decHead.getDecType())+ac.getTwoStepDeclaration()
                                : "  "+ac.getTwoStepDeclaration());
                        decHead.setProfileType("1");
                    }
                    decHeads.add(decHead);
                }
            }else if (exempt.getHasDec()){
                ApplyConfig ac = orderInfo.getApplyConfigMap().get(orderInfo.getConfigKey(orderInfo.getSupervisionMode()));

                DecHeadTemplete decHeadTemplete = null;
                if (ac != null && isNotEmpty(ac.getDecTemplate())) {
//                    decHeadTemplete =
//                            RpcKit.get(decTempleteApi, DecTempleteApi::getDecTemplete, ac.getDecTemplate()).getData();
                    decHeadTemplete = decHeadTempleteService.getDecTemplete(ac.getDecTemplate());
                }
                String profileType = "";
//                if (isNotEmpty(ac.getTwoStepDeclaration()) && "1".equals(ac.getTwoStepDeclaration().substring(2,3))){
//                    profileType = "1";
//                }
                DecHead decHead = toDecHead(splitMap.get(key), orderInfo, decHeadTemplete, exempt, profileType);
                createDec_Im(decHead, orderInfo);
                if (isNotEmpty(ac.getTwoStepDeclaration())){
                    decHead.setDecType(isNotEmpty(decHead.getDecType())
                            ? (decHead.getDecType().length() <2 ? decHead.getDecType()+" " : decHead.getDecType())+ac.getTwoStepDeclaration()
                            : "  "+ac.getTwoStepDeclaration());
                    decHead.setProfileType("1");
                }
                decHeads.add(decHead);
            }

        }
        if (isNotEmpty(invtHeads)){
            orderInfo.setNemsInvtHeads(invtHeads);
        }
        if (isNotEmpty(decHeads)){
            orderInfo.setDecHeads(decHeads);
        }
        return orderInfo;
    }

    /**
     * 区外重新获取手账册信息
     * @param apply
     * @param type 料件or成品（0or1）
     */
    private void getCommodityByOut(OrderInfo apply,Integer type){
        List<String> outGNos = new ArrayList<>();
        for (OrderProductInfo applyInvoices : apply.getProductList()) {
            if (applyInvoices.getOutGNo() != null) {
                applyInvoices.setInvoicesRecordItem(applyInvoices.getOutGNo());
                outGNos.add(applyInvoices.getOutGNo().toString());
            }
        }
        if (outGNos.isEmpty()){
            return;
        }
        Map<String, EmsGoods> emsGoodsMap = new HashMap<>();
//        RequestKit.copyHeaders(false, XID, TENANT_ID, HEADER_TOKEN, TENANT_TYPE, CUSTOMER_ID);
//        EmsQueryDto emsQueryDto = new EmsQueryDto();
//        emsQueryDto.setEmsNo(apply.getEmsNo());// 账册号
//        emsQueryDto.setgNoList(outGNos);// 账册备案序号集合
//        YmMsg<EmsHead> emsListYmMsg = emsQueryServe.listEmsDetail(type, null, null, emsQueryDto);
        PtsEmsHead emsHead = emsHeadMapper.selectOne(new LambdaQueryWrapper<PtsEmsHead>()
                .eq(PtsEmsHead::getEmsNo, apply.getRecordNumber()));
        List<PtsEmsAimg> emsAimgList = emsAimgMapper.listAimgList(apply.getRecordNumber(), outGNos);
        List<PtsEmsAexg> emsAexgList = emsAexgMapper.listAexgList(apply.getRecordNumber(), outGNos);
        emsHead.setEmsAimgList(emsAimgList);
        emsHead.setEmsAexgList(emsAexgList);
        if (isNotEmpty(emsHead)) {
            if (type == 0) {
                for (PtsEmsAimg aimg : emsHead.getEmsAimgList()) {
                    EmsGoods emsGoods = new EmsGoods();
                    emsGoods.setCopGno(aimg.getCopGno());// 料号
                    emsGoods.setgNo(aimg.getGNo());// 备案序号
                    emsGoods.setgName(aimg.getGName());// 申报品名
                    emsGoods.setCodet(aimg.getCodet());// 申报税号
                    emsGoods.setgModel(aimg.getGModel());// 申报要素
                    emsGoods.setUnit(aimg.getUnit());// 申报单位
                    String identifying = new StringBuffer().append(aimg.getEmsNo()).append("|")
                            .append(aimg.getGNo()).toString();
                    emsGoodsMap.put(identifying, emsGoods);
                }
            } else if (type == 1) {
                for (PtsEmsAexg aexg : emsHead.getEmsAexgList()) {
                    EmsGoods emsGoods = new EmsGoods();
                    emsGoods.setCopGno(aexg.getCopGno());// 料号
                    emsGoods.setgNo(aexg.getGNo());// 备案序号
                    emsGoods.setgName(aexg.getGName());// 申报品名
                    emsGoods.setCodet(aexg.getCodet());// 申报税号
                    emsGoods.setgModel(aexg.getGModel());// 申报要素
                    emsGoods.setUnit(aexg.getUnit());// 申报单位
                    String identifying = new StringBuffer().append(aexg.getEmsNo()).append("|")
                            .append(aexg.getGNo()).toString();
                    emsGoodsMap.put(identifying, emsGoods);
                }
            }
        }

        for (OrderProductInfo applyInvoices : apply.getProductList()) {
            if (applyInvoices.getOutGNo() != null) {
                String identifying = new StringBuffer().append(applyInvoices.getRecordNumber()).append("|")
                        .append(applyInvoices.getOutGNo()).toString();
                if (emsGoodsMap.containsKey(identifying)) {
                    EmsGoods emsGoods = emsGoodsMap.get(identifying);
                    applyInvoices.setPn(emsGoods.getCopGno());// 料号
                    applyInvoices.setHscode(emsGoods.getCodet());// 编码
                    applyInvoices.setHsname(emsGoods.getgName());// 名称
                    applyInvoices.setHsmodel(emsGoods.getgModel());// 规格型号
                    applyInvoices.setShipmentUnit(emsGoods.getUnit());// 计量单位
                    applyInvoices.setUcnsverno(emsGoods.getUcnsverno());//单耗版本号
                }
            }
        }

    }

    /**
     * 生成进口报关单单独配置
     * @param decHead
     * @param apply
     */
    private void createDec_Im(DecHead decHead,OrderInfo apply){
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap0 = new HashMap<>();
        // 将 dictModel 列表转换为字典 map
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
        }
        if (isNotEmpty(dictModels0)) {
            dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
        }
        // 使用 dictMap1 中的 value 替换 dictMap0 的 key
        Map<String, String> replacedDictMap0 = new HashMap<>();
        dictMap0.forEach((key, value) ->
                replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
        );
        if (isNotEmpty(apply.getGoodsType()) && "111".contains(apply.getGoodsType())){
            decHead.setNetWeight(decHead.getGrossWeight());
            String tradeCountry = isNotBlank(decHead.getTradeCountry()) && replacedDictMap0.get(decHead.getTradeCountry()) != null
                    ? replacedDictMap0.get(decHead.getTradeCountry()) : decHead.getTradeCountry();
            String markNumber = new StringBuilder(isNotEmpty(decHead.getMarkNumber()) ? decHead.getMarkNumber() : "")
                    .append(tradeCountry).append(isNotEmpty(decHead.getOverseasConsignorEname()) ? decHead.getOverseasConsignorEname() : "")
                    .append("航空公司所属,飞机注册号:").append(isNotEmpty(apply.getProductList().get(0).getBatch()) ? apply.getProductList().get(0).getBatch() : "")
                    .append(",发动机序列号:").append(isNotEmpty(apply.getProductList().get(0).getGrnNo()) ? apply.getProductList().get(0).getGrnNo() : "").append(",应检商品.").toString();
            decHead.setMarkNumber(markNumber);
        }
		/*if ("费斯托气动有限公司".equals(apply.getConsignor()) || "1".equals(apply.getAeroImportFlag())){
			decHead.setDespDate(new SimpleDateFormat("yyyy-MM-dd").format(apply.getCreateDate()));
		}*/
    }

    /**
     * 转化报关单表头
     *
     * @param list
     * @param apply
     * @return
     * @throws RuntimeException
     */
    private DecHead toDecHead(List<OrderProductInfo> list, OrderInfo apply, DecHeadTemplete decHeadTemplete, CustomsExempt exempt, String profileType)
            throws RuntimeException {

        DecHead decHead = new DecHead();
        DecListTemplete decListTemplete = null;
        if (decHeadTemplete != null) {
			/*复制排除：口岸检验检疫机关、目的地检验检疫机关、检验检疫受理机关、领证机关,企业资质,使用人,检验检疫签证，启运时间,B/L号,
			 关联号码及理由,原箱运输,特殊业务标识,关联号码,关联理由*/
            BeanUtil.copyProperties(decHeadTemplete,decHead,"inspOrgCode","purpOrgCode","orgCode","vsaOrgCode",
                    "copLimitType","decUserType","requestCertType","despDate","blNo","correlationNo","origBoxFlag","specDeclFlag",
                    "correlationNo","correlationReasonFlag");
            decListTemplete = decHeadTemplete.getDecListTempletes() != null
                    && !decHeadTemplete.getDecListTempletes().isEmpty() ? decHeadTemplete.getDecListTempletes().get(0)
                    : null;
        }
        decHead.setInputId(TenantContext.getTenant());
        decHead.setMarkNo(isNotEmpty(decHead.getMarkNo()) ? decHead.getMarkNo() : "N/M");

        // 默认信息
        decHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
        decHead.setId(IdWorker.getIdStr());
        decHead.setCreateTime(new Date());// 创建日期
        decHead.setAudited(false);// 审核状态
        decHead.setSend(null);// 发送状态
        decHead.setSynchronism(false);// 同步标记
        decHead.setDclTenantId(TenantContext.getTenant());// 申请人租户ID TODo:替换id
//		decHead.setDclTrnRelFlag("0");//一般报关单
        judgeDdeclarationType(exempt.getDecType(),decHead);//报关单类型

        String[] arr = exempt.getTaxType().split("\\|");
        decHead.setTaxTypeCode(arr[0]);//征免性质
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 来自委托
        decHead.setApplyNumber(apply.getId());// 委托单号
        decHead.setCreatePerson(loginUser.getUsername());// 创建人
        decHead.setIeFlag(apply.getIeFlag());
//        decHead.setAccountNo(apply.getAccountNo()); // 2022/6/15 14:27@ZHANGCHAO 追加/变更/完善：台账号！
        decHead.setArrivalArea(
                StringUtils.isNotBlank(apply.getCountryArrival()) ? apply.getCountryArrival() : decHead.getArrivalArea());// 启运国/运抵国
        decHead.setShipTypeCode(
                StringUtils.isNotBlank(apply.getShippingType()) ? apply.getShippingType() : decHead.getShipTypeCode());// 运输方式
        String termsTypeCode = isNotBlank(apply.getTransMode()) ? apply.getTransMode() : (isNotEmpty(apply.getTradingType()) ? String.valueOf(apply.getTradingType()) : null);
        decHead.setTermsTypeCode(
                StringUtils.isNotBlank(termsTypeCode) ? termsTypeCode : decHead.getTermsTypeCode());// 成交方式
        // 2024/11/29 10:52@ZHANGCHAO 追加/变更/完善：申报地海关只取模板！！
//        decHead.setDeclarePlace(
//                StringUtils.isNotBlank(apply.getExitClearance()) ? apply.getExitClearance() : decHead.getDeclarePlace());// 申报地海关
        decHead.setOtherCurr(
                StringUtils.isNotBlank(apply.getOtherCurr()) ? apply.getOtherCurr() : decHead.getOtherCurr());// 杂费币制

        String shipCurrencyCode = null;
        if (StringUtils.isNotEmpty(apply.getShipCurrencyCode())){
            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode, apply.getShipCurrencyCode()));
            shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
        }
        decHead.setShipCurrencyCode(
                StringUtils.isNotBlank(shipCurrencyCode) ? shipCurrencyCode : decHead.getShipCurrencyCode());// 运费币制
        decHead.setTradeTypeCode(
                StringUtils.isNotBlank(apply.getSupervisionMode()) ? apply.getSupervisionMode() : decHead.getTradeTypeCode());// 贸易方式
        decHead.setEntyPortCode(
                StringUtils.isNotBlank(apply.getDeparturePort()) ? apply.getDeparturePort() : decHead.getEntyPortCode());// 入境口岸/离境口岸
        decHead.setContract(
                StringUtils.isNotBlank(apply.getExportContractNo()) ? apply.getExportContractNo() : decHead.getContract());// 合同协议号
		/*if (!"1233".equals(decHead.getTradeTypeCode())){
			decHead.setContract(
					StringUtils.isNotBlank(apply.getContracts()) ? apply.getContracts() : decHead.getContract());// 合同协议号
		}*/
        decHead.setShipFee(apply.getFreightAmount() != null ? apply.getFreightAmount() : decHead.getShipFee());// 运费
        decHead.setExtras(apply.getMiscellaneousAmount() != null ? apply.getMiscellaneousAmount() : decHead.getExtras());// 杂费
        decHead.setTradeCountry(
                StringUtils.isNotBlank(apply.getTradingCountry()) ? apply.getTradingCountry() : decHead.getTradeCountry()); // 贸易国
        decHead.setPacks(apply.getPacks() != null ? apply.getPacks() : decHead.getPacks());// 件数
        decHead.setGrossWeight(
                apply.getGw() != null && !(BigDecimal.ZERO.compareTo(apply.getGw()) == 0) ? apply.getGw()
                        : decHead.getGrossWeight());// 毛重
        decHead.setShipName(
                (StringUtils.isNotBlank(apply.getTransportName())) ? apply.getTransportName() : decHead.getShipName());// 运输工具名称
        decHead.setVoyage((StringUtils.isNotBlank(apply.getVoy())) ? apply.getVoy() : decHead.getVoyage());// 航次号
        if ("I".equals(apply.getIeFlag())) {
            decHead.setDespPortCode((StringUtils.isNotBlank(apply.getPortDestination())) ? apply.getPortDestination()
                    : decHead.getDespPortCode());// 启运港
//            decHead.setDesPort(
//                    StringUtils.isNotBlank(apply.getDesPort()) ? apply.getDesPort() : decHead.getDesPort());// 经停港
        } else if ("E".equals(apply.getIeFlag())) {
            decHead.setDesPort(StringUtils.isNotBlank(apply.getPortDestination()) ? apply.getPortDestination() : decHead.getDesPort());// 目的港(指运港)
        }
//		decHead.setOutPortCode(StringUtils.isNotBlank(apply.getPort()) ? apply.getPort() : decHead.getOutPortCode());// 申报口岸
        decHead.setOutPortCode(StringUtils.isNotBlank(apply.getExitClearance()) ? apply.getExitClearance() : decHead.getOutPortCode());// 进出境关别

        // 获取任意一个非空且非空字符串的 type
        String packsKinds = list.stream()
                .map(OrderProductInfo::getShipmentPackingType)
                .filter(i -> i != null && !i.isEmpty())
                .findAny()
                .orElse(null);
        decHead.setPacksKinds(packsKinds);// 包装种类
        decHead.setPackType(isNotEmpty(apply.getOtherPack()) ? apply.getOtherPack() : decHead.getPackType());// 其他包装
        decHead.setApplyCreateName(apply.getContactPerson());
        decHead.setGoodsType(apply.getGoodsType());
        decHead.setChargedWeight(apply.getChargedWeight());//计费重量
        String markNumber = decHead.getMarkNumber();//备注
        if (isNotEmpty(apply.getDecRemark())){
            if (isNotEmpty(markNumber)){
                markNumber = new StringBuilder(apply.getDecRemark()).append(";").append(markNumber).toString();
            }else {
                markNumber = new StringBuilder(apply.getDecRemark()).toString();
            }
        }
        // 2024/11/27 13:23@ZHANGCHAO 追加/变更/完善：
        // 2. 报关单备注：博汇默认：加工费：取值导入表加工费，交货单号：取值导入表交货单号（多个拼接）。
        // 示例：加工费：53497.35，交货单号：8900197314
        if ("山东博汇纸业股份有限公司".equals(decHead.getOptUnitName())){
            String shipCurrencyCode1 = list.get(0).getCustomsDeclarationCurrency();
            if (isNotBlank(list.get(0).getCustomsDeclarationCurrency()) && isNumeric(list.get(0).getCustomsDeclarationCurrency())) {
                if (StringUtils.isNotEmpty(list.get(0).getCustomsDeclarationCurrency())){
                    ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                            .eq(ErpCurrencies::getCode, list.get(0).getCustomsDeclarationCurrency()));
                    shipCurrencyCode1 =currencies != null ? currencies.getCurrency() : null;
                }
            }
            markNumber = "加工费：" + (isNotEmpty(apply.getProcessingFee()) ? apply.getProcessingFee().stripTrailingZeros().toPlainString() : "")
                    + " " + shipCurrencyCode1
                    + "，交货单号：" + (isNotBlank(apply.getDeliveryNoteNumber()) ? apply.getDeliveryNoteNumber() : "");        }
        decHead.setMarkNumber(markNumber);

        LoveUBaby loveUBaby = pleaseKissMe(apply.getReceiver(), apply.getIeFlag(), "1");
        decHead.setOptUnitName(apply.getReceiver());// 境内收发货人（关联经营单位）
        decHead.setOptUnitId(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : decHead.getOptUnitId());
        decHead.setOptUnitSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : decHead.getOptUnitSocialCode());
        decHead.setTradeCiqCode(isNotEmpty(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : decHead.getTradeCiqCode());

        // 境外取业务报关单标签的供应商--页面联动基本信息的境外供应商
        LoveUBaby loveUBaby1 = pleaseKissMe(apply.getDomesticSuppliersInfoName(), apply.getIeFlag(), "2");
        if ("I".equals(apply.getIeFlag())) {
            decHead.setOverseasConsignorEname(loveUBaby1.getDepartName()); // 境外发货人名称
            decHead.setOverseasConsignorCode(loveUBaby1.getDepartcd()); // 境外发货人代码
        } else {
            decHead.setOverseasConsigneeEname(loveUBaby1.getDepartName()); // 境外收货人名称
            decHead.setOverseasConsigneeCode(loveUBaby1.getDepartcd());
        }

        // 消费使用单位取报关单标签的境内
        decHead.setDeliverUnitName(apply.getReceiver());// 消费使用单位（加工单位）
        decHead.setDeliverUnit(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : decHead.getDeliverUnit());
        decHead.setDeliverUnitSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : decHead.getDeliverUnitSocialCode());
        decHead.setOwnerCiqCode(isNotEmpty(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : decHead.getOwnerCiqCode());

        decHead.setBillCode(isNotBlank(apply.getDeliveryNumbers()) ? apply.getDeliveryNumbers() : decHead.getBillCode());

        decHead.setDeclareUnitName(apply.getDeclareUnitName());// 申报单位名称
        List<CustomerEnterprise> declEnterprise = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, apply.getDeclareUnitName()));
        if (isNotEmpty(declEnterprise)){
            decHead.setDeclareUnit(declEnterprise.get(0).getDepartcd());// 申报单位代码
            decHead.setDeclareUnitSocialCode(declEnterprise.get(0).getSocialCode());// 申报单位社会统一代码
            decHead.setDeclCiqCode(declEnterprise != null && isNotEmpty(declEnterprise.get(0).getCiqCode()) ? declEnterprise.get(0).getCiqCode() : decHead.getDeclCiqCode());// 申报单位检验检疫编码
        }

        decHead.setInspMonitorCond("0");//默认非涉检
        dealSupv(list,apply,decHead);

        decHead.setDecLists(new ArrayList<>());
        BigDecimal netWeight = BigDecimal.ZERO;
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal goodsCount = BigDecimal.ZERO;
        int item= 1;
        for (OrderProductInfo v : list) {
            DecList decList = invoice2DecList(v, decListTemplete, apply, decHead.getIeFlag(),exempt);
            if ((isNotEmpty(decHead.getInspMonitorCond()) && !"0".equals(decHead.getInspMonitorCond())) || "1".equals(profileType)){
                if ((isNotEmpty(v.getMonitorcondition()) && (v.getMonitorcondition().contains("A")
                        || v.getMonitorcondition().contains("L") || v.getMonitorcondition().contains("P")
                        || v.getMonitorcondition().contains("M") || v.getMonitorcondition().contains("R")))
                        || (isNotEmpty(v.getDgFlag()) && v.getDgFlag())//判断涉检项或者危险品
                        || ("2".equals(decHead.getInspMonitorCond()) && item == 1)//判断木质包装第一项涉检
                        || "1".equals(profileType)) {
                    decList.setCiqCode(v.getCiqCode());
                    decList.setCiqName(v.getCiqName());
                    decList.setGoodsAttr(isNotEmpty(decList.getGoodsAttr())
                            ? isNotEmpty(v.getGoodsAttr()) ? decList.getGoodsAttr()+","+v.getGoodsAttr() : decList.getGoodsAttr()
                            : v.getGoodsAttr());
                }else if ("2".equals(decHead.getInspMonitorCond()) && item>1){
                }
            }
            decList.setDecId(decHead.getId());
            decList.setItem(item);
            item++;

            String hstype;
            hstype = isNotBlank(v.getPreviewTag()) ? v.getPreviewTag() : "";
            decList.setHstype(new StringBuilder(hstype)
                    .append("(").append(decList.getHstype()).append(")").append(isNotEmpty(v.getHsTypeSign()) ? v.getHsTypeSign() : "").toString());
            netWeight = netWeight.add(decList.getNetWeight());
            total = total.add(decList.getTotal());
            goodsCount = goodsCount.add(decList.getGoodsCount());
            decHead.getDecLists().add(decList);
            if (v.getDecHead() != null && apply.getExchangeRateMaps() != null) {
                BigDecimal extras = null;
                if ("1".equals(v.getDecHead().getTermsTypeCode())){
                    extras = extras == null ? BigDecimal.ZERO : extras;
                }else if (extras == null) {
                    extras = countExtras(apply.getExchangeRateMaps(), decList, v.getDecHead());
                }else {
                    extras = extras.add(countExtras(apply.getExchangeRateMaps(), decList, v.getDecHead()));
                }
                if (extras != null && extras.compareTo(BigDecimal.ZERO) ==1){
                    decHead.setExtras(extras);
                    decHead.setExtrasCode("3");
                    decHead.setOtherCurr("USD");
                }
            }
        }
//		decHead.setNetWeight(netWeight.compareTo(BigDecimal.ONE) >=1 ? netWeight.stripTrailingZeros() : BigDecimal.ONE.stripTrailingZeros());// 净重
        decHead.setNetWeight(netWeight.compareTo(new BigDecimal("0.01"))==-1? new BigDecimal("0.01")
                : (netWeight.compareTo(new BigDecimal("1"))==-1?netWeight.setScale(2,BigDecimal.ROUND_HALF_DOWN).stripTrailingZeros():netWeight.stripTrailingZeros()));// 净重
        decHead.setTotal(total);
        decHead.setGoodsCount(goodsCount);
        decHead.setTradeTypeCode(apply.getSupervisionMode());
        // 调整合同号
        resetContact(decHead,apply);
        //太古处理合同号
//        handleContactByTaigu(decHead,apply);
        //处理特殊事项
        handlePromiseItmes(apply,list.get(0),decHead,decHeadTemplete != null ? decHeadTemplete.getPromiseItmes() : null);
        //处理涉检项
        handleInspection(decHead, decHeadTemplete, apply.getBuyerName(), profileType, null);
        //费斯托处理保费
//        handleDecHeadToInsurance(decHead,apply);

        return decHead;
    }

    /**
     * 区外生成核注单单独配置
     * @param decHead
     * @param nemsInvtHead
     * @param apply
     */
    private void createDecByInvt_out(DecHead decHead,NemsInvtHead nemsInvtHead,OrderInfo apply,List<OrderProductInfo> invoices,DecHeadTemplete decHeadTemplete){
        decHead.setBillType(null);//清单类型 若是报关单分类为备案清单时，清单类型为普通备案清单
//        decHead.setOuter(true);
//        if (!(isEmpty(apply.getApplyKind()) && isNotEmpty(apply.getRecordNumber())
//                && "B|C|E|L".contains(apply.getRecordNumber().substring(0,1)))){//TODO：忽略太古的账册
//            decHead.setShipTypeCode("Y");
//        }
//        Customer customer = null;
//        if (apply.getConsignorId() != null){
//            YmMsg<Customer> customerYmMsg = customerApi.getCustomerByName(apply.getConsignee(),apply.getConsignorId().toString());
//            if (customerYmMsg.getData() != null){
//                customer =customerYmMsg.getData();
//            }
//        }

        if (isNotEmpty(apply.getApplyKind())&&(apply.getApplyKind().contains("2I") || apply.getApplyKind().contains("2E"))){
            decHead.setBillCode(null);
        }
        if (isNotEmpty(apply.getGoodsType()) && "105/106".contains(apply.getGoodsType())){//2022-02-09 安装件和转税的毛重=净重
            decHead.setGrossWeight(decHead.getNetWeight());
        }
//        List<CustomerEnterprise> overseasPayerList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
//                .eq(CustomerEnterprise::getDepartName, apply.getOverseasPayerInfoName()));
        if ("E".equals(decHead.getIeFlag())) {

//					decHead.setIeFlag("E");//进出口
            decHead.setEntyPortCode(isNotBlank(apply.getDeparturePort())
                    ? apply.getDeparturePort() : decHead.getEntyPortCode());//离境口岸（委托带入口岸）
            decHead.setDespPortCode(isNotBlank(apply.getPortDestination())
                    ? apply.getPortDestination() : decHead.getDespPortCode());//启运港（启运港）
            decHead.setDesPort(isNotBlank(apply.getPortDestination()) ? apply.getPortDestination() : decHead.getDesPort());//指运港（-目的港）
            decHead.setOverseasConsigneeEname(isNotEmpty(apply.getOverseasWarehouseName()) ? apply.getOverseasWarehouseName() : "");//境外收货人 TODO:取值逻辑
//            decHead.setOverseasConsigneeCode(customer != null ? customer.getAeo()
//                    : "NO".equals(apply.getConsignee()) ? "NO" : null);
            decHead.setOverseasConsigneeCode("NO".equals(apply.getOverseasPayerInfoName()) ? "NO" : null);
//			decHead.setTradeCountry(customer != null ? customer.getCountry()
//					: "NO".equals(apply.getConsignee()) ? "CHN" : null);//贸易国

        } else if ("I".equals(decHead.getIeFlag())) {
//					decHead.setIeFlag("I");//进出口
            decHead.setEntyPortCode(isNotBlank(apply.getDeparturePort())
                    ? apply.getDeparturePort() : decHead.getEntyPortCode());//入境口岸（委托带入口岸）
			/*decHead.setOptUnitId(nemsInvtHead.getBizopEtpsno());//境内收发货人（关联经营单位）2021-12-02 去除 获取委托
			decHead.setOptUnitName(nemsInvtHead.getBizopEtpsNm());
			decHead.setOptUnitSocialCode(nemsInvtHead.getBizopEtpsSccd());
			CustomerEnterprise optUnitCustomer = RpcKit.get(customerApi,CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,
					null,nemsInvtHead.getBizopEtpsNm()).getData();
			decHead.setTradeCiqCode(optUnitCustomer != null ? optUnitCustomer.getCiqCode() : null);*/

            decHead.setDespPortCode(isNotBlank(apply.getPortDestination())
                    ? apply.getPortDestination() : decHead.getDespPortCode());//启运港（启运港）
            decHead.setDesPort(isNotBlank(apply.getDesPort()) ? apply.getDesPort() : decHead.getDesPort());//经停港(-中转港)
            decHead.setOverseasConsignorEname(isNotEmpty(apply.getOverseasPayerInfoName()) ? apply.getOverseasPayerInfoName() : "");//境外发货人
            decHead.setOverseasConsignorCode("NO".equals(apply.getOverseasPayerInfoName()) ? "NO" : null);
//			decHead.setTradeCountry(customer != null ? customer.getCountry()
//					: "NO".equals(apply.getConsignee()) ? "CHN" : null);//贸易国

			/*if ("费斯托气动有限公司".equals(apply.getConsignor()) || "1".equals(apply.getAeroImportFlag())){
				decHead.setDespDate(new SimpleDateFormat("yyyy-MM-dd").format(apply.getCreateDate()));
			}*/

            if ("4303".equals(decHead.getOutPortCode())) {//济综保区
                decHead.setGoodsPlace("济南综合保税区");//济南综合保税区
            }
            if ("4306".equals(decHead.getOutPortCode())) {//章锦综保
                decHead.setGoodsPlace("济南章锦综合保税区");//济南章锦综合保税区
            }
        }
        decHead.setTradeTypeCode(nemsInvtHead.getSupvModecd());//监管方式(来料加工）
        if (isNotEmpty(apply.getApplyKind())){
            decHead.setArrivalArea("CHN");//启运国，运抵国
        }else {
            decHead.setShipName(apply.getTransportName());//运输工具名称
            decHead.setVoyage(apply.getVoy());//航次号
            decHead.setArrivalArea(apply.getCountryArrival());//启运国，运抵国
        }

        for (DecList declist : decHead.getDecLists()) {
            if (isNotEmpty(decHead.getOptUnitId())) {
                declist.setDistrictCode(decHead.getOptUnitId().substring(0, 5));//境内目的地/境内货源地
            }
        }

        //瀚瑞森二线分拨出区处理合同号
//        handleContactByHRS(decHead,apply,invoices);
        //处理特殊事项
        handlePromiseItmes(apply, invoices.get(0), decHead,decHeadTemplete != null ? decHeadTemplete.getPromiseItmes() : null);
    }

    /**
     * 特殊事项处理
     * @param apply
     * @param invoices
     * @param decHead
     */
    private void handlePromiseItmes(OrderInfo apply,OrderProductInfo invoices,DecHead decHead,String templPromiseItmes){
        if (isEmpty(decHead.getPromiseItmes())){
            if ("I".equals(decHead.getIeFlag())){
                decHead.setPromiseItmes("0|0|0|0");//默认
            }else if ("E".equals(decHead.getIeFlag())){
                decHead.setPromiseItmes("9|9|9|9");//默认
            }
        }
        //特殊事项
        if ("费斯托气动有限公司".equals(decHead.getOptUnitName())){
            String consignee = isNotEmpty(decHead.getOverseasConsigneeEname()) ? decHead.getOverseasConsigneeEname()
                    : decHead.getOverseasConsignorEname();
            if (isNotEmpty(consignee) && consignee.toUpperCase().contains("FESTO")){//供应商（境外发货人）
                if ("RSC".equals(apply.getGoodsType())){//RSC invoices.getType() 2021-11-01 将赋值移动到表头，表体不在赋值
                    decHead.setPromiseItmes("1|0|0|0");
                }
                if ("GPC".equals(apply.getGoodsType())){//GPC: invoices.getType()
                    decHead.setPromiseItmes("1|0|1|0");
                }
            }else {
                decHead.setPromiseItmes("0|0|0|0");
            }
        }
        if ("瀚瑞森（中国）汽车悬挂系统有限公司".equals(decHead.getOptUnitName())){
            if ("I".equals(apply.getIeFlag())){
                apply.setIeFlag("E");
            }else {
                apply.setIeFlag("I");
            }
            String consignee = isNotEmpty(decHead.getOverseasConsigneeEname()) ? decHead.getOverseasConsigneeEname()
                    : decHead.getOverseasConsignorEname();
            if (isNotEmpty(consignee) && consignee.toUpperCase().contains("HENDRICKSON")
                    && ("E".equals(apply.getIeFlag())
                    || (apply.getOuter() != null && apply.getOuter() && "I".equals(decHead.getIeFlag())))){
                decHead.setPromiseItmes("1|0|0|0");
            }
        }
        if (isNotEmpty(decHead.getPromiseItmes()) && decHead.getPromiseItmes().length() < 11){
            /**
             * 进口 公式定价 空 暂定价格 空 / 出口 公式定价 空 暂定价格 空 2021-11-02(原:进口 公式定价 否 暂定价格 空 / 出口 公式定价 空 暂定价格 空)
             */
            String promiseItmes = decHead.getPromiseItmes().substring(0,7);
            if ("I".equals(decHead.getIeFlag())){// 2022-01-04 修改 进口默认：否|否。出口默认：空|空
				/*String sixPromiseItmes = "9";
				//存在模板价格说明字段为老数据五位，需要判断
				if (isNotEmpty(templPromiseItmes)&&templPromiseItmes.length()>9){
					sixPromiseItmes = templPromiseItmes.substring(10,11);
				}
				decHead.setPromiseItmes(new StringBuilder(promiseItmes).append("|0|").append(sixPromiseItmes).toString());*/
                decHead.setPromiseItmes(new StringBuilder(promiseItmes).append("|0").toString());
            }else {
//				decHead.setPromiseItmes(new StringBuilder(promiseItmes).append("|9|9").toString());
                decHead.setPromiseItmes(new StringBuilder(promiseItmes).append("|9").toString());
            }
        }
    }

//    /**
//     * 瀚瑞森处理合同号
//     * @param decHead
//     * @param apply
//     */
//    private void handleContactByHRS(DecHead decHead,Apply apply,List<ApplyInvoices> invoices) {
//        //需从表体取合同号
//        if ("瀚瑞森（中国）汽车悬挂系统有限公司".equals(apply.getConsignor())){
//            List<String> list = new ArrayList();
//            String pos = "";
//            for (ApplyInvoices ai : invoices) {
//                if (!list.contains(ai.getPo()) && isNotEmpty(ai.getPo())) {
//                    if (isNotEmpty(pos)){
//                        pos = new StringBuilder(pos).append("、").append(ai.getPo()).toString();
//                    }else {
//                        pos = ai.getPo();
//                    }
//                    list.add(ai.getPo());
//                }
//            }
//            if (!list.isEmpty()){
//                if (list.size()>1){
//                    decHead.setContract(list.get(0)+"等");
//                    if (isNotEmpty(decHead.getMarkNumber())){
//                        decHead.setMarkNumber(new StringBuilder(decHead.getMarkNumber()).append(";完整合同协议号:").append(pos).toString());
//                    }else {
//                        decHead.setMarkNumber(new StringBuilder("完整合同协议号:").append(pos).toString());
//                    }
//                }else {
//                    decHead.setContract(list.get(0));
//                }
//            }
//        }
//    }

    /**
     * 根据核注单创建报关单
     * @param nemsInvtHead 核注单
     * @param apply 委托单
     * @param invoices 发票集合
     * @param decHeadTemplete 报关单模板
     * @return
     * @throws RuntimeException
     */
    private DecHead createByNemsInvt(NemsInvtHead nemsInvtHead, OrderInfo apply, List<OrderProductInfo> invoices,
                                     DecHeadTemplete decHeadTemplete, CustomsExempt exempt, String profileType) throws RuntimeException {
        DecHead decHead = new DecHead();
        if (decHeadTemplete != null){
			/*复制排除：口岸检验检疫机关、目的地检验检疫机关、检验检疫受理机关、领证机关,企业资质,使用人,检验检疫签证，启运时间,B/L号,
			 关联号码及理由,原箱运输,特殊业务标识,关联号码,关联理由*/
            BeanUtil.copyProperties(decHeadTemplete,decHead,"inspOrgCode","purpOrgCode","orgCode","vsaOrgCode",
                    "copLimitType","decUserType","requestCertType","despDate","blNo","correlationNo","origBoxFlag","specDeclFlag",
                    "correlationNo","correlationReasonFlag");
        }
        decHead.setId(IdWorker.getIdStr());
        decHead.setAudited(false);
        decHead.setSend(null);
        decHead.setCreateTime(new Date());
        decHead.setSynchronism(false);
        decHead.setClearanceType("M");//报关单类型
        decHead.setCustomsCode("D" + decHead.getId());
        decHead.setInvId(String.valueOf(nemsInvtHead.getId()));

        decHead.setInputId(TenantContext.getTenant());
        decHead.setApplyCreateName(apply.getContactPerson());
        decHead.setMarkNo(isNotEmpty(decHead.getMarkNo()) ? decHead.getMarkNo() : "N/M");

        String[] arr = exempt.getTaxType().split("\\|");
        decHead.setTaxTypeCode(arr[0]);//征免性质

        decHead.setApplyNumber(isNotEmpty(nemsInvtHead.getApplyNumber()) ? nemsInvtHead.getApplyNumber().toString() : null);
        decHead.setEtpsInnerInvtNo(nemsInvtHead.getEtpsInnerInvtNo());
        decHead.setShipTypeCode(isNotBlank(nemsInvtHead.getTrspModecd()) ? nemsInvtHead.getTrspModecd() : decHead.getShipTypeCode());//运输方式
        decHead.setTenantId(isNotEmpty(nemsInvtHead.getTenantId()) ? nemsInvtHead.getTenantId() : null);//租户Id
        decHead.setDclTenantId(isNotEmpty(nemsInvtHead.getDclTenantId()) ? String.valueOf(nemsInvtHead.getDclTenantId()) : null);// 申请人租户ID
        decHead.setRecordNumber(nemsInvtHead.getPutrecNo());//备案号


//        CustomerEnterprise optUnitEnterprise = RpcKit.get(customerApi,CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,null,apply.getShipper()).getData();
        // 境内取业务报关单标签的境内--如果是企业登录，则页面联动委托方
//        Commissioner commissioner = getCommissionerByCond(apply.getReceiver());
//        CustomerEnterprise customerEnterprise = getCustomerEnterpriseByCond(apply.getReceiver());
//        String optUnitId = isNotEmpty(commissioner) ? commissioner.getDepartcd() : null;
//        String optUnitSocialCode = isNotEmpty(commissioner) ? commissioner.getUnifiedSocialCreditCode() : null;
//        if (isBlank(optUnitId) && isNotEmpty(customerEnterprise)) {
//            optUnitId = customerEnterprise.getDepartcd();
//        }
//        if (isBlank(optUnitSocialCode) && isNotEmpty(customerEnterprise)) {
//            optUnitSocialCode = customerEnterprise.getSocialCode();
//        }
        LoveUBaby loveUBaby = pleaseKissMe(apply.getReceiver(), apply.getIeFlag(), "1");
        decHead.setOptUnitName(apply.getReceiver());// 境内收发货人（关联经营单位）
        decHead.setOptUnitId(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : decHead.getOptUnitId());
        decHead.setOptUnitSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : decHead.getOptUnitSocialCode());
        decHead.setTradeCiqCode(isNotEmpty(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : decHead.getTradeCiqCode());
        // 境外取业务报关单标签的供应商--页面联动基本信息的境外供应商
        if ("I".equals(apply.getIeFlag())) {
            LoveUBaby loveUBaby1 = pleaseKissMe(apply.getDomesticSuppliersInfoName(), apply.getIeFlag(), "2");
            decHead.setOverseasConsignorEname(loveUBaby1.getDepartName()); // 境外发货人名称
            decHead.setOverseasConsignorCode(loveUBaby1.getDepartcd()); // 境外发货人代码
        } else {
            LoveUBaby loveUBaby1 = pleaseKissMe(apply.getOverseasPayerInfoId(), apply.getIeFlag(), "2");
            decHead.setOverseasConsigneeEname(loveUBaby1.getDepartName()); // 境外收货人名称
            decHead.setOverseasConsigneeCode(loveUBaby1.getDepartcd());
        }
        // 消费使用单位取报关单标签的境内
        decHead.setDeliverUnitName(apply.getReceiver());// 消费使用单位（加工单位）
        decHead.setDeliverUnit(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : decHead.getDeliverUnit());
        decHead.setDeliverUnitSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : decHead.getDeliverUnitSocialCode());
        decHead.setOwnerCiqCode(isNotEmpty(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : decHead.getOwnerCiqCode());


        decHead.setDeclareUnit(nemsInvtHead.getDclEtpsno());//申报企业
        decHead.setDeclareUnitName(nemsInvtHead.getDclEtpsNm());
        decHead.setDeclareUnitSocialCode(nemsInvtHead.getDclEtpsSccd());
//        CustomerEnterprise declEnterprise = RpcKit.get(customerApi,CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,
//                null,nemsInvtHead.getDclEtpsNm()).getData();
        List<CustomerEnterprise> declEnterprise = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, nemsInvtHead.getDclEtpsNm()));
        decHead.setDeclCiqCode(isNotEmpty(declEnterprise) && isNotEmpty(declEnterprise.get(0).getCiqCode()) ? declEnterprise.get(0).getCiqCode() : decHead.getDeclCiqCode());

//        List<CustomerEnterprise> customerEnterpriseList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
//                .eq(CustomerEnterprise::getDepartName, apply.getOverseasPayerInfoName()));
//        if ("I".equals(decHead.getIeFlag())) {
//            decHead.setOverseasConsignorEname(apply.getOverseasPayerInfoName()); // 境外发货人名称
//            decHead.setOverseasConsignorCode(isNotEmpty(customerEnterpriseList) ? customerEnterpriseList.get(0).getDepartcd() : null); // 境外发货人代码
//        } else {
//            decHead.setOverseasConsigneeEname(apply.getOverseasPayerInfoName()); // 境外收货人名称
//            decHead.setOverseasConsigneeCode(isNotEmpty(customerEnterpriseList) ? customerEnterpriseList.get(0).getDepartcd() : null);
//        }

        decHead.setIeFlag(nemsInvtHead.getImpexpMarkcd());//进出口
        decHead.setCreatePerson(nemsInvtHead.getCreatePerson());//录入人

        decHead.setPacks(apply.getPacks() != null ? apply.getPacks() : decHead.getPacks());//件数
        decHead.setGrossWeight(apply.getGw() != null ? apply.getGw() : decHead.getGrossWeight());//毛重

        decHead.setShipName(
                (StringUtils.isNotBlank(apply.getTransportName())) ? apply.getTransportName() : decHead.getShipName());// 运输工具名称
        decHead.setVoyage((StringUtils.isNotBlank(apply.getVoy())) ? apply.getVoy() : decHead.getVoyage());// 航次号
        if ("I".equals(apply.getIeFlag())) {
            decHead.setDespPortCode((StringUtils.isNotBlank(apply.getPortDestination())) ? apply.getPortDestination()
                    : decHead.getDespPortCode());// 启运港
//            decHead.setDesPort(
//                    StringUtils.isNotBlank(apply.getDesPort()) ? apply.getDesPort() : decHead.getDesPort());// 经停港
        } else if ("E".equals(apply.getIeFlag())) {
            decHead.setDesPort(StringUtils.isNotBlank(apply.getPortDestination()) ? apply.getPortDestination() : decHead.getDesPort());// 目的港(指运港)
        }

        decHead.setShipFeeCode(isNotBlank(apply.getShipFeeCode()) ? apply.getShipFeeCode() : decHead.getShipFeeCode());//运费代码
        decHead.setShipFee(apply.getFreightAmount() != null ? apply.getFreightAmount() : decHead.getShipFee());//运费
        decHead.setShipCurrencyCode(isNotBlank(apply.getShipCurrencyCode()) ? apply.getShipCurrencyCode() : decHead.getShipCurrencyCode());//运费币制
        decHead.setInsuranceCode(isNotBlank(apply.getInsuranceCode()) ? apply.getInsuranceCode() : decHead.getInsuranceCode());//保费代码
        decHead.setInsurance(apply.getPremiumAmount() != null ? apply.getPremiumAmount() : decHead.getInsurance());//保费
        decHead.setInsuranceCurr(isNotBlank(apply.getInsuranceCurr()) ? apply.getInsuranceCurr() : decHead.getInsuranceCurr());//保费
        decHead.setOtherCurr(isNotBlank(apply.getOtherCurr()) ? apply.getOtherCurr() : decHead.getOtherCurr());//杂费币制
        decHead.setExtras(apply.getMiscellaneousAmount() != null ? apply.getMiscellaneousAmount() : decHead.getExtras());//杂费
        decHead.setExtrasCode(isNotBlank(apply.getExtrasCode()) ? apply.getExtrasCode() : decHead.getExtrasCode());//杂费代码
        String termsTypeCode = isNotBlank(apply.getTransMode()) ? apply.getTransMode() : (isNotEmpty(apply.getTradingType()) ? String.valueOf(apply.getTradingType()) : null);
        decHead.setTermsTypeCode(isNotBlank(termsTypeCode) ? termsTypeCode : decHead.getTermsTypeCode());//成交方式
        decHead.setOutPortCode(!isEmpty(apply.getExitClearance()) ? apply.getExitClearance() : decHead.getOutPortCode());//进出境关别
        if (!"1233".equals(nemsInvtHead.getSupvModecd())){
            decHead.setContract(isNotBlank(apply.getExportContractNo()) ? apply.getExportContractNo() : decHead.getContract());//合同号（取委托订单号）
        }
        // 获取任意一个非空且非空字符串的 type
        String packsKinds = invoices.stream()
                .map(OrderProductInfo::getShipmentPackingType)
                .filter(i -> i != null && !i.isEmpty())
                .findAny()
                .orElse(null);
        decHead.setPacksKinds(packsKinds);// 包装种类
        decHead.setPackType(isNotBlank(apply.getOtherPack()) ? apply.getOtherPack() : decHead.getPackType());//其他包装
        // 2024/11/29 10:52@ZHANGCHAO 追加/变更/完善：申报地海关只取模板！！
//        decHead.setDeclarePlace(isNotEmpty(apply.getExitClearance())
//                ? apply.getExitClearance() : nemsInvtHead.getDclplcCuscd());//申报地海关（有委托取委托，没委托取核注单主管海关）
        decHead.setTradeCountry(
                StringUtils.isNotBlank(apply.getTradingCountry()) ? apply.getTradingCountry() : decHead.getTradeCountry()); // 贸易国
//        decHead.setHawb(apply.getHbl());//分单号
        decHead.setGoodsType(apply.getGoodsType());//货物类别
        decHead.setChargedWeight(apply.getChargedWeight());//计费重量
        String markNumber = decHead.getMarkNumber();
        if (isNotEmpty(apply.getDecRemark())){
            if (isNotEmpty(markNumber)){
                markNumber = new StringBuilder(apply.getDecRemark()).append(";").append(markNumber).toString();
            }else {
                markNumber = new StringBuilder(apply.getDecRemark()).toString();
            }
        }
        // 2024/11/27 13:23@ZHANGCHAO 追加/变更/完善：
        // 2. 报关单备注：博汇默认：加工费：取值导入表加工费，交货单号：取值导入表交货单号（多个拼接）。
        // 示例：加工费：53497.35，交货单号：8900197314
        if ("山东博汇纸业股份有限公司".equals(decHead.getOptUnitName())){
            String shipCurrencyCode = invoices.get(0).getCustomsDeclarationCurrency();
            if (isNotBlank(invoices.get(0).getCustomsDeclarationCurrency()) && isNumeric(invoices.get(0).getCustomsDeclarationCurrency())) {
                if (StringUtils.isNotEmpty(invoices.get(0).getCustomsDeclarationCurrency())){
                    ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                            .eq(ErpCurrencies::getCode, invoices.get(0).getCustomsDeclarationCurrency()));
                    shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
                }
            }
            markNumber = "加工费：" + (isNotEmpty(apply.getProcessingFee()) ? apply.getProcessingFee().stripTrailingZeros().toPlainString() : "")
                    + " " + shipCurrencyCode
                    + "，交货单号：" + (isNotBlank(apply.getDeliveryNoteNumber()) ? apply.getDeliveryNoteNumber() : "");
        }
        decHead.setMarkNumber(markNumber);
        // 提运单号处理（无分单号直接用主单号，有分单号：主单号_分单号）
//        String billCode = isNotEmpty(apply.getMBillNo()) ? apply.getMBillNo() : "";
        String billCode = isNotEmpty(apply.getDeliveryNumbers()) ? apply.getDeliveryNumbers() : "";
        if (isNotEmpty(apply.getHbl())) {
            if (isEmpty(billCode)) {
                billCode = isNotEmpty(apply.getHbl()) ? apply.getHbl() : "";
            } else if (isNotEmpty(apply.getHbl())) {
                billCode = new StringBuilder().append(billCode).append("_").append(apply.getHbl()).toString();
            }
        }
        decHead.setBillCode(isNotEmpty(billCode) ? billCode : isNotEmpty(decHead.getBillCode()) ? decHead.getBillCode() : "");
        decHead.setArrivalArea(
                StringUtils.isNotBlank(apply.getCountryArrival()) ? apply.getCountryArrival() : decHead.getArrivalArea());// 启运国/运抵国
        //处理报关单的进出口及类型
        judgeDdeclarationType(nemsInvtHead.getDecType(), decHead);

        decHead.setInspMonitorCond("0");//默认非涉检5
        //判断报关单是否涉检
        dealSupv(invoices, apply, decHead);

        decHead.setDecLists(new ArrayList<>());
        BigDecimal netWeight = BigDecimal.ZERO;
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal goodsCount = BigDecimal.ZERO;
        BigDecimal extras = null;
        int item= 1;
        for (OrderProductInfo v : invoices) {
            DecList decList = invoice2DecList(v, decHeadTemplete != null && decHeadTemplete.getDecListTempletes()!=null
                    && !decHeadTemplete.getDecListTempletes().isEmpty()
                    ? decHeadTemplete.getDecListTempletes().get(0) : null, apply, decHead.getIeFlag(),exempt);

            if ((isNotEmpty(decHead.getInspMonitorCond()) && !"0".equals(decHead.getInspMonitorCond())) || "1".equals(profileType)){
                if ((isNotEmpty(v.getMonitorcondition()) && (v.getMonitorcondition().contains("A")
                        || v.getMonitorcondition().contains("L") || v.getMonitorcondition().contains("P")
                        || v.getMonitorcondition().contains("M") || v.getMonitorcondition().contains("R")))
                        || (isNotEmpty(v.getDgFlag()) && v.getDgFlag())//判断涉检项或者危险品
                        || ("2".equals(decHead.getInspMonitorCond()) && item == 1)//判断木质包装第一项涉检
                        || "1".equals(profileType)) {
                    decList.setCiqCode(v.getCiqCode());
                    decList.setCiqName(v.getCiqName());
                    decList.setGoodsAttr(isNotEmpty(decList.getGoodsAttr())
                            ? isNotEmpty(v.getGoodsAttr()) ? decList.getGoodsAttr()+","+v.getGoodsAttr() : decList.getGoodsAttr()
                            : v.getGoodsAttr());
                }else {
//					if ("2".equals(decHead.getInspMonitorCond()) && item>1){}
                }

            }

            // 国别地区
            List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
            List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
            Map<String, String> dictMap1 = new HashMap<>();
            Map<String, String> dictMap0 = new HashMap<>();
            // 将 dictModel 列表转换为字典 map
            if (isNotEmpty(dictModels1)) {
                dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
            }
            if (isNotEmpty(dictModels0)) {
                dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
            }
            // 使用 dictMap1 中的 value 替换 dictMap0 的 key
            Map<String, String> replacedDictMap0 = new HashMap<>();
            dictMap1.forEach((key, value) ->
                    replacedDictMap0.put(dictMap0.getOrDefault(key, key), value) // BAR-410
            );

            String dectinationCountry = "";
//            if("费斯托气动有限公司".equals(apply.getConsignor())){
//                dectinationCountry = v.getToCountry();
//            }
            if(isEmpty(dectinationCountry)){
                dectinationCountry = replacedDictMap0.get(nemsInvtHead.getNemsInvtLists().get(0).getNatcd()) != null
                        ? replacedDictMap0.get(nemsInvtHead.getNemsInvtLists().get(0).getNatcd()) : nemsInvtHead.getNemsInvtLists().get(0).getNatcd();
            }
            decList.setDestinationCountry(isNotBlank(dectinationCountry) ? dectinationCountry : decList.getDestinationCountry());
            decList.setDecId(decHead.getId());
            decList.setItem(item);
            decList.setRecordItem(v.getInvoicesRecordItem());
            item++;
//			if ("费斯托气动有限公司".equals(apply.getConsignor())){
            String hstype = "";
//			if (isNotEmpty(decHead.getInspMonitorCond()) && !"0".equals(decHead.getInspMonitorCond())){
            hstype = isNotBlank(v.getPreviewTag()) ? v.getPreviewTag() : "";
//			}
            //处理标记：核酸，两部申报
			/*String hsTypeSign = "";
			if (isNotEmpty(v.getHsCodeSign())){
				if (v.getHsCodeSign().contains("0")){
					hsTypeSign = "NO-LB";
				}
				if (v.getHsCodeSign().contains("1")){
					if (isNotEmpty(hsTypeSign)){
						hsTypeSign = new StringBuilder(hsTypeSign).append(",HS").toString();
					}else {
						hsTypeSign = "HS";
					}
				}
			}*/
            decList.setHstype(new StringBuilder(hstype)
                    .append("(").append(decList.getHstype()).append(")").append(isNotEmpty(v.getHsTypeSign()) ? v.getHsTypeSign() : "").toString());

//			}
            decHead.getDecLists().add(decList);
            decHead.setTradeTypeCode(v.getSupervisionMode());
            netWeight = netWeight.add(decList.getNetWeight() != null ? decList.getNetWeight() : BigDecimal.ZERO);
            total = total.add(decList.getTotal() != null ? decList.getTotal() : BigDecimal.ZERO);
            goodsCount = goodsCount.add(isNotEmpty(decList.getGoodsCount()) ? decList.getGoodsCount() : BigDecimal.ZERO);
            if (v.getDecHead() != null && apply.getExchangeRateMaps() != null) {
                if ("1".equals(v.getDecHead().getTermsTypeCode())){
                    extras = extras == null ? BigDecimal.ZERO : extras;
                }else if (extras == null) {
                    extras = countExtras(apply.getExchangeRateMaps(), decList, v.getDecHead());
                }else {
                    extras = extras.add(countExtras(apply.getExchangeRateMaps(), decList, v.getDecHead()));
                }
                if (extras != null && extras.compareTo(BigDecimal.ZERO) ==1){
                    decHead.setExtras(extras);
                    decHead.setExtrasCode("3");
                    decHead.setOtherCurr("USD");
                }

            }
        }
        //处理涉检项
        handleInspection(decHead, decHeadTemplete, apply.getBuyerName(), profileType, null);
        resetContact(decHead,apply);
        //太古处理合同号
//		handleContactByTaigu(decHead,apply);

        decHead.setTradeTypeCode(apply.getSupervisionMode());
//		decHead.setNetWeight(netWeight.compareTo(BigDecimal.ONE) >=1 ? netWeight.stripTrailingZeros() : BigDecimal.ONE.stripTrailingZeros());
        decHead.setNetWeight(netWeight.compareTo(new BigDecimal("0.01"))==-1? new BigDecimal("0.01")
                : (netWeight.compareTo(new BigDecimal("1"))==-1?netWeight.setScale(2,BigDecimal.ROUND_HALF_DOWN).stripTrailingZeros():netWeight.stripTrailingZeros()));
        decHead.setTotal(total);
        decHead.setGoodsCount(goodsCount);
        //费斯托处理保费
//        handleDecHeadToInsurance(decHead,apply);
        return decHead;
    }

//    private void handleDecHeadToInsurance(DecHead decHead,Apply apply){
//        if ("费斯托气动有限公司".equals(apply.getConsignor()) && isNotEmpty(apply.getTransMode()) && "7/3/2".contains(apply.getTransMode())){
//            if ("0".equals(apply.getShippingType())){
////				decHead.setInsurance(new BigDecimal("0.3"));
//                decHead.setInsurance(new BigDecimal(fstZbInsurance));
//            }else if ("1".equals(apply.getShippingType())){
////				decHead.setInsurance(new BigDecimal("0.0154"));
//                decHead.setInsurance(new BigDecimal(fstGysInsurance));
//            }
//        }
//
//    }

    /**
     * 重置合同号
     * [
     * 费斯托特殊处理:
     * 从发票表体取合同号并组合; 合同号前+C;
     * ]
     *
     * 海运(Ab,Ac,Ad ==> Ab/c/d)
     * 空运(Ab,Ac,Ad ==> Ab等)
     * 铁路(TODO)
     *
     * @param decHead
     * @return
     */
    private void resetContact(DecHead decHead, OrderInfo apply) {
        //费斯托需从表体取合同号 YTG-GW-4480:若费斯托委托合同号有值则取委托，无值时原逻辑
        if (apply.getBuyerName() != null && apply.getBuyerName().contains("费斯托")) {//decHead.getDeliverUnitName() != null && decHead.getDeliverUnitName().contains("费斯托")
            if (isNotEmpty(apply.getBuyerName())){
                decHead.setContract(apply.getBuyerName());
                return;
            }
            StringBuilder sb = new StringBuilder("");
            List list = new ArrayList();
//            for (DecList dl : decHead.getDecLists()) {
//                if (isNotEmpty(dl.getContract())){
//                    for (String contrcat : dl.getContract().split(",")){
//                        if(!list.contains(contrcat)){
//                            sb.append(sb.length()>0?",":"").append("C" + contrcat);
//                            list.add(contrcat);
//                        }
//                    }
//                }
//            }
            decHead.setContract(sb.toString());
        }

        if (isNotEmpty(decHead.getContract())) {
            String contracts = decHead.getContract().replaceAll(".*,$", "");

            if (isNotEmpty(apply.getShippingType()) && "0".equals(apply.getShippingType())){//总部：合同号加等
                contracts = mergeContractAir(contracts.split(","));
            }else if (isNotEmpty(apply.getShippingType()) && "1".equals(apply.getShippingType())){//供应商合同号为空
                contracts ="";
            }else {

                switch (isNotEmpty(decHead.getShipTypeCode()) ? decHead.getShipTypeCode() : "") {
                    case "2"://水运
                        contracts = mergeContractSea(contracts.split(","), null);
                        break;
                    case "3"://铁路
                    case "4"://公路
                    case "5"://空运
                        contracts = mergeContractAir(contracts.split(","));
                        break;
                    default:
                }
            }
            if (isNotEmpty(contracts) && contracts.length() > 100) {// 如果合同号大于50个字符
                decHead.setContract(contracts.substring(0, 51));// 合同号
            } else {
                decHead.setContract(contracts);// 合同号
            }
        }
    }

    /**
     * 处理海运的合同号(Ab,Ac,Ad ==> Ab/c/d)
     *
     * @param strArr
     * @param contract
     * @return
     */
    private String mergeContractSea(String[] strArr, String contract) {
        if (strArr == null || strArr.length == 0)
            return null;

        if (contract == null)
            contract = strArr[0];

        boolean choise = false;
        for (String str : strArr) {
            if (str.indexOf(contract) != 0) {
                choise = true;
            }
        }

        if (choise) {
            contract = contract.substring(0, contract.length() - 1);
            return mergeContractSea(strArr, contract);
        }

        String finalStr = "";
        for (String string : strArr) {
            if ("".equals(finalStr)) {
                finalStr = string.substring(contract.length(), string.length());
            } else {
                finalStr += "/" + string.substring(contract.length(), string.length());
            }
        }
        return contract + finalStr;
    }

    /**
     * 处理空运的合同号(Ab,Ac,Ad ==> Ab等)
     *
     * @param strArr
     * @return
     */
    private String mergeContractAir(String[] strArr) {

        String contacts = null;
        if (strArr != null && strArr.length > 1) {
            contacts = strArr[0] + "等";
        }else {
            contacts = strArr[0];
        }
        return contacts;
    }

    /**
     * 处理报关单涉检项信息
     * @param decHead
     * @param decHeadTemplete
     */
    private void handleInspection(DecHead decHead,DecHeadTemplete decHeadTemplete,String consignor,String profileType,String applyType){
        if((isNotEmpty(decHead.getInspMonitorCond()) && !"0".equals(decHead.getInspMonitorCond())) || "1".equals(profileType)){
            /*口岸检验检疫机关、目的地检验检疫机关、检验检疫受理机关、领证机关,企业资质,使用人,检验检疫签证，启运时间,B/L号,关联号码及理由,原箱运输,特殊业务标识*/
            if (decHeadTemplete != null){
//				decHead.setInspOrgCode(decHeadTemplete.getInspOrgCode());//口岸检验检疫机关
                decHead.setPurpOrgCode(decHeadTemplete.getPurpOrgCode());//目的地检验检疫机关
//				decHead.setOrgCode(decHeadTemplete.getOrgCode());//检验检疫受理机关
//				decHead.setVsaOrgCode(decHeadTemplete.getVsaOrgCode());//领证机关
                decHead.setCopLimitType(decHeadTemplete.getCopLimitType());//企业资质
                decHead.setDecUserType(decHeadTemplete.getDecUserType());//使用人
                decHead.setRequestCertType(decHeadTemplete.getRequestCertType());//检验检疫签证
//				decHead.setDespDate(decHeadTemplete.getDespDate());//启运时间

                decHead.setBlNo(decHeadTemplete.getBlNo());//B/L号
                decHead.setCorrelationNo(decHeadTemplete.getCorrelationNo());//关联号码及理由
                decHead.setCorrelationReasonFlag(decHeadTemplete.getCorrelationReasonFlag());//关联理由
                decHead.setOrigBoxFlag(decHeadTemplete.getOrigBoxFlag());//原箱运输
                decHead.setSpecDeclFlag(decHeadTemplete.getSpecDeclFlag());//特殊业务标识

            }
            if ("5".equals(decHead.getShipTypeCode())){//空运启运时间获取当前操作日期
                String despDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                decHead.setDespDate(despDate);//启运时间
            }
            String guarantee = "";
            if ("I".equals(decHead.getIeFlag())){
                guarantee = "{\"EntQualifTypeCode\":\"101040\",\"EntQualifNo\":\"\",\"EntQualifTypeName\":\"合格保证\"}";
            }else {
                guarantee = "{\"EntQualifTypeCode\":\"102053\",\"EntQualifNo\":\"\",\"EntQualifTypeName\":\"合格保证\"}";
            }
            //判断企业资质的勾选是否存在
            if (isNotEmpty(decHead.getCopLimitType())
                    && ((!decHead.getCopLimitType().contains("101040") && "I".equals(decHead.getIeFlag()))
                    || (!decHead.getCopLimitType().contains("102053") && "E".equals(decHead.getIeFlag())))){
                //增加企业资质勾选
                if (decHead.getCopLimitType().length()>2){
                    String copLimitType = decHead.getCopLimitType().substring(0,decHead.getCopLimitType().lastIndexOf("]"));
                    copLimitType = new StringBuilder(copLimitType)
                            .append(",").append(guarantee).append("]").toString();
                    decHead.setCopLimitType(copLimitType);
                }else {
                    decHead.setCopLimitType(new StringBuilder("[").append(guarantee).append("]").toString());
                }
            }else {
                decHead.setCopLimitType(new StringBuilder("[").append(guarantee).append("]").toString());
            }


            //处理表体项
            if (decHead.getDecLists() != null && !decHead.getDecLists().isEmpty()){
                List<Integer> nums = new ArrayList<>();
                nums.add(1);
                decHead.getDecLists().forEach(v->{
                    if ((isNotEmpty(v.getSupvModecd()) && (v.getSupvModecd().contains("A")
                            || v.getSupvModecd().contains("L") || v.getSupvModecd().contains("P")
                            || v.getSupvModecd().contains("M") || v.getSupvModecd().contains("R")))
                            || (isNotEmpty(v.getDgFlag()) && v.getDgFlag())//判断涉检项或者危险品
                            || ("2".equals(decHead.getInspMonitorCond()) && nums.get(0) == 1)//判断木质包装第一项涉检
                            || "1".equals(profileType)) {
                        v.setPurpose("99");//用途
                        String goodsAttr =null;
                        if (isNotEmpty(v.getSupvModecd())){
                            if (v.getSupvModecd().contains("L")){
                                goodsAttr = "12,19";
                            }else {
                                goodsAttr = "19";
                            }
                        }else {
                            goodsAttr = "19";
                        }
                        if (isNotEmpty(v.getGoodsAttr()) && !v.getGoodsAttr().contains(goodsAttr)){
                            for (String str : Arrays.asList(v.getGoodsAttr().split(","))){
                                if (!goodsAttr.contains(str)){
                                    goodsAttr = new StringBuilder(goodsAttr).append(",").append(str).toString();
                                }
                            }

                        }
                        v.setGoodsAttr(goodsAttr);
                        int num = nums.get(0);
                        nums.set(0,++num);
                    }else if ("2".equals(decHead.getInspMonitorCond()) && nums.get(0)>1){
                        return;
                    }
                });
            }
            //进口保税仓库，有涉检项的报关单备注里都加上“应检商品,集中检验.”
            if ("BWS".equals(applyType)&&"I".equals(decHead.getIeFlag()) && "1".equals(decHead.getInspMonitorCond())){
                decHead.setMarkNumber(new StringBuilder(decHead.getMarkNumber()).append(";")
                        .append("应检商品,集中检验").toString());
            }

        }

        //费斯托涉检和非涉检备注处理
        if ("费斯托气动有限公司".equals(consignor)){
            if (isNotEmpty(decHead.getInspMonitorCond()) && !"0".equals(decHead.getInspMonitorCond())){
                if (isNotEmpty(decHead.getMarkNumber())){
                    if (!decHead.getMarkNumber().contains("应检商品,429不适用特种设备许可.430不适用特种设备许可.非医疗器械")){
                        if (decHead.getMarkNumber().contains("非医疗器械")){
                            decHead.setMarkNumber(decHead.getMarkNumber().replace("非医疗器械","应检商品,429不适用特种设备许可.430不适用特种设备许可.非医疗器械"));
                        }else {
                            decHead.setMarkNumber(new StringBuilder(decHead.getMarkNumber()).append(";")
                                    .append("应检商品,429不适用特种设备许可.430不适用特种设备许可.非医疗器械").toString());
                        }
                    }
                }else {
                    decHead.setMarkNumber("应检商品,429不适用特种设备许可.430不适用特种设备许可.非医疗器械");
                }
            }else {
                if (isNotEmpty(decHead.getMarkNumber())){
                    if (!decHead.getMarkNumber().contains("非医疗器械")){
                        decHead.setMarkNumber(new StringBuilder(decHead.getMarkNumber()).append(";")
                                .append("非医疗器械").toString());
                    }
                }else {
                    decHead.setMarkNumber("非医疗器械");
                }
            }
        }
    }

    private BigDecimal countExtras(Map<String, BigDecimal> exchangeRateMaps,DecList decList,DecHead decHead){
        BigDecimal extras = BigDecimal.ZERO;
		/*
		（本票本项净重/原进口报关单表头总净重*原进口报关单的表头运费*当前日期上月海关汇率（对美元））
		+（本票本项货值/原进口报关单总货值*当前日期上月海关汇率（对美元））
		*（原进口报关单的运费*当前日期上月海关汇率（对美元）+原进口报关单总货值*当前日期上月海关汇率（对美元））/0.997*0.003；四舍五入保留小数点后两位。
		 */
        if (decHead.getNetWeight() == null){
            return null;
        }
        if (decHead.getTotal() == null){
            return null;
        }
        //净重比
        BigDecimal netWtRatio = decList.getNetWeight().divide(decHead.getNetWeight(),5,BigDecimal.ROUND_HALF_UP);
        //计算运费
        BigDecimal shipFeeExchange = exchangeRateMaps.containsKey(decHead.getShipCurrencyCode()) ? exchangeRateMaps.get(decHead.getShipCurrencyCode()) : BigDecimal.ZERO;
        BigDecimal headShipFee = decHead.getShipFee() == null ? BigDecimal.ZERO : decHead.getShipFee();
        BigDecimal shipFee = headShipFee.multiply(shipFeeExchange);
        //货值比
        BigDecimal totalRatio = decList.getTotal().divide(decHead.getTotal(),5,BigDecimal.ROUND_HALF_UP);
        //当前汇率
        BigDecimal exchanges = exchangeRateMaps.containsKey(decList.getCurrencyCode()) ? exchangeRateMaps.get(decList.getCurrencyCode()) : BigDecimal.ZERO;
        //原报关单总货值
        BigDecimal total = shipFee.add(decHead.getTotal().multiply(exchanges)).divide(new BigDecimal("0.997"),5,BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("0.003"));
        extras = (netWtRatio.multiply(shipFee)
                .add(totalRatio.multiply(exchanges).multiply(total))).setScale(2,BigDecimal.ROUND_HALF_UP);
        return extras;
    }

    /**
     * 转化报关单表体
     *
     * @param invoices
     * @return
     */
    private DecList invoice2DecList(OrderProductInfo invoices, DecListTemplete decListTemplete, OrderInfo apply,
                                    String ieFlag,CustomsExempt exempt) {
        DecList decList = new DecList();
        if (decListTemplete != null) {
            //2022/12/30 移除"goodsAttr","purpose",
            BeanUtil.copyProperties(decListTemplete,decList,"ciqCode","ciqName",
                    "noDangFlag","uncode","dangName","dangPackType","dangPackSpec","goodsAttrStr","goodsLimitType","id");//危险货物信息(6个)和产品资质
        }

//        decList.setInvoiceListNo(invoices.getInvoiceListNo());//赋值虚拟id

        if (isNotEmpty(exempt.getTaxWay())){
            String[] faxTypeCode = exempt.getTaxWay().split("\\|");
            decList.setFaxTypeCode(faxTypeCode != null ? faxTypeCode[0] : null);
        }

        decList.setGoodsId(
                invoices.getMegerSequence() != null ? invoices.getMegerSequence() : invoices.getSequence().toString());
        decList.setDgFlag(invoices.getDgFlag() != null ? invoices.getDgFlag() : false);

        // 国别地区
//        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
//        Map<String, String> dictMap1 = new HashMap<>();
//        if (isNotEmpty(dictModels1)) {
//            dictModels1.forEach(dictModel -> {
//                dictMap1.put(dictModel.getText(), dictModel.getValue());
//            });
//        }
//        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
//        Map<String, String> dictMap0 = new HashMap<>();
//        if (isNotEmpty(dictModels0)) {
//            dictModels0.forEach(dictModel -> {
//                dictMap0.put(dictModel.getText(), dictModel.getValue());
//            });
//        }
//        Map<String, String> replacedDictMap0 = new HashMap<>();
//        for (Map.Entry<String, String> entry : dictMap0.entrySet()) {
//            String originalKey = entry.getKey();
//            String originalValue = entry.getValue();
//            if (dictMap1.containsKey(originalKey)) {
//                String newKey = dictMap1.get(originalKey);
//                replacedDictMap0.put(newKey, originalValue);
//            } else {
//                // 如果dictMap1中没有找到对应的键，则保留原始键值对
//                replacedDictMap0.put(originalKey, originalValue);
//            }
//        }
        // 获取启用和禁用的国家/地区字典
        List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
        List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
        Map<String, String> dictMap1 = new HashMap<>();
        Map<String, String> dictMap0 = new HashMap<>();
        // 将 dictModel 列表转换为字典 map
        if (isNotEmpty(dictModels1)) {
            dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
        }
        if (isNotEmpty(dictModels0)) {
            dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
        }
        // 使用 dictMap1 中的 value 替换 dictMap0 的 key
        Map<String, String> replacedDictMap0 = new HashMap<>();
        dictMap0.forEach((key, value) ->
                replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
        );

        // 币制
//        String shipCurrencyCode = null;
//        if (StringUtils.isNotEmpty(invoices.getCustomsDeclarationCurrency())){
//            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
//                    .eq(ErpCurrencies::getCode, invoices.getCustomsDeclarationCurrency()));
//            shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
//        }

        decList.setDesCountry(invoices.getOriginCountry());
        decList.setDistrictCode(apply.getDomesticSourceOfGoods()); // 境内目的地/境内货源地
        decList.setTotal(invoices.getShipmentGoodsValue());
//        decList.setCurrencyCode(shipCurrencyCode);
        // 币制报关单是字母，核注单是数字！！！
        String shipCurrencyCode = invoices.getCustomsDeclarationCurrency();
        if (StringUtils.isNotEmpty(invoices.getCustomsDeclarationCurrency()) && isNumeric(invoices.getCustomsDeclarationCurrency())){
            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode, invoices.getCustomsDeclarationCurrency()));
            shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
        }
        decList.setCurrencyCode(shipCurrencyCode);
        decList.setNetWeight(invoices.getNetWeight().stripTrailingZeros());
        decList.setGoodsCount(invoices.getShipmentQuantity());
//        decList.setInvoiceNo(invoices.getInvoiceNo());
        decList.setHscode(invoices.getHscode());
        decList.setHsname(invoices.getHsname());
        decList.setHsmodel(invoices.getHsmodel());
        decList.setHstype(apply.getSupervisionMode());
//		decList.setHstype(invoices.getTradeType());
//		decList.setCiqCode(invoices.getCiqCode());
//		decList.setCiqName(invoices.getCiqName());
        String qunit = invoices.getShipmentUnit();
        if (isNotBlank(invoices.getShipmentUnit())) {
            if (!isNumeric(invoices.getShipmentUnit())) {
                ErpUnits units = erpUnitsService.getOne(new QueryWrapper<ErpUnits>().lambda()
                        .eq(ErpUnits::getItemKey, invoices.getShipmentUnit()));
                qunit = units != null ? units.getCode() : null;
            }
        }
        decList.setUnitCode(qunit);
//        decList.setContract(invoices.getContract());
        decList.setPrice(invoices.getShipmentUnitPrice());
//        decList.setGdsMtno(invoices.getRecordPn());
//        decList.setUniversal(invoices.getType());// FIXME ???
        decList.setSupvModecd(invoices.getMonitorcondition());

        //个别税号增加货物属性默认38非医疗器械---9405429000、9405499000、3926909090
        String hscodes = ",9405429000,9405499000,3926909090,9025191010,8421399090,9031809090,8421399090,8479899990,9020000000,";
        if (isNotEmpty(invoices.getHscode()) && hscodes.contains(","+invoices.getHscode()+",") && isNotEmpty(apply.getIeFlag())&& "I".equals(apply.getIeFlag())){
            if(isNotEmpty(decList.getGoodsAttr())){
                if(!decList.getGoodsAttr().contains("38")){
                    decList.setGoodsAttr(decList.getGoodsAttr()+",38");
                }
            }else{
                decList.setGoodsAttr("38");
            }
        }


//        if("费斯托气动有限公司".equals(apply.getConsignor()) && isNotEmpty(invoices.getToCountry())){
//            decList.setDestinationCountry(invoices.getToCountry());
//        }else {
            if ("E".equals(ieFlag)) {//if ("E".equals(apply.getImSign()))
                decList.setDestinationCountry(apply.getCountryArrival());
            }
//        }
//		decList.setKeyElements();
        decList.setUnit1(invoices.getLegalUnitCode());
        decList.setUnit2(invoices.getSecondUnitCode());
        if (isNotEmpty(decList.getUnit1())) {
            if (isNotEmpty(invoices.getLegalQuantity()) && BigDecimal.ZERO.compareTo(invoices.getLegalQuantity()) >0){
                decList.setCount1(invoices.getLegalQuantity());//当委托表体存在发动数量时默认取委托表体
            }else {
                decList.setCount1(decList.getGoodsCount());//默认取成交数量
                if (("035".equals(decList.getUnit1()) || "036".equals(decList.getUnit1()))) {
                    //&& !decList.getUnitCode().equals(decList.getUnit1())
                    if (new BigDecimal("0.001").compareTo(decList.getNetWeight()) > 0) {
                        if ("036".equals(decList.getUnit1())) {
                            decList.setCount1(new BigDecimal("1"));
                        } else {
                            decList.setCount1(new BigDecimal("0.001"));
                        }
                        decList.setNetWeight(new BigDecimal("0.001"));
                    } else {
                        if ("036".equals(decList.getUnit1())){
                            decList.setCount1(decList.getNetWeight().multiply(new BigDecimal("1000")));
                        }else {
                            decList.setCount1(decList.getNetWeight());
                        }
                    }
                } else{
                    decList.setCount1(handleUnitConversion(decList.getUnitCode(),decList.getUnit1(),decList.getGoodsCount()));
                }
            }
        }
        if (isNotEmpty(decList.getUnit2())) {
            if (isNotEmpty(invoices.getSecondNumbers())  && BigDecimal.ZERO.compareTo(invoices.getSecondNumbers()) >0){
                decList.setCount2(invoices.getSecondNumbers());//当委托表体存在发动数量时默认取委托表体
            }else {
                decList.setCount2(decList.getGoodsCount());//默认取成交数量
                if (("035".equals(decList.getUnit2()) || "036".equals(decList.getUnit2()))) {
                    //&& !decList.getUnitCode().equals(decList.getUnit2())
                    if (new BigDecimal("0.001").compareTo(decList.getNetWeight()) > 0) {
                        if ("036".equals(decList.getUnit2())) {
                            decList.setCount2(new BigDecimal("1"));
                        } else {
                            decList.setCount2(new BigDecimal("0.001"));
                        }
                        decList.setNetWeight(new BigDecimal("0.001"));
                    } else {
                        if ("036".equals(decList.getUnit1())) {
                            decList.setCount2(decList.getNetWeight().multiply(new BigDecimal("1000")));
                        } else {
                            decList.setCount2(decList.getNetWeight());
                        }
                    }
                } else {
                    decList.setCount2(handleUnitConversion(decList.getUnitCode(), decList.getUnit2(),
                            decList.getGoodsCount()));
                }
            }
        }

        return decList;
    }

    /**
     * 处理报关单的进出口及类型
     * @param decType
     * @param decHead
     */
    private void judgeDdeclarationType(String decType,DecHead decHead){
        switch (decType){
            case "1"://进口报关单
            case "I"://进口提前/暂时进口报关单
            case "K"://进口提前/中欧班列报关单
            case "X"://进口两部申报报关单
            case "e"://进口两部申报一次录入报关单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("0");//一般报关单
                break;
            case "2"://出口报关单
            case "H"://出口提前/工厂验放报关单
            case "J"://出口提前/暂时出口报关单
            case "L"://出口提前/中欧班列报关单
            case "M"://出口提前/市场采购报关单
            case "N"://出口提前/空运连程报关单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("0");//一般报关单
                break;
            case "3"://进境备案清单
            case "5"://进境两单一审备案清单
            case "O"://进口提前/工厂验放备案清单
            case "Y"://进口两部申报备案清单
            case "f"://进口两部申报一次录入备案清单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("2");//备案清单
                break;
            case "4"://出境备案清单
            case "6"://出境两单一审备案清单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("2");//备案清单
                break;
            case "9"://转关提前进口报关单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("1");//转关提前报关单
                break;
            case "A"://转关提前出口报关单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("1");//转关提前报关单
                break;
            case "B"://转关提前进境备案清单
                decHead.setIeFlag("I");//进出口
                decHead.setDclTrnRelFlag("3");//转关提前备案清单
                break;
            case "C"://转关提前出境备案清单
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("3");//转关提前备案清单
                break;
            case "F":
                decHead.setIeFlag("E");//进出口
                decHead.setDclTrnRelFlag("4");//备案清单
                break;
        }
    }

    /**
     * 判断涉检项
     *
     * @param invoices
     * @param apply
     */
    private void dealSupv(List<OrderProductInfo> invoices, OrderInfo apply,DecHead decHead) {
        invoices.forEach(v-> {

            if ("I".equals(decHead.getIeFlag()) && (isEmpty(decHead.getInspMonitorCond()) || "0".equals(decHead.getInspMonitorCond()))) {//2021.05.25 su 只判断进口是否涉检

//                if ("费斯托气动有限公司".equals(apply.getConsignor()) && isNotEmpty(v.getPreviewTag())
//                        && !"CC".equals(v.getPreviewTag())){//费斯托
//                    decHead.setInspMonitorCond("1");
//                    return;
//                }

                if ((isNotEmpty(v.getMonitorcondition()) && (v.getMonitorcondition().contains("A")
                        || v.getMonitorcondition().contains("L") || v.getMonitorcondition().contains("P")
                        || v.getMonitorcondition().contains("M") || v.getMonitorcondition().contains("R")))
                        || (v.getDgFlag() != null && v.getDgFlag())) {
//					if (isEmpty(v.getPreviewTag())){
//						v.setPreviewTag(v.getSupvModecd());
//					}
                    decHead.setInspMonitorCond("1");
                    return;
                } else if ((isNotEmpty(apply.getPacksKinds()) && (apply.getPacksKinds().contains("23")
                        || apply.getPacksKinds().contains("33") || apply.getPacksKinds().contains("93")
                        || apply.getPacksKinds().contains("98")))
                        || (isNotEmpty(apply.getOtherPack()) && (apply.getOtherPack().contains("23")
                        || apply.getOtherPack().contains("33") || apply.getOtherPack().contains("93")
                        || apply.getOtherPack().contains("98")))) {//包装种类和其他包装都判断是否是木质包装
                    //木质包装下二线出区的区外进口数据不做涉检判断
                    if (!(isNotEmpty(apply.getApplyKind()) && apply.getApplyKind().contains("2") && "I".equals(apply.getIeFlag()) && apply.getOuter() != null && apply.getOuter())) {
//							if (isEmpty(v.getPreviewTag())){
//								v.setPreviewTag(v.getSupvModecd());
//							}
                        decHead.setInspMonitorCond("2");
                        return;
                    }
                }
            }
        });
    }

    /**
     * 带取料件成品标识
     * @param invtHead
     * @param apply
     * @param emsNo
     */
    private void setMtpckEndprdMarkcd(NemsInvtHead invtHead, OrderInfo apply, String emsNo){
        if (isNotEmpty(emsNo)){
            if ("H".equals(emsNo.substring(0,1))){
//                invtHead.setMtpckEndprdMarkcd(apply.getOutZoneMode());
                if ("I".equals(apply.getIeFlag())){
                    invtHead.setMtpckEndprdMarkcd("I");
                }else if ("E".equals(apply.getIeFlag())){
                    invtHead.setMtpckEndprdMarkcd("E");
                }
            }
            if ("T/L".contains(emsNo.substring(0,1))){//TODO:增加太古业务的账册 "T".equals(emsNo.substring(0,1))
                invtHead.setMtpckEndprdMarkcd("I");
            }
            if ("B/C/E".contains(emsNo.substring(0,1))){
                if ("I".equals(apply.getIeFlag())){
                    invtHead.setMtpckEndprdMarkcd("I");
                }else if ("E".equals(apply.getIeFlag())){
                    invtHead.setMtpckEndprdMarkcd("E");
                }
            }
        }
    }
    /**
     * 7.2.1 生成通用核注单信息
     *
     * @param list
     * @param orderInfo
     * @param emsHead
     * @return
     * @throws RuntimeException
     */
    private NemsInvtHead toNemsInvtHead(List<OrderProductInfo> list, OrderInfo orderInfo, CustomsExempt exempt, PtsEmsHead emsHead) throws RuntimeException {

        if (exempt.getHasInvt() && isEmpty(orderInfo.getRecordNumber())) {
			/*if (apply.getOuter() != null && apply.getOuter()){
				throw new YmException(RspCode.REQ_ERR, "缺少区外账册号");
			}else {
				throw new YmException(RspCode.REQ_ERR, "缺少区内账册号");
			}*/
            throw new RuntimeException("缺少手账册号");
        }

        NemsInvtHead nemsInvtHead = new NemsInvtHead();
        nemsInvtHead.setId(IdWorker.getId());

        // 默认信息
        String tenantId = TenantContext.getTenant();
        nemsInvtHead.setTenantId(Long.valueOf(tenantId));
        nemsInvtHead.setDclTenantId(Long.valueOf(tenantId));
        nemsInvtHead.setVrfdedMarkcd("0");// 核扣状态
        nemsInvtHead.setDclTypecd("1");// 申报类型-备案
        nemsInvtHead.setInputTime(new Date());// 录入日期
        nemsInvtHead.setCreateDate(new Date());// 创建日期
        nemsInvtHead.setAudited(false);// 审核状态
        nemsInvtHead.setSend(false);// 发送状态
        // TODO -ZJ
//		nemsInvtHead.setDclEtpsno("3701983939");// 申报企业海关编码
//		nemsInvtHead.setDclEtpsNm("山东迅吉安国际物流有限公司");// 申报企业名称
//		nemsInvtHead.setDclEtpsSccd("91370100560757589T");// 申报企业社会信用代码
        nemsInvtHead.setDclEtpsno(orderInfo.getDeclareUnit());// 申报企业海关编码
        nemsInvtHead.setDclEtpsNm(orderInfo.getDeclareUnitName());// 申报企业名称
        nemsInvtHead.setDclEtpsSccd(orderInfo.getDeclareUnitSocialCode());// 申报企业社会信用代码
//        Result<Tenant> tenant = sysBaseApi.getTenantByName(nemsInvtHead.getDclEtpsNm());
//        Integer customerTenantId;
//        if (isNotEmpty(tenant.getResult())) {
//            customerTenantId = tenant.getResult().getId();
//        } else {
//            customerTenantId = -1;
//        }
//        YmMsg<Customer> customerYmMsg = RpcKit.get(customerApi, CustomerApi::getCustomerByName,nemsInvtHead.getDclEtpsNm(),"-1");
//        Customer dclEtps = isNotEmpty(customerYmMsg.getData()) ? customerYmMsg.getData() : null;
//        nemsInvtHead.setDclTenantId(isNotEmpty(dclEtps) ? dclEtps.getId() : null);// 申请人租户ID TODO：id替换
        nemsInvtHead.setInvtType("0");// 清单类型
        // 录入单位 = 当前企业
        LoveUBaby loveUBaby_ = pleaseKissMe(TenantContext.getTenant(), orderInfo.getIeFlag(), "1");
        nemsInvtHead.setInputCode(loveUBaby_.getDepartcd());
        nemsInvtHead.setInputCreditCode(loveUBaby_.getSocialCode());
        nemsInvtHead.setInputName(loveUBaby_.getDepartName());

//        nemsInvtHead.setNonBusiness(choise);//是否是自己单子

        //处理是否报关及报关类型
        nemsInvtHead.setDclcusFlag(exempt.getDclcusFlag()); //否报关标志
        nemsInvtHead.setDclcusTypecd(exempt.getDclcusTypecd()); //报关类型代码
        nemsInvtHead.setGenDecFlag(exempt.getHasDec() ? "1" : "2");//是否生成报关单
        nemsInvtHead.setDecType(exempt.getDecType());//报关单类型

//        String customerId = getHeaders(CUSTOMER_ID);
        nemsInvtHead.setInputId(Long.valueOf(TenantContext.getTenant()));

        // 经营单位 = 境内收货人
        LoveUBaby loveUBaby = pleaseKissMe(orderInfo.getReceiver(), orderInfo.getIeFlag(), "1");
        nemsInvtHead.setBizopEtpsNm(orderInfo.getReceiver());// 境内收发货人（关联经营单位）
        nemsInvtHead.setBizopEtpsno(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : nemsInvtHead.getBizopEtpsno());
        nemsInvtHead.setBizopEtpsSccd(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : nemsInvtHead.getBizopEtpsSccd());

        // 加工单位 = 境内收货人
        nemsInvtHead.setRcvgdEtpsNm(orderInfo.getReceiver());// 境内收发货人（关联经营单位）
        nemsInvtHead.setRcvgdEtpsno(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : nemsInvtHead.getRcvgdEtpsno());
        nemsInvtHead.setRvsngdEtpsSccd(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : nemsInvtHead.getRvsngdEtpsSccd());

//        if (isNotEmpty(orderInfo.getRecordNumber()) && "T".contains(orderInfo.getRecordNumber().substring(0,1))
//                && !(isNotEmpty(exempt.getSource()) && "Im/Ex".contains(exempt.getSource()))
//                && emsHead != null){//保税物流业务
//            // 经营企业信息
//            nemsInvtHead.setBizopEtpsNm(emsHead.getTradeName());
//            nemsInvtHead.setBizopEtpsno(emsHead.getTradeCode());
//            nemsInvtHead.setBizopEtpsSccd(emsHead.getTradeSccd());
//            // 加工企业信息
//            nemsInvtHead.setRcvgdEtpsNm(emsHead.getTradeName());
//            nemsInvtHead.setRcvgdEtpsno(emsHead.getTradeCode());
//            nemsInvtHead.setRvsngdEtpsSccd(emsHead.getTradeSccd());
//        }else {
//            String buyerName = orderInfo.getBuyerName();
//            Commissioner commissioner = commissionerMapper.selectById(orderInfo.getBuyer());
//            if (isNotEmpty(commissioner)) {
//                buyerName = commissioner.getCommissionerFullName();
//            }
//            // 经营企业信息
////            String bizopEtpsNm =(isNotEmpty(orderInfo.getRecordNumber()) && "H".equals(orderInfo.getRecordNumber().substring(0,1)) && !(isNotEmpty(exempt.getSource()) && "Im/Ex".contains(exempt.getSource())))
////                    || (isNotEmpty(exempt.getSource()) && "Im/Ex".contains(exempt.getSource()) && isNotEmpty(orderInfo.getSign()) && "1".equals(orderInfo.getSign()))
////                    ? orderInfo.getConsignor() : orderInfo.getShipper();
//            String bizopEtpsNm = buyerName;
////            CustomerEnterprise bizopEtpsNmEnterprise = RpcKit.get(customerApi, CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,
////                    null, bizopEtpsNm).getData();
//            List<CustomerEnterprise> customerEnterpriseList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
//                    .eq(CustomerEnterprise::getDepartName, buyerName));
//            nemsInvtHead.setBizopEtpsNm(bizopEtpsNm);
////            if (bizopEtpsNmEnterprise != null) {
////                nemsInvtHead.setBizopEtpsno(bizopEtpsNmEnterprise.getDepartcd());
////                nemsInvtHead.setBizopEtpsSccd(bizopEtpsNmEnterprise.getSocialCode());
////            }
//            if (isNotEmpty(customerEnterpriseList)) {
//                nemsInvtHead.setBizopEtpsno(customerEnterpriseList.get(0).getDepartcd());
//                nemsInvtHead.setBizopEtpsSccd(customerEnterpriseList.get(0).getSocialCode());
//            }
//            // 加工企业信息（委托消费使用单位
//            String rcvgdEtpsNm = (isNotEmpty(orderInfo.getRecordNumber()) && "H".equals(orderInfo.getRecordNumber().substring(0,1)) && !(isNotEmpty(exempt.getSource()) && "Im/Ex".contains(exempt.getSource())))
//                    || (isNotEmpty(exempt.getSource()) && "Im/Ex".contains(exempt.getSource()) && isNotEmpty(orderInfo.getSign()) && "1".equals(orderInfo.getSign()))
//                    ? orderInfo.getBuyerName() : orderInfo.getDeliverUnitName();
////            CustomerEnterprise rcvgdEtpsEnterprise = RpcKit.get(customerApi, CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,
////                    null, rcvgdEtpsNm).getData();
//            nemsInvtHead.setRcvgdEtpsNm(rcvgdEtpsNm);
////            if (rcvgdEtpsEnterprise != null) {
////                nemsInvtHead.setRcvgdEtpsno(rcvgdEtpsEnterprise.getDepartcd());
////                nemsInvtHead.setRvsngdEtpsSccd(rcvgdEtpsEnterprise.getSocialCode());
////            }
//            if (isNotEmpty(customerEnterpriseList)) {
//                nemsInvtHead.setRcvgdEtpsno(customerEnterpriseList.get(0).getDepartcd());
//                nemsInvtHead.setRvsngdEtpsSccd(customerEnterpriseList.get(0).getSocialCode());
//            }
//        }



		/*if (emsHead != null) {//2022-01-28 主管海关直接截取
			nemsInvtHead.setDclplcCuscd(emsHead.getMasterCustoms());// 申报地关区代码(主管海关）
		}else */
        if (isNotEmpty(orderInfo.getRecordNumber()) && orderInfo.getRecordNumber().length()>=5) {
            nemsInvtHead.setDclplcCuscd(orderInfo.getRecordNumber().substring(1,5));// 申报地关区代码(获取手册号去除第一位的后四位）
        }


//        String token = getHeaders(HEADER_TOKEN);
//        // 从redis获取当前登录用户名
//        String username = (String) ((HashMap<String, Object>) redisUtil.get(PREFIX_USER_TOKEN_INFO + token))
//                .get(USERNAME);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 来自委托
        nemsInvtHead.setApplyNumber(Long.valueOf(orderInfo.getId()));// 委托单号
        nemsInvtHead.setCreatePerson(loginUser.getUsername());// 创建人
        nemsInvtHead.setImpexpMarkcd(orderInfo.getIeFlag());//进出口
        nemsInvtHead.setPutrecNo(orderInfo.getRecordNumber());// 备案号
        nemsInvtHead.setImpexpPortcd(orderInfo.getDeparturePort());// 进出关别
        nemsInvtHead.setGoodsType(orderInfo.getGoodsType());//货物类别
        nemsInvtHead.setChargedWeight(orderInfo.getChargedWeight());//计费重量


        if ("1".equals(nemsInvtHead.getDclcusTypecd())) {// 关联报关
            // 关联报关单申报单位信息
            nemsInvtHead.setRltEntryDclEtpsno(nemsInvtHead.getDclEtpsno());
            nemsInvtHead.setRltEntryDclEtpsNm(nemsInvtHead.getDclEtpsNm());
            nemsInvtHead.setRltEntryDclEtpsSccd(nemsInvtHead.getDclEtpsSccd());

//            String rltEntryRcvgd = (isNotEmpty(apply.getSign()) && "1".equals(apply.getSign()) ) || ApplyType.BWS.equals(apply.getApplyType())
//                    ? apply.getConsignor() : apply.getUseConsigner();//关联报关经营企业 若是一线入二线出的取委托单位
            String rltEntryRcvgd = (isNotEmpty(orderInfo.getSign()) && "1".equals(orderInfo.getSign()) )
                    ? orderInfo.getBuyerName() : orderInfo.getDeliverUnitName();//关联报关经营企业 若是一线入二线出的取委托单位
//            String customer = isNotEmpty(apply.getSign()) && "1".equals(apply.getSign())
//                    ? apply.getConsignor() : apply.getShipper();// 关联报关单收发货单位  若是一线入二线出的取委托单位
            String customer = isNotEmpty(orderInfo.getSign()) && "1".equals(orderInfo.getSign())
                    ? orderInfo.getBuyerName() : orderInfo.getOverseasPayerInfoName();// 关联报关单收发货单位  若是一线入二线出的取委托单位
//            CustomerEnterprise rltEntryRcvgdEnterprise = RpcKit
//                    .get(customerApi, CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,null, rltEntryRcvgd).getData();
            List<CustomerEnterprise> rltEntryRcvgdEnterprise = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                    .eq(CustomerEnterprise::getDepartName, rltEntryRcvgd));
//            CustomerEnterprise customerEnterprise = RpcKit
//                    .get(customerApi, CustomerApi::getEnterpriseByDepartcdOrSocialCodeOrDepartName,null, customer).getData();
            List<CustomerEnterprise> customerEnterprise = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                    .eq(CustomerEnterprise::getDepartName, customer));
            nemsInvtHead.setRltEntryBizopEtpsNm(rltEntryRcvgd);// 关联报关经营企业名称
            nemsInvtHead.setRltEntryRcvgdEtpsNm(customer);// 关联报关单收发货单位名称

            if (isNotEmpty(rltEntryRcvgdEnterprise)) {
//          关联报关经营企业信息(生产销售代码)
                nemsInvtHead.setRltEntryBizopEtpsno(rltEntryRcvgdEnterprise.get(0).getDepartcd());
                nemsInvtHead.setRltEntryBizopEtpsSccd(rltEntryRcvgdEnterprise.get(0).getSocialCode());
            }
            if (isNotEmpty(customerEnterprise)){
//             关联报关单收发货单位信息
                nemsInvtHead.setRltEntryRcvgdEtpsno(customerEnterprise.get(0).getDepartcd());
                nemsInvtHead.setRltEntryRvsngdEtpsSccd(customerEnterprise.get(0).getSocialCode());
            }
        } else if ("2".equals(nemsInvtHead.getDclcusTypecd())) { // 对应报关
            // 对应报关单申报单位信息
            nemsInvtHead.setCorrEntryDclEtpsno(nemsInvtHead.getDclEtpsno());
            nemsInvtHead.setCorrEntryDclEtpsNm(nemsInvtHead.getDclEtpsNm());
            nemsInvtHead.setCorrEntryDclEtpsSccd(nemsInvtHead.getDclEtpsSccd());

        }
        // 关联备案编号 区内：报关-空/非报关-取区外手册号 区外：取区内账册号
        nemsInvtHead.setRltputrecNo(orderInfo.getRltputrecNo());

        nemsInvtHead.setNemsInvtLists(new ArrayList<>());
        List<Integer> number = new ArrayList<>();
        number.add(1);
        list.forEach(v -> {
            NemsInvtList nemsInvtList = invoice2InvtList(v, exempt);
            int gdsseqNo = number.get(0);
            nemsInvtList.setGdsseqNo(gdsseqNo);
            nemsInvtList.setEntryGdsSeqno(gdsseqNo);
            number.set(0,++gdsseqNo);
//            if (isNotEmpty(apply.getOutZoneMode()) && "1".equals(apply.getOutZoneMode())){
//                nemsInvtList.setImportBillNo(v.getInvoiceNo());//原企业内部编号
//                if (isNotEmpty(apply.getApplyType()) && ApplyType.BWS.equals(apply.getApplyType()) && "E".equals(apply.getImSign())){
//                    nemsInvtList.setImportBillNo(v.getAwbno());//原企业内部编号
//                }
//            }
//            if ("山东太古飞机工程有限公司".equals(apply.getConsignor()) && "E".equals(apply.getImSign())){
//                nemsInvtList.setRmk(v.getMrNo());
//            }
            //危化品标志(表头备案号以T、H开头或以L开头且第6位字符为‘B’，并且清单类型不是简单加工、监管方式不是‘AAAA’时，必填)
            if ("T|H".contains(nemsInvtHead.getPutrecNo().substring(0,1)) && !"4".equals(nemsInvtHead.getInvtType())
                    && !"AAAA".equals(nemsInvtHead.getSupvModecd())){
                nemsInvtList.setClymarkcd("0");
            }

//            if ("费斯托气动有限公司".equals(apply.getConsignor()) && isNotEmpty(v.getToCountry())){
//                nemsInvtList.setNatcd(gbdq.get(v.getToCountry()) != null ? gbdq.get(v.getToCountry()).getItemKey() : v.getToCountry());
//            }

            nemsInvtHead.getNemsInvtLists().add(nemsInvtList);


        });
        nemsInvtHead.setSupvModecd(exempt.getTradeType());// 监管方式

        nemsInvtHead.setSysId(handleSysId(nemsInvtHead.getPutrecNo()));//子系统Id

        return nemsInvtHead;
    }

    /**
     * 处理子系统ID 根据手账册号
     * @param emsNo 手账册号
     * @return
     */
    private String handleSysId(String emsNo) {
        if (isNotEmpty(emsNo)){
//            if (emsNo.indexOf("L")>-1){
//                return "Z8";
//            }else if (emsNo.indexOf("T")>-1 || emsNo.indexOf("H")>-1){
//                return "Z7";
//            }else if (emsNo.indexOf("E")>-1){
                return "95";
//            }else if (emsNo.indexOf("B")>-1 || emsNo.indexOf("C")>-1){
//                return "B1";
//            }
        }
        return null;
    }

    /**
     * 7.2.1.1 转化核注单表体信息
     *
     * @param applyInvoices
     * @return
     */
    private NemsInvtList invoice2InvtList(OrderProductInfo applyInvoices, CustomsExempt exempt) {// Apply apply
        //获取配置是否替换单耗版本号
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(
                new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, "DHBBHDQ"));

        NemsInvtList nemsInvtList = new NemsInvtList();

//        nemsInvtList.setInvoiceListNo(applyInvoices.getInvoiceListNo());//赋值虚拟id
        if (isNotEmpty(exempt.getTaxWay())){
            String[] arr = exempt.getTaxWay().split("\\|");
            nemsInvtList.setLvyrlfModecd(arr[0]);
        }

        try {// 转换币制、原产国
            // 币制
//            List<DictModel> dictModels3 = sysBaseApi.getDictItems("erp_currencies,code,currency,1=1");
//            Map<String, String> dictMap3 = new HashMap<>();
//            if (isNotEmpty(dictModels3)) {
//                dictModels3.forEach(dictModel -> {
//                    dictMap3.put(dictModel.getValue(), dictModel.getText());
//                });
//            }

            // 币制
//            String shipCurrencyCode = null;
//            if (StringUtils.isNotEmpty(applyInvoices.getCustomsDeclarationCurrency())){
//                ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
//                        .eq(ErpCurrencies::getCode, applyInvoices.getCustomsDeclarationCurrency()));
//                shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
//            }
//            CustomsDict v_bzdm = bzdm.get(applyInvoices.getCurrency());
//            CustomsDict v_gbdq = gbdq.get(applyInvoices.getUla());
//            nemsInvtList.setDclCurrcd(v_bzdm != null ? v_bzdm.getItemKey() : null);
//            if(dictMap3.containsKey(applyInvoices.getCustomsDeclarationCurrency())) {
                nemsInvtList.setDclCurrcd(applyInvoices.getCustomsDeclarationCurrency());
//            }
            // 国别地区
//            List<DictModel> dictModels2 = sysBaseApi.getDictItems("erp_countries,name,code");
//            Map<String, String> dictMap2 = new HashMap<>();
//            if (isNotEmpty(dictModels2)) {
//                dictModels2.forEach(dictModel -> {
//                    dictMap2.put(dictModel.getValue(), dictModel.getText());
//                });
//            }
            List<DictModel> dictModels1 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=1");
            List<DictModel> dictModels0 = sysBaseApi.getDictItems("erp_countries,name,code,isenabled=0");
            Map<String, String> dictMap1 = new HashMap<>();
            Map<String, String> dictMap0 = new HashMap<>();
            // 将 dictModel 列表转换为字典 map
            if (isNotEmpty(dictModels1)) {
                dictModels1.forEach(dictModel -> dictMap1.put(dictModel.getText(), dictModel.getValue()));
            }
            if (isNotEmpty(dictModels0)) {
                dictModels0.forEach(dictModel -> dictMap0.put(dictModel.getText(), dictModel.getValue()));
            }
            // 使用 dictMap1 中的 value 替换 dictMap0 的 key
            Map<String, String> replacedDictMap0 = new HashMap<>();
            dictMap0.forEach((key, value) ->
                    replacedDictMap0.put(dictMap1.getOrDefault(key, key), value) // BAR-410
            );
//            nemsInvtList.setOriginCountry(v_gbdq != null ? v_gbdq.getItemKey() : applyInvoices.getUla());
            // 原产国
            if (isNotBlank(applyInvoices.getOriginCountry())) {
                if(replacedDictMap0.containsKey(applyInvoices.getOriginCountry())) {
                    nemsInvtList.setOriginCountry(replacedDictMap0.get(applyInvoices.getOriginCountry()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        nemsInvtList.setApplyNumber(Long.valueOf(applyInvoices.getOrderInfoId()));
        nemsInvtList.setGdsseqNo(applyInvoices.getItem());
        nemsInvtList.setEntryGdsSeqno(applyInvoices.getItem());
        nemsInvtList.setPutrecSeqno(applyInvoices.getInvoicesRecordItem());
        nemsInvtList.setHscode(applyInvoices.getHscode());
        nemsInvtList.setHsname(applyInvoices.getHsname());
        nemsInvtList.setHsmodel(applyInvoices.getHsmodel());
        nemsInvtList.setDclQty(applyInvoices.getShipmentQuantity());
        nemsInvtList.setDclUnitcd(applyInvoices.getLegalUnitCode());
        nemsInvtList.setDclUprcamt(applyInvoices.getShipmentUnitPrice());
        nemsInvtList.setDclTotalamt(applyInvoices.getShipmentGoodsValue());
        nemsInvtList.setNetWt(applyInvoices.getNetWeight().stripTrailingZeros());
        nemsInvtList.setGdsMtno(applyInvoices.getPn());
        nemsInvtList.setSupvModecd(applyInvoices.getMonitorcondition());
        nemsInvtList.setCiqCode(applyInvoices.getCiqCode());
        nemsInvtList.setCiqName(applyInvoices.getCiqName());
        nemsInvtList.setLawfUnitcd(applyInvoices.getLegalUnitCode());
        nemsInvtList.setSecdlawfUnitcd(applyInvoices.getSecondUnitCode());
        nemsInvtList.setGoodsId(applyInvoices.getMegerSequence() != null ? applyInvoices.getMegerSequence()
                : applyInvoices.getSequence().toString());
        nemsInvtList.setHstype(applyInvoices.getSupervisionMode());
        if(sysConfigs.size()>0&&isNotBlank(applyInvoices.getRecordNumber())){
            String value = sysConfigs.get(0).getConfigValue();
            List<String> valueList = Arrays.asList(value.split(","));
            if(valueList.contains(applyInvoices.getRecordNumber().substring(0,1))){
                nemsInvtList.setUcnsVerno(String.valueOf(nemsInvtList.getPutrecSeqno()));
            }
        }else {
            nemsInvtList.setUcnsVerno(applyInvoices.getUcnsverno());
        }

        nemsInvtList.setPacks(applyInvoices.getShipmentPackagesNumbers());
        if (isNotEmpty(nemsInvtList.getLawfUnitcd())) {
            if (isNotEmpty(applyInvoices.getLegalQuantity()) && BigDecimal.ZERO.compareTo(applyInvoices.getLegalQuantity()) >0){
                nemsInvtList.setLawfQty(applyInvoices.getLegalQuantity());//当委托表体存在发动数量时默认取委托表体
            }else {
                nemsInvtList.setLawfQty(nemsInvtList.getDclQty());//默认取成交数量
                if (("035".equals(nemsInvtList.getLawfUnitcd()) || "036".equals(nemsInvtList.getLawfUnitcd()))) {
                    //&& !nemsInvtList.getDclUnitcd().equals(nemsInvtList.getLawfUnitcd())
                    if (new BigDecimal("0.001").compareTo(nemsInvtList.getNetWt()) > 0) {
                        if ("036".equals(nemsInvtList.getLawfUnitcd())){
                            nemsInvtList.setLawfQty(new BigDecimal("1"));
                        }else {
                            nemsInvtList.setLawfQty(new BigDecimal("0.001"));
                        }
                        nemsInvtList.setNetWt(new BigDecimal("0.001"));
                    } else {
                        if ("036".equals(nemsInvtList.getLawfUnitcd())){
                            nemsInvtList.setLawfQty(nemsInvtList.getNetWt().multiply(new BigDecimal("1000")));
                        }else {
                            nemsInvtList.setLawfQty(nemsInvtList.getNetWt());
                        }
                    }
                } else {
                    nemsInvtList.setLawfQty(handleUnitConversion(nemsInvtList.getDclUnitcd(),nemsInvtList.getLawfUnitcd(),
                            nemsInvtList.getDclQty()));
                }
            }
        }
        if (isNotEmpty(nemsInvtList.getSecdlawfUnitcd())) {
            if (isNotEmpty(applyInvoices.getSecondNumbers()) && BigDecimal.ZERO.compareTo(applyInvoices.getSecondNumbers()) >0){
                nemsInvtList.setSecdLawfQty(applyInvoices.getSecondNumbers());//当委托表体存在发动数量时默认取委托表体
            }else {
                nemsInvtList.setSecdLawfQty(nemsInvtList.getDclQty());//默认取成交数量
                if (("035".equals(nemsInvtList.getSecdlawfUnitcd()) || "036".equals(nemsInvtList.getSecdlawfUnitcd()))) {
                    //&& !nemsInvtList.getDclUnitcd().equals(nemsInvtList.getSecdlawfUnitcd())
                    if (new BigDecimal("0.001").compareTo(nemsInvtList.getNetWt()) > 0) {
                        if ("036".equals(nemsInvtList.getSecdlawfUnitcd())) {
                            nemsInvtList.setSecdLawfQty(new BigDecimal("1"));
                        } else {
                            nemsInvtList.setSecdLawfQty(new BigDecimal("0.001"));
                        }
                        nemsInvtList.setNetWt(new BigDecimal("0.001"));

                    } else {
                        if ("036".equals(nemsInvtList.getLawfUnitcd())){
                            nemsInvtList.setSecdLawfQty(nemsInvtList.getNetWt().multiply(new BigDecimal("1000")));
                        }else {
                            nemsInvtList.setSecdLawfQty(nemsInvtList.getNetWt());
                        }
                    }
                } else{
                    nemsInvtList.setSecdLawfQty(handleUnitConversion(nemsInvtList.getDclUnitcd(),
                            nemsInvtList.getSecdlawfUnitcd(),nemsInvtList.getDclQty()));
                }
            }
        }
        return nemsInvtList;
    }

    /**
     * 处理单位转换
     * @param dclUnitcd 申报单位
     * @param legalUnit 法定单位
     * @param dclQty 数量
     * @return
     */
    private BigDecimal handleUnitConversion(String dclUnitcd,String legalUnit,BigDecimal dclQty){
        BigDecimal legaQty = dclQty;
        if ("007".equals(dclUnitcd) && "054".equals(legalUnit)){//个→千个
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        }else if ("054".equals(dclUnitcd) && "007".equals(legalUnit)){//千个→个
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        } else if ("007".equals(dclUnitcd) && "044".equals(legalUnit)){//个→百片
            legaQty = dclQty.multiply(new BigDecimal("0.01"));
        } else if ("044".equals(dclUnitcd) && "007".equals(legalUnit)){//百片→个
            legaQty = dclQty.multiply(new BigDecimal("100"));
        }else if ("011".equals(dclUnitcd) && "054".equals(legalUnit)){//件→千个
            legaQty = dclQty.multiply(new BigDecimal("0.001"));
        }else if ("054".equals(dclUnitcd) && "011".equals(legalUnit)){//千个→件
            legaQty = dclQty.multiply(new BigDecimal("1000"));
        }
        return legaQty;
    }

    /**
     * 处理数据（检查数量，合并，税则赋值，分票）
     * @param orderInfo
     * @param exempt
     * @return
     * @throws
     */
    private Map<String, List<OrderProductInfo>> generalDraft(OrderInfo orderInfo, CustomsExempt exempt, List<String> hscodeList) throws RuntimeException {
        //处理发票信息
        orderInfo.getProductList().forEach(v->{
            if (isNotEmpty(v.getShipmentGoodsValue())){
                v.setShipmentGoodsValue(v.getShipmentGoodsValue().setScale(2, RoundingMode.HALF_UP).stripTrailingZeros());
            }
        });
        // 4.合并委托明细信息
        // 处理申报要素的合并、拼接明细合同号（费斯托）
        List<OrderProductInfo> mergedList = mergeInvoiceLists(orderInfo, getMergeRule(orderInfo), getSpecifications(hscodeList));

        // 5.带入税则监管条件
        setTarrifInfo(mergedList, getTariff(hscodeList));
        ApplyConfig ac = orderInfo.getApplyConfigMap().get(orderInfo.getConfigKey(orderInfo.getSupervisionMode()));
        Map<String, List<OrderProductInfo>> splitMap = splitWithPrinciple(ac, mergedList);

        return splitMap;
    }

    /**
     * 处理涉检品集中分票
     * @param applyInvoicesList
     * @returnw
     */
    private List<OrderProductInfo> handleFocusInspection(List<OrderProductInfo> applyInvoicesList){
        List<OrderProductInfo> spec_A = new ArrayList<>();
        List<OrderProductInfo> spec_M = new ArrayList<>();
        List<OrderProductInfo> spec_L = new ArrayList<>();
        List<OrderProductInfo> spec_P = new ArrayList<>();
        List<OrderProductInfo> spec_R = new ArrayList<>();
        List<OrderProductInfo> spec_DgFlag = new ArrayList<>();
        List<OrderProductInfo> invoices = new ArrayList<>();

        applyInvoicesList.forEach(v->{
            //排列顺序（A\M\L\P\R\危险品\非涉检）
            if (isNotEmpty(v.getMonitorcondition())){
                if (v.getMonitorcondition().contains("A")){
                    spec_A.add(v);
                }else if (v.getMonitorcondition().contains("M")){
                    spec_M.add(v);
                }else if (v.getMonitorcondition().contains("L")){
                    spec_L.add(v);
                }else if (v.getMonitorcondition().contains("P")){
                    spec_P.add(v);
                }else if (v.getMonitorcondition().contains("R")){
                    spec_R.add(v);
                }
            }
            if (v.getDgFlag() != null && v.getDgFlag() && (isEmpty(v.getMonitorcondition())
                    || !(isNotEmpty(v.getMonitorcondition()) && (v.getMonitorcondition().contains("A")
                    || v.getMonitorcondition().contains("L") || v.getMonitorcondition().contains("P")
                    || v.getMonitorcondition().contains("M") || v.getMonitorcondition().contains("R"))))){
                spec_DgFlag.add(v);
            }else if (!(isNotEmpty(v.getMonitorcondition()) && (v.getMonitorcondition().contains("A")
                    || v.getMonitorcondition().contains("L") || v.getMonitorcondition().contains("P")
                    || v.getMonitorcondition().contains("M") || v.getMonitorcondition().contains("R")))
                    && !(v.getDgFlag() != null && v.getDgFlag())){
                invoices.add(v);
            }

//			if ((isNotEmpty(v.getSupvModecd()) && (v.getSupvModecd().contains("A")
//					|| v.getSupvModecd().contains("L") || v.getSupvModecd().contains("P")
//					|| v.getSupvModecd().contains("M") || v.getSupvModecd().contains("R")))
//					|| (v.getDgFlag() != null && v.getDgFlag())) {
//				specList.add(v);
//			}else {
//				invoices.add(v);
//			}

        });
        List<OrderProductInfo> invoicesList = new ArrayList<>();
        invoicesList.addAll(spec_A);
        invoicesList.addAll(spec_M);
        invoicesList.addAll(spec_L);
        invoicesList.addAll(spec_P);
        invoicesList.addAll(spec_R);
        invoicesList.addAll(spec_DgFlag);
        invoicesList.addAll(invoices);

        return invoicesList;

    }

    /**
     * 6.按分票原则拆分委托商品
     *
     * @param applyConfig
     * @param list
     * @return Map<" Null_1 ", list>
     */
    private Map<String, List<OrderProductInfo>> splitWithPrinciple(ApplyConfig applyConfig, List<OrderProductInfo> list)
            throws RuntimeException{

        final int MAX_SIZE = applyConfig != null && applyConfig.getVotes()!=null ? applyConfig.getVotes() : 50;// 单票分票数

        // 生成分票关键字 并暂存分票
        Map<String, List<OrderProductInfo>> zcListMap = new LinkedHashMap<>();
        list.forEach(v -> {
            // 关键字 = 分票规则：货物类型
            String key = v.getHawb() + "_"; // 就是表头的提运单号！！deliveryNumbers

            List<OrderProductInfo> subList = zcListMap.get(key);
            if(subList==null){
                zcListMap.put(key.toString(), subList = new LinkedList<>());
            }
            subList.add(v);
        });

        // 按分票关键字排序
        Map<String, List<OrderProductInfo>> allMap = new LinkedHashMap<>();
        zcListMap.forEach((k, subList)-> {
            allMap.putAll(splitBySize(MAX_SIZE, 0, k, subList, null));
        });

        return allMap;
    }
    /**
     * 6.1 费斯托自定义分票
     * (hsType: 扩展分票规则,用于 山太飞机注册号/费斯托铁路 等用户自定义的分票)
     *
     * @param ac
     * @param list
     * @return
     */
    private Map<String, List<OrderProductInfo>> splitWithFst(ApplyConfig ac, List<OrderProductInfo> list) {
        // TODO
        final int MAX_SIZE = ac != null ? ac.getVotes() : 50;// 单票分票数

        // 生成分票关键字 并暂存分票
        Map<String, List<OrderProductInfo>> zcListMap = new LinkedHashMap<>();
        list.forEach(v -> {
            // 关键字 = 分票规则：货物类型
            String key = (isNotEmpty(v.getType()) ? v.getType() : "") + "_";

            List<OrderProductInfo> subList = zcListMap.get(key);
            if(subList==null){
                zcListMap.put(key.toString(), subList = new LinkedList<>());
            }
            subList.add(v);
        });

        // 按分票关键字排序
        Map<String, List<OrderProductInfo>> allMap = new LinkedHashMap<>();
        zcListMap.forEach((k, subList)-> {
            List<OrderProductInfo> KGDL = new LinkedList<>();
            List<OrderProductInfo> XCQG = new LinkedList<>();
            List<OrderProductInfo> YZ = new LinkedList<>();
            List<OrderProductInfo> LL = new LinkedList<>();
            List<OrderProductInfo> ACID = new LinkedList<>();//核酸
            List<OrderProductInfo> ANIT = new LinkedList<>();//反倾销
            List<OrderProductInfo> LYWX = new LinkedList<>();

            // 自定义法检类型 生成分票
            subList.forEach(v -> {
                if (isNotEmpty(v.getHsCodeSign())  && !v.getHsCodeSign().contains("0")){
                    if (v.getHsCodeSign().contains("1")){
                        ACID.add(v);
                        return;
                    }
                    if (v.getHsCodeSign().contains("2")){
                        YZ.add(v);
                        return;
                    }
                    if (v.getHsCodeSign().contains("3")){
                        LYWX.add(v);
                        return;
                    }
                    if (v.getHsCodeSign().contains("5")){
                        ANIT.add(v);
                        return;
                    }

                }
                int lk = 0;
                if( v.getHsType()==null){//无法检普通项
                    if(is3C(v.getMonitorcondition()))
                        LL.add(lk++, v);
                    else
                        LL.add(v);

                }else {
                    switch (v.getHsType()){
                        case "KGDL" :
                            KGDL.add(v);
                            break;
                        case "XCQG" :
                            XCQG.add(v);
                            break;

//						case "YZ" :
//							YZ.add(v);
//							break;
                        default:
                            if(is3C(v.getMonitorcondition()))
                                LL.add(lk++, v);
                            else
                                LL.add(v);
                    }
                }
            });
            //处理反倾销提前
            ANIT.addAll(LL);
            LL.clear();
            LL.addAll(ANIT);
            // 排序 KGDL=>XCQG=>L=>其它=>YZ
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "KGDL_", KGDL, LL));
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "XCQG_", XCQG, LL));
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "L_", LL, null));
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "ACID_", ACID, null));
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "LYWX_", LYWX, null));
            allMap.putAll(splitBySize(MAX_SIZE, allMap.size(), k + "YZ_", YZ, null));
        });

        return allMap;
    }
    /**
     * 按指定大小分票
     *
     * @param size
     * @param tag
     * @param key
     * @param list
     * @param supply
     * @return
     */
    private Map<String, List<OrderProductInfo>> splitBySize(int size, int tag, String key, List<OrderProductInfo> list, List<OrderProductInfo> supply){
        Map<String, List<OrderProductInfo>> map = new LinkedHashMap<>();
        int p = tag;
        while(list!=null && list.size()>=size){
            List<OrderProductInfo> l = new LinkedList<>();
            while(l.size()<size) {
                l.add(list.remove(0));
            }
            map.put(key + p++, l);
        }

        if(list.size()>0){
            List<OrderProductInfo> l = new LinkedList<>(list);
            while(l.size()<size && supply!=null && supply.size()>0){
                l.add(supply.remove(0));
            }
            map.put(key + p, l);
            while(list.size()>0)
                list.remove(0);
        }

        return map;
    }

    /**
     * 3C 判断（费斯托）
     * 海关监管条件 + 检验检疫标志 (含 'L'标志)
     * @param supvModecd
     * @return
     */
    private boolean is3C(String supvModecd){
        return isEmpty(supvModecd) ? false : supvModecd.contains("L");
    }

    /**
     * 处理危险品税号和危险品的货物属性
     * @param list
     */
    private void setFlagAndGoodsAttr(List<OrderProductInfo> list){
        Map<String, ErpCiq> ciqMap = new HashMap<>();
        List<String> codeTsAndCiqCode = new ArrayList<>();
        list.forEach(v->{
            String identifing = new StringBuilder(isNotEmpty(v.getHscode()) ? v.getHscode() : "").append("|")
                    .append(isNotEmpty(v.getCiqCode()) ? v.getCiqCode() : "").toString();
            ciqMap.put(identifing,null);
            codeTsAndCiqCode.add(identifing);
        });
        List<ErpCiq> customsCiqs = erpCiqMapper.listCiqByCodeTsAndCiqCode(codeTsAndCiqCode);
        if (isNotEmpty(customsCiqs)){
            customsCiqs.forEach(v->{
                String identifing = new StringBuilder(v.getCodeTs()).append("|").append(v.getCiqCode()).toString();
                ciqMap.put(identifing,v);
            });
            list.forEach(v->{
                String identifing = new StringBuilder(isNotEmpty(v.getHscode()) ? v.getHscode() : "").append("|").append(v.getCiqCode()).toString();
                ErpCiq customsCiq = ciqMap.get(identifing);
                if (customsCiq != null){
                    v.setDgFlag("1".equals(customsCiq.getFlag()));
                    v.setGoodsAttr(customsCiq.getGoodsAttr());
                }
            });
        }


    }
    /**
     * 5.带入税则监管条件
     *
     * @param list
     * @param map
     * @throws RuntimeException
     */
    private void setTarrifInfo(List<OrderProductInfo> list, Map<String, ErpHscodes> map) throws RuntimeException {

        StringBuilder err = new StringBuilder();
        if (map == null) {
//            throw new YmException(RspCode.SVR_ERR, "请检查商品信息的税号是否符合规范");
            return;
        }

        list.forEach(v -> {
            //处理税则信息赋值
            ErpHscodes customsTariff = map.get(v.getHscode());
            if (customsTariff == null) {
                if (err.length() == 0) {
                    err.append("下列税则信息未找到: \r\n");
                }
                err.append(v.getHscode()).append(", ");
                return;
            }
			/*
			处理法一法二计量单位
			 */
            if (isNotEmpty(v.getLegalUnitCode())){
                v.setLegalUnitCode(v.getLegalUnitCode());
            }else {
                v.setLegalUnitCode(customsTariff.getQtyunit());
            }
            if (isNotEmpty(v.getSecondUnitCode())){
                v.setSecondUnitCode(v.getSecondUnitCode());
            }else {
                v.setSecondUnitCode(customsTariff.getQtcunit());
            }
            // 3C
            // 监管条件 + " " + 检验检疫
            String supvModecd = null;
            if (isNotEmpty(customsTariff.getMonitorcondition()) && isNotEmpty(customsTariff.getIaqcategory())) {
                supvModecd = customsTariff.getMonitorcondition() + " " + customsTariff.getIaqcategory();

            }else if(isNotEmpty(customsTariff.getMonitorcondition())) {
                supvModecd = customsTariff.getMonitorcondition();

            }else{
                supvModecd = customsTariff.getIaqcategory();
            }

            v.setMonitorcondition(supvModecd);
        });
    }
    /**
     * 取税则
     *
     * @param hscodeList
     * @return
     */
    private Map<String, ErpHscodes> getTariff(List<String> hscodeList) {
        // 获取税则信息，若税则信息为空则  做缓存
        List<ErpHscodes> customsTariffs = erpHscodesMapper.selectList(new QueryWrapper<ErpHscodes>().lambda()
                .in(ErpHscodes::getHscode, hscodeList).orderByDesc(ErpHscodes::getHscode));
        if (customsTariffs == null || customsTariffs.isEmpty()) {
            return null;
        }

        Map<String, ErpHscodes> customsTariffMap = new HashMap<>();
        customsTariffs.forEach(v -> {
            customsTariffMap.put(v.getHscode(), v);
        });

        return customsTariffMap;
    }

    /**
     * <pre>
     * 4.合并委托商品项
     * (处理申报要素的合并)
     *
     * &#64;param apply 委托单信息
     * &#64;param rules 合并规则
     * &#64;param specMap 申报规范Map
     * &#64;return
     * &#64;throws YmException
     * </pre>
     */
    private List<OrderProductInfo> mergeInvoiceLists(OrderInfo orderInfo, String[] rules, Map<String, ErpHscodes> specMap)
            throws RuntimeException {

        if (rules == null || rules.length == 0) {// 无归并
            return orderInfo.getProductList();
        }

        List<Method> methodList = new ArrayList<>();
        StringBuilder err = null;
        for (String fn : rules) {
            try {
                String name = "get" + fn.replaceFirst("(^.).*", "$1").toUpperCase() + fn.replaceFirst("^.(.*)", "$1");
                Method m = OrderProductInfo.class.getDeclaredMethod(name);

                methodList.add(m);
            } catch (NoSuchMethodException | SecurityException e) {
                if (err == null) {
                    err = new StringBuilder("下列合并规则错误:\r\n");
                }
                err.append(fn).append(",");
            }
        }

        if (err != null) {
            throw new RuntimeException(err.toString());
        }

        if (methodList.size() == 0) {// FIXME
            return orderInfo.getProductList();
        }

        Map<String, OrderProductInfo> map = new LinkedHashMap<>();
        Map<String, Boolean> okMap = new LinkedHashMap<>();// 标记申报要素已处理好
        for (OrderProductInfo orderProduct : orderInfo.getProductList()) {
            // 生成合并关键字
            StringBuilder key = new StringBuilder();
            for (Method m : methodList) {
                try {
                    Object obj = m.invoke(orderProduct);
                    key.append("_").append(isEmpty(obj) ? "Null" : obj.toString());
                } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }
            }
            if (isNotBlank(orderProduct.getHsmodel())){
                key.append("_").append(orderProduct.getHsmodel().charAt(0));
            }


            //合并数据
            OrderProductInfo merged = map.get(key.toString());
            if (merged == null) {// 新建 临时 草单表体
                merged = copyValue(orderProduct, new OrderProductInfo());//FIXME
                merged.setMegerSequence(orderProduct.getSequence().toString());

                map.put(key.toString(), merged);

            } else {
                // 不同合同号不会合并
                if (!merged.getHscode().equals(orderProduct.getHscode()))
                    throw new RuntimeException("不同申报税号不可合并");
                // 避免重复件号及多次处理申报要素
                if (!Boolean.TRUE.equals(okMap.get(key.toString())) && merged.getPn()!=null && !merged.getPn().equals(orderProduct.getPn())) {
                    if (specMap.get(merged.getHscode()) == null){
                        throw new RuntimeException(String.format("未找到对应的申报规范，税号：%s",merged.getHscode()));
                    }
                    // 申报要素 规格型号加...等
                    merged.setHsmodel( redoKeyElements( specMap.get(merged.getHscode()).getCustbasic(), merged.getHsmodel()) );
                    okMap.put(key.toString(), true);
                }
                // 处理合并数据
                dealMergeData(merged, orderProduct);
            }
        }

        List<OrderProductInfo> list = new ArrayList<>();
        list.addAll(map.values());

        return list;
    }
    /**
     * 处理合并数据时需要处理的数值
     *
     * @param merged   合并数据
     * @param invoices 被合并数据
     * @return
     * @throws RuntimeException
     */
    private OrderProductInfo dealMergeData(OrderProductInfo merged, OrderProductInfo invoices) throws RuntimeException {
        if (merged != null) {
            // 数量
            merged.setShipmentQuantity(merged.getShipmentQuantity().add(invoices.getShipmentQuantity()));
            merged.setDecQty(merged.getDecQty().add(invoices.getDecQty()));
            // 总价
            merged.setShipmentGoodsValue(merged.getShipmentGoodsValue().add(invoices.getShipmentGoodsValue()));
            // 净重
            merged.setNetWeight(merged.getNetWeight().add(invoices.getNetWeight()));
            // 清单序号
            merged.setMegerSequence(String.format("%s,%s", merged.getMegerSequence(), invoices.getSequence()));

            // 单价 分票后计算
            merged.setShipmentUnitPrice(merged.getShipmentGoodsValue().divide(merged.getDecQty(), 4, RoundingMode.HALF_UP));
            if(isNotEmpty(merged.getContract()) && isNotEmpty(invoices.getContract())
                    && !merged.getContract().contains(invoices.getContract())){
                String contrcat =String.format("%s,%s",merged.getContract(),invoices.getContract());
                merged.setContract(contrcat);
            }else if (isNotEmpty(invoices.getContract()) && isEmpty(merged.getContract())){
                merged.setContract(invoices.getContract());
            }
            //法定数量
            merged.setLegalQuantity(merged.getLegalQuantity() != null ? merged.getLegalQuantity()
                    .add(invoices.getLegalQuantity() != null ? invoices.getLegalQuantity() : BigDecimal.ZERO) : invoices.getLegalQuantity());
            merged.setSecondNumbers(merged.getSecondNumbers() != null ? merged.getSecondNumbers()
                    .add(invoices.getSecondNumbers() != null ? invoices.getSecondNumbers() : BigDecimal.ZERO) : invoices.getSecondNumbers());
        } else {
            merged = invoices;
        }

        return merged;
    }
    /**
     * <pre>
     * 重组申报要素
     * （假定:相同税号和申报规范）
     *
     * &#64;param reqs 申报要求(规范)
     * &#64;param keys 申报要素
     * &#64;param mergedKeys 需要合并的数据的申报要素
     * &#64;return
     * </pre>
     *
     * @throws RuntimeException
     */
//	 * &#64;param mode 型号 , String mode
    private static String redoKeyElements(String reqs, String keys) throws RuntimeException {
//	private static String redoKeyElements(String reqs, String keys,String mergedKeys) throws YmException {

        String keyEle = "";

        if (reqs.contains("尺寸") || reqs.contains("型号")) {
            String[] reqArr = reqs.split(";");
            String[] keyArr = keys.split("\\|", -1);
//			String[] mergedKeyArr = mergedKeys.split("\\|", -1);

            if (keyArr.length > reqArr.length)
                throw new RuntimeException("申报要素项数 > 申报规范项数");

            // 重新组装申报要素
            StringBuilder sb = new StringBuilder();
            for (int index = 0; index < reqArr.length; index++) {
                // 判断申报规范中是否存在尺寸或型号
                sb.append(keyArr[index]);
                // 判断是否需要加“等” 2023-12-27 所有项都判断加等
                if (!keyArr[index].contains("等") && (reqArr[index].contains("尺寸") || reqArr[index].contains("型号"))) {
                    sb.append("等");
                }
//				if (!keyArr[index].contains("等") && !keyArr[index].equals(mergedKeyArr[index])) {
//					sb.append("等");
//				}
                if (index + 1 < keyArr.length)
                    sb.append("|");
                else
                    break;
            }

            keyEle = sb.toString();
        } else
            keyEle = keys;

        return keyEle;
    }
    /**
     * 取申报规范
     *
     * @param hscodeList
     * @return
     */
    private Map<String, ErpHscodes> getSpecifications(List<String> hscodeList) {
        List<ErpHscodes> specifications = erpHscodesMapper.selectList(new QueryWrapper<ErpHscodes>().lambda()
                .in(ErpHscodes::getHscode, hscodeList).orderByDesc(ErpHscodes::getHscode));
        if (isNotEmpty(specifications)) {
            return specifications.stream().collect(Collectors.toMap(ErpHscodes::getHscode, v -> v));
        }
        return null;
    }
    /**
     * 取委托的合并规则
     *
     * @param orderInfo
     * @return
     */
    private String[] getMergeRule(OrderInfo orderInfo) {
        String[] rules = null;

        String tradeType = orderInfo.getSupervisionMode();
        String identifing = orderInfo.getConfigKey(tradeType);

        ApplyConfig applyConfig = orderInfo.getApplyConfigMap().get(identifing);
        rules = applyConfig != null && isNotEmpty(applyConfig.getMergeRule())
                ? applyConfig.getMergeRule().split("\\|")
                : null;

        return rules;
    }
    /**
     * <pre>
     * 1. 检查委托商品数据（待完善）
     *    收集第一账册备案序号及货号
     *    多手帐册提前分多Apply
     *
     *    &#64;param apply
     *    &#64;return
     *    &#64;throws YmException
     * </pre>
     */
    private void checkInvoiceLists(OrderInfo orderInfo, List<String> hscodeList) throws RuntimeException {

        final StringBuffer errMsg = new StringBuffer();

        // 遍历清单，收集手账册备案信息
        for (OrderProductInfo v : orderInfo.getProductList()) {
            if (null == v.getSequence()) {
                errMsg.append("\r\n件号:").append(v.getPn()).append(" 合并序号不能为空!");
            }
            if (v.getNetWeight() == null) {
                errMsg.append("\n件号:").append(v.getPn()).append(" 发票净重不允许为空");
            }
            if (isNotEmpty(v.getHscode())) {// 收集申报税号
                if(hscodeList!=null && !hscodeList.contains(v.getHscode()))
                    hscodeList.add(v.getHscode());
            }
        }

        if (errMsg != null && errMsg.length() > 0) {
            throw new RuntimeException(errMsg.toString());
        }
        if (isEmpty(hscodeList)) {
            throw new RuntimeException("商品项无税号信息！");
        }
    }

    /**
     * 配置错误
     * @param exempt 配置信息
     * @param tradeType 监管方式
     * @param emsNo 手帐册号
     * @throws RuntimeException
     */
    private void error(CustomsExempt exempt, String tradeType, String emsNo) throws RuntimeException {
        if (exempt == null) {
            if (isNotEmpty(emsNo)) {
                throw new RuntimeException("无对应配置：监管方式|手账册号;" + tradeType + "|" + emsNo);
            } else {
                throw new RuntimeException("无对应配置：监管方式(无手账册号);" + tradeType);
            }
        }
    }
    /**
     * 复制属性
     * 支持List、Map、自定义对象属性的完全复制
     *
     * @param <T1>
     * @param <T2>
     * @param from
     * @param to
     * @return
     */
    public static <T1, T2> T2 copyValue(T1 from, T2 to) {
        if(from==null || to ==null) {
            return null;
        }

        try {
            if(List.class.isAssignableFrom(from.getClass())) {
                if(List.class.isAssignableFrom(to.getClass())) {
                    copyList((List)from, (List)to);
                }else {
                    log.debug("List声明类型不一致" + from.getClass() + " <=> " + to.getClass() );
                }

            }else if(Map.class.isAssignableFrom(from.getClass())) {
                if(Map.class.isAssignableFrom(to.getClass())) {
                    copyMap((Map)from, (Map)to);
                }else {
                    log.debug("Map声明类型不一致" + from.getClass() + " <=> " + to.getClass() );
                }

            }else if(isBaseDataType(to.getClass())) {
                log.debug("目标类型不能赋值" + to.getClass() );

            }else {// 对象处理
                Map<String,Field> fromMap = getFields(from.getClass());
                Map<String,Field> toMap = getFields(to.getClass());

                for(String k : toMap.keySet()) {
                    if(!fromMap.containsKey(k)) {
                        log.debug(from.getClass() + " 无属性: " + k);
                        continue;
                    }

                    Field ff = fromMap.get(k);
                    Field tf = toMap.get(k);
                    if(!tf.getType().equals(ff.getType())) {
                        log.debug(from.getClass() + " 属性: " + k + " 声明类型不一致");
                        continue;
                    }

                    boolean a = tf.isAccessible();
                    boolean b = ff.isAccessible();
                    try {
                        tf.setAccessible(true);
                        ff.setAccessible(true);

                        if( isBaseDataType(tf.getType())) {
                            tf.setAccessible(true);
                            ff.setAccessible(true);
                            tf.set(to, ff.get(from));

                        }else {
                            Object fObj = ff.get(from);
//							boolean isArray = ff.getType().isArray();
//							Type t = ff.getType();
//							Class oc = fObj.getClass();
                            if(fObj!=null) {
                                Object tObj = fObj.getClass().newInstance();
                                tf.set(to, copyValue(fObj, tObj));
                            }
                        }

                    }catch(Exception e) {
                        e.printStackTrace();
                    }finally {
                        tf.setAccessible(a);
                        ff.setAccessible(b);
                    }

                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);//不可抛出
        }

        return to;
    }
    /**
     * 获取属性列表
     *
     * @param t
     * @return
     */
    private static Map<String, Field> getFields(Class<?> t) {
        Map<String, Field> map = new HashMap<>();
        // 循环获取要转换的字段,包括父类的字段
        for (Class castClass = t;
             castClass != null && !Object.class.equals(castClass);
             castClass = castClass.getSuperclass()) {
//			if (castClass.getPackage().getPackage("sun") != null) {
            if (hasPackage(castClass.getPackage(),"sun")) {
                continue;
            }
            for(Field f : castClass.getDeclaredFields()) {
                if( !Modifier.isFinal(f.getModifiers()) &&
                        !Modifier.isNative(f.getModifiers()) &&
                        !Modifier.isVolatile(f.getModifiers()) &&
                        !map.containsKey(f.getName())) {
                    map.put(f.getName(), f);
                }
            }
        }

        return map;
    }
    /**
     * 是否包含子包
     *
     * @param pkg
     * @param subPkg
     * @return
     */
    public static boolean hasPackage(Package pkg, String subPkg){
        return pkg==null || subPkg==null ? false : pkg.getName().matches("^(.*\\.)?" + subPkg + "(\\..*)?$");
    }
    /**
     * List复制
     *
     * @param fList
     * @param tList
     * @throws Exception
     */
    private static void copyList(List fList, List tList) throws Exception {
        Boolean isBaseType = null;
        for(Object x : fList){
            if(isBaseType==null) {
                isBaseType = isBaseDataType(x.getClass());
            }
            if(isBaseType) {
                break;
            }
            tList.add(
                    copyValue(x,  x.getClass().newInstance()));
        }

        if(isBaseType!=null && isBaseType) {
            tList.addAll(fList);
        }
    }

    /**
     * Map复制
     *
     * @param fMap
     * @param tMap
     * @throws Exception
     */
    private static void copyMap(Map fMap, Map tMap) throws Exception {
        if (!fMap.isEmpty()) {
            Boolean isBaseType = null;
            for (Object key : fMap.keySet()) {
                Object v = fMap.get(key);
                if (isBaseType == null) {
                    isBaseType = isBaseDataType(v.getClass());
                    isBaseType &= isBaseDataType(key.getClass());
                }
                if (isBaseType) {
                    break;
                }
                // TODO key 非简单类型
                tMap.put(key, copyValue(fMap.get(key), v.getClass().newInstance()));
            }

            if (isBaseType!=null && isBaseType) {
                tMap.putAll(fMap);
            }
        }
    }
    /**
     * 判断一个类是否为基本数据类型。
     *
     * @param clazz
     *            要判断的类。
     * @return true 表示为基本数据类型。
     */
    private static boolean isBaseDataType(Class clazz) throws Exception {
        return (clazz.isPrimitive() || clazz.equals(String.class) || clazz.equals(Integer.class) ||
                clazz.equals(Byte.class) || clazz.equals(Long.class) || clazz.equals(Double.class) ||
                clazz.equals(Float.class) || clazz.equals(Character.class) || clazz.equals(Short.class) ||
                clazz.equals(BigDecimal.class) || clazz.equals(BigInteger.class) || clazz.equals(Boolean.class) ||
                clazz.equals(Date.class) || Enum.class.isAssignableFrom(clazz));
    }

    private void generateDecHeadByOrder(OrderInfo orderInfo, DecHead decHead, HttpServletRequest request){
        Long tenantId = Long.valueOf(TenantContext.getTenant());;
        /*
         * 境内收发货人-生产销售单位 对应 委托方；境外收发货人 对应境外付款方、供应商；申报企业对应报关行；
         * 2023/11/1 13:29@ZHANGCHAO
         */
        String buyerName;
        Commissioner commissioner = commissionerMapper.selectById(orderInfo.getBuyer());
        if (isNotEmpty(commissioner)) {
            buyerName = commissioner.getCommissionerFullName();
        } else {
            buyerName = commonService.getTenantName(request);
        }
        List<CustomerEnterprise> customerEnterpriseList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, buyerName));

        String overseasPayerInfoId = "";
        if (isNotBlank(orderInfo.getOverseasPayerInfoId())) {
            OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
            if (isNotEmpty(overseasPayerInfo)) {
                overseasPayerInfoId = overseasPayerInfo.getOverseasPayerName();
            } else {
                DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(orderInfo.getOverseasPayerInfoId());
                if (isNotEmpty(domesticSuppliersInfo)) {
                    overseasPayerInfoId = domesticSuppliersInfo.getSuppliersFullName();
                } else {
                    overseasPayerInfoId = orderInfo.getOverseasPayerInfoId();
                }
            }
        }
        List<CustomerEnterprise> overseasPayerList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, overseasPayerInfoId));
        String customsBrokerId = "";
        if (isNotBlank(orderInfo.getCustomsBrokerId())) {
            CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoMapper.selectById(orderInfo.getCustomsBrokerId());
            if (isNotEmpty(customsBrokerInfo)) {
                customsBrokerId = customsBrokerInfo.getCustomsBrokerName();
            } else {
                customsBrokerId = orderInfo.getCustomsBrokerId();
            }
        }
        List<CustomerEnterprise> customsBrokerList = customerEnterpriseMapper.selectList(new LambdaQueryWrapper<CustomerEnterprise>()
                .eq(CustomerEnterprise::getDepartName, customsBrokerId));

        //orderInfo
        if (isNotEmpty(customerEnterpriseList)) {
            decHead.setOptUnitSocialCode(customerEnterpriseList.get(0).getSocialCode()); // 境内收发货人 - 18位社会信用代码
            decHead.setOptUnitId(customerEnterpriseList.get(0).getDepartcd()); // 境内收发货人 - 10位海关代码
            decHead.setTradeCiqCode(customerEnterpriseList.get(0).getCiqCode()); // 境内收发货人 - 10位检验检疫编码
            decHead.setOptUnitName(buyerName);

            decHead.setDeliverUnitSocialCode(customerEnterpriseList.get(0).getSocialCode()); // 消费使用单位/生产销售单位 - 18位社会信用代码
            decHead.setDeliverUnit(customerEnterpriseList.get(0).getDepartcd()); // 消费使用单位/生产销售单位 - 10位海关代码
            decHead.setOwnerCiqCode(customerEnterpriseList.get(0).getCiqCode()); // 消费使用单位/生产销售单位 - 10位检验检疫编码
            decHead.setDeliverUnitName(buyerName);
        }
        // 2024/4/8 16:20@ZHANGCHAO 追加/变更/完善：如果是出口(二线)订单，生成的是进口的报关单！！
        decHead.setIeFlag(!"5".equals(orderInfo.getOrderType())?orderInfo.getIeFlag():"I");//进出口标识
        decHead.setOutDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        if ("I".equals(decHead.getIeFlag())) {
            decHead.setOverseasConsignorEname(overseasPayerInfoId); // 境外发货人名称
            decHead.setOverseasConsignorCode(isNotEmpty(overseasPayerList) ? overseasPayerList.get(0).getDepartcd() : null); // 境外发货人代码
        } else {
            decHead.setOverseasConsigneeEname(overseasPayerInfoId); // 境外收货人名称
            decHead.setOverseasConsigneeCode(isNotEmpty(overseasPayerList) ? overseasPayerList.get(0).getDepartcd() : null);
        }
        if (isNotEmpty(customsBrokerList)){
            decHead.setDeclareUnitSocialCode(customsBrokerList.get(0).getSocialCode());
            decHead.setDeclareUnit(customsBrokerList.get(0).getDepartcd());
            decHead.setDeclCiqCode(customsBrokerList.get(0).getCiqCode());
            decHead.setDeclareUnitName(customsBrokerId);
        }

        decHead.setClearanceNo(orderInfo.getCustomsNumber());
        decHead.setDeclarePlace(orderInfo.getExitClearance());//申报地海关
        decHead.setOutPortCode(orderInfo.getExitClearance());//进出境关别
        decHead.setTradeCountry(orderInfo.getTradingCountry());//贸易国
        String termsTypeCode = isNotBlank(orderInfo.getTransMode()) ? orderInfo.getTransMode() : (isNotEmpty(orderInfo.getTradingType()) ? String.valueOf(orderInfo.getTradingType()) : null);
        decHead.setTermsTypeCode(termsTypeCode);//成交方式
        decHead.setShipFeeCode(orderInfo.getShipFeeCode());//运费代码
        decHead.setShipFee(orderInfo.getFreightAmount());//运费值
        String shipCurrencyCode = null;
        if (StringUtils.isNotEmpty(orderInfo.getShipCurrencyCode())){
            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode, orderInfo.getShipCurrencyCode()));
            shipCurrencyCode =currencies != null ? currencies.getCurrency() : null;
        }
        decHead.setShipCurrencyCode(shipCurrencyCode);//运费币制
        decHead.setInsuranceCode(orderInfo.getInsuranceCode());//保费代码
        decHead.setInsurance(orderInfo.getPremiumAmount());//保费值
        String insuranceCurr = null;
        if (StringUtils.isNotEmpty(orderInfo.getInsuranceCurr())){
            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode, orderInfo.getInsuranceCurr()));
            insuranceCurr =currencies != null ? currencies.getCurrency() : null;
        }
        decHead.setInsuranceCurr(insuranceCurr);//保费币制
        decHead.setExtrasCode(orderInfo.getExtrasCode());//杂费代码
        decHead.setExtras(orderInfo.getMiscellaneousAmount());//杂费值
        String otherCurr = null;
        if (StringUtils.isNotEmpty(orderInfo.getOtherCurr())){
            ErpCurrencies currencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode, orderInfo.getOtherCurr()));
            otherCurr =currencies != null ? currencies.getCurrency() : null;
        }
        decHead.setOtherCurr(otherCurr);//杂费币制
        decHead.setContract(orderInfo.getExportContractNo());//合同协议号
        decHead.setBillCode(orderInfo.getDeliveryNumbers());//提运单号
        decHead.setOrderProtocolNo(orderInfo.getOrderProtocolNo());//订单协议号
        decHead.setTaxTypeCode(orderInfo.getTaxTypeCode());//征免性质
        decHead.setLicenceNumber(orderInfo.getLicenceNumber());//许可证号
//        decHead.setOrderId(orderInfo.getId());
        decHead.setApplyNumber(orderInfo.getId());
        decHead.setDclTrnRelFlag("0");//报关单分类
        decHead.setTradeTypeCode("0110");//监管方式
        decHead.setClearanceType("M");//报关单类型
        //orderTransportation
        OrderTransportationInfo orderTransportation = orderTransportationInfoService.getOne(new LambdaQueryWrapper<OrderTransportationInfo>()
                .eq(OrderTransportationInfo::getOrderInfoId, orderInfo.getId())
                .eq(OrderTransportationInfo::getTenantId, tenantId)
                .eq(OrderTransportationInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        decHead.setShipTypeCode(orderTransportation.getShippingType());//运输方式
        decHead.setEntyPortCode(orderTransportation.getDeparturePort());//入境口岸/离境口岸
        decHead.setShipName(orderTransportation.getTransportName());//运输工具名称
        decHead.setVoyage(orderTransportation.getVoy());//航次

        decHead.setArrivalArea(orderTransportation.getCountryArrival());//启运国
        decHead.setMarkNo(orderTransportation.getShippingMark());//标记唛码
        decHead.setMarkNumber(orderTransportation.getRemarks());//备注
        if ("I".equals(decHead.getIeFlag())){
            decHead.setDespPortCode(orderInfo.getPortDestination());//启运港代码
        }
        decHead.setDesPort(orderTransportation.getDesPort());//经停港/指运港
        //orderProductInfos
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.list(new QueryWrapper<OrderProductInfo>().lambda()
                .eq(OrderProductInfo::getOrderInfoId, orderInfo.getId())
                .eq(OrderProductInfo::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (orderProductInfos != null && !orderProductInfos.isEmpty()){
            List<DecList> decLists = new ArrayList<>();
            ErpCurrencies erpCurrencies = erpCurrenciesService.getOne(new QueryWrapper<ErpCurrencies>().lambda()
                    .eq(ErpCurrencies::getCode,orderInfo.getCustomsDeclarationCurrency()));
            decHead.setPacksKinds(orderProductInfos.get(0).getShipmentPackingType());
            decHead.setNetWeight(BigDecimal.ZERO);
            decHead.setGrossWeight(BigDecimal.ZERO);
            decHead.setPacks(0);
            List<Integer> number = new ArrayList<>();
            number.add(1);
            orderProductInfos.forEach(v->{
                DecList decList = new DecList();
                decList.setHsname(v.getChineseName());//申报品名
                decList.setHsmodel(v.getSpecificationModel());//规格型号 申报要素
                decList.setCount1(v.getLegalQuantity());//法定数量
                decList.setUnit1(v.getLegalUnitCode());//法定单位
                decList.setGoodsCount(v.getShipmentQuantity());//成交数量
                decList.setUnitCode(v.getShipmentUnit());//成交单位
                decList.setPrice(v.getShipmentUnitPrice());//单价
                decList.setTotal(v.getShipmentGoodsValue());//总价
                decList.setHscode(v.getCustomsCodeInfoCode());//税号
                // 2024/4/15 上午12:16@ZHANGCHAO 追加/变更/完善：申报要素
                decList.setHsmodel(v.getCustomsDeclarationElements());
                try {
                    if (isNotBlank(v.getCustomsCodeInfoCode())) {
                        ErpCiq erpCiq = erpCiqMapper.selectOne(new QueryWrapper<ErpCiq>().lambda()
                                .eq(ErpCiq::getCodeTs, v.getCustomsCodeInfoCode()));
                        if (isNotEmpty(erpCiq)) {
                            decList.setCiqName(erpCiq.getCiqName());
                            decList.setCiqCode(erpCiq.getCiqCode());
                        }
                    }
                } catch (Exception e) {
                    log.error("erpCiqMapper.selectOne error", e);
                }
                decList.setUnit2(v.getSecondUnitCode());//第二法定单位
                decList.setCount2(v.getSecondNumbers());//第二法定数量
                decList.setDesCountry(v.getOriginCountry());//原产国
                decList.setDestCode(v.getDistrictCode());//境内目的地 境内目的地/境内货源地辅助字段
                decList.setFaxTypeCode(v.getFaxTypeCode());//征免方式
//                decList.setCurrencyCode(erpCurrencies.getCurrency());//币制
                decList.setCurrencyCode(orderInfo.getCustomsDeclarationCurrency());//币制
                decList.setDistrictCode(orderInfo.getDomesticSourceOfGoods());//境内目的地
                decList.setDestinationCountry(orderInfo.getFinalContry());//最终目的国
                decList.setNetWeight(v.getNetWeight());
                if ("I".equals(decHead.getIeFlag())){
                    decList.setDestinationCountry("CHN");//最终目的国
                }
                decList.setItem(number.get(0));
                number.set(0,number.get(0)+1);
                decList.setId(String.valueOf(IdWorker.getId()));
                //表体关联产品id
                decList.setProductInfoId(v.getProductId());
                decLists.add(decList);

                BigDecimal netWet = v.getNetWeight() == null ? BigDecimal.ZERO : v.getNetWeight();
                BigDecimal grossWet = v.getGrossWeight() == null ? BigDecimal.ZERO : v.getGrossWeight();
                Integer packs = v.getShipmentPackagesNumbers() == null ? 0 :
                        v.getShipmentPackagesNumbers();
                decHead.setNetWeight(decHead.getNetWeight().add(netWet));
                decHead.setGrossWeight(decHead.getGrossWeight().add(grossWet));
                decHead.setPacks(decHead.getPacks() + packs);

            });
            decHead.setDecLists(decLists);
        }
        log.info("【转换后的报关单】：" + decHead);
    }

    /**
     * 根据条件获取海关备案信息
     *
     * @param name
     * @return org.jeecg.modules.business.entity.CustomerEnterprise
     * <AUTHOR>
     * @date 2024/10/18 08:16
     */
    private CustomerEnterprise getCustomerEnterpriseByName(String name) {
        return customerEnterpriseMapper.getCustomerEnterpriseByName(name);
    }

    /**
     * 根据条件获取海关备案信息
     *
     * @param searchText
     * @return org.jeecg.modules.business.entity.CustomerEnterprise
     * <AUTHOR>
     * @date 2024/10/18 08:16
     */
    private Commissioner getCommissionerByCond(String searchText) {
        return commissionerMapper.getCommissionerByCond(searchText);
    }

    /**
     * 统一获取海关备案信息
     *
     * @param searchText
     * @param type 1 委托方或租户
     * @return org.jeecg.modules.business.entity.CustomerEnterprise
     * <AUTHOR>
     * @date 2024/10/18 08:16
     */
    private LoveUBaby pleaseKissMe(String searchText, String ieFlag, String type) {
        type = isBlank(type) ? "1" : type;
        LoveUBaby loveUBaby = new LoveUBaby();
        // 委托方或租户
        if ("1".equals(type)) {
            Commissioner commissioner = getCommissionerByCond(searchText);
            if (isNotEmpty(commissioner)) {
                loveUBaby.setCustomerId(commissioner.getId());
                loveUBaby.setDepartName(commissioner.getCommissionerFullName());
                loveUBaby.setDepartcd(commissioner.getDepartcd());
                loveUBaby.setSocialCode(commissioner.getUnifiedSocialCreditCode());
            } else {
                EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(searchText);
                if (isNotEmpty(enterpriseInfo)) {
                    loveUBaby.setDepartName(enterpriseInfo.getEnterpriseFullName());
                    loveUBaby.setDepartcd(enterpriseInfo.getCustomsDeclarationCode());
                    loveUBaby.setSocialCode(enterpriseInfo.getUnifiedSocialCreditCode());
                    loveUBaby.setCiqCode(enterpriseInfo.getCustomsInspectionCode());
                }
            }
            CustomerEnterprise customerEnterprise = getCustomerEnterpriseByName(loveUBaby.getDepartName());
            if (isNotEmpty(customerEnterprise)) {
                loveUBaby.setDepartName(isNotBlank(loveUBaby.getDepartName()) ? loveUBaby.getDepartName() : customerEnterprise.getDepartName());
                loveUBaby.setDepartcd(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : customerEnterprise.getDepartcd());
                loveUBaby.setSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : customerEnterprise.getSocialCode());
                loveUBaby.setCiqCode(isNotBlank(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : customerEnterprise.getCiqCode());
            }
            // 境外供应商或境外客户
        } else if ("2".equals(type)) {
            if (isBlank(ieFlag)) {
                return loveUBaby;
            }
            // 进口 - 境外供应商
            if ("I".equals(ieFlag)) {
                DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoMapper.getDomesticSuppliersInfoByCond(searchText);
                if (isNotEmpty(domesticSuppliersInfo)) {
                    loveUBaby.setCustomerId(domesticSuppliersInfo.getId());
                    loveUBaby.setDepartName(domesticSuppliersInfo.getSuppliersFullName());
                }
            } else if ("E".equals(ieFlag)) {
                OverseasPayerInfo overseasPayerInfo = overseasPayerInfoMapper.getOverseasPayerInfoByCond(searchText);
                if (isNotEmpty(overseasPayerInfo)) {
                    loveUBaby.setCustomerId(overseasPayerInfo.getId());
                    loveUBaby.setDepartName(overseasPayerInfo.getOverseasPayerName());
                }
            }
            CustomerEnterprise customerEnterprise = getCustomerEnterpriseByName(loveUBaby.getDepartName());
            if (isNotEmpty(customerEnterprise)) {
                loveUBaby.setDepartName(isNotBlank(loveUBaby.getDepartName()) ? loveUBaby.getDepartName() : customerEnterprise.getDepartName());
                loveUBaby.setDepartcd(isNotBlank(loveUBaby.getDepartcd()) ? loveUBaby.getDepartcd() : customerEnterprise.getDepartcd());
                loveUBaby.setSocialCode(isNotBlank(loveUBaby.getSocialCode()) ? loveUBaby.getSocialCode() : customerEnterprise.getSocialCode());
                loveUBaby.setCiqCode(isNotBlank(loveUBaby.getCiqCode()) ? loveUBaby.getCiqCode() : customerEnterprise.getCiqCode());
            }
        }
        return loveUBaby;
    }

    /**
     * 利用正则表达式判断字符串是否是数字
     * @param str
     * @return
     */
    private boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if( !isNum.matches() ){
            return false;
        }
        return true;
    }
}
