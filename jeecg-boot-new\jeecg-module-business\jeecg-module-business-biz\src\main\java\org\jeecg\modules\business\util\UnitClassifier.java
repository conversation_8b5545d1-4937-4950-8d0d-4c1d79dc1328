package org.jeecg.modules.business.util;

import java.util.*;

/**
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/4/21 14:22
 */
public class UnitClassifier {

    // 重量级单位集合
    private static final Set<String> WEIGHT_UNITS = new HashSet<>(Arrays.asList(
            "千克", "克", "克拉", "035", "036", "084"
    ));
    // 数量级单位集合
    private static final Set<String> QUANTITY_UNITS = new HashSet<>(Arrays.asList(
            "台", "座", "辆", "艘", "架", "套", "个", "只", "张", "件",
            "支", "根", "条", "把", "块", "副", "片", "幅", "双", "株",
            "盘", "百个", "001", "002", "003", "004", "005", "006", "007", "008",
            "010", "011", "012", "014", "015", "016", "017", "019", "020", "023",
            "025", "028", "031", "043"
    ));
    // 体积容量单位
    private static final Set<String> VOLUME_UNITS = new HashSet<>(Arrays.asList(
            "升", "千升", "063", "095"
    ));
    // 面积单位
    private static final Set<String> AREA_UNITS = new HashSet<>(Arrays.asList(
            "平方米", "032"
    ));
    // 长度单位
    private static final Set<String> LENGTH_UNITS = new HashSet<>(Arrays.asList(
            "米", "030"
    ));
    // 能源单位
    private static final Set<String> ENERGY_UNITS = new HashSet<>(Arrays.asList(
            "千瓦时", "062"
    ));
    // 代码到中文单位的映射表
    private static final Map<String, String> CODE_TO_NAME = new HashMap<>();

    static {
        // 初始化映射表
        initializeCodeToNameMap();
    }

    // 测试
    public static void main(String[] args) {
        // 使用中文单位测试
        System.out.println("台: " + classifyUnit("台"));          // 输出: 数量级
        System.out.println("千克: " + classifyUnit("千克"));      // 输出: 重量级
        System.out.println("米: " + classifyUnit("米"));          // 输出: 长度级
        System.out.println("平方米: " + classifyUnit("平方米"));  // 输出: 面积级
        System.out.println("升: " + classifyUnit("升"));          // 输出: 体积容量级

        // 使用代码测试
        System.out.println("001 (台): " + classifyUnit("001"));   // 输出: 数量级
        System.out.println("035 (千克): " + classifyUnit("035")); // 输出: 重量级
        System.out.println("030 (米): " + classifyUnit("030"));   // 输出: 长度级
        System.out.println("032 (平方米): " + classifyUnit("032")); // 输出: 面积级
        System.out.println("095 (升): " + classifyUnit("095"));   // 输出: 体积容量级

        // 测试不存在的单位
        System.out.println("未知单位: " + classifyUnit("未知单位")); // 输出: 其他单位
        System.out.println("999: " + classifyUnit("999"));       // 输出: 其他单位
    }

    /**
     * 初始化代码到单位名称的映射
     */
    private static void initializeCodeToNameMap() {
        CODE_TO_NAME.put("001", "台");
        CODE_TO_NAME.put("002", "座");
        CODE_TO_NAME.put("003", "辆");
        CODE_TO_NAME.put("004", "艘");
        CODE_TO_NAME.put("005", "架");
        CODE_TO_NAME.put("006", "套");
        CODE_TO_NAME.put("007", "个");
        CODE_TO_NAME.put("008", "只");
        CODE_TO_NAME.put("010", "张");
        CODE_TO_NAME.put("011", "件");
        CODE_TO_NAME.put("012", "支");
        CODE_TO_NAME.put("014", "根");
        CODE_TO_NAME.put("015", "条");
        CODE_TO_NAME.put("016", "把");
        CODE_TO_NAME.put("017", "块");
        CODE_TO_NAME.put("019", "副");
        CODE_TO_NAME.put("020", "片");
        CODE_TO_NAME.put("023", "幅");
        CODE_TO_NAME.put("025", "双");
        CODE_TO_NAME.put("028", "株");
        CODE_TO_NAME.put("030", "米");
        CODE_TO_NAME.put("031", "盘");
        CODE_TO_NAME.put("032", "平方米");
        CODE_TO_NAME.put("035", "千克");
        CODE_TO_NAME.put("036", "克");
        CODE_TO_NAME.put("043", "百个");
        CODE_TO_NAME.put("062", "千瓦时");
        CODE_TO_NAME.put("063", "千升");
        CODE_TO_NAME.put("084", "克拉");
        CODE_TO_NAME.put("095", "升");
    }

    /**
     * 判断单位是数量级、重量级或其他类型
     *
     * @param unit 单位名称或代码，如 "台"、"千克"、"001"、"035"
     * @return 单位类型描述
     */
    public static String classifyUnit(String unit) {
        // 标准化输入 - 如果是代码，尝试转换为中文名称
        String normalizedUnit = unit;

        // 如果是数字编码格式，尝试从映射中获取中文名称
        if (unit.matches("\\d{3}") && CODE_TO_NAME.containsKey(unit)) {
            normalizedUnit = CODE_TO_NAME.get(unit);
        }

        // 直接判断输入单位或其代码
        if (WEIGHT_UNITS.contains(unit) || WEIGHT_UNITS.contains(normalizedUnit)) {
            return "重量级";
        } else if (QUANTITY_UNITS.contains(unit) || QUANTITY_UNITS.contains(normalizedUnit)) {
            return "数量级";
        } else if (VOLUME_UNITS.contains(unit) || VOLUME_UNITS.contains(normalizedUnit)) {
            return "体积容量级";
        } else if (AREA_UNITS.contains(unit) || AREA_UNITS.contains(normalizedUnit)) {
            return "面积级";
        } else if (LENGTH_UNITS.contains(unit) || LENGTH_UNITS.contains(normalizedUnit)) {
            return "长度级";
        } else if (ENERGY_UNITS.contains(unit) || ENERGY_UNITS.contains(normalizedUnit)) {
            return "能源级";
        } else {
            return "其他单位";
        }
    }

    /**
     * 获取单位中文名称
     * @param unitCode 单位代码
     * @return 中文名称，如果不存在则返回原代码
     */
    public static String getUnitName(String unitCode) {
        return CODE_TO_NAME.getOrDefault(unitCode, unitCode);
    }
}
