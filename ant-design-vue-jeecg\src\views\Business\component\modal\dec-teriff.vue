<template>
    <!--检验检疫名称-->
    <a-modal :centered="true"
             :visible="showModal"
             :maskClosable= "maskClosable"
             @ok="handleOk"
             @cancel="handleCancel"
             cancel-text=''
             title="选择商品列表"
             width="60%"
             class="decModalTeriff">
        <a-card :bordered="false" :bodyStyle="{padding:'0px'}">

            <div>
                <!--列表-->
                <a-table
									size="small"
                    ref="tableRef"
                    :bordered="true"
                    :columns="columns"
                    :dataSource="dataSource"
                    :loading="loading"
                    :pagination="false"
                    :rowSelection="{type:'radio', selectedRowKeys: selectedRowKeys,onChange:handleTableSelectionChange}"
                    :scroll="{ x:false, y: 280,scrollToFirstRowOnChange:true,}"
                    rowKey="rowIndex">
                </a-table>
            </div>

        </a-card>
        <template slot="footer">
            <a-button size="small" type="primary" @click="handleReject">取消</a-button>
            <a-button size="small" type="primary" @click="handleOk">确定</a-button>
        </template>


    </a-modal>
</template>

<script>
    import {decTeriff} from "@/api/dec/dec"
    import {getAction, postAction} from '@/api/manage'

    export default {
        name: 'dec-teriff',
        data() {
            return {

                formItem: {
                    labelCol: {
                        span: 5,
                    },
                    wrapperCol: {
                        span: 19,
                    },
                },

                // 已选的键
                selectedRowKeys: [],
                // 已选的内容
                selectedRows: [],
                // value副本
                val: '',
                // show副本
                showModal: false,
                maskClosable:false,
                dataSource: [],
                columns: [{
                    title: '序号',
                    key: 'rowIndex',
                    width: 80,
                    align: 'center',
                    customRender: (t, r, i) => 1 + i,
                },
                    {
                        title: '商品编码',
                        width: 160,
                        dataIndex: 'hscode',
                        key: 'hscode',
                        ellipsis: true,
                    },
                    {
                        title: '商品名称',
												width: 240,
                        dataIndex: 'hsname',
                        key: 'hsname',
                        ellipsis: true,
                    },
                    {
                        title: '备注',
                        dataIndex: 'remark',
                        key: 'remark',
                        ellipsis: true,
                    },

                ],
                loading: false,
                zzlbList: [],
                copLimitTypeList1: [],
                txtrequestCertType1: {},
            }
        },
        created() {
        },
        props: {
            value: {
                type: String,
                require: true,
            },
            show: {
                type: Boolean,
            },
            txtrequestCertType: {
                type: Object,
                default: () => []
            },

        },
        model: {
            prop: 'value',
            event: 'change',
        },
        watch: {
            value: {
                handler(val) {
                    if (!!val) {
                        this.val = val
                    }
                },
            },
            val: {
                handler() {
                    this.$emit('change', this.val)
                },
            },
            show: {
                handler(val) {
                    this.showModal = !this.showModal
                    this.decCiqNameMessage(this.val)
                    this.$nextTick(() => {
                        setTimeout(() => {
                            //默认初始化的光标
                            let el = document.getElementsByClassName("decModalTeriff")
                            let inputs = el[0].querySelectorAll("input,button")
                            if (this.showModal) {
                                const inLength = inputs.length
                                for (let i = 0; i < inLength; i++) {
                                    inputs[i].setAttribute("tabIndex", i)
                                    inputs[i].onkeyup = (ev) => {
                                        let attrIndex = ev.srcElement.getAttribute('tabIndex')
                                        let ctlI = parseInt(attrIndex)
                                        if (ev.keyCode === 13) {
                                            inputs[inLength-1].focus()
                                            inputs[inLength-1].click()
                                        }
                                    }
                                }
                                inputs[1].focus()
                                inputs[1].click()
                            }
                        }, 1000)
                    });
                },
            },
            txtrequestCertType: {
                handler(val) {
                    if (!!val.hscode) {
                        // this.decCiqNameMessage(val.hscode)
                    }
                    this.txtrequestCertType1 = val
                },
            },
            txtrequestCertType1: {
                handler(val) {
                    this.$emit("upadte:txtrequestCertType", val)
                }
            }

        },
        methods: {
            async decCiqNameMessage(hscode) {
                const auditRess = await decTeriff(hscode).catch(reason => this.$message.error(reason));

                if (auditRess.success) {
                    this.dataSource = auditRess.result
                    let idexList = []
                    if (!!this.txtrequestCertType.hsname) {
                        for (let i = 0; i < this.dataSource.length; i++) {
                            if (this.dataSource[i].name === this.txtrequestCertType.hsname) {
                                idexList.push(i)
                                break
                            }
                        }
                    }
                    if (idexList.length == 0) {
                        idexList.push(0)
                    }
                    this.selectedRowKeys = idexList
                } else {
                    this.dataSource =[]
                    // this.$message.error(auditRess.message+"6666666666666")
                }
            },
            /**
             * 取消修改
             */
            handleCancel() {
                this.showModal = !this.showModal
            },
            handleReject() {
                this.selectedRowKeys = []
                this.txtrequestCertType.ciqName = ''
                this.txtrequestCertType.ciqCode = ''
                this.showModal = !this.showModal
            },

            /**
             * 确认修改
             */
            handleOk() {

                if (this.dataSource.length > 0 && this.selectedRowKeys.length > 0) {
                    if (this.txtrequestCertType.hsname == "" || this.txtrequestCertType.hsname == null) {
                        this.txtrequestCertType.hsname = this.dataSource[this.selectedRowKeys[0]].hsname
                    }
                    if (this.txtrequestCertType.gName == "" || this.txtrequestCertType.gName == null) {
                        this.txtrequestCertType.gName = this.dataSource[this.selectedRowKeys[0]].hsname
                    }
                    //法定第一，二计量单位
                    this.txtrequestCertType.unit1 = this.dataSource[this.selectedRowKeys[0]].qtyunit
                    this.txtrequestCertType.unit2 = this.dataSource[this.selectedRowKeys[0]].qtcunit
                    //成交单位默认与法定第一计量单位一致。
                    this.txtrequestCertType.unitCode = this.dataSource[this.selectedRowKeys[0]].qtyunit
                    //如果法定第一单位和成交计量单位相同，
                    // 则法定第一数量取申报数量。
                    // 如果法定第二单位和成交计量单位相同，则法定第二数量取申报数量。
                    if(this.txtrequestCertType.unit1==this.txtrequestCertType.unitCode){
                        this.txtrequestCertType.count1 =this.txtrequestCertType.goodsCount
                    }
                    if(this.txtrequestCertType.unit2==this.txtrequestCertType.unitCode){
                        this.txtrequestCertType.count2 =this.txtrequestCertType.goodsCount
                    }
                    // this.transitionUnit()
                    this.txtrequestCertType.hscode = this.dataSource[this.selectedRowKeys[0]].hscode
                    this.txtrequestCertType1 = JSON.parse(JSON.stringify(this.txtrequestCertType))
                    this.$forceUpdate()
                }
                this.showModal = !this.showModal
                setTimeout(() => {
                    this.$emit('keyFromPromise', "decteriff")
                }, 20)
            },
            transitionUnit(){
                if(this.txtrequestCertType.unitCode=='007'){//个
                    if(this.txtrequestCertType.unit1=='054'){//千个
                        this.txtrequestCertType.count1=this.txtrequestCertType.goodsCount/1000
                    }
                    if(this.txtrequestCertType.unit2=='054'){//千个
                        this.txtrequestCertType.count2=this.txtrequestCertType.goodsCount/1000
                    }
                }else if(this.txtrequestCertType.unitCode=='054'){
                    if(this.txtrequestCertType.unit1=='007'){//个
                        this.txtrequestCertType.count1=this.txtrequestCertType.goodsCount*1000
                    }
                    if(this.txtrequestCertType.unit2=='007'){//千个
                        this.txtrequestCertType.count2=this.txtrequestCertType.goodsCount*1000
                    }
                }
            },

            /**
             * 列选择变更
             * @param selectedRowKeys 选中列的rowKey
             */
            handleTableSelectionChange(selectedRowKeys) {
                this.selectedRowKeys = selectedRowKeys
            },

        },
    }
</script>
