package org.jeecg.modules.system.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * 封装一些 sso 共用方法
 *
 * <AUTHOR>
 * @since 2022-4-30
 */
@Slf4j
@Data
@Component
public class SsoRequestUtil {
    /****************下面的配置可以都放配置文件里取，先这样吧。。*****************/

    private static SsoProperties ssoProperties;

    public SsoRequestUtil(SsoProperties ssoProperties) {
        SsoRequestUtil.ssoProperties = ssoProperties;
    }

    /**
     * 获取SSO-Server端主机地址
     */
    public static String getServerUrl() {
        return ssoProperties.getServer().getUrl();
    }

    /**
     * 获取SSO-Server端统一认证地址
     */
    public static String getAuthUrl() {
        return ssoProperties.getServer().getAuthUrl();
    }

    /**
     * 获取SSO-Server端ticket校验地址
     */
    public static String getCheckTicketUrl() {
        return ssoProperties.getServer().getCheckTicketUrl();
    }

    /**
     * 获取单点注销地址
     */
    public static String getSloUrl() {
        return ssoProperties.getServer().getSloUrl();
    }

    /**
     * 获取SSO-Server端查询userinfo地址
     */
    public static String getGetDataUrl() {
        return ssoProperties.getServer().getGetDataUrl();
    }

    /**
     * 是否启用单点注销功能
     */
    public static boolean isSlo() {
        return ssoProperties.isSloEnabled();
    }

    /**
     * 获取接口调用秘钥
     */
    public static String getSecretKey() {
        return ssoProperties.getSecretKey();
    }

    /**
     * 客户端唯一标识
     */
    public static String getClient() {
        return ssoProperties.getClient();
    }

    // -------------------------- 工具方法

    /**
     * 发出请求，并返回 SaResult 结果
     *
     * @param url 请求地址
     * @return 返回的结果
     */
    public static AjaxJson request(String url) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = httpClient.execute(httpPost);

        HttpEntity entity = response.getEntity();
        String json = EntityUtils.toString(entity);

        Map<String, Object> map = JSONObject.parseObject(json, Map.class);
        return new AjaxJson(map);
    }

    /**
     * 根据参数计算签名
     *
     * @param loginId   账号id
     * @param timestamp 当前时间戳，13位
     * @param nonce     随机字符串
     * @return 签名
     */
    public static String getSign(Object loginId, String timestamp, String nonce) {
//        return md5("apiType=userInfo" + "&client=" + getClient() + "&loginId=" + loginId + "&nonce=" + nonce + "&timestamp=" + timestamp + "&key=" + getSecretKey());
        return md5("loginId=" + loginId + "&nonce=" + nonce + "&timestamp=" + timestamp + "&key=" + getSecretKey());
    }

    /**
     * 根据参数计算签名
     *
     * @param loginId   账号id
     * @param timestamp 当前时间戳，13位
     * @param nonce     随机字符串
     * @return 签名
     */
    public static String getSignout(Object loginId, String timestamp, String nonce) {
//        return md5("client=" + getClient() + "&loginId=" + loginId + "&nonce=" + nonce + "&timestamp=" + timestamp + "&key=" + getSecretKey());
        return md5("loginId=" + loginId + "&nonce=" + nonce + "&timestamp=" + timestamp + "&key=" + getSecretKey());
    }

    // 单点注销回调时构建签名
    public static String getSignByLogoutCall(Object loginId, String autoLogout, String timestamp, String nonce) {
        return md5("autoLogout=" + autoLogout + "&loginId=" + loginId + "&nonce=" + nonce + "&timestamp=" + timestamp + "&key=" + getSecretKey());
    }

    // 校验ticket 时构建签名
    public static String getSignByTicket(String ticket, String ssoLogoutCall, String timestamp, String nonce) {
//        return md5("client=" + getClient() + "&nonce=" + nonce + "&ssoLogoutCall=" + ssoLogoutCall + "&ticket=" + ticket + "&timestamp=" + timestamp + "&key=" + getSecretKey());
        return md5("nonce=" + nonce + "&ssoLogoutCall=" + ssoLogoutCall + "&ticket=" + ticket + "&timestamp=" + timestamp + "&key=" + getSecretKey());
    }

    /**
     * 指定元素是否为null或者空字符串
     *
     * @param str 指定元素
     * @return 是否为null或者空字符串
     */
    public static boolean isEmpty(Object str) {
        return str == null || "".equals(str);
    }

    /**
     * md5加密
     *
     * @param str 指定字符串
     * @return 加密后的字符串
     */
    public static String md5(String str) {
        str = (str == null ? "" : str);
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            byte[] btInput = str.getBytes();
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char[] strA = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                strA[k++] = hexDigits[byte0 >>> 4 & 0xf];
                strA[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(strA);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 字符串的长度
     * @return 一个随机字符串
     */
    public static String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    /**
     * URL编码
     *
     * @param url see note
     * @return see note
     */
    public static String encodeUrl(String url) {
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
//        String url = "http://www.jgsoft.com.cn/api-user/business/enterprises/queryBalanceByBiz/91370602MAD6C9999";
//        System.out.println(requestGet(url));

        String url = "http://www.jgsoft.com.cn/api-user/business/consumptionRecords/save";
        JSONObject paramJson = new JSONObject();
        paramJson.put("creditCode", "91370602MAD6C9999");
        paramJson.put("userId", 1923257344081338370L);
        paramJson.put("serviceInstanceId", "55556");
        paramJson.put("serviceCode", "jg-api-01");
        paramJson.put("uuid", UUID.randomUUID().toString());
        log.info(requestPost(url, paramJson.toString()));
    }
    /**
     * 请求三方接口
     *
     * @param url
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/18 下午4:08
     */
    public static String requestPost(String url, String jsonString) {
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 系统参数
        Map<String, Object> param = new HashMap<>();
        param.put("timeStamp", timestamp);
        String sign = null;
        try {
            sign = buildSign(param, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/json");
        request.header("timestamp", timestamp);
        request.header("sign", sign);
        request.body(jsonString);
        int readTimeout = 180000;
        request.timeout(readTimeout);
        HttpResponse response = request.execute();
        String responseContent = response.body();
        log.info("[requestPost]服务调用结果：{}", responseContent);
        return responseContent;
    }
    /**
     * 请求三方接口
     *
     * @param url
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/18 下午4:08
     */
    public static String requestGet(String url) {
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 系统参数
        Map<String, Object> param = new HashMap<>();
        param.put("timeStamp", timestamp);
        String sign = null;
        try {
            sign = buildSign(param, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HttpRequest request = HttpUtil.createGet(url);
        request.header("Content-Type", "application/json");
        request.header("timestamp", timestamp);
        request.header("sign", sign);
        int readTimeout = 180000;
        request.timeout(readTimeout);
        HttpResponse response = request.execute();
        String responseContent = response.body();
        log.info("[requestGet]服务调用结果：{}", responseContent);
        return responseContent;
    }
    /**
     * 构建签名
     *
     * @param paramsMap 参数
     * @param secret    密钥
     * @return
     */
    public static String buildSign(Map<String, ?> paramsMap, String secret) {
        Set<String> keySet = paramsMap.keySet();
        List<String> paramNames = new ArrayList<String>(keySet);
        Collections.sort(paramNames);
        StringBuilder paramNameValue = new StringBuilder();
        for (String paramName : paramNames) {
            paramNameValue.append(paramName).append(paramsMap.get(paramName));
        }
        String source = secret + paramNameValue + secret;
        log.info("source: {}", source);
        return md5_(source);
    }
    /**
     * 生成md5,全部大写
     *
     * @param message
     * @return
     */
    public static String md5_(String message) {
        try {
            // 1 创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 2 将消息变成byte数组
            byte[] input = message.getBytes();
            // 3 计算后获得字节数组,这就是那128位了
            byte[] buff = md.digest(input);
            // 4 把数组每一字节（一个字节占八位）换成16进制连成md5字符串
            return byte2hex(buff);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 二进制转十六进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
}
