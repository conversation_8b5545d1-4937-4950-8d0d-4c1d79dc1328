package org.jeecg.modules.business.controller;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.RecContainerCargoNote;
import org.jeecg.modules.business.entity.RecShipPlan;
import org.jeecg.modules.business.entity.RecShipPlanI;
import org.jeecg.modules.business.service.IRecShipPlanIService;
import org.jeecg.modules.business.service.IRecShipPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 出口船舶计划推送 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@RestController
@RequestMapping("/business/shipPlan")
public class RecShipPlanController {

    @Autowired
    private IRecShipPlanService recShipPlanService;
    @Autowired
    private IRecShipPlanIService recShipPlanIService;

    /**
     * 出口船舶计划列表查询
     *
     * @param recShipPlan
     * @param pageNo
     * @param pageSize
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/2 16:32
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(RecShipPlan recShipPlan,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        return recShipPlanService.queryPageList(pageNo, pageSize, recShipPlan, req);
    }

    /**
     * 进口船舶计划列表查询
     *
     * @param recShipPlanI
     * @param pageNo
     * @param pageSize
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/2 16:32
     */
    @GetMapping(value = "/listI")
    public Result<?> queryPageListI(RecShipPlanI recShipPlanI,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        return recShipPlanIService.queryPageList(pageNo, pageSize, recShipPlanI, req);
    }

    /**
     * 运抵回执列表查询
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/4 17:08
     */
    @GetMapping(value = "/listContainerCargoNodeByDecId")
    public Result<?> listContainerCargoNodeByDecId(@RequestParam("decId") String decId,
                                                   HttpServletRequest req) {
        return recShipPlanIService.listContainerCargoNodeByDecId(decId, req);
    }

    /**
     * 获取船信息数据
     *
     * @param decId
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/7/24 17:08
     */
    @GetMapping(value = "/getShipInfoByDecId")
    public Result<?> getShipInfoByDecId(@RequestParam("decId") String decId,
                                        HttpServletRequest req) {
        return recShipPlanIService.getShipInfoByDecId(decId, req);
    }
}
