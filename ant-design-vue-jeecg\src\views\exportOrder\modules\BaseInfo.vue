<template>
  <j-form-container :disabled="formDisabled">
    <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
      <a-row>
        <a-col :span="8">
          <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orderNo">
            <template #label>
              <span>销售订单号</span>
              <a-tooltip
                slot="suffix"
                title="您可以输入订单号，如果为空，则系统会自动生成订单号"
              >
                <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
              </a-tooltip>
            </template>
            <a-input v-model="model.orderNo" placeholder="请输入订单号" :maxLength="32"></a-input>
          </a-form-model-item>
        </a-col>
				<a-col :span="8">
					<a-form-model-item label="订单类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orderType">
						<a-select placeholder="请选择订单类型"  v-model="model.orderType" @change='orderTypeChange'>
							<a-select-option value="3">外销订单</a-select-option>
							<a-select-option value="4">内销订单</a-select-option>
							<a-select-option value="5">二线出区</a-select-option>
						</a-select>
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<a-form-model-item label="成交方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradingType">
						<j-dict-select-tag
							type="list"
							v-model="model.transMode"
							dictCode="trading_type"
							placeholder="请选择成交方式"
						/>
					</a-form-model-item>
				</a-col>
        <a-col :span="8">
          <a-form-model-item label="委托方" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="buyer">
						<j-search-select-tag_ v-model="model.buyer" :defaultValue="store.getters.tenantInfo.id" :dict-options="dictOptions" placeholder="请选择委托方"/>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
					<a-form-model-item
						label="付款方"
						:labelCol="labelCol"
						:wrapperCol="wrapperCol"
						prop="overseasPayerInfoId"
					>
						<j-search-select-tag_ v-model="model.overseasPayerInfoId" :dict="dictCodePayerName" @change="handleChangeOverseasPayerInfo"/>
					</a-form-model-item>
        </a-col>
				<a-col :span="8">
					<a-form-model-item label="运输方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shippingType">
						<j-search-select-tag_ v-model="model.shippingType" :dict="dictCodeShippingTypeName" />
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<a-form-model-item label="采购人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="salesman">
						<a-input v-model="model.salesman" placeholder="请输入采购人" :maxLength="32"></a-input>
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="销售邮箱" prop="salesEmail">
						<j-search-select-tag-all
							v-model="model.salesEmail"
							dict="SALES_EMAIL_DICT"
							placeholder="请选择销售邮箱"
							type="list"
						/>
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="银行信息" prop="bankInfoId">
						<j-search-select-tag-all
							v-model="model.bankInfoId"
							:dict="dictStoreCode"
							placeholder="请选择银行信息"
							type="list"
						/>
					</a-form-model-item>
				</a-col>
        <a-col :span="8">
          <a-form-model-item
            label="订单日期"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="signDate"
          >
            <j-date placeholder="请选择订单日期" @change="signDateChange" v-model="model.signDate" style="width: 100%" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item
            label="付款日期"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="payDate"
          >
            <j-date placeholder="请选择付款日期" @change="payDateChange" v-model="model.payDate" style="width: 100%" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item
            label="交货日期"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="deliveryDate"
          >
            <j-date placeholder="请选择交货日期" @change="deliveryDateChange" v-model="model.deliveryDate" style="width: 100%" />
          </a-form-model-item>
        </a-col>
<!--        <a-col :span="8">-->
<!--          <a-form-model-item-->
<!--            label="币制"-->
<!--            :labelCol="labelCol"-->
<!--            :wrapperCol="wrapperCol"-->
<!--            prop="currency"-->
<!--          >-->
<!--            <j-search-select-tag-->
<!--              v-model="model.currency"-->
<!--              dict="erp_currencies,currency,code,1=1 order by currency_order desc"-->
<!--            />-->
<!--          </a-form-model-item>-->
<!--        </a-col>-->
        <a-col :span="8">
          <a-form-model-item label="支付条款" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="paymentClause">
<!--            <j-remarks-component-->
<!--              v-model="model.paymentClause"-->
<!--              placeholder="请输入支付条款"-->
<!--              :maxLength="256"-->
<!--            />-->
						<j-search-select-tag-all
							v-model="model.paymentClause"
							dict="exchange_collection_type"
							placeholder="请选择支付条款"
							type="list"
						/>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="运输条款" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="transportClause">
            <j-remarks-component
              v-model="model.transportClause"
              placeholder="请输入运输条款"
              :maxLength="256"
            />
          </a-form-model-item>
        </a-col>
				<a-col :span="8">
					<a-form-model-item label="订单总金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalAmount">
						<a-input v-model="model.totalAmount" placeholder="请输入订单总金额" :maxLength="32"></a-input>
					</a-form-model-item>
				</a-col>
        <a-col :span="8">
          <a-form-model-item label="启运地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="arrivalPort">
<!--            <a-input v-model="model.arrivalPort" placeholder="请输入启运地" :maxLength="32"></a-input>-->
						<j-search-select-tag
							v-model="model.arrivalPort"
							dict="erp_cityports,cnname,cityport_code,isenabled=1"
							:async="true"
							:pageSize="50"
						/>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="目的地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="desPort">
<!--            <a-input v-model="model.desPort" placeholder="请输入目的地" :maxLength="32"></a-input>-->
						<j-search-select-tag
							v-model="model.desPort"
							dict="erp_cityports,cnname,cityport_code,isenabled=1"
							:async="true"
							:pageSize="50"
						/>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注" prop="remark">
            <j-remarks-component
              v-model="model.remark"
              placeholder="备注"
              :maxLength="256"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </j-form-container>
</template>
<script>
import Vue from 'vue'
import { TENANT_ID } from '@/store/mutation-types'
import { dateRandomNumber } from '@/utils/util'
import { getAction } from '@/api/manage'
import store from '@/store'

export default {
  name: 'BaseInfo',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    labelCol: Object,
    wrapperCol: Object,
    menuName: {
      type: String,
      default: 'unknown',
      required: false,
    },
  },
	data() {
		return {
			overseasPayerInfoAll: [],
			dictCodePayerName: '',
			dictCodeJiaCustomer: `commissioner,COMMISSIONER_FULL_NAME,id,tenant_id='${store.getters.tenantId}' and del_flag=0`,
			dictOptions: [],
			dictCodeOverseasPayerInfoId: '',
			model: {},
			labelCol1: {
				xs: { span: 24 },
				sm: { span: 8 },
				xl: { span: 5}
			},
			wrapperCol1: {
				xs: { span: 24 },
				sm: { span: 14 },
				xl: { span: 19}
			},
			validatorRules: {
				// purchaseOrderNo: [{ required: true, message: '请输入采购订单号!' }],
				buyer: [{ required: true, message: '请选择委托方!' }],
				overseasPayerInfoId: [{ required: true, message: '请选择供应商!' }],
				orderType: [{ required: true, message: '请选择订单类型!' }],
			},
			dictCodeShippingTypeName:'',
			dictStoreCode: '',
		}
	},
  created() {
    // 查询当前租户id
    let tenantId = Vue.ls.get(TENANT_ID)
    if (!tenantId) {
      tenantId = 0
    }
    this.dictCodePayerName =
      'overseas_payer_info,overseas_payer_name,id,tenant_id=' + tenantId + 'and is_effective_flag=1 and del_flag=0'
    // this.dictCodeShippingTypeName = 'trans_type'
    this.dictCodeShippingTypeName = 'YSFS'
    this.dictCodeOverseasPayerInfoId =
      'DOMESTIC_SUPPLIERS_INFO,SUPPLIERS_FULL_NAME,id,tenant_id=' + tenantId + ' and del_flag=0'
		this.dictStoreCode =
			'bank_account_info_hx,DESCRIPTION,ACCOUNT_NO,tenant_id=' + tenantId
    this.loadDictOptions()
  },
  computed: {
		store() {
			return store
		},
    formDisabled() {
      return this.disabled
    },
  },
  updated() {
    this.modelChangeTimes = this.modelChangeTimes + 1
  },
  methods: {
		orderTypeChange(value) {
			// console.log(value)
		},
    // 查询委托方数据
    async loadDictOptions() {
      this.dictOptions = []
      await getAction(`/sys/dict/getDictItems/${this.dictCodeJiaCustomer}`,{}).then(res=>{
        if (res.success) {
          this.dictOptions = res.result.map(item => ({value: item.value, text: item.text}))
        } else {
          console.error('getDictItems error: : ', res)
          this.dictOptions = []
        }
        if (store.getters.tenantInfo) {
          this.dictOptions.push({value: String(store.getters.tenantInfo.id), text: store.getters.tenantInfo.name})
        }
      })
			this.overseasPayerInfoAll = []
			await getAction(`/sys/dict/getDictItems/${this.dictCodePayerName}`,{}).then(res=>{
				if (res.success) {
					this.overseasPayerInfoAll = res.result.map(item => ({value: item.value, text: item.text}))
				} else {
					console.error('getDictItems error: : ', res)
					this.overseasPayerInfoAll = []
				}
			})
    },
    signDateChange(val){
      if(!val){
        this.model.signDate=null
      }else {
        this.model.signDate=val
      }
    },
    payDateChange(val){
      if(!val){
        this.model.payDate=null
      }else {
        this.model.payDate=val
      }
    },
    deliveryDateChange(val){
      if(!val){
        this.model.deliveryDate=null
      }else {
        this.model.deliveryDate=val
      }
    },
    initModel(value) {
      let model = value
      this.model = model
      this.modelChangeTimes--
    },
    async validateForm() {
      try {
        return await this.$refs.form.validate()
      } catch (error) {
        return false
      }
    },
    getModel() {
      return this.model
    },
		handleChangeOverseasPayerInfo(data) {
			if (this.overseasPayerInfoAll.length > 0) {
				let item = this.overseasPayerInfoAll.find(item => item.value == data)
				this.model.receiver = item ? item.text : ''
			}
		},
    changeTotalAmount(val) {
      this.model.totalAmount = val
      this.$forceUpdate()
    }
  }
}
</script>
