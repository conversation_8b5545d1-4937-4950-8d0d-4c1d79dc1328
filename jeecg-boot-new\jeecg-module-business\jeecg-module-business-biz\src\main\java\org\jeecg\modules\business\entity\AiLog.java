package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
@TableName("ai_log")
public class AiLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 日志类型（1登录日志，2操作日志）
     */
    @TableField("LOG_TYPE")
    private Integer logType;

    /**
     * 日志内容
     */
    @TableField("LOG_CONTENT")
    private String logContent;

    /**
     * 操作类型
     */
    @TableField("OPERATE_TYPE")
    private Integer operateType;

    /**
     * 操作用户账号
     */
    @TableField("USERID")
    private String userid;

    /**
     * 操作用户名称
     */
    @TableField("USERNAME")
    private String username;

    /**
     * IP
     */
    @TableField("IP")
    private String ip;

    /**
     * 请求JAVA方法
     */
    @TableField("METHOD")
    private String method;

    /**
     * 请求路径
     */
    @TableField("REQUEST_URL")
    private String requestUrl;

    /**
     * 请求参数
     */
    @TableField("REQUEST_PARAM")
    private String requestParam;

    /**
     * 请求类型
     */
    @TableField("REQUEST_TYPE")
    private String requestType;

    /**
     * 耗时
     */
    @TableField("COST_TIME")
    private BigDecimal costTime;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID")
    private String tenantId;

    /**
     * 关联单号流水id
     */
    @TableField("SOURCE_ID")
    private String sourceId;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 是否成功
     */
    @TableField("IS_SUCCESS")
    private Boolean isSuccess = false;
}
