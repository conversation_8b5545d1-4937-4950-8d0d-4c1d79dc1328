<template>
    <div>
      <a-spin :spinning="confirmLoading">
        <!--表单-->
        <a-form-model :model="record" ref="emsCmForm" :rules="rules" v-enterToNext>
          <table class="my-table">
            <tr>
              <input-item :readonly="true" label="序号" iprop="gNo" v-model="record.gNo" />
              <input-item :readonly="addType" required label="成品序号" iprop="exgNo" v-model="record.exgNo" @pressEnter="searchByExgNo"  />
              <input-item :readonly="true" label="成品料号" v-model="record.exgCopGno" />
            </tr>
            <tr>
              <input-item :readonly="true" label="成品商品编码" v-model="record.exgHscode" />
              <input-item :readonly="true" label="成品商品名称" v-model="record.exgGname" />
              <!-- 添加 @pressEnter 事件 -->
              <input-item 
                :readonly="addType" 
                required 
                label="料件序号" 
                iprop="imgNo" 
                v-model="record.imgNo" 
                @pressEnter="searchByImgNo" 
              />
            </tr>
            <tr>
            <input-item :readonly="true" label="料件料号" v-model="record.imgCopGno" />
            <input-item :readonly="true" label="料件商品编码" v-model="record.imgHscode" />
            <input-item :readonly="true" label="料件商品名称" v-model="record.imgGname" />
            </tr>
            <tr>
             <input-item :readonly="addType" required label="单耗版本号" iprop="ucnsverno" v-model="record.ucnsverno" />
             <input-item :readonly="addType" required iprop="decCm"label="净耗" v-model="record.decCm" />
             <select-item  ref="selectItem"
                            :options="[{value: '1',text: '已申报'},{value: '2',text: '未申报'}]"
                            :value-width="1"
                            required
                            label="单耗申报状态"
                            iprop="unitConsumptionStatus"
                            v-model="record.unitConsumptionStatus"
              />
            </tr>
            <tr>
                <input-num-item
              :readonly="addType"
              stype="number"
              label="有形损耗率（%）"
              iprop="decDm"
              required
              v-model="record.decDm"
            />
            <input-num-item
              :readonly="addType"
              stype="number"
              label="无形损耗率（%）"
              iprop="intangibleLossRate"
              required
              v-model="record.intangibleLossRate"
            />
            <input-num-item
              :readonly="addType"
              stype="number"
              label="保税料件比例（%）"
              iprop="proportionOfBondedMaterials"
              required
              v-model="record.proportionOfBondedMaterials"
            />
            </tr>
            <tr>
              <select-item  ref="selectItem"
                            :options="[{value: '1',text: '修改'},{value: '3',text: '增加'}]"
                            :value-width="1"
                            label="修改标志"
                            required
                            iprop="modifyFlag"
                            v-model="record.modifyFlag"
              />
              <select-item  ref="selectItem" :readonly ="true"
                            :options="[{value: '1',text: '运行'}]"
                            :value-width="1"
                            required
                            label="企业执行标志"
                            iprop="enterpriseExecutionFlag"
                            v-model="record.enterpriseExecutionFlag"
              />
              <input-item :readonly="addType" label="备注" @pressEnter="handleNoteEnter()"
              iprop="note" v-model="record.note" placeholder="按回车键保存"/>
            </tr>
          </table>
        </a-form-model>
        <!--列表-->
        <div style="font-size: 14px;color: #64A5EB;font-weight: bold">
          <a-icon type="appstore" theme="twoTone" /><span style="margin-left: 4px">单损耗列表</span>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-row>
            <a-col :span="9">
              <a-button size="small" @click="handleAdd" type="primary" icon="plus">新增</a-button>
              <a-button size="small" @click="balanceCheck" type="primary" 
              icon="check">平衡检查</a-button>
              <a-button
                size="small"
                @click="batchDel"
                v-if="selectedRowKeys.length > 0"
                ghost
                type="primary"
                icon="delete">批量删除
              </a-button>
            </a-col>
            <a-col :span="15">
              <!-- 查询区域 -->
              <div class="table-page-search-wrapper">
                <a-form layout="inline" @keyup.enter.native="searchQuery">
                  <a-row :gutter="24">
                    <!-- <a-col :xl="4" :sm="24" :xxl="4" :md="12" style="height: 34px">
                      <a-form-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-input placeholder="序号" v-model="queryParam.gNo"></a-input>
                      </a-form-item>
                    </a-col> -->
					<a-col :xl="6" :sm="24" :xxl="6" :md="12" style="height: 34px">
						<a-form-item label="成品序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入成品序号" v-model="queryParam.exgNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12" style="height: 34px">
						<a-form-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件序号" v-model="queryParam.imgNo"></a-input>
						</a-form-item>
					</a-col>
                    <a-col :xl="5" :sm="24" :xxl="6" :md="12" style="height: 34px;margin-top: -2px">
                              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                              </span>
                    </a-col>
                  </a-row>
                </a-form>
              </div>
  
            </a-col>
  
          </a-row>
  
        </div>
  
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            size="small"
            :scroll="{ x: true }"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            class="j-table-force-nowrap"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, fixed: true}"
            :rowClassName="getRowClassname"
            @change="handleTableChange"
            :customRow="rowEvent"
          >
            <!-- 可出口数量START -->
            <template slot="exportedQy" slot-scope="text, record">
          <span :class="{ textGreen: computedExportedQy(record) > 0 }">
            {{ computedExportedQy(record) }}
          </span>
            </template>
            <!-- 可出口数量END -->
            <!-- 备案数量START -->
            <template slot="qty" slot-scope="text, record">
          <span :class="{ textGreen: record.qty > 0 }">
            {{ record.qty }}
          </span>
              <!--					<a-icon  v-if="$store.getters.tenantType == 1" type="edit" theme='twoTone' @click="showBeianModal({ row })"/>-->
            </template>
            <!-- 备案数量END -->
            <!-- 已出口数量START -->
            <template slot="exportedQtySlots" slot-scope="text, record">
          <span :class="{ textGreen: record.exportedQty > 0 }">
            {{ record.exportedQty }}
          </span>
            </template>
            <!-- 已出口数量END -->
            <!-- 已进口数量START -->
            <template slot="calculateSlots" slot-scope="text, record">
              <template v-if="record.calculateNum || record.calculateNum === 0">
                <a-tag color="#87d068">
                  {{ record.calculateNum }}
                </a-tag>
              </template>
              <a-button
                :size="$types.SMALL_SIZE"
                @click="calculateNumFun(record)"
                :loading="countLoadingFlag"
              >计算
              </a-button>
            </template>
            <!-- 已进口数量END -->
            <!-- 申报记录START -->
            <template slot="applyHistory" slot-scope="text, record">
              <a @click="getApplyHistory(record)">申报记录</a>
            </template>
            <!-- 申报记录END 已经通过其他方法计算-->
            <span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
            {{subStrForColumns(record.gModel, 15)}}
          </span>
          </a-table>
        </div>
        <a-modal
  title="平衡检查"
  :width="1000"
  :visible="balanceModalVisible"
  @cancel="balanceModalVisible = false"
>
<!-- 底部按钮自定义 -->
  <div slot="footer" class="balance-modal-footer">
    <a-button type="primary" @click="balanceModalVisible = false">
      关闭
    </a-button>
    </div>

  <a-table
    :columns="balanceTableColumns"
    :dataSource="balanceTableData"
    :loading="balanceModalLoading"
    class="j-table-force-nowrap"
  />
</a-modal>
  
      </a-spin>
    </div>
  
  
  </template>
  
  <script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import InputNumItem from '@views/declaration/component/m-table-input-num-item'
  import InputItem from '@views/declaration/component/m-table-input-item'
  import SelectItem from '@views/declaration/component/m-table-select-item'
  import DateItem from '@views/declaration/component/m-table-date-item'
  import { getAction, postAction, deleteAction, _postAction, httpAction } from '@/api/manage'
  import { ajaxGetDictItems, duplicateCheckByGrouping } from '@/api/api'
  import { nextTick } from 'vuedraggable'
  import lodash from 'lodash'
  import { Mul } from '@/utils/util'
  import { subStrForColumns } from '@/utils/util'
  export default {
    mixins: [JeecgListMixin],
    name: 'list-ems-aexg',
    components: {
      SelectItem,
      InputItem,
      DateItem,
      InputNumItem,
    },
    props: {
      emsHead: {
        type: Object,
      }
    },
    data() {
      return {
        balanceModalLoading: false,
        queryParam:{
          type: '3',
        },
        disableMixinCreated:true,
        confirmLoading:false,
        record:{},
        rules:{
          exgNo: [{ checkName: '成品序号', required: true, max: 50, validator: this.checkNo }],
          imgNo: [{ checkName: '料件序号', required: true, max: 50, validator: this.checkNo }],
          ucnsverno: [{ required: true, message: '请填写单耗版本号!'}],
          decCm: [{ required: true, message: '请填写净耗!'}],
          gModel: [{ required: true, message: '请填写规格型号!'}],
          qty: [{ required: true, message: '请填写申报数量!'}],
          unitConsumptionStatus: [{ required: true, message: '请选择单耗申报状态!' }],
          unit1: [{ required: true, message: '请选择法定计量单位!' }],
          decDm : [{ required: true, message: '请填写有形损耗率!'}],
          proportionOfBondedMaterials : [{ required: true, message: '请填写保税料件比例!' }],
          intangibleLossRate : [{ required: true, message: '请填写无形损耗率!'}],
          modifyFlag : [{ required: true, message: '请选择修改标志!'}],
          enterpriseExecutionFlag : [{ required: true, message: '请选择企业执行标志!' }]
        },
        columns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'gNo'
          },
          {
            title: '成品序号',
            align: 'center',
            dataIndex: 'exgNo'
          },
          {
            title: '成品料号',
            align: 'center',
            dataIndex: 'exgCopGno'
          },
          {
            title: '成品商品编码',
            align: 'center',
            dataIndex: 'exgHscode'
          },
          {
            title: '成品商品名称',
            align: 'center',
            dataIndex: 'exgGname'
          },
          {
            title: '料件序号',
            align: 'center',
            dataIndex: 'imgNo'
          },
          {
            title: '料件料号',
            align: 'center',
            dataIndex: 'imgCopGno'
          },
          {
            title: '料件商品编码',
            align: 'center',
            dataIndex: 'imgHscode'
          },
          {
            title: '料件商品名称',
            align: 'center',
            dataIndex: 'imgGname'
          },
          {
            title: '单耗版本号',
            align: 'center',
            dataIndex: 'ucnsverno'
          },
          {
            title: '净耗',
            align: 'center',
            dataIndex: 'decCm'
          },
          {
            title: '有形损耗率（%）',
            align: 'center',
            dataIndex: 'decDm'
          },
          {
            title: '无形损耗率（%）',
            align: 'center',
            dataIndex: 'intangibleLossRate'
          },
          {
            title: '单耗申报状态',
            align: 'center',
            dataIndex: 'unitConsumptionStatus',
            customRender: function (text) {
            if (text == '1') {
              return "已申报";
            } else if(text == '2'){
              return "未申报";
            } else {
              return '';
            }
          }
          },
          {
            title: '保税料件比例（%）',
            align: 'center',
            dataIndex: 'proportionOfBondedMaterials'
          },
          {
            title: '修改标志',
            align: 'center',
            dataIndex: 'modifyFlag',
            customRender: function (text) {
            if (text == '1') {
              return "修改";
            } else if(text == '3'){
              return "增加";
            } else {
              return '';
            }
          }
          },
        ],
        addType: false,
        url: {
          list: '/business/ems/listEmsDetail',
          save: '/business/ems/saveEmsCm',
          getById: '/business/ems/getEmsCmById',
          deleteBatch: '/business/ems/deleteDetailBatch',
          balanceCheck: '/business/ems/balanceCheck'
        },
        labelCol: {
          xs: { span: 5 },
          // sm: { span: 7 },
          xxl:{ span: 5},
          xl:{ span: 9}
        },
        wrapperCol: {
          xs: { span: 16 },
          // sm: { span: 16 },
        },
        balanceModalVisible: false, // 控制模态框显示隐藏
    balanceTableColumns: [
      {
        title: '料件序号',
        align: 'center',
        dataIndex: 'gNo'
      },
      {
        title: '料件料号',
        align: 'center',
        dataIndex: 'copGno'
      },
      {
        title: '商品名称',
        align: 'center',
        dataIndex: 'gName'
      },
      {
        title: '计量单位',
        align: 'center',
        dataIndex: 'unit'
      },
      {
        title: '申报数量',
        align: 'center',
        dataIndex: 'qty'
      },
      {
        title: '消耗量',
        align: 'center',
        dataIndex: 'consumption'
      },
      {
        title: '差额量',
        align: 'center',
        dataIndex: 'difference'
      },
      {
        title: '差额率',
        align: 'center',
        dataIndex: 'differenceRate'
      }
    ],
    balanceTableData: [] // 表格数据源
  
      }
  
  
    },
    created() {
  
  
  
    },
    mounted() {
  
    },
    methods: {
      subStrForColumns,
      computedImportedQy(records) {
        return this.floatSub(records.qty, records.importedQty)
      },
      balanceCheck() {
        this.balanceModalVisible = true; // 显示模态框
        this.balanceModalLoading = true; // 显示加载状态
        getAction(this.url.balanceCheck, {emsHeadId:this.emsHead.id})
          .then(res => {
            this.balanceModalLoading = false; // 隐藏加载状态
            if (res.success) {
              this.balanceTableData = res.result.records || res.result; // 处理表格数据  
            }   
          })



    // 事件内容暂空，后续可根据需求添加逻辑
         },
      handleAdd(){
        this.queryParam.emsId = this.emsHead.id
        this.initData()
      },
      initData(){
        this.record = {}
        this.record.emsNo = this.emsHead.emsNo
        this.record.emsId = this.emsHead.id
        this.record.gNo = this.ipagination.total + 1
        //单耗版本号默认 0 
        this.record.ucnsverno = '0'
        //单耗申报状态默认 已申报
        this.record.unitConsumptionStatus = '1'
        //有形损耗率（%）默认 0
        this.record.decDm = '0'
        //无形损耗率（%）默认 0
        this.record.intangibleLossRate = '0'
        //保税料件比例（%）默认 100
        this.record.proportionOfBondedMaterials = '100'
        //修改标志默认 增加
        this.record.modifyFlag = '3'
        //企业执行标志默认 运行
        this.record.enterpriseExecutionFlag = '1'
        this.$forceUpdate()
      },
      init(){
        this.queryParam.emsId = this.emsHead.id
        if (this.queryParam.emsId) {
          this.loadData(1)
        }
        this.initData()
      },
      handleNoteEnter(){
        this.saveForm()
      },
      handleSave() {
        this.saveForm()
      },
      async saveForm() {
        if(!this.record.emsId){
          this.$message.warning('请先保存表头信息。')
          return
        }
        const that = this
        // 触发表单验证
        this.$refs.emsCmForm.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            console.log('最终保存的加贸手册料件数据：', this.record)
            httpAction(this.url.save, this.record, 'post')
              .then((res) => {
                if (res.success) {
                  that.$message.success('保存成功！')
                  that.init()
                } else {
                  that.$message.error(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          } else {
            this.$message.error('表单校验失败！')
          }
        })
      },
      checkNo (rule, value, callback) {
        if (rule.required) {
          if (this.isEmpty(value)) {
            callback(new Error(`请输入${rule.checkName}!`))
          }
        }
        if (!this.isEmpty(value)) {
          let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
          if (rule.checkNum) {
            if (!reg.test(value)) {
              callback(new Error('请输入数字!'))
            }
          }
          if (value < 0) {
            callback(new Error('不能输入负数!'))
          }
          if (!this.isEmpty(rule.max) && value.length > rule.max) {
            callback(new Error(`长度不能大于${rule.max}位!`))
          }
          if ((value.toString()).indexOf('.') != -1) {
            callback(new Error('不能含有小数点!'))
          }
        }
        callback()
      },
      // 增加样式方法返回值
      getRowClassname(record) {
        if (record.status == '2') {
          return 'data-rule-invalid'
        }
      },
      rowEvent: function(record, index) {
        return {
          on: {
            click: async () => {
              let keys = []
              this.selectionRows = []
              keys.push(record.id)
              this.selectedRowKeys = keys
              this.selectionRows.push(record)
              this.record = record
              this.$forceUpdate()
            },
          }
        }
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        if (!this.queryParam.emsId) {
          this.searchReset()
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params)
          .then(res => {
            if (res.success) {
              //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
              this.dataSource = res.result.records || res.result
              this.$forceUpdate()
              if (res.result.total) {
                this.ipagination.total = res.result.total
              } else {
                this.ipagination.total = 0
              }
              this.record.gNo = this.ipagination.total + 1
              //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            } else {
              this.$message.warning(res.message || res)
            }
          })
          .finally(() => {
            // this.handleEmptyIcon(params.pageSize)
            this.loading = false
          })
      },
      searchReset() {
        this.queryParam = {
          type: '3', // 1料件 2成品 3损耗
          emsId: this.emsHead.id
        }
        this.loadData(1)
      },
      searchByExgNo() {
        if (!this.emsHead.id) {
          this.$message.warning('请先保存表头信息后进行操作！');
          return;
        }
        const params = {
          type: '2',
          emsId: this.emsHead.id,
          gNo: this.record.exgNo
        };
        this.loading = true;
        getAction(this.url.list, params)
          .then(res => {
            if (res.success) {
              const exgList = res.result.records || res.result;
              this.record.exgCopGno = exgList&&exgList.length>0?exgList[0].copGno:''
              this.record.exgGname = exgList&&exgList.length>0?exgList[0].gName:''
              this.record.exgHscode = exgList&&exgList.length>0?exgList[0].codet:''
              this.$forceUpdate() 
            } else {
              this.$message.warning(res.message || res);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      computedExportedQy(records) {
              return this.floatSub(records.qty, records.exportedQty)
          },
      searchByImgNo() {
        if (!this.emsHead.id) {
          this.$message.warning('请先保存表头信息后进行操作！');
          return;
        }
        const params = {
          type: '1',
          emsId: this.emsHead.id,
          gNo: this.record.imgNo
        };
        this.loading = true;
        getAction(this.url.list, params)
          .then(res => {
            if (res.success) {
              const imgList = res.result.records || res.result;
              this.record.imgCopGno = imgList&&imgList.length>0?imgList[0].copGno:''
              this.record.imgGname = imgList&&imgList.length>0?imgList[0].gName:''
              this.record.imgHscode = imgList&&imgList.length>0?imgList[0].codet:''
              this.$forceUpdate() 
            } else {
              this.$message.warning(res.message || res);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, { ids: ids, type: '3' })
                .then(res => {
                  if (res.success) {
                    that.$message.success(res.message)
                    that.loadData(1)
                    that.record = {}
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
      /**
       ** 减法函数，用来得到精确的减法结果
       ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
       ** 调用：accSub(arg1,arg2)
       ** 返回值：arg1加上arg2的精确结果
       **/
      floatSub (arg1, arg2, n) {
        arg1 = !arg1 ? 0 : arg1
        arg2 = !arg2 ? 0 : arg2
        var r1, r2, m, n
        try { r1 = arg1.toString().split('.')[1].length } catch (e) { r1 = 0 }
        try { r2 = arg2.toString().split('.')[1].length } catch (e) { r2 = 0 }
        m = Math.pow(10, Math.max(r1, r2))
        // 动态控制精度长度
        if (!n) {
          n = (r1 >= r2) ? r1 : r2
        }
        return ((arg1 * m - arg2 * m) / m).toFixed(n)
      }
    }
  
  
  
  
  
  }
  
  
  </script>
  
  <style scoped lang='less'>
  @import '~@assets/less/common.less';
  
  </style>