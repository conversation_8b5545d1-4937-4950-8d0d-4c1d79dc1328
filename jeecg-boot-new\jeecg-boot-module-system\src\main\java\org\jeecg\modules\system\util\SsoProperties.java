package org.jeecg.modules.system.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SSO配置类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/1/17 14:17
 */
@Data
@Component
@ConfigurationProperties(prefix = "sso")
public class SsoProperties {
    private Server server = new Server();
    private boolean sloEnabled;
    private String client;
    private String secretKey;

    @Data
    public static class Server {
        private String url;
        private String authUrl;
        private String checkTicketUrl;
        private String sloUrl;
        private String getDataUrl;
    }
}
