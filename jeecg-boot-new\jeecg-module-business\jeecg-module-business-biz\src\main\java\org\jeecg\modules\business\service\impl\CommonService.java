package org.jeecg.modules.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.event.ReceiveEvent;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.IDecContainerService;
import org.jeecg.modules.business.service.IRecAlreadySubscribedService;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.common.constant.CommonConstant.I;
import static org.jeecg.modules.business.util.ApiUtil.getTime;
import static org.jeecg.modules.business.util.ApiUtil.md5;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;
import static org.jeecg.modules.business.util.PreApiSample.*;

/**
 * 共通service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/11/1 13:41
 */
@Slf4j
@Service
public class CommonService implements ApplicationEventPublisherAware {
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    private CommissionerMapper commissionerMapper;
    @Autowired
    private AiRelTableMapper aiRelTableMapper;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private ServiceLicMapper serviceLicMapper;
    private static final String URL_CQ_Subscribe = "https://api.jgsoft.com.cn:15555/open-api/sg/CqSubscribe"; // 船期信息订阅
    @Value("${justauth.third.url}")
    private String url;
    @Value("${ai.config.url.prod}")
    private String aiBaseUrl;
    private static final String URL_TDH_Subscribe = "https://api.jgsoft.com.cn:15555/open-api/sg/TdhSubscribe"; // 箱货节点订阅(提单号)
    private static final String URL_Container_Subscribe = "https://api.jgsoft.com.cn:15555/open-api/sg/ContainerSubscribe"; // 箱货节点订阅(提单号+箱号)
    private static String APP_KEY;
    @Autowired
    private IRecAlreadySubscribedService recAlreadySubscribedService;
    @Autowired
    private IDecContainerService decContainerService;
    private ApplicationEventPublisher applicationEventPublisher;
    /**
     * Set the ApplicationEventPublisher that this object runs in.
     * <p>Invoked after population of normal bean properties but before an init
     * callback like InitializingBean's afterPropertiesSet or a custom init-method.
     * Invoked before ApplicationContextAware's setApplicationContext.
     *
     * @param applicationEventPublisher event publisher to be used by this object
     */
    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Value("${api.auth.appKey}")
    public void setAppKey(String appKey) {
        this.APP_KEY = appKey;
    }

    /**
     * 获取租户企业名称
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/1 13:45
     */
    public String getTenantName(HttpServletRequest request) {
        String tenantName = null;
        try {
            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
            Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
            if (isNotEmpty(tenant.getResult())) {
                tenantName = tenant.getResult().getName();
            } else {
                List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoMapper.selectList(new LambdaQueryWrapper<EnterpriseInfo>()
                        .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
                if (isNotEmpty(enterpriseInfoList)) {
                    tenantName = enterpriseInfoList.get(0).getEnterpriseFullName();
                } else {
                    String accessToken = request.getHeader(X_ACCESS_TOKEN);
                    String o = (String) redisUtil.get(PREFIX_TENANT_TOKEN_OBJ + accessToken);
                    tenantName = (String) JSONObject.parseObject(o).get("name");
                }
            }
        } catch (Exception e) {
            log.error("获取租户名出现异常：" + e.getMessage());
        }
        return tenantName;
    }

    /**
     * 根据企业ID获取企业名称
     *
     * @param customerId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/4/3 13:30
     */
    public String getCustomerNameById(String customerId) {
        String tenantName = null;
        try {
            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
            Result<Tenant> tenant = sysBaseApi.getTenantById(customerId);
            if (isNotEmpty(tenant.getResult())) {
                tenantName = tenant.getResult().getName();
            }
        } catch (Exception e) {
            log.error("获取租户名出现异常：" + e.getMessage());
        }
        Commissioner commissioner = commissionerMapper.selectById(customerId);
        if (isNotEmpty(commissioner)) {
            tenantName = commissioner.getCommissionerFullName();
        }
        return tenantName;
    }

    /**
     * 根据企业ID获取企业名称
     *
     * @param customerId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/4/3 13:30
     */
    public String getCustomerNameById_(String customerId) {
        String tenantName = null;
        try {
            // 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
            Tenant tenant = commonMapper.getTenantById(customerId);
            if (isNotEmpty(tenant)) {
                tenantName = tenant.getName();
            }
        } catch (Exception e) {
            log.error("获取租户名出现异常：" + e.getMessage());
        }
        Commissioner commissioner = commissionerMapper.selectById(customerId);
        if (isNotEmpty(commissioner)) {
            tenantName = commissioner.getCommissionerFullName();
        }
        return tenantName;
    }

    /**
     * 获取不规则字典数据
     *
     * @return java.util.List<org.jeecg.modules.business.entity.dto.DictQuery>
     * <AUTHOR>
     * @date 2024/3/5 11:55
     */
    public List<DictQuery> listDictQuery(String table, String code, String name, String title, String suffix) {
        return commonMapper.listDictQuery(table, code, name, title, suffix);
    }
    /**
     * 获取典数据
     *
     * @return java.util.List<org.jeecg.modules.business.entity.dto.DictQuery>
     * <AUTHOR>
     * @date 2024/3/5 11:55
     */
    public List<DictQuery> listDict(String dictCode) {
        return commonMapper.listDict(dictCode);
    }

    /**
     * 第三方获取Token
     *
     * @param creditCode
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/5 12:10
     */
    public Result<?> getThirdToken(String creditCode) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<EnterpriseInfo> enterpriseInfos = enterpriseInfoMapper.selectList(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getUnifiedSocialCreditCode, creditCode)
                .eq(EnterpriseInfo::getIsThirdParts, true));
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (isEmpty(enterpriseInfos)) {
            return Result.error("该税号[" + creditCode + "]在系统中不存在，请检查!");
        }
        ServiceLic serviceLic = serviceLicMapper.selectOne(new LambdaQueryWrapper<ServiceLic>()
                .eq(ServiceLic::getCustomerId, enterpriseInfos.get(0).getId())
                .eq(ServiceLic::getIsWhite, true));
        if (isEmpty(serviceLic)) {
            return Result.error("该税号[" + creditCode + "]在系统中不存在许可，请检查!");
        }
        // 业务参数, -- 要用LinkedHashMap
        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("creditCode", creditCode);

        String accessKey = serviceLic.getAccessKey();
        String secretKey = serviceLic.getSecretKey();

        String timestamp = getTime();
        String nonce = getNonce();

        // 组装请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("accessKey", accessKey);
        headers.put("timestamp", timestamp);
        headers.put("nonce", nonce);
        String sign = null;
        try {
            sign = getLocalSign(paramMap, timestamp, nonce, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        headers.put("sign", sign);
        String result = HttpUtil.createGet(url + "/open-api/thirdParty/v1/getToken")
                .addHeaders(headers)
                .form(paramMap)
                .execute()
                .body();
        log.info("[result]:{}", result);
        String token = null;
        JSONObject jsonObject;
        try {
            jsonObject = JSON.parseObject(result);
            if (jsonObject.getBoolean("success")) {
                JSONObject resultJson = jsonObject.getJSONObject("result");
                token = resultJson.getString("token");
            } else {
                log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
            }
        } catch (Exception e) {
            log.error("[result]:{}", result);
        }
        return Result.ok(token);
    }

    /**
     * 获取日志状态记录
     *
     * @param sourceId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/16 15:18
     */
    public Result<?> getStatusRecords(String logType, String sourceId) {
        List<AiLog> logDTOList = commonMapper.getStatusRecords(logType, sourceId);
        return Result.ok(logDTOList);
    }

    /**
     * 文档逻辑校验
     *
     * @param decId
     * @param isQz
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/4/22 14:33
     */
    public Result<?> verify(String decId, String isQz) {
        // 获取当前登录用户
        LoginUser sysUser;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            sysUser = new LoginUser();
            sysUser.setUsername("AI");
        }
        AiRelTable aiRelTable = aiRelTableMapper.selectOne(new LambdaQueryWrapper<AiRelTable>()
                .eq(AiRelTable::getType, "4")
                .eq(AiRelTable::getDecId, decId).last("limit 1"));
        if (isNotEmpty(aiRelTable) && isNotBlank(aiRelTable.getCheckContent()) && !"1".equals(isQz)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(aiRelTable.getCheckContent());
                return Result.ok(jsonArray);
            } catch (Exception e) {
                log.error("[文档逻辑校验]服务调用结果解析异常：{}", e.getMessage());
            }
            return Result.ok(aiRelTable.getCheckContent());
        } else {
            List<AiLog> logDTOList = commonMapper.getStatusRecords("521", decId);
            if (isNotEmpty(logDTOList)) {
                List<AiLog> logDTOList1 = logDTOList.stream().filter(logDTO ->
                        "2".equals(logDTO.getRequestType()) && isNotBlank(logDTO.getLogContent())).collect(Collectors.toList());
                if (isNotEmpty(logDTOList1)) {
                    String logContent = logDTOList1.get(0).getLogContent();
                    if (isNotBlank(logContent)) {
                        JSONObject jsonObject = JSON.parseObject(logContent);
                        JSONArray jsonList = jsonObject.getJSONArray("detail_format");
                        String pythonServiceUrl = aiBaseUrl + "/api/verify";
                        HttpRequest request = HttpUtil.createPost(pythonServiceUrl);
                        request.header("Content-Type", "application/json");
                        // 2025/4/24 16:43@ZHANGCHAO 追加/变更/完善：加鉴权了！！
                        request.header("timestamp", getHeadersForAi().get("timestamp"));
                        request.header("sign", getHeadersForAi().get("sign"));
                        JSONObject paramJson = new JSONObject();
                        paramJson.put("detail_format_list", jsonList);
                        request.body(paramJson.toString());
                        int readTimeout = 180000;
                        request.timeout(readTimeout);
                        HttpResponse response = request.execute();
                        String responseContent = response.body();
                        log.info("[文档逻辑校验]服务调用结果：{}", responseContent);
                        if (isNotBlank(responseContent)) {
                            try {
                                JSONArray jsonArray = JSONArray.parseArray(responseContent);
                                try {
                                    aiRelTableMapper.delete(new LambdaQueryWrapper<AiRelTable>()
                                            .eq(AiRelTable::getType, "4")
                                            .eq(AiRelTable::getDecId, decId));
                                    aiRelTable = new AiRelTable();
                                    aiRelTable.setType("4");
                                    aiRelTable.setCheckContent(responseContent);
                                    aiRelTable.setDecId(decId);
                                    aiRelTable.setCreateBy(sysUser.getUsername());
                                    aiRelTable.setCreateDate(new Date());
                                    aiRelTableMapper.insert(aiRelTable);
                                } catch (Exception e) {
                                    log.error("[文档逻辑校验]保存服务调用结果出现异常：{}", e.getMessage());
                                }
                                return Result.ok(jsonArray);
                            } catch (Exception e) {
                                log.error("[文档逻辑校验]服务调用结果解析异常：{}", e.getMessage());
                            }
                        }
                        return Result.ok(responseContent);
                    }
                }
            }
        }
        return Result.error("未查询到文档逻辑校验结果");
    }

    /**
     * 订阅信息
     * 1. 船期信息订阅 - 根据英文船名、航次订阅船期信息
     * 2. 箱货节点订阅 - 根据提单号自动获取箱号，并推送箱子动态信息
     * 3. 同时订阅船期和箱货节点信息
     *
     * @param decHead 报关单表头对象
     * @param type 订阅类型：1-船期信息，2-箱货节点，3-两者都订阅
     * @return void
     * 2025/4/3 @ZHANGCHAO
     */
    public void subscribeSchedule(DecHead decHead, Integer type) {
        if (type == null) {
            log.info("订阅类型不能为空");
            return;
        }
        if (type == 1 || type == 3) {
            subscribeShipInfo(decHead);
        }
        if (type == 2 || type == 3) {
            subscribeContainerInfo(decHead);
        }
        if (type != 1 && type != 2 && type != 3) {
            log.info("未知的订阅类型:{}", type);
        }
    }

    /**
     * 订阅船期信息
     * @param decHead 报关单表头对象
     */
    private void subscribeShipInfo(DecHead decHead) {
        if (!"2".equals(decHead.getShipTypeCode()) || isBlank(decHead.getShipName()) || isBlank(decHead.getVoyage())) {
            log.debug("【订阅船期信息】参数不符合订阅条件，跳过订阅");
            return;
        }
        long count = recAlreadySubscribedService.count(new LambdaQueryWrapper<RecAlreadySubscribed>()
                .eq(RecAlreadySubscribed::getShipName, decHead.getShipName())
                .eq(RecAlreadySubscribed::getVoy, decHead.getVoyage()));
        if (count > 0) {
            log.info("【订阅船期信息】该船期信息{}已经订阅过了，跳过订阅", decHead.getShipName() + " - " + decHead.getVoyage());
            return;
        }
        try {
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("user", APP_KEY);
            jsonMap.put("enShipName", decHead.getShipName());
            jsonMap.put("voy", decHead.getVoyage());
            jsonMap.put("nodeType", I.equals(decHead.getIeFlag()) ? "1" : "2");

            log.info("订阅船期信息请求参数：{}", JSON.toJSONString(jsonMap));
            String result = sendOpenApi(URL_CQ_Subscribe, JSON.toJSONString(jsonMap));
            log.info("【订阅船期信息】请求结果：{}", result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (isNotEmpty(jsonObject) && isNotEmpty(jsonObject.getJSONObject("data"))) {
                boolean ok = jsonObject.getJSONObject("data").getBooleanValue("ok");
                if (ok) {
                    // 获取当前登录用户
                    LoginUser sysUser;
                    try {
                        sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                    } catch (Exception e) {
                        sysUser = new LoginUser();
                        sysUser.setUsername("unknow");
                    }
                    log.info("【订阅船期信息】订阅成功，开始存库...");
                    RecAlreadySubscribed recAlreadySubscribed = new RecAlreadySubscribed();
                    recAlreadySubscribed.setShipName(decHead.getShipName());
                    recAlreadySubscribed.setVoy(decHead.getVoyage());
                    recAlreadySubscribed.setType(1);
                    recAlreadySubscribed.setCreateBy(sysUser.getUsername());
                    recAlreadySubscribed.setCreateDate(new Date());
                    recAlreadySubscribedService.save(recAlreadySubscribed);
                }
            }
        } catch (Exception e) {
            log.error("【订阅船期信息】请求异常：{}", e.getMessage());
        }
    }

    /**
     * 订阅箱货节点信息
     * @param decHead 报关单表头对象
     */
    private void subscribeContainerInfo(DecHead decHead) {
        if (isBlank(decHead.getBillCode())) {
            log.debug("【箱货节点订阅(提单号)】参数不符合订阅条件，跳过订阅");
            return;
        }
        // 2025/4/11 09:50@ZHANGCHAO 追加/变更/完善：又加了个提单号+箱号的订阅接口！！
        List<DecContainer> decContainers = decContainerService.list(new QueryWrapper<DecContainer>().lambda()
                .eq(DecContainer::getDecId, decHead.getId()));
        // 现在有已知的集装箱
        if (isNotEmpty(decContainers)) {
            List<String> containerIds = decContainers.stream().distinct().map(DecContainer::getContainerId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
            if (isNotEmpty(containerIds)) {
                List<String> newContainerIds = new ArrayList<>();
                containerIds.forEach(containerId -> {
                    long count = recAlreadySubscribedService.count(new LambdaQueryWrapper<RecAlreadySubscribed>()
                            .eq(RecAlreadySubscribed::getBillCode, decHead.getBillCode())
                            .eq(RecAlreadySubscribed::getContainerNo, containerId));
                    if (count > 0) {
                        log.info("【箱货节点订阅(提单号+箱号)】提单号{}箱号{}已订阅过，跳过订阅", decHead.getBillCode(), containerId);
                    } else {
                        newContainerIds.add(containerId);
                    }
                });
                if (isNotEmpty(newContainerIds)) {
                    try {
                        Map<String, Object> jsonMap = new LinkedHashMap<>();
                        jsonMap.put("user", APP_KEY);
                        jsonMap.put("tdh", decHead.getBillCode());
                        jsonMap.put("xh", CollUtil.join(newContainerIds, ","));
                        jsonMap.put("nodeType", I.equals(decHead.getIeFlag()) ? "1" : "2");

                        log.info("箱货节点订阅(提单号+箱号)请求参数：{}", JSON.toJSONString(jsonMap));
                        String result = sendOpenApi(URL_Container_Subscribe, JSON.toJSONString(jsonMap));
                        log.info("【箱货节点订阅(提单号+箱号)】请求结果：{}", result);
                        JSONObject jsonObject = JSONObject.parseObject(result);
                        if (isNotEmpty(jsonObject) && isNotEmpty(jsonObject.getJSONObject("data"))) {
                            boolean ok = jsonObject.getJSONObject("data").getBooleanValue("ok");
                            if (ok) {
                                // 获取当前登录用户
                                LoginUser sysUser;
                                try {
                                    sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                                } catch (Exception e) {
                                    sysUser = new LoginUser();
                                    sysUser.setUsername("unknow");
                                }
                                log.info("【箱货节点订阅(提单号+箱号)】订阅成功，开始存库...");
                                LoginUser finalSysUser = sysUser;
                                newContainerIds.forEach(containerId -> {
                                    RecAlreadySubscribed recAlreadySubscribed = new RecAlreadySubscribed();
                                    recAlreadySubscribed.setBillCode(decHead.getBillCode());
                                    recAlreadySubscribed.setContainerNo(containerId);
                                    recAlreadySubscribed.setType(2);
                                    recAlreadySubscribed.setCreateBy(finalSysUser.getUsername());
                                    recAlreadySubscribed.setCreateDate(new Date());
                                    recAlreadySubscribedService.save(recAlreadySubscribed);
                                });
                            }
                        }
                    } catch (Exception e) {
                        log.error("【箱货节点订阅(提单号+箱号)】请求异常：{}", e.getMessage());
                    }
                }
            } else {
                log.info("【箱货节点订阅(提单号+箱号)】提单号{}有集装箱数据但是没有集装箱号，跳过订阅", decHead.getBillCode());
            }
            // 暂时没有集装箱，只根据原来的提单号去订阅
        } else {
            long count = recAlreadySubscribedService.count(new LambdaQueryWrapper<RecAlreadySubscribed>()
                    .eq(RecAlreadySubscribed::getBillCode, decHead.getBillCode()));
            if (count > 0) {
                log.info("【箱货节点订阅(提单号)】提单号{}已订阅过，跳过订阅", decHead.getBillCode());
                return;
            }
            try {
                Map<String, Object> jsonMap = new LinkedHashMap<>();
                jsonMap.put("user", APP_KEY);
                jsonMap.put("tdh", decHead.getBillCode());
                jsonMap.put("nodeType", I.equals(decHead.getIeFlag()) ? "1" : "2");

                log.info("箱货节点订阅请求参数：{}", JSON.toJSONString(jsonMap));
                String result = sendOpenApi(URL_TDH_Subscribe, JSON.toJSONString(jsonMap));
                log.info("【箱货节点订阅(提单号)】请求结果：{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (isNotEmpty(jsonObject) && isNotEmpty(jsonObject.getJSONObject("data"))) {
                    boolean ok = jsonObject.getJSONObject("data").getBooleanValue("ok");
                    if (ok) {
                        // 获取当前登录用户
                        LoginUser sysUser;
                        try {
                            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                        } catch (Exception e) {
                            sysUser = new LoginUser();
                            sysUser.setUsername("unknow");
                        }
                        log.info("【箱货节点订阅(提单号)】订阅成功，开始存库...");
                        RecAlreadySubscribed recAlreadySubscribed = new RecAlreadySubscribed();
                        recAlreadySubscribed.setBillCode(decHead.getBillCode());
                        recAlreadySubscribed.setType(2);
                        recAlreadySubscribed.setCreateBy(sysUser.getUsername());
                        recAlreadySubscribed.setCreateDate(new Date());
                        recAlreadySubscribedService.save(recAlreadySubscribed);
                    }
                }
            } catch (Exception e) {
                log.error("【箱货节点订阅(提单号)】请求异常：{}", e.getMessage());
            }
        }
    }

    /**
     * 推送数据重新执行
     *
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2025/4/7 15:01
     */
    public Result<?> retrieveJob() {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        try {
            List<AiLog> logDTOs = commonMapper.listLog();
            if (isEmpty(logDTOs)) {
                return Result.ok("没有需要重新执行的推送任务");
            }
            int processedCount = 0;
            ObjectMapper objectMapper = new ObjectMapper();
            for (AiLog logDTO : logDTOs) {
                if (isBlank(logDTO.getRequestParam())) {
                    continue;
                }
                processedCount += processLogEntry(logDTO, objectMapper);
            }
            return Result.ok("成功重新执行 " + processedCount + " 个推送任务");
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            return Result.error("重新执行推送任务时发生系统异常: " + e.getMessage());
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
    }

    /**
     * 处理单个日志条目
     */
    private int processLogEntry(AiLog logDTO, ObjectMapper objectMapper) {
        try {
            Map<String, Object> params = parseRequestParams(logDTO.getRequestParam(), objectMapper);
            if (params == null) {
                return 0;
            }
            publishReceiveEventBasedOnLogType(logDTO, params);
            return 1;
        } catch (Exception e) {
            log.error("【推送数据重新执行】处理日志ID {} 异常：{}", logDTO.getId(), e.getMessage());
            return 0;
        }
    }

    /**
     * 解析请求参数
     */
    private Map<String, Object> parseRequestParams(String requestParam, ObjectMapper objectMapper) {
        try {
            Map<String, Object> map = objectMapper.readValue(
                    requestParam,
                    new TypeReference<Map<String, Object>>() {}
            );
            log.debug("【推送数据重新执行】转换后的数据为：{}", map);
            return map;
        } catch (Exception e) {
            log.warn("【推送数据重新执行】参数解析失败，日志参数: {}", requestParam);
            return null;
        }
    }

    /**
     * 获取鉴权请求头
     *
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2025/4/24 16:56
     */
    private Map<String, String> getHeadersForAi() {
        String timestamp = getTime();
        // TODO 先写死吧
        String secretKey = "5a8a7b07becf6fa4cd4db280f2979a1a7e5f20b18b6e99a86a8d8748f124d0d0";
        Map<String, String> param = new HashMap<>();
        param.put("timestamp", timestamp);
        param.put("sign", md5(secretKey + timestamp).toUpperCase());
        return param;
    }

    /**
     * 根据日志类型发布事件
     */
    private void publishReceiveEventBasedOnLogType(AiLog logDTO, Map<String, Object> params) {
        Integer eventType = null;
        switch (logDTO.getLogType()) {
            case LOG_TYPE_3:
                eventType = 4;
                break;
            case LOG_TYPE_4:
                eventType = 1;
                break;
            case LOG_TYPE_5:
                eventType = 2;
                break;
            case LOG_TYPE_6:
                eventType = 3;
                break;
            default:
                break;
        }
        if (eventType != null) {
            applicationEventPublisher.publishEvent(
                    new ReceiveEvent(logDTO.getId(), params, eventType)
            );
        }
    }

}
