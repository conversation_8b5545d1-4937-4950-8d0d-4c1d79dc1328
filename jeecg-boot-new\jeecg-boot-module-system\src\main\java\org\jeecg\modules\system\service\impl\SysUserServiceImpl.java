package org.jeecg.modules.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.lettuce.core.dynamic.annotation.Param;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysUserCacheInfo;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.controller.SysUserController;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.mapper.*;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.model.SysUserSysTenantModel;
import org.jeecg.modules.system.service.*;
import org.jeecg.modules.system.util.ZCSUtil;
import org.jeecg.modules.system.vo.SysUserDepVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static org.jeecg.modules.system.util.SsoRequestUtil.requestPost;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * @Author: scott
 * @Date: 2018-12-20
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

	@Autowired
	private SysUserMapper userMapper;
	@Autowired
	private SysPermissionMapper sysPermissionMapper;
	@Autowired
	private SysUserRoleMapper sysUserRoleMapper;
	@Autowired
	private SysUserDepartMapper sysUserDepartMapper;
	@Autowired
	private SysDepartMapper sysDepartMapper;
	@Autowired
	private SysRoleMapper sysRoleMapper;
	@Autowired
	private SysDepartRoleUserMapper departRoleUserMapper;
	@Autowired
	private SysDepartRoleMapper sysDepartRoleMapper;
	@Resource
	private BaseCommonService baseCommonService;
	@Autowired
	private SysThirdAccountMapper sysThirdAccountMapper;
	@Autowired
	ThirdAppWechatEnterpriseServiceImpl wechatEnterpriseService;
	@Autowired
	ThirdAppDingtalkServiceImpl dingtalkService;
	@Autowired
	FeeItemServiceImpl feeItemService;
	@Autowired
	FeeItemBackupServiceImpl feeItemBackupService;
	@Autowired
	IRateInfoService rateInfoService;
	@Autowired
	IErpCurrenciesService erpCurrenciesService;
	@Autowired
	private ISysTenantService sysTenantService;
	@Autowired
	private IEnterpriseInfoService enterpriseInfoService;
	@Autowired
	@Lazy
	private RedisUtil redisUtil;
	@Value("${sso.api.sync-user-enterprise}")
	private String syncUserUrl;

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword) {
        SysUser user = userMapper.getUserByName(username);
        String passwordEncode = PasswordUtil.encrypt(username, oldpassword, user.getSalt());
        if (!user.getPassword().equals(passwordEncode)) {
            return Result.error("旧密码输入错误!");
        }
        if (oConvertUtils.isEmpty(newpassword)) {
            return Result.error("新密码不允许为空!");
        }
        if (!newpassword.equals(confirmpassword)) {
            return Result.error("两次输入密码不一致!");
        }
        String password = PasswordUtil.encrypt(username, newpassword, user.getSalt());
		// 2025/2/28 14:09@ZHANGCHAO 追加/变更/完善：同时更新最后修改时间和清空备注！！
        this.userMapper.update(new SysUser().setPassword(password).setLastPasswordChangeDate(new Date()).setRemark(""),
				new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
        return Result.ok("密码重置成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
	@Transactional(rollbackFor = Exception.class)
    public Result<?> changePassword(SysUser sysUser) throws Exception {
		// 设置忽略租户插件
		InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
    	SysUser oldUser = getById(sysUser.getId());

        String salt = oConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String password = sysUser.getPassword();
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
        sysUser.setPassword(passwordEncode);
		// 2025/2/28 14:09@ZHANGCHAO 追加/变更/完善：同时更新最后修改时间和清空备注！！
		sysUser.setLastPasswordChangeDate(new Date());
		sysUser.setRemark("");
        this.userMapper.updateById(sysUser);
		// 关闭忽略策略
		InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok("密码修改成功!");
    }

    @Override
    @CacheEvict(value={CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteUser(String userId) throws Exception {
		// 租户ID
//		if(!TenantContext.getTenant().equals("0")){
//			SysTenant sysTenant = sysTenantService.getById(TenantContext.getTenant());
//
//			if(sysTenant != null){
//				SysUser sysUser = this.getById(userId);
//				if(sysTenant.getType().contains("1") && !sysUser.getZcsPerm().equals("")){
//
//					EnterpriseInfo enterpriseInfo = new EnterpriseInfo();
//					enterpriseInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
//					List<EnterpriseInfo> listEnter = enterpriseInfoService.getCollectionEnterpriseList(enterpriseInfo);
//					String getUnifiedSocialCreditCode = "";
//					if(listEnter.size() > 0){
//						getUnifiedSocialCreditCode = listEnter.get(0).getUnifiedSocialCreditCode();
//					}
//					Boolean pushResult = ZCSUtil.registerthirdusers(sysUser.getPassword(),sysUser,sysTenant,getUnifiedSocialCreditCode,"3");
//
//					if(!pushResult){
//						throw new Exception();
//					}
//				}
//			}
//		}
		SysUser sysUser = this.getById(userId);
		if (isNotEmpty(sysUser) && isNotBlank(sysUser.getUsername())) {
			String loginUserKey = CacheConstant.SYS_USERS_CACHE + "::" + sysUser.getUsername();
			if (redisUtil.hasKey(loginUserKey)) {
				redisUtil.del(loginUserKey);
			}
		}

        //1.删除用户
        this.removeById(userId);

		return false;
	}

	@Override
    @CacheEvict(value={CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteBatchUsers(String userIds) {
		//1.删除用户
		this.removeByIds(Arrays.asList(userIds.split(",")));
		return false;
	}

	@Override
	public SysUser getUserByName(String username) {
		return userMapper.getUserByName(username);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addUserWithRole(SysUser user, String roles) {
		this.save(user);
		if(oConvertUtils.isNotEmpty(roles)) {
			String[] arr = roles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), roleId);
				sysUserRoleMapper.insert(userRole);
			}
		}
	}

	@Override
	@CacheEvict(value= {CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	@Transactional(rollbackFor = Exception.class)
	public void editUserWithRole(SysUser user, String roles) {
		this.updateById(user);
		//先删后加
		sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
		if(oConvertUtils.isNotEmpty(roles)) {
			String[] arr = roles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), roleId);
				sysUserRoleMapper.insert(userRole);
			}
		}
	}


	@Override
	public List<String> getRole(String username) {
		return sysUserRoleMapper.getRoleByUserName(username);
	}

	/**
	 * 通过用户名获取用户角色集合
	 * @param username 用户名
     * @return 角色集合
	 */
	@Override
	public Set<String> getUserRolesSet(String username) {
		// 查询用户拥有的角色集合
		List<String> roles = sysUserRoleMapper.getRoleByUserName(username);
		log.info("-------通过数据库读取用户拥有的角色Rules------username： " + username + ",Roles size: " + (roles == null ? 0 : roles.size()));
		return new HashSet<>(roles);
	}

	/**
	 * 通过用户名获取用户权限集合
	 *
	 * @param username 用户名
	 * @return 权限集合
	 */
	@Override
	public Set<String> getUserPermissionsSet(String username) {
		Set<String> permissionSet = new HashSet<>();
		List<SysPermission> permissionList = sysPermissionMapper.queryByUser(username);
		for (SysPermission po : permissionList) {
//			// TODO URL规则有问题？
//			if (oConvertUtils.isNotEmpty(po.getUrl())) {
//				permissionSet.add(po.getUrl());
//			}
			if (oConvertUtils.isNotEmpty(po.getPerms())) {
				permissionSet.add(po.getPerms());
			}
		}
		log.info("-------通过数据库读取用户拥有的权限Perms------username： "+ username+",Perms size: "+ (permissionSet==null?0:permissionSet.size()) );
		return permissionSet;
	}

	/**
	 * 升级SpringBoot2.6.6,不允许循环依赖
	 * @author:qinfeng
	 * @update: 2022-04-07
	 * @param username
	 * @return
	 */
	@Override
	@Cacheable(cacheNames=CacheConstant.SYS_USERS_CACHE, key="#username")
	public SysUserCacheInfo getCacheUser(String username) {
		SysUserCacheInfo info = new SysUserCacheInfo();
		info.setOneDepart(true);
		if(oConvertUtils.isEmpty(username)) {
			return null;
		}

		//查询用户信息
		SysUser sysUser = userMapper.getUserByName(username);
		if(sysUser!=null) {
			info.setSysUserCode(sysUser.getUsername());
			info.setSysUserName(sysUser.getRealname());
			info.setSysOrgCode(sysUser.getOrgCode());
		}

		//多部门支持in查询
		List<SysDepart> list = sysDepartMapper.queryUserDeparts(sysUser.getId());
		List<String> sysMultiOrgCode = new ArrayList<String>();
		if(list==null || list.size()==0) {
			//当前用户无部门
			//sysMultiOrgCode.add("0");
		}else if(list.size()==1) {
			sysMultiOrgCode.add(list.get(0).getOrgCode());
		}else {
			info.setOneDepart(false);
			for (SysDepart dpt : list) {
				sysMultiOrgCode.add(dpt.getOrgCode());
			}
		}
		info.setSysMultiOrgCode(sysMultiOrgCode);

		return info;
	}

    /**
     * 根据部门Id查询
     * @param page
     * @param departId 部门id
     * @param username 用户账户名称
     * @return
     */
	@Override
	public IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId,String username) {
		return userMapper.getUserByDepId(page, departId,username);
	}

	@Override
	public IPage<SysUser> getUserByDepIds(Page<SysUser> page, List<String> departIds, String username) {
		return userMapper.getUserByDepIds(page, departIds,username);
	}

	@Override
	public Map<String, String> getDepNamesByUserIds(List<String> userIds) {
		List<SysUserDepVo> list = this.baseMapper.getDepNamesByUserIds(userIds);

		Map<String, String> res = new HashMap(5);
		list.forEach(item -> {
					if (res.get(item.getUserId()) == null) {
						res.put(item.getUserId(), item.getDepartName());
					} else {
						res.put(item.getUserId(), res.get(item.getUserId()) + "," + item.getDepartName());
					}
				}
		);
		return res;
	}

	@Override
	public IPage<SysUser> getUserByDepartIdAndQueryWrapper(Page<SysUser> page, String departId, QueryWrapper<SysUser> queryWrapper) {
		LambdaQueryWrapper<SysUser> lambdaQueryWrapper = queryWrapper.lambda();

		lambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.inSql(SysUser::getId, "SELECT user_id FROM sys_user_depart WHERE dep_id = '" + departId + "'");

        return userMapper.selectPage(page, lambdaQueryWrapper);
	}

	@Override
	public IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUser userParams, IPage page) {
		List<SysUserSysDepartModel> list = baseMapper.getUserByOrgCode(page, orgCode, userParams);
		Integer total = baseMapper.getUserByOrgCodeTotal(orgCode, userParams);

		IPage<SysUserSysDepartModel> result = new Page<>(page.getCurrent(), page.getSize(), total);
		result.setRecords(list);

		return result;
	}

    /**
     * 根据角色Id查询
     * @param page
     * @param roleId 角色id
     * @param username 用户账户名称
     * @return
     */
	@Override
	public IPage<SysUser> getUserByRoleId(Page<SysUser> page, String roleId, String username,String tenantId) {
		return userMapper.getUserByRoleId(page,roleId,username,tenantId);
	}


	@Override
	@CacheEvict(value= {CacheConstant.SYS_USERS_CACHE}, key="#username")
	public void updateUserDepart(String username,String orgCode) {
		baseMapper.updateUserDepart(username, orgCode);
	}


	@Override
	public SysUser getUserByPhone(String phone) {
		return userMapper.getUserByPhone(phone);
	}


	@Override
	public SysUser getUserByEmail(String email) {
		return userMapper.getUserByEmail(email);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addUserWithDepart(SysUser user, String selectedParts) {
//		this.save(user);  //保存角色的时候已经添加过一次了
		if(oConvertUtils.isNotEmpty(selectedParts)) {
			String[] arr = selectedParts.split(",");
			for (String deaprtId : arr) {
				SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
				sysUserDepartMapper.insert(userDeaprt);
			}
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	@CacheEvict(value={CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	public void editUserWithDepart(SysUser user, String departs) {
        //更新角色的时候已经更新了一次了，可以再跟新一次
		this.updateById(user);
		String[] arr = {};
		if(oConvertUtils.isNotEmpty(departs)){
			arr = departs.split(",");
		}
		//查询已关联部门
		List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
		if(userDepartList != null && userDepartList.size()>0){
			for(SysUserDepart depart : userDepartList ){
				//修改已关联部门删除部门用户角色关系
				if(!Arrays.asList(arr).contains(depart.getDepId())){
					List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(
							new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId,depart.getDepId()));
					List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
					if(roleIds != null && roleIds.size()>0){
						departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId())
								.in(SysDepartRoleUser::getDroleId,roleIds));
					}
				}
			}
		}
		//先删后加
		sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
		if(oConvertUtils.isNotEmpty(departs)) {
			for (String departId : arr) {
				SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
				sysUserDepartMapper.insert(userDepart);
			}
		}
	}

	/**
	   * 校验用户是否有效
	 * @param sysUser
	 * @return
	 */
	@Override
	public Result<?> checkUserIsEffective(SysUser sysUser) {
		Result<?> result = new Result<Object>();
		//情况1：根据用户信息查询，该用户不存在
		if (sysUser == null) {
			result.error500("该用户不存在，请注册");
			baseCommonService.addLog("用户登录失败，用户不存在！", CommonConstant.LOG_TYPE_1, null);
			return result;
		}
		//情况2：根据用户信息查询，该用户已注销
		//update-begin---author:王帅   Date:20200601  for：if条件永远为falsebug------------
		if (CommonConstant.DEL_FLAG_1.equals(sysUser.getDelFlag())) {
		//update-end---author:王帅   Date:20200601  for：if条件永远为falsebug------------
			baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已注销！", CommonConstant.LOG_TYPE_1, null);
			result.error500("该用户已注销");
			return result;
		}
		//情况3：根据用户信息查询，该用户已冻结
		if (CommonConstant.USER_FREEZE.equals(sysUser.getStatus())) {
			baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已冻结！", CommonConstant.LOG_TYPE_1, null);
			result.error500("该用户已冻结");
			return result;
		}
		return result;
	}

	@Override
	public List<SysUser> queryLogicDeleted() {
		return this.queryLogicDeleted(null);
	}

	@Override
	public List<SysUser> queryLogicDeleted(LambdaQueryWrapper<SysUser> wrapper) {
		if (wrapper == null) {
			wrapper = new LambdaQueryWrapper<>();
		}
		wrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_1);
		return userMapper.selectLogicDeleted(wrapper);
	}

	@Override
	@CacheEvict(value={CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	public boolean revertLogicDeleted(List<String> userIds, SysUser updateEntity) {
		String ids = String.format("'%s'", String.join("','", userIds));
		return userMapper.revertLogicDeleted(ids, updateEntity) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeLogicDeleted(List<String> userIds) throws Exception {
		// 设置忽略租户插件
		InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
		String ids = String.format("'%s'", String.join("','", userIds));
		// 更新用户del_flag = 1
		this.deleteUser(userIds.get(0));

		// 1. 删除用户
		int line = userMapper.deleteLogicDeleted(ids);
		// 2. 删除用户部门关系
		line += sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getUserId, userIds));
		//3. 删除用户角色关系
		line += sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, userIds));
		//4.同步删除第三方App的用户
		try {
			dingtalkService.removeThirdAppUser(userIds);
			wechatEnterpriseService.removeThirdAppUser(userIds);
		} catch (Exception e) {
			log.error("同步删除第三方App的用户失败：", e);
		}
		//5. 删除第三方用户表（因为第4步需要用到第三方用户表，所以在他之后删）
		line += sysThirdAccountMapper.delete(new LambdaQueryWrapper<SysThirdAccount>().in(SysThirdAccount::getSysUserId, userIds));
		// 关闭忽略策略
		InterceptorIgnoreHelper.clearIgnoreStrategy();
		return line != 0;
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNullPhoneEmail() {
        userMapper.updateNullByEmptyString("email");
        userMapper.updateNullByEmptyString("phone");
        return true;
    }

	@Override
	public void saveThirdUser(SysUser sysUser) {
		//保存用户
		String userid = UUIDGenerator.generate();
		sysUser.setId(userid);
		baseMapper.insert(sysUser);
		//获取第三方角色
		SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, "third_role"));
		//保存用户角色
		SysUserRole userRole = new SysUserRole();
		userRole.setRoleId(sysRole.getId());
		userRole.setUserId(userid);
		sysUserRoleMapper.insert(userRole);
	}

	@Override
	public List<SysUser> queryByDepIds(List<String> departIds, String username) {
		return userMapper.queryByDepIds(departIds,username);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveUser(SysUser user, String selectedRoles, String selectedDeparts) {
		if (isBlank(user.getTenantId())) {
			user.setTenantId("0");
			user.setRelTenantIds("0");
		}
		// 设置忽略租户插件
		InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
		if (isBlank(user.getUserType())) {
			user.setUserTypeRel("");
			user.setUserTypeRelStr("");
		}
		// 2025/2/28 14:00@ZHANGCHAO 追加/变更/完善：记录修改时间！！
		user.setLastPasswordChangeDate(new Date());
		//step.1 保存用户
		this.save(user);
		// 2025/5/20 11:30@ZHANGCHAO 追加/变更/完善：推送到SSO认证中心！！
		ThreadUtil.execAsync(() -> {
			SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, user.getUsername()).last("LIMIT 1"));
			JSONObject paramJson = new JSONObject();
			paramJson.put("userId", isNotEmpty(sysUser) ? sysUser.getId() : user.getId());
			paramJson.put("username", isNotEmpty(sysUser) ? sysUser.getUsername() : user.getUsername());
			paramJson.put("password", isNotEmpty(sysUser) ? sysUser.getPassword() : user.getPassword());
			paramJson.put("salt", isNotEmpty(sysUser) ? sysUser.getSalt() : user.getSalt());
			paramJson.put("phone", isNotEmpty(sysUser) ? sysUser.getPhone() : user.getPhone());
			paramJson.put("adminFlag", 0);
			String createBy = isNotEmpty(sysUser) ? sysUser.getCreateBy() : user.getCreateBy();
			if (isNotBlank(createBy) && "admin".equals(createBy)) {
				paramJson.put("adminFlag", 1);
			}
			// 设置忽略租户插件
			InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
			EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
					.eq(EnterpriseInfo::getTenantId, isNotEmpty(sysUser) ? sysUser.getTenantId() : user.getTenantId()));
			// 关闭忽略策略
			InterceptorIgnoreHelper.clearIgnoreStrategy();
			if (isNotEmpty(enterpriseInfo)) {
				paramJson.put("enterpriseId", enterpriseInfo.getId());
				paramJson.put("enterpriseName", enterpriseInfo.getEnterpriseFullName());
				paramJson.put("creditCode", enterpriseInfo.getUnifiedSocialCreditCode());
			}
			log.info("[editUser]推送到SSO认证中心用户和企业信息参数：{}", paramJson.toString());
			String result = requestPost(syncUserUrl, paramJson.toString());
            log.info("推送到SSO认证中心用户和企业信息返回结果：{}", result);
		});
		//step.2 保存角色
		if(oConvertUtils.isNotEmpty(selectedRoles)) {
			String[] arr = selectedRoles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), roleId);
				sysUserRoleMapper.insert(userRole);
			}
		}
		//step.3 保存所属部门
		if(oConvertUtils.isNotEmpty(selectedDeparts)) {
			String[] arr = selectedDeparts.split(",");
			for (String deaprtId : arr) {
				SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
				sysUserDepartMapper.insert(userDeaprt);
			}
		}
		// 关闭忽略策略
		InterceptorIgnoreHelper.clearIgnoreStrategy();
//		// 保存费用项目表
//		List<FeeItem> feeItemList = feeItemService.list(new LambdaQueryWrapper<FeeItem>().eq(FeeItem::getTenantId, Integer.parseInt(tenantId)));
//		if(feeItemList.size() == 0){
//			// 取出备份费用表
//			List<FeeItemBackup> list = feeItemBackupService.list();
//			for(FeeItemBackup feeItemBackup : list){
//				FeeItem feeItem = new FeeItem();
//				BeanUtils.copyProperties(feeItemBackup, feeItem);
//				feeItem.setId(null);
//				feeItem.setTenantId(Integer.parseInt(tenantId));
//				feeItemService.save(feeItem);
//			}
//		}
		// 保存汇率表
//		List<RateInfo> rateInfoList = rateInfoService.list(new LambdaQueryWrapper<RateInfo>().eq(RateInfo::getTenantId, Integer.parseInt(tenantId)));
//		if(rateInfoList.size() == 0){
//			// 取出常用数据库币种
//			List<ErpCurrencies> list = erpCurrenciesService.list();
//			for(ErpCurrencies currencies : list){
//				RateInfo rateInfo = new RateInfo();
//				rateInfo.setTenantId(Integer.parseInt(tenantId));
//				rateInfo.setCreateTime(new Date());
//				rateInfo.setUpdateTime(new Date());
//				rateInfo.setRateDate(new Date());
//				rateInfo.setCurrencyCode(currencies.getCode());
//				rateInfo.setCurrency(currencies.getCurrency());
//				rateInfo.setCurrencyName(currencies.getName());
//				rateInfo.setCurrencyEname(currencies.getEnname());
//				rateInfo.setRate(null);
//				rateInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
//				rateInfo.setCurrencyOrder(currencies.getCurrencyOrder());
//				rateInfoService.save(rateInfo);
//			}
//		}
//
//		String tenId = TenantContext.getTenant();
//		// 租户ID
//		if(!tenantId.equals("0") && !tenId.equals("0")){
//
//			SysTenant sysTenant = sysTenantService.getById(tenantId);
//			if(sysTenant != null){
//				if(sysTenant.getType().contains("1")){
//					EnterpriseInfo enterpriseInfo = new EnterpriseInfo();
//					enterpriseInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
//					List<EnterpriseInfo> listEnter = enterpriseInfoService.getCollectionEnterpriseList(enterpriseInfo);
//					String getUnifiedSocialCreditCode = "";
//					if(listEnter.size() > 0){
//						getUnifiedSocialCreditCode = listEnter.get(0).getUnifiedSocialCreditCode();
//					}
//					Boolean pushResult = ZCSUtil.registerthirdusers(user.getPassword(),user,sysTenant,getUnifiedSocialCreditCode,"1");
//
//					if(!pushResult){
//						throw new Exception();
//					}
//				}
//			}
//		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@CacheEvict(value={CacheConstant.SYS_USERS_CACHE}, allEntries=true)
	public void editUser(SysUser user, String roles, String departs) throws Exception {
		if (isBlank(user.getTenantId())) {
			user.setRelTenantIds("0");
			user.setTenantId("0");
		}
		if (isBlank(user.getUserType())) {
			user.setUserTypeRel("");
			user.setUserTypeRelStr("");
		}
		//step.1 修改用户基础信息
		this.updateById(user);
		// 2025/5/20 11:30@ZHANGCHAO 追加/变更/完善：推送到SSO认证中心！！
		ThreadUtil.execAsync(() -> {
			SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, user.getUsername()).last("LIMIT 1"));
			JSONObject paramJson = new JSONObject();
			paramJson.put("userId", isNotEmpty(sysUser) ? sysUser.getId() : user.getId());
			paramJson.put("username", isNotEmpty(sysUser) ? sysUser.getUsername() : user.getUsername());
			paramJson.put("password", isNotEmpty(sysUser) ? sysUser.getPassword() : user.getPassword());
			paramJson.put("salt", isNotEmpty(sysUser) ? sysUser.getSalt() : user.getSalt());
			paramJson.put("phone", isNotEmpty(sysUser) ? sysUser.getPhone() : user.getPhone());
			paramJson.put("adminFlag", 0);
			String createBy = isNotEmpty(sysUser) ? sysUser.getCreateBy() : user.getCreateBy();
			if (isNotBlank(createBy) && "admin".equals(createBy)) {
				paramJson.put("adminFlag", 1);
			}
			// 设置忽略租户插件
			InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
			EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
					.eq(EnterpriseInfo::getTenantId, isNotEmpty(sysUser) ? sysUser.getTenantId() : user.getTenantId()));
			// 关闭忽略策略
			InterceptorIgnoreHelper.clearIgnoreStrategy();
			if (isNotEmpty(enterpriseInfo)) {
				paramJson.put("enterpriseId", enterpriseInfo.getId());
				paramJson.put("enterpriseName", enterpriseInfo.getEnterpriseFullName());
				paramJson.put("creditCode", enterpriseInfo.getUnifiedSocialCreditCode());
			}
			log.info("[editUser]推送到SSO认证中心用户和企业信息参数：{}", paramJson.toString());
			String result = requestPost(syncUserUrl, paramJson.toString());
			log.info("推送到SSO认证中心用户和企业信息返回结果：{}", result);
		});
		String tenantId = "";
		//租户ID
		if(user.getTenantId().equals("")){
			tenantId = "0";
		}else{
			tenantId = user.getTenantId();
		}

		//step.2 修改角色
		//处理用户角色 先删后加
		sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
		if(oConvertUtils.isNotEmpty(roles)) {
			String[] arr = roles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), roleId);
				sysUserRoleMapper.insert(userRole);
			}
		}

		//step.3 修改部门
		String[] arr = {};
		if(oConvertUtils.isNotEmpty(departs)){
			arr = departs.split(",");
		}
		//查询已关联部门
		List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
		if(userDepartList != null && userDepartList.size()>0){
			for(SysUserDepart depart : userDepartList ){
				//修改已关联部门删除部门用户角色关系
				if(!Arrays.asList(arr).contains(depart.getDepId())){
					List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(
							new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId,depart.getDepId()));
					List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
					if(roleIds != null && roleIds.size()>0){
						departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId())
								.in(SysDepartRoleUser::getDroleId,roleIds));
					}
				}
			}
		}
		//先删后加
		sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
		if(oConvertUtils.isNotEmpty(departs)) {
			for (String departId : arr) {
				SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
				sysUserDepartMapper.insert(userDepart);
			}
		}
		//step.4 修改手机号和邮箱
		// 更新手机号、邮箱空字符串为 null
		userMapper.updateNullByEmptyString("email");
		userMapper.updateNullByEmptyString("phone");

		// 保存费用项目表
		List<FeeItem> feeItemList = feeItemService.list(new LambdaQueryWrapper<FeeItem>().eq(FeeItem::getTenantId, tenantId));
		if(feeItemList.size() == 0){
			// 取出备份费用表
			List<FeeItemBackup> list = feeItemBackupService.list();
			for(FeeItemBackup feeItemBackup : list){
				FeeItem feeItem = new FeeItem();
				BeanUtils.copyProperties(feeItemBackup, feeItem);
				feeItem.setId(null);
				feeItem.setTenantId(Long.valueOf(tenantId));
				feeItemService.save(feeItem);
			}
		}
		// 保存汇率表
		List<RateInfo> rateInfoList = rateInfoService.list(new LambdaQueryWrapper<RateInfo>().eq(RateInfo::getTenantId, tenantId));
		if(rateInfoList.size() == 0){
			// 取出常用收据库币种
			List<ErpCurrencies> list = erpCurrenciesService.list();
			for(ErpCurrencies currencies : list){
				RateInfo rateInfo = new RateInfo();
				rateInfo.setTenantId(Long.parseLong(tenantId));
				rateInfo.setCreateTime(new Date());
				rateInfo.setUpdateTime(new Date());
				rateInfo.setRateDate(new Date());
				rateInfo.setCurrencyCode(currencies.getCode());
				rateInfo.setCurrency(currencies.getCurrency());
				rateInfo.setCurrencyName(currencies.getName());
				rateInfo.setCurrencyEname(currencies.getEnname());
				rateInfo.setRate(null);
				rateInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
				rateInfo.setCurrencyOrder(currencies.getCurrencyOrder());
				rateInfoService.save(rateInfo);
			}
		}

		String tenId = TenantContext.getTenant();
		// 租户ID
		if(!tenantId.equals("0") && !tenId.equals("0")){
			SysTenant sysTenant = sysTenantService.getById(tenantId);

			if(sysTenant != null){
//				if(sysTenant.getType().contains("1")){
//					EnterpriseInfo enterpriseInfo = new EnterpriseInfo();
//					enterpriseInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
//					List<EnterpriseInfo> listEnter = enterpriseInfoService.getCollectionEnterpriseList(enterpriseInfo);
//					String getUnifiedSocialCreditCode = "";
//					if(listEnter.size() > 0){
//						getUnifiedSocialCreditCode = listEnter.get(0).getUnifiedSocialCreditCode();
//					}
//					HashMap<String,String> map = new HashMap<>();
//					map = ZCSUtil.thirdlogin(user);
//					String role = ZCSUtil.queryuserinfo(user.getUsername(),map.get("token"));
//					System.out.println("role1：" + role);
//					Boolean pushResult;
//					if(role == ""){
//						pushResult = ZCSUtil.registerthirdusers(user.getPassword(),user,sysTenant,getUnifiedSocialCreditCode,"1");
//					}else{
//						pushResult = ZCSUtil.registerthirdusers(user.getPassword(),user,sysTenant,getUnifiedSocialCreditCode,"2");
//					}
//					if(!pushResult){
//						throw new Exception();
//					}
//				}
			}
		}
	}

	@Override
	public List<String> userIdToUsername(Collection<String> userIdList) {
		LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.in(SysUser::getId, userIdList);
		List<SysUser> userList = super.list(queryWrapper);
		return userList.stream().map(SysUser::getUsername).collect(Collectors.toList());
	}

	/***
	 * 一览数据取得
	 * @param page
	 * @param queryWrapper
	 * @return
	 */
	@Override
	public IPage<SysUserSysTenantModel> querySysUserXhj(Page<SysUserSysTenantModel> page, @Param(Constants.WRAPPER) QueryWrapper<SysUserSysTenantModel> queryWrapper) {
		IPage<SysUserSysTenantModel> result = userMapper.querySysUserXhj(page, queryWrapper);
		return result;
	}

	/***
	 * 一览数据取得
	 * @param page
	 * @param queryWrapper
	 * @return
	 */
	@Override
	public IPage<SysUserSysTenantModel> querySysUserZcsXhj(Page<SysUserSysTenantModel> page, @Param(Constants.WRAPPER) QueryWrapper<SysUserSysTenantModel> queryWrapper) {
		IPage<SysUserSysTenantModel> result = userMapper.querySysUserZcsXhj(page, queryWrapper);
		return result;
	}

	/**
	 * 获取用户角色列表
	 *
	 * @param id@return
	 */
	@Override
	public List<SysRole> getRolesByUserId(String id) {
		return baseMapper.getRolesByUserId(id);
	}

	/**
	 * @param id
	 * @param newRealname
	 */
	@Override
	public void updateUser(String id, String newRealname) {
		baseMapper.update(null, new UpdateWrapper<SysUser>().lambda().set(SysUser::getRealname, newRealname).eq(SysUser::getId, id));
	}

	/**
	 * 密码修改监测任务
	 *
	 * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
	 * <AUTHOR>
	 * @date 2025/2/28 14:39
	 */
	@Override
	public Result<?> passwordChangeMonitoringJob() {
		// 设置忽略租户插件
		InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
		// 获取系统配置的密码更新周期（单位：天）
		String passwordUpdateCycle = baseMapper.getPasswordUpdateCycle();
		if (isBlank(passwordUpdateCycle)) {
			log.info("密码修改周期配置为空，请在系统配置中设置密码更新周期");
			return Result.error("密码修改周期配置为空，请在系统配置中设置密码更新周期");
		}
		List<String> users = new ArrayList<>();
		StringBuilder msg = new StringBuilder();
		try {
			int cycleDays = Integer.parseInt(passwordUpdateCycle);
			Date now = new Date();
			// 获取所有需要检查的用户列表
			List<SysUser> sysUserList = baseMapper.getSysUserList();
			if (isEmpty(sysUserList)) {
				log.info("没有需要检查密码有效期的用户");
				return Result.error("没有需要检查密码有效期的用户");
			}
			List<SysUser> toFreezeUsers = new ArrayList<>();
			for (SysUser sysUser : sysUserList) {
				Date lastChange = sysUser.getLastPasswordChangeDate();
				if (lastChange == null) {
					continue;
				}
				// 计算密码过期时间
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(lastChange);
				calendar.add(Calendar.DAY_OF_MONTH, cycleDays);
				Date expireDate = calendar.getTime();
				// 判断是否过期
				if (now.after(expireDate)) {
					sysUser.setStatus(CommonConstant.USER_FREEZE); // 2表示冻结状态
					toFreezeUsers.add(sysUser);
					log.info("用户{}密码已过期，将被冻结", sysUser.getUsername());
				}
			}
			// 批量更新状态
			if (!toFreezeUsers.isEmpty()) {
//				this.updateBatchById(toFreezeUsers);
				for (SysUser toFreezeUser : toFreezeUsers) {
					baseMapper.update(null, new LambdaUpdateWrapper<SysUser>()
							.set(SysUser::getStatus, CommonConstant.USER_FREEZE)
							.set(SysUser::getRemark, "到期未修改密码，强制冻结")
							.eq(SysUser::getId, toFreezeUser.getId()));
					log.info("用户{}密码已过期，将被冻结", toFreezeUser.getUsername());
					users.add(toFreezeUser.getUsername());
				}
				log.info("已冻结{}个密码过期用户", toFreezeUsers.size());
				msg.append("已冻结").append(toFreezeUsers.size()).append("个密码过期用户");
			} else {
				log.info("没有需要冻结的密码过期用户");
				msg.append("没有需要冻结的密码过期用户");
			}
			if (!users.isEmpty()) {
				msg.append("，用户名：").append(CollUtil.join(users, ","));
			}
			// 关闭忽略策略
			InterceptorIgnoreHelper.clearIgnoreStrategy();
		} catch (NumberFormatException e) {
			log.error("密码更新周期配置格式错误，应为整数天数。当前配置值：{}", passwordUpdateCycle);
			return Result.error("密码更新周期配置格式错误，应为整数天数。当前配置值：" + passwordUpdateCycle);
		} catch (Exception e) {
			log.error("密码修改监测任务执行异常", e);
			return Result.error("密码修改监测任务执行异常", e.getMessage());
		}
		return Result.ok(msg.toString());
	}
}
