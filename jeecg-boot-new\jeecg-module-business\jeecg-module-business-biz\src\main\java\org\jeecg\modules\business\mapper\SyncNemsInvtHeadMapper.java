package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.jeecg.modules.business.entity.SyncNemsInvtHead;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 同步核注清单表头 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface SyncNemsInvtHeadMapper extends BaseMapper<SyncNemsInvtHead> {

    @InterceptorIgnore(tenantLine = "true")
    List<SyncNemsInvtHead> selectUnClosed(String tenantId, String startTime, String endTime);
}
