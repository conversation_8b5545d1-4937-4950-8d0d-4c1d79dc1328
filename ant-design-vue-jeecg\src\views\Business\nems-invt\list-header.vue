<template>
    <a-form-model :model="record" ref="listHeader" :rules="rules" v-enterToNext>
        <table class="my-table">
            <tr>
                <input-item :readonly="disableEdit" label="预录入统一编号" iprop="seqNo" v-model="record.seqNo" />
                <input-item :readonly="disableEdit" label="清单编号" v-model="record.bondInvtNo" />
                <select-item
                    :tableName="'sys_dict'"
                    :dict-key="dictKeys.QDLX"
                    required
                    iprop="invtType"
                    label="清单类型"
                    v-model="record.invtType"
                />
            </tr>
            <tr>
                <input-item label="手(帐)册编号" required iprop="putrecNo" refresh v-model="record.putrecNo" />
                <select-item
                    :options="this.sysIDDict"
                    :value-width="1"
                    iprop="sysId"
                    label="金二子系统"
                    v-model="record.sysId"

                />
            </tr>
            <tr>
                <input-item
                    label="经营单位编码"
                    required
                    iprop="bizopEtpsno"
                    v-model="record.bizopEtpsno"
                    @pressEnter="handleEventEnter('', record.bizopEtpsno, '','', 0)"
                />
                <input-item
                    label="经营单位社会信用代码"
                    v-model="record.bizopEtpsSccd"
                    @pressEnter="handleEventEnter(record.bizopEtpsSccd, '', '', '', 0)"
                />
                <input-item
                    label="经营单位名称"
                    required
                    iprop="bizopEtpsNm"
                    v-model="record.bizopEtpsNm"
                    @pressEnter="handleEventEnter('', '', '', record.bizopEtpsNm, 0)"
                />
            </tr>
            <tr>
                <input-item
                    label="加工单位编码"
                    required
                    iprop="rcvgdEtpsno"
                    v-model="record.rcvgdEtpsno"
                    @pressEnter="handleEventEnter('', record.rcvgdEtpsno, '','', 1)"
                />
                <input-item
                    label="加工单位社会信用代码"
                    v-model="record.rvsngdEtpsSccd"
                    @pressEnter="handleEventEnter(record.rvsngdEtpsSccd, '', '','', 1)"
                />
                <input-item
                    label="加工单位名称"
                    required
                    iprop="rcvgdEtpsNm"
                    v-model="record.rcvgdEtpsNm"
                    @pressEnter="handleEventEnter('', '', '',record.rcvgdEtpsNm, 1)"
                />
            </tr>
            <tr>
                <input-item
                    label="申报单位编码"
                    required
                    iprop="dclEtpsno"
                    v-model="record.dclEtpsno"
                    @pressEnter="handleEventEnter('', record.dclEtpsno, '','', 2)"
                />
                <input-item
                    label="申报单位社会信用代码"
                    iprop="dclEtpsSccd"
                    v-model="record.dclEtpsSccd"
                    @pressEnter="handleEventEnter(record.dclEtpsSccd, '', '','', 2)"
                />
                <input-item
                    label="申报单位名称"
                    required
                    iprop="dclEtpsNm"
                    v-model="record.dclEtpsNm"
                    @pressEnter="handleEventEnter('', '', '',record.dclEtpsNm, 2)"
                />
            </tr>
            <tr>
                <input-item
                    label="录入单位编码"
                    iprop="inputCode"
                    v-model="record.inputCode"
                    @pressEnter="handleEventEnter('', record.inputCode, '','', 3)"
                />
                <input-item
                    label="录入单位社会信用代码"
                    iprop="dclEtpsSccd"
                    v-model="record.inputCreditCode"
                    @pressEnter="handleEventEnter(record.inputCreditCode, '', '','', 3)"
                />
                <input-item
                    label="录入单位名称"
                    v-model="record.inputName"
                    @pressEnter="handleEventEnter('', '', '',record.inputName, 3)"
                />
            </tr>
            <tr>
                <input-item label="企业内部编号"  v-model="record.etpsInnerInvtNo" />
                <date-item :readonly="disableDefault" :placeholder='placeholder' label="录入日期" v-model="record.inputTime"/>
                <date-item :readonly="disableDefault" :placeholder='placeholder' align-right label="清单申报日期" v-model="record.invtDclTime" />
            </tr>
            <tr>
                <select-item
                    :tableName="'sys_dict'"
                    :dict-key="dictKeys.LJCPBS"
                    required
                    iprop="mtpckEndprdMarkcd"
                    label="料件、成品标志"
                    v-model="record.mtpckEndprdMarkcd"
                />
                <select-item
                  :tableName="'sys_dict'"
                    :dict-key="dictKeys.JGFS"
                    required
                    iprop="supvModecd"
                    label="监管方式"
                    v-model="record.supvModecd"
                />
                <select-item
                    :dict-key="dictKeys.YSFS"
                    required
                    iprop="trspModecd"
                    label="运输方式"
                    v-model="record.trspModecd"
                />
            </tr>
            <tr>
                <select-item v-if='!fromStore' ref="selectItem"
                    dict-key="erp_customs_ports,name,customs_port_code"
                    :label="record.impexpMarkcd == 'I' ? '进境关别' : '出境关别'"
                    required
                    iprop="impexpPortcd"
                    v-model="record.impexpPortcd"
                />
							<select-item v-if='fromStore' ref="selectItem"
													 :options="this.customsPortsOptions"
													 :value-width="1"
													 :label="record.impexpMarkcd == 'I' ? '进境关别' : '出境关别'"
													 required
													 iprop="impexpPortcd"
													 v-model="record.impexpPortcd"
							/>
                <select-item v-if='!fromStore' ref="selectItem2"
                    dict-key="erp_customs_ports,name,customs_port_code"
                    label="主管海关"
                    required
                    iprop="dclplcCuscd"
                    v-model="record.dclplcCuscd"
                />
							<select-item v-if='fromStore' ref="selectItem2"
													 :options="this.customsPortsOptions"
													 label="主管海关"
													 required
													 iprop="dclplcCuscd"
													 v-model="record.dclplcCuscd"
							/>
                <select-item v-if='!fromStore' ref="selectItem3"
                    dict-key="erp_countries,name,code,isenabled=0"
                    :label="record.impexpMarkcd == 'I' ? '启运国(地区)' : '运抵国'"
                    required
                    iprop="stshipTrsarvNatcd"
                    v-model="record.stshipTrsarvNatcd"
                />
							<select-item v-if='fromStore' ref="selectItem3"
													 :options="this.countries"
													 :label="record.impexpMarkcd == 'I' ? '启运国(地区)' : '运抵国'"
													 required
													 iprop="stshipTrsarvNatcd"
													 v-model="record.stshipTrsarvNatcd"
							/>
            </tr>
            <tr>
                <select-item
                    :dict-key="dictKeys.JCKKZT"
                    label="清单进出卡口状态"

                    v-model="record.invtIochkptStucd"
                    :readonly="disableDefault"
                />
                <input-item label="申报表编号" v-model="record.applyNo" />
                <select-item :dict-key="dictKeys.LZLX" label="流转类型" v-model="record.listType" />
            </tr>
            <tr>
                <select-item
                    :options="this.sysBGDict"
                    label="报关标志"
                    required
                    iprop="dclcusFlag"
                    v-model="record.dclcusFlag"
                />
                <select-item
                    :dict-key="dictKeys.BGLX"
                    :required="record.dclcusFlag == 1 ? true : false"
                    iprop="dclcusTypecd"
                    label="报关类型"
                    v-model="record.dclcusTypecd"
                />
                <select-item :dict-key="dictKeys.BGDLX" label="报关单类型" v-model="record.decType" />
            </tr>
            <tr>
                <input-item label="对应报关单编号" v-model="record.entryNo" />
                <input-item label="对应报关单申报单位编码" v-model="record.corrEntryDclEtpsno" />
                <input-item label="对应报关单申报单位社会信用代码" v-model="record.corrEntryDclEtpsSccd" />
            </tr>
            <tr>
                <input-item label="对应报关单申报单位名称" v-model="record.corrEntryDclEtpsNm" />
<!--                :table-name="dicttablekey"-->
                <select-item
                    :readonly="disableDefault"
                    :dict-key="dictKeys.HZSBLX"
                    label="申报类型"
                    v-model="record.dclTypecd"
                />

                <select-item :dict-key="dictKeys.HKBZ" label="核扣标志" v-model="record.vrfdedMarkcd" :readonly='true'/>
            </tr>
            <tr>
<!--                :table-name="dicttablekey"-->
                <select-item
                    :options="this.sysBGDict1"
                    label="是否系统生成报关单"
                    required
                    iprop="genDecFlag"
                    v-model="record.genDecFlag"
                />
                <td></td>
                <td></td>
                <date-item  label="已核扣日期" v-model="record.warehousingDate" />
            </tr>
            <tr>
                <input-item label="关联报关单编号" v-model="record.rltEntryNo" />
                <input-item label="关联清单编号" v-model="record.rltinvtNo" />
                <input-item label="关联手(账)册备案号" v-model="record.rltputrecNo" />
            </tr>
            <tr>
                <input-item
                    label="关联报关单境内收发货人编码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryRcvgdEtpsno"
                    v-model="record.rltEntryRcvgdEtpsno"
                    @pressEnter="handleEventEnter('', record.rltEntryRcvgdEtpsno, '','', 4)"
                />
                <input-item
                    label="关联报关单境内收发货人社会信用代码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryRvsngdEtpsSccd"
                    v-model="record.rltEntryRvsngdEtpsSccd"
                    @pressEnter="handleEventEnter(record.rltEntryRvsngdEtpsSccd, '', '','', 4)"
                />
                <input-item
                    label="关联报关单境内收发货人名称"
                    v-model="record.rltEntryRcvgdEtpsNm"
                    @pressEnter="handleEventEnter('', '', '',record.rltEntryRcvgdEtpsNm, 4)"
                />
            </tr>

            <tr>
                <input-item
                    label="关联报关单生产销售(消费使用)单位编码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryBizopEtpsno"
                    v-model="record.rltEntryBizopEtpsno"
                    @pressEnter="handleEventEnter('', record.rltEntryBizopEtpsno, '','', 5)"
                />
                <input-item
                    label="关联报关单生产销售(消费使用)社会信用代码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryBizopEtpsSccd"
                    v-model="record.rltEntryBizopEtpsSccd"
                    @pressEnter="handleEventEnter(record.rltEntryBizopEtpsSccd, '', '','', 5)"
                />
                <input-item
                    label="关联报关单生产销售(消费使用)单位名称"
                    v-model="record.rltEntryBizopEtpsNm"
                    @pressEnter="handleEventEnter('', '', '',record.rltEntryBizopEtpsNm, 5)"
                />
            </tr>
            <tr>
                <input-item
                    label="关联报关单申报单位编码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryDclEtpsno"
                    v-model="record.rltEntryDclEtpsno"
                    @pressEnter="handleEventEnter('', record.rltEntryDclEtpsno, '','', 6)"
                />
                <input-item
                    label="关联报关单申报社会信用代码"
                    :required="record.dclcusTypecd == 1 ? true : false"
                    iprop="rltEntryDclEtpsSccd"
                    v-model="record.rltEntryDclEtpsSccd"
                    @pressEnter="handleEventEnter(record.rltEntryDclEtpsSccd, '', '','', 6)"
                />
                <input-item
                    label="关联报关单申报名称"
                    v-model="record.rltEntryDclEtpsNm"
                    @pressEnter="handleEventEnter('', '', '',record.rltEntryDclEtpsNm, 6)"
                />
            </tr>
            <tr>
                <date-item :readonly="disableDefault" label="报关单申报日期" v-model="record.entryDclTime" />

                <input-item label="备注" v-model="record.rmk" value-width="4" />
            </tr>
            <tr>
                <date-item :readonly="!disableDefault" label="申报日期(保税仓库月报表用)" v-model="record.declarationDate"/>
<!--                <td-->
<!--                    colspan="1"-->
<!--                    title="dddd"-->
<!--                >-->
<!--                    11-->
<!--                </td>-->
                <input-item
                    :readonly="disableDefault"
                    label="报关单草稿(备注)"
                    v-model="record.tmp53"
                    value-width="4"
                />
            </tr>
        </table>
    </a-form-model>
</template>

<script>
import InputItem from '@views/declaration/component/m-table-input-item'
import SelectItem from '@views/declaration/component/m-table-select-item'
import DateItem from '@views/declaration/component/m-table-date-item'
import store from '@/store'
import { axios } from '@/utils/request'
import { getAction, postAction } from '@/api/manage'
import { ajaxGetDictItems, duplicateCheck } from '@/api/api'
import moment from 'moment'
import { dateRandomNumber } from '@/utils/util'

export default {
    name: 'listHeader',
    components: {
        SelectItem,
        InputItem,
        DateItem
    },
    data() {
        return {
					customsPortsOptions: [],
					countries: [],
					placeholder: '',
					fromStore: false,
					emsHead: {},
					enterpriseInfo: {},
            dicttablekey: 'sys_dict',
            applyNumber: '',
            disableDefault: true,
            dictKeys: {
                // 运输方式
                YSFS: 'trans_type',
                // 进境关别
                GQDM: 'GQDM',
                // 国别地区
                GBDQ: 'GBDQ',
                // 料件.成品标志
                LJCPBS: 'LJCPBS',
                // 监管方式
                JGFS: 'JGFS',
                // 报关类型(核注清单)
                GBDQ_NUM: 'GBDQ_NUM',
                BGDLX: 'BGLX',
                //清单类型
                QDLX: 'QDLX',
                //流转类型
                LZLX: 'LZLX',
                //核扣标记
                HKBZ: 'HKBZ',
                //报关单类型
                BGDZL: 'BGDZL',
                //是否报关标志（字典表））
                SFBGBZ: 'SFBGBZ',
                //报关类型（核注）（字典表）
                BGLX: 'HZBGLX',
                //是否系统生成报关单（ 字典表）
                SFXTSCBGD: 'SFXTSCBGD',
                //申报类型（ 海关库）
                HZSBLX: 'HZSBLX',
                //清单进出卡口状态 （ 字典表）
                JCKKZT: 'JCKKZT'
            },
            rules: {
                seqNo: [
                    {
                        validator: this.checkSeqNoSpaceVal
                    }
                ],
                invtType: [
                    {
                        required: true,
                        message: '请选择清单类型!'
                    }
                ],
                putrecNo: [
                    {
                        required: true,
                        message: '请填写手(帐)册编号!'
                    }
                ],
                bizopEtpsno: [
                    {
                        required: true,
                        message: '请填写经营单位编码!'
                    },
                    {
                        max: 10,
                        message: '长度不能大于10位!'
                    }
                ],
                bizopEtpsNm: [
                    {
                        required: true,
                        message: '请填写经营单位名称!'
                    }
                ],
                rcvgdEtpsNm: [
                    {
                        required: true,
                        message: '请填写加工单位名称!'
                    }
                ],
                rcvgdEtpsno: [
                    {
                        required: true,
                        message: '请添加加工单位编码!'
                    },
                    // {
                    //     max: 10,
                    //     message: '长度不能大于10位!'
                    // }
                ],
                corrEntryDclEtpsno: [
                    {
                        max: 10,
                        message: '长度不能大于10位!'
                    }
                ],

                dclEtpsNm: [
                    {
                        required: true,
                        message: '请填写申报单位名称!'
                    }
                ],

                dclEtpsno: [
                    {
                        required: true,
                        message: '请填写申报单位编码!'
                    },
                    {
                        max: 10,
                        message: '长度不能大于10位!'
                    }
                ],
                inputCode: [
                    {
                        max: 10,
                        message: '长度不能大于10位!'
                    }
                ],
                mtpckEndprdMarkcd: [
                    {
                        required: true,
                        message: '请选择料件、成品标志!'
                    }
                ],
                supvModecd: [
                    {
                        required: true,
                        message: '请选择监管方式!'
                    }
                ],
                trspModecd: [
                    {
                        required: true,
                        message: '请选择运输方式!'
                    }
                ],
                dclplcCuscd: [
                    {
                        required: true,
                        message: '请选择主管海关!'
                    }
                ],
                impexpPortcd: [
                    {
                        required: true,
                        checkCode: 'impexpPortcd',
                        validator: this.checkimpexpPortcd
                    }
                ],
                stshipTrsarvNatcd: [
                    {
                        required: true,
                        checkCode: 'stshipTrsarvNatcd',
                        validator: this.checkimpexpPortcd
                    }
                ],

                dclcusFlag: [
                    {
                        required: true,
                        message: '请选择报关标志!'
                    }
                ],
                dclcusTypecd: [
                    {
                        required: true,
                        message: '请选择报关类型!',
                        validator: this.checkDclcusTypecd
                    }
                ],
                sysId: [
                    {
                        required: true,
                        message: '请选择金二子系统!'
                    }
                ],
                genDecFlag: [
                    {
                        required: true,
                        message: '请选择是否自动生成报关单!'
                    }
                ],
                rltEntryDclEtpsno: [
                    {
                        required: true,
                        checkName: '关联报关单申报单位编码',
                        max: 10,
                        validator: this.checkRelated
                    }
                ],
                rltEntryDclEtpsSccd: [
                    {
                        required: true,
                        message: '请输入关联报关单申报社会信用代码!',

                        validator: this.checkRelated
                    }
                ],
                rltEntryRcvgdEtpsno: [
                    {
                        required: true,
                        checkName: '关联报关单境内收发货人编码',
                        max: 10,
                        validator: this.checkRelated
                    }
                ],
                rltEntryRvsngdEtpsSccd: [
                    {
                        required: true,
                        message: '请输入关联报关单境内收发货人社会信用代码!',
                        validator: this.checkRelated
                    }
                ],

                rltEntryBizopEtpsno: [
                    {
                        required: true,
                        checkName: '关联报关单生产销售(消费使用)单位编码',
                        max: 10,
                        validator: this.checkRelated
                    }
                ],
                rltEntryBizopEtpsSccd: [
                    {
                        required: true,
                        message: '请输入关联报关单生产销售(消费使用)社会信用代码!',
                        validator: this.checkRelated
                    }
                ],
                etpsInnerInvtNo: [
                    {
                        required: true,
                      //  validator: this.validateEtpsInnerInvtNo
                    }
                ]
            },

            record: {
                // 核注单流水号
                id: '',
                // 委托流水号
                applyNumber: '',
                // 清单编号 （返填 - 海关审批通过后系统自动返填）
                bondInvtNo: '',
                // 分票序号
                partId: '',
                // 清单预录入统一编号 （返填 - 第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号）
                seqNo: '',
                // 变更次数
                chgTmsCnt: '',
                // 备案编号(手(帐)册编号)
                putrecNo: '',
                // 企业内部清单编号 （由企业自行编写）
                etpsInnerInvtNo: '',
                // 经营企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                bizopEtpsSccd: '',
                // 经营企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                bizopEtpsno: '',
                // 经营企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                bizopEtpsNm: '',
                // 收货企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                rcvgdEtpsno: '',
                // 收发货企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                rvsngdEtpsSccd: '',
                // 收货企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
                rcvgdEtpsNm: '',
                // 申报企业社会信用代码
                dclEtpsSccd: '',
                // 申报企业编号
                dclEtpsno: '',
                // 申报企业名称
                dclEtpsNm: '',
                // 清单申报时间 （返填 - 系统自动反填）
                invtDclTime: '',
                // 报关单申报日期 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
                entryDclTime: '',
                // 对应报关单编号 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
                entryNo: '',
                // 关联清单编号 （结转类专用，检控要求复杂，见需求文档）
                rltinvtNo: '',
                // 关联备案编号 （结转类专用）（关联手(账)册备案号）
                rltputrecNo: '',
                // 关联报关单编号 （可录入或者系统自动生成报关单后返填二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
                rltEntryNo: '',
                // 关联报关单经营企业社会信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
                //(关联报关单生产销售(消费使用)单位编码)
                rltEntryBizopEtpsSccd: '',
                // 关联报关单经营企业编号 （当报关类型DCLCUSTYPECD字段为1时，该字段必填\r\n报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
                //（关联报关单生产销售(消费使用)社会信用代码）
                rltEntryBizopEtpsno: '',
                // 关联报关单经营企业名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填\r\n同上）(关联报关单生产销售(消费使用)单位编码)
                //关联报关单生产销售(消费使用)单位名称
                rltEntryBizopEtpsNm: '',
                // 关联报关单收发货单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
                rltEntryRvsngdEtpsSccd: '',
                // 关联报关单海关收发货单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填\r\n报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。）
                rltEntryRcvgdEtpsno: '',
                // 关联报关单收发货单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
                rltEntryRcvgdEtpsNm: '',
                // 关联报关单申报单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
                rltEntryDclEtpsSccd: '',
                // 关联报关单海关申报单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
                rltEntryDclEtpsno: '',
                // 关联报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
                rltEntryDclEtpsNm: '',
                // 进出口口岸代码
                impexpPortcd: '',
                // 申报地关区代码
                dclplcCuscd: '',
                // 进出口标记代码 （返填 - I：进口，E：出口）
                impexpMarkcd:
                    this.$route.query.impexpMarkcd == null || this.$route.query.impexpMarkcd == undefined
                        ? ''
                        : this.$route.query.impexpMarkcd,
                // 料件成品标记代码 （I：料件，E：成品）
                mtpckEndprdMarkcd: '',
                // 监管方式代码
                supvModecd: '',
                // 运输方式代码
                trspModecd: '',
                // 是否报关标志 （1.报关 2.非报关）
                dclcusFlag: '',
                // 报关类型代码 （1.关联报关 2.对应报关；当报关标志为“1.报关”时，企业可选择“关联报关单”/“对应报关单”；当报关标志填写为“2.非报关”时，报关标志填写为“2.非报关”该项不可填。）
                dclcusTypecd: '',
                // 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
                vrfdedMarkcd: '0',
                // 清单进出卡口状态代码 （系统自动反填。未出卡口，已出卡口。需求不明确，暂留）
                invtIochkptStucd: '',
                // 预核扣时间
                prevdTime: '',
                // 正式核扣时间 （返填）
                formalVrfdedTime: '',
                // 申请表编号
                applyNo: '',
                // 流转类型 （非流转类不填写，流转类填写：A：加工贸易深加工结转、B：加工贸易余料结转、C：不作价设备结转、D：区间深加工结转、E：区间料件结转）
                listType: '',
                // 录入企业编号 （保存首次暂存时IC卡的企业信息）
                inputCode: '',
                // 录入企业社会信用代码 （返填 - 保存首次暂存时IC卡的企业信息）
                inputCreditCode: '',
                // 录入单位名称 （保存首次暂存时IC卡的企业信息）
                inputName: '',
                // 申报人IC卡号 （企业端专用）
                icCardNo: '',
                // 录入日期 （企业端专用）
                inputTime: '',
                // 清单状态 （系统自动反填。1-已申报、C-退单、改单、删单、审核通过）
                listStat: '',
                // 对应报关单申报单位社会统一信用代码
                corrEntryDclEtpsSccd: '',
                // 对应报关单申报单位代码 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
                corrEntryDclEtpsno: '',
                // 对应报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
                corrEntryDclEtpsNm: '',
                // 报关单类型 （1-进口报关单\r\n2-出口报关单\r\n3-进境备案清单\r\n4-出境备案清单\r\n5-进境两单一审备案清单\r\n6-出境两单一审备案清单\r\n7-进境备案清单（简化）\r\n8-出境备案清单（简化）\r\n9-转关提前进口报关单\r\nA-转关提前出口报关单\r\nB-转关提前进境备案清单\r\nC-转关提前出境备案清单\r\nD-转关提前进境备案清单（简化）\r\nE-转关提前出境备案清单（简化）\r\nF-出口二次转关单）
                decType: '',
                // 入库时间 （返填）
                addTime: '',
                // 起运运抵国别代码
                stshipTrsarvNatcd: '',
                // 清单类型 （(SAS项目新增)\r\n标识清单类别，0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：异常补录，默认为0：普通清单）
                invtType: '',
                // 报关状态 （(SAS项目新增)\r\n标明对应（关联）报关单放行状态，目前只区分 0：未放行，1：已放行。该字段用于区域或物流账册的清单，该类型清单满足两个条件才能核扣：报关单被放行+货物全部过卡）
                entryStucd: '',
                // 核放单生成标志代码 （(SAS项目新增)\r\n1：未生成、2：部分生成、3：已生成，核放单生成时系统返填）
                passportUsedTypeCd: '',
                // 备注
                rmk: '',
                // 0-暂存成功1-通过（已核扣）2-转人工3-退单4-预核扣5-通过（未核扣）Y-入库成功Z-入库失败
                status: '',
                // 分运单号
                hawb: '',
                // 是否核对
                checked: '',
                // 核对时间
                checkDate: '',
                // 申报日期 (报关：报关单申报，非报关：核注单申报日期)
                declarationDate: '',
                // 货物列表
                goodsType: '',
                // 剩余数量
                balanceQty: '',
                // 子系统ID(95 :加工贸易账册系统B1: 加工贸易手册系统B2 :加工贸易担保管理系统B3: 保税货物流转系统二期Z7: 海关特殊监管区域管理系统Z8 :保税物流管理系统)
                sysId: '',
                // 收货(加工)企业社会信用代码
                //rcvgdEtpsSccd: '',
                //是否系统生成报关单
                genDecFlag: '',
                //申报类型代码(1-备案、3-作废
                dclTypecd: '1',
                //申报单位编码
                dclTenantId: '',
                //创建时间
                createDate: '',
                //创建人
                createPerson: '',
                nemsInvtLists: [],
                //是否审核
                audited: '',

                //自身创建租户编码
                tenantId: '',
								fromStock: false
            },

            sysIDDict: [
                { text: '加工贸易账册系统', value: '95' },
                { text: '加工贸易手册系统', value: 'B1' },
                { text: '加工贸易担保管理系统', value: 'B2' },
                { text: '保税货物流转系统二期', value: 'B3' },
                { text: '海关特殊监管区域管理系统', value: 'Z7' },
                { text: '保税物流管理系统', value: 'Z8' }
            ],
            sysBGDict: [
                { text: '报关', value: '1' },
                { text: '非报关', value: '2' }
            ],
            sysBGDict1: [
              { text: '是', value: '1' },
              { text: '否', value: '2' }
            ],
            etpsInnerInvtNo:
                this.$route.query.etpsInnerInvtNo == null || this.$route.query.etpsInnerInvtNo == undefined
                    ? ''
                    : this.$route.query.etpsInnerInvtNo,
            //复制标识1：复制状态
            copyStatus:
                this.$route.query.copyStatus == null || this.$route.query.copyStatus == undefined
                    ? ''
                    : this.$route.query.copyStatus,
            preForm: {},
            url: {
                list: '/dcl/invt/getInvtById',
                savaHeaderurl: '/dcl/invt/saveInvt',
                //获取模板地址
                defaultTeUrl: '/dcl/invtTemplete/listInvtTempleteByTrustee1st'
            }
        }
    },
   // inject: ['closeCurrent'],
    props: {
        disableEdit: {
            type: Boolean
        },
        emsObject: {
            type: Object
        },
			storeToInvtDTO: {
					type: Object,
					default: {}
			},
			stockHeadTypeToInvtDTO: {
					type: Object,
					default: {}
			}
    },

    methods: {
        checkSeqNoSpaceVal(rule, value, callback) {
            if (!this.isEmpty(value)) {
                let reg = /^\S+$/
                if (!reg.test(value)) {
                    callback(new Error('请不要输入空格!'))
                } else {
                    callback()
                }
            } else {
                callback()
            }
        },

        checkNo(rule, value, callback) {
            if (rule.required) {
                if (this.isEmpty(value)) {
                    callback(new Error(`请输入${rule.checkName}!`))
                } else {
                    callback()
                }
            }
            if (!this.isEmpty(value)) {
                let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
                if (rule.checkNum) {
                    if (!reg.test(value)) {
                        callback(new Error('请输入数字!'))
                    }
                }
                if (value < 0) {
                    callback(new Error('不能输入负数!'))
                }
                if (!this.isEmpty(rule.max) && (value.length > rule.max || value > Math.pow(10, rule.max))) {
                    callback(new Error(`长度不能大于${rule.max}位!`))
                }
                if (value.toString().indexOf('.') != -1) {
                    callback(new Error('不能含有小数点!'))
                } else {
                    callback()
                }
            } else {
                callback()
            }
        },
        //检验企业内部编号唯一
        validateEtpsInnerInvtNo(rule, value, callback) {
            if (rule.required) {
                if (this.isEmpty(value) && !this.isEmpty(this.record.id)) {
                    callback('必须填写企业内部编号!')
                }
                if (!this.isEmpty(value)) {
                    let reg = /^\S+$/
                    if (!reg.test(value)) {
                        callback(new Error('请不要输入空格!'))
                    }
                }
                var params = {
                    tableName: 'NEMS_INVT_HEAD',
                    fieldName: 'ETPS_INNER_INVT_NO',
                    fieldVal: value,
                    dataId: this.record.id
                }
                let that = this
                duplicateCheck(params).then(res => {
                    if (res.success) {
                        callback()
                    } else {
                        if (value == that.etpsInnerInvtNo) {
                            callback()
                        } else {
                            callback('该编号已存在!')
                        }
                    }
                })
            }
        },
        //区别提醒进出口个别不同字段验证信息
        checkimpexpPortcd(rule, value, callback) {
            let that = this
            name = ''
            if (rule.required) {
                if (this.isEmpty(value)) {
                    if (this.record.impexpMarkcd == 'I' && rule.checkCode == 'impexpPortcd') {
                        name = '进境关别'
                    } else if (this.record.impexpMarkcd == 'E' && rule.checkCode == 'impexpPortcd') {
                        name = '出境关别'
                    }
                    if (this.record.impexpMarkcd == 'I' && rule.checkCode == 'stshipTrsarvNatcd') {
                        name = '启运国'
                    } else if (this.record.impexpMarkcd == 'E' && rule.checkCode == 'stshipTrsarvNatcd') {
                        name = '运抵国'
                    }
                    callback(new Error(`请输入${name}!`))
                }
                if (!this.isEmpty(value)) {
                    if (!this.isEmpty(rule.max) && value.length > rule.max) {
                        callback(new Error(`长度不能大于${rule.max}位!`))
                    }
                }
            }

            callback()
        },
        checkDclcusTypecd(rule, value, callback) {
            let that = this
            if (this.record.dclcusFlag == 1) {
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                    }
                }
            } else {
                if (!this.isEmpty(value)) {
                    if (!this.isEmpty(rule.max) && value.length > rule.max) {
                        callback(new Error(`长度不能大于${rule.max}位!`))
                    }
                }
            }

            callback()
        },

        //检测关联关系单报关类型为1 检测关联必填
        checkRelated(rule, value, callback) {
            let that = this
            if (this.record.dclcusTypecd == 1) {
                if (rule.required) {
                    if (this.isEmpty(value)) {
                        callback(new Error(`请输入${rule.checkName}!`))
                    }
                    if (!this.isEmpty(value)) {
                        if (!this.isEmpty(rule.max) && value.length > rule.max) {
                            callback(new Error(`长度不能大于${rule.max}位!`))
                        }
                    }
                }
            } else {
                if (!this.isEmpty(value)) {
                    if (!this.isEmpty(rule.max) && value.length > rule.max) {
                        callback(new Error(`长度不能大于${rule.max}位!`))
                    }
                }
            }

            callback()
        },

        initThisPage() {
            this.applyNumber = this.record.applyNumber = this.$route.query.applyNumber
            this.etpsInnerInvtNo = this.record.etpsInneretpsInnerInvtNoInvtNo = this.$route.query.etpsInnerInvtNo
            this.id = this.record.id = this.$route.query.id
            if (!!this.id) {
                getAction(this.url.list, {
                    id: this.id
                })
								.then(res => {
										this.record = res.result
										if (this.copyStatus == 1) {
												let copyNewRecord = this.copyNewRecord(this.record)
												this.preForm = JSON.parse(JSON.stringify(copyNewRecord))
										} else {
												this.preForm = JSON.parse(JSON.stringify(this.record))
										}
								})
								.catch(reason => console.log(reason))
            }
						if (!this.isEmpty(this.storeToInvtDTO)) {
							this.fromStore = true
							this.emsHead = this.storeToInvtDTO.ptsEmsHead ? this.storeToInvtDTO.ptsEmsHead : {}
							this.enterpriseInfo = this.storeToInvtDTO.enterpriseInfo ? this.storeToInvtDTO.enterpriseInfo : {}
							let result = {}
							result.putrecNo = this.emsHead.emsNo
							result.sysId = 'Z8'
							result.invtType = '0'
							result.bizopEtpsno = this.enterpriseInfo.customsDeclarationCode
							result.bizopEtpsSccd = this.enterpriseInfo.unifiedSocialCreditCode
							result.bizopEtpsNm = this.enterpriseInfo.enterpriseFullName
							result.rcvgdEtpsno = this.storeToInvtDTO.storeInfo ? this.storeToInvtDTO.storeInfo.storeCode : ''
							result.rcvgdEtpsNm = this.storeToInvtDTO.storeInfo ? this.storeToInvtDTO.storeInfo.storeName : ''
							result.dclEtpsno = this.enterpriseInfo.customsDeclarationCode
							result.dclEtpsSccd = this.enterpriseInfo.unifiedSocialCreditCode
							result.dclEtpsNm = this.enterpriseInfo.enterpriseFullName
							result.rltEntryDclEtpsno = this.enterpriseInfo.customsDeclarationCode
							result.rltEntryDclEtpsSccd = this.enterpriseInfo.unifiedSocialCreditCode
							result.rltEntryDclEtpsNm = this.enterpriseInfo.enterpriseFullName
							result.inputCode = this.enterpriseInfo.customsDeclarationCode
							result.inputCreditCode = this.enterpriseInfo.unifiedSocialCreditCode
							result.inputName = this.enterpriseInfo.enterpriseFullName
							result.inputTime = moment().format('YYYY-MM-DD HH:mm:ss')
							result.invtDclTime = moment().format('YYYY-MM-DD HH:mm:ss')
							this.placeholder = result.inputTime
							result.mtpckEndprdMarkcd = 'I'
							result.supvModecd = '1200'
							result.trspModecd = '9'
							result.dclplcCuscd = this.emsHead.masterCustoms
							result.impexpPortcd = this.emsHead.masterCustoms
							result.stshipTrsarvNatcd = '142'
							result.invtIochkptStucd = '1'
							result.dclcusFlag = '1'
							result.dclcusTypecd = '1'
							result.decType = '1'
							result.vrfdedMarkcd = '0'
							result.genDecFlag = '1'
							Object.assign(this.record, result)
						}
						// 2024/3/4 10:40@ZHANGCHAO 追加/变更/完善：出入库单集中申报！！
					if (!this.isEmpty(this.stockHeadTypeToInvtDTO.nemsInvtHead)) {
						this.fromStore = true
						Object.assign(this.record, this.stockHeadTypeToInvtDTO.nemsInvtHead)
						this.record.fromStock = true
					}
					// 2024/3/18 11:29@ZHANGCHAO 追加/变更/完善：单证转换
					if(this.$route.query.dclcusTypecd){
						this.record.dclcusTypecd = this.$route.query.dclcusTypecd
					}
					if(this.$route.query.decType){
						this.record.decType = this.$route.query.decType
					}
        },
        //复制时重置对象关联信息
        copyNewRecord(record) {

            Object.keys(record).forEach(key => {
                if (
                    key == 'applyNumber' ||
                    key == 'id' ||
                    key == 'rltEntryNo' ||
                    key == 'inputTime' ||
                    key == 'seqNo' ||
                    key == 'invtDclTime' ||
                    key == 'entryDclTime' ||
                    key == 'entryNo' ||
                    key == 'rltputrecNo' ||
                    key == 'rltEntryNo' ||
                    key == 'partId' ||
                    key == 'invtIochkptStucd' ||
                    key == 'vrfdedMarkcd' ||
                    key == 'bondInvtNo' ||
                    key == 'etpsInnerInvtNo' ||
                    key == ' audited' ||
                    key == 'tenantId' ||
                    key == 'createPerson' ||
                    key == 'createDate' ||
                    key == 'applyNumber' ||
                    key == 'etpsInnerInvtNo' ||
                    key == 'tenantId' ||
                    key == 'invId' ||
                    key == 'applyNumber' ||
                    key == 'id' ||
                    key == 'etpsInnerInvtNo' ||
                    key == 'tenantId' ||
                    key == 'invId' ||
                    key == 'updateBy' ||
                    key == 'updateDate' ||
                    key == 'createDate' ||
                    key == 'createPerson' ||
                    key == 'invtIochkptStucd' ||
                    key == 'entryStucd' ||
                    key == 'send' ||
                    key == 'audited' ||
                    key == 'listStat' ||
                    key == 'prevdTime' ||
                    key == 'formalVrfdedTime' ||
                    key ==  'addTime'     ||
                    key == 'emsFlowsId'||
                    key == 'status'||
                    key == 'stockHeadId'||
                    key == 'doNotPush'
                ) {
                    record[key] = ''
                } else if (key == 'dclTypecd') {
                    record[key] = '1'
                }
                if (key == 'dclTypecd') {
                    record[key] = '1'
                }
                if (key == 'decPushStatus') {
                    record[key] = '0'
                }
            })
            return record
        },
        initThisCustomer() {
            if (this.disableEdit) {
                if (!this.isEmpty(store.getters.customerInfo)) {
                    ;(this.record.inputCode =
                        typeof store.getters.customerInfo.departcd == 'undefined'
                            ? ''
                            : store.getters.customerInfo.departcd),
                        (this.record.inputName =
                            typeof store.getters.customerInfo.departName == 'undefined'
                                ? ''
                                : store.getters.customerInfo.departName),
                        (this.record.inputCreditCode =
                            typeof store.getters.customerInfo.socialCode == 'undefined'
                                ? ''
                                : store.getters.customerInfo.socialCode),
                        (this.record.dclEtpsno =
                            typeof store.getters.customerInfo.departcd == 'undefined'
                                ? ''
                                : store.getters.customerInfo.departcd),
                        (this.record.dclEtpsNm =
                            typeof store.getters.customerInfo.departName == 'undefined'
                                ? ''
                                : store.getters.customerInfo.departName),
                        (this.record.dclEtpsSccd =
                            typeof store.getters.customerInfo.socialCode == 'undefined'
                                ? ''
                                : store.getters.customerInfo.socialCode)
                }
                let info = store.getters.tenantInfo
            }
        },
        returnBackFn() {
            this.$emit('change', JSON.parse(JSON.stringify(this.record)))
        },
        returnDclcusTypecdBackFn2(val) {
            // this.$watch('record', () => {})
            if (this.disableEdit) {
                if (val == 2) {
                    let that = this //修改之后

                    if (!that.isEmpty(store.getters.customerInfo)) {
                        that.$set(
                            that.record,
                            'corrEntryDclEtpsSccd',

                            typeof store.getters.customerInfo.socialCode == 'undefined'
                                ? ''
                                : store.getters.customerInfo.socialCode
                        )
                        that.$set(
                            that.record,
                            'corrEntryDclEtpsNm',
                            typeof store.getters.customerInfo.departName == 'undefined'
                                ? ''
                                : store.getters.customerInfo.departName
                        )
                        that.$set(
                            that.record,
                            'corrEntryDclEtpsno',
                            typeof store.getters.customerInfo.departcd == 'undefined'
                                ? ''
                                : store.getters.customerInfo.departcd
                        )
                    }
                } else {
                    let that = this //修改之后
                    this.$nextTick(() => {
                        setTimeout(() => {
                            that.$set(that.record, 'corrEntryDclEtpsSccd', '')
                            that.$set(that.record, 'corrEntryDclEtpsNm', '')
                            that.$set(that.record, 'corrEntryDclEtpsno', '')
                        }, this.delay)
                    })
                }
            }
        },
				async handleEventEnter(socialCode, departcd, tradeCiqCode, customerName, type) {
					let that = this
					await getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', {
						socialCode: socialCode ? socialCode : '',
						departcd: departcd ? departcd : '',
						tradeCiqCode: tradeCiqCode ? tradeCiqCode : '',
						customerName: customerName ? customerName : ''
					}).then((res) => {
						if (res.success) {
							let cu = res.result
							// this.record.optUnitSocialCode = res.result.socialCode //18位
							// this.record.optUnitId = res.result.departcd //海关十位
							// this.record.tradeCiqCode = res.result.ciqCode //检验检疫代码
							// this.record.optUnitName = res.result.departName //企业名称
							if (type == 0) {
								that.isEmpty(cu.departcd)
									? (that.record.bizopEtpsno = '')
									: (that.record.bizopEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.bizopEtpsSccd = '')
									: (that.record.bizopEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.bizopEtpsNm = '')
									: (that.record.bizopEtpsNm = cu.departName)
							}
							if (type == 1) {
								that.isEmpty(cu.departcd)
									? (that.record.rcvgdEtpsno = '')
									: (that.record.rcvgdEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.rvsngdEtpsSccd = '')
									: (that.record.rvsngdEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.rcvgdEtpsNm = '')
									: (that.record.rcvgdEtpsNm = cu.departName)
							}
							if (executeCode == 2) {
								that.isEmpty(cu.departcd)
									? (that.record.dclEtpsno = '')
									: (that.record.dclEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.dclEtpsSccd = '')
									: (that.record.dclEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.dclEtpsNm = '')
									: (that.record.dclEtpsNm = cu.departName)
							}
							if (executeCode == 3) {
								that.isEmpty(cu.departcd)
									? (that.record.inputCode = '')
									: (that.record.inputCode = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.inputCreditCode = '')
									: (that.record.inputCreditCode = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.inputName = '')
									: (that.record.inputName = cu.departName)
							}
							if (executeCode == 4) {
								that.isEmpty(cu.departcd)
									? (that.record.rltEntryRcvgdEtpsno = '')
									: (that.record.rltEntryRcvgdEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.rltEntryRvsngdEtpsSccd = '')
									: (that.record.rltEntryRvsngdEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.rltEntryRcvgdEtpsNm = '')
									: (that.record.rltEntryRcvgdEtpsNm = cu.departName)
							}
							if (executeCode == 5) {
								that.isEmpty(cu.departcd)
									? (that.record.rltEntryBizopEtpsno = '')
									: (that.record.rltEntryBizopEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.rltEntryBizopEtpsSccd = '')
									: (that.record.rltEntryBizopEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.rltEntryBizopEtpsNm = '')
									: (that.record.rltEntryBizopEtpsNm = cu.departName)
							}
							if (executeCode == 6) {
								that.isEmpty(cu.departcd)
									? (that.record.rltEntryDclEtpsno = '')
									: (that.record.rltEntryDclEtpsno = cu.departcd)
								that.isEmpty(cu.socialCode)
									? (that.record.rltEntryDclEtpsSccd = '')
									: (that.record.rltEntryDclEtpsSccd = cu.socialCode)
								that.isEmpty(cu.departName)
									? (that.record.rltEntryDclEtpsNm = '')
									: (that.record.rltEntryDclEtpsNm = cu.departName)
							}
						}
					})
				},
        bizopEtpsnoEnter(row, executeCode,flag) {

            let that = this
            flag=null
            getAction('/dcl/invt/getEnterpriseByDepartcdOrSocialCodeOrDepartName', {
                flag:'',
                searchText: row
            }).then(res => {
                if (res.success) {
                    let cu = res.result

                    if (executeCode == 0) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.bizopEtpsno = '')
                            : (that.record.bizopEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.bizopEtpsSccd = '')
                            : (that.record.bizopEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.bizopEtpsNm = '')
                            : (that.record.bizopEtpsNm = cu.departName)
                    }
                    if (executeCode == 1) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.rcvgdEtpsno = '')
                            : (that.record.rcvgdEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.rvsngdEtpsSccd = '')
                            : (that.record.rvsngdEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.rcvgdEtpsNm = '')
                            : (that.record.rcvgdEtpsNm = cu.departName)
                    }
                    if (executeCode == 2) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.dclEtpsno = '')
                            : (that.record.dclEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.dclEtpsSccd = '')
                            : (that.record.dclEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.dclEtpsNm = '')
                            : (that.record.dclEtpsNm = cu.departName)
                    }
                    if (executeCode == 3) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.inputCode = '')
                            : (that.record.inputCode = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.inputCreditCode = '')
                            : (that.record.inputCreditCode = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.inputName = '')
                            : (that.record.inputName = cu.departName)
                    }
                    if (executeCode == 4) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.rltEntryRcvgdEtpsno = '')
                            : (that.record.rltEntryRcvgdEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.rltEntryRvsngdEtpsSccd = '')
                            : (that.record.rltEntryRvsngdEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.rltEntryRcvgdEtpsNm = '')
                            : (that.record.rltEntryRcvgdEtpsNm = cu.departName)
                    }
                    if (executeCode == 5) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.rltEntryBizopEtpsno = '')
                            : (that.record.rltEntryBizopEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.rltEntryBizopEtpsSccd = '')
                            : (that.record.rltEntryBizopEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.rltEntryBizopEtpsNm = '')
                            : (that.record.rltEntryBizopEtpsNm = cu.departName)
                    }
                    if (executeCode == 6) {
                        that.isEmpty(cu.departcd)
                            ? (that.record.rltEntryDclEtpsno = '')
                            : (that.record.rltEntryDclEtpsno = cu.departcd)
                        that.isEmpty(cu.socialCode)
                            ? (that.record.rltEntryDclEtpsSccd = '')
                            : (that.record.rltEntryDclEtpsSccd = cu.socialCode)
                        that.isEmpty(cu.departName)
                            ? (that.record.rltEntryDclEtpsNm = '')
                            : (that.record.rltEntryDclEtpsNm = cu.departName)
                    }
                }
            })
        },
        returnDclcusTypecdBackFn1() {
        },
        varietyDisableEditD() {
            this.$emit('varietyDisableEditD', true)
        },
        saveInvtHead() {
            const that = this
            this.record.applyNumber = this.applyNumber
            that.$refs.listHeader.validate(valid => {
                if (valid) {
                    postAction(that.url.savaHeaderurl, {
                        invtHead: that.record
                    })
                        .then(res => {
                            if (res.success) {
                                that.record = res.data
                                that.$router.push({
                                    path: '/Business/NemsInvt/nemsInvtDetailsEdit',
                                    query: {
                                        id: res.data.id,
                                        // applyNumber: res.data.applyNumber,
                                        // etpsInnerInvtNo: res.data.etpsInnerInvtNo
                                    }
                                })

                                that.$message.success('保存成功!')
                                that.etpsInnerInvtNo = res.data.etpsInnerInvtNo
                                this.returnBackFn()
                            } else {
                                that.$message.warning(res.message)
                            }
                        })
                        .finally(() => {
                            // this.saveingStatu = false
                        })
                } else {
                    //     console.log("error submit!!");
                    return false
                }
            })
        },
			// 加载字典值
			initDictData(dictCode) {
				let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
				if (dictOptions != null && dictOptions.length > 0) {
					if (dictCode.includes('erp_customs_ports')) {
						let allOptions = []
						dictOptions.forEach(item => {
							allOptions.push({
								text: item.value + ' | ' + item.title,
								value: item.value
							})
						})
						this.customsPortsOptions = allOptions
						this.$refs.selectItem.value = this.record.impexpPortcd
						this.$refs.selectItem2.value = this.record.dclplcCuscd
					} else if (dictCode.includes('erp_countries')) {
						let allOptions = []
						dictOptions.forEach(item => {
							allOptions.push({
								text: item.value + ' | ' + item.title,
								value: item.value
							})
						})
						this.countries = allOptions
						this.$refs.selectItem3.value = this.record.stshipTrsarvNatcd
					}
				} else {
					//根据字典Code, 初始化字典数组
					ajaxGetDictItems(dictCode, null).then((res) => {
						if (res.success) {
							sessionStorage.setItem(dictCode, JSON.stringify(res.result))
							let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
							if (dictOptions != null && dictOptions.length > 0) {
								if (dictCode.includes('erp_customs_ports')) {
									let allOptions = []
									dictOptions.forEach(item => {
										allOptions.push({
											text: item.value + ' | ' + item.title,
											value: item.value
										})
									})
									this.customsPortsOptions = allOptions
									this.$refs.selectItem.customsPortsOptions = allOptions
									this.$refs.selectItem2.customsPortsOptions = allOptions
									this.$refs.selectItem.value = this.record.impexpPortcd
									this.$refs.selectItem2.value = this.record.dclplcCuscd
									this.$refs.selectItem.$forceUpdate()
									this.$refs.selectItem2.$forceUpdate()
								} else if (dictCode.includes('erp_countries')) {
									let allOptions = []
									dictOptions.forEach(item => {
										allOptions.push({
											text: item.value + ' | ' + item.title,
											value: item.value
										})
									})
									this.countries = allOptions
									this.$refs.selectItem3.value = this.record.stshipTrsarvNatcd
									this.$refs.selectItem3.$forceUpdate()
								}
							}
						}
					})
				}
			},
    },
    mounted() {
        this.initThisPage()
        this.initThisCustomer()
			//国别地区
			this.initDictData('erp_customs_ports,name,customs_port_code')
			this.initDictData('erp_countries,name,code,isenabled=0')
			this.record.sysId = this.$route.query.sysid
            this.record.dclTypecd = '1'
            this.record.vrfdedMarkcd = '0'
            this.$forceUpdate()
    },
    watch: {
        record: {
            handler(newValue, oldValue) {
                this.returnBackFn()
            },
            deep: true
        },
        'record.dclcusTypecd': {
            handler(newValue, oldValue) {
                this.$nextTick(() => {
                    //修改之后
                    setTimeout(() => {
                        this.returnDclcusTypecdBackFn2(newValue)
                    }, this.delay)
                })
            }
        }
    }
}
</script>
