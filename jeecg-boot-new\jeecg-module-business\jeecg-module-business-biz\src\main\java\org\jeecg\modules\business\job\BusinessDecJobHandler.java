package org.jeecg.modules.business.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.config.mqtoken.UserTokenContext;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.business.mapper.CommonMapper;
import org.jeecg.modules.business.service.IDockingEasyPassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * 业务模块定时任务处理类 -- 报关单类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2024/11/28 23:40
 */
@Component
@Slf4j
public class BusinessDecJobHandler {

    @Autowired
    private IDockingEasyPassService dockingEasyPassService;
    @Autowired
    private CommonMapper commonMapper;

    /**
     * 每日自动同步报关单[通用]定时任务
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/29 10:55
     */
    @XxlJob(value = "SyncDecCommonJob")
    public ReturnT<String> SyncDecCommonJob(String params) {
        log.info("每日自动同步报关单[通用]定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String ownerCode = jsonObject.getString("ownerCode");
        String ownerName = jsonObject.getString("ownerName");
        String startDate = jsonObject.getString("startDate");
        String lastDate = jsonObject.getString("lastDate");
        String isAll = jsonObject.getString("isAll");
        log.info("【SyncDecCommonJob】获取到的参数：{};{}", ownerCode, ownerName);
        // 1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        Result<?> result = dockingEasyPassService.dockingDecCommonSync(ownerCode, ownerName, startDate, lastDate, isAll);
        // 2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        log.info("【每日自动同步报关单[通用]定时任务】执行结果：{}", result.getResult());
        log.info("每日自动同步报关单[通用]定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }

    /**
     * 每日自动同步报关单[通用]定时任务 -- 根据申报单位
     *
     * @param params
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/29 10:55
     */
    @XxlJob(value = "SyncDecSBCommonJob")
    public ReturnT<String> SyncDecSBCommonJob(String params) {
        log.info("每日自动同步报关单[通用--根据申报单位]定时任务====start");
        if (isBlank(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(params);
        String agentCode = jsonObject.getString("agentCode");
        String agentName = jsonObject.getString("agentName");
        String startDate = jsonObject.getString("startDate");
        String lastDate = jsonObject.getString("lastDate");
        String isAll = jsonObject.getString("isAll");
        log.info("【SyncDecSBCommonJob】获取到的参数：{};{}", agentCode, agentName);
        // 1.设置线程会话Token
        UserTokenContext.setToken(getTemporaryToken());
        Result<?> result = dockingEasyPassService.dockingDecSBCommonSync(agentCode, agentName, startDate, lastDate, isAll);
        // 2.使用完删除Token，避免性能（这一步可以不做，但是为了性能建议执行）
        UserTokenContext.remove();
        log.info("【每日自动同步报关单[通用--根据申报单位]定时任务】执行结果：{}", result.getResult());
        log.info("每日自动同步报关单[通用--根据申报单位]定时任务====end");
        if (!result.isSuccess()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
        }
        StringBuilder message = new StringBuilder("执行完毕!");
        Optional.ofNullable(result)
                .map(Result::getResult)
                .ifPresent(obj -> message.append(Objects.toString(obj, "")));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, message.toString());
    }

    /**
     * 获取临时令牌
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/19 上午11:16
     */
    public String getTemporaryToken() {
        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        LoginUser sysUser = commonMapper.getUserByName("admin");
        // 模拟登录生成Token
        String token = JwtUtil.sign(sysUser.getUsername(), sysUser.getPassword());
        // 设置Token缓存有效时间为 5 分钟
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, 5 * 60 * 1000);
        return token;
    }
}
