package org.jeecg.modules.business.controller;

import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.modules.business.entity.Order;
import org.jeecg.modules.business.entity.StorageInfo;
import org.jeecg.modules.business.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 订单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@RestController
@RequestMapping("/business/order")
public class OrderController {
    @Autowired
    private IOrderService orderService;

    /**
     * 订单列表 -- 分页
     *
     * @param order
     * @param pageNo
     * @param pageSize
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/18 16:24
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(Order order,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        return orderService.queryPageList(pageNo, pageSize, order, req);
    }

    /**
     * 根据ID查询订单信息
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:37
     */
    @ApiOperation(value = "根据ID查询订单信息", notes = "根据ID查询订单信息")
    @GetMapping(value = "/getOrderById")
    public Result<?> getOrderById(@RequestParam("id") String id) {
        return orderService.getOrderById(id);
    }

    /**
     * 保存订单
     *
     * @param order
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:12
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存订单")
    @ApiOperation(value = "保存订单", notes = "保存订单")
    @PostMapping(value = "/saveOrder")
    public Result<?> saveOrder(@RequestBody Order order) {
        return orderService.saveOrder(order);
    }

    /**
     * 删除订单
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:27
     */
    @AutoLog(value = "删除订单")
    @ApiOperation(value = "删除订单", notes = "删除订单")
    @RequestMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam("ids") String ids) {
        return orderService.deleteBatch(ids);
    }

    /**
     * 操作失效
     *
     * @param id
     * @param status
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 13:24
     */
    @Idempotent(timeout = 2, message = "存在重复请求，已忽略")
    @AutoLog(value = "操作失效")
    @ApiOperation(value = "操作失效", notes = "操作失效")
    @PostMapping(value = "/handleLose")
    public Result<?> handleLose(@RequestParam("id") String id,
                                     @RequestParam("status") String status) {
        return orderService.handleLose(id, status);
    }

    /**
     * 订单生成进口业务
     *
     * @param ids
     * @param ieFlag
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/24 10:47
     */
    @ApiOperation(value = "订单生成进口业务", notes = "订单生成进口业务")
    @GetMapping(value = "/getCreateBusiness")
    public Result<?> getCreateBusiness(@RequestParam("ids") String ids,
                                        @RequestParam("ieFlag") String ieFlag,
                                        @RequestParam(value = "type", required = false) String type) {
        return orderService.getCreateBusiness(ids, ieFlag, type);
    }

    /**
     * 从品名库重新带取数据
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/2/27 11:19
     */
    @ApiOperation(value = "从品名库重新带取数据", notes = "从品名库重新带取数据")
    @GetMapping(value = "/getRebringData")
    public Result<?> getRebringData(@RequestParam("ids") String ids) {
        return orderService.getRebringData(ids);
    }

    /**
     * 导入订单
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 15:24
     */
    @RequestMapping(value = "/importOrder", method = RequestMethod.POST)
    public Result<?> importOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return orderService.importOrder(request, response);
    }

    /**
     * 导出订单列表Excel
     *
     * @param request
     * @param response
     * @param order
     * @return void
     * <AUTHOR>
     * @date 2024/9/20 10:02
     */
    @RequestMapping(value = "/exportOrderXls")
    public void exportOrderXls(HttpServletRequest request, HttpServletResponse response,
                                      Order order) throws IOException {
        orderService.exportOrderXls(request, response, order);
    }
    /**
     * 导入订单（亚是加企业）
     *
     * @param request HttpServletRequest对象，用于获取请求信息
     * @param response HttpServletResponse对象，用于返回响应信息
     * @return 操作结果
     * <AUTHOR>
     */
    @RequestMapping(value = "/importOrderByYSJ", method = RequestMethod.POST)
    public Result<?> importOrderByYSJ(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return orderService.importOrderByYSJ(request, response);
    }




}
