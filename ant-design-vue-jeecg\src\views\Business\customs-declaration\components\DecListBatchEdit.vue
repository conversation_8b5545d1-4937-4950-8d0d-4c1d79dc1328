<template>
	<a-modal :disableSubmit="disableSubmit" :title="title" :visible="visible" :width="width" cancelText="取消"
		selfCloseAction="closePop" @cancel="handleCancel" @ok="handleSave">
		<j-form-container :disabled="disableSubmit">
			<a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules">
				<a-row :gutter="24" justify="center" type="flex">
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="币制" prop="currencyCode">
							<j-dict-select-tag v-model="model.currencyCode" dictCode="erp_currencies,name,currency,currency,1=1"
								placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="目的国" prop="destinationCountry">
							<j-dict-select-tag v-model="model.destinationCountry" dictCode="GBDQ-DEC" placeholder="请选择，支持搜索"
								type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="境内货源地" prop="districtCode">
							<j-dict-select-tag v-model="model.districtCode" dictCode="erp_districts,name,code,del_Flag=0"
								placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="产地" prop="destCode">
							<j-dict-select-tag v-model="model.destCode" dictCode="XZQH" placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="征免方式" prop="faxTypeCode">
							<j-dict-select-tag v-model="model.faxTypeCode" dictCode="ZJMSFS" placeholder="请选择，支持搜索"
								type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="原产国" prop="desCountry">
							<j-dict-select-tag v-model="model.desCountry" dictCode="GBDQ-DEC" placeholder="请选择，支持搜索"
								type="node-limit" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="品牌类型" prop="pplx">
							<j-dict-select-tag v-model="model.pplx" dictCode="PPLX" placeholder="请选择，支持搜索" type="node-limit" />
						</a-form-model-item>
					</a-col>
				</a-row>
			</a-form-model>
		</j-form-container>
	</a-modal>
</template>
<script>

export default {
	name: "DecListBatchEdit",
	data() {
		return {
			title: "批量修改",
			visible: false,
			disableSubmit: false,
			width: 500,
			model: {
			},
			validatorRules: {
				// ieFlag: [{ required: true, message: '请选择进出口标识' }],
				// cabinCode: [{ required: true, message: '请输入储位代码!' }],
				// areaCode: [{required: true, validator: validateAreaCode, trigger: 'change'}]
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 6 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
		}
	},
	methods: {
		add() {
			this.visible = true
		},
		handleSave() {
			// 触发表单验证
			this.$refs.form.validate(async valid => {
				if (valid) {
					console.log('最终保存的批量修改数据是：', this.model)
					// 向父组件发送数据，用于覆盖表体项
					this.$emit('batchUpdate', this.model)
					this.handleCancel()
				}
			})
		},
		handleCancel() {
			this.model = {}
			this.visible = false
		}
	},
}
</script>

<style lang="less" scoped></style>