<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-card size="small" :bordered="false" title="基础信息" class="card-box">
          <a-row type="flex" justify="center" class="wrapper_row a-left text-left" id="box-ko">
            <a-col :span="24">
              <a-form-model-item label="公司全称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseFullName">
                <j-remarks-component v-model="model.enterpriseFullName" placeholder="请输入公司全称" :maxLength="128"
                  :readOnly="formDisabled" disabled="true"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="英文名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="englishName">
                <j-remarks-component v-model="model.englishName" placeholder="请输入英文名称" :maxLength="128"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="注册地址（省市区）" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="registeredAddress">
                <j-area-linkage type="cascader" v-model="model.registeredAddress" placeholder="请输入省市区" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="注册详细地址" :labelCol="labelCol" :wrapperCol="wrapperCol"
                prop="registeredAddressDetail">
                <j-remarks-component v-model="model.registeredAddressDetail" placeholder="请输入注册详细地址" :maxLength="256"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="英文地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="englishAdress">
                <j-remarks-component v-model="model.englishAdress" placeholder="请输入英文地址" :maxLength="256"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="统一社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol"
                prop="unifiedSocialCreditCode">
                <a-input v-model="model.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" :maxLength="32" disabled="true"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="企业法人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseLegalPerson">
                <j-remarks-component v-model="model.enterpriseLegalPerson" placeholder="请输入企业法人" :maxLength="64"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="联系人中文姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cotactChName">
                <j-remarks-component v-model="model.cotactChName" placeholder="请输入联系人中文姓名" :maxLength="64"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="联系人英文姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cotactEnName">
                <j-remarks-component v-model="model.cotactEnName" placeholder="请输入联系人英文姓名" :maxLength="128"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="海关注册编码" :labelCol="labelCol" :wrapperCol="wrapperCol"
                prop="customsDeclarationCode">
                <a-input v-model="model.customsDeclarationCode" placeholder="请输入海关注册编码" :maxLength="32"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="海关商检代码" :labelCol="labelCol" :wrapperCol="wrapperCol"
                prop="customsInspectionCode">
                <a-input v-model="model.customsInspectionCode" placeholder="请输入海关商检代码" :maxLength="32"></a-input>
              </a-form-model-item>
            </a-col>

            <a-col :span="24">
              <a-form-model-item label="网址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="webSite">
                <j-remarks-component v-model="model.webSite" placeholder="请输入网址" :maxLength="128"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="compayTelphone">
                <template #label>
                  <span>公司电话</span>
                  <a-tooltip slot="suffix" title="请输入10到16位带区号的电话">
                    <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                  </a-tooltip>
                </template>
                <a-input v-model="model.compayTelphone" placeholder="请输入公司电话" :maxLength="16"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="compayFax">
                <template #label>
                  <span>公司传真</span>
                  <a-tooltip slot="suffix" title="请输入带区号的传真">
                    <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                  </a-tooltip>
                </template>
                <a-input v-model="model.compayFax" placeholder="请输入公司传真" :maxLength="16"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="公司邮箱" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="suppliersEmail">
                <j-remarks-component v-model="model.suppliersEmail" placeholder="请输入公司邮箱" :maxLength="64"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remarks">
                <j-remarks-component placeholder="请输入备注" v-model="model.remarks" :maxLength="256"
                  :readOnly="formDisabled" />
              </a-form-model-item>
            </a-col>
						<a-col :span="24">
							<a-form-model-item label="操作员卡号" :labelCol="labelCol" :wrapperCol="wrapperCol"
																 prop="swid">
								<a-input v-model="model.swid" placeholder="请输入操作员卡号" :maxLength="64"></a-input>
							</a-form-model-item>
						</a-col>
          </a-row>
        </a-card>

        <a-row v-if="false" type="flex" justify="center" class="wrapper_row a-left text-left">

          <a-card size="small" :bordered="false" title="报关信息" class="card-box">
            <template #extra><a class="tipsText" @click="showMsgModal">如何获取私钥证书</a></template>
            <div class="box-row">
              <a-col :span="24">
                <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sditdsUserloginname">
                  <template #label>
                    <span>用户名</span>
                    <a-tooltip slot="suffix" title="登录山东电子口岸跨境电商系统所使用的用户名">
                      <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                    </a-tooltip>
                  </template>
                  <j-remarks-component v-model="model.sditdsUserloginname" placeholder="请输入用户名" :maxLength="64"
                    :readOnly="formDisabled" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sditdsUserpassowrd">
                  <template #label>
                    <span>密码</span>
                    <a-tooltip slot="suffix" title="登录山东电子口岸跨境电商系统所使用的密码">
                      <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                    </a-tooltip>
                  </template>
                  <!-- <a-input
                v-model="model.sditdsUserpassowrd"
                placeholder="请输入密码"
                :maxLength="64"
              ></a-input> -->
                  <a-input-password v-model="model.sditdsUserpassowrd" placeholder="请输入密码" :maxLength="64" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24" class="privateKey-left">
                <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="privateKeyCertificate">
                  <template #label>
                    <span>私钥证书</span>
                    <a-tooltip slot="suffix" title="电子口岸颁发给企业的私钥证书">
                      <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                    </a-tooltip>
                  </template>
                  <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="privateKeyCertificate">
                    <j-upload v-model="model.privateKeyCertificate"></j-upload>
                  </a-form-model-item>
                </a-form-model-item>
              </a-col>
              <!-- <a-col :span="24">
            <a-form-model-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              prop="messageTransmissionNumber"
            >
            <template #label>
                <span>报文传输编号</span>
                <a-tooltip slot="suffix" title="向中国电子口岸数据中心申请数据交换平台的用户编号">
                  <a-icon type="question-circle" theme="twoTone" style="margin-top: 13px; margin-left: 2px" />
                </a-tooltip>
              </template>
              <a-input
                v-model="model.messageTransmissionNumber"
                placeholder="请输入报文传输编号"
                :maxLength="32"
              ></a-input>
            </a-form-model-item>
          </a-col> -->
              <a-col :span="24" :pull="2" style="margin-top: 15px">
                <a-modal v-model="visible2" title="如何获取私钥证书" :width="800" @ok="handleOk2">
                  <template slot="footer">
                    <a-button type="primary" @click="handleOk2" icon="bulb">我知道了</a-button>
                  </template>

                  <div v-viewer class="box-ming">
                    <p>如何获取私钥证书：</p>
                    <p>(1)首先打开网址 <a href="http://www.sditds.gov.cn/ecommerceceb/js/ceb/portalMap.jsp"
                        target="_blank">http://www.sditds.gov.cn/ecommerceceb/js/ceb/portalMap.jsp</a>
                      <br> 山东电子口岸跨境电商系统
                    </p>
                    <p>(2)找到自己的区域，以下以青岛为例</p>
                    <img src="../../../assets/QQ图片20220422165339.png" class="tupStyle" />
                    <p>(3)用户名和密码：下图红框所圈的内容</p>
                    <img src="../../../assets/QQ图片20220422165513.png" class="tupStyle" />
                    <p>(4)私钥证书：登录进来后，下载右边红框中的内容</p>
                    <img src="../../../assets/QQ图片20220422165603.png" class="tupStyle" />
                  </div>

                </a-modal>
              </a-col>
            </div>


          </a-card>
        </a-row>

				<a-card size="small" :bordered="false" title="信用等级及行政处罚" class="card-box">
					<EnterpriseCustomsRecord ref="customsRecordRef"></EnterpriseCustomsRecord>
				</a-card>

				<a-card size="small" :bordered="false" title="箱单发票模版" class="card-box">
					<attachments-info ref="attachmentRef" :disabled="formDisabled"/>
				</a-card>

				<a-card size="small" :bordered="false" title="报文传输编号" class="card-box">
					<CustomsAreaInfoList></CustomsAreaInfoList>
				</a-card>

        <a-card size="small" :bordered="false" title="签章信息" class="card-box">
          <a-row class="qianz-box">
            <a-col :span="4">
              <div class="imglabel">公章：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="companyStamp">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.companyStamp"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <div class="imglabel">合同章：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="contractStamp">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.contractStamp"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <div class="imglabel">单据章：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="documentStamp">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.documentStamp"></j-image-upload>
              </a-form-model-item>
            </a-col>


            <a-col :span="4">
              <div class="imglabel">采购章：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="purchasingStamp">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.purchasingStamp"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <div class="imglabel">财务章：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="financialStamp">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.financialStamp"></j-image-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <div class="imglabel">公司照片：</div>
              <a-form-model-item label="" :labelCol="imgLabelCol" :wrapperCol="imgWrapperCol" prop="compayPicture">
                <j-image-upload bizPath="orderPaymentInfo" v-model="model.compayPicture"></j-image-upload>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>

        <a-divider />
        <a-row type="flex" justify="end">
          <a-col :span="2">
            <a-button type="primary" icon="plus" @click="submitForm">保存</a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
    <a-modal v-model="visible" title="提示：" @ok="handleOk" @cancel="handleCancel">
      <p>是否保存您的更改？</p>
    </a-modal>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import CustomsAreaInfoList from '../../customsAreaInfo/CustomsAreaInfoList.vue'
import EnterpriseCustomsRecord from "@/views/enterprise/components/EnterpriseCustomsRecord.vue";
import AttachmentsInfo from "@/views/enterprise/components/AttachmentsInfo.vue";

export default {
  name: 'EnterpriseInfoForm',
  components: {
		EnterpriseCustomsRecord,
    CustomsAreaInfoList,
		AttachmentsInfo
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
  },
  data() {
    return {
      model: {enterpriseCustomsRecordList:[]},
      isSave: 0,
      visible: false,
      visible2: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      imgLabelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      imgWrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
      },
      confirmLoading: false,
      validatorRules: {
        enterpriseFullName: [{ required: true, message: '请输入公司全称!' }],
        englishName: [{ required: false, message: '请输入英文名称!' },
        {
          pattern: /^[^(\u4E00-\u9FA5)]+$/,
          message: '请输入正确的英文名称!',
        },],
        unifiedSocialCreditCode: [
          { required: false, message: '请输统一社会信用代码!' },
          {
            pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/,
            message: '请输入正确的统一社会信用代码'
          }
        ],
        // englishAdress: [{ required: false, message: '请输入英文地址!' },
        // {
        //   pattern: /^[^(\u4E00-\u9FA5)]+$/,
        //   message: '请输入正确的英文地址!',
        // },],
        cotactEnName: [{ required: false, message: '请输入联系人英文姓名!' },
        {
          pattern: /^[^(\u4E00-\u9FA5)]+$/,
          message: '请输入正确的英文姓名!',
        },],
        registeredAddress: [{ required: true, message: '请输入注册地址!' }],
        registeredAddressDetail: [{ required: true, message: '请输入注册详细地址!' }],
        cotactChName: [{ required: true, message: '请输入联系人中文姓名!' }],
        customsDeclarationCode: [
          { required: false, message: '请输入海关注册编码!' },
          {
            pattern: /^[A-Za-z0-9]{10,10}$/,
            message: '请输入10位英数字的海关注册编码!',
          },
        ],
        customsInspectionCode: [
          { required: false, message: '请输入海关注册编码!' },
          {
            pattern: /^[A-Za-z0-9]{10,10}$/,
            message: '请输入10位英数字的海关注册编码!',
          },
        ],
        webSite: [
          { required: false },
          {
            pattern: /^((ht|f)tps?):\/\/[\w\-]+(\.[\w\-]+)+([\w\-.,@?^=%&:\/~+#]*[\w\-@?^=%&\/~+#])?$/,
            message: '请输入正确的网址!',
          },
        ],
        compayTelphone: [
          { required: false, message: '请输入联系电话!' },
          {
            pattern: /^((([+0-9]{1,4})?[\s-]?([0-9]{3,4})([\s-]?[0-9]{6,8})[\s-]?([0-9]{3,5})?)|[0-9]{11})$/,
            message: '请输入格式正确的电话!'
          },
          {
            pattern: /^.{0,16}$/,
            message: '请控制在16个字符以内!'
          }

        ],
        compayFax: [
          { required: false, message: '请输入传真号码!' },
          {
            pattern: /^((([+0-9]{1,4})?[\s-]?([0-9]{3,4})([\s-]?[0-9]{6,8})[\s-]?([0-9]{3,5})?)|[0-9]{11})$/,
            message: '请输入格式正确的传真号码!',
          },

        ],
        suppliersEmail: [
          { required: false },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/,
            message: '请输入正确的电子邮件!',
          },
        ],
      },
      url: {
        add: '/EnterpriseInfo/enterpriseInfo/add',
        edit: '/EnterpriseInfo/enterpriseInfo/edit',
        list: '/EnterpriseInfo/enterpriseInfo/list',
      },
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    //获取企业信息，并渲染到页面
    this.$nextTick(() => {
      getAction('/EnterpriseInfo/enterpriseInfo/getCollectionEnterpriseList').then((res) => {
        if (res.success && res.result.list[0]) {
          this.model = res.result.list[0]
					this.$emit("initEnterpriseModel",this.model)
					this.$refs.customsRecordRef.dataSource = res.result.list[0].enterpriseCustomsRecordList
					let attachmentList = []
					if (this.model.packListTempl) {
						let attachment = {
							attachmentsFileName: this.model.packListTempl,
							attachmentsFileType: 'receipt'
						}
						attachmentList.push(attachment)
					}
					if (this.model.invoiceTempl) {
						let attachment = {
							attachmentsFileName: this.model.invoiceTempl,
							attachmentsFileType: 'release'
						}
						attachmentList.push(attachment)
					}
					if (this.model.salescontractTempl) {
						let attachment = {
							attachmentsFileName: this.model.salescontractTempl,
							attachmentsFileType: 'salescontract'
						}
						attachmentList.push(attachment)
					}
					this.model.attachmentList = attachmentList
					this.$refs.attachmentRef.initModel(this.model)
        }
      })
    })
  },
  mounted() { },
  updated() {
    if (this.model.id) {
      this.$emit('handleCodeInfo', this.model.id)
    }
  },
  watch: {
    model: {
      deep: true,
      handler(val) {
        this.isSave = this.isSave + 1
        if (this.isSave > 1) {
          this.$emit('handleSave', false)
        }
      },
    },
  },
  methods: {
    showMsgModal() {
      this.visible2 = true
    },
    handleOk2() {
      this.visible2 = false
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          httpAction(httpurl, this.model, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                this.$emit('handleCodeInfo', res.result)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.$emit('handleSave', true)
              this.isSave = 0
              that.confirmLoading = false
            })
        }
      })
    },

    showModal() {
      this.visible = true
    },
    handleOk(e) {
      this.submitForm()
      this.visible = false
    },
    handleCancel() {
      getAction('/EnterpriseInfo/enterpriseInfo/getCollectionEnterpriseList')
        .then((res) => {
          if (res.success && res.result.list[0]) {
            this.model = res.result.list[0]
          } else {
            this.$router.go(0)
          }
        })
        .then(() => {
          this.isSave = 0
          this.$emit('handleSave', true)
        })
        .catch()
    },
  },
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';

#box-ko {
  padding: 4px;
  margin-left: 59px;
}

.wrapper_row .ant-form-item {
  width: 500px;

}

.ant-col-24 {
  display: block;
  box-sizing: border-box;
  width: 100%;
  /* margin-bottom: -5px; */
}

.box-row {
  margin-left: 62px;
}

.qianz-box {
  margin-left: 25px;
}

.box-ming {
  margin-left: 20%;
  font-size: 14px;
  color: black;
}

.tupStyle {
  width: 400px;
}

.tipsText {
  font-size: 14px;
  margin-left: -610%;
  font-weight: 600;
  color: #1A3176;
}

/deep/.text-left .ant-form-item-control {
  position: relative;
  line-height: 40px;
  zoom: 1;
  margin-left: 50px;
}

/deep/.privateKey-left .ant-form-item-control {
  position: relative;
  line-height: 40px;
  zoom: 1;
  margin-left: 25px;
}
</style>
