package org.jeecg.modules.business.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.shiro.SecurityUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.base.BaseMap;
import org.jeecg.common.modules.redis.client.JeecgRedisClient;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.base.entity.SysAnnouncement;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.oss.OssBootUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.core.*;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DictQuery;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.*;
import org.jeecg.modules.business.util.word.DefineMethodPolicy;
import org.jeecg.modules.business.util.zipcompress.CompressUtil;
import org.jeecg.modules.business.util.zipcompress.FilesZipUtil;
import org.jeecg.modules.business.util.zipcompress.ZipTempPO;
import org.jeecg.modules.business.vo.DictModelVO;
import org.jeecg.modules.business.vo.OrderProductExcelVO;
import org.jeecg.modules.business.vo.OrderProductJinKouExcelVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.result.ExcelImportResult;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.modules.business.util.PrintUtil.wordToPdf;

/**
 * @Description: 订单信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-17
 * @Version: V1.0
 */
@Api(tags="订单信息表")
@RestController
@RequestMapping("/export/orderInfo")
@Slf4j
public class OrderInfoController extends JeecgController<OrderInfo, IOrderInfoService> {
	@Autowired
	private IOrderInfoService orderInfoService;
	@Autowired
	private IOrderTransportationInfoService orderTransportationInfoService;
	@Autowired
	private IEnterpriseInfoService enterpriseInfoService;
	@Autowired
	private IOrderReceiptNoticeInfoService orderReceiptNoticeInfoService;
	@Autowired
	private IOrderProductInfoService orderProductInfoService;
	@Autowired
	private ILogisticsInfoService logisticsInfoService;
	@Autowired
	private IOrderShippingBookingInfoService orderShippingBookingInfoService;
	@Autowired
	private IDomesticSuppliersInfoService domesticSuppliersInfoService;
	@Autowired
	private ICustomsBrokerInfoService customsBrokerInfoService;
	@Autowired
	private IOrderCustomsDecExportInfoService orderCustomsDecExportInfoService;
	@Autowired
	private IDecHeadService decHeadService;
	@Autowired
	private IEcommercePlatformInfoService iEcommercePlatformInfoService;
	@Autowired
	private IProductInfoService productInfoService;
	@Autowired
	private IMatchingInfoService matchingInfoService;
	@Autowired
	private IOverseasPayerInfoService overseasPayerInfoService;
	@Autowired
	private IErpCityportsService erpCityportsService;
	@Autowired
	private IErpChinaPortsService erpChinaPortsService;
	@Autowired
	private IErpCurrenciesService erpCurrenciesService;
	@Autowired
	private IErpUnitsService erpUnitsService;
	@Autowired
	private IErpPackagesTypesService erpPackagesTypesService;
	@Autowired
	private IErpCustomsPortsService erpCustomsPortsService;
	@Autowired
	private IErpTransportTypesService erpTransportTypesService;
	@Autowired
	private IErpCountriesService erpCountriesService;
	@Autowired
	private IErpDistrictsService erpDistrictsService;
	@Autowired
	private IOrderSummaryInfoService orderSummaryInfoService;
	@Autowired
	private ICustomsAreaInfoService customsAreaInfoService;
	@Autowired
    private IBankAccountInfoService bankAccountInfoService;
	@Autowired
	private IBankAccountInfoHxService bankAccountInfoHxService;
	@Autowired
	private OrderInfoMapper orderInfoMapper;
	@Autowired
	private OrderProductInfoMapper orderProductInfoMapper;
	@Autowired
	private ProductInfoMapper productInfoMapper;
	@Resource
	private JeecgRedisClient jeecgRedisClient;
	@Autowired
	private AttachmentsInfoMapper attachmentsInfoMapper;
	@Autowired
	private ProductCategoryInfoMapper productCategoryInfoMapper;
	@Autowired
	private RedisUtil redisUtil;
	@Lazy
	@Autowired
	private ISysBaseAPI sysBaseApi;
	@Autowired
	private ICommissionerService commissionerService;
	@Autowired
	private IStorageInfoService storageInfoService;
	@Autowired
	private IItfDclIoDeclService iItfDclIoDeclService;
	@Autowired
	private CommonMapper commonMapper;
	@Autowired
	private DecListMapper decListMapper;
	@Value("${jeecg.path.upload}")
	private String upLoadPath;


	// 敏感国家一级
//	private List<String> country_l1 = Arrays.asList("PRK", "IRN", "SDN", "MMR", "CUB");
	// 敏感国家二级
//	private List<String> country_l2 = Arrays.asList("CIV", "ERI", "LBR", "SLE", "SOM", "SYR", "COD");
	// 敏感国家三级
//	private List<String> country_l3 = Arrays.asList("AND", "ARE", "ASM", "BHS", "BLZ", "CYM", "CYP", "DJI", "LBN", "LIE", "MHL", "NRU", "PAN", "SYC", "VGB", "BMU", "WSM");
	// 敏感商品类别
//	private List<String> productRisks = Arrays.asList("电子产品", "钟表", "化妆品", "眼镜", "箱包", "服装", "家具", "水产品");


	/**
	 * 分页列表查询
	 *
	 * @param orderInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="订单信息表-分页列表查询", notes="订单信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OrderInfo orderInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="orderStatus", defaultValue = "-1") Integer orderStatus,
								   @RequestParam(name="statusQuery", defaultValue = "-1") String statusQuery,
								   @RequestParam(name="orderStatusStr",defaultValue = "") String orderStatusStr,
								   @RequestParam(name="notEq",defaultValue = "") String notEq,
								   HttpServletRequest req) {
		orderInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
//		if (!"-1".equals(orderStatus)) {
//			orderInfo.setOrderStatus(orderStatus);
//		}
		orderInfo.setDelFlag(DEL_FLAG_0);
		if (isBlank(orderInfo.getOrderTypes())) {
			orderInfo.setOrderTypes("");
		}
//		QueryWrapper<OrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(orderInfo, req.getParameterMap());
		//自定义查询条件
		QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<OrderInfo>()
				.eq(isNotNull(orderInfo.getOrderStatus()),"order_info.order_status",orderInfo.getOrderStatus())
				.eq(isNotNull(orderInfo.getIeFlag()),"order_info.ie_flag",orderInfo.getIeFlag())
				.eq(isNotNull(orderInfo.getReceiveMoneyFlag()),"order_info.RECEIVE_MONEY_FLAG",orderInfo.getReceiveMoneyFlag())
				.in(!statusQuery.equals("-1"), "order_info.order_status", Arrays.asList(statusQuery.split(",")))
				.in(isNotBlank(orderInfo.getOrderTypes()), "order_info.ORDER_TYPE",Arrays.asList(orderInfo.getOrderTypes().split(",")))
				.notIn(isNotBlank(notEq), "order_info.ORDER_TYPE",Arrays.asList(notEq.split(",")))
				.like(isNotNull(orderInfo.getOrderProtocolNo()),"order_info.ORDER_PROTOCOL_NO",orderInfo.getOrderProtocolNo())
//				.eq(isNotNull(orderInfo.getSupervisionMode()),OrderInfo::getSupervisionMode,orderInfo.getSupervisionMode())
				.eq(isNotNull(orderInfo.getTradingType()),"order_info.TRADING_TYPE",orderInfo.getTradingType())
				.eq(isNotBlank(orderInfo.getBuyer()),"order_info.BUYER",orderInfo.getBuyer())
				.eq(isNotBlank(orderInfo.getOverseasPayerInfoId()),"order_info.OVERSEAS_PAYER_INFO_ID",orderInfo.getOverseasPayerInfoId())
				.like(isNotBlank(orderInfo.getSalesman()),"order_info.SALESMAN",orderInfo.getSalesman())
				.eq(isNotBlank(orderInfo.getOrderType()),"order_info.ORDER_TYPE", orderInfo.getOrderType())
				.like(isNotNull(orderInfo.getCustomsNumber()),"order_info.CUSTOMS_NUMBER",orderInfo.getCustomsNumber())
				.like(isNotNull(orderInfo.getDeliveryNumbers()),"order_info.DELIVERY_NUMBERS",orderInfo.getDeliveryNumbers())
				.like(isNotNull(orderInfo.getInvoiceNo()),"order_info.INVOICE_NO",orderInfo.getInvoiceNo())
				.like(isNotNull(orderInfo.getExportContractNo()),"order_info.EXPORT_CONTRACT_NO",orderInfo.getExportContractNo())

				.apply(isNotBlank(orderInfo.getSupervisionMode()), "ORDER_INFO.SUPERVISION_MODE = "+orderInfo.getSupervisionMode())
				.apply("ORDER_INFO.DEL_FLAG = "+orderInfo.getDelFlag())
				.apply("ORDER_INFO.TENANT_ID = "+orderInfo.getTenantId())
				.apply(isNotNull(orderInfo.getEnglishName()),
						"ORDER_PRODUCT_INFO.ENGLISH_NAME LIKE  '%"+orderInfo.getEnglishName()+"%' ")
				.apply(isNotNull(orderInfo.getChineseName()),
						"ORDER_PRODUCT_INFO.CHINESE_NAME LIKE  '%"+orderInfo.getChineseName()+"%' ")
				//是否生成报关单
				.apply("1".equals(orderInfo.getHasRelDecNos()),"dec_head.APPLY_NUMBER IS NOT NULL")
				.apply("2".equals(orderInfo.getHasRelDecNos()),"dec_head.APPLY_NUMBER IS NULL");

		Page<OrderInfo> page = new Page<OrderInfo>(pageNo, pageSize);
//		IPage<OrderInfo> pageList = orderInfoService.page(page, queryWrapper);
		IPage<OrderInfo> pageList = orderInfoMapper.queryPageList(page, queryWrapper,orderInfo.getHasRelDecNos());
		String tenantName = null;
		try {
			// 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
			Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
			if (isNotEmpty(tenant.getResult())) {
				tenantName = tenant.getResult().getName();
			} else {
				String accessToken = req.getHeader(X_ACCESS_TOKEN);
				String o = (String) redisUtil.get(PREFIX_TENANT_TOKEN_OBJ + accessToken);
				tenantName = (String) JSONObject.parseObject(o).get("name");
			}
		} catch (Exception e) {
			log.error("获取租户名出现异常：" + e.getMessage());
		}
		if (isNotEmpty(pageList.getRecords())) {
			List<String> orderIds = pageList.getRecords().stream().map(OrderInfo::getId).collect(Collectors.toList());
			List<String> orderNos = pageList.getRecords().stream().map(OrderInfo::getOrderProtocolNo).collect(Collectors.toList());
			List<DecHead> decHeadList = decHeadService.list(new LambdaQueryWrapper<DecHead>()
					.in(DecHead::getApplyNumber, orderIds));
			List<StorageInfo> storageInfoList = storageInfoService.list(new LambdaQueryWrapper<StorageInfo>().in(StorageInfo::getOrderNo, orderNos));
			List<ItfDclIoDecl> itfDclIoDeclList = iItfDclIoDeclService.list(new LambdaQueryWrapper<ItfDclIoDecl>().in(ItfDclIoDecl::getOrderProtocolNo, orderNos));
			Map<String, List<DecHead>> decHeadMap = new HashMap<>();
			Map<String, List<StorageInfo>> storageInfoMap = new HashMap<>();
			Map<String, List<ItfDclIoDecl>> itfDclIoDeclMap = new HashMap<>();
			if (isNotEmpty(decHeadList)) {
				decHeadMap = decHeadList.stream().collect(Collectors.groupingBy(DecHead::getApplyNumber));
			}
			if (isNotEmpty(storageInfoList)) {
				storageInfoMap = storageInfoList.stream().collect(Collectors.groupingBy(StorageInfo::getOrderNo));
			}
			if (isNotEmpty(itfDclIoDeclList)) {
				itfDclIoDeclMap = itfDclIoDeclList.stream().collect(Collectors.groupingBy(ItfDclIoDecl::getOrderProtocolNo));
			}
			for (OrderInfo o : pageList.getRecords()) {
				// 委托方
				if (isNotBlank(o.getBuyer())) {
					Commissioner commissioner = commissionerService.getById(o.getBuyer());
					if (isNotEmpty(commissioner)) {
						o.setBuyer(commissioner.getCommissionerFullName());
					} else {
						o.setBuyer(tenantName);
					}
				}
				// 境外付款方
				if (isNotBlank(o.getOverseasPayerInfoId())) {
					OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(o.getOverseasPayerInfoId());
					if (isNotEmpty(overseasPayerInfo)) {
						o.setOverseasPayerInfoId(overseasPayerInfo.getOverseasPayerName());
					} else {
						DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(o.getOverseasPayerInfoId());
						if (isNotEmpty(domesticSuppliersInfo)) {
							o.setOverseasPayerInfoId(domesticSuppliersInfo.getSuppliersFullName());
						} else {
							o.setOverseasPayerInfoId(o.getOverseasPayerInfoId());
						}
					}
				}
				// 2023/10/31 17:59@ZHANGCHAO 追加/变更/完善：有没有报关单？？
				List<DecHead> decHeads = decHeadMap.get(o.getId());
				if (isNotEmpty(decHeads)) {
					String decIds = decHeads.stream().map(DecHead::getId).collect(Collectors.joining(","));
					o.setRelDecNos(decIds);
				}
				// 2024/1/31 17:51@ZHANGCHAO 追加/变更/完善：关联出入库单
				List<StorageInfo> storageInfos = storageInfoMap.get(o.getOrderProtocolNo());
				if (isNotEmpty(storageInfos)) {
					String storageNos = storageInfos.stream().map(StorageInfo::getStorageNo).collect(Collectors.joining(","));
					o.setRelStorageNos(storageNos);
				}
				// 2024/4/25 下午5:01@ZHANGCHAO 追加/变更/完善：有没有商检单！
				List<ItfDclIoDecl> itfDclIoDecls = itfDclIoDeclMap.get(o.getOrderProtocolNo());
				if (isNotEmpty(itfDclIoDecls)) {
					String itfDclIoDeclIds = itfDclIoDecls.stream().map(ItfDclIoDecl::getId).collect(Collectors.joining(","));
					o.setRelCiqNos(itfDclIoDeclIds);
				}
				// 2024/4/25 下午2:00@ZHANGCHAO 追加/变更/完善：处理涉商检！！
				List<OrderProductInfo> orderProductInfos = orderProductInfoService.list(new QueryWrapper<OrderProductInfo>().lambda()
						.eq(OrderProductInfo::getOrderInfoId, o.getId()));
				o.setProductList(orderProductInfos);
				AtomicBoolean isSFJ = new AtomicBoolean(false);
				if (isNotEmpty(orderProductInfos)) {
					orderProductInfos.forEach(orderProductInfo -> {
						if (containsCharacter(orderProductInfo.getIaqcategory(), orderInfo.getIeFlag())) {
							isSFJ.set(true);
						}
					});
					o.setIsSFJ(isSFJ.get());
					//首项货名
					o.setFirstGoodsName(orderProductInfos.get(0).getChineseName());
				}
			}
		}
		return Result.OK(pageList);
	}

	/**
	 * 检查给定的字符串是否包含特定的一组字符中的任意一个字符。
	 *
	 * @param str 需要检查的字符串。
	 * @param ieFlag 进出口标志，"I"表示进口，"E"表示出口。
	 * @return 如果字符串中包含指定字符集中的任意一个字符，则返回true；
	 * 如果字符串为空、只包含空格或者不包含指定字符集中的任何字符，则返回false。
	 */
	public static boolean containsCharacter(String str, String ieFlag) {
		if (isBlank(str)) {
			return false;
		}
		String[] charactersToCheck;
		if (I.equals(ieFlag)) {
			// 进口时判断含A
			charactersToCheck = new String[]{"A"};
		} else if (E.equals(ieFlag)) {
			// 出口时判断含B
			charactersToCheck = new String[]{"B"};
		} else {
			charactersToCheck = new String[]{"B"};
		}
		for (char charToCheck : str.toCharArray()) {
			for (String character : charactersToCheck) {
				if (character.equals(String.valueOf(charToCheck))) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 分页列表查询
	 *
	 * @param orderMatchingInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="外汇收齐明细-分页列表查询", notes="外汇收齐明细-分页列表查询")
	@GetMapping(value = "/orderMatchingList")
	public Result<?> queryPageListOrderMatching(OrderMatchingInfoBiz orderMatchingInfo,
												@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												HttpServletRequest req) {
		orderMatchingInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
		orderMatchingInfo.setDelFlag(DEL_FLAG_0);
		Page<OrderMatchingInfoBiz> page = new Page<OrderMatchingInfoBiz>(pageNo,pageSize);
		IPage<OrderMatchingInfoBiz> pageList = matchingInfoService.queryOrderMatching(page, orderMatchingInfo);
		return Result.OK(pageList);
	}

	/**
	 * 一键导出excel
	 *
	 * @param
	 * @param
	 */
	@RequestMapping(value = "/exportXls")
	public void exportXls2(@RequestParam(name="fileNameList",required=true) List fileNameList,
                           @RequestParam(name="id",required=true) String id,
                           @RequestParam(name="enterpriseAccountId",defaultValue="") String enterpriseAccountId,
                           @RequestParam(name="suppliersAccountId",defaultValue="") String suppliersAccountId,
                           HttpServletResponse response) throws IOException {

		// 2024/10/25 16:24@ZHANGCHAO 追加/变更/完善：先清空目录下的文件！
		String zipPath = null;
		File savefile = null;
		try {
			zipPath = upLoadPath + "/downloadExcel";
			log.info(zipPath);
			savefile = new File(zipPath);
			if (!savefile.exists()) {
				savefile.mkdirs();
			}
			// 清空目录下文件
			FileUtil.del(zipPath);
		} catch (Exception e) {
			log.error("清空目录下文件失败：", e);
		}

		String fileXlsName = "";
		String fileName = "";
		for (int i = 0; i < fileNameList.size(); i++) {
			fileName = fileNameList.get(i).toString();
			HashMap<String, Object> map = setMap(fileName,id,enterpriseAccountId,suppliersAccountId);
			if("箱单".equals(fileName)){
				fileXlsName = "packingList";
				// 2024/10/21 15:19@ZHANGCHAO 追加/变更/完善：是否有单独维护的箱单模版！！
                try {
                    doesItHaveItsOwnTemplate(fileName, id, null);
                } catch (Exception e) {
					e.printStackTrace();
                    log.error("单独维护的箱单发票模版导出出现异常：" + e.getMessage());
                }
            }else if("发票".equals(fileName)){
				fileXlsName = "commercialInvoice";
				// 2024/10/21 15:19@ZHANGCHAO 追加/变更/完善：是否有单独维护的箱单模版！！
				try {
					doesItHaveItsOwnTemplate(fileName, id, null);
				} catch (Exception e) {
					e.printStackTrace();
					log.error("单独维护的箱单发票模版导出出现异常：" + e.getMessage());
				}
			}else if("外销合同".equals(fileName)){
				fileXlsName = "salesContract";
				// 2025/04/08 15:19zhengliansong 追加/变更/完善：是否有单独维护的外销合同模板！！
				try {
					doesItHaveItsOwnTemplate(fileName, id, null);
				} catch (Exception e) {
					e.printStackTrace();
					log.error("单独维护的外销合同模版导出出现异常：" + e.getMessage());
				}
			}else if("订舱委托书".equals(fileName)){
				fileXlsName = "orderShippingBooking";
			}else if("入货通知书".equals(fileName)){
				fileXlsName = "receiptNoticeInfo";
			}else if("报关单".equals(fileName)){
				fileXlsName = "customsDec";
			}else if("9710订单信息".equals(fileName)){
				fileXlsName = "9710";
			}else if("9810订仓单信息".equals(fileName)){
				fileXlsName = "9810";
			}else if("结算单".equals(fileName)){
				fileXlsName = "SummaryInfo";
			}else if("供货服务合同".equals(fileName)){
				fileXlsName = "purchaseContract";
			}else if("开票通知".equals(fileName)){
				fileXlsName = "invoicingNotice";
			}else if("送货单".equals(fileName)){
				fileXlsName = "deliveryNote";
			}
			new PrintUtil().listPrintXLS(map,fileXlsName,fileName,true,orderInfoMapper.selectById(id),response);
		}

//		String zipPath = upLoadPath + "/downloadExcel";
//		log.info(zipPath);
//		File savefile = new File(zipPath);
//		if (!savefile.exists()) {
//			savefile.mkdirs();
//		}

        try {
            // 遍历zipPath目录，检查并删除对应的箱单.xlsx文件
            File[] files = savefile.listFiles();
            for (File file : files) {
                if (file.getName().contains("箱单.docx")||file.getName().contains("箱单信息.xlsx")) {
                    File xlsxFile = new File(savefile, "箱单.xls");
                    if (xlsxFile.exists()) {
                        xlsxFile.delete();
                    }
                }
                if (file.getName().contains("发票.docx")||file.getName().contains("发票信息.xlsx")) {
                    File xlsxFile = new File(savefile, "发票.xls");
                    if (xlsxFile.exists()) {
                        xlsxFile.delete();
                    }
                }
				if (file.getName().contains("外销合同信息.xlsx")) {
					File xlsxFile = new File(savefile, "外销合同.xls");
					if (xlsxFile.exists()) {
						xlsxFile.delete();
					}
				}
            }
        } catch (Exception e) {
            log.error("遍历文件夹时发生错误：", e);
        }

        try {
			FolderToZipUtil.zip(zipPath,response);
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			File file2 = new File(upLoadPath + "/downloadExcel");
			IOCloseUtil.deleteAll(file2);
		}

	}

	/**
	 * 企业是否有自己的箱单发票模版
	 *
	 * @param
	 * @return void
	 * <AUTHOR>
	 * @date 2024/10/22 15:22
	 */
	private void doesItHaveItsOwnTemplate(String fileName, String id, String type) throws FileNotFoundException {
		String path = !"PDF".equals(type) ? "/downloadExcel" : "/downloadPdf";
		if ("箱单".equals(fileName)) {
			List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.list(new QueryWrapper<EnterpriseInfo>().lambda()
					.eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
			if (isNotEmpty(enterpriseInfos)) {
				if (isNotBlank(enterpriseInfos.get(0).getPackListTempl())) {
					//20250304追加 如果企业上传的模板为xlsx类型，需要导出为excel
					if (enterpriseInfos.get(0).getPackListTempl().substring(
							enterpriseInfos.get(0).getPackListTempl().lastIndexOf(".")
                    ).equals(".xlsx")) {
						//根据企业配置的模板导出excel
						exportCustomizeXlsxTemplate(fileName, id, type,enterpriseInfos.get(0),path);
					}else {
//					String filePath = MinioUtil.downloadFile(enterpriseInfos.get(0).getPackListTempl(), null);
                    // 如果包含trade-service-platform则表示是minio文件，否则是阿里云OSS文件
                    InputStream filePath;
                    if (enterpriseInfos.get(0).getPackListTempl().contains("trade-service-platform")) {
                        filePath = MinioUtil.getMinioFile(enterpriseInfos.get(0).getPackListTempl().split("trade-service-platform")[1]);
                    } else {
                        filePath = OssBootUtil.getOssFileByUrl(enterpriseInfos.get(0).getPackListTempl());
                    }
					log.info("箱单下载的文件路径：{}", filePath);
					// HackLoopTableRenderPolicy插件，根据集合数据循环表格行。
//					HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
					Configure config = Configure.builder()
//							.bind("orderInfo", policy)
							.bind("productList", new DefineMethodPolicy()).build();
					OrderInfo orderInfo = setDocData(id);
					XWPFTemplate template = XWPFTemplate.compile(filePath, config).render(
							new HashMap<String, Object>() {{
								put("orderInfo", orderInfo);
								put("productList", orderInfo.getProductList());
							}});
					//一键下载
					File savefile = new File(upLoadPath+path);
					if (!savefile.exists()) {
						savefile.mkdirs();
					}
					FileOutputStream FileFos;
					FileFos = new FileOutputStream(upLoadPath+path + "/" + fileName + ".docx");
					try {
						template.write(FileFos);
					} catch (IOException e) {
						e.printStackTrace();
					} finally {
						try {
							if (FileFos != null) {
								FileFos.close();
							}
//							FileUtil.del(filePath);
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
					}
				}
			}
		} else if ("发票".equals(fileName)) {
			List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.list(new QueryWrapper<EnterpriseInfo>().lambda()
					.eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
			if (isNotEmpty(enterpriseInfos)) {
				if (isNotBlank(enterpriseInfos.get(0).getInvoiceTempl())) {
					//20250304追加 如果企业上传的模板为xlsx类型，需要导出为excel
					if (enterpriseInfos.get(0).getInvoiceTempl().substring(
							enterpriseInfos.get(0).getInvoiceTempl().lastIndexOf(".")
					).equals(".xlsx")) {
						//根据企业配置的模板导出excel
						exportCustomizeXlsxTemplate(fileName, id, type, enterpriseInfos.get(0), path);
					}else {
//					String filePath = MinioUtil.downloadFile(enterpriseInfos.get(0).getInvoiceTempl(), null);
                    InputStream filePath;
                    if (enterpriseInfos.get(0).getPackListTempl().contains("trade-service-platform")) {
                        filePath = MinioUtil.getMinioFile(enterpriseInfos.get(0).getPackListTempl().split("trade-service-platform")[1]);
                    } else {
                        filePath = OssBootUtil.getOssFileByUrl(enterpriseInfos.get(0).getPackListTempl());
                    }
					log.info("发票下载的文件路径：{}", filePath);
					// HackLoopTableRenderPolicy插件，根据集合数据循环表格行。
					HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
					Configure config = Configure.builder()
							.bind("orderInfo", policy).bind("productList", policy).build();
					OrderInfo orderInfo = setDocData(id);
					XWPFTemplate template = XWPFTemplate.compile(filePath, config).render(
							new HashMap<String, Object>() {{
								put("orderInfo", orderInfo);
								put("productList", orderInfo.getProductList());
							}});
					//一键下载
					File savefile = new File(upLoadPath + path);
					if (!savefile.exists()) {
						savefile.mkdirs();
					}
					FileOutputStream FileFos;
					FileFos = new FileOutputStream(upLoadPath + path + "/" + fileName + ".docx");
					try {
						template.write(FileFos);
					} catch (IOException e) {
						e.printStackTrace();
					} finally {
						try {
							if (FileFos != null) {
								FileFos.close();
							}
//							FileUtil.del(filePath);
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
				}
			}
		}else if ("外销合同".equals(fileName)) {
			List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.list(new QueryWrapper<EnterpriseInfo>().lambda()
					.eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
			if (isNotEmpty(enterpriseInfos)) {
				if (isNotBlank(enterpriseInfos.get(0).getSalescontractTempl())) {
					//20250304追加 如果企业上传的模板为xlsx类型，需要导出为excel
					if (enterpriseInfos.get(0).getSalescontractTempl().substring(
							enterpriseInfos.get(0).getSalescontractTempl().lastIndexOf(".")
					).equals(".xlsx")) {
						//根据企业配置的模板导出excel
						exportCustomizeXlsxTemplate(fileName, id, type, enterpriseInfos.get(0), path);
					}
				}
			}
		}

	}

	private void exportCustomizeXlsxTemplate(String fileName, String id, String type,
											 EnterpriseInfo enterpriseInfo,String path){
		TemplateExportParams params = new TemplateExportParams();
		Map<String, Object> dataMap = new HashMap<>(16);
		if ("箱单".equals(fileName)) {
//			InputStream filePath = MinioUtil.getMinioFile(enterpriseInfo.getPackListTempl().split("trade-service-platform")[1]);
            InputStream filePath;
            if (enterpriseInfo.getPackListTempl().contains("trade-service-platform")) {
                filePath = MinioUtil.getMinioFile(enterpriseInfo.getPackListTempl().split("trade-service-platform")[1]);
            } else {
                filePath = OssBootUtil.getOssFileByUrl(enterpriseInfo.getPackListTempl());
            }
			log.info("箱单下载的文件路径：{}", filePath);
            try {
				//导出模板转workbook
                Workbook templateWorkbook = WorkbookFactory.create(filePath);
				params.setTemplateWb(templateWorkbook);
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())) {
					setExportCustomizeXlsxTemplateDataByYS(id, dataMap, fileName);
				}else {
					setExportCustomizeXlsxTemplateData(id,dataMap,fileName,enterpriseInfo);
				}
				Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
				//益尚的特殊处理
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					//获取sheet
					Sheet sheet = workbook.getSheetAt(0);
					List<Map<String, Object>> list = (List<Map<String, Object>>)dataMap.get("listMapList");
					if(!list.isEmpty()){
						//手动合并前需要清除本来已合并的单元格
						removeMergedRegion(sheet,new CellRangeAddress(12,14,0,1));
						//手动合并唛头单元格
						CellRangeAddress cellRangeAddress = new CellRangeAddress(12,13+list.size(),0,1);
						sheet.addMergedRegion(cellRangeAddress);
					}
					//手动添加图片
					//获取图片
					// 读取图片文件
//					InputStream imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    InputStream imageStream;
                    if (enterpriseInfo.getCompanyStamp().contains("trade-service-platform")) {
                        imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    } else {
                        imageStream = OssBootUtil.getOssFileByUrl(enterpriseInfo.getCompanyStamp());
                    }
					byte[] imageBytes = IOUtils.toByteArray(imageStream);
					imageStream.close();
					// 添加图片到工作簿
					int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
					// 创建绘图对象
					CreationHelper helper = workbook.getCreationHelper();
					Drawing<?> drawing = sheet.createDrawingPatriarch();

					// 创建锚点并设置图片位置
					ClientAnchor anchor = helper.createClientAnchor();
					anchor.setCol1(6); // 起始列 (0-based)
					anchor.setRow1(12+list.size()+3); // 起始行 (0-based)
					anchor.setCol2(8); // 结束列
					anchor.setRow2(12+list.size()+7); // 结束行

					// 创建图片并添加到工作表
					Picture picture = drawing.createPicture(anchor, pictureIdx);

					//重新定义分页符
					// 在指定行后添加水平分页符
					sheet.setRowBreak(10+list.size()+9); //
					// 移除所有手动分页符
					sheet.removeRowBreak(19);

					// 自动调整列宽和行高以适应图片
//					sheet.autoSizeColumn(1);
//					sheet.autoSizeColumn(2);
//					sheet.getRow(1).setHeightInPoints((float)picture.getImageDimension().getHeight());

					//手动设置边框
					CellStyle cellStyle = workbook.createCellStyle();
					// 设置边框样式为实线
					cellStyle.setBorderLeft(BorderStyle.THIN);
					CellStyle cellStyle2 = workbook.createCellStyle();
					cellStyle2.setBorderRight(BorderStyle.THIN);
					CellStyle cellStyle3 = workbook.createCellStyle();
					cellStyle3.setBorderBottom(BorderStyle.THIN);
					cellStyle3.setBorderLeft(BorderStyle.THIN);
					// 创建一个CellStyle对象并设置居中对齐
					CellStyle cellStyle4 = workbook.createCellStyle();
					cellStyle4.setAlignment(HorizontalAlignment.CENTER); // 水平居中
					cellStyle4.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
					cellStyle4.setBorderLeft(BorderStyle.THIN);
					CellStyle cellStyle5 = workbook.createCellStyle();
					cellStyle5.setBorderBottom(BorderStyle.THIN);
					cellStyle5.setBorderRight(BorderStyle.THIN);
					for(int i=0;i<sheet.getLastRowNum();i++){
						for(int j=0; j < sheet.getRow(i).getLastCellNum();j++){
							if(i>=12&&i<=(13+list.size())){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle2);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle2);
									}
								}
							}
							if(i==13+list.size()){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle3);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle3);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle5);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle5);
									}
								}
							}
							if(i==12&&j==0){
								if(null!=sheet.getRow(i).getCell(j)){
									sheet.getRow(i).getCell(j).setCellStyle(cellStyle4);
								}
							}
						}
					}
				}
				File savefile = new File(upLoadPath+path);
				if (!savefile.exists()) {
					savefile.mkdirs();
				}
				FileOutputStream FileFos;
				FileFos = new FileOutputStream(upLoadPath+path + "/" + fileName + "信息.xlsx");
				try {
					workbook.write(FileFos);
				} catch (IOException e) {
					e.printStackTrace();
				} finally {
					try {
						if (FileFos != null) {
							FileFos.close();
						}
//                    FileUtil.del(filePath);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			} catch (IOException e) {
                throw new RuntimeException(e);
            }
        }else if ("发票".equals(fileName))
		{
//			InputStream filePath = MinioUtil.getMinioFile(enterpriseInfo.getInvoiceTempl().split("trade-service-platform")[1]);
            InputStream filePath;
            if (enterpriseInfo.getInvoiceTempl().contains("trade-service-platform")) {
                filePath = MinioUtil.getMinioFile(enterpriseInfo.getInvoiceTempl().split("trade-service-platform")[1]);
            } else {
                filePath = OssBootUtil.getOssFileByUrl(enterpriseInfo.getInvoiceTempl());
            }
			log.info("发票下载的文件路径：{}", filePath);
			try {
				//导出模板转workbook
				Workbook templateWorkbook = WorkbookFactory.create(filePath);
				params.setTemplateWb(templateWorkbook);
				//益尚处理
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					setExportCustomizeXlsxTemplateDataByYS(id,dataMap,fileName);
				}else {
					setExportCustomizeXlsxTemplateData(id,dataMap,fileName,enterpriseInfo);
				}
				Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
				//益尚的特殊处理
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					//获取sheet
					Sheet sheet = workbook.getSheetAt(0);
					List<Map<String, Object>> list = (List<Map<String, Object>>)dataMap.get("listMapList");
					if(!list.isEmpty()){
						for(int i=0;i<list.size();i++){
							Row row = sheet.getRow(13+(i*2));
							Cell cell = row.getCell(2);
							Cell cell2 = row.getCell(4);
							cell.setCellValue(cell2.getStringCellValue());
							cell2.setCellValue("");
						}
						//手动合并前需要清除本来已合并的单元格
						removeMergedRegion(sheet,new CellRangeAddress(12,14,0,1));
						//手动合并唛头单元格
						CellRangeAddress cellRangeAddress = new CellRangeAddress(12,12+list.size()*2,0,1);
						sheet.addMergedRegion(cellRangeAddress);
					}
					//手动添加图片
					//获取图片
					// 读取图片文件
//					InputStream imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    InputStream imageStream;
                    if (enterpriseInfo.getCompanyStamp().contains("trade-service-platform")) {
                        imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    } else {
                        imageStream = OssBootUtil.getOssFileByUrl(enterpriseInfo.getCompanyStamp());
                    }
					byte[] imageBytes = IOUtils.toByteArray(imageStream);
					imageStream.close();
					// 添加图片到工作簿
					int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
					// 创建绘图对象
					CreationHelper helper = workbook.getCreationHelper();
					Drawing<?> drawing = sheet.createDrawingPatriarch();

					// 创建锚点并设置图片位置
					ClientAnchor anchor = helper.createClientAnchor();
					anchor.setCol1(5); // 起始列 (0-based)
					anchor.setRow1(12+list.size()*2+2); // 起始行 (0-based)
					anchor.setCol2(6); // 结束列
					anchor.setRow2(12+list.size()*2+6); // 结束行

					// 创建图片并添加到工作表
					Picture picture = drawing.createPicture(anchor, pictureIdx);

					//重新定义分页符
					// 在指定行后添加水平分页符
					sheet.setRowBreak(12+list.size()*2+6); //
					// 移除所有手动分页符
					sheet.removeRowBreak(19);

					// 自动调整列宽和行高以适应图片
//					sheet.autoSizeColumn(1);
//					sheet.autoSizeColumn(2);
//					sheet.getRow(1).setHeightInPoints((float)picture.getImageDimension().getHeight());
					//手动设置边框
					CellStyle cellStyle = workbook.createCellStyle();
					// 设置边框样式为实线
					cellStyle.setBorderLeft(BorderStyle.THIN);
					CellStyle cellStyle2 = workbook.createCellStyle();
					cellStyle2.setBorderRight(BorderStyle.THIN);
					CellStyle cellStyle3 = workbook.createCellStyle();
					cellStyle3.setBorderBottom(BorderStyle.THIN);
					cellStyle3.setBorderLeft(BorderStyle.THIN);
					// 创建一个CellStyle对象并设置居中对齐
					CellStyle cellStyle4 = workbook.createCellStyle();
					cellStyle4.setAlignment(HorizontalAlignment.CENTER); // 水平居中
					cellStyle4.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
					cellStyle4.setBorderLeft(BorderStyle.THIN);
					CellStyle cellStyle5 = workbook.createCellStyle();
					cellStyle5.setBorderBottom(BorderStyle.THIN);
					cellStyle5.setBorderRight(BorderStyle.THIN);
					for(int i=0;i<sheet.getLastRowNum();i++){
						for(int j=0; j < sheet.getRow(i).getLastCellNum();j++){
							if(i>=12&&i<=12+list.size()*2){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle2);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle2);
									}
								}
							}
							if(i==12+list.size()*2){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle3);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle3);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle5);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle5);
									}
								}
							}
							if(i==12&&j==0){
								if(null!=sheet.getRow(i).getCell(j)){
									sheet.getRow(i).getCell(j).setCellStyle(cellStyle4);
								}
							}
						}
					}

				}
				File savefile = new File(upLoadPath+path);
				if (!savefile.exists()) {
					savefile.mkdirs();
				}
				FileOutputStream FileFos;
				FileFos = new FileOutputStream(upLoadPath+path + "/" + fileName + "信息.xlsx");
				try {
					workbook.write(FileFos);
				} catch (IOException e) {
					e.printStackTrace();
				} finally {
					try {
						if (FileFos != null) {
							FileFos.close();
						}
//                    FileUtil.del(filePath);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}else if ("外销合同".equals(fileName))
		{
//			InputStream filePath = MinioUtil.getMinioFile(enterpriseInfo.getSalescontractTempl().split("trade-service-platform")[1]);
            InputStream filePath;
            if (enterpriseInfo.getSalescontractTempl().contains("trade-service-platform")) {
                filePath = MinioUtil.getMinioFile(enterpriseInfo.getSalescontractTempl().split("trade-service-platform")[1]);
            } else {
                filePath = OssBootUtil.getOssFileByUrl(enterpriseInfo.getSalescontractTempl());
            }
			log.info("外销合同下载的文件路径：{}", filePath);
			try {
				//导出模板转workbook
				Workbook templateWorkbook = WorkbookFactory.create(filePath);
				params.setTemplateWb(templateWorkbook);
				//益尚处理
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					setExportCustomizeXlsxTemplateDataByYS(id,dataMap,fileName);
				}
//				else {
//					setExportCustomizeXlsxTemplateData(id,dataMap,fileName,enterpriseInfo);
//				}
				Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
				//益尚的特殊处理
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					//获取sheet
					Sheet sheet = workbook.getSheetAt(0);
					List<Map<String, Object>> list = (List<Map<String, Object>>)dataMap.get("listMapList");
					if(!list.isEmpty()){
						//手动合并前需要清除本来已合并的单元格
						removeMergedRegion(sheet,new CellRangeAddress(10,15,0,1));
						//手动合并唛头单元格
						CellRangeAddress cellRangeAddress = new CellRangeAddress(10,10+list.size()+4,0,1);
						sheet.addMergedRegion(cellRangeAddress);
					}
					//手动添加图片
					//获取图片
					// 读取图片文件
//					InputStream imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    InputStream imageStream;
                    if (enterpriseInfo.getCompanyStamp().contains("trade-service-platform")) {
                        imageStream = MinioUtil.getMinioFile(enterpriseInfo.getCompanyStamp().split("trade-service-platform")[1]);
                    } else {
                        imageStream = OssBootUtil.getOssFileByUrl(enterpriseInfo.getCompanyStamp());
                    }
					byte[] imageBytes = IOUtils.toByteArray(imageStream);
					imageStream.close();
					// 添加图片到工作簿
					int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
					// 创建绘图对象
					CreationHelper helper = workbook.getCreationHelper();
					Drawing<?> drawing = sheet.createDrawingPatriarch();

					// 创建锚点并设置图片位置
					ClientAnchor anchor = helper.createClientAnchor();
					anchor.setCol1(1); // 起始列 (0-based)
					anchor.setRow1(16+list.size()); // 起始行 (0-based)
					anchor.setCol2(3); // 结束列
					anchor.setRow2(20+list.size()); // 结束行
					anchor.setDx1(Units.EMU_PER_PIXEL * 10); // 起始单元格内的X偏移(像素)
					anchor.setDx2(Units.EMU_PER_PIXEL * 20); // 结束单元格内的X偏移(像素)
//					anchor.setDy1(Units.EMU_PER_PIXEL * 10); // 起始单元格内的y偏移(像素)
					anchor.setDy2(Units.EMU_PER_PIXEL * 20); // 结束单元格内的y偏移(像素)

					// 创建图片并添加到工作表
					Picture picture = drawing.createPicture(anchor, pictureIdx);
					 picture.resize(0.8); // 缩放

					//设置买方的公章
					//     订单信息
					OrderInfo orderInfo = orderInfoService.getById(id);
					OverseasPayerInfo overseasPayerInfo = new OverseasPayerInfo();
					//境外客户信息
					if(isNotBlank(orderInfo.getOverseasPayerInfoId())){
						overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
						if(isNotBlank(overseasPayerInfo.getOfficialSeal())){
							// 读取图片文件
//							InputStream imageStream2 = MinioUtil.getMinioFile(overseasPayerInfo.getOfficialSeal().split("trade-service-platform")[1]);
                            InputStream imageStream2;
                            if (overseasPayerInfo.getOfficialSeal().contains("trade-service-platform")) {
                                imageStream2 = MinioUtil.getMinioFile(overseasPayerInfo.getOfficialSeal().split("trade-service-platform")[1]);
                            } else {
                                imageStream2 = OssBootUtil.getOssFileByUrl(overseasPayerInfo.getOfficialSeal());
                            }
							byte[] imageBytes2 = IOUtils.toByteArray(imageStream2);
							imageStream2.close();
							// 添加图片到工作簿
							int pictureIdx2 = workbook.addPicture(imageBytes2, Workbook.PICTURE_TYPE_PNG);
							// 创建绘图对象
							CreationHelper helper2 = workbook.getCreationHelper();
							Drawing<?> drawing2 = sheet.createDrawingPatriarch();

							// 创建锚点并设置图片位置
							ClientAnchor anchor2 = helper2.createClientAnchor();
							anchor2.setCol1(3); // 起始列 (0-based)
							anchor2.setRow1(15+list.size()); // 起始行 (0-based)
							anchor2.setCol2(5); // 结束列
							anchor2.setRow2(20+list.size()); // 结束行
							anchor2.setDx1(Units.EMU_PER_PIXEL * 38); // 起始单元格内的X偏移(像素)
							anchor2.setDx2(Units.EMU_PER_PIXEL * 78); // 结束单元格内的X偏移(像素)

							// 创建图片并添加到工作表
							Picture picture2 = drawing2.createPicture(anchor2, pictureIdx2);
							picture2.resize(1.2); // 放大
						}
					}


					//重新定义分页符
					// 在指定行后添加水平分页符
					sheet.setRowBreak(12+list.size()*2+6); //
					// 移除所有手动分页符
					sheet.removeRowBreak(20);

					// 自动调整列宽和行高以适应图片
//					sheet.autoSizeColumn(1);
//					sheet.autoSizeColumn(2);
//					sheet.getRow(1).setHeightInPoints((float)picture.getImageDimension().getHeight());
					//手动设置边框
					CellStyle cellStyle = workbook.createCellStyle();
					// 设置边框样式为实线
					cellStyle.setBorderLeft(BorderStyle.THIN);
					CellStyle cellStyle2 = workbook.createCellStyle();
					cellStyle2.setBorderRight(BorderStyle.THIN);
					CellStyle cellStyle3 = workbook.createCellStyle();
					cellStyle3.setBorderBottom(BorderStyle.THIN);
					cellStyle3.setBorderLeft(BorderStyle.THIN);
					// 创建一个CellStyle对象并设置居中对齐
					CellStyle cellStyle4 = workbook.createCellStyle();
					cellStyle4.setAlignment(HorizontalAlignment.CENTER); // 水平居中
					cellStyle4.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
					cellStyle4.setBorderLeft(BorderStyle.THIN);
					cellStyle4.setBorderTop(BorderStyle.THIN);
					CellStyle cellStyle6 = workbook.createCellStyle();
					cellStyle6.setBorderTop(BorderStyle.THIN);
					cellStyle6.setBorderRight(BorderStyle.THIN);
					CellStyle cellStyle5 = workbook.createCellStyle();
					cellStyle5.setBorderBottom(BorderStyle.THIN);
					cellStyle5.setBorderRight(BorderStyle.THIN);
					for(int i=0;i<sheet.getLastRowNum();i++){
						for(int j=0; j < sheet.getRow(i).getLastCellNum();j++){
							if(i>=10&&i<=14+list.size()){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle2);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle2);
									}
								}
							}
							if(i==(14+list.size())){
								if(j==0){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle3);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle3);
									}
								}
								if(j==1){
									if(null!=sheet.getRow(i).getCell(j)){
										sheet.getRow(i).getCell(j).setCellStyle(cellStyle5);
									}else {
										sheet.getRow(i).createCell(j).setCellStyle(cellStyle5);
									}
								}
							}
							if(i==10&&j==0){
								if(null!=sheet.getRow(i).getCell(j)){
									sheet.getRow(i).getCell(j).setCellStyle(cellStyle4);
								}
							}
							if(i==10&&j==1){
								if(null!=sheet.getRow(i).getCell(j)){
									sheet.getRow(i).getCell(j).setCellStyle(cellStyle6);
								}
							}
						}
					}

				}
				File savefile = new File(upLoadPath+path);
				if (!savefile.exists()) {
					savefile.mkdirs();
				}
				FileOutputStream FileFos;
				FileFos = new FileOutputStream(upLoadPath+path + "/" + fileName + "信息.xlsx");
				try {
					workbook.write(FileFos);
				} catch (IOException e) {
					e.printStackTrace();
				} finally {
					try {
						if (FileFos != null) {
							FileFos.close();
						}
//                    FileUtil.del(filePath);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
	}
	private void setExportCustomizeXlsxTemplateData(String id,Map<String, Object> dataMap,
													String fileName,EnterpriseInfo enterpriseInfo){
//     订单信息
		OrderInfo orderInfo = orderInfoService.getById(id);
//     订单商品信息
		LambdaQueryWrapper<OrderProductInfo> queryWrapper4 = new LambdaQueryWrapper<>();
		queryWrapper4.eq(OrderProductInfo::getOrderInfoId,id);
		//港口信息
		List<ErpCityports> erpCityports = erpCityportsService.list();
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(queryWrapper4);
		OverseasPayerInfo overseasPayerInfo = new OverseasPayerInfo();
		//		订单运输信息
		LambdaQueryWrapper<OrderTransportationInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrderTransportationInfo::getOrderInfoId,id);
		queryWrapper.eq(OrderTransportationInfo::getDelFlag, DEL_FLAG_0);
		OrderTransportationInfo orderTransportationInfo = orderTransportationInfoService.getOne(queryWrapper);
		//境外客户信息
		if(isNotBlank(orderInfo.getOverseasPayerInfoId())){
			overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
		}
		//赋值数据
		dataMap.put("overseasPayerInfoName",orderInfo.getOverseasPayerInfoName());//境外客户名称
		dataMap.put("overseasPayerAddressDetail",isNotEmpty(overseasPayerInfo)?
				overseasPayerInfo.getOverseasPayerAddressDetail():"");//境外客户地址
		dataMap.put("compayTelphone",isNotEmpty(overseasPayerInfo)?
				overseasPayerInfo.getCompayTelphone():"");//境外客户电话
		if(isNotBlank(orderInfo.getDeparturePort())){
			List<ErpCityports> erpCityportsOne = erpCityports.stream().filter(i->i.getCityportCode()
					.equals(orderInfo.getDeparturePort())).collect(Collectors.toList());
			dataMap.put("departurePort",erpCityportsOne.isEmpty()?"":erpCityportsOne.get(0).getEnname());//出发港
		}
		if(isNotBlank(orderInfo.getPortDestination())){
			List<ErpCityports> erpCityportsOne = erpCityports.stream().filter(i->i.getCityportCode()
					.equals(orderInfo.getPortDestination())).collect(Collectors.toList());
			dataMap.put("portDestination",erpCityportsOne.isEmpty()?"":erpCityportsOne.get(0).getEnname());//目的港
		}
		dataMap.put("invoiceNo",orderInfo.getInvoiceNo());//发票号
		if(isNotEmpty(orderInfo.getInvoiceDate())){
			LocalDate currentDate  = orderInfo.getInvoiceDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			// 定义日期格式（简写月 日, 年）
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM d yyyy", Locale.US);
			// 格式化日期
			String formattedDate = currentDate .format(formatter);
			dataMap.put("invoiceDate",formattedDate);//发票日期
		}else {
			dataMap.put("invoiceDate","");//发票日期
		}

		dataMap.put("relOrderNo",orderInfo.getRelOrderNo());//关联订单号
		dataMap.put("payment",orderInfo.getPayment());//付款方式
		dataMap.put("transportName",orderInfo.getTransportName());//运输工具名称
		dataMap.put("shippingMark",isEmpty(orderTransportationInfo)?"N/M":
				isBlank(orderTransportationInfo.getShippingMark())?"N/M":orderTransportationInfo.getShippingMark());//唛头
        //汇总总数量 汇总展示
		BigDecimal shipmentQuantitySum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentQuantity)
				.filter(ObjectUtil::isNotEmpty)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		dataMap.put("shipmentQuantityTotal",shipmentQuantitySum.stripTrailingZeros().toPlainString());//汇总总数量
		//汇总净重 汇总展示
		BigDecimal netWeightSum=orderProductInfoList.stream().map(OrderProductInfo::getNetWeight)
				.filter(ObjectUtil::isNotEmpty)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		dataMap.put("netWeightTotal",netWeightSum.stripTrailingZeros().toPlainString());//汇总净重
		if("箱单".equals(fileName)){
			//箱单计算导出的箱数
			String englishAmount = "PACKED IN "+
					NumberToWords.convert(shipmentQuantitySum.stripTrailingZeros().intValue())
					+" CARTONS ONLY";
			dataMap.put("englishAmount",englishAmount.toUpperCase());//英文大写描述金额/数量
			//表体数据
			List<Map<String, Object>> listMapList = new ArrayList<>();
			for(OrderProductInfo orderProductInfo:orderProductInfoList){
				Map<String, Object> lm = new HashMap<>();
				lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
				lm.put("packKind",isNotBlank(orderProductInfo.getPackKind())?orderProductInfo.getPackKind():"");//包装规格
				lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
						orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
				lm.put("netWeight",null!=orderProductInfo.getNetWeight()?
						orderProductInfo.getNetWeight().stripTrailingZeros():"");//净重
				lm.put("grossWeight",null!=orderProductInfo.getGrossWeight()?
						orderProductInfo.getGrossWeight().stripTrailingZeros():"");//毛重
				lm.put("bdls","");
				listMapList.add(lm);
			}
			//汇总毛重 汇总展示
			BigDecimal grossWeightSum=orderProductInfoList.stream().map(OrderProductInfo::getGrossWeight)
					.filter(ObjectUtil::isNotEmpty)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("grossWeightTotal",grossWeightSum.stripTrailingZeros().toPlainString());//汇总毛重
			//收汇方式
			if(isNotEmpty(orderInfo.getExchangeCollectionType())){
				List<DictModelVO> exchangeCollectionTypeDict = decListMapper.getDictItemByCode("exchange_collection_type");
				List<DictModelVO> dictModelVOS = exchangeCollectionTypeDict.stream().filter(i->i.getValue()
						.equals(orderInfo.getExchangeCollectionType().toString())).collect(Collectors.toList());
				dataMap.put("payment",dictModelVOS.isEmpty()?"":
						dictModelVOS.get(0).getText().replaceAll("[\\u4e00-\\u9fa5]", ""));//收汇方式
			}
			List<DictModelVO> goodsArrtDict = decListMapper.getDictItemByCode("exchange_collection_type");


			dataMap.put("listMapList", listMapList); // 表体

		}
		if("发票".equals(fileName)){
			//币制信息转换用 数字要转为字母
			List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list();
			//发票计算总货值 加上币制信息
			BigDecimal totalAmount=orderProductInfoList.stream().map(OrderProductInfo::getShipmentGoodsValue)
					.filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
			String englishAmount = "";

			if(!orderProductInfoList.isEmpty()&&isNotBlank(orderProductInfoList.get(0).getCustomsDeclarationCurrency())){
				List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getCode()
						.equals(orderProductInfoList.get(0).getCustomsDeclarationCurrency())||
						i.getCurrency().equals(orderProductInfoList.get(0).getCustomsDeclarationCurrency())).
						collect(Collectors.toList());


				englishAmount = "SAY "+NumberToWords.getCurrencyName(erpCurrenciesList1.get(0).getCurrency())+" "
						+NumberToWords.convert(totalAmount.stripTrailingZeros().intValue())+" ONLY";
				dataMap.put("englishAmount",englishAmount.toUpperCase());//英文大写描述金额/数量
			}else {
				englishAmount = "SAY "+" "
						+NumberToWords.convert(totalAmount.stripTrailingZeros().intValue())+" ONLY";
				dataMap.put("englishAmount",englishAmount.toUpperCase());//英文大写描述金额/数量
			}
			//表体数据
			List<Map<String, Object>> listMapList = new ArrayList<>();
			for(OrderProductInfo orderProductInfo:orderProductInfoList){
				//益尚的表体数据，easypoi循环需要字段统一，否则报错
				if("益尚国际贸易(山东)有限公司".equals(enterpriseInfo.getEnterpriseFullName())){
					Map<String, Object> lm = new HashMap<>();
					lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
					lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
							orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
					//单价
					lm.put("shipmentUnitPrice",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentUnitPrice()+"/CTN"):(orderProductInfo.getShipmentUnitPrice()+"/CTN"));
					//总价
					lm.put("shipmentGoodsValue",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentGoodsValue()):(orderProductInfo.getShipmentUnitPrice()));
					lm.put("hsmodel",isNotBlank(orderProductInfo.getHsmodel())?orderProductInfo.getHsmodel():"");//申报要素
					listMapList.add(lm);
				}else {
					Map<String, Object> lm = new HashMap<>();
					lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
					lm.put("packKind",isNotBlank(orderProductInfo.getPackKind())?orderProductInfo.getPackKind():"");//包装规格
					lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
							orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
					lm.put("netWeight",null!=orderProductInfo.getNetWeight()?
							orderProductInfo.getNetWeight().stripTrailingZeros():"");//净重
					//币制数字转字母
					if(isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())){
						List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getCode()
								.equals(orderProductInfo.getCustomsDeclarationCurrency())||
								i.getCurrency().equals(orderProductInfo.getCustomsDeclarationCurrency())).collect(Collectors.toList());
						orderProductInfo.setCustomsDeclarationCurrency(erpCurrenciesList1.isEmpty()?"":
								erpCurrenciesList1.get(0).getCurrency());
					}
					//单价
					lm.put("shipmentUnitPrice",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentUnitPrice()+"/CTN"):(orderProductInfo.getShipmentUnitPrice()+"/CTN"));
					//总价
					lm.put("shipmentGoodsValue",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentGoodsValue()):(orderProductInfo.getShipmentUnitPrice()));
					lm.put("bdls","");
					listMapList.add(lm);
				}
			}
			//汇总总货值 汇总展示
			BigDecimal totalAmountSum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentGoodsValue)
					.filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("shipmentGoodsValueTotal",isNotBlank(orderProductInfoList.get(0).getCustomsDeclarationCurrency())?
					(NumberToWords.getCurrencySymbol(orderProductInfoList.get(0).getCustomsDeclarationCurrency())+
							totalAmountSum.stripTrailingZeros().toPlainString())
					:(totalAmountSum.stripTrailingZeros().toPlainString()));//汇总总货值
			//运输方式
			dataMap.put("shippingType",isBlank(orderInfo.getShippingType())?""
					:orderInfo.getShippingType());//运输方式
			dataMap.put("listMapList", listMapList); // 表体
		}
	}

	/**
	 * 益尚的发票箱单处理
	 */
	private void setExportCustomizeXlsxTemplateDataByYS(String id,Map<String, Object> dataMap,
													String fileName){
//     订单信息
		OrderInfo orderInfo = orderInfoService.getById(id);
//     订单商品信息
		LambdaQueryWrapper<OrderProductInfo> queryWrapper4 = new LambdaQueryWrapper<>();
		queryWrapper4.eq(OrderProductInfo::getOrderInfoId,id);
		//港口信息
		List<ErpCityports> erpCityports = erpCityportsService.list();
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(queryWrapper4);
		OverseasPayerInfo overseasPayerInfo = new OverseasPayerInfo();
		//		订单运输信息
		LambdaQueryWrapper<OrderTransportationInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrderTransportationInfo::getOrderInfoId,id);
		queryWrapper.eq(OrderTransportationInfo::getDelFlag, DEL_FLAG_0);
		OrderTransportationInfo orderTransportationInfo = orderTransportationInfoService.getOne(queryWrapper);
		//境外客户信息
		if(isNotBlank(orderInfo.getOverseasPayerInfoId())){
			overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
		}
		//赋值数据
		dataMap.put("overseasPayerInfoName",orderInfo.getOverseasPayerInfoName());//境外客户名称
		dataMap.put("overseasPayerAddressDetail",isNotEmpty(overseasPayerInfo)?
				overseasPayerInfo.getOverseasPayerAddressDetail():"");//境外客户地址
		if(isNotBlank(orderInfo.getDeparturePort())){
			List<ErpCityports> erpCityportsOne = erpCityports.stream().filter(i->i.getCityportCode()
					.equals(orderInfo.getDeparturePort())).collect(Collectors.toList());
			dataMap.put("departurePort",erpCityportsOne.isEmpty()?"":erpCityportsOne.get(0).getEnname());//出发港
		}
		if(isNotBlank(orderInfo.getPortDestination())){
			List<ErpCityports> erpCityportsOne = erpCityports.stream().filter(i->i.getCityportCode()
					.equals(orderInfo.getPortDestination())).collect(Collectors.toList());
			dataMap.put("portDestination",erpCityportsOne.isEmpty()?"":erpCityportsOne.get(0).getEnname());//目的港
		}
		dataMap.put("invoiceNo",orderInfo.getInvoiceNo());//发票号
		if(isNotEmpty(orderInfo.getInvoiceDate())){
			LocalDate currentDate  = orderInfo.getInvoiceDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			// 定义日期格式（简写月 日, 年）
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MMM/yy", Locale.US);
			// 格式化日期
			String formattedDate = currentDate .format(formatter);
			dataMap.put("invoiceDate",formattedDate);//发票日期
		}else {
			dataMap.put("invoiceDate","");//发票日期
		}

		dataMap.put("relOrderNo",orderInfo.getRelOrderNo());//关联订单号
		dataMap.put("shippingMark",isEmpty(orderTransportationInfo)?"N/M":
				isBlank(orderTransportationInfo.getShippingMark())?"N/M":orderTransportationInfo.getShippingMark());//唛头
		//汇总总数量 汇总展示
		BigDecimal shipmentQuantitySum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentQuantity)
				.filter(ObjectUtil::isNotEmpty)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		dataMap.put("shipmentQuantityTotal",shipmentQuantitySum.stripTrailingZeros().toPlainString());//汇总总数量
		//运输方式
		dataMap.put("shippingType",isBlank(orderInfo.getShippingType())?""
				:orderInfo.getShippingType());//运输方式
		//汇总净重 汇总展示
//		BigDecimal netWeightSum=orderProductInfoList.stream().map(OrderProductInfo::getNetWeight)
//				.filter(ObjectUtil::isNotEmpty)
//				.reduce(BigDecimal.ZERO, BigDecimal::add);
//		dataMap.put("netWeightTotal",netWeightSum.stripTrailingZeros().toPlainString());//汇总净重
		if("箱单".equals(fileName)){
			//表体数据
			List<Map<String, Object>> listMapList = new ArrayList<>();
			for(OrderProductInfo orderProductInfo:orderProductInfoList){
				Map<String, Object> lm = new HashMap<>();
				lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
				lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
						orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
				lm.put("shipmentPackagesNumbers",null!=orderProductInfo.getShipmentPackagesNumbers()?
						orderProductInfo.getShipmentPackagesNumbers().toString():"");//包装数量
				lm.put("netWeight",null!=orderProductInfo.getNetWeight()?
						orderProductInfo.getNetWeight().stripTrailingZeros():"");//净重
				lm.put("grossWeight",null!=orderProductInfo.getGrossWeight()?
						orderProductInfo.getGrossWeight().stripTrailingZeros():"");//毛重
				lm.put("volume",null!=orderProductInfo.getVolume()?
						orderProductInfo.getVolume().stripTrailingZeros():"");//体积
				listMapList.add(lm);
			}
			//汇总发货包装数量
			Integer shipmentPackagesNumbersSum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentPackagesNumbers)
					.filter(ObjectUtil::isNotEmpty)
					.reduce(0, Integer::sum);
			dataMap.put("shipmentPackagesNumbersTotal",shipmentPackagesNumbersSum.toString());
			//汇总净重 汇总展示
			BigDecimal netWeightTotal=orderProductInfoList.stream().map(OrderProductInfo::getNetWeight)
					.filter(ObjectUtil::isNotEmpty)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("netWeightTotal",netWeightTotal.stripTrailingZeros().toPlainString());//汇总净重
			//汇总毛重 汇总展示
			BigDecimal grossWeightSum=orderProductInfoList.stream().map(OrderProductInfo::getGrossWeight)
					.filter(ObjectUtil::isNotEmpty)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("grossWeightTotal",grossWeightSum.stripTrailingZeros().toPlainString());//汇总毛重
			//汇总体积 汇总展示
			BigDecimal volumeTotal=orderProductInfoList.stream().map(OrderProductInfo::getVolume)
					.filter(ObjectUtil::isNotEmpty)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("volumeTotal",volumeTotal.stripTrailingZeros().toPlainString());//汇总体积

			dataMap.put("listMapList", listMapList); // 表体

		}
		if("发票".equals(fileName)){
			//币制信息转换用 数字要转为字母
			List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list();
			//发票计算总货值 加上币制信息
			BigDecimal totalAmount=orderProductInfoList.stream().map(OrderProductInfo::getShipmentGoodsValue)
					.filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);

			//表体数据
			List<Map<String, Object>> listMapList = new ArrayList<>();
			for(OrderProductInfo orderProductInfo:orderProductInfoList){
				//益尚的表体数据，easypoi循环需要字段统一，否则报错
					Map<String, Object> lm = new HashMap<>();
					lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
					lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
							orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
				//币制数字转字母 可能无需转换
				if(isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())){
					List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getCode()
							.equals(orderProductInfo.getCustomsDeclarationCurrency())||
							i.getCurrency().equals(orderProductInfo.getCustomsDeclarationCurrency())).collect(Collectors.toList());
					orderProductInfo.setCustomsDeclarationCurrency(erpCurrenciesList1.isEmpty()?"":
							erpCurrenciesList1.get(0).getCurrency());
				}
					//单价
					lm.put("shipmentUnitPrice",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentUnitPrice()):(orderProductInfo.getShipmentUnitPrice()));
					//总价
					lm.put("shipmentGoodsValue",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
							(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
									orderProductInfo.getShipmentGoodsValue()):(orderProductInfo.getShipmentUnitPrice()));
					lm.put("hsmodel",isNotBlank(orderProductInfo.getHsmodel())?orderProductInfo.getHsmodel():"");//申报要素
					listMapList.add(lm);
			}
			//汇总总货值 汇总展示
			BigDecimal totalAmountSum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentGoodsValue)
					.filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("shipmentGoodsValueTotal",isNotBlank(orderProductInfoList.get(0).getCustomsDeclarationCurrency())?
					(NumberToWords.getCurrencySymbol(orderProductInfoList.get(0).getCustomsDeclarationCurrency())+
							totalAmountSum.stripTrailingZeros().toPlainString())
					:(totalAmountSum.stripTrailingZeros().toPlainString()));//汇总总货值

			dataMap.put("listMapList", listMapList); // 表体
		}
		if("外销合同".equals(fileName)){
			//币制信息转换用 数字要转为字母
			List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list();

			//表体数据
			List<Map<String, Object>> listMapList = new ArrayList<>();
			for(OrderProductInfo orderProductInfo:orderProductInfoList){
				//益尚的表体数据，easypoi循环需要字段统一，否则报错
				Map<String, Object> lm = new HashMap<>();
				lm.put("englishName",isNotBlank(orderProductInfo.getEnglishName())?orderProductInfo.getEnglishName():"");//英文名
				lm.put("shipmentQuantity",null!=orderProductInfo.getShipmentQuantity()?
						orderProductInfo.getShipmentQuantity().stripTrailingZeros():"");//数量
				//币制数字转字母 可能无需转换
				if(isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())){
					List<ErpCurrencies> erpCurrenciesList1=erpCurrenciesList.stream().filter(i->i.getCode()
							.equals(orderProductInfo.getCustomsDeclarationCurrency())||
							i.getCurrency().equals(orderProductInfo.getCustomsDeclarationCurrency())).collect(Collectors.toList());
					orderProductInfo.setCustomsDeclarationCurrency(erpCurrenciesList1.isEmpty()?"":
							erpCurrenciesList1.get(0).getCurrency());
				}
				//单价
				lm.put("shipmentUnitPrice",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
						(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
								orderProductInfo.getShipmentUnitPrice()):(orderProductInfo.getShipmentUnitPrice()));
				//总价
				lm.put("shipmentGoodsValue",isNotBlank(orderProductInfo.getCustomsDeclarationCurrency())?
						(NumberToWords.getCurrencySymbol(orderProductInfo.getCustomsDeclarationCurrency())+
								orderProductInfo.getShipmentGoodsValue()):(orderProductInfo.getShipmentUnitPrice()));
				lm.put("hsmodel",isNotBlank(orderProductInfo.getHsmodel())?orderProductInfo.getHsmodel():"");//申报要素
				listMapList.add(lm);
			}
			//汇总总货值 汇总展示
			BigDecimal totalAmountSum=orderProductInfoList.stream().map(OrderProductInfo::getShipmentGoodsValue)
					.filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
			dataMap.put("shipmentGoodsValueTotal",isNotBlank(orderProductInfoList.get(0).getCustomsDeclarationCurrency())?
					(NumberToWords.getCurrencySymbol(orderProductInfoList.get(0).getCustomsDeclarationCurrency())+
							totalAmountSum.stripTrailingZeros().toPlainString())
					:(totalAmountSum.stripTrailingZeros().toPlainString()));//汇总总货值

			dataMap.put("listMapList", listMapList); // 表体
		}
	}

	//主要用于原来的excel模板已经存在合并区域、再次合并会导致合并异常
	private boolean removeMergedRegion(Sheet sheet, CellRangeAddress mergedRegionToRemove) {
		boolean removed = false;
		for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
			CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
			if (mergedRegionToRemove.equals(mergedRegion)) {
				sheet.removeMergedRegion(i);
				removed = true;
				break;
			}
		}
		return removed;
	}


	/**
	 * 一键导出pdf
	 *
	 * @param
	 * @param id
	 * @param response
	 */
	@RequestMapping(value = "/exportPdf")
	public void exportPdf2(@RequestParam(name="fileNameList",required=true) List fileNameList,
                           @RequestParam(name="id",required=true) String id,
                           @RequestParam(name="enterpriseAccountId",defaultValue="") String enterpriseAccountId,
                           @RequestParam(name="suppliersAccountId",defaultValue="") String suppliersAccountId,
                           HttpServletResponse response) {
		// 2024/10/25 16:24@ZHANGCHAO 追加/变更/完善：先清空目录下的文件！
        String zipPath = null;
        File savefile = null;
        try {
            zipPath = upLoadPath + "/downloadPdf";
            log.info(zipPath);
            savefile = new File(zipPath);
            if (!savefile.exists()) {
                savefile.mkdirs();
            }
			// 清空目录下文件
			FileUtil.del(zipPath);
        } catch (Exception e) {
            log.error("清空目录下文件失败：", e);
        }

		String filePdfName = "";
		String fileName = "";
		for (int i = 0; i < fileNameList.size(); i++) {
			fileName = fileNameList.get(i).toString();
			HashMap<String, Object> map = setMap(fileName,id,enterpriseAccountId,suppliersAccountId);
			//最大行数
			int listMax = 0;
			if("箱单".equals(fileName)){
				listMax = 27;
				filePdfName = "packingList";
				try {
					doesItHaveItsOwnTemplate(fileName, id, "PDF");
				} catch (Exception e) {
					log.error("单独维护的箱单发票模版导出出现异常：", e.getMessage());
				}
			}else if("发票".equals(fileName)){
				listMax = 27;
				filePdfName = "commercialInvoice";
				try {
					doesItHaveItsOwnTemplate(fileName, id, "PDF");
				} catch (Exception e) {
					log.error("单独维护的箱单发票模版导出出现异常：", e.getMessage());
				}
			}else if("外销合同".equals(fileName)){
				listMax = 27;
				filePdfName = "salesContract";
				// 2025/04/08 15:19zhengliansong 追加/变更/完善：是否有单独维护的外销合同模板！！
				try {
					doesItHaveItsOwnTemplate(fileName, id, "PDF");
				} catch (Exception e) {
					e.printStackTrace();
					log.error("单独维护的外销合同模版导出出现异常：" + e.getMessage());
				}
			}else if("订舱委托书".equals(fileName)){
				listMax = 27;
				filePdfName = "orderShippingBooking";
			}else if("入货通知书".equals(fileName)){
				listMax = 27;
				filePdfName = "receiptNoticeInfo";
			}else if("报关单".equals(fileName)){
				listMax = 5;
				filePdfName = "customsDec";
			}else if("结算单".equals(fileName)){
				listMax = 23;
				filePdfName = "SummaryInfo";
			}else if("供货服务合同".equals(fileName)){
				listMax = 5;
				filePdfName = "purchaseContract";
			}else if("开票通知".equals(fileName)){
				listMax = 5;
				filePdfName = "invoicingNotice";
			}else if("送货单".equals(fileName)){
				listMax = 20;
				filePdfName = "deliveryNote";
			}
			new PrintUtil().listPrintPdf(map,filePdfName,fileName,true,listMax,orderInfoMapper.selectById(id),response);

		}

//		String zipPath = upLoadPath + "/downloadPdf";
//		log.info(zipPath);
//		File savefile = new File(zipPath);
//		if (!savefile.exists()) {
//			savefile.mkdirs();
//		}

		try {
			// 遍历zipPath目录，检查并删除对应的箱单.xlsx文件
			boolean isxdPdf = false;
			boolean isfpPdf = false;
			boolean iswxhtPdf = false;
			boolean isxdXlsxToPdf = false;
			boolean isfpXlsxToPdf = false;
			boolean iswxhtXlsxPdf = false;

			File[] files = savefile.listFiles();
			for (File file : files) {
				if (file.getName().contains("箱单.docx")) {
					isxdPdf = true;
				}if (file.getName().contains("发票.docx")) {
					isfpPdf = true;
				}
				if(file.getName().contains("箱单信息.xlsx")){
					isxdXlsxToPdf = true;
				}
				if(file.getName().contains("发票信息.xlsx")){
					isfpXlsxToPdf = true;
				}
				if(file.getName().contains("外销合同信息.xlsx")){
					iswxhtXlsxPdf = true;
				}
			}
			if (isxdPdf||isxdXlsxToPdf) {
				File xlsxFile = new File(savefile, "箱单.pdf");
				if (xlsxFile.exists()) {
					xlsxFile.delete();
				}
			}
			if (isfpPdf||isfpXlsxToPdf) {
				File xlsxFile = new File(savefile, "发票.pdf");
				if (xlsxFile.exists()) {
					xlsxFile.delete();
				}
			}
			if(iswxhtXlsxPdf){
				File xlsxFile = new File(savefile, "外销合同.pdf");
				if (xlsxFile.exists()) {
					xlsxFile.delete();
				}
			}
			if(isxdXlsxToPdf){
				//excel转pdf 箱单
				String filePathPdf = upLoadPath+"/downloadPdf/箱单信息.pdf";
				File tempFile = new File(upLoadPath+"/downloadPdf/箱单信息.xlsx");
				ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf_(tempFile.getAbsolutePath(), "箱单信息", "");
				byte[] pdfBytes = responseEntity.getBody();
				FileOutputStream outputStream = new FileOutputStream(filePathPdf);
				outputStream.write(pdfBytes);
				outputStream.close();
				try{
				// 删除文件excel
					Files.deleteIfExists(Paths.get(upLoadPath+"/downloadPdf/箱单信息.xlsx"));
//					tempFile.delete();
				}catch (Exception e){
					// 延迟重试
					try {
						Thread.sleep(1000); // 等待1秒
						FileUtil.del(upLoadPath+"/downloadPdf/箱单信息.xlsx");
					} catch (InterruptedException ex) {
						ex.printStackTrace();
					}
				}
			}else {
				//work转pdf 箱单
				String filePathPdf = upLoadPath+"/downloadPdf/箱单.pdf";
				//word转pdf
				try {
					wordToPdf(upLoadPath+"/downloadPdf/箱单.docx",filePathPdf);
					// 删除文件doc
					FileUtil.del(upLoadPath+"/downloadPdf/箱单.docx");
				} catch (Exception e) {
					e.printStackTrace();
					log.error("word转pdf失败：{}", e.getMessage());
				}
				log.info("生成的pdf：{}", filePathPdf);
			}


			if(isfpXlsxToPdf){
				//excel转pdf 发票
				//excel转pdf 箱单
				String filePathPdf = upLoadPath+"/downloadPdf/发票信息.pdf";
				File tempFile = new File(upLoadPath+"/downloadPdf/发票信息.xlsx");
				ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf_(tempFile.getAbsolutePath(), "发票信息", "");
				byte[] pdfBytes = responseEntity.getBody();
				FileOutputStream outputStream = new FileOutputStream(filePathPdf);
				outputStream.write(pdfBytes);
				outputStream.close();
				try{
					// 删除文件excel
					Files.deleteIfExists(Paths.get(upLoadPath+"/downloadPdf/发票信息.xlsx"));
//					tempFile.delete();
				}catch (Exception e){
					// 延迟重试
					try {
						Thread.sleep(1000); // 等待1秒
						FileUtil.del(upLoadPath+"/downloadPdf/发票信息.xlsx");
					} catch (InterruptedException ex) {
						ex.printStackTrace();
					}
				}
			}else {
//				work转pdf 发票
				String filePathPdf1 = upLoadPath+"/downloadPdf/发票.pdf";
				//word转pdf
				try {
					wordToPdf(upLoadPath+"/downloadPdf/发票.docx",filePathPdf1);
					// 删除文件doc
					FileUtil.del(upLoadPath+"/downloadPdf/发票.docx");
				} catch (Exception e) {
					e.printStackTrace();
					log.error("word转pdf失败：{}", e.getMessage());
				}
//				log.info("生成的pdf：{}", filePathPdf);


			}
			if(iswxhtXlsxPdf){
				//excel转pdf 箱单
				String filePathPdf = upLoadPath+"/downloadPdf/外销合同信息.pdf";
				File tempFile = new File(upLoadPath+"/downloadPdf/外销合同信息.xlsx");
				ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf_(tempFile.getAbsolutePath(), "外销合同信息", "");
				byte[] pdfBytes = responseEntity.getBody();
				FileOutputStream outputStream = new FileOutputStream(filePathPdf);
				outputStream.write(pdfBytes);
				outputStream.close();
				try{
					// 删除文件excel
					Files.deleteIfExists(Paths.get(upLoadPath+"/downloadPdf/外销合同信息.xlsx"));
//					tempFile.delete();
				}catch (Exception e){
					// 延迟重试
					try {
						Thread.sleep(1000); // 等待1秒
						FileUtil.del(upLoadPath+"/downloadPdf/外销合同信息.xlsx");
					} catch (InterruptedException ex) {
						ex.printStackTrace();
					}
				}
			}

		} catch (Exception e) {
			log.error("遍历文件夹时发生错误：", e);
		}
		try {
			FolderToZipUtil.zip(zipPath,response);
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			File file2 = new File(upLoadPath + "/downloadPdf");
//            IOCloseUtil.deleteAll(file2);
		}

	}

	/**
	 * 获取订单数据（外部接口使用）
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getOrderInformation", method = RequestMethod.GET)
	public HashMap<String,Object> importExcel(@RequestParam(name="fileName",required=true) String fileName,@RequestParam(name="id",required=true) String id) {
		OrderInfo orderInfo = orderInfoService.getById(id);
		LambdaQueryWrapper<EnterpriseInfo> queryWrapper2 = new LambdaQueryWrapper<>();
		queryWrapper2.eq(EnterpriseInfo::getTenantId,orderInfo.getTenantId());
		queryWrapper2.eq(EnterpriseInfo::getDelFlag, DEL_FLAG_0);
		EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(queryWrapper2);
		EcommercePlatformInfo ecommercePlatformInfo = iEcommercePlatformInfoService.getById(orderInfo.getOverseasWarehousePlatform());
		LambdaQueryWrapper<OrderProductInfo> queryWrapper4 = new LambdaQueryWrapper<>();
		queryWrapper4.eq(OrderProductInfo::getOrderInfoId,id);
		queryWrapper4.eq(OrderProductInfo::getDelFlag, DEL_FLAG_0);
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(queryWrapper4);
		List<ProductInfo> productInfoList = new ArrayList<>();
		String customsAreaInfo = orderInfo.getCustomsAreaInfoId() == null || customsAreaInfoService.getById(orderInfo.getCustomsAreaInfoId()) == null ?
				"" : customsAreaInfoService.getById(orderInfo.getCustomsAreaInfoId()).getDxpId();
		LambdaQueryWrapper<OrderCustomsDecExportInfo> queryWrapper6 = new LambdaQueryWrapper<>();
		queryWrapper6.eq(OrderCustomsDecExportInfo::getOrderInfoId,orderInfo.getId());
		queryWrapper6.eq(OrderCustomsDecExportInfo::getDelFlag, DEL_FLAG_0);
		OrderCustomsDecExportInfo orderCustomsDecExportInfo = orderCustomsDecExportInfoService.getOne(queryWrapper6);
		for(OrderProductInfo orderProductInfo : orderProductInfoList){
			ProductInfo productInfo = productInfoService.getById(orderProductInfo.getProductId());
			productInfoList.add(productInfo);
		}
		HashMap<String, Object> map = new PrintUtil().NumberPutMap(orderInfo,enterpriseInfo,ecommercePlatformInfo,orderProductInfoList,productInfoList,fileName,customsAreaInfo,orderCustomsDecExportInfo);
		return map;
	}
	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> selectOrder(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, OrderInfo.class);
	}

	/**
	 * 导入业务
	 *
	 * @param request
	 * @param response
	 * @return org.jeecg.common.api.vo.Result<?>
	 * <AUTHOR>
	 * @date 2024/9/23 09:58
	 */
	@RequestMapping(value = "/importBusiness", method = RequestMethod.POST)
	public Result<?> importBusiness(HttpServletRequest request, HttpServletResponse response) throws IOException {
		return orderInfoService.importBusiness(request, response);
	}

	/**
	 * 导入出口业务
	 *
	 * @param request
	 * @param response
	 * @return org.jeecg.common.api.vo.Result<?>
	 * <AUTHOR>
	 * @date 2024/9/23 13:46
	 */
	@RequestMapping(value = "/importBusinessE", method = RequestMethod.POST)
	public Result<?> importBusinessE(HttpServletRequest request, HttpServletResponse response) throws IOException {
		return orderInfoService.importBusinessE(request, response);
	}

	/**
	 * 出口订单和商品excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importOrderProduct", method = RequestMethod.POST)
	public Result<?> importOrderProduct(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			// 获取上传文件对象
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
//			params.setTitleRows(1);
			params.setHeadRows(1);
			params.setNeedSave(true);
			OutputStream fos = null;
			try {
				ExcelImportResult<OrderProductExcelVO> list = ExcelImportUtil.importExcelVerify(file.getInputStream(),
						OrderProductExcelVO.class, params);
				//update-begin-author:zhengls date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				if(list.getList().size()==0){
					return Result.error("未获取到文件数据，或请验证必填项内容");
				}
				//处理数据
				Map<String, List<OrderProductExcelVO>> orderProductExcelVOMap =
						list.getList().stream().collect(Collectors.groupingBy(OrderProductExcelVO::getOrderProtocolNo));
				orderProductExcelVOMap.forEach((orderProtocolNo, orderProductExcelVOList) -> {
                   //-----------处理表头
					OrderInfo orderInfo=orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfo>()
					.eq(OrderInfo::getOrderProtocolNo,orderProtocolNo)
							.eq(OrderInfo::getTenantId,Long.valueOf(TenantContext.getTenant()))
							.eq(OrderInfo::getDelFlag,"0"));
					//新增表头
					if(null==orderInfo){
						OrderInfo orderInfoAdd=new OrderInfo();
						BeanUtils.copyProperties(orderProductExcelVOList.get(0),orderInfoAdd);
						orderInfoAdd.setUpdateTime(new Date());
						orderInfoAdd.setIeFlag("E");
						orderInfoAdd.setOrderStatus(0);
						orderInfoAdd.setTenantId(Long.valueOf(TenantContext.getTenant()));
						orderInfoMapper.insert(orderInfoAdd);
						//表体全部新增
                        for(OrderProductExcelVO orderProductExcelVO:orderProductExcelVOList){
                        	//新增商品库
							ProductInfo productInfo=new ProductInfo();
							productInfo.setEnglishName(orderProductExcelVO.getEnglishName());
							productInfo.setChineseName(orderProductExcelVO.getChineseName());
							productInfo.setProductSpecificationModel(orderProductExcelVO.getProductSpecificationModel());
							productInfo.setIeFlag("E");
							productInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
							productInfoMapper.insert(productInfo);
                        	OrderProductInfo orderProductInfoAdd=new OrderProductInfo();
							BeanUtils.copyProperties(orderProductExcelVO,orderProductInfoAdd);
							orderProductInfoAdd.setProductId(productInfo.getId());
							orderProductInfoAdd.setOrderInfoId(orderInfoAdd.getId());
							orderProductInfoAdd.setUpdateTime(new Date());
							orderProductInfoAdd.setTenantId(Long.valueOf(TenantContext.getTenant()));
							orderProductInfoMapper.insert(orderProductInfoAdd);
						}
					}else { //存在此订单
						BeanUtils.copyProperties(orderProductExcelVOList.get(0),orderInfo);
						orderInfoMapper.updateById(orderInfo);
//						for(OrderProductExcelVO orderProductExcelVO:orderProductExcelVOList){
//							OrderProductInfo orderProductInfoAdd=new OrderProductInfo();
//							BeanUtils.copyProperties(orderProductExcelVO,orderProductInfoAdd);
//							orderProductInfoAdd.setOrderInfoId(orderInfo.getId());
//							orderProductInfoAdd.setUpdateTime(new Date());
//							orderProductInfoMapper.insert(orderProductInfoAdd);
//						}
					}
				});
				if(list.isVerfiyFail()){
//					Workbook workbook = list.getWorkbook();
//					//普通下载
//					fos = response.getOutputStream();
//					workbook.write(fos);
					return Result.ok("文件导入成功！成功数据行数：" + list.getList().size()+",存在未成功导入的数据，" +
							"请检查必填项或部分数据是否正确");
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:zhengls date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.getList().size());
			} catch (Exception e) {
				//update-begin-author:zhengls date:20211124 for: 导入数据重复增加提示
				String msg = e.getMessage();
				log.error(msg, e);
				if(msg!=null && msg.indexOf("Duplicate entry")>=0){
					return Result.error("文件导入失败:有重复数据！");
				}else{
					return Result.error("文件导入失败:" + e.getMessage());
				}
				//update-end-author:zhengls date:20211124 for: 导入数据重复增加提示
			} finally {
				try {
					file.getInputStream().close();
					if (fos != null) {
						fos.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");
	}
	/**
	 * 进口订单和商品excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importOrderProductJinKou", method = RequestMethod.POST)
	public Result<?> importOrderProductJinKou(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			// 获取上传文件对象
			MultipartFile file = entity.getValue();
			ImportParams params = new ImportParams();
//			params.setTitleRows(1);
			params.setHeadRows(1);
			params.setNeedSave(true);
			OutputStream fos = null;
			try {
				ExcelImportResult<OrderProductJinKouExcelVO> list = ExcelImportUtil.importExcelVerify(file.getInputStream(),
						OrderProductJinKouExcelVO.class, params);
				//update-begin-author:zhengls date:20190528 for:批量插入数据
				long start = System.currentTimeMillis();
				if(list.getList().size()==0){
					return Result.error("未获取到文件数据，或请验证必填项内容");
				}
				//处理数据
				Map<String, List<OrderProductJinKouExcelVO>> orderProductExcelVOMap =
						list.getList().stream().collect(Collectors.groupingBy(OrderProductJinKouExcelVO::getOrderProtocolNo));
				orderProductExcelVOMap.forEach((orderProtocolNo, orderProductExcelVOList) -> {
					//-----------处理表头
					OrderInfo orderInfo=orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfo>()
							.eq(OrderInfo::getOrderProtocolNo,orderProtocolNo)
							.eq(OrderInfo::getTenantId,Long.valueOf(TenantContext.getTenant()))
							.eq(OrderInfo::getDelFlag,"0"));
					//新增表头
					if(null==orderInfo){
						OrderInfo orderInfoAdd=new OrderInfo();
						BeanUtils.copyProperties(orderProductExcelVOList.get(0),orderInfoAdd);
						orderInfoAdd.setUpdateTime(new Date());
						orderInfoAdd.setIeFlag("I");
						orderInfoAdd.setOrderStatus(0);
						orderInfoAdd.setTenantId(Long.valueOf(TenantContext.getTenant()));
						orderInfoMapper.insert(orderInfoAdd);
						//表体全部新增
						for(OrderProductJinKouExcelVO orderProductExcelVO:orderProductExcelVOList){
							//新增商品库
							ProductInfo productInfo=new ProductInfo();
							productInfo.setEnglishName(orderProductExcelVO.getEnglishName());
							productInfo.setChineseName(orderProductExcelVO.getChineseName());
							productInfo.setProductSpecificationModel(orderProductExcelVO.getProductSpecificationModel());
							productInfo.setIeFlag("I");
							productInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
							productInfoMapper.insert(productInfo);
							OrderProductInfo orderProductInfoAdd=new OrderProductInfo();
							BeanUtils.copyProperties(orderProductExcelVO,orderProductInfoAdd);
							orderProductInfoAdd.setProductId(productInfo.getId());
							orderProductInfoAdd.setOrderInfoId(orderInfoAdd.getId());
							orderProductInfoAdd.setUpdateTime(new Date());
							orderProductInfoAdd.setTenantId(Long.valueOf(TenantContext.getTenant()));
							orderProductInfoMapper.insert(orderProductInfoAdd);
						}
					}else { //存在此订单
						BeanUtils.copyProperties(orderProductExcelVOList.get(0),orderInfo);
						orderInfoMapper.updateById(orderInfo);
//						for(OrderProductJinKouExcelVO orderProductExcelVO:orderProductExcelVOList){
//							OrderProductInfo orderProductInfoAdd=new OrderProductInfo();
//							BeanUtils.copyProperties(orderProductExcelVO,orderProductInfoAdd);
//							orderProductInfoAdd.setOrderInfoId(orderInfo.getId());
//							orderProductInfoAdd.setUpdateTime(new Date());
//							orderProductInfoMapper.insert(orderProductInfoAdd);
//						}
					}
				});
				if(list.isVerfiyFail()){
//					Workbook workbook = list.getWorkbook();
//					//普通下载
//					fos = response.getOutputStream();
//					workbook.write(fos);
					return Result.ok("文件导入成功！成功数据行数：" + list.getList().size()+",存在未成功导入的数据，" +
							"请检查必填项或部分数据是否正确");
				}
				//400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
				//1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
				log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				//update-end-author:zhengls date:20190528 for:批量插入数据
				return Result.ok("文件导入成功！数据行数：" + list.getList().size());
			} catch (Exception e) {
				//update-begin-author:zhengls date:20211124 for: 导入数据重复增加提示
				String msg = e.getMessage();
				log.error(msg, e);
				if(msg!=null && msg.indexOf("Duplicate entry")>=0){
					return Result.error("文件导入失败:有重复数据！");
				}else{
					return Result.error("文件导入失败:" + e.getMessage());
				}
				//update-end-author:zhengls date:20211124 for: 导入数据重复增加提示
			} finally {
				try {
					file.getInputStream().close();
					if (fos != null) {
						fos.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return Result.error("文件导入失败！");


	}

	/**
	 * 通过id查询
	 * 查询订单信息
	 * @param orderInfoId
	 * @return
	 */
	@ApiOperation(value="查询订单信息-通过id查询", notes="查询订单信息-通过id查询")
	@GetMapping(value = "/getOrderById")
	public Result<?> getOrderById(@RequestParam("orderInfoId") String orderInfoId, HttpServletRequest req) {
		OrderInfoBiz orderInfo = orderInfoService.getOrderById(orderInfoId);
		if(isEmpty(orderInfo)) {
			return Result.error("未找到对应数据");
		}
//		String tenantName = null;
//		try {
//			// 2023/10/16 15:04@ZHANGCHAO 追加/变更/完善：openfeign服务调用获取租户
//			Result<Tenant> tenant = sysBaseApi.getTenantById(TenantContext.getTenant());
//			if (isNotEmpty(tenant.getResult())) {
//				tenantName = tenant.getResult().getName();
//			} else {
//				String accessToken = req.getHeader(X_ACCESS_TOKEN);
//				String o = (String) redisUtil.get(PREFIX_TENANT_TOKEN_OBJ + accessToken);
//				tenantName = (String) JSONObject.parseObject(o).get("name");
//			}
//		} catch (Exception e) {
//			log.error("获取租户名出现异常：" + e.getMessage());
//		}
		// 委托方
		if (isNotBlank(orderInfo.getBuyer())) {
			Commissioner commissioner = commissionerService.getById(orderInfo.getBuyer());
			if (isNotEmpty(commissioner)) {
				orderInfo.setBuyerStr(commissioner.getCommissionerEnName());
				if (isNotBlank(commissioner.getCommissionerEnAddressDetail())) {
					// 使用正则表达式来匹配行尾符（跨平台支持）
					String[] lines = commissioner.getCommissionerEnAddressDetail().split("\\R");
					// 创建一个长度为3的数组，初始化为0
					String[] result = new String[] { "0", "0", "0" };
					// 将分割后的内容填充到数组中
					for (int i = 0; i < Math.min(lines.length, 3); i++) {
						result[i] = lines[i];
					}
					// 如果剩余部分大于3，将其合并为新字符串
					if (lines.length > 3) {
						StringBuilder remaining = new StringBuilder();
						for (int i = 2; i < lines.length; i++) {
							remaining.append(lines[i]);
						}
						result[2] = remaining.toString();
					}
					orderInfo.setBuyerAddresss(String.join("|", result));
				}
			} else {
				// 找不到說明是默認的租户
//				orderInfo.setBuyerStr(tenantName);
				List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoService.list(new LambdaQueryWrapper<EnterpriseInfo>()
						.eq(EnterpriseInfo::getTenantId, orderInfo.getBuyer()));
				if (isNotEmpty(enterpriseInfoList)) {
					orderInfo.setBuyerStr(enterpriseInfoList.get(0).getEnglishName());
					if (isNotBlank(enterpriseInfoList.get(0).getEnglishAdress())) {
						// 使用正则表达式来匹配行尾符（跨平台支持）
						String[] lines = enterpriseInfoList.get(0).getEnglishAdress().split("\\R");
						// 创建一个长度为3的数组，初始化为0
						String[] result = new String[] { "0", "0", "0" };
						// 将分割后的内容填充到数组中
						for (int i = 0; i < Math.min(lines.length, 3); i++) {
							result[i] = lines[i];
						}
						// 如果剩余部分大于3，将其合并为新字符串
						if (lines.length > 3) {
							StringBuilder remaining = new StringBuilder();
							for (int i = 2; i < lines.length; i++) {
								remaining.append(lines[i]);
							}
							result[2] = remaining.toString();
						}
						orderInfo.setBuyerAddresss(String.join("|", result));
					}
				}
			}
		}
		// 境外付款方/境外供应商
		if (isNotBlank(orderInfo.getOverseasPayerInfoId())) {
			// 境外付款方
			OverseasPayerInfo overseasPayerInfo = overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId());
			if (isNotEmpty(overseasPayerInfo)) {
				orderInfo.setOverseasPayerInfoIdStr(overseasPayerInfo.getOverseasPayerName());
				// 创建一个长度为3的数组，初始化为0
				String[] result = new String[] { "0", "0", "0" };
				if (isNotBlank(overseasPayerInfo.getOverseasPayerAddressDetail())) {
					result[0] = overseasPayerInfo.getOverseasPayerAddressDetail();
				} else {
					result[0] = isNotBlank(orderInfo.getSellerAddress())?orderInfo.getSellerAddress():"";
				}
				if (isNotBlank(overseasPayerInfo.getCompayTelphone())) {
					result[1] = overseasPayerInfo.getCompayTelphone();
				}
				if (isNotBlank(overseasPayerInfo.getOverseasPayerEmail())) {
					result[2] = overseasPayerInfo.getOverseasPayerEmail();
				}
				orderInfo.setOverseasPayerInfoIdAddresss(String.join("|", result));
			} else {
				// 境外供应商
				DomesticSuppliersInfo domesticSuppliersInfo = domesticSuppliersInfoService.getById(orderInfo.getOverseasPayerInfoId());
				if (isNotEmpty(domesticSuppliersInfo)) {
					orderInfo.setOverseasPayerInfoIdStr(domesticSuppliersInfo.getSuppliersFullName());
					// 创建一个长度为3的数组，初始化为0
					String[] result = new String[] { "0", "0", "0" };
					if (isNotBlank(domesticSuppliersInfo.getSuppliersAddressDetail())) {
						result[0] = domesticSuppliersInfo.getSuppliersAddressDetail();
					} else {
						result[0] = isNotBlank(orderInfo.getSellerAddress())?orderInfo.getSellerAddress():"";
					}
					if (isNotBlank(domesticSuppliersInfo.getCompayTelphone())) {
						result[1] = domesticSuppliersInfo.getCompayTelphone();
					}
					if (isNotBlank(domesticSuppliersInfo.getCompayFax())) {
						result[2] = domesticSuppliersInfo.getCompayFax();
					}
					orderInfo.setOverseasPayerInfoIdAddresss(String.join("|", result));
				} else {
					orderInfo.setOverseasPayerInfoIdStr(orderInfo.getOverseasPayerInfoId());
				}
			}
		}
		// 2023/11/6 8:22@ZHANGCHAO 追加/变更/完善：毛重净重！！
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(new LambdaQueryWrapper<OrderProductInfo>()
				.eq(OrderProductInfo::getOrderInfoId, orderInfo.getId()));
		BigDecimal pcs = BigDecimal.ZERO;
		BigDecimal gw = BigDecimal.ZERO;
		BigDecimal nw = BigDecimal.ZERO;
		if (isNotEmpty(orderProductInfoList)) {
//			List<ProductInfo> productInfoList = new ArrayList<>();
			for (OrderProductInfo orderProductInfo : orderProductInfoList) {
				if (isNotEmpty(orderProductInfo.getShipmentQuantity())) {
					pcs = pcs.add(orderProductInfo.getShipmentQuantity());
				}
				if (isNotEmpty(orderProductInfo.getGrossWeight())) {
					gw = gw.add(orderProductInfo.getGrossWeight());
				}
				if (isNotEmpty(orderProductInfo.getNetWeight())) {
					nw = nw.add(orderProductInfo.getNetWeight());
				}
//				ProductInfo productInfo = productInfoService.getById(orderProductInfo.getProductId());
//				if (isNotEmpty(productInfo)) {
//					productInfo.setOrderProductInfo(orderProductInfo);
//					productInfoList.add(productInfo);
//				}
			}
			orderInfo.setProductList(orderProductInfoList);
		}
		orderInfo.setPcs(isEmpty(orderInfo.getPcs()) ? pcs : orderInfo.getPcs());
		orderInfo.setGw(isEmpty(orderInfo.getGw()) ? gw : orderInfo.getGw());
		orderInfo.setNw(isEmpty(orderInfo.getNw()) ? nw : orderInfo.getNw());
		return Result.OK(orderInfo);
	}

	/**
	 * 审核/取消审核
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "审核/取消审核")
	@ApiOperation(value = "审核/取消审核", notes = "审核/取消审核")
	@PostMapping(value = "/audits")
	public Result<?> audits(@RequestParam("ids") String ids,
							@RequestParam("type") Integer type) {
		orderInfoService.update(new LambdaUpdateWrapper<OrderInfo>()
				.set(OrderInfo::getIsAudits, type)
				.in(OrderInfo::getId, Arrays.asList(ids.split(","))));
		return Result.OK(type == 1 ? "审核成功！" : "取消审核成功！");
	}

	/**
	 * 提交订单
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "提交订单")
	@ApiOperation(value = "提交订单", notes = "提交订单")
	@PostMapping(value = "/handleSubmit")
	public Result<?> handleSubmit(@RequestParam("ids") String ids) {
		orderInfoService.update(new LambdaUpdateWrapper<OrderInfo>()
				.set(OrderInfo::getOrderStatus, 1)
				.in(OrderInfo::getId, Arrays.asList(ids.split(","))));
		return Result.OK("操作成功！");
	}

	/**
	 * 保存订单
	 * @param orderInfo
	 * @return
	 */
	@AutoLog(value = "保存订单")
	@ApiOperation(value="保存订单", notes="保存订单")
	@RequestMapping(value = "/saveOrder", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<?> saveOrder(@RequestBody OrderInfoBiz orderInfo) {
		OrderInfo oldOrderInfo=new OrderInfo();
		if(isNotEmpty(orderInfo.getId())){
			 oldOrderInfo=orderInfoMapper.selectById(orderInfo.getId());
		}
		String orderInfoId = orderInfoService.saveOrder(orderInfo);
        //进口订单改状态时发消息
		if(isNotEmpty(oldOrderInfo.getId())&&"I".equals(orderInfo.getIeFlag())&&
				!oldOrderInfo.getOrderStatus().equals(orderInfo.getOrderStatus())){
           sendMessage(orderInfo);
		}
		return Result.OK("保存成功！",orderInfoId);
	}
	private void sendMessage(OrderInfoBiz orderInfo) {
		SysAnnouncement sysAnnouncement = new SysAnnouncement();
		String id = IdWorker.getIdStr(sysAnnouncement);
		sysAnnouncement.setId(id);
		sysAnnouncement.setTitile("进口订单状态变更通知");
		sysAnnouncement.setCreateTime(new Date());
		//消息类型为业务消息
		sysAnnouncement.setMsgCategory("3");
		sysAnnouncement.setMsgType(MSG_TYPE_UESR);
		String orderStatus="";
        //订单状态
		switch (orderInfo.getOrderStatus()){
			case 0:
				orderStatus="草稿";
				break;
			case 1:
				orderStatus="提交";
				break;
			case 2:
				orderStatus="到港";
				break;
			case 3:
				orderStatus="通关完成";
				break;
			case 9:
				orderStatus="订单完成";
				break;
			default:
				orderStatus="";
		}
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		sysAnnouncement.setMsgContent("订单状态变更，订单号：" + orderInfo.getOrderProtocolNo()+"状态变更为"+orderStatus
				+ (isNotEmpty(sysUser) ? ("，操作人：" + sysUser.getRealname()) : ""));
		sysAnnouncement.setBusType("Order");
		List<String> idList = new ArrayList<>(16);
		idList.add(orderInfoMapper.getUserIdByUserName(orderInfo.getCreateBy()));
		if (isEmpty(idList)) {
			log.info("[sendMessage]用户ID列表是空的，无法发送消息！");
			return;
		}
		sysAnnouncement.setUserIds(CollUtil.join(idList, ","));
//		sysAnnouncement.setOpenType("component");
//		sysAnnouncement.setOpenPage("/documentManage/DocumentManageList");
		sysAnnouncement.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
		sysAnnouncement.setSender(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
		String json = JSON.toJSONString(sysAnnouncement);
		BaseMap baseMap = new BaseMap();
		baseMap.put("message", json);
		jeecgRedisClient.sendMessage(REDIS_BUSINESS_HANDLER, baseMap);
	}

	/**
	 * 通过id查询
	 * 查询订单概览信息
	 * @param orderInfoId
	 * @return
	 */
	@ApiOperation(value="查询订单概览信息-通过id查询", notes="查询订单概览信息-通过id查询")
	@GetMapping(value = "/getOverviewInfoByOrderInfoId")
	public Result<?> getOverviewInfoByOrderInfoId(@RequestParam(name="orderInfoId",required=true) String orderInfoId) {
		OrderOverviewInfoBiz orderInfo = orderInfoService.getOverviewInfoByOrderInfoId(orderInfoId);
		if(orderInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(orderInfo);
	}

	/**
	 * 拷贝新增
	 * @param orderBaseInfo
	 * @return
	 */
	@AutoLog(value = "拷贝新增-保存")
	@ApiOperation(value="拷贝新增-保存", notes="拷贝新增-保存")
	@PostMapping(value = "/saveCopyOrderInfo")
	public Result<?> saveCopyOrderInfo(@RequestBody OrderBaseInfoBiz orderBaseInfo) {
		String orderInfoIdOld = orderBaseInfo.getId();
		orderBaseInfo.setId(null);
		String orderInfoId = orderInfoService.saveCopyOrderInfo(orderBaseInfo, orderInfoIdOld);

		Boolean smgb=false;
		Boolean swmg=false;
//		if(country_l1.contains(orderBaseInfo.getTradingCountry())||
//				country_l2.contains(orderBaseInfo.getTradingCountry())||
//				country_l3.contains(orderBaseInfo.getTradingCountry())){
//			smgb=true;
//		}
//		if(orderBaseInfo.getProductList()!=null&&
//				orderBaseInfo.getProductList().size()>0){
//			List<String> productIdList=orderBaseInfo.getProductList().stream().map(OrderProductInfo::getProductId)
//					.collect(Collectors.toList());
//			List<ProductInfo> productInfoList=productInfoMapper.selectBatchIds(productIdList);
//			List<String> productCategoryIds=productInfoList.stream().map(
//					ProductInfo::getProductCategoryInfoId
//			).collect(Collectors.toList());
//			List<ProductCategoryInfo> productCategoryInfoList=
//					productCategoryInfoMapper.selectBatchIds(productCategoryIds);
//			for(ProductCategoryInfo productCategoryInfo:productCategoryInfoList){
//				if(productRisks.contains(productCategoryInfo.getProductCategoryName())){
//					swmg=true;
//					break;
//				}
//			}
//
//		}
		return Result.OK("保存成功！"+(smgb?"请注意该订单贸易国为涉敏国别":"")
				+(swmg?"该订单产品包含税务敏感产品":""),orderInfoId);
	}

	/**
	 * 通过id删除
	 * 删除订单信息
	 * @param id
	 * @param updateTime
	 * @return
	 */
	@AutoLog(value = "删除订单信息-通过id删除")
	@ApiOperation(value="删除订单信息-通过id删除", notes="删除订单信息-通过id删除")
	@RequestMapping(value = "/deleteOrder")
//	public Result<?> deleteOrder(@RequestParam(name="id",required=true) String id) {
	public Result<?> deleteOrder(@RequestParam(name="id",required=true) String id, @RequestParam(name="updateTime",required=true) String updateTime) {
		orderInfoService.deleteOrder(id, updateTime);
		return Result.OK("删除成功！");
	}

	/**
	 * 发送报文文件至山东电子口岸
	 *
	 * @param
	 * @param
	 * @param
	 */
	private boolean messageTransmissionSend(EnterpriseInfo enterpriseInfo) throws Exception {
		boolean flage = false;
		String strPfxPath = "";//
		String strPfxPassword = "";
		String sditds_UserLoginName = "";// 企业在山东电子口岸单一窗口注册时使用的用户名
		String sditds_UserPassowrd = "";// 企业在山东电子口岸单一窗口注册时使用的用户名所对应的明文密码
		String enteSendXmlDirPath = upLoadPath + "/upload";//跨境发送报文存放目录路径
		if (true) {
			sditds_UserLoginName = enterpriseInfo.getSditdsUserloginname();
			sditds_UserPassowrd = enterpriseInfo.getSditdsUserpassowrd();
			strPfxPath = Dol(enterpriseInfo.getPrivateKeyCertificate());
			strPfxPassword = "123456";//私钥密码为123456
		}
		String encPwdBase64 = "";
		PfxInfo pfxInfo = null;
		if (true) {
			// 从pfx用户证书中读取公钥和私钥信息
			pfxInfo = BCUtil.getPfxInfo(strPfxPath, strPfxPassword);
			// 使用用户私钥对密码进行加密
			byte[] encPwd = BCUtil.encryptByPrivateKey(sditds_UserPassowrd.getBytes(),pfxInfo.getPrivateKey());
			// 把加密后的数据转换为base64字符串形式
			encPwdBase64 = Base64.encodeBase64String(encPwd);
		}
		EnteLoginInfo enteLoginInfo = new EnteLoginInfo();
		enteLoginInfo.setLoginName(sditds_UserLoginName);
		enteLoginInfo.setLoginPassword(encPwdBase64);
		FilesZipUtil filesZipUtil = new FilesZipUtil();
		ZipTempPO zipTempPO =  filesZipUtil.scanDirCompressZip(enteSendXmlDirPath);

		/**
		 * 扫描xml目录中文件压缩为zip形式。只有压缩成功后才进行下一步处理
		 */
		if (zipTempPO.isProcessSuccess()==true&&zipTempPO.getZipData()!=null&&zipTempPO.getZipData().length>10) {
			UploadFilesInfo uploadFilesInfo = new UploadFilesInfo();
			uploadFilesInfo.setFileFullName(zipTempPO.getZipFileName());
			uploadFilesInfo.setFileLength(zipTempPO.getZipData().length);
			//定时（1-5分钟）把多个xml报文压缩为zip文件然后上传至电子口岸口岸
			uploadFilesInfo.setFileMimeType(EdiFileUtil.MIME_APPLICATION_ZIP);
			uploadFilesInfo.setTextEncode("");
			uploadFilesInfo.setProjectEName("ecommerce");
			uploadFilesInfo.setMsgType("ecommerce");
			byte[] fileByteArray = zipTempPO.getZipData();
			String fileByteArrayBase64Str = Base64.encodeBase64String(fileByteArray);
			uploadFilesInfo.setFileBase64(fileByteArrayBase64Str);//
			//FileUtils.writeStringToFile(new File("C:\\tmp\\31f83f97-a0a0-44d8-bb86-60301abfd9a6.xml"), uploadFilesInfo.getFileBase64());
			if (true) {
				byte[] digestMD5 = BCUtil.digestMD5(fileByteArrayBase64Str.getBytes());
				String digestBase64 = Base64.encodeBase64String(digestMD5);
				CertificateSign certificateSign = new CertificateSign();
				certificateSign.setDigestBase64(digestBase64);
				//企业使用电子口岸颁发的私钥证书，对摘要值进行签名操作
				byte[] signBytes = BCUtil.sign(pfxInfo.getPrivateKey(), digestMD5);
				String rsaSignBase64 = Base64.encodeBase64String(signBytes);
				certificateSign.setSignBase64(rsaSignBase64);
				uploadFilesInfo.setCertificateSign(certificateSign);
			}
			EnteUploadFilesRequest enteUploadFilesRequest = new EnteUploadFilesRequest();
			enteUploadFilesRequest.setEnteLoginInfo(enteLoginInfo);
			enteUploadFilesRequest.setUploadFilesInfo(uploadFilesInfo);

			// 接口地址
			String address = "https://edi1500.sditds.com:7701/service_1501_webservice/ws/edi/EnteTransportSendFiles?wsdl";
			// 代理工厂
			JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
			// 设置代理地址
			jaxWsProxyFactoryBean.setAddress(address);
			// 设置接口类型
			jaxWsProxyFactoryBean.setServiceClass(IEnteTransportSendFilesService.class);
			// 创建一个代理接口实现
			IEnteTransportSendFilesService us = (IEnteTransportSendFilesService) jaxWsProxyFactoryBean.create();

			// 调用代理接口的方法调用并返回结果
			EnteUploadFilesResponse enteUploadFilesResponse = us.uploadFiles(enteUploadFilesRequest, null);

			DefaultResponse defaultResponse = enteUploadFilesResponse.getDefaultResponse();
			System.out.println("respCode:["+defaultResponse.getRespCode()+"],procMessage["+defaultResponse.getProcMessage()+"]");
			if ("0000".equals(defaultResponse.getRespCode())) {
				//报文上传成功后，需要把send文件夹下上传的报文删除或者移动到备份目录
				List<File> oldXmlFileList = zipTempPO.getFileList();
				for (int i = 0; i < oldXmlFileList.size(); i++) {
					File oldXmlFile = oldXmlFileList.get(i);
					boolean delFlag = FileUtils.deleteQuietly(oldXmlFile);
					if (delFlag) {
						flage = true;
						log.debug("成功删除已发送报文："+oldXmlFile.getAbsolutePath());
					}else {
						log.debug("删除已经发送报文出错....本地编程");
					}
				}
				File file = new File(upLoadPath + "/certificate/MA3.pfx");
				if (file.exists()){
					file.delete();
				}
			}else if ("9999".equals(defaultResponse.getRespCode())) {
				//因服务端在维护，什么都不需要做，可一直定时报文上传，发送目录下的xml报文也不要删除
				log.debug("服务端系统维护中，暂时不提供服务，请稍后再试");
			}else {
				//其他错误，根据不同错误做相应处理......
				log.error("根据状态代码进行相应本地编程......");
			}
		}else if (zipTempPO.isProcessSuccess()==true&&zipTempPO.getFileList()==null&&zipTempPO.getZipData()==null) {
			log.debug("目录下没有报文，不压缩，不发送");
		}else {
			log.error("xml报文文件压缩异常......");
		}
		return flage;
	}

	/**
	 * 生成xml方法
	 */
	@RequestMapping(value = "/getOrderInformationXml", method = RequestMethod.GET)
	public Result<?> createXml(@RequestParam(name="fileName",required=true) String fileName,@RequestParam(name="id",required=true) String id){
		boolean result = true;
		HashMap<String,Object> getOrder = importExcel(fileName,id);
		List<HashMap<String,Object>> list = (List<HashMap<String,Object>>)getOrder.get("listMap");
		try {
			UUID uuid = UUID.randomUUID();
			UUID uuid2 = UUID.randomUUID();
			// 1、创建document对象
			Document document = DocumentHelper.createDocument();
			// 2、创建根节点rss
			Element rss = document.addElement("ceb:CEB303Message");
			// 3、向rss节点添加guid属性
			rss.addAttribute("guid", uuid.toString());
			rss.addAttribute("version", "1.0");
			rss.addNamespace("ceb","http://www.chinaport.gov.cn/ceb");
			rss.addNamespace("xsi","http://www.w3.org/2001/XMLSchema-instance");

			for (int i = 0; i < list.size(); i++) {
				// 4、生成子节点及子节点内容
				Element Order = rss.addElement("ceb:Order");
				//添加OrderHead节点
				Element OrderHead = Order.addElement("ceb:OrderHead");

				Element guid = OrderHead.addElement("ceb:" + "guid");
				Element appType = OrderHead.addElement("ceb:appType");
				Element appTime = OrderHead.addElement("ceb:appTime");
				Element appStatus = OrderHead.addElement("ceb:appStatus");
				Element orderType = OrderHead.addElement("ceb:orderType");
				Element orderNo = OrderHead.addElement("ceb:orderNo");
				Element ebpCode = OrderHead.addElement("ceb:ebpCode");
				Element ebpName = OrderHead.addElement("ceb:ebpName");
				Element ebcCode = OrderHead.addElement("ceb:ebcCode");
				Element ebcName = OrderHead.addElement("ceb:ebcName");
				Element goodsValue = OrderHead.addElement("ceb:goodsValue");
				Element freight = OrderHead.addElement("ceb:freight");
				Element currency = OrderHead.addElement("ceb:currency");
				Element note = OrderHead.addElement("ceb:note");

				guid.setText(uuid2.toString());
				appType.setText("1");
				appTime.setText("20180507153001");
				appStatus.setText("2");
				if ("9710订单信息".equals(fileName)) {
					orderType.setText("B");
				} else if ("9810订仓单信息".equals(fileName)) {
					orderType.setText("W");
				}

				orderNo.setText(getOrder.get("order_no_nine_seven").toString());
				ebpCode.setText(getOrder.get("ebpcode").toString());
				ebpName.setText(getOrder.get("ecommerce_platform_name").toString());
				ebcCode.setText(getOrder.get("customs_declaration_code").toString());
				ebcName.setText(getOrder.get("enterprise_full_name").toString());
				goodsValue.setText(getOrder.get("foreign_exchange_amount").toString());
				freight.setText(getOrder.get("freight").toString());
				currency.setText(getOrder.get("customs_declaration_currency").toString());
				note.setText(getOrder.get("remarks").toString());

				//添加OrderList节点
				Element OrderList = Order.addElement("ceb:OrderList");
				Element gnum = OrderList.addElement("ceb:gnum");
				Element itemNo = OrderList.addElement("ceb:itemNo");
				Element itemName = OrderList.addElement("ceb:itemName");
				Element itemDescribe = OrderList.addElement("ceb:itemDescribe");
				Element barCode = OrderList.addElement("ceb:barCode");
				Element unit = OrderList.addElement("ceb:unit");
				Element currencys = OrderList.addElement("ceb:currency");
				Element qty = OrderList.addElement("ceb:qty");
				Element price = OrderList.addElement("ceb:price");
				Element totalPrice = OrderList.addElement("ceb:totalPrice");
				Element notes = OrderList.addElement("ceb:note");

				gnum.setText(list.get(i).get("N/A").toString());
				itemNo.setText("无");
				itemName.setText(list.get(i).get("chinese_name").toString());
				itemDescribe.setText(list.get(i).get("chinese_name").toString());
				barCode.setText(list.get(i).get("bar_code").toString());
				unit.setText(list.get(i).get("legal_unit").toString());
				currencys.setText(list.get(i).get("customs_declaration_currency").toString());
				qty.setText(list.get(i).get("legal_quantity").toString());
				price.setText(list.get(i).get("shipment_unit_price").toString());
				totalPrice.setText(list.get(i).get("total_price").toString());
				notes.setText("");
			}

			//添加BaseTransfer节点
			Element BaseTransfer = rss.addElement("ceb:BaseTransfer");
			Element copCode = BaseTransfer.addElement("ceb:copCode");
			Element copName = BaseTransfer.addElement("ceb:copName");
			Element dxpMode = BaseTransfer.addElement("ceb:dxpMode");
			Element dxpId = BaseTransfer.addElement("ceb:dxpId");
			Element noter = BaseTransfer.addElement("ceb:note");

			copCode.setText(getOrder.get("customs_declaration_code").toString());
			copName.setText(getOrder.get("enterprise_full_name").toString());
			dxpMode.setText("DXP");
			dxpId.setText(getOrder.get("message_transmission_number").toString());
			noter.setText("");

			// 5、设置生成xml的格式
			OutputFormat format = OutputFormat.createPrettyPrint();
			// 设置编码格式
			format.setEncoding("UTF-8");

			// 6、生成xml文件
//			File file0 = new File(this.getClass().getResource("/").getPath()+"upload");
			File file0 = new File(upLoadPath + "/upload");
//			System.out.println(this.getClass().getResource("/").getPath()+"upload");
			// 如果目录不存在，则创建目录
			if (!file0.exists()){
				file0.mkdirs();
			}
			file0.setExecutable(true);//设置可执行权限
			file0.setReadable(true);//设置可读权限
			file0.setWritable(true);//设置可写权限
//			File file = new File(this.getClass().getResource("/").getPath()+"upload/CEB303Message.xml");
			File file = new File(upLoadPath + "/upload/CEB303Message.xml");
//			System.out.println(this.getClass().getResource("/").getPath()+"upload/CEB303Message.xml");
			file.setExecutable(true);//设置可执行权限
			file.setReadable(true);//设置可读权限
			file.setWritable(true);//设置可写权限
			XMLWriter writer = new XMLWriter(new FileOutputStream(file), format);
			// 设置是否转义，默认使用转义字符
			writer.setEscapeText(false);
			writer.write(document);
			writer.close();
			System.out.println("生成CEB303Message.xml成功");
			result = addSignToreqDate();

		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("生成CEB303Message.xml失败");
			return Result.error("生成报文失败！");
		}
		if (!result){
			return Result.error("推送失败！");
		}
		//推送成功后更新推送状态为1
		boolean oResult = true;
		Long tenantId = Long.valueOf(TenantContext.getTenant());
		OrderInfo orderInfo = orderInfoService.getOne(new LambdaQueryWrapper<OrderInfo>()
				.eq(OrderInfo::getId, id)
				.eq(OrderInfo::getTenantId, tenantId)
				.eq(OrderInfo::getDelFlag, DEL_FLAG_0));
		orderInfo.setPushStatus(DEL_FLAG_1);
		LambdaUpdateWrapper<OrderInfo> updateWrapperO = new UpdateWrapper().lambda();
		updateWrapperO.last(CommonUtils.SetUpdateSqlCondition(orderInfo.getUpdateTime(), orderInfo.getId()));
		oResult = orderInfoService.update(orderInfo, updateWrapperO);
		if (!oResult){
			return Result.error("更新失败！");
		}
		return Result.OK("推送成功！");
	}

	/**
	 * 加签方法并重新生成xml
	 */
	private  boolean addSignToreqDate() throws DocumentException {
		// 文件路径
//		String path = "/usr/jar/" + this.getClass().getResource("/").getPath()+"upload/CEB303Message.xml";
		String path = upLoadPath + "/upload/CEB303Message.xml";

//		InputStream inputStream = this.getClass().getResourceAsStream("/upload/CEB303Message.xml");
		boolean sendFlage = false;
		try {
			EnterpriseInfo enterpriseInfo =	orderInfoService.getEnterpriseInfoInfoId(Long.valueOf(TenantContext.getTenant()));

		// 读取xml文件
		Document document = getDocument(path);
		// 获取根节点(此处为Users)
		Element root = document.getRootElement();
		//添加Signature节点
			Element Signature = root.addElement("ds:Signature", "http://www.w3.org/2000/09/xmldsig#");

			Element SignedInfo = Signature.addElement("ds:SignedInfo");
			Element CanonicalizationMethod = SignedInfo.addElement("ds:CanonicalizationMethod");
			CanonicalizationMethod.addAttribute("Algorithm", "http://www.w3.org/TR/2001/REC-xml-c14n-20010315");

			Element SignatureMethod = SignedInfo.addElement("ds:SignatureMethod");
			SignatureMethod.addAttribute("Algorithm","http://www.w3.org/2000/09/xmldsig#rsa-sha1");

			Element Reference = SignedInfo.addElement("ds:Reference");
			Reference.addAttribute("URI","");

			Element Transforms = Reference.addElement("ds:Transforms");
			Element Transform = Transforms.addElement("ds:Transform");
			Transform.addAttribute("Algorithm","http://www.w3.org/2000/09/xmldsig#enveloped-signature");

			Element DigestMethod = Reference.addElement("ds:DigestMethod");
			DigestMethod.addAttribute("Algorithm","http://www.w3.org/2000/09/xmldsig#sha1");

			Element DigestValue = Reference.addElement("ds:DigestValue");
			//获取摘要值

//			byte[] digestMD5 = BCUtil.digestMD5(toByteArray("/usr/jar/" + this.getClass().getResource("/").getPath()+"upload/CEB303Message.xml"));
			byte[] digestMD5 = BCUtil.digestMD5(toByteArray(upLoadPath + "/upload/CEB303Message.xml"));
			String digestBase64 = Base64.encodeBase64String(digestMD5);
			DigestValue.setText(digestBase64);

			Element SignatureValue = Signature.addElement("ds:SignatureValue");
			//获取数字签名
			PfxInfo pfxInfo = null;
			pfxInfo = BCUtil.getPfxInfo(Dol(enterpriseInfo.getPrivateKeyCertificate()), "123456");
			byte[] signBytes = BCUtil.sign(pfxInfo.getPrivateKey(), digestMD5);
			String rsaSignBase64 = Base64.encodeBase64String(signBytes);
			SignatureValue.setText(rsaSignBase64);

			Element KeyInfo = Signature.addElement("ds:KeyInfo");
			Element KeyName = KeyInfo.addElement("ds:KeyName");

			//调用方法获取证书序列号
			String kName = ReadPFX.getOpertatorSn(Dol(enterpriseInfo.getPrivateKeyCertificate()), "123456");
			KeyName.setText(kName);

			Element X509Data = KeyInfo.addElement("ds:X509Data");
			Element X509Certificate = X509Data.addElement("ds:X509Certificate");

			//获取x509证书
			File pfxFile = new File(Dol(enterpriseInfo.getPrivateKeyCertificate()));
			Base64 base64 = new Base64();
			String string  = ReadPFX.getX509Certificate(FileUtils.readFileToByteArray(pfxFile),"123456").toString();
			String base64Sign = base64.encodeToString(string.getBytes("UTF-8"));
			X509Certificate.setText(base64Sign);

		// 5、设置生成xml的格式
		OutputFormat format = OutputFormat.createPrettyPrint();
		// 设置编码格式
		format.setEncoding("UTF-8");

		// 6、生成xml文件
		//File file = new File(this.getClass().getResource("/").getPath()+"upload/CEB303Message.xml");
			File file = new File(upLoadPath+"/upload/CEB303Message.xml");
			file.setExecutable(true);//设置可执行权限
			file.setReadable(true);//设置可读权限
			file.setWritable(true);//设置可写权限
		XMLWriter writer = new XMLWriter(new FileOutputStream(file), format);
		// 设置是否转义，默认使用转义字符
		writer.setEscapeText(false);
		writer.write(document);
		writer.close();
		System.out.println("加签CEB303Message.xml成功");
		sendFlage = messageTransmissionSend(enterpriseInfo);

		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("加签CEB303Message.xml失败");
			return false;
		}

		return sendFlage;
	}
	/**
	 * 获取Document对象
	 *
	 * @param inputStream
	 * @return
	 * @throws DocumentException
	 */
	private Document getDocument(String inputStream) {
		Document document = null;
		try {
			// 1.需要通过SAXReader流加载XML文件
			SAXReader saxReader = new SAXReader();
			// 2.获取Document文件
			document = saxReader.read(inputStream);
		} catch (DocumentException e) {
			System.out.println("读取失败");
			e.printStackTrace();
		}

		return document;
	}

	/**
	 * oss下载文件
	 *
	 * @param
	 * @return
	 * @throws
	 */
	private String Dol(String familyDiagram){
		// OSS存储文件的路径
		String repUrl = "https://" + OssBootUtil.getBucketName() + "." + OssBootUtil.getStaticDomain() + "/";
		String url = familyDiagram.replace(repUrl, "");
		String basePath = url;
		String certificate = "";
		// 下载到本地的目录
//		String localPath = this.getClass().getResource("/").getPath()+"certificate/";
		String localPath = upLoadPath + "/certificate/";
		File file = new File(localPath);
		// 如果目录不存在，则创建目录
		if (!file.exists()){
			file.mkdirs();
		}
		file.setExecutable(true);//设置可执行权限
		file.setReadable(true);//设置可读权限
		file.setWritable(true);//设置可写权限
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(OssBootUtil.getEndPoint(), OssBootUtil.getAccessKeyId(), OssBootUtil.getAccessKeySecret());
		// 下载OSS文件到本地文件。如果指定的本地文件存在会覆盖，不存在则新建。
		String lujing = localPath+"MA3.pfx";
		File file2 = new File(lujing);
		ossClient.getObject(new GetObjectRequest(OssBootUtil.getBucketName(), basePath), file2);
		file2.setExecutable(true);//设置可执行权限
		file2.setReadable(true);//设置可读权限
		file2.setWritable(true);//设置可写权限
		// 关闭OSSClient。
		ossClient.shutdown();
		certificate = localPath + "MA3.pfx";
		System.out.println(certificate);
		return certificate;
	}

	/**
	 * 获取文件字节
	 *
	 * @param filename
	 * @return
	 * @throws IOException
	 */
	private static byte[] toByteArray(String filename) throws IOException {

		File f = new File(filename);
		if (!f.exists()) {
			throw new FileNotFoundException(filename);
		}

		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			bos.close();
		}
	}

	/**
	 * 判断是否是字母加两位数字的格式
	 *
	 * @param a
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/3/5 10:23
	 */
	public static boolean isValidFormat(String a) {
		String pattern = "[A-Za-z]\\d{2}";
		return Pattern.matches(pattern, a);
	}

//	/**
//	 *
//	 *
//	 * @param shipmentUnit
//	 * @return java.lang.String
//	 * <AUTHOR>
//	 * @date 2024/10/22 16:18
//	 */
//	private String getUnitChange(String shipmentUnit) {
//		return null;
//	}

	/**
	 * 获取打印数据
	 *
	 * @param fileName
	 * @param id
	 * @return
	 * @throws IOException
	 */
	private HashMap<String, Object> setMap(String fileName, String id, String enterpriseAccountId, String suppliersAccountId){
//		订单信息
		OrderInfo orderInfo = orderInfoService.getById(id);
//		订单运输信息
		LambdaQueryWrapper<OrderTransportationInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrderTransportationInfo::getOrderInfoId,id);
		queryWrapper.eq(OrderTransportationInfo::getDelFlag, DEL_FLAG_0);
		OrderTransportationInfo orderTransportationInfo = orderTransportationInfoService.getOne(queryWrapper);
//		企业信息
		LambdaQueryWrapper<EnterpriseInfo> queryWrapper2 = new LambdaQueryWrapper<>();
		queryWrapper2.eq(EnterpriseInfo::getTenantId,orderInfo.getTenantId());
		queryWrapper2.eq(EnterpriseInfo::getDelFlag, DEL_FLAG_0);
		EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(queryWrapper2);
//		订单入货通知单信息
		LambdaQueryWrapper<OrderReceiptNoticeInfo> queryWrapper3 = new LambdaQueryWrapper<>();
		queryWrapper3.eq(OrderReceiptNoticeInfo::getOrderInfoId,id);
		queryWrapper3.eq(OrderReceiptNoticeInfo::getDelFlag, DEL_FLAG_0);
		OrderReceiptNoticeInfo orderReceiptNoticeInfo = orderReceiptNoticeInfoService.getOne(queryWrapper3);
//		订单商品信息
		LambdaQueryWrapper<OrderProductInfo> queryWrapper4 = new LambdaQueryWrapper<>();
		queryWrapper4.eq(OrderProductInfo::getOrderInfoId,id);
		queryWrapper4.eq(OrderProductInfo::getDelFlag, DEL_FLAG_0);
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(queryWrapper4);
		List<ProductInfo> productInfoList = new ArrayList<>();
		String customsAreaInfo = "";
		for(OrderProductInfo orderProductInfo : orderProductInfoList){
			ProductInfo productInfo = productInfoService.getById(orderProductInfo.getProductId());
			if (isNotEmpty(productInfo)) {
				productInfoList.add(productInfo);
			}
		}
//		货代信息
		LogisticsInfo logisticsInfo = orderReceiptNoticeInfo == null||orderReceiptNoticeInfo.getLogisticsId() ==null ? null : logisticsInfoService.getById(orderReceiptNoticeInfo.getLogisticsId());
//		报关信息
		LambdaQueryWrapper<OrderCustomsDecExportInfo> queryWrapper6 = new LambdaQueryWrapper<>();
		queryWrapper6.eq(OrderCustomsDecExportInfo::getOrderInfoId,orderInfo.getId());
		queryWrapper6.eq(OrderCustomsDecExportInfo::getDelFlag, DEL_FLAG_0);
		OrderCustomsDecExportInfo orderCustomsDecExportInfo = orderCustomsDecExportInfoService.getOne(queryWrapper6);
//		供应商信息
		DomesticSuppliersInfo domesticSuppliersInfo = orderCustomsDecExportInfo == null || orderCustomsDecExportInfo.getDomesticSuppliersInfoId() == null ? null : domesticSuppliersInfoService.getById(orderCustomsDecExportInfo.getDomesticSuppliersInfoId());
//		电商平台信息
		EcommercePlatformInfo ecommercePlatformInfo = orderInfo == null || orderInfo.getOverseasWarehousePlatform() == null ? null : iEcommercePlatformInfoService.getById(orderInfo.getOverseasWarehousePlatform());
		String overseasPayerInfoName = "";
		String erpCityportsName = "";
		String erpChinaPortsName = "";
		String erpCurrenciesName = "";
		String erpCustomsPorts = "";
		String erpTransportTypes = "";
		List<ErpDistricts> erpDistricts = null;
		List<ErpCountries> erpCountriesList = null;
		if(orderInfo != null){
			overseasPayerInfoName = orderInfo.getOverseasPayerInfoId() == null || overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId()) == null ?
					"" : overseasPayerInfoService.getById(orderInfo.getOverseasPayerInfoId()).getOverseasPayerName();
		}
		if("报关单".equals(fileName) && orderTransportationInfo != null && orderInfo != null ){
			LambdaQueryWrapper<ErpCityports> queryWrapper7 = new LambdaQueryWrapper<>();
			queryWrapper7.eq(ErpCityports::getCityportCode,orderTransportationInfo.getPortDestination());
			queryWrapper7.eq(ErpCityports::getDelFlag, DEL_FLAG_0);
			erpCityportsName = orderTransportationInfo.getPortDestination() == null || erpCityportsService.getOne(queryWrapper7) == null ?
					"" : erpCityportsService.getOne(queryWrapper7).getCnname();
			LambdaQueryWrapper<ErpChinaPorts> queryWrapper8 = new LambdaQueryWrapper<>();
			queryWrapper8.eq(ErpChinaPorts::getChinaPortCode,orderTransportationInfo.getDeparturePort());
			queryWrapper8.eq(ErpChinaPorts::getDelFlag, DEL_FLAG_0);
			LambdaQueryWrapper<ErpCurrencies> queryWrapper9 = new LambdaQueryWrapper<>();
			queryWrapper9.eq(ErpCurrencies::getCode,orderInfo.getCustomsDeclarationCurrency());
			queryWrapper9.eq(ErpCurrencies::getDelFlag, DEL_FLAG_0);
			erpChinaPortsName = orderTransportationInfo.getDeparturePort() == null || erpChinaPortsService.getOne(queryWrapper8) == null ?
					"" : erpChinaPortsService.getOne(queryWrapper8).getName();
			erpCurrenciesName = orderInfo.getCustomsDeclarationCurrency() == null || erpCurrenciesService.getOne(queryWrapper9) == null ?
					"" : erpCurrenciesService.getOne(queryWrapper9).getName();
			LambdaQueryWrapper<ErpCustomsPorts> queryWrapper12 = new LambdaQueryWrapper<>();
			queryWrapper12.eq(ErpCustomsPorts::getCustomsPortCode,orderInfo.getExitClearance());
			queryWrapper12.eq(ErpCustomsPorts::getDelFlag, DEL_FLAG_0);
			erpCustomsPorts = orderInfo.getExitClearance() == null || erpCustomsPortsService.getOne(queryWrapper12) == null ?
					"" : erpCustomsPortsService.getOne(queryWrapper12).getName();
			LambdaQueryWrapper<ErpTransportTypes> queryWrapper13 = new LambdaQueryWrapper<>();
			queryWrapper13.eq(ErpTransportTypes::getCode,orderTransportationInfo.getShippingType());
			queryWrapper13.eq(ErpTransportTypes::getDelFlag, DEL_FLAG_0);
			erpTransportTypes =orderTransportationInfo.getShippingType() == null || erpTransportTypesService.getOne(queryWrapper13) == null ?
					"" : erpTransportTypesService.getOne(queryWrapper13).getName();
			LambdaQueryWrapper<ErpCountries> queryWrapper14 = new LambdaQueryWrapper<>();
			queryWrapper14.eq(ErpCountries::getDelFlag, DEL_FLAG_0);
			queryWrapper14.eq(ErpCountries::getIsenabled, DEL_FLAG_1);
			erpCountriesList = erpCountriesService.list(queryWrapper14);
			LambdaQueryWrapper<ErpDistricts> queryWrapper15 = new LambdaQueryWrapper<>();
			queryWrapper15.eq(ErpDistricts::getDelFlag, DEL_FLAG_0);
			erpDistricts = erpDistrictsService.list(queryWrapper15);
		}else if(orderTransportationInfo != null && orderInfo != null ){
			LambdaQueryWrapper<ErpCityports> queryWrapper7 = new LambdaQueryWrapper<>();
			queryWrapper7.eq(ErpCityports::getCityportCode,orderTransportationInfo.getPortDestination());
			queryWrapper7.eq(ErpCityports::getDelFlag, DEL_FLAG_0);
			erpCityportsName = orderTransportationInfo.getPortDestination() == null || erpCityportsService.getOne(queryWrapper7) == null ?
					"" : erpCityportsService.getOne(queryWrapper7).getEnname();
			LambdaQueryWrapper<ErpChinaPorts> queryWrapper8 = new LambdaQueryWrapper<>();
			queryWrapper8.eq(ErpChinaPorts::getChinaPortCode,orderTransportationInfo.getDeparturePort());
			queryWrapper8.eq(ErpChinaPorts::getDelFlag, DEL_FLAG_0);
			LambdaQueryWrapper<ErpCurrencies> queryWrapper9 = new LambdaQueryWrapper<>();
			queryWrapper9.eq(ErpCurrencies::getCode,orderInfo.getCustomsDeclarationCurrency());
			queryWrapper9.eq(ErpCurrencies::getDelFlag, DEL_FLAG_0);
			erpChinaPortsName = orderTransportationInfo.getDeparturePort() == null || erpChinaPortsService.getOne(queryWrapper8) == null ?
					"" : erpChinaPortsService.getOne(queryWrapper8).getEnname();
			erpCurrenciesName = orderInfo.getCustomsDeclarationCurrency() == null || erpCurrenciesService.getOne(queryWrapper9) == null ?
					"" : erpCurrenciesService.getOne(queryWrapper9).getCurrency();
			LambdaQueryWrapper<ErpCountries> queryWrapper14 = new LambdaQueryWrapper<>();
			queryWrapper14.eq(ErpCountries::getDelFlag, DEL_FLAG_0);
			queryWrapper14.eq(ErpCountries::getIsenabled, DEL_FLAG_1);
			erpCountriesList = erpCountriesService.list(queryWrapper14);
			LambdaQueryWrapper<ErpDistricts> queryWrapper15 = new LambdaQueryWrapper<>();
			queryWrapper15.eq(ErpDistricts::getDelFlag, DEL_FLAG_0);
			erpDistricts = erpDistrictsService.list(queryWrapper15);
		}
		if(!"9710订单信息".equals(fileName)&&!"9810订仓单信息".equals(fileName)){
			for(OrderProductInfo orderProductInfo : orderProductInfoList){
				LambdaQueryWrapper<ErpUnits> queryWrapper10 = new LambdaQueryWrapper<>();
				queryWrapper10.eq(ErpUnits::getCode,orderProductInfo.getShipmentUnit());
				queryWrapper10.eq(ErpUnits::getDelFlag, DEL_FLAG_0);
				String erpUnitsName = "";
				if("报关单".equals(fileName)||"供货服务合同".equals(fileName)||"开票通知".equals(fileName)||"送货单".equals(fileName)){
					erpUnitsName = orderProductInfo.getShipmentUnit() == null || erpUnitsService.getOne(queryWrapper10) == null ?
							"" : erpUnitsService.getOne(queryWrapper10).getName();
				}else{
					erpUnitsName = orderProductInfo.getShipmentUnit() == null || erpUnitsService.getOne(queryWrapper10) == null ?
							"" : erpUnitsService.getOne(queryWrapper10).getEnname();
				}
				orderProductInfo.setShipmentUnit(erpUnitsName);
			}
		}else{
			customsAreaInfo = orderInfo.getCustomsAreaInfoId() == null || customsAreaInfoService.getById(orderInfo.getCustomsAreaInfoId()) == null ?
					"" : customsAreaInfoService.getById(orderInfo.getCustomsAreaInfoId()).getDxpId();
		}
		if("订舱委托书".equals(fileName) || "报关单".equals(fileName)){
			for(OrderProductInfo orderProductInfo : orderProductInfoList){
				LambdaQueryWrapper<ErpPackagesTypes> queryWrapper11 = new LambdaQueryWrapper<>();
				queryWrapper11.eq(ErpPackagesTypes::getCode,orderProductInfo.getShipmentPackingType());
				queryWrapper11.eq(ErpPackagesTypes::getDelFlag, DEL_FLAG_0);
				String erpPackagesTypesName = orderProductInfo.getShipmentPackingType() == null || erpPackagesTypesService.getOne(queryWrapper11) == null ?
						"" : erpPackagesTypesService.getOne(queryWrapper11).getName();
				orderProductInfo.setShipmentPackingType(erpPackagesTypesName);
			}
		}
		HashMap<String, Object> map = null;
		if("箱单".equals(fileName)){
			map = new PrintUtil().packingListPutMap(orderInfo,orderTransportationInfo,enterpriseInfo,orderProductInfoList,productInfoList,overseasPayerInfoName,erpCityportsName,erpChinaPortsName,erpCurrenciesName,fileName);
		}else if("发票".equals(fileName)){
			map = new PrintUtil().packingListPutMap(orderInfo,orderTransportationInfo,enterpriseInfo,orderProductInfoList,productInfoList,overseasPayerInfoName,erpCityportsName,erpChinaPortsName,erpCurrenciesName,fileName);
		}else if("外销合同".equals(fileName)){
			map = new PrintUtil().packingListPutMap(orderInfo,orderTransportationInfo,enterpriseInfo,orderProductInfoList,productInfoList,overseasPayerInfoName,erpCityportsName,erpChinaPortsName,erpCurrenciesName,fileName);
		}else if("订舱委托书".equals(fileName)){
			LambdaQueryWrapper<OrderShippingBookingInfo> queryWrapper5 = new LambdaQueryWrapper<>();
			queryWrapper5.eq(OrderShippingBookingInfo::getOrderInfoId,orderInfo.getId());
			queryWrapper5.eq(OrderShippingBookingInfo::getDelFlag, DEL_FLAG_0);
			OrderShippingBookingInfo orderShippingBookingInfo = orderShippingBookingInfoService.getOne(queryWrapper5);
			map = new PrintUtil().shippingBookingPutMap(orderInfo,orderTransportationInfo,enterpriseInfo,logisticsInfo,orderShippingBookingInfo,erpCityportsName,erpChinaPortsName,orderProductInfoList,orderReceiptNoticeInfo);
		}else if("入货通知书".equals(fileName)){
			map = new PrintUtil().receiptNoticeInfoMap(orderInfo,orderTransportationInfo,enterpriseInfo,logisticsInfo,orderProductInfoList,orderReceiptNoticeInfo);
		}else if("报关单".equals(fileName)){
			CustomsBrokerInfo customsBrokerInfo = customsBrokerInfoService.getById(orderInfo.getCustomsBrokerId());
			map = new PrintUtil().customsDecPutMap(orderInfo,orderTransportationInfo,enterpriseInfo,domesticSuppliersInfo,orderProductInfoList,customsBrokerInfo,orderCustomsDecExportInfo,
					erpCityportsName,erpChinaPortsName,erpCurrenciesName,erpCustomsPorts,erpTransportTypes,erpCountriesList,erpDistricts);
		}else if("9710订单信息".equals(fileName)){
			map = new PrintUtil().NumberPutMap(orderInfo,enterpriseInfo,ecommercePlatformInfo,orderProductInfoList,productInfoList,fileName,customsAreaInfo,orderCustomsDecExportInfo);
		}else if("9810订仓单信息".equals(fileName)){
			map = new PrintUtil().NumberPutMap(orderInfo,enterpriseInfo,ecommercePlatformInfo,orderProductInfoList,productInfoList,fileName,customsAreaInfo,orderCustomsDecExportInfo);
		}else if("结算单".equals(fileName)){
			Map<String,Object> printMap = new HashMap<>();
			String supervisionModeName = orderInfo.getSupervisionMode();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");

			String finalContryText = "";
			String exitClearanceText = "";
			if(erpCountriesList != null){
				for(ErpCountries erpCountries:erpCountriesList){
					if(orderInfo.getFinalContry() != null && orderInfo.getFinalContry().equals(erpCountries.getCode())){
						finalContryText = erpCountries.getName();
					}
				}
			}
			if(erpDistricts != null){
				for(ErpDistricts erpDistricts1:erpDistricts){
					if(orderInfo.getExitClearance() != null && orderInfo.getExitClearance().equals(erpDistricts1.getCode())){
						exitClearanceText = erpDistricts1.getName();
					}
				}
			}
			printMap.put("overseasPayerInfoId_dictText",overseasPayerInfoName);
			printMap.put("supervisionMode_dictText",supervisionModeName);
			printMap.put("orderProtocolNo",orderInfo.getOrderProtocolNo());
			printMap.put("closeDate",orderInfo.getCloseDate() !=null ? df.format(orderInfo.getCloseDate()) : "");
			printMap.put("finalContry_dictText",finalContryText);
			printMap.put("exitClearance_dictText",exitClearanceText);
			printMap.put("customsNumber",orderInfo.getCustomsNumber());
			printMap.put("totalContractAmount",orderInfo.getTotalContractAmount().intValue());
			printMap.put("customsDeclarationCurrency_dictText",erpCurrenciesName);
			OrderSummaryInfoBiz orderSummaryInfoBiz = new OrderSummaryInfoBiz();
			orderSummaryInfoBiz.setId(orderInfo.getId());
			orderSummaryInfoBiz.setTenantId(orderInfo.getTenantId());
			List<HashMap> orderPrintInAmountXlsList = orderSummaryInfoService.getOrderPrintInAmountXlsList(orderSummaryInfoBiz);
			List<HashMap> orderPrintOutAmountXlsList = orderSummaryInfoService.getOrderPrintOutAmountXlsList(orderSummaryInfoBiz);
			map = new PrintUtil().queryPagePrint3PutMap(printMap,orderPrintInAmountXlsList,orderPrintOutAmountXlsList);
		}else if("供货服务合同".equals(fileName)){
			map = new PrintUtil().purchaseContractPutMap(orderInfo,enterpriseInfo,orderProductInfoList,domesticSuppliersInfo);
		}else if("开票通知".equals(fileName)){
			BankAccountInfo enterpriseBankAccountInfo = bankAccountInfoService.getById(enterpriseAccountId);
			BankAccountInfo suppliersBankAccountInfo = bankAccountInfoService.getById(suppliersAccountId);
			map = new PrintUtil().invoicingNoticePutMap(orderInfo,enterpriseInfo,orderProductInfoList,domesticSuppliersInfo,enterpriseBankAccountInfo,suppliersBankAccountInfo);
		}else if("送货单".equals(fileName)){
			map = new PrintUtil().deliveryNotePutMap(orderInfo,enterpriseInfo,orderProductInfoList,domesticSuppliersInfo,orderCustomsDecExportInfo);
		}
		return map;
	}
	/**
	 *  冻结解冻企业
	 * @param
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "/handleFreezeThaw", method = {RequestMethod.POST, RequestMethod.PUT})
	public Result<?> handleFreezeThaw(@RequestBody JSONObject json) {
		String id = json.getString("id");
		String status = json.getString("status");
		if(null==id){
			return Result.error("未获取到订单信息，请刷新重试");
		}
		orderInfoMapper.update(null,new UpdateWrapper<OrderInfo>()
				.lambda().set(OrderInfo::getHasNormalFlag,status).eq(OrderInfo::getId,id));
		return Result.ok("操作成功");
	}
	/**
	 *  正常失效
	 * @param
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "/handleOrderFlag")
	public Result<?> handleOrderFlag(@RequestBody JSONObject json) {
		String id = json.getString("id");
		String status = json.getString("status");
		if(null==id){
			return Result.error("未获取到订单信息，请刷新重试");
		}
		orderInfoMapper.update(null,new UpdateWrapper<OrderInfo>()
				.lambda().set(OrderInfo::getOrderFlag,status).eq(OrderInfo::getId,id));
		return Result.ok("操作成功");
	}
	/**
	 * 批量删除订单
	 * @param ids
	 * @return
	 */
	//@RequiresRoles({ "admin" })
	@DeleteMapping(value = "/deleteBatch")
	public Result<OrderInfo> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		Result<OrderInfo> result = new Result<>();
		try {
			String[] arr = ids.split(",");
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
			for (String id : arr) {
				if (oConvertUtils.isNotEmpty(id)) {
//					OrderInfo orderInfo=orderInfoMapper.selectById(id);
					orderInfoService.deleteOrder(id,null);
				}
			}
			result.success("删除成功!");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.error500("删除成功!");
		}
		return result;
	}
	@RequestMapping("/handleQueryOrder")
	public Result<String> handleQueryOrder(@RequestBody JSONObject jsonObject){
		String ids=jsonObject.getString("ids");
		List<String> idList=Arrays.asList(ids.split(","));
		orderInfoMapper.update(null,new LambdaUpdateWrapper<OrderInfo>()
				.set(OrderInfo::getThirdPartyStatus,1)
				.in(OrderInfo::getId,idList));
		return Result.ok("查询订单状态成功");
	}
	@RequestMapping("/handleBack")
	public Result<String> handleBack(@RequestBody JSONObject jsonObject){
		String ids=jsonObject.getString("ids");
		List<String> idList=Arrays.asList(ids.split(","));
		orderInfoMapper.update(null,new LambdaUpdateWrapper<OrderInfo>()
		.set(OrderInfo::getHasBack,1)
		.in(OrderInfo::getId,idList));
		return Result.ok("操作撤销成功");
	}
	@RequestMapping("/handleUrge")
	public Result<String> handleUrge(@RequestBody JSONObject jsonObject) {
		String ids=jsonObject.getString("ids");
		List<String> idList=Arrays.asList(ids.split(","));
		orderInfoMapper.update(null,new LambdaUpdateWrapper<OrderInfo>()
				.set(OrderInfo::getHasUrge,1)
				.in(OrderInfo::getId,idList));
		return Result.ok("操作催单成功");
	}

	/**
	 * 操作批量通关登记
	 * @param jsonObject
	 * @return
	 */
	@RequestMapping("/handleBatchCustomsClearance")
	public Result<String> handleBatchCustomsClearance(@RequestBody JSONObject jsonObject) {
		String ids=jsonObject.getString("ids");
		List<String> idList=Arrays.asList(ids.split(","));
		orderInfoMapper.update(null,new LambdaUpdateWrapper<OrderInfo>()
				.set(OrderInfo::getOrderStatus,2)
				.in(OrderInfo::getId,idList));
		return Result.ok("操作批量通关登记成功,请到【通关登记】模块查看");
	}
	/**
	 * 导出excel
	 *
	 * @param request
	 * @param orderInfo
	 */
	@RequestMapping(value = "/exportOrderProduct")
	public ModelAndView exportOrderProduct(HttpServletRequest request, OrderInfo orderInfo) {
		// Step.1 组装查询条件
		orderInfo.setDelFlag(0);
		orderInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
		QueryWrapper<OrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(orderInfo, request.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// Step.2 获取导出数据
		List<OrderInfo> pageList = service.list(queryWrapper);
		List<OrderProductExcelVO> exportList = new ArrayList<>();
		//表头和表体一起导出 组装数据
		for(OrderInfo orderInfo1:pageList){
           //该表头全部表体
			List<OrderProductInfo> orderProductInfoList=orderProductInfoMapper.selectList(
					new LambdaQueryWrapper<OrderProductInfo>()
							.eq(OrderProductInfo::getOrderInfoId,orderInfo1.getId())
			);
			if(orderProductInfoList.size()==0){
				OrderProductExcelVO orderProductExcelVO=new OrderProductExcelVO();
				BeanUtils.copyProperties(orderInfo1,orderProductExcelVO);
				exportList.add(orderProductExcelVO);
			}else {
				for(OrderProductInfo orderProductInfo:orderProductInfoList){
					OrderProductExcelVO orderProductExcelVO=new OrderProductExcelVO();
					BeanUtils.copyProperties(orderInfo1,orderProductExcelVO);
					BeanUtils.copyProperties(orderProductInfo,orderProductExcelVO);
					exportList.add(orderProductExcelVO);
				}
			}
		}

		// Step.3 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "出口订单信息");
		mv.addObject(NormalExcelConstants.CLASS, OrderProductExcelVO.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("出口订单信息", "导出人:" + sysUser.getRealname());
		exportParams.setImageBasePath(upLoadPath);
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
		return mv;
	}
	/**
	 * 导出进口订单excel
	 *
	 * @param request
	 * @param orderInfo
	 */
	@RequestMapping(value = "/exportOrderProductJinKou")
	public ModelAndView exportOrderProductJinKou(HttpServletRequest request, OrderInfo orderInfo) {
		// Step.1 组装查询条件
		orderInfo.setDelFlag(0);
		orderInfo.setTenantId(Long.valueOf(TenantContext.getTenant()));
		QueryWrapper<OrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(orderInfo, request.getParameterMap());
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// Step.2 获取导出数据
		List<OrderInfo> pageList = service.list(queryWrapper);
		List<OrderProductJinKouExcelVO> exportList = new ArrayList<>();
		//表头和表体一起导出 组装数据
		for(OrderInfo orderInfo1:pageList){
			//该表头全部表体
			List<OrderProductInfo> orderProductInfoList=orderProductInfoMapper.selectList(
					new LambdaQueryWrapper<OrderProductInfo>()
							.eq(OrderProductInfo::getOrderInfoId,orderInfo1.getId())
			);
			if(orderProductInfoList.size()==0){
				OrderProductJinKouExcelVO orderProductExcelVO=new OrderProductJinKouExcelVO();
				BeanUtils.copyProperties(orderInfo1,orderProductExcelVO);
				exportList.add(orderProductExcelVO);
			}else {
				for(OrderProductInfo orderProductInfo:orderProductInfoList){
					OrderProductJinKouExcelVO orderProductExcelVO=new OrderProductJinKouExcelVO();
					BeanUtils.copyProperties(orderInfo1,orderProductExcelVO);
					BeanUtils.copyProperties(orderProductInfo,orderProductExcelVO);
					exportList.add(orderProductExcelVO);
				}
			}
		}

		// Step.3 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "进口订单信息");
		mv.addObject(NormalExcelConstants.CLASS, OrderProductJinKouExcelVO.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("进口订单信息", "导出人:" + sysUser.getRealname());
		exportParams.setImageBasePath(upLoadPath);
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
		return mv;
	}

	/**
	 * 订单附件批量导出 压缩到zip
	 *
	 * @param
	 * @param
	 */
	@RequestMapping(value = "/batchDownloadFiles")
	public void batchDownloadFiles(@RequestParam(name="id",required=true) String id,
						   HttpServletResponse response) throws IOException {
		response.setHeader("content-type", "application/zip");
		response.setContentType("application/zip");
		// 下载文件能正常显示中文
		response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
		OrderInfo orderInfo=orderInfoMapper.selectById(id);
        AttachmentsInfo attachmentsInfo=attachmentsInfoMapper.selectOne(
    		new LambdaQueryWrapper<AttachmentsInfo>().eq(AttachmentsInfo::getRelationId,id)
	);
    //存在附件数据
   		 if(isNotEmpty(attachmentsInfo)&& StringUtils.isNotBlank(attachmentsInfo.getRelationId())){
   		 	String tempFolderName="订单"+orderInfo.getOrderProtocolNo()+"附件";
			 String tempPatch= upLoadPath+"/"+tempFolderName+"/";
   		 	String[] fileSrcs= attachmentsInfo.getAttachmentsFileName().split(",");
   		 	for(String fileSrc:fileSrcs){
					if(fileSrc.startsWith("http")){
						String filePath = MinioUtil.downloadFile(fileSrc, null);
						FileUtil.copy(new File(filePath),
								new File(tempPatch+fileSrc.substring(fileSrc.lastIndexOf("/"))),false);
					} else {
						FileUtil.copy(new File(upLoadPath+ File.separator+fileSrc),
								new File(tempPatch+fileSrc.substring(12)),false);
					}
			}
   		 	//进行文件夹压缩
			 CompressUtil.zip(tempPatch);
			 InputStream inputStream=null;
			 OutputStream outputStream=null;
			 try {
				 // 创建File对象
				 File file = new File(tempPatch.substring(0,tempPatch.length()-1)+".zip");
				 // 创建FileInputStream对象
				  inputStream = new FileInputStream(file);
				  outputStream = response.getOutputStream();
				 // 将文件内容写入输出流
				 byte[] buffer = new byte[1024];
				 int bytesRead;
				 while ((bytesRead = inputStream.read(buffer)) != -1) {
					 outputStream.write(buffer, 0, bytesRead);
				 }
			 } catch (IOException e) {
				 e.printStackTrace();
			 }finally {
				 inputStream.close();
				 outputStream.close();
				 FileUtil.del(tempPatch);
				 FileUtil.del(tempPatch.substring(0,tempPatch.length()-1)+".zip");
			 }
		}
	}

	/**
	 * 出运订单给订单创建人系统通知
	 * @return
	 */
	@GetMapping("/handleMessage")
	public Result<String> handleMessage( @RequestParam(name="id") String id){
		if(StringUtils.isBlank(id)){
			return Result.error("未获取到订单信息，请刷新重试");
		}
		OrderInfo orderInfo=orderInfoMapper.selectById(id);
		SysAnnouncement sysAnnouncement = new SysAnnouncement();
		String sysAnnouncementId = IdWorker.getIdStr(sysAnnouncement);
		sysAnnouncement.setId(sysAnnouncementId);
		sysAnnouncement.setTitile("入货通知");
		sysAnnouncement.setCreateTime(new Date());
		//消息类型为业务消息
		sysAnnouncement.setMsgCategory("3");
		sysAnnouncement.setMsgType(MSG_TYPE_UESR);
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		sysAnnouncement.setMsgContent("订单号:"+orderInfo.getOrderProtocolNo()+"已入货，请及时取货"
				+ (isNotEmpty(sysUser) ? ("，操作人：" + sysUser.getRealname()) : ""));
		sysAnnouncement.setBusType("Order");
		List<String> idList = new ArrayList<>(16);
		idList.add(orderInfoMapper.getUserIdByUserName(orderInfo.getCreateBy()));
		if (isEmpty(idList)) {
			log.info("[sendMessage]用户ID列表是空的，无法发送消息！");
		}
		sysAnnouncement.setUserIds(CollUtil.join(idList, ","));
//		sysAnnouncement.setOpenType("component");
//		sysAnnouncement.setOpenPage("/documentManage/DocumentManageList");
		sysAnnouncement.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
		sysAnnouncement.setSender(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
		String json = JSON.toJSONString(sysAnnouncement);
		BaseMap baseMap = new BaseMap();
		baseMap.put("message", json);
		jeecgRedisClient.sendMessage(REDIS_BUSINESS_HANDLER, baseMap);
        return Result.ok("操作成功");
	}

	@GetMapping(value = "/generateDec")
	public Result<?> generateDec(@RequestParam(name="orderInfoId") String orderInfoId, HttpServletRequest request){
		try {
			String decId = orderInfoService.generateDec(orderInfoId, request);
			return Result.OK(decId);
		} catch (Exception e) {
			log.error(e.getMessage());
			return Result.error(e.getMessage());
		}
	}

	@GetMapping(value = "/generateDecHeadByOrderNo")
	public Result<?> generateDecHeadByOrderNo(@RequestParam(name="orderInfoNo") String orderInfoNo, HttpServletRequest request){
		try {
			DecHead decHead = orderInfoService.generateDecHeadByOrderNo(orderInfoNo, request);
			//检查本次生成的报关单 表体价格 商品库价格检测
			if(isNotEmpty(decHead.getDecLists())){
				List<DecList> decLists=decHead.getDecLists();
				//获取全部报关单表体的产品id
				List<String> productInfoIdList=decLists.stream().map(DecList::getProductInfoId)
						.filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList());
				if(isNotEmpty(productInfoIdList)){
					//未通过检测的申报品名
					List<String> failItem=new ArrayList<>();
					List<ProductInfo> productInfoList = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
							.in(ProductInfo::getId, productInfoIdList));
					for(ProductInfo productInfo:productInfoList){
						//产品的价格和波动范围不为空
						if(isNotEmpty(productInfo.getPrice())&&isNotEmpty(productInfo.getPriceFluctuationRatio())){
							//该产品最高价格
							BigDecimal maxPrice=productInfo.getPrice().multiply(new BigDecimal(1)
									.add(productInfo.getPriceFluctuationRatio().divide(new BigDecimal(100))));
							//该产品最低价格
							BigDecimal minPrice=productInfo.getPrice().multiply(new BigDecimal(1)
									.subtract(productInfo.getPriceFluctuationRatio().divide(new BigDecimal(100))));
							//该产品的所有表体
							List<DecList> decListList=decLists.stream().filter(i->i.getProductInfoId()
									.equals(productInfo.getId())).collect(Collectors.toList());
							for(DecList decList:decListList){
								//小于范围最低价或高于最高价
								if(decList.getPrice().compareTo(minPrice)<0||
										decList.getPrice().compareTo(maxPrice)>0){
									failItem.add(String.valueOf(decList.getItem()));
								}

							}
						}
					}
					if(failItem.size()>0){
						String msgContent="请注意，以下项数的商品库价格检查未通过，申报单价超出对应商品的价格上下波动范围。" +
								System.lineSeparator()+"【"+
								String.join(",",failItem)+"】";
						return Result.OK(msgContent,decHead);
					}
				}
			}
			return Result.OK(decHead);
		} catch (Exception e) {
			log.error(e.getMessage());
			return Result.error(e.getMessage());
		}
	}

	/**
	 * 业务快捷生成草单
	 *
	 * @param id
	 * @return org.jeecg.common.api.vo.Result<?>
	 * <AUTHOR>
	 * @date 2024/9/26 13:45
	 */
	@PostMapping(value = "/generateSaveDecAndInvt")
	public Result<?> generateSaveDecAndInvt(@RequestParam("id") String id, HttpServletRequest request) {
		return orderInfoService.generateSaveDecAndInvt(id, request);
	}

	/**
	 * 根据订单生成商检单
	 *
	 * @param orderInfoNo
	 * @return
	 */
	@AutoLog(value = "根据订单生成商检单")
	@ApiOperation(value = "根据订单生成商检单", notes = "根据订单生成商检单")
	@RequestMapping(value = "/generateCiqByOrderNo")
	public Result<?> generateCiqByOrderNo(@RequestParam("orderInfoNo") String orderInfoNo) {
		return orderInfoService.generateCiqByOrderNo(orderInfoNo);
	}

	/**
	 * 用户登录接口-获取Token
	 *
	 * @param account 用户名
	 * @param password 	密码
	 * @return 查询结果
	 */
	@ApiOperation(value = "用户登录接口-获取Token", notes = "用户登录接口-获取Token")
	@PostMapping(value = "/getToken")
	public Result<?> getToken(@RequestParam("Account") String account,
							  @RequestParam("PassWord") String password,
							  @RequestParam("isCache") boolean isCache) {
		return orderInfoService.getToken(account, password, isCache);
	}

	/**
	 * 舱单场站数据查询
	 *
	 * @param mblno 主提单号
	 * @param yard 	场站
	 * @return 查询结果
	 */
	@ApiOperation(value = "舱单场站数据查询", notes = "舱单场站数据查询")
	@PostMapping(value = "/getManifestResult")
	public Result<?> getManifestResult(@RequestParam("MBLNO") String mblno,
									   @RequestParam("YARD") String yard,
									   @RequestParam(value = "Account", required = false) String account,
									   @RequestParam(value = "PassWord", required = false) String password,
									   @RequestParam(value = "Token", required = false) String token) {
		return orderInfoService.getManifestResult(mblno, yard, account, password, token);
	}

	/**
	 * 利用正则表达式判断字符串是否是数字
	 * @param str
	 * @return
	 */
	private boolean isNumeric(String str){
		Pattern pattern = Pattern.compile("[0-9]*");
		Matcher isNum = pattern.matcher(str);
		if( !isNum.matches() ){
			return false;
		}
		return true;
	}

	/**
	 * 获取打印数据
	 *
	 * @param id
	 * @return org.jeecg.modules.business.entity.OrderInfo
	 * <AUTHOR>
	 * @date 2024/10/21 15:46
	 */
	private OrderInfo setDocData(String id) {
		//		订单信息
		OrderInfo orderInfo = orderInfoService.getById(id);
		//		订单商品信息
		LambdaQueryWrapper<OrderProductInfo> queryWrapper4 = new LambdaQueryWrapper<>();
		queryWrapper4.eq(OrderProductInfo::getOrderInfoId,id);
		List<OrderProductInfo> orderProductInfoList = orderProductInfoService.list(queryWrapper4);
		orderInfo.setProductList(orderProductInfoList);
		if (isNotEmpty(orderInfo)) {
			// 处理关联订单号
			// 2025/2/10 14:25@ZHANGCHAO 追加/变更/完善：改为发票号！！！
			if (isNotBlank(orderInfo.getInvoiceNo())) {
				if (orderInfo.getInvoiceNo().contains(",")) {
					orderInfo.setRelOrderNo(orderInfo.getInvoiceNo().replaceAll(",", "&"));
				} else {
					orderInfo.setRelOrderNo(orderInfo.getInvoiceNo());
				}
			} else {
				orderInfo.setRelOrderNo("");
			}
			// 处理合同号
			if (isNotBlank(orderInfo.getExportContractNo())) {
				if (orderInfo.getExportContractNo().contains(",")) {
					orderInfo.setExportContractNo(orderInfo.getExportContractNo().replaceAll(",", "&"));
				}
			}
			// 处理ETD
			if (isNotEmpty(orderInfo.getEstimatedPortTime())) {
				orderInfo.setEstimatedPortTimeStr(DateUtil.format(orderInfo.getEstimatedPortTime(), "dd/MM/yyyy"));
			}
			// 处理DATE
			orderInfo.setTodayStr(DateUtil.format(new Date(), "dd/MM/yyyy"));
			List<DictQuery> dictModels8 = commonMapper.listDict("trading_type");
			Map<String, String> dictMap8 = new HashMap<>();
			if (isNotEmpty(dictModels8)) {
				dictModels8.forEach(dictModel -> dictMap8.put(dictModel.getCode(), dictModel.getName()));
			}
			// 2025/3/6 13:56@ZHANGCHAO 追加/变更/完善：处理银行信息！！
			if (isNotBlank(orderInfo.getBankInfoId())) {
				List<BankAccountInfoHx> bankAccountInfoHxs = bankAccountInfoHxService.list(new LambdaUpdateWrapper<BankAccountInfoHx>()
						.eq(BankAccountInfoHx::getAccountNo, orderInfo.getBankInfoId()));
				if (isNotEmpty(bankAccountInfoHxs)) {
					orderInfo.setBankInfoMark(bankAccountInfoHxs.get(0).getRemark());
				} else {
					orderInfo.setBankInfoMark("Bank Name: Bank of China Jinan Branch                                  Tel : + 86-531- ********/31           \n" +
							"Address: No.22 LuoYuan Street, Jinan, China 250063                         Fax: + 86-531- ********        \n" +
							"Account No.: 2 2 3 4 0 0 0 0 3 2 8 1                                         SWIFT: BKCHCNBJ500");
				}
			} else {
				orderInfo.setBankInfoMark("Bank Name: Bank of China Jinan Branch                                  Tel : + 86-531- ********/31           \n" +
						"Address: No.22 LuoYuan Street, Jinan, China 250063                         Fax: + 86-531- ********        \n" +
						"Account No.: 2 2 3 4 0 0 0 0 3 2 8 1                                         SWIFT: BKCHCNBJ500");
			}
			// 2025/3/13 13:50@ZHANGCHAO 追加/变更/完善：如果是空的，则取运输方式！！
			if (isBlank(orderInfo.getShipVia())) {
				orderInfo.setShipVia(orderInfo.getShippingType());
			}
			// 2025/3/5 10:21@ZHANGCHAO 追加/变更/完善：处理支付条款！！
			if (isNotBlank(orderInfo.getPayment()) && isValidFormat(orderInfo.getPayment())) {
				List<DictQuery> dictModels = commonMapper.listDict("PAYMENT_CLAUSE_DICT");
				Map<String, String> dictMap = new HashMap<>();
				if (isNotEmpty(dictModels8)) {
					dictModels.forEach(dictModel -> dictMap.put(dictModel.getCode(), dictModel.getName()));
				}
				if(dictMap.containsKey(orderInfo.getPayment())) {
					orderInfo.setPayment(dictMap.get(orderInfo.getPayment()));
				}
			}
			// 处理表体
			if (isNotEmpty(orderInfo.getProductList())) {
				orderInfo.getProductList().forEach(item -> {
					// 处理数量
					item.setQty(isNotEmpty(item.getShipmentQuantity()) ? item.getShipmentQuantity().setScale(1, RoundingMode.HALF_UP).toString() : "");
					// 处理单位
					if (isNotBlank(item.getShipmentUnit())) {
						item.setUnit(item.getShipmentUnit());
						// 数字需要转换
						if (isNumeric(item.getShipmentUnit())) {
							DictQuery dictQuery = commonMapper.getUnitsQuery(item.getShipmentUnit());
							if (isNotEmpty(dictQuery)) {
								item.setUnit(isNotBlank(dictQuery.getText()) ? dictQuery.getText() : "");
							}
						}
					} else {
						item.setUnit("");
					}
					// 处理英文品名
					item.setEnglishName(isNotBlank(item.getEnglishName()) ? item.getEnglishName() : "");
					// 处理包装规格
					item.setPackKind(isNotBlank(item.getPackKind()) ? item.getPackKind() : "");
					item.setShipmentUnitPrice(isNotEmpty(item.getShipmentUnitPrice()) ? item.getShipmentUnitPrice().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
					item.setShipmentGoodsValue(isNotEmpty(item.getShipmentGoodsValue()) ? item.getShipmentGoodsValue().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
					BigDecimal nw = orderInfo.getProductList().stream().map(OrderProductInfo::getNetWeight).filter(ObjectUtil::isNotEmpty)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
					BigDecimal gw = orderInfo.getProductList().stream().map(OrderProductInfo::getGrossWeight).filter(ObjectUtil::isNotEmpty)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
					BigDecimal vo = orderInfo.getProductList().stream().map(OrderProductInfo::getVolume).filter(ObjectUtil::isNotEmpty)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
					item.setNw(nw.setScale(1, RoundingMode.HALF_UP).toString());
					item.setGw(gw.setScale(1, RoundingMode.HALF_UP).toString());
					item.setVo(vo.setScale(3, RoundingMode.HALF_UP).toString());
					// 2024/10/25 11:18@ZHANGCHAO 追加/变更/完善：补充字段！！
					item.setShipVia(orderInfo.getShipVia());
					item.setDesPortEn(orderInfo.getDesPortEn());
					if (isNotEmpty(orderInfo.getTradingType())) {
						if(dictMap8.containsKey(orderInfo.getTradingType().toString())) {
							item.setTradingTypeStr(dictMap8.get(orderInfo.getTradingType().toString()));
						}
					}
				});
			}
		}
		log.info("打印数据：{}", JSON.toJSONString(orderInfo));
		return orderInfo;
	}
	@GetMapping("/listOrderInfo")
	public Result<?> listOrderInfo(OrderInfo orderInfo,
								 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
								 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
								 HttpServletRequest req) {
		Page<OrderInfo> page = new Page(pageNo, pageSize);
		LambdaQueryWrapper<OrderInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.like(isNotBlank(orderInfo.getOrderProtocolNo()),
				OrderInfo::getOrderProtocolNo,orderInfo.getOrderProtocolNo());
		queryWrapper.eq(OrderInfo::getTenantId,TenantContext.getTenant());
		queryWrapper.isNotNull(OrderInfo::getOrderProtocolNo);
		queryWrapper.orderByDesc(OrderInfo::getCreateTime);
		Page<OrderInfo> orderInfoPage = orderInfoMapper.selectPage(page, queryWrapper);
		return Result.OK(orderInfoPage);
	}

	/**
	 * 从品名库重新带取数据
	 *
	 * @param ids
	 * @return org.jeecg.common.api.vo.Result<?>
	 * <AUTHOR>
	 * @date 2025/4/3 19:05
	 */
	@GetMapping(value = "/getRebringData")
	public Result<?> getRebringData(@RequestParam("ids") String ids) {
		return orderInfoService.getRebringData(ids);
	}
}
