package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.PtsEmsAimg;
import org.jeecg.modules.business.entity.PtsEmsHead;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 手账册表头 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface IPtsEmsHeadService extends IService<PtsEmsHead> {

    /**
     * 查询指定分页列表的PtsEmsHead对象
     *
     * @param page  分页对象
     * @param ptsEmsHead PtsEmsHead对象
     * @return 指定分页列表的PtsEmsHead对象
     */
    IPage<PtsEmsHead> queryPageList(Page<PtsEmsHead> page, PtsEmsHead ptsEmsHead);

    /**
     * 保存电子手册信息
     *
     * @param ptsEmsHead 电子手册信息
     * @return 保存结果
     */
    Result<?> saveEmsHead(PtsEmsHead ptsEmsHead);

    /**
     * 根据id获取EmsHead对象
     *
     * @param id EmsHead的唯一标识
     * @return 返回对应的EmsHead对象，如果不存在则返回null
     */
    Result<?> getEmsHeadById(String id);

    /**
     * 批量删除方法
     *
     * @param ids 要删除的ID集合
     * @return 删除结果
     */
    Result<?> deleteBatch(String ids);

    /**
     * 获取EMS详细信息列表
     *
     * @param page   分页信息
     * @param emsQueryDto EMSS查询条件
     * @return 列表页面
     */
    IPage listEmsDetail(Page page, EmsQueryDto emsQueryDto);

    /**
     * 获取EMS详细信息列表-手册报表中心
     *
     * @param page   分页信息
     * @param emsQueryDto EMSS查询条件
     * @return 列表页面
     */
    IPage listEmsDetailByReport(Page page, EmsQueryDto emsQueryDto);
    /**
     * 获取EMS手册报表统计-核销平衡
     *
     * @param page   分页信息
     * @param emsQueryDto EMSS查询条件
     * @return 列表页面
     */
    IPage listEmsDetailWriteOffBalance(Page page, EmsQueryDto emsQueryDto);

    /**
     * 获取EMS手册报表统计-进口保税科件重量统计/出口保税成品重量统计
     */
    Result<?> listEmsDetailWeightStatistics(EmsQueryDto emsQueryDto);

    /**
     * 导出记录
     *
     * @param request  请求对象
     * @param putrecNo 记录编号
     * @param gNos     记录编号列表
     * @param type     类型
     * @return 返回导出记录的视图对象
     */
    void exportRecord(HttpServletRequest request, HttpServletResponse response, String putrecNo, String gNos, String type);

    /**
     * 批量删除指定类型的细节信息
     *
     * @param ids    分隔符为","的细节信息ID集合
     * @param type    细节信息类型
     * @return        返回删除结果
     */
    Result<?> deleteDetailBatch(String ids, String type);

    /**
     * 通过字段批量导出料件
     *
     * @param emsQueryDto   查询条件DTO对象
     * @param request       HTTP请求对象
     * @param response      HTTP响应对象
     */
    void exportAimgByFieldsBatch(EmsQueryDto emsQueryDto, HttpServletRequest request, HttpServletResponse response);

    /**
     * 检查库存
     *
     * @param emsNo  物流单号
     * @param gNos  商品编号集合
     * @return  结果对象
     */
    Result<?> checkInventory(String emsNo, String gNos);

    /**
     * 获取EMS统计信息
     *
     * @param emsNo EMS编号
     * @return 统计结果
     */
    Result<?> emsStatistics(String emsNo);

    /**
     * 获取可用的AEXG结果
     *
     * @param emsId 仓库ID
     * @param gNo 商品编号
     * @return Result<?> 获取结果
     */
    Result<?> getUsableAexg(String emsId, String gNo);

    /**
     * 检查所有者编码
     *
     * @param ownerCode 所有者编码
     * @param id        ID
     * @return 结果
     */
    Result<?> checkOwnerCode(String ownerCode, String id);

    /**
     * 根据门店编码获取配送头部信息
     *
     * @param storeCode 门店编码
     * @return 配送头部信息的结果
     */
    Result<?> getEmsHeadByStoreCode(String storeCode);

    /**
     * 账册库存查询列表
     *
     * @param page
     * @param inventoryFlowsVO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO>
     * <AUTHOR>
     * @date 2024/2/1 11:53
     */
    IPage<InventoryFlowsVO> listInventoryFlows(IPage<InventoryFlowsVO> page, InventoryFlowsVO inventoryFlowsVO);

    Result<?> xxx(String emsNo, String gNo);

    /**
     * 操作导入手帐册
     * @param type
     * @param emsHead
     * @return
     */
    Result<Map<String,Object>> importEms(Integer type, PtsEmsHead emsHead);

    /**
     * 账册发送报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/1/20 15:16
     */
    Result<?> handlePush(String ids,String type,String flag);

    /**
     * 回执回填统一编号
     *
     * @param id
     * @param etpsPreentNo
     * @param seqNo
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.PtsEmsHead>
     * <AUTHOR>
     * @date 2025/1/22 10:02
     */
    Result<PtsEmsHead> setSeqNoByEtpsNoOrId(String id, String etpsPreentNo, String seqNo);

    /**
     * 获取EMS详细信息列表进口报税料件/成品金额统计-手册报表中心
     *
     * @param page   分页信息
     * @param emsQueryDto EMSS查询条件
     * @return 列表页面
     */
    IPage listEmsDetailAmountByReport(Page page, EmsQueryDto emsQueryDto);
    /**
     * 获取EMS详细信息列表单价比较
     *
     * @param page   分页信息
     * @param emsQueryDto EMSS查询条件
     * @return 列表页面
     */
    IPage<PtsEmsAimg> listEmsDetailByUnitPriceComparison(Page page, EmsQueryDto emsQueryDto);
    /**
     * 根据条件获取树茂的手帐册表头数据
     *
     */
    Result<?> syncSmPtsEmsHeadData(String swid, String systemId, String seqNo,String tradeCode,String manualNo);
    /**
     * 根据条件获取树茂的手帐册的备案信息（明细）
     */
    Result<?> syncSmPtsEmsRecordData(String swid, String systemId, String seqNo,String tradeCode,
                                     String manualNo,String tenantId,String PageNo,PtsEmsHead ptsEmsHead);
    /**
     * 手册回执更新回填信息，状态
     */
    Result<?> updatePtsEmsByReceipt(PtsEmsHead ptsEmsHead,String status);
}
