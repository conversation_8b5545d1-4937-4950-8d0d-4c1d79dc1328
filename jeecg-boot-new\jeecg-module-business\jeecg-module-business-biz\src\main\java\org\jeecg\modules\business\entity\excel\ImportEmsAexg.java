package org.jeecg.modules.business.entity.excel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelVerify;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 账册导入成品excel
 */
@Data
public class ImportEmsAexg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号（报关用）(备案号)
     */
    @Excel(name = "序号")
    @TableField("G_NO")
    private Integer gNo;

    /**
     * 货号(归并后可以没有货号)
     */
    @Excel(name = "料号")
    @ExcelVerify(notNull = true)
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 商品编码(税号)
     */
    @Excel(name = "商品编码")
    @ExcelVerify(notNull = true)
    @TableField("CODET")
    private String codet;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    @ExcelVerify(notNull = true)
    @TableField("G_NAME")
    private String gName;

    /**
     * 规格型号(申报要素)
     */
    @Excel(name = "规格型号")
    @ExcelVerify(notNull = true)
    @TableField("G_MODEL")
    private String gModel;

    /**
     * 计量单位
     */
    @Excel(name = "申报计量单位", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_CODE,CJDW", dicText = "ITEM_NAME")
    @ExcelVerify(notNull = true)
    @TableField("UNIT")
    private String unit;

    /**
     * 法定计量单位
     */
    @Excel(name = "法定计量单位", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_CODE,CJDW", dicText = "ITEM_NAME")
    @ExcelVerify(notNull = true)
    @TableField("UNIT1")
    private String unit1;

    /**
     * 第二法定计量单位
     */
    @Excel(name = "法定第二计量单位", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_CODE,CJDW", dicText = "ITEM_NAME")
    @TableField("UNIT2")
    private String unit2;

    /**
     * 产销国
     */
    @Excel(name = "产销国(地区)", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_KEY,GBDQ", dicText = "ITEM_NAME")
    @TableField("COUNTRY_CODE")
    private String countryCode;


    /**
     * 申报单价
     */
    @Excel(name = "申报单价")
//    @ExcelVerify(regex = IS_NUMBER)
    @TableField("DEC_PRICE")
    private BigDecimal decPrice;

    /**
     * 币制
     */
    @Excel(name = "币制", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_KEY,BZDM", dicText = "ITEM_NAME")
    @TableField("CURR")
    private String curr;


    /**
     * 库存数量
     */
    @Excel(name = "申报数量")
//    @ExcelVerify(regex = IS_NUMBER)
    private BigDecimal qty;


    /**
     * 备注
     */
    @Excel(name = "备注")
    @TableField("NOTE")
    private String note;

    /**
     * 申报总价
     */
    @Excel(name = "申报总价")
    @TableField("APPR_AMT")
    private BigDecimal apprAmt;

    /**
     * 征免方式
     */
    @Excel(name = "征免方式", dictTable = "CUSTOMS_DICT", dicCode = "ITEM_CODE,ZJMSFS", dicText = "ITEM_NAME")
    @TableField("DUTY_MODE")
    private String dutyMode;


    /**
     * 记账清单编号(海关要求回填值(可累计的无意义))
     */
    @Excel(name = "记账清单编号")
    @TableField("BOND_INVT_NO")
    private String bondInvtNo;

    /**
     * 记账清单序号(海关要求回填值(可累计的无意义))
     */
    @Excel(name = "记账清单序号")
    @TableField("BOND_INVT_ITEM")
    private String bondInvtItem;

    /**
     * 企业内部编号(统计需要(可累计的无意义))
     */
    @Excel(name = "企业内部编号")
    @TableField("INTERNAL_NO")
    private String internalNo;

    /**
     * 单耗版本号
     */
    @Excel(name = "单耗版本号")
    @TableField(exist = false)
    private String ucnsverno;


    /**
     * 商品属性
     */
    @Excel(name = "商品属性")
    private String attributes;
    /**
     * 修改标志
     */
    @Excel(name = "修改标志")
    private String modifyFlag;

    /**
     * 海关执行标志
     */
    @Excel(name = "海关执行标志")
    private String customsEnforcementMark;

    /**
     * 单耗质疑标志
     */
    @Excel(name = "单耗质疑标志")
    private String unitConsumptionQueryFlag;

    /**
     * 磋商标志
     */
    @Excel(name = "磋商标志")
    private String negotiationSymbol;

}
