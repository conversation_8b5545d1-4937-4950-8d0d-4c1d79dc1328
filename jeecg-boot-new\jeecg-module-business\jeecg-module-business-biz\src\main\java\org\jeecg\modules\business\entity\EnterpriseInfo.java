package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date: 2022-02-18
 * @Version: V1.0
 */
@Data
@TableName("enterprise_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "enterprise_info对象", description = "企业信息表")
public class EnterpriseInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 公司全称
     */
    @Excel(name = "公司全称", width = 15)
    @ApiModelProperty(value = "公司全称")
    private java.lang.String enterpriseFullName;
    /**
     * 英文名称
     */
    @Excel(name = "英文名称", width = 15)
    @ApiModelProperty(value = "英文名称")
    private java.lang.String englishName;
    /**
     * 注册地址（省市区）
     */
    @Excel(name = "注册地址（省市区）", width = 15)
    @ApiModelProperty(value = "注册地址（省市区）")
    private java.lang.String registeredAddress;
    /**
     * 注册详细地址
     */
    @Excel(name = "注册详细地址", width = 15)
    @ApiModelProperty(value = "注册详细地址")
    private java.lang.String registeredAddressDetail;
    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码", width = 15)
    @ApiModelProperty(value = "统一社会信用代码")
    private java.lang.String unifiedSocialCreditCode;
    /**
     * 企业法人
     */
    @Excel(name = "企业法人", width = 15)
    @ApiModelProperty(value = "企业法人")
    private java.lang.String enterpriseLegalPerson;
    /**
     * 联系人中文姓名
     */
    @Excel(name = "联系人中文姓名", width = 15)
    @ApiModelProperty(value = "联系人中文姓名")
    private java.lang.String cotactChName;
    /**
     * 联系人英文姓名
     */
    @Excel(name = "联系人英文姓名", width = 15)
    @ApiModelProperty(value = "联系人英文姓名")
    private java.lang.String cotactEnName;
    /**
     * 报关代码
     */
    @Excel(name = "报关代码", width = 15)
    @ApiModelProperty(value = "报关代码")
    private java.lang.String customsDeclarationCode;
    /**
     * 网址
     */
    @Excel(name = "网址", width = 15)
    @ApiModelProperty(value = "网址")
    private java.lang.String webSite;
    /**
     * 英文地址
     */
    @Excel(name = "英文地址", width = 15)
    @ApiModelProperty(value = "英文地址")
    private java.lang.String englishAdress;
    /**
     * 公司电话
     */
    @Excel(name = "公司电话", width = 15)
    @ApiModelProperty(value = "公司电话")
    private java.lang.String compayTelphone;
    /**
     * 公司传真
     */
    @Excel(name = "公司传真", width = 15)
    @ApiModelProperty(value = "公司传真")
    private java.lang.String compayFax;
    /**
     * 公司邮箱
     */
    @Excel(name = "公司邮箱", width = 15)
    @ApiModelProperty(value = "公司邮箱")
    private java.lang.String suppliersEmail;
    /**
     * 公司照片
     */
    @Excel(name = "公司照片", width = 15)
    @ApiModelProperty(value = "公司照片")
    private java.lang.String compayPicture;
    /**
     * 公章
     */
    @Excel(name = "公章", width = 15)
    @ApiModelProperty(value = "公章")
    private java.lang.String companyStamp;
    /**
     * 合同章
     */
    @Excel(name = "合同章", width = 15)
    @ApiModelProperty(value = "合同章")
    private java.lang.String contractStamp;
    /**
     * 单据章
     */
    @Excel(name = "单据章", width = 15)
    @ApiModelProperty(value = "单据章")
    private java.lang.String documentStamp;
    /**
     * 采购章
     */
    @Excel(name = "采购章", width = 15)
    @ApiModelProperty(value = "采购章")
    private java.lang.String purchasingStamp;
    /**
     * 财务章
     */
    @Excel(name = "财务章", width = 15)
    @ApiModelProperty(value = "财务章")
    private java.lang.String financialStamp;
    /**
     * 用户名
     */
    @Excel(name = "用户名", width = 15)
    @ApiModelProperty(value = "用户名")
    private java.lang.String sditdsUserloginname;
    /**
     * 密码
     */
    @Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private java.lang.String sditdsUserpassowrd;
    /**
     * 私钥证书
     */
    @Excel(name = "私钥证书", width = 15)
    @ApiModelProperty(value = "私钥证书")
    private java.lang.String privateKeyCertificate;
    /**
     * 报文传输编号
     */
    @Excel(name = "报文传输编号", width = 15)
    @ApiModelProperty(value = "报文传输编号")
    private java.lang.String messageTransmissionNumber;
    /**
     * 删除标记
     */
    @Excel(name = "删除标记", width = 15, dicCode = "del_flag")
    @Dict(dicCode = "del_flag")
    @ApiModelProperty(value = "删除标记")
    private java.lang.Integer delFlag;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
    /**
     * 海关商检代码
     */
    @Excel(name = "海关商检代码", width = 15)
    @ApiModelProperty(value = "海关商检代码")
    private java.lang.String customsInspectionCode;
    /**
     * 采购平台账号
     */
    @ApiModelProperty(value = "采购平台账号")
    private java.lang.String procurementPlatformAccount;
    /**
     * 采购平台密码
     */
    @ApiModelProperty(value = "采购平台密码")
    private java.lang.String procurementPlatformPassword;

    /**
     * 箱单模版
     */
    private String packListTempl;

    /**
     * 发票模版
     */
    private String invoiceTempl;

    /**
     * 企业编码 ********
     */
    private String customerCode;

    private String swid;

    /**
     * 是否三方注册企业
     */
    @TableField("IS_THIRD_PARTS")
    private Boolean isThirdParts;

    @TableField(exist = false)
    private List<EnterpriseCustomsRecord> enterpriseCustomsRecordList;

    /**
     * 外销合同模板
     */
    private String salescontractTempl;
}
