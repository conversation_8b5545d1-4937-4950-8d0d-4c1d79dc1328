package org.jeecg.modules.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.JgVFlyBgDechead;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IJgVFlyBgDecheadService extends IService<JgVFlyBgDechead> {

    IPage<JgVFlyBgDechead> queryPageList(Page<JgVFlyBgDechead> page, String customerName, String starDate, String lastDate);

    IPage<JgVFlyBgDechead> queryPageListCommon(Page<JgVFlyBgDechead> page, String ownerCode, String ownerName, String startDate, String lastDate, String isAll);

    IPage<JgVFlyBgDechead> queryPageListSBCommon(Page<JgVFlyBgDechead> page, String agentCode, String agentName, String startDate, String lastDate, String isAll);
}
