package org.jeecg.modules.business.entity.messages.emlMessages;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * NptsEmlHead
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/6/24 9:51
 * @version 1.0
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class NptsEmlHead {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "EmlNo")
    private String emlNo;

    @XmlElement(name = "EmlType")
    private String emlType;

    @XmlElement(name = "EtpsPreentNo")
    private String etpsPreentNo;

    @XmlElement(name = "DclTypecd")
    private String dclTypecd;

    @XmlElement(name = "MasterCuscd")
    private String masterCuscd;

    @XmlElement(name = "BizopEtpsSccd")
    private String bizopEtpsSccd;

    @XmlElement(name = "BizopEtpsno")
    private String bizopEtpsno;

    @XmlElement(name = "BizopEtpsNm")
    private String bizopEtpsNm;

    @XmlElement(name = "RvsngdEtpsSccd")
    private String rvsngdEtpsSccd;

    @XmlElement(name = "RcvgdEtpsno")
    private String rcvgdEtpsno;

    @XmlElement(name = "RcvgdEtpsNm")
    private String rcvgdEtpsNm;

    @XmlElement(name = "RcvgdEtpsDtcd")
    private String rcvgdEtpsDtcd;

    @XmlElement(name = "DclEtpsTypecd")
    private String dclEtpsTypecd;

    @XmlElement(name = "DclEtpsSccd")
    private String dclEtpsSccd;

    @XmlElement(name = "DclEtpsno")
    private String dclEtpsno;

    @XmlElement(name = "DclEtpsNm")
    private String dclEtpsNm;

    @XmlElement(name = "DclTime")
    private String dclTime;

    @XmlElement(name = "SupvModecd")
    private String supvModecd;

    @XmlElement(name = "ApcretNo")
    private String apcretNo;

    @XmlElement(name = "ImpCtrtNo")
    private String impCtrtNo;

    @XmlElement(name = "ExpCtrtNo")
    private String expCtrtNo;

    @XmlElement(name = "ValidDate")
    private String validDate;

    @XmlElement(name = "ReduNatrcd")
    private String reduNatrcd;

    @XmlElement(name = "ProduceTypecd")
    private String produceTypecd;

    @XmlElement(name = "ImpexpPortcd")
    private String impexpPortcd;

    @XmlElement(name = "UcnsDclSegcd")
    private String ucnsDclSegcd;

    @XmlElement(name = "StndbkBankcd")
    private String stndbkBankcd;

    @XmlElement(name = "ProductRatio")
    private String productRatio;

    @XmlElement(name = "ImpCurrcd")
    private String impCurrcd;

    @XmlElement(name = "ExpCurrcd")
    private String expCurrcd;

    @XmlElement(name = "DclTypeMarkcd")
    private String dclTypeMarkcd;

    @XmlElement(name = "PauseImpexpMarkcd")
    private String pauseImpexpMarkcd;

    @XmlElement(name = "InputEtpsNm")
    private String inputEtpsNm;

    @XmlElement(name = "InputEtpsSccd")
    private String inputEtpsSccd;

    @XmlElement(name = "InputEtpsTypecd")
    private String inputEtpsTypecd;

    @XmlElement(name = "Rmk")
    private String rmk;

    @XmlElement(name = "InputTime")
    private String inputTime;

    @XmlElement(name = "LinkMan")
    private String linkMan;

    @XmlElement(name = "LinkManTel")
    private String linkManTel;

    @XmlElement(name = "Col1")
    private String col1;

    // Getters and Setters
    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getEmlNo() {
        return emlNo;
    }

    public void setEmlNo(String emlNo) {
        this.emlNo = emlNo;
    }

    public String getEmlType() {
        return emlType;
    }

    public void setEmlType(String emlType) {
        this.emlType = emlType;
    }

    public String getEtpsPreentNo() {
        return etpsPreentNo;
    }

    public void setEtpsPreentNo(String etpsPreentNo) {
        this.etpsPreentNo = etpsPreentNo;
    }

    public String getDclTypecd() {
        return dclTypecd;
    }

    public void setDclTypecd(String dclTypecd) {
        this.dclTypecd = dclTypecd;
    }

    public String getMasterCuscd() {
        return masterCuscd;
    }

    public void setMasterCuscd(String masterCuscd) {
        this.masterCuscd = masterCuscd;
    }

    public String getBizopEtpsSccd() {
        return bizopEtpsSccd;
    }

    public void setBizopEtpsSccd(String bizopEtpsSccd) {
        this.bizopEtpsSccd = bizopEtpsSccd;
    }

    public String getBizopEtpsno() {
        return bizopEtpsno;
    }

    public void setBizopEtpsno(String bizopEtpsno) {
        this.bizopEtpsno = bizopEtpsno;
    }

    public String getBizopEtpsNm() {
        return bizopEtpsNm;
    }

    public void setBizopEtpsNm(String bizopEtpsNm) {
        this.bizopEtpsNm = bizopEtpsNm;
    }

    public String getRvsngdEtpsSccd() {
        return rvsngdEtpsSccd;
    }

    public void setRvsngdEtpsSccd(String rvsngdEtpsSccd) {
        this.rvsngdEtpsSccd = rvsngdEtpsSccd;
    }

    public String getRcvgdEtpsno() {
        return rcvgdEtpsno;
    }

    public void setRcvgdEtpsno(String rcvgdEtpsno) {
        this.rcvgdEtpsno = rcvgdEtpsno;
    }

    public String getRcvgdEtpsNm() {
        return rcvgdEtpsNm;
    }

    public void setRcvgdEtpsNm(String rcvgdEtpsNm) {
        this.rcvgdEtpsNm = rcvgdEtpsNm;
    }

    public String getRcvgdEtpsDtcd() {
        return rcvgdEtpsDtcd;
    }

    public void setRcvgdEtpsDtcd(String rcvgdEtpsDtcd) {
        this.rcvgdEtpsDtcd = rcvgdEtpsDtcd;
    }

    public String getDclEtpsTypecd() {
        return dclEtpsTypecd;
    }

    public void setDclEtpsTypecd(String dclEtpsTypecd) {
        this.dclEtpsTypecd = dclEtpsTypecd;
    }

    public String getDclEtpsSccd() {
        return dclEtpsSccd;
    }

    public void setDclEtpsSccd(String dclEtpsSccd) {
        this.dclEtpsSccd = dclEtpsSccd;
    }

    public String getDclEtpsno() {
        return dclEtpsno;
    }

    public void setDclEtpsno(String dclEtpsno) {
        this.dclEtpsno = dclEtpsno;
    }

    public String getDclEtpsNm() {
        return dclEtpsNm;
    }

    public void setDclEtpsNm(String dclEtpsNm) {
        this.dclEtpsNm = dclEtpsNm;
    }

    public String getDclTime() {
        return dclTime;
    }

    public void setDclTime(String dclTime) {
        this.dclTime = dclTime;
    }

    public String getSupvModecd() {
        return supvModecd;
    }

    public void setSupvModecd(String supvModecd) {
        this.supvModecd = supvModecd;
    }

    public String getApcretNo() {
        return apcretNo;
    }

    public void setApcretNo(String apcretNo) {
        this.apcretNo = apcretNo;
    }

    public String getImpCtrtNo() {
        return impCtrtNo;
    }

    public void setImpCtrtNo(String impCtrtNo) {
        this.impCtrtNo = impCtrtNo;
    }

    public String getExpCtrtNo() {
        return expCtrtNo;
    }

    public void setExpCtrtNo(String expCtrtNo) {
        this.expCtrtNo = expCtrtNo;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getReduNatrcd() {
        return reduNatrcd;
    }

    public void setReduNatrcd(String reduNatrcd) {
        this.reduNatrcd = reduNatrcd;
    }

    public String getProduceTypecd() {
        return produceTypecd;
    }

    public void setProduceTypecd(String produceTypecd) {
        this.produceTypecd = produceTypecd;
    }

    public String getImpexpPortcd() {
        return impexpPortcd;
    }

    public void setImpexpPortcd(String impexpPortcd) {
        this.impexpPortcd = impexpPortcd;
    }

    public String getUcnsDclSegcd() {
        return ucnsDclSegcd;
    }

    public void setUcnsDclSegcd(String ucnsDclSegcd) {
        this.ucnsDclSegcd = ucnsDclSegcd;
    }

    public String getStndbkBankcd() {
        return stndbkBankcd;
    }

    public void setStndbkBankcd(String stndbkBankcd) {
        this.stndbkBankcd = stndbkBankcd;
    }

    public String getProductRatio() {
        return productRatio;
    }

    public void setProductRatio(String productRatio) {
        this.productRatio = productRatio;
    }

    public String getImpCurrcd() {
        return impCurrcd;
    }

    public void setImpCurrcd(String impCurrcd) {
        this.impCurrcd = impCurrcd;
    }

    public String getExpCurrcd() {
        return expCurrcd;
    }

    public void setExpCurrcd(String expCurrcd) {
        this.expCurrcd = expCurrcd;
    }

    public String getDclTypeMarkcd() {
        return dclTypeMarkcd;
    }

    public void setDclTypeMarkcd(String dclTypeMarkcd) {
        this.dclTypeMarkcd = dclTypeMarkcd;
    }

    public String getPauseImpexpMarkcd() {
        return pauseImpexpMarkcd;
    }

    public void setPauseImpexpMarkcd(String pauseImpexpMarkcd) {
        this.pauseImpexpMarkcd = pauseImpexpMarkcd;
    }

    public String getInputEtpsNm() {
        return inputEtpsNm;
    }

    public void setInputEtpsNm(String inputEtpsNm) {
        this.inputEtpsNm = inputEtpsNm;
    }

    public String getInputEtpsSccd() {
        return inputEtpsSccd;
    }

    public void setInputEtpsSccd(String inputEtpsSccd) {
        this.inputEtpsSccd = inputEtpsSccd;
    }

    public String getInputEtpsTypecd() {
        return inputEtpsTypecd;
    }

    public void setInputEtpsTypecd(String inputEtpsTypecd) {
        this.inputEtpsTypecd = inputEtpsTypecd;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getInputTime() {
        return inputTime;
    }

    public void setInputTime(String inputTime) {
        this.inputTime = inputTime;
    }

    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    public String getLinkManTel() {
        return linkManTel;
    }

    public void setLinkManTel(String linkManTel) {
        this.linkManTel = linkManTel;
    }

    public String getCol1() {
        return col1;
    }

    public void setCol1(String col1) {
        this.col1 = col1;
    }
}
