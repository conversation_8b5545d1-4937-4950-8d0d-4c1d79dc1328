<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.SysConfigMapper">

    <select id="getConfigByConfigKey" resultType="org.jeecg.modules.business.entity.SysConfig">
        SELECT
            *
        FROM
            `sys_config`
        WHERE
            CONFIG_KEY = #{configKey}
            LIMIT 1
    </select>
</mapper>
