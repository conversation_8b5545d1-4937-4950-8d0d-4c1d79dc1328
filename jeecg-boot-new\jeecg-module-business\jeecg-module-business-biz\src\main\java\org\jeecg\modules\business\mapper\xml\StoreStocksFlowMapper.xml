<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StoreStocksFlowMapper">

    <select id="listGoodsFlow" resultType="org.jeecg.modules.business.entity.StoreStocksFlow">
        SELECT
            *,
            b.BOND_INVT_NO,
            IFNULL(a.STOCK_VAR, 0) STOCK_VAR,
            IFNULL(a.OCCUPY_VAR, 0) OCCUPY_VAR
        FROM
            `store_stocks_flow` a
            LEFT JOIN storage_detail b ON a.STORAGE_DETAIL_ID = b.ID
        <where>
            a.STORE_CODE = #{storeStocksDTO.storeCode}
            AND a.CUSTOMER = #{storeStocksDTO.customer}
            AND a.COP_GNO = #{storeStocksDTO.copGno}
            AND a.ITEM_NUMBER = #{storeStocksDTO.itemNumber}
        <choose>
            <when test="storeStocksDTO.batchNo != null and storeStocksDTO.batchNo != ''">
                AND a.BATCH_NO = #{storeStocksDTO.batchNo}
            </when>
            <otherwise>
                AND (a.BATCH_NO IS NULL OR a.BATCH_NO = '')
            </otherwise>
        </choose>
        <choose>
            <when test="storeStocksDTO.spaceName != null and storeStocksDTO.spaceName != ''">
                AND a.SPACE_NAME = #{storeStocksDTO.spaceName}
            </when>
            <otherwise>
                AND (a.SPACE_NAME IS NULL OR a.SPACE_NAME = '')
            </otherwise>
        </choose>
            AND a.TENANT_ID = #{storeStocksDTO.tenantId}
        </where>
        ORDER BY
            a.CREATE_DATE DESC
    </select>

    <select id="selectFlowsForTask" resultType="org.jeecg.modules.business.entity.StoreStocksFlow">
        SELECT
            *
        FROM
            `store_stocks_flow`
        WHERE
            STORE_CODE = #{storeCode}
          AND CUSTOMER = #{customer}
          AND TENANT_ID = #{tenantId}
          AND DATE_FORMAT( EXEC_DATE, '%Y-%m-%d' ) = #{execDate}
    </select>
    <select id="selectFlowsEveryGoodsForTask" resultType="org.jeecg.modules.business.entity.StoreStocksFlow">
        SELECT
            *
        FROM
            `store_stocks_flow`
        WHERE
            CONCAT( STORE_CODE, CUSTOMER, IFNULL( BATCH_NO, 'null' ), COP_GNO, TENANT_ID, QUNIT ) = #{key}
            AND DATE_FORMAT( EXEC_DATE, '%Y-%m-%d' ) &lt;= #{execDate}
        ORDER BY
            EXEC_DATE
    </select>
</mapper>
