package org.jeecg.modules.business.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 境外付款方信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-18
 * @Version: V1.0
 */
@Data
@TableName("overseas_payer_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="overseas_payer_info对象", description="境外付款方信息表")
public class OverseasPayerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tenantId;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**境外汇款人名称*/
	@Excel(name = "境外汇款人名称", width = 15)
    @ApiModelProperty(value = "境外汇款人名称")
    private java.lang.String overseasPayerName;
	/**国别地区*/
	@Excel(name = "国别地区", width = 15, dictTable = "erp_countries", dicText = "name", dicCode = "code")
	@Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "国别地区")
    private java.lang.String countryRegion;
	/**有效/无效标记*/
	@Excel(name = "有效/无效标记", width = 15, dicCode = "is_effective_flag")
	@Dict(dicCode = "is_effective_flag")
    @ApiModelProperty(value = "有效/无效标记")
    private java.lang.Integer isEffectiveFlag;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @ApiModelProperty(value = "删除标记")
    private java.lang.Integer delFlag;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
    /**公司电话*/
    @Excel(name = "公司电话", width = 15)
    @ApiModelProperty(value = "公司电话")
    private java.lang.String compayTelphone;
    /**公司传真*/
    @Excel(name = "公司传真", width = 15)
    @ApiModelProperty(value = "公司传真")
    private java.lang.String compayFax;
    /**邮编*/
    @Excel(name = "邮编", width = 15)
    @ApiModelProperty(value = "邮编")
    private java.lang.String postNumber;
    /**供应商详细地址*/
    @Excel(name = "客户详细地址", width = 15)
    @ApiModelProperty(value = "客户详细地址")
    private java.lang.String overseasPayerAddressDetail;
    /**供应商邮箱*/
    @Excel(name = "客户邮箱", width = 15)
    @ApiModelProperty(value = "客户邮箱")
    private java.lang.String overseasPayerEmail;
    /**
     *  公章信息地址
     */
    private String officialSeal;
}
