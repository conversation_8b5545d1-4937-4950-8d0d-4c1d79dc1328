package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 出入库流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Data
@Accessors(chain = true)
@TableName("store_stocks_flow")
public class StoreStocksFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 出入库类型 I入库 E出库
     */
    @TableField("IE_FLAG")
    private String ieFlag;

    /**
     * 操作类型
     */
    @Dict(dicCode = "BSCKANDSTORE_TYPE")
    @TableField("OPT_TYPE")
    private Integer optType;

    /**
     * 仓库编码
     */
    @TableField("STORE_CODE")
    private String storeCode;

    /**
     * 货主
     */
    @TableField("CUSTOMER")
    private String customer;

    /**
     * 项号
     */
    @TableField("ITEM_NUMBER")
    private String itemNumber;

    /**
     * 批次号/备案号/备案项号
     */
    @TableField("BATCH_NO")
    private String batchNo;

    /**
     * 物料号
     */
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 库区编码20240822
     */
    @TableField("AREA_CODE")
    private String areaCode;

    /**
     * 库区名称20240822
     */
    @TableField("AREA_NAME")
    private String areaName;

    /**
     * 关联储位编码
     */
    @TableField("SPACE_CODE")
    private String spaceCode;

    /**
     * 储位名称
     */
    @TableField("SPACE_NAME")
    private String spaceName;

    /**
     * 单位
     */
    @TableField("QUNIT")
    private String qunit;

    /**
     * 出入库单号
     */
    @TableField("STORAGE_NO")
    private String storageNo;

    /**
     * 出入库单明细ID
     */
    @TableField("STORAGE_DETAIL_ID")
    private String storageDetailId;

    /**
     * 库存ID
     */
    @TableField("STORE_STOCKS_ID")
    private String storeStocksId;

    /**
     * 库存变化值（操作变化数量，值不变为0, 增加为正值减少为负值）
     */
    @TableField("STOCK_VAR")
    private BigDecimal stockVar;

    /**
     * 占用变化值（操作变化数量，值不变为0, 增加为正值减少为负值）
     */
    @TableField("OCCUPY_VAR")
    private BigDecimal occupyVar;

    /**
     * 执行人
     */
    @TableField("EXEC_BY")
    private String execBy;

    /**
     * 执行时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("EXEC_DATE")
    private Date execDate;

    /**
     * 状态
     */
    @TableField("STATUS")
    private String status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 最后更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 最后更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    /**
     * 清单编号 （返填 - 海关审批通过后系统自动返填）
     */
    @TableField(exist = false)
    private String bondInvtNo;
}
