package org.jeecg.modules.business.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.idempotent.annotation.Idempotent;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.EmsQueryDto;
import org.jeecg.modules.business.entity.excel.ImportEmsAexg;
import org.jeecg.modules.business.entity.excel.ImportEmsAimg;
import org.jeecg.modules.business.entity.excel.ImportEmsCm;
import org.jeecg.modules.business.entity.excel.ImportEmsHead;
import org.jeecg.modules.business.entity.paramVo.InventoryFlowsVO;
import org.jeecg.modules.business.entity.paramVo.ReportStatisticsExportVO;
import org.jeecg.modules.business.entity.paramVo.ReportStatisticsVO;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.excel.EmsExcelImportResult;
import org.jeecg.modules.business.util.excel.EmsExcelImportUtil;
import org.jeecg.modules.business.util.excel.ExcelExportStylerBorderImpl;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.result.ExcelImportResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.apache.commons.lang.StringUtils.isBlank;
import static org.apache.commons.lang.StringUtils.isNotBlank;
import static org.jeecg.modules.business.util.exception.ExceptionUtil.ResExHandle;

/**
 * <p>
 * 手账册表头 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@RestController
@Slf4j
@RequestMapping("/business/ems")
public class PtsEmsHeadController {
    private static final Map<String, Class<?>> SHEET_NAME_TO_POJO_CLASS_MAP = new HashMap<>();
    @Autowired
    private IPtsEmsHeadService emsHeadService;
    @Autowired
    private IPtsEmsAimgService emsAimgService;
    @Autowired
    private IPtsEmsAexgService emsAexgService;
    @Autowired
    private IPtsEmsCmService emsCmService;
    @Autowired
    private IPtsEmsDetailService emsDetailService;
    @Autowired
    private IPtsEmsFlowsService emsFlowsService;
    @Autowired
    private IEmsStocksFlowService emsStocksFlowService;
    @Autowired
    private INemsInvtHeadService nemsInvtHeadService;
    @Autowired
    private INemsInvtListService nemsInvtListService;
    @Autowired
    private PtsEmsHeadMapper ptsEmsHeadMapper;
    @Autowired
    private ReportHeadMapper reportHeadMapper;
    @Autowired
    private IReportListService reportListService;
    @Autowired
    private ProductMergeMapper productMergeMapper;
    @Autowired
    private NemsInvtListMapper nemsInvtListMapper;
    @Autowired
    private PtsEmsAuditRecordsMapper ptsEmsAuditRecordsMapper;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    static {
        initSheetNameToPojoClassMap();
    }
    /**
     * 初始化存储sheet名称对应的pojo类的Map集合
     */
    private static void initSheetNameToPojoClassMap() {
        SHEET_NAME_TO_POJO_CLASS_MAP.put("表头", ImportEmsHead.class);
        SHEET_NAME_TO_POJO_CLASS_MAP.put("表体", ImportEmsAimg.class);
        SHEET_NAME_TO_POJO_CLASS_MAP.put("料件", ImportEmsAimg.class);
        SHEET_NAME_TO_POJO_CLASS_MAP.put("成品", ImportEmsAexg.class);
        SHEET_NAME_TO_POJO_CLASS_MAP.put("单耗", ImportEmsCm.class);
        SHEET_NAME_TO_POJO_CLASS_MAP.put("单损耗", ImportEmsCm.class);
    }

    /**
     * 账册列表查询
     *
     * @param ptsEmsHead
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "账册-分页列表查询", notes = "账册-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(PtsEmsHead ptsEmsHead,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page<PtsEmsHead> page = new Page<>(pageNo, pageSize);
        IPage<PtsEmsHead> pageList = emsHeadService.queryPageList(page, ptsEmsHead);
        return Result.ok(pageList);
    }

    /**
     * 账册列表查询
     *
     * @param ptsEmsHead
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "账册-分页列表查询", notes = "账册-分页列表查询")
    @GetMapping(value = "/listForReport")
    public Result<?> listForReport(PtsEmsHead ptsEmsHead,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page<PtsEmsHead> page = new Page<>(pageNo, pageSize);
        IPage<PtsEmsHead> pageList = emsHeadService.queryPageList(page, ptsEmsHead);
        if (isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(item -> {
                List<NemsInvtHead> nemsInvtHeadList = nemsInvtHeadService.list(new LambdaQueryWrapper<NemsInvtHead>()
                        .eq(NemsInvtHead::getPutrecNo, item.getEmsNo())
                        .eq(NemsInvtHead::getImpexpMarkcd, "I")
                        .eq(NemsInvtHead::getVrfdedMarkcd,"2")
                        .isNotNull(NemsInvtHead::getInvtDclTime));
                item.setTotalInList(0);
                if (isNotEmpty(nemsInvtHeadList)) {
                    // 一次遍历获取最小和最大值
                    Date[] dateRange = getMinMaxDates(nemsInvtHeadList, NemsInvtHead::getInvtDclTime);
                    // 料件首次进口日期
                    String inDateAimg1 = dateRange[0] != null ? DateUtil.format(dateRange[0], DatePattern.NORM_DATE_PATTERN) : null;
                    item.setInDateAimg1(inDateAimg1);
                    // 料件末次进口日期
                    String inDateAimg2 = dateRange[1] != null ? DateUtil.format(dateRange[1], DatePattern.NORM_DATE_PATTERN) : null;
                    item.setInDateAimg2(inDateAimg2);
                    item.setTotalInList(nemsInvtHeadList.size());
                    List<NemsInvtList> nemsInvtListList = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
//                            .eq(NemsInvtList::getDclCurrcd, "502")
                            .in(NemsInvtList::getInvId, nemsInvtHeadList.stream().map(NemsInvtHead::getId).collect(Collectors.toList())));
                    if (isNotEmpty(nemsInvtListList)) {
                        BigDecimal actualInUsd = nemsInvtListList.stream().map(NemsInvtList::getUsdstatTotalamt).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        item.setActualInUsd(actualInUsd.toPlainString());
                    }
                }
                List<NemsInvtHead> nemsInvtHeadListE = nemsInvtHeadService.list(new LambdaQueryWrapper<NemsInvtHead>()
                        .eq(NemsInvtHead::getPutrecNo, item.getEmsNo())
                        .eq(NemsInvtHead::getImpexpMarkcd, "E")
                                .eq(NemsInvtHead::getVrfdedMarkcd,"2")
                        .isNotNull(NemsInvtHead::getInvtDclTime));
                item.setTotalOutList(0);
                if (isNotEmpty(nemsInvtHeadListE)) {
                    // 一次遍历获取最小和最大值
                    Date[] dateRange = getMinMaxDates(nemsInvtHeadListE, NemsInvtHead::getInvtDclTime);
                    // 料件首次进口日期
                    String outDateAexg1 = dateRange[0] != null ? DateUtil.format(dateRange[0], DatePattern.NORM_DATE_PATTERN) : null;
                    item.setOutDateAexg1(outDateAexg1);
                    // 料件末次进口日期
                    String outDateAexg2 = dateRange[1] != null ? DateUtil.format(dateRange[1], DatePattern.NORM_DATE_PATTERN) : null;
                    item.setOutDateAexg2(outDateAexg2);
                    item.setTotalOutList(nemsInvtHeadListE.size());
                    List<NemsInvtList> nemsInvtListList = nemsInvtListService.list(new LambdaQueryWrapper<NemsInvtList>()
//                            .eq(NemsInvtList::getDclCurrcd, "502")
                            .in(NemsInvtList::getInvId, nemsInvtHeadListE.stream().map(NemsInvtHead::getId).collect(Collectors.toList())));
                    if (isNotEmpty(nemsInvtListList)) {
                        BigDecimal actualOutUsd = nemsInvtListList.stream().map(NemsInvtList::getUsdstatTotalamt).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        item.setActualOutUsd(actualOutUsd.toPlainString());
                    }
                }
           });
        }
        return Result.ok(pageList);
    }

    /**
     * 获取列表中日期字段的最小和最大值
     *
     * @param list 包含日期对象的列表
     * @param dateGetter 用于从对象中获取日期的函数式接口
     * @return 包含最小和最大日期的数组，如果列表为空或所有日期为null，则最小和最大值均为null
     */
    private static Date[] getMinMaxDates(List<NemsInvtHead> list, Function<NemsInvtHead, Date> dateGetter) {
        Comparator<Date> comparator = Comparator.nullsLast(Date::compareTo);
        Optional<Date> minDate = list.stream()
                .map(dateGetter)
                .filter(Objects::nonNull)
                .min(comparator);
        Optional<Date> maxDate = list.stream()
                .map(dateGetter)
                .filter(Objects::nonNull)
                .max(comparator);
        return new Date[]{minDate.orElse(null), maxDate.orElse(null)};
    }

    /**
     * 根据ID查询账册信息
     *
     * @param id 要查询的仓库ID
     * @return 查询结果
     */
    @ApiOperation(value = "查询账册信息根据ID", notes = "查询账册信息根据ID")
    @GetMapping(value = "/getEmsHeadById")
    public Result<?> getEmsHeadById(@RequestParam("id") String id) {
        return emsHeadService.getEmsHeadById(id);
    }

    /**
     * 保存账册
     * 如果重复请求存在，将会被忽略
     *
     * @param ptsEmsHead 要保存的账册信息
     * @return 保存结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存账册")
    @ApiOperation(value = "保存账册", notes = "保存账册")
    @PostMapping(value = "/saveEmsHead")
    public Result<?> saveEmsHead(@RequestBody PtsEmsHead ptsEmsHead) {
        return emsHeadService.saveEmsHead(ptsEmsHead);
    }

    /**
     * 删除账册
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "删除账册")
    @ApiOperation(value = "删除账册", notes = "删除账册")
    @RequestMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam("ids") String ids) {
        return emsHeadService.deleteBatch(ids);
    }

    /**
     * 账册表体列表查询
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "账册表体-分页列表查询", notes = "账册表体-分页列表查询")
    @GetMapping(value = "/listEmsDetail")
    public Result<?> listEmsDetail(EmsQueryDto emsQueryDto,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage pageList = emsHeadService.listEmsDetail(page, emsQueryDto);
        return Result.ok(pageList);
    }
    /**
     * 账册表体列表查询-手册报表统计
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "账册表体-分页列表查询-手册报表统计", notes = "账册表体-分页列表查询-手册报表统计")
    @GetMapping(value = "/listEmsDetailByReport")
    public Result<?> listEmsDetailByReport(EmsQueryDto emsQueryDto,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage pageList = emsHeadService.listEmsDetailByReport(page, emsQueryDto);
        return Result.ok(pageList);
    }
    /**
     * 获取EMS手册报表统计-核销平衡
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "获取EMS手册报表统计-核销平衡", notes = "获取EMS手册报表统计-核销平衡")
    @GetMapping(value = "/listEmsDetailWriteOffBalance")
    public Result<?> listEmsDetailWriteOffBalance(EmsQueryDto emsQueryDto,
                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                           HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage pageList = emsHeadService.listEmsDetailWriteOffBalance(page, emsQueryDto);
        return Result.ok(pageList);
    }

    /**
     * 获取EMS手册报表统计-进口保税科件重量统计/出口保税成品重量统计
     */
    @ApiOperation(value = "获取EMS手册报表统计-进口保税科件重量统计/出口保税成品重量统计", notes = "获取EMS手册报表统计-进口保税科件重量统计/出口保税成品重量统计")
    @GetMapping(value = "/listEmsDetailWeightStatistics")
    public Result<?> listEmsDetailWeightStatistics(EmsQueryDto emsQueryDto) {
        emsQueryDto.setTenantId(TenantContext.getTenant());
        return emsHeadService.listEmsDetailWeightStatistics(emsQueryDto);
    }

    /**
     * 查询账册的审核记录
     *
     */
    @ApiOperation(value = "查询账册的审核记录", notes = "查询账册的审核记录")
    @GetMapping(value = "/listExamineRecords")
    public Result<?> listExamineRecords(EmsQueryDto emsQueryDto,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<PtsEmsAuditRecords> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PtsEmsAuditRecords::getTenantId,TenantContext.getTenant());
        lambdaQueryWrapper.eq(PtsEmsAuditRecords::getPtsEmsHeadId,emsQueryDto.getEmsId());
        lambdaQueryWrapper.orderByDesc(PtsEmsAuditRecords::getAuditDate);
        IPage pageList = ptsEmsAuditRecordsMapper.selectPage(page, lambdaQueryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 保存料件
     * 如果重复请求存在，将会被忽略
     *
     * @param ptsEmsAimg 要保存的料件信息
     * @return 保存结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存料件")
    @ApiOperation(value = "保存料件", notes = "保存料件")
    @PostMapping(value = "/saveEmsAimg")
    public Result<?> saveEmsAimg(@RequestBody PtsEmsAimg ptsEmsAimg) {
        return emsAimgService.saveEmsAimg(ptsEmsAimg);
    }

    /**
     * 保存成品
     * 如果重复请求存在，将会被忽略
     *
     * @param ptsEmsAexg 要保存的成品信息
     * @return 保存结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存成品")
    @ApiOperation(value = "保存成品", notes = "保存成品")
    @PostMapping(value = "/saveEmsAexg")
    public Result<?> saveEmsAexg(@RequestBody PtsEmsAexg ptsEmsAexg) {
        return emsAexgService.saveEmsAexg(ptsEmsAexg);
    }

    /**
     * 保存单损耗
     * 如果重复请求存在，将会被忽略
     *
     * @param ptsEmsCm 要保存的单损耗信息
     * @return 保存结果
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "保存单损耗")
    @ApiOperation(value = "保存单损耗", notes = "保存单损耗")
    @PostMapping(value = "/saveEmsCm")
    public Result<?> saveEmsCm(@RequestBody PtsEmsCm ptsEmsCm) {
        return emsCmService.saveEmsCm(ptsEmsCm);
    }

    /**
     * 新增流水明细
     *
     * @param flowId 账册流水ID
     * @param actual 实际的操作流水ID（冲正用）
     * @param type   数据类型（单独导入和单独修改表体用）
     * @return java.util.List<com.yorma.ems.entity.PtsEmsDetail>
     * @apiNote <pre>
     *   新增流水明细
     * </pre>
     * <AUTHOR> 2021/12/9 14:33
     * @version 1.0
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @AutoLog(value = "新增流水明细")
    @ApiOperation(value = "新增流水明细", notes = "新增流水明细")
    @PostMapping(value = "/addEmsDetails")
    public Result<?> addEmsDetails(@RequestParam("flowId") String flowId,
                                   @RequestParam("actual") String actual,
                                   @RequestParam("type") String type) {
        return emsDetailService.addEmsDetails(flowId, actual, type);
    }

    /**
     * 根据ID查询料件信息
     *
     * @param id 要查询的料件信息
     * @return 查询结果
     */
    @ApiOperation(value = "查询料件信息根据ID", notes = "查询料件信息根据ID")
    @GetMapping(value = "/getEmsAimgById")
    public Result<?> getEmsAimgById(@RequestParam("id") String id) {
        return emsAimgService.getEmsAimgById(id);
    }

    /**
     * 根据ID查询成品信息
     *
     * @param id 要查询的成品信息
     * @return 查询结果
     */
    @ApiOperation(value = "查询成品信息根据ID", notes = "查询成品信息根据ID")
    @GetMapping(value = "/getEmsAexgById")
    public Result<?> getEmsAexgById(@RequestParam("id") String id) {
        return emsAexgService.getEmsAexgById(id);
    }

    /**
     * 根据ID查询单损耗信息
     *
     * @param id 要查询的单损耗信息
     * @return 查询结果
     */
    @ApiOperation(value = "根据ID查询单损耗信息", notes = "根据ID查询单损耗信息")
    @GetMapping(value = "/getEmsCmById")
    public Result<?> getEmsCmById(@RequestParam("id") String id) {
        return emsCmService.getEmsCmById(id);
    }

    /**
     * 导出申报记录Excel
     *
     * @param request
     * @param
     */
    @RequestMapping(value = "/exportRecord")
    public void exportRecord(HttpServletRequest request, HttpServletResponse response,
                                     @RequestParam("putrecNo") String putrecNo,
                                     @RequestParam("gNos") String gNos,
                                     @RequestParam("type") String type) {
        emsHeadService.exportRecord(request, response, putrecNo, gNos, type);
    }

    /**
     * 导出Excel
     *
     * @param request
     * @param
     */
    @RequestMapping(value = "/exportAimgByFieldsBatch")
    public void exportAimgByFieldsBatch(EmsQueryDto emsQueryDto,
                                        HttpServletRequest request, HttpServletResponse response) {
        emsHeadService.exportAimgByFieldsBatch(emsQueryDto, request, response);
    }

    /**
     * 删除账册表体
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "删除账册表体")
    @ApiOperation(value = "删除账册表体", notes = "删除账册表体")
    @RequestMapping(value = "/deleteDetailBatch")
    public Result<?> deleteDetailBatch(@RequestParam("ids") String ids,
                                       @RequestParam("type") String type) {
        return emsHeadService.deleteDetailBatch(ids, type);
    }

    /**
     * 物流账册检测申报库存数量与核注单是否一致
     * @apiNote
     * <pre>
     *   物流账册检测申报库存数量与核注单是否一致
     * </pre>
     *
     * @param emsNo, gNos
     * @return com.yorma.entity.YmMsg<java.lang.String>
     *
     * <AUTHOR> 2021/12/9 14:33
     * @version 1.0
     */
    @ApiOperation(value = "库存检测", notes = "库存检测")
    @GetMapping(value = "/checkInventory")
    public Result<?> checkInventory(@RequestParam("emsNo") String emsNo,
                                    @RequestParam(value = "gNos", required = false) String gNos) {
        return emsHeadService.checkInventory(emsNo, gNos);
    }

    /**
     * 料件展期
     *
     * @param ids
     * @return com.yorma.entity.YmMsg<java.lang.String>
     * @apiNote <pre>
     *   料件展期
     * </pre>
     * <AUTHOR> 2022/3/2 18:44
     * @version 1.0
     */
    @ApiOperation(value = "料件展期", notes = "料件展期")
    @RequestMapping(value = "/changeRollover")
    public Result<?> changeRollover(@RequestParam("ids") String ids) {
        return emsAimgService.changeRollover(ids);
    }

    /**
     * 汇总统计
     *
     * @param emsNo
     * @return com.yorma.entity.YmMsg<java.lang.String>
     * @apiNote <pre>
     *   料件展期
     * </pre>
     * <AUTHOR> 2022/3/2 18:44
     * @version 1.0
     */
    @ApiOperation(value = "汇总统计", notes = "汇总统计")
    @RequestMapping(value = "/emsStatistics")
    public Result<?> emsStatistics(@RequestParam("emsNo") String emsNo) {
        return emsHeadService.emsStatistics(emsNo);
    }

    /**
     * 根据手账册号、核注单企业内部编号 查询操作流水
     *
     * @param emsNo
     * @param invtList
     * @return
     */
    @ApiOperation(value = "根据账册号核注单流水号查询操作流水", notes = "根据账册号核注单流水号查询操作流水")
    @RequestMapping(value = "/listFlowsByEmsNoAndInvtIds")
    public Result<?> listFlowsByEmsNoAndInvtIds(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                @RequestParam("emsNo") String emsNo,
                                                @RequestParam(value = "goodsType", required = false) String goodsType,
                                                @RequestParam("invtList") String invtList) {
        return emsFlowsService.listFlowsByEmsNoAndInvtIds(pageNo, pageSize, emsNo, goodsType, invtList);
    }

    /**
     * 根据手账册号、备案序号 查询操作流水
     *
     * @param emsNo
     * @param gNoList
     * @return
     */
    @ApiOperation(value = "根据账册号备案序号查询操作流水", notes = "根据账册号备案序号查询操作流水")
    @RequestMapping(value = "/listFlowsByEmsNoAndGNos")
    public Result<?> listFlowsByEmsNoAndGNos(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam("emsNo") String emsNo,
                                             @RequestParam(value = "goodsType", required = false) String goodsType,
                                             @RequestParam("gNoList") String gNoList) {
        return emsFlowsService.listFlowsByEmsNoAndGNos(pageNo, pageSize, emsNo, goodsType, gNoList);
    }

    /**
     * 根据账册ID和成品序号查询可用成品数量
     *
     * @return
     */
    @ApiOperation(value = "根据账册ID和成品序号查询可用成品数量", notes = "根据账册ID和成品序号查询可用成品数量")
    @RequestMapping(value = "/getUsableAexg")
    public Result<?> getUsableAexg(@RequestParam("emsId") String emsId,
                                   @RequestParam("gNo") String gNo) {
        return emsHeadService.getUsableAexg(emsId, gNo);
    }

    /**
     * 查询仓库代码是否已被绑定
     *
     * @param ownerCode 仓库代码
     * @return 查询结果
     */
    @ApiOperation(value = "查询仓库代码是否已被绑定", notes = "查询仓库代码是否已被绑定")
    @GetMapping(value = "/checkOwnerCode")
    public Result<?> checkOwnerCode(@RequestParam("ownerCode") String ownerCode,
                                    @RequestParam(value = "id", required = false) String id) {
        return emsHeadService.checkOwnerCode(ownerCode, id);
    }

    /**
     * 根据仓库代码查询账册信息
     *
     * @param storeCode 仓库代码
     * @return 查询结果
     */
    @ApiOperation(value = "根据仓库代码查询账册信息", notes = "根据仓库代码查询账册信息")
    @GetMapping(value = "/getEmsHeadByStoreCode")
    public Result<?> getEmsHeadByStoreCode(@RequestParam("storeCode") String storeCode) {
        return emsHeadService.getEmsHeadByStoreCode(storeCode);
    }

    /**
     * 账册库存列表查询
     *
     * @param inventoryFlowsVO
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "账册库存列表查询-分页列表查询", notes = "账册库存列表查询-分页列表查询")
    @GetMapping(value = "/listInventoryFlows")
    public Result<?> listInventoryFlows(InventoryFlowsVO inventoryFlowsVO,
                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<InventoryFlowsVO> page = new Page<>(pageNo, pageSize);
        IPage<InventoryFlowsVO> pageList = emsHeadService.listInventoryFlows(page, inventoryFlowsVO);
        return Result.ok(pageList);
    }

    /**
     * 仓库商品库存流水列表查询
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "仓库商品库存流水-分页列表查询", notes = "仓库商品库存流水-分页列表查询")
    @GetMapping(value = "/listGoodsFlow")
    public Result<?> listGoodsFlow(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam("emsNo") String emsNo,
                                   @RequestParam("gNo") String gNo,
                                   @RequestParam(value = "copGno", required = false) String copGno) {
        Page<EmsStocksFlow> page = new Page<>(pageNo, pageSize);
        IPage<EmsStocksFlow> pageList = emsStocksFlowService.listGoodsFlow(page, emsNo, gNo, copGno);
        return Result.OK(pageList);
    }

    /**
     * 保税预警列表
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "保税预警列表-分页列表查询", notes = "保税预警列表-分页列表查询")
    @GetMapping(value = "/listBondedWarning")
    public Result<?> listBondedWarning(EmsQueryDto emsQueryDto,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<PtsEmsAimg> page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage<PtsEmsAimg> pageList = emsAimgService.listBondedWarning(page, emsQueryDto);
        return Result.ok(pageList);
    }

    /**
     * 账册发送报文
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/1/20 15:16
     */
    @Idempotent(timeout = 3, message = "存在重复请求，已忽略")
    @ApiOperation(value = "账册发送报文", notes = "账册发送报文")
    @RequestMapping(value = "/handlePush")
    public Result<?> handlePush(@RequestParam("ids") String ids,@RequestParam("type") String type,
                                @RequestParam("flag") String flag) {
        return emsHeadService.handlePush(ids,type,flag);
    }

    /**
     * 根据备案序号或者商品料号查询详情
     * @param type
     * @param emsNo
     * @param accurateGNo
     * @param gdsMtno
     * @return
     */
//    @GetMapping("/listEmsDetail")
//    public Result<?> listEmsDetail(@RequestParam(name = "type") String type,
//                                       @RequestParam(name = "emsNo") String emsNo,
//                                       @RequestParam(name = "accurateGNo") String accurateGNo,
//                                       @RequestParam(name = "gdsMtno") String gdsMtno) {
////       if("0".equals(type)){
////           List<PtsEmsAimg> ptsEmsAimgs = ptsEmsHeadMapper.listEmsDetailAimg(emsNo, accurateGNo, gdsMtno);
////           return Result.ok(ptsEmsAimgs);
////       }else {
////           List<PtsEmsAexg> ptsEmsAexgs = ptsEmsHeadMapper.listEmsDetailAexg(emsNo, accurateGNo, gdsMtno);
////           return Result.ok(ptsEmsAexgs);
////
////       }
//        return null;
//
//    }

    /**
     * xxx
     */
    @GetMapping(value = "/xxx")
    public Result<?> xxx(@RequestParam("emsNo") String emsNo,
                         @RequestParam(value = "gNo", required = false) String gNo) {
        return emsHeadService.xxx(emsNo, gNo);
    }


    /**
     * 报核管理列表
     *
     * @param ptsEmsHead
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "报核管理列表", notes = "报核管理列表")
    @GetMapping(value = "/reportHeadList")
    public Result<?> queryPageList(ReportHead reportHead,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<ReportHead> page = new Page<>(pageNo, pageSize);
        reportHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
        Page<ReportHead> reportHeadPage = reportHeadMapper.selectPage(page, new LambdaQueryWrapper<ReportHead>()
                .like(isNotEmpty(reportHead.getFileName()), ReportHead::getFileName, reportHead.getFileName())
                .eq(ReportHead::getTenantId, reportHead.getTenantId())
                .orderByDesc(ReportHead::getStartDate)
        );
        return Result.ok(reportHeadPage);
    }

    /**
     * 删除报核表头
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "删除报核表头")
    @ApiOperation(value = "删除报核表头", notes = "删除报核表头")
    @RequestMapping(value = "/deleteBatchReportHead")
    public Result<?> deleteBatchReportHead(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        reportHeadMapper.deleteBatchIds(idList);
        //删除下行表体
        for(String id:idList){
            reportListService.remove(new LambdaQueryWrapper<ReportList>()
                    .eq(ReportList::getReportHeadId,id));
        }
        return Result.OK("删除成功");
    }
    /**
     * 导入报核数据
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/27 15:24
     */
    @RequestMapping(value = "/importReportHead", method = RequestMethod.POST)
    public Result<?> importReportHead(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //未转换的物料号
        List<String> errorPnInList = new ArrayList<>();
        //未转换的成品号
        List<String> errorPnOutList = new ArrayList<>();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            try {
                ExcelImportResult<ReportList> excelImportResult =
                        ExcelImportUtil.importExcelVerify(file.getInputStream(), ReportList.class, params);
                if (null == excelImportResult || isEmpty(excelImportResult.getList())) {
                    return Result.error("文件格式错误，请严格按模版填写！");
                }
                List<ReportList> reportListList = excelImportResult.getList();
                if(reportListList.size()>0){
                    //投入的小物料号合集
                    List<String> inPnList= reportListList.stream().map
                            (ReportList::getInPn).distinct().collect(Collectors.toList());
                    //投入的大成品号号合集
                    List<String> outPnList= reportListList.stream().map
                            (ReportList::getOutPn).distinct().collect(Collectors.toList());
                    //获取对应的全部大物料
                    List<ProductMerge> productMergeListIn = productMergeMapper.selectList(new LambdaQueryWrapper<ProductMerge>()
                            .in(ProductMerge::getMergePn, inPnList));
                    List<ProductMerge> productMergeListOut = productMergeMapper.selectList(new LambdaQueryWrapper<ProductMerge>()
                            .in(ProductMerge::getMergePn, outPnList));
                    //小物料，大物料对应
                    Map<String,String> stringMapIn =
                            productMergeListIn.stream().collect(Collectors.toMap
                                    (ProductMerge::getMergePn,ProductMerge::getProductPn,(value1, value2) -> value1));
                    Map<String,String> stringMapOut =
                            productMergeListOut.stream().collect(Collectors.toMap
                                    (ProductMerge::getMergePn,ProductMerge::getProductPn,(value1, value2) -> value1));


                    //先存入表头数据
                    ReportHead reportHead=new ReportHead();
                    reportHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
                    reportHead.setFileName(file.getOriginalFilename());
                    reportHead.setStartDate(reportListList.get(0).getStartDate());
                    reportHead.setEndDate(reportListList.get(0).getEndDate());
                    reportHead.setCreateBy(loginUser.getUsername());
                    reportHead.setCreateTime(new Date());
                    reportHeadMapper.insert(reportHead);
                    for(ReportList reportList:reportListList){
                        if(stringMapIn.containsKey(reportList.getInPn())){
                            //存个大的投入物料号
                            reportList.setInPnBig(stringMapIn.get(reportList.getInPn()));
                        }else {
                            //不存在
                            errorPnInList.add(reportList.getInPn());
                        }

                        if(stringMapOut.containsKey(reportList.getOutPn())){
                            //存个大的产出成品号
                            reportList.setOutPnBig(stringMapOut.get(reportList.getOutPn()));
                        }else {
                            errorPnOutList.add(reportList.getOutPn());
                        }


                        reportList.setReportHeadId(reportHead.getId());
                        reportList.setTenantId(Long.valueOf(TenantContext.getTenant()));
                        reportList.setCreateBy(loginUser.getUsername());
                        reportList.setCreateTime(new Date());
                    }
                    reportListService.saveBatch(reportListList);
                }
            }catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败：" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                    log.error(e.getMessage(), e);
                }
            }


        }
        if(!errorPnInList.isEmpty()||!errorPnOutList.isEmpty()){
            return new Result<>(400,"全部导入成功,部分物料号未找到对应的大物料号，如下："+
                    (!errorPnInList.isEmpty()? errorPnInList.stream().distinct().collect(Collectors.joining(",")) :"")+","+
                    (!errorPnOutList.isEmpty()? errorPnOutList.stream().distinct().collect(Collectors.joining(",")) :""));

        }else {
            return Result.OK("全部导入成功");
        }

    }

    /**
     * 操作报核统计
     *
     */
    @AutoLog(value = "操作报核统计")
    @ApiOperation(value="操作报核统计", notes="操作报核统计")
    @PostMapping(value = "/handleReportStatistics")
    public Result<List<ReportStatisticsVO>> handleReportStatistics(String startDate,String endDate,String ids){
        List<String> idList = Arrays.asList(ids.split(","));
        //获取选择得数据的表体
        List<ReportList> reportLists = reportListService.list(new LambdaQueryWrapper<ReportList>()
                .in(ReportList::getReportHeadId, idList));
        if(reportLists.isEmpty()){
            return Result.error("未查询到该周期的报核数据。");
        }
        //投入的小物料号合集
        List<String> inPnList= reportLists.stream().map(ReportList::getInPn).distinct().collect(Collectors.toList());
        //获取对应的全部大物料
        List<ProductMerge> productMergeList = productMergeMapper.selectList(new LambdaQueryWrapper<ProductMerge>()
                .in(ProductMerge::getMergePn, inPnList));
        //小物料，大物料对应
        Map<String,String> stringMap2 =
                productMergeList.stream().collect(Collectors.toMap
                        (ProductMerge::getMergePn,ProductMerge::getProductPn,(value1, value2) -> value1));

        //过滤出大物料
        List<String> bigPnList=productMergeList.stream().map(ProductMerge::getProductPn).distinct()
                .collect(Collectors.toList());
        List<NemsInvtList> nemsInvtListList = nemsInvtListMapper.listByReportStatistics(startDate, endDate, bigPnList);
        //组装返回数据
        List<ReportStatisticsVO> reportStatisticsVOLis=new ArrayList<>();
        for(String bigPn:bigPnList){
            ReportStatisticsVO reportStatisticsVO=new ReportStatisticsVO();
            //项号(备案序号) 根据物料找表体备案序号
            List<NemsInvtList> nemsInvtListList1=nemsInvtListList.stream().filter(i->i.getGdsMtno().equals(bigPn))
                    .collect(Collectors.toList());
            reportStatisticsVO.setPutrecSeqno(nemsInvtListList1.size()>0?nemsInvtListList1.get(0).getPutrecSeqno():null);
            reportStatisticsVO.setGdsMtno(bigPn);//物料号
            //本期总进口数量
            BigDecimal inQty=nemsInvtListList.stream().filter(i->i.getGdsMtno().equals(bigPn)).map(NemsInvtList::getDclQty)
                    .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            reportStatisticsVO.setInQty(inQty);
            //本期总消耗数量
            //获取该大料号对应的所有小料号
            List<ProductMerge> productMergeList1=productMergeList.stream().filter(i->i.getProductPn()
                    .equals(bigPn)).collect(Collectors.toList());
            List<String> smallPns =productMergeList1.stream().map(ProductMerge::getMergePn).distinct()
                    .collect(Collectors.toList());
            BigDecimal useQty=reportLists.stream().filter(i->smallPns.contains(i.getInPn())).map(ReportList::getInQty)
                    .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            reportStatisticsVO.setUseQty(useQty);
            //本期理论库存
            reportStatisticsVO.setStockQty(inQty.subtract(useQty));
            //开始日期
            reportStatisticsVO.setStartDate(startDate);
            //结束日期
            reportStatisticsVO.setEndDate(endDate);
            reportStatisticsVOLis.add(reportStatisticsVO);

        }
        return Result.OK(reportStatisticsVOLis);
    }

    /**
     * 操作单耗统计
     *
     */
    @AutoLog(value = "操作单耗统计")
    @ApiOperation(value="操作单耗统计", notes="操作单耗统计")
    @PostMapping(value = "/handleReportStatisticsCustomize")
    public Result<List<ReportStatisticsExportVO>> handleReportStatisticsCustomize(String startDate,String endDate,String ids){
        List<String> idList = Arrays.asList(ids.split(","));
        //获取选择得数据的表体
        List<ReportList> reportLists = reportListService.list(new LambdaQueryWrapper<ReportList>()
                .in(ReportList::getReportHeadId, idList));
        if(reportLists.isEmpty()){
            return Result.error("未查询到该周期的报核数据。");
        }
        //全部的大 投入物料号 商品库查询(转换品名用)
        List<String> inPnBig = reportLists.stream().map(ReportList::getInPnBig).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfoListIn = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                .in(ProductInfo::getPn,inPnBig));
        //全部的大 产出成品号 商品库查询
        List<String> outPnBig = reportLists.stream().map(ReportList::getOutPnBig).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfoListOut = productInfoService.list(new LambdaQueryWrapper<ProductInfo>()
                .in(ProductInfo::getPn,outPnBig));
        //大物料号，品名对应
        Map<String,String> stringMapIn =
                productInfoListIn.stream().collect(Collectors.toMap
                        (ProductInfo::getPn,ProductInfo::getChineseName,(value1, value2) -> value1));
        Map<String,String> stringMapOut =
                productInfoListOut.stream().collect(Collectors.toMap
                        (ProductInfo::getPn,ProductInfo::getChineseName,(value1, value2) -> value1));

        //以成品料号为主键
        Map<String, List<ReportList>> groupedReportList = reportLists.stream().filter(i->StringUtils.isNotBlank(i.getOutPnBig()))
                .collect(Collectors.groupingBy(ReportList::getOutPnBig));
        List<ReportStatisticsExportVO> reportStatisticsExportVOList=new ArrayList<>();
        AtomicInteger item= new AtomicInteger(1);
        groupedReportList.forEach((outPnBigItem, reportListList) -> {
            //存在多个相同的投入物料，需要进行汇总
            //以投入物料号为主键
            Map<String, List<ReportList>> groupedReportListByInPnBig = reportListList.stream()
                    .collect(Collectors.groupingBy(ReportList::getInPnBig));
            groupedReportListByInPnBig.forEach((inPnBigItem, reportListListIn) -> {
                ReportStatisticsExportVO reportStatisticsExportVO=new ReportStatisticsExportVO();
                reportStatisticsExportVO.setItem(item.get());//序号
                reportStatisticsExportVO.setAexgName(stringMapOut.get(outPnBigItem));//产出成品名称
                BigDecimal exportQty=reportListList.stream().map(ReportList::getOutQty)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                reportStatisticsExportVO.setExportQty(exportQty);//出口数量
                BigDecimal actualConsumption=reportListListIn.stream().map(ReportList::getInQty)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                reportStatisticsExportVO.setActualConsumption(actualConsumption);//实耗
                reportStatisticsExportVO.setUnitConsumption(actualConsumption.divide(
                        exportQty,9, RoundingMode.HALF_UP).stripTrailingZeros());//单耗 =实耗/出口数量
                reportStatisticsExportVO.setAimgName(stringMapIn.get(inPnBigItem));//物料名称
                reportStatisticsExportVOList.add(reportStatisticsExportVO);
            });
            item.getAndIncrement();
        });
        return Result.OK(reportStatisticsExportVOList);


    }

    /**
     * 导出报核统计结果excel(新版，自定义的)
     *
     * @param request
     * @param productInfo
     */
    @RequestMapping(value = "/exportReportXlsCustomize")
    public void exportReportXlsCustomize(HttpServletRequest request, HttpServletResponse response, ReportStatisticsVO reportList) {
        Result<List<ReportStatisticsExportVO>> result = this.handleReportStatisticsCustomize(
                reportList.getStartDate(), reportList.getEndDate(), reportList.getId());
        if(result.getResult().size()>0){
            List<ReportStatisticsExportVO> reportStatisticsVOList=result.getResult();
            List<ReportStatisticsExportVO> reportStatisticsVOListTemp= new ArrayList<>(reportStatisticsVOList);
            cn.afterturn.easypoi.excel.entity.ExportParams params = new ExportParams();
            params.setSheetName("单耗统计");
            params.setType(ExcelType.XSSF);
            params.setStyle(ExcelExportStylerBorderImpl.class);
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> mapInv = new HashMap<>();
            mapInv.put("title", params);
            mapInv.put("entity", ReportStatisticsExportVO.class);
            mapInv.put("data", reportStatisticsVOList);
            list.add(mapInv);
            Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);
            //处理合并
            Sheet sheet = workbook.getSheetAt(0);
            List<Integer> itemList=reportStatisticsVOListTemp.stream().map(
                    ReportStatisticsExportVO::getItem
            ).collect(Collectors.toList());
            List<Integer> itemAllList = handleDuplicates(itemList);
            if(!itemAllList.isEmpty()){
                for(Integer item:itemAllList){
                    int indexOf = itemList.indexOf(item);//需要合并的序号 出现的第一个坐标
                    long count = itemList.stream().filter(i->i.equals(item))
                            .count();
                    //合并列
                    CellRangeAddress cra=new CellRangeAddress(indexOf+1, ((int)count+indexOf), 0, 0);
                    CellRangeAddress cra2=new CellRangeAddress(indexOf+1, ((int)count+indexOf), 1, 1);
                    CellRangeAddress cra3=new CellRangeAddress(indexOf+1, ((int)count+indexOf), 2, 2);
                    sheet.addMergedRegion(cra);
                    sheet.addMergedRegion(cra2);
                    sheet.addMergedRegion(cra3);

                }
            }



            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            // 下载文件能正常显示中文
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream fos = null;
            FileOutputStream FileFos = null;
            try {
                //普通下载
                fos = response.getOutputStream();
                workbook.write(fos);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (FileFos != null) {
                        FileFos.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }
    private List<Integer> handleDuplicates(List<Integer> list){
        List<Integer> duplicates = new ArrayList<>(); // 存放重复对象的List
        for (int i = 0; i < list.size(); i++) {
            Integer current = list.get(i);
            for (int j = i + 1; j < list.size(); j++) {
                Integer compare = list.get(j);
                if (current.equals(compare)) {
                    if (!duplicates.contains(current)) {
                        duplicates.add(current);
                    }
                    break;
                }
            }
        }
        return duplicates;
    }
    /**
     * 导出报核统计结果excel（旧版，暂时未用）
     *
     * @param request
     * @param productInfo
     */
    @RequestMapping(value = "/exportReportXls")
    public void exportReportXls(HttpServletRequest request, HttpServletResponse response, ReportStatisticsVO reportList) {
        Result<List<ReportStatisticsVO>> result = this.handleReportStatistics(reportList.getStartDate(), reportList.getEndDate(), reportList.getId());
        if(result.getResult().size()>0){
            List<ReportStatisticsVO> reportStatisticsVOList=result.getResult();

            cn.afterturn.easypoi.excel.entity.ExportParams params = new ExportParams();
            params.setSheetName("报核统计");
            params.setType(ExcelType.XSSF);
            params.setStyle(ExcelExportStylerBorderImpl.class);
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> mapInv = new HashMap<>();
            mapInv.put("title", params);
            mapInv.put("entity", ReportStatisticsVO.class);
            mapInv.put("data", reportStatisticsVOList);
            list.add(mapInv);
            Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.XSSF);

            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            // 下载文件能正常显示中文
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream fos = null;
            FileOutputStream FileFos = null;
            try {
                //普通下载
                fos = response.getOutputStream();
                workbook.write(fos);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (FileFos != null) {
                        FileFos.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 操作账册初复审
     */
    @AutoLog(value = "操作账册初复审")
    @ApiOperation(value="操作账册初复审", notes="操作账册初复审")
    @PostMapping(value = "/handleInitialReview")
    public Result<?> handleInitialReview(String ids,String initialReviewStatus,String opinion){
        if(StringUtils.isBlank(ids)){
            return Result.error("请选择至少一条记录进行初复审");
        }
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> idList=Arrays.asList(ids.split(","));
        if("1".equals(initialReviewStatus)){
            //更新账册表头 初复审状态
            ptsEmsHeadMapper.update(null,new LambdaUpdateWrapper<PtsEmsHead>()
                    .set(PtsEmsHead::getFirstEStatus,"1").in(PtsEmsHead::getId,idList));

        }else if("2".equals(initialReviewStatus)){
            //更新账册表头 初复审状态
            ptsEmsHeadMapper.update(null,new LambdaUpdateWrapper<PtsEmsHead>()
                    .set(PtsEmsHead::getReEStatus,"1").in(PtsEmsHead::getId,idList));
        }
        for(String id :idList){
            //添加审核记录表
            PtsEmsAuditRecords ptsEmsAuditRecords=new PtsEmsAuditRecords();
            ptsEmsAuditRecords.setPtsEmsHeadId(Long.valueOf(id));
            ptsEmsAuditRecords.setAuditType(initialReviewStatus);
            ptsEmsAuditRecords.setAuditBy(loginUser.getRealname());
            ptsEmsAuditRecords.setAuditDate(new Date());
            ptsEmsAuditRecords.setAuditContent(opinion);
            ptsEmsAuditRecords.setTenantId(Long.valueOf(TenantContext.getTenant()));
            ptsEmsAuditRecordsMapper.insert(ptsEmsAuditRecords);
        }
        return Result.OK("操作成功");
    }

    /**
     * 导入加贸账册
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/1/14 15:24
     */
    @RequestMapping(value = "/importEms", method = RequestMethod.POST)
    public Result<?> importEms(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
//        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        int type = Integer.parseInt(request.getParameter("type"));
        String emsType = request.getParameter("emsType");
//        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
        //手动取第一个文件，因为目前前端大部分只传单文件
            MultipartFile file = fileMap.get(fileMap.keySet().iterator().next());
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            try{
                // 导入Excel
                EmsExcelImportResult<?> excelImportResult = EmsExcelImportUtil.importExcelVerify(
                        file.getInputStream(), SHEET_NAME_TO_POJO_CLASS_MAP, getSheetName(emsType, type),
                        getImportParams(), emsType);
                log.info("最终导入的数据是==> " + excelImportResult.getList());
                // 将导入的数据进行分组
                Map<String, List<?>> emsMap = groupEms(excelImportResult.getList());
                log.info("表头==>" + emsMap.get("emsHead").size());
                log.info("料件==>" + emsMap.get("emsAimg").size());
                log.info("成品==>" + emsMap.get("emsAexg").size());
                log.info("单耗==>" + emsMap.get("emsCm").size());
                if (type == 3 && emsMap.get("emsHead").size() <= 0) {
                    return Result.error("账册表头数据校验失败，请检查数据格式！");
                } else if (type == 3 && emsMap.get("emsHead").size() > 1) {
                    log.info("importEms方法,导入类型:{},手/账册表头数量:{},全部导入,手/账册表头数量不允许大于1", type, emsMap.get("emsHead").size());
                    return Result.error("全部导入,手/账册表头数量不允许大于1");
                }
                /*
                 * 判断账册号重复！
                 * 2025/1/17 13:14@zls
                 */
                ImportEmsHead emsHead = (ImportEmsHead) emsMap.get("emsHead").get(0);
                //各种类型的转换，手册和账册不同字段
                emsHead.setEmsNo(isNotBlank(emsHead.getEmsNo_()) ? emsHead.getEmsNo_() : emsHead.getEmsNo());
                emsHead.setEmsNo(isNotBlank(emsHead.getEmsNoSC()) ? emsHead.getEmsNoSC() : emsHead.getEmsNo());
                emsHead.setEmsType(isNotBlank(emsHead.getEmsTypeSC()) ? emsHead.getEmsTypeSC() : emsHead.getEmsType());
                emsHead.setEndDate(isNotEmpty(emsHead.getEndDate_()) ? emsHead.getEndDate_() : emsHead.getEndDate());
                emsHead.setEndDate(isNotEmpty(emsHead.getEndDateSC()) ? emsHead.getEndDateSC() : emsHead.getEndDate());
                emsHead.setOwnerCode(isNotBlank(emsHead.getOwnerCodeCkdm()) ? emsHead.getOwnerCodeCkdm() : emsHead.getOwnerCode());
                emsHead.setOwnerName(isNotBlank(emsHead.getOwnerNameCkname()) ? emsHead.getOwnerNameCkname() : emsHead.getOwnerName());



                List<PtsEmsHead> ptsEmsHeads = emsHeadService.list(new LambdaQueryWrapper<PtsEmsHead>().eq(PtsEmsHead::getTenantId, TenantContext.getTenant())
                        .eq(PtsEmsHead::getEmsNo, emsHead.getEmsNo()));
                if(!ptsEmsHeads.isEmpty()){
                    return Result.error("当前租户下,此手/账册编号已存在！");
                }
                Result<Map<String,Object>> result = emsHeadService.importEms(type,
                        getEmsHeadByMap(type, emsMap, request));
                if(result.isSuccess()){
                    List<String> errorMessageList=(List<String>)result.getResult().get("errorMessageList");
                    errorMessageList.addAll(excelImportResult.getVerfiyMsg());
                    int successRows = (int)result.getResult().get("successRows");

                    return EmsExcelImportUtil.importResp(excelImportResult.getAllRows(), successRows,
                            excelImportResult.getAllRows() - successRows,
                            errorMessageList);
                }else {
                    return Result.error(result.getMessage());
                }

            }catch (Exception e){
                log.error("importEms方法,系统运行过程中出现异常,异常信息是:", e);
                e.printStackTrace();
                String msg = e.getMessage();
                if (isNotBlank(msg)) {
                    if (msg.contains("长度超过限制")) {
                        if (msg.contains("TRADE_CODE")) {
                            msg = "经营单位编码格式错误，必须为十位编码！";
                        }
                        if (msg.contains("OWNER_CODE")) {
                            msg = "加工单位编码格式错误，必须为十位编码！";
                        }
                        if (msg.contains("DECLARE_CODE")) {
                            msg = "申报单位编码格式错误，必须为十位编码！";
                        }
                    }
                }
                return Result.error(msg);
            }

//        }

    }
        public static <K, V> V getFirstValue(Map<K, V> map) {
            if (map == null || map.isEmpty()) {
                return null;
            }
            return map.get(map.keySet().iterator().next());
        }

    /**
     * 获取手/账册表头对象
     *
     * @param type       导入类型
     *                   <p>
     *                   0:料件
     *                   <p>
     *                   1:成品
     *                   <p>
     *                   2:单损耗
     *                   <p>
     *                   3:全部
     * @param emsMap     导入的集合数据按照类型进行分组对象
     * @param attributes 属性集合
     * @return 手/账册表头对象
     */
    @SuppressWarnings("all")
    private PtsEmsHead getEmsHeadByMap(Integer type, Map<String, List<?>> emsMap, HttpServletRequest request) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 料件、成品、单损耗需要设置主键ID、手/账册编号、表体项
        if (type == 0 || type == 1 || type == 2) {
            PtsEmsHead emsHead = new PtsEmsHead();
            // 设置主键ID
            if (isNotBlank(request.getParameter("emsId"))) {
                emsHead.setId(Long.parseLong(String.valueOf(request.getParameter("emsId"))));
            }
            // 设置手/账册编号
            if (isNotBlank(request.getParameter("emsNo"))) {
                emsHead.setEmsNo(String.valueOf(request.getParameter("emsNo")));
            }
            // 设置表体项
            if (type == 0) {
                emsHead.setEmsAimgList((List<PtsEmsAimg>) emsMap.get("emsAimg"));
            } else if (type == 1) {
                emsHead.setEmsAexgList((List<PtsEmsAexg>) emsMap.get("emsAexg"));
            } else if (type == 2) {
                emsHead.setEmsCmList((List<PtsEmsCm>) emsMap.get("emsCm"));
            }
            // 设置租户ID
            emsHead.setTenantId(Long.valueOf(TenantContext.getTenant()));

            emsHead.setByName(loginUser.getUsername());
            // 2020/12/15 7:30@ZHANGCHAO 追加/变更/完善：不同模版赋值！！
//            emsHead.setEmsNo(isNotBlank(emsHead.getEmsNo_()) ? emsHead.getEmsNo_() : emsHead.getEmsNo());
//            emsHead.setEndDate(isNotEmpty(emsHead.getEndDate_()) ? emsHead.getEndDate_() : emsHead.getEndDate());
            if (isBlank(emsHead.getEmsType())) {
                if (emsHead.getEmsNo().startsWith("T") || emsHead.getEmsNo().startsWith("t")) {
                    emsHead.setEmsType("TW");
                } else if (emsHead.getEmsNo().startsWith("L") || emsHead.getEmsNo().startsWith("l")) {
                    emsHead.setEmsType("L");
                } else {
                    throw new RuntimeException("未识别的账册类型！");
                }
            }
            return emsHead;
        } else {
            PtsEmsHead emsHead = new PtsEmsHead();
            ImportEmsHead importEmsHead = (ImportEmsHead) emsMap.get("emsHead").get(0);
            BeanUtils.copyProperties(importEmsHead,emsHead);
            //料件
            List<ImportEmsAimg> importPtsEmsAimg = (List<ImportEmsAimg>) emsMap.get("emsAimg");
            List<PtsEmsAimg> emsAimgList= new ArrayList<>();
            for(ImportEmsAimg importEmsAimg:importPtsEmsAimg){
                PtsEmsAimg ptsEmsAimg = new PtsEmsAimg();
                BeanUtils.copyProperties(importEmsAimg,ptsEmsAimg);
                emsAimgList.add(ptsEmsAimg);
            }
            emsHead.setEmsAimgList(emsAimgList);
            //成品
            List<ImportEmsAexg> importPtsEmsAexg = (List<ImportEmsAexg>) emsMap.get("emsAexg");
            List<PtsEmsAexg> emsAexgList= new ArrayList<>();
            for(ImportEmsAexg importEmsAexg:importPtsEmsAexg){
                PtsEmsAexg ptsEmsAexg = new PtsEmsAexg();
                BeanUtils.copyProperties(importEmsAexg,ptsEmsAexg);
                emsAexgList.add(ptsEmsAexg);
            }
            emsHead.setEmsAexgList(emsAexgList);
            //单损耗
            List<ImportEmsCm> importEmsCms = (List<ImportEmsCm>) emsMap.get("emsCm");
            List<PtsEmsCm> ptsEmsCmList= new ArrayList<>();
            for(ImportEmsCm importEmsCm:importEmsCms){
                PtsEmsCm ptsEmsCm = new PtsEmsCm();
                BeanUtils.copyProperties(importEmsCm,ptsEmsCm);
                ptsEmsCmList.add(ptsEmsCm);
            }
            emsHead.setEmsCmList(ptsEmsCmList);


            // 设置租户ID
            emsHead.setTenantId(Long.valueOf(TenantContext.getTenant()));

            emsHead.setByName(loginUser.getUsername());
            // 2020/12/15 7:30@ZHANGCHAO 追加/变更/完善：不同模版赋值！！
//            emsHead.setEmsNo(isNotBlank(emsHead.getEmsNo_()) ? emsHead.getEmsNo_() : emsHead.getEmsNo());
//            emsHead.setEndDate(isNotEmpty(emsHead.getEndDate_()) ? emsHead.getEndDate_() : emsHead.getEndDate());
            if (isBlank(emsHead.getEmsType())) {
                if (emsHead.getEmsNo().startsWith("T") || emsHead.getEmsNo().startsWith("t")) {
                    emsHead.setEmsType("TW");
                } else if (emsHead.getEmsNo().startsWith("L") || emsHead.getEmsNo().startsWith("l")) {
                    emsHead.setEmsType("L");
                } else {
                    throw new RuntimeException("未识别的账册类型！");
                }
            }
            return emsHead;
        }
    }

    /**
     * 将导入的集合数据按照类型进行分组
     *
     * @param emsList 导入的集合数据
     * @return 分组后的数据
     */
    private Map<String, List<?>> groupEms(List<?> emsList) {
        // 手/账册表头集合
        List<ImportEmsHead> emsHeadList = new ArrayList<ImportEmsHead>();
        // 手/账册归并后料件集合
        List<ImportEmsAimg> emsAimgList = new ArrayList<ImportEmsAimg>();
        // 手/账册归并后成品集合
        List<ImportEmsAexg> emsAexgList = new ArrayList<ImportEmsAexg>();
        // 手/账册单损耗集合
        List<ImportEmsCm> emsCmList = new ArrayList<ImportEmsCm>();
        for (Object object : emsList) {
            if (object instanceof ImportEmsHead) {
                emsHeadList.add((ImportEmsHead) object);
            } else if (object instanceof ImportEmsAimg) {
                emsAimgList.add((ImportEmsAimg) object);
            } else if (object instanceof ImportEmsAexg) {
                emsAexgList.add((ImportEmsAexg) object);
            } else if (object instanceof ImportEmsCm) {
                emsCmList.add((ImportEmsCm) object);
            }
//            else if (object instanceof HandBookEmsAimg) {
//                HandBookEmsAimg bookEmsAimg = (HandBookEmsAimg) object;
//                EmsAimg emsAimg = new EmsAimg();
//                BeanUtil.copyProperties(bookEmsAimg, emsAimg, CopyOptions.create().ignoreNullValue());
//                emsAimgList.add(emsAimg);
//            } else if (object instanceof HandBookEmsAexg) {
//                HandBookEmsAexg bookEmsAexg = (HandBookEmsAexg) object;
//                EmsAexg emsAexg = new EmsAexg();
//                BeanUtil.copyProperties(bookEmsAexg, emsAexg, CopyOptions.create().ignoreNullValue());
//                emsAexgList.add(emsAexg);
//            }
        }
        // 2021/1/29 14:03@ZHANGCHAO 追加/变更/完善：去掉特殊字符！！回车和换行
        if (isNotEmpty(emsAimgList)) {
            for (ImportEmsAimg emsAimg : emsAimgList) {
                emsAimg.setGModel(replaceSpecialStr(emsAimg.getGModel()));
            }
        }
        if (isNotEmpty(emsAexgList)) {
            for (ImportEmsAexg emsAexg : emsAexgList) {
                emsAexg.setGModel(replaceSpecialStr(emsAexg.getGModel()));
            }
        }
        return new HashMap<String, List<?>>() {
            private static final long serialVersionUID = 1L;

            {
                put("emsHead", emsHeadList);
                put("emsAimg", emsAimgList);
                put("emsAexg", emsAexgList);
                put("emsCm", emsCmList);
            }
        };
    }
    /**
     * 去除字符串中的回车、换行符
     *
     * @param str
     * @return
     */
    private String replaceSpecialStr(String str) {
        String repl = "";
        if (str != null) {
            Pattern p = Pattern.compile("\r|\n");
            Matcher m = p.matcher(str);
            repl = m.replaceAll("");
        }
        return repl;
    }
    /**
     * 获取Excel的Sheet名称
     *
     * @param type 导入类型
     *             <p>
     *             0:料件
     *             <p>
     *             1:成品
     *             <p>
     *             2:单损耗
     *             <p>
     *             3:全部
     * @return Excel的Sheet名称
     */
    private String[] getSheetName(String type_, Integer type) {
        /*
         * 区分加贸手账册和物流账册！
         * 2020/11/30 16:35@ZHANGCHAO
         */
        String emsType = null;
        String[] SAS_H = {"1", "2", "3", "4", "B", "C"}; // 保税加工手账册
        String[] SAS_TW = {"TW", "L"}; // 保税加工手账册
        if (Arrays.asList(SAS_H).contains(type_)) {
            log.info("此账册是加贸手账册类型！");
            emsType = "SAS_H";
        } else if (Arrays.asList(SAS_TW).contains(type_)) {
            log.info("此账册是物流账册类型！");
            emsType = "SAS_TW";
        } else {
            return new String[]{};
        }
        if ("SAS_H".equalsIgnoreCase(emsType)) {
            switch (type) {
                case 0: // 料件
                    return new String[]{"料件"};
                case 1: // 成品
                    return new String[]{"成品"};
                case 2: // 单损耗
                    return new String[]{"单耗"};
                case 3: // 全部
                default:
                    return new String[]{"表头", "料件", "成品", "单耗", "单损耗"};
            }
        } else if ("SAS_TW".equalsIgnoreCase(emsType)) {
            switch (type) {
//                case 0: // 料件
//                    return new String[]{"料件"};
//                case 1: // 成品
//                    return new String[]{"成品"};
//                case 2: // 单损耗
//                    return new String[]{"单耗"};
                case 3: // 全部
                default:
                    return new String[]{"表头", "表体"};
            }
        } else {
            return new String[]{};
        }
    }
    /**
     * 获取导入参数设置对象
     *
     * @return 导入参数设置对象
     */
    private ImportParams getImportParams() {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setNeedSave(false);
        return params;
    }
    /**
     * 获取EMS详细信息列表进口报税料件/成品金额统计-手册报表中心
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "获取EMS详细信息列表进口报税料件/成品金额统计-手册报表中心", notes = "获取EMS详细信息列表进口报税料件/成品金额统计-手册报表中心")
    @GetMapping(value = "/listEmsDetailAmountByReport")
    public Result<?> listEmsDetailAmountByReport(EmsQueryDto emsQueryDto,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
//        QueryWrapper<Dispatch> queryWrapper = QueryGenerator.initQueryWrapper(dispatch, req.getParameterMap());
        Page page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage pageList = emsHeadService.listEmsDetailAmountByReport(page, emsQueryDto);
        return Result.ok(pageList);
    }
    /**
     * 获取EMS详细信息列表单价比较
     *
     * @param emsQueryDto
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "获取EMS详细信息列表单价比较", notes = "获取EMS详细信息列表单价比较")
    @GetMapping(value = "/listEmsDetailByUnitPriceComparison")
    public Result<?> listEmsDetailByUnitPriceComparison(EmsQueryDto emsQueryDto,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 HttpServletRequest req) {
        Page<PtsEmsAimg> page = new Page<>(pageNo, pageSize);
        emsQueryDto.setTenantId(TenantContext.getTenant());
        IPage<PtsEmsAimg> pageList = emsHeadService.listEmsDetailByUnitPriceComparison(page, emsQueryDto);
        return Result.ok(pageList);
    }
    /**
     * 根据账册号获取最新账册表头数据（可为空）
     *
     * @param emsQueryDto
     * @return
     */
    @ApiOperation(value = "根据账册号获取最新账册表头数据（可为空）", notes = "根据账册号获取最新账册表头数据（可为空）")
    @PostMapping(value = "/synEmsHead")
    public Result<?> synEmsHead(@RequestBody EmsQueryDto emsQueryDto) {
        //获取登录企业信息
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, TenantContext.getTenant()));
        if(isEmpty(enterpriseInfo)){
            return Result.error("未查询到当前操作企业信息");
        }
        Result<?> result = emsHeadService.syncSmPtsEmsHeadData(enterpriseInfo.getSwid(), emsQueryDto.getType(),
                emsQueryDto.getSeqNo(), enterpriseInfo.getCustomsDeclarationCode(), emsQueryDto.getEmsNo());
        return result;
    }
    /**
     * 手册单损耗的平衡检查
     *
     * @param emsHeadId
     * @return
     */
    @ApiOperation(value = "手册单损耗的平衡检查", notes = "手册单损耗的平衡检查")
    @GetMapping(value = "/balanceCheck")
    public Result<?> balanceCheck(@RequestParam(name = "emsHeadId") Long emsHeadId){
        //获取全部料件
        List<PtsEmsAimg> ptsEmsAimgs = emsAimgService.list(new LambdaQueryWrapper<PtsEmsAimg>()
                .eq(PtsEmsAimg::getEmsId, emsHeadId));
        //单损耗
        List<PtsEmsCm> ptsEmsCms = emsCmService.list(new LambdaQueryWrapper<PtsEmsCm>()
                .eq(PtsEmsCm::getEmsId, emsHeadId));
        //成品
        List<PtsEmsAexg> ptsEmsAexgs = emsAexgService.list(new LambdaQueryWrapper<PtsEmsAexg>()
                .eq(PtsEmsAexg::getEmsId, emsHeadId));
        //计算料件消耗量，根据单损耗
        for (PtsEmsAimg ptsEmsAimg : ptsEmsAimgs) {
            //获取该料件序号的单耗
            List<PtsEmsCm> emsCmsList = ptsEmsCms.stream().filter(ptsEmsCm -> ptsEmsCm.getImgNo().equals(ptsEmsAimg.getGNo()))
                    .collect(Collectors.toList());
            if(!isEmpty(emsCmsList)){
                BigDecimal consumption = BigDecimal.ZERO;
                for(PtsEmsCm ptsEmsCm: emsCmsList){
                    //该单号的成品
                    List<PtsEmsAexg> ptsEmsAexgList = ptsEmsAexgs.stream().
                            filter(ptsEmsAexg -> ptsEmsAexg.getGNo().equals(ptsEmsCm.getExgNo())).collect(Collectors.toList());
                    if(!ptsEmsAexgList.isEmpty()){
                        BigDecimal jh = ptsEmsAexgList.get(0).getQty().multiply(ptsEmsCm.getDecCm())
                                .setScale(4,BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
                        //有形损耗量 = 料件数量乘单耗的有形损耗率（为百分率，需要转换）
                        BigDecimal yxshl = ptsEmsAimg.getQty().multiply(ptsEmsCm.getDecDm()
                                .divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP)).setScale(4,BigDecimal.ROUND_HALF_UP)
                                .stripTrailingZeros();
                        BigDecimal wxshl = ptsEmsAimg.getQty().multiply(ptsEmsCm.getIntangibleLossRate()
                                .divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP)).setScale(4,BigDecimal.ROUND_HALF_UP)
                                .stripTrailingZeros();
                        consumption = consumption.add(jh).add(yxshl).add(wxshl);
                    }
                }
                //消耗量
                ptsEmsAimg.setConsumption(consumption);
                //差额量 = 料件申报数量-消耗量
                ptsEmsAimg.setDifference(ptsEmsAimg.getQty().subtract(consumption));
                //差额率 = 差额量/料件申报数量
                ptsEmsAimg.setDifferenceRate(ptsEmsAimg.getDifference().divide(ptsEmsAimg.getQty(),4,BigDecimal.ROUND_HALF_UP));
            }



        }
        return Result.ok(ptsEmsAimgs);


    }



}
