ALTER TABLE pts_ems_head
    ADD COLUMN REGION_CODE VARCHAR(50) DEFAULT NULL COMMENT '加工企业地区代码',
    ADD COLUMN TRADE_TYPE_CODE VARCHAR(50) DEFAULT NULL COMMENT '监管方式',
    ADD COLUMN CONTRACT_NO_I VARCHAR(255) DEFAULT NULL COMMENT '进口合同号',
    ADD COLUMN CONTRACT_NO_E VARCHAR(255) DEFAULT NULL COMMENT '出口合同号',
    ADD COLUMN TAX_TYPE_CODE VARCHAR(50) DEFAULT NULL COMMENT '征免性质',
    ADD COLUMN PROCESSING_TYPE VARCHAR(50) DEFAULT NULL COMMENT '加工种类',
    ADD COLUMN IE_PORT VARCHAR(50) DEFAULT NULL COMMENT '进出口岸',
    ADD COLUMN IMPORT_CURRENCY VARCHAR(50) DEFAULT NULL COMMENT '进口币制',
   ADD COLUMN EXPORT_CURRENCY VARCHAR(50) DEFAULT NULL COMMENT '出口币制',
     ADD COLUMN CODE_DECLARATION_UNIT_CONSUMPTION VARCHAR(5) DEFAULT NULL COMMENT '单耗申报环节代码',
    ADD COLUMN MANUAL_PURPOSE VARCHAR(5) DEFAULT NULL COMMENT '手册用途',
    ADD COLUMN SUSPEND_IE VARCHAR(5) DEFAULT NULL COMMENT '暂停进出口标记',
     ADD COLUMN FIRST_EXPORT_DATE datetime DEFAULT NULL COMMENT '首次出口日期',
    ADD COLUMN SUSPEND_CHANGE_MARK VARCHAR(5) DEFAULT NULL COMMENT '暂停变更标记',
    ADD COLUMN SELF_VERIFICATION_MARK VARCHAR(5) DEFAULT NULL COMMENT '自核资格标记',
