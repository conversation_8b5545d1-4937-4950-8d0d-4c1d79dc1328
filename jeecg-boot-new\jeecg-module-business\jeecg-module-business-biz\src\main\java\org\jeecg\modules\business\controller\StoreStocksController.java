package org.jeecg.modules.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.InOrOutStorageDetailDTO;
import org.jeecg.modules.business.entity.dto.StoreStocksDTO;
import org.jeecg.modules.business.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * <p>
 * 仓库库存建账表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
@RestController
@RequestMapping("/business/storeStocks")
public class StoreStocksController {
    @Autowired
    private IStoreStocksService storeStocksService;

    /**
     * 仓库信息列表查询
     *
     * @param storeStocksDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "仓库信息-分页列表查询", notes = "仓库信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(StoreStocksDTO storeStocksDTO,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        String tenantId = TenantContext.getTenant();
        if (isNotBlank(tenantId) && "56".equals(tenantId)) {
            return storeStocksService.queryPageList_(storeStocksDTO, pageNo, pageSize);
        }
        return storeStocksService.queryPageList(storeStocksDTO, pageNo, pageSize);
    }

    /**
     * 仓库信息列表查询
     *
     * @param storeStocksDTO

     * @return
     */
    @ApiOperation(value = "仓库信息-分页列表查询", notes = "仓库信息-分页列表查询")
    @GetMapping(value = "/loadTotalSummary")
    public Result<?> loadTotalSummary(StoreStocksDTO storeStocksDTO) {
        return storeStocksService.loadTotalSummary(storeStocksDTO);
    }

    /**
     * 仓库商品库存流水列表查询
     *
     * @return
     */
    @ApiOperation(value = "仓库商品库存流水-分页列表查询", notes = "仓库商品库存流水-分页列表查询")
    @GetMapping(value = "/listGoodsFlow")
    public Result<?> listGoodsFlow(StoreStocksDTO storeStocksDTO) {
        Page<StoreStocksFlow> page = new Page<>(storeStocksDTO.getPageNo(), storeStocksDTO.getPageSize());
        IPage<StoreStocksFlow> pageList = storeStocksService.listGoodsFlow(page, storeStocksDTO);
        return Result.OK(pageList);
    }

    /**
     * 根据出入库单表体获取对应的仓库库存信息
     *
     * @param storageDetail 存储地点代码
     * @return 查询结果
     */
    @ApiOperation(value = "根据出入库单表体获取对应的仓库库存信息", notes = "根据出入库单表体获取对应的仓库库存信息")
    @GetMapping(value = "/getStockByDetail")
    public Result<StoreStocks> getStockByDetail(@RequestBody StorageDetail storageDetail) {
        return storeStocksService.getStockByDetail(storageDetail);
    }

    /**
     * 导出库存Excel
     *
     * @param request
     * @param response
     * @param storeStocksDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/9 15:59
     */
    @RequestMapping(value = "/exportStocks")
    public void exportStocks(HttpServletRequest request, HttpServletResponse response,
                             StoreStocksDTO storeStocksDTO) throws IOException {
        storeStocksService.exportStocks(request, response, storeStocksDTO);
    }

    /**
     * 导出出入库明细Excel
     *
     * @param request
     * @param response
     * @param inOrOutStorageDetailDTO
     * @return void
     * <AUTHOR>
     * @date 2024/9/13 14:51
     */
    @RequestMapping(value = "/exportStoreDetail")
    public void exportStoreDetail(HttpServletRequest request, HttpServletResponse response,
                                  InOrOutStorageDetailDTO inOrOutStorageDetailDTO) throws IOException {
        storeStocksService.exportStoreDetail(request, response, inOrOutStorageDetailDTO);
    }
    /**
     * 保税预警 有效期预警分页数据
     *
     * @param storeStocksDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "保税预警 有效期预警分页数据-分页列表查询", notes = "保税预警 有效期预警分页数据-分页列表查询")
    @GetMapping(value = "/limitedTermWarningList")
    public Result<?> limitedTermWarningList(StoreStocksDTO storeStocksDTO,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return storeStocksService.limitedTermWarningList(storeStocksDTO, pageNo, pageSize);
    }
}
