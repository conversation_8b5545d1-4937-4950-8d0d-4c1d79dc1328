package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.EnterpriseInfo;

import java.util.List;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-18
 * @Version: V1.0
 */
public interface IEnterpriseInfoService extends IService<EnterpriseInfo> {

    List<EnterpriseInfo> getCollectionEnterpriseList(EnterpriseInfo enterpriseInfo) ;

    /**
     * 将公司全称同步到系统企业管理表
     * @param id
     * @param name
     */
    void updateTenantName(Long id, String name);

    /**
     * 查询企业余额
     *
     * @param tenantId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/5/20 14:09
     */
    Result<?> queryBalanceByBiz(String tenantId);
}
