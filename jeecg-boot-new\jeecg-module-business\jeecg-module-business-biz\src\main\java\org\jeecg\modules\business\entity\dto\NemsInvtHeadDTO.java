package org.jeecg.modules.business.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <p>
 * 核注清单表头
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
public class NemsInvtHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 核注单流水号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String ids;

    /**
     * 委托流水号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyNumber;

    /**
     * 清单编号 （返填 - 海关审批通过后系统自动返填）
     */
    private String bondInvtNo;

    /**
     * 分票序号
     */
    private String partId;

    /**
     * 清单预录入统一编号 （返填 - 第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号）
     */
    private String seqNo;

    /**
     * 变更次数
     */
    private Integer chgTmsCnt;

    /**
     * 备案编号
     */
    private String putrecNo;

    /**
     * 企业内部清单编号 （由企业自行编写）
     */
    private String etpsInnerInvtNo;

    /**
     * 经营企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String bizopEtpsSccd;

    /**
     * 经营企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String bizopEtpsno;

    /**
     * 经营企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String bizopEtpsNm;

    /**
     * 收货企业编号 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String rcvgdEtpsno;

    /**
     * 收发货企业社会信用代码 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String rvsngdEtpsSccd;

    /**
     * 收货企业名称 （返填 - 系统根据手(账)册备案数据自动返填，允许企业修改）
     */
    private String rcvgdEtpsNm;

    /**
     * 申报企业社会信用代码
     */
    private String dclEtpsSccd;

    /**
     * 申报企业编号
     */
    private String dclEtpsno;

    /**
     * 申报企业名称
     */
    private String dclEtpsNm;

    /**
     * 清单申报时间 （返填 - 系统自动反填）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date invtDclTime;

    /**
     * 报关单申报日期 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date entryDclTime;

    /**
     * 对应报关单编号 （清单报关时使用。海关端报关单入库时，反填并反馈企业端）
     */
    private String entryNo;

    /**
     * 关联清单编号 （结转类专用，检控要求复杂，见需求文档）
     */
    private String rltinvtNo;

    /**
     * 关联备案编号 （结转类专用）
     */
    private String rltputrecNo;

    /**
     * 关联报关单编号 （可录入或者系统自动生成报关单后返填二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    private String rltEntryNo;

    /**
     * 关联报关单消费使用单位社会信用代码
     */
    private String rltEntryBizopEtpsSccd;

    /**
     * 关联报关单消费使用单位编号
     */
    private String rltEntryBizopEtpsno;

    /**
     * 关联报关单消费使用单位名称
     */
    private String rltEntryBizopEtpsNm;

    /**
     * 关联报关单境内收发货单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    private String rltEntryRvsngdEtpsSccd;

    /**
     * 关联报关单境内收发货单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填	报关类型为关联报关时必填。二线取消报关的情况下使用，用于生成区外一般贸易报关单。）
     */
    private String rltEntryRcvgdEtpsno;

    /**
     * 关联报关单境内收发货单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    private String rltEntryRcvgdEtpsNm;

    /**
     * 关联报关单申报单位社会统一信用代码 （二线取消报关的情况下使用，用于生成区外一般贸易报关单。暂未使用）
     */
    private String rltEntryDclEtpsSccd;

    /**
     * 关联报关单海关申报单位编码 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    private String rltEntryDclEtpsno;

    /**
     * 关联报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为1时，该字段必填）
     */
    private String rltEntryDclEtpsNm;

    /**
     * 进出口口岸代码
     */
    private String impexpPortcd;

    /**
     * 申报地关区代码
     */
    private String dclplcCuscd;

    /**
     * 进出口标记代码 （返填 - I：进口，E：出口）
     */
    private String impexpMarkcd;
    private String ieFlag;

    /**
     * 料件成品标记代码 （I：料件，E：成品）
     */
    private String mtpckEndprdMarkcd;

    /**
     * 监管方式代码
     */
    private String supvModecd;

    /**
     * 运输方式代码
     */
    private String trspModecd;

    /**
     * 是否报关标志 （1.报关 2.非报关）
     */
    private String dclcusFlag;

    /**
     * 报关类型代码 （1.关联报关 2.对应报关；当报关标志为“1.报关”时，企业可选择“关联报关单”/“对应报关单”；当报关标志填写为“2.非报关”时，报关标志填写为“2.非报关”该项不可填。）
     */
    private String dclcusTypecd;

    /**
     * 核扣标记代码 （返填 - 系统自动反填。0.未核扣、1.预核扣、2.已核扣）
     */
    private String vrfdedMarkcd;

    /**
     * 清单进出卡口状态代码 （系统自动反填。未出卡口，已出卡口。需求不明确，暂留）
     */
    private String invtIochkptStucd;

    /**
     * 预核扣时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date prevdTime;

    /**
     * 正式核扣时间 （返填）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date formalVrfdedTime;

    /**
     * 申请表编号
     */
    private String applyNo;

    /**
     * 流转类型 （非流转类不填写，流转类填写：A：加工贸易深加工结转、B：加工贸易余料结转、C：不作价设备结转、D：区间深加工结转、E：区间料件结转）
     */
    private String listType;

    /**
     * 录入企业编号 （保存首次暂存时IC卡的企业信息）
     */
    private String inputCode;

    /**
     * 录入企业社会信用代码 （返填 - 保存首次暂存时IC卡的企业信息）
     */
    private String inputCreditCode;

    /**
     * 录入单位名称 （保存首次暂存时IC卡的企业信息）
     */
    private String inputName;

    /**
     * 申报人IC卡号 （企业端专用）
     */
    private String icCardNo;

    /**
     * 录入日期 （企业端专用）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date inputTime;

    /**
     * 清单状态 （系统自动反填。1-已申报、C-退单、改单、删单、审核通过）
     */
    private String listStat;

    /**
     * 对应报关单申报单位社会统一信用代码
     */
    private String corrEntryDclEtpsSccd;

    /**
     * 对应报关单申报单位代码 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
     */
    private String corrEntryDclEtpsno;

    /**
     * 对应报关单申报单位名称 （当报关类型DCLCUSTYPECD字段为2时，该字段必填）
     */
    private String corrEntryDclEtpsNm;

    /**
     * 报关单类型 （1-进口报关单	2-出口报关单	3-进境备案清单	4-出境备案清单	5-进境两单一审备案清单	6-出境两单一审备案清单	7-进境备案清单（简化）	8-出境备案清单（简化）	9-转关提前进口报关单	A-转关提前出口报关单	B-转关提前进境备案清单	C-转关提前出境备案清单	D-转关提前进境备案清单（简化）	E-转关提前出境备案清单（简化）	F-出口二次转关单）
     */
    private String decType;

    /**
     * 入库时间 （返填）
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /**
     * 起运运抵国别代码
     */
    private String stshipTrsarvNatcd;

    /**
     * 清单类型 （(SAS项目新增)	标识清单类别，0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：异常补录，默认为0：普通清单）
     */
    private String invtType;

    /**
     * 报关状态 （(SAS项目新增)	标明对应（关联）报关单放行状态，目前只区分 0：未放行，1：已放行。该字段用于区域或物流账册的清单，该类型清单满足两个条件才能核扣：报关单被放行+货物全部过卡）
     */
    private String entryStucd;

    /**
     * 核放单生成标志代码 （(SAS项目新增)	1：未生成、2：部分生成、3：已生成，核放单生成时系统返填）
     */
    private String passportUsedTypeCd;

    /**
     * 备注
     */
    private String rmk;

    /**
     * 创建人员
     */
    private String createPerson;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 0-暂存、1-申报成功、4成功发送海关、5-海关接收成功、6-海关接收失败、B-海关终审通过、C-海关退单、E-删除、N-待导入其他报文、P-预审批通过
     */
    private String status;

    /**
     * 是否审核
     */
    private Boolean audited;

    /**
     * 分运单号
     */
    private String hawb;

    /**
     * 是否核对
     */
    private Boolean checked;

    /**
     * 核对时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date checkDate;

    /**
     * 申报日期 (报关：报关单申报，非报关：核注单申报日期)
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date declarationDate;

    /**
     * 货物类别
     */
    private String goodsType;

    /**
     * 剩余数量
     */
    private BigDecimal balanceQty;

    /**
     * 子系统ID(95 :加工贸易账册系统B1: 加工贸易手册系统B2 :加工贸易担保管理系统B3: 保税货物流转系统二期Z7: 海关特殊监管区域管理系统Z8 :保税物流管理系统)
     */
    private String sysId;

    /**
     * 模板标识
     */
    private String mouldId;

    /**
     * 是否系统生成报关单,报文使用: 1生成,2不生成
     */
    private String genDecFlag;

    /**
     * 是否为自己的单子（true;自己，false:他人）
     */
    private Boolean nonBusiness;

    /**
     * 是否发送
     */
    private Boolean send;

    /**
     * 申报类型代码(1-备案、2-变更、3-作废）
     */
    private String dclTypecd;

    /**
     * 占用信息id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emsFlowsId;

    /**
     * 业务流水
     */
    private String applyId;

    /**
     * 创建人租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tenantId;

    /**
     * 申请人租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dclTenantId;

    /**
     * 计费重量
     */
    private BigDecimal chargedWeight;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 空客导入报关单统一编号
     */
    private String importSeqNo;

    /**
     * 权限字段
     */
    private String rbacIds;

    /**
     * 录入企业ID 创建人的企业ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long inputId;

    /**
     * 是否推核注单
     */
    private String decPushStatus;

    /**
     * 不再推送核注单（1否 1是）
     */
    private String doNotPush;

    /**
     * 出入库单表头流水号
     */
    private String stockHeadId;

    /**
     * 是否生成核放单
     */
    private Boolean createPassPort;

    /**
     * 已核扣日期(已核扣时添加)
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date warehousingDate;

    /**
     * 飞机注册号
     */
    private String aircraftRegistrationNumber;

    /**
     * 飞机类别
     */
    private String aircraftType;

    /**
     * 初复审状态（0未审核、1已初审/未复审、2已复核）
     */
    private String initialReviewStatus;

    /**
     * 初审人
     */
    private String firstTrialBy;

    /**
     * 初审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date firstTrialDate;

    /**
     * 复审人
     */
    private String reviewBy;

    /**
     * 复审时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date reviewDate;

    /**
     * 关联易通关的HeadId
     */
    private String flyId;

    /**
     * 是否确认收货
     */
    private Boolean isReceipt;

    /**
     * 收货时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date receiptDate;

    private String starCreateDate; //创建日期开始
    private String lastCreateDate; //创建日期截止
    private String starDclDate; //申报日期开始
    private String lastDclDate; //申报日期截止
    private String hasSC;
    private String isSend;
    private String storageNos;
    /**
     * 备案序号
     */
    private String putrecSeqno;
    /**
     * 料号
     */
    private String gdsMtno;
    /**
     * 自定义列(导出用)
     */
    private String[] columnList;
}
