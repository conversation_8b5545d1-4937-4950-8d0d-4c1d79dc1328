package org.jeecg.modules.business.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.business.entity.Order;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface IOrderService extends IService<Order> {

    /**
     * 订单列表 -- 分页
     *
     * @param order
     * @param pageNo
     * @param pageSize
     * @param req
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/18 16:24
     */
    Result<?> queryPageList(Integer pageNo, Integer pageSize, Order order, HttpServletRequest req);

    /**
     * 根据ID查询订单信息
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:37
     */
    Result<?> getOrderById(String id);

    /**
     * 保存订单
     *
     * @param order
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:12
     */
    Result<?> saveOrder(Order order);

    /**
     * 删除订单
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 09:27
     */
    Result<?> deleteBatch(String ids);

    /**
     * 操作失效
     *
     * @param id
     * @param status
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 13:24
     */
    Result<?> handleLose(String id, String status);

    /**
     * 导入订单
     *
     * @param request
     * @param response
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/19 15:24
     */
    Result<?> importOrder(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导出订单列表Excel
     *
     * @param request
     * @param response
     * @param order
     * @return void
     * <AUTHOR>
     * @date 2024/9/20 10:02
     */
    void exportOrderXls(HttpServletRequest request, HttpServletResponse response, Order order);

    /**
     * 订单生成进口业务
     *
     * @param ids
     * @param ieFlag
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/9/24 10:47
     */
    Result<?> getCreateBusiness(String ids, String ieFlag, String type);

    /**
     * 从品名库重新带取数据
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2025/2/27 11:19
     */
    Result<?> getRebringData(String ids);
    /**
     * 导入订单（亚是加企业）
     *
     * @param request HttpServletRequest对象，用于获取请求信息
     * @param response HttpServletResponse对象，用于返回响应信息
     * @return 操作结果
     * @throws IOException 处理请求或响应时可能抛出的异常
     */
    Result<?> importOrderByYSJ(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
