<template>
	<div>
		<a-card :bordered="false">
			<!-- 查询区域 -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline" @keyup.enter.native="searchQuery">
					<a-row :gutter="24">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="手册编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入手册编号" v-model="queryParam.emsNo"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="经营单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入经营单位" v-model="queryParam.tradeName"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="数据状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-dict-select-tag
									v-model="queryParam.status"
									type="node-limit"
									dictCode="SZC_STATUS"
									placeholder="请选择数据状态"
								/>
<!--								<a-select placeholder="请选择状态" allowClear showSearch v-model="queryParam.status">-->
<!--									<a-select-option value="1">正在使用</a-select-option>-->
<!--									<a-select-option value="2">暂停使用</a-select-option>-->
<!--									<a-select-option value="3">已核销</a-select-option>-->
<!--								</a-select>-->
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
										<a @click="handleToggleSearch" style="margin-left: 8px">
								{{ toggleSearchStatus ? '收起' : '展开' }}
								<a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
								</a>
							</span>
						</a-col>
						<template v-if="toggleSearchStatus">
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-form-item label="企业内部编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入企业内部编码" v-model="queryParam.copEmsNo"></a-input>
								</a-form-item>
							</a-col>
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-form-item label="预录入统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入预录入统一编号" v-model="queryParam.seqNo"></a-input>
								</a-form-item>
							</a-col>
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-form-item label="有效期" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-range-picker v-model="selectEndDate" format="YYYY-MM-DD"
																	:placeholder="['开始时间', '结束时间']"
																	@change="selectEndDateChange" />
								</a-form-item>
							</a-col>
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-form-item label="录入日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-range-picker v-model="selectInputDate" format="YYYY-MM-DD"
																	:placeholder="['开始时间', '结束时间']"
																	@change="selectInputDateChange" />
								</a-form-item>
							</a-col>
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-form-item label="申报日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-range-picker v-model="selectDeclareDate" format="YYYY-MM-DD"
																	:placeholder="['开始时间', '结束时间']"
																	@change="selectDeclareDateChange" />
								</a-form-item>
							</a-col>



						</template>
					</a-row>
				</a-form>
			</div>
			<!-- 查询区域-END -->

			<!-- 操作按钮区域 -->
<!--			<div class="table-operator">-->
<!--				<a-row>-->
<!--				<a-col :span="9">-->
<!--				<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
<!--					<a-button @click="handleImport" type="primary" icon="upload">导入手册</a-button>-->
<!--					<a-button @click="handleSynEmsHandBook" :loading = 'synLoading' type="primary" icon="redo" >更新手册</a-button>-->
<!--&lt;!&ndash;					<a-button @click="handleUpdateQty" type="primary" icon="upload">更新数量</a-button>&ndash;&gt;-->
<!--					<a-button-->
<!--					@click="batchDel"-->
<!--					v-if="selectedRowKeys.length > 0"-->
<!--					ghost-->
<!--					type="primary"-->
<!--					icon="delete">批量删除-->
<!--				</a-button>-->
<!--				</a-col>-->
<!--				<a-col :span="15">-->
<!--					<span>颜色标识：</span>-->
<!--					<span style="background-color: darkgrey;display: inline-block;width: 30px;-->
<!--					text-align: center;color: white">灰</span>-->
<!--					<span>：过期</span>-->
<!--					<span style="background-color: red;display: inline-block;width: 30px;-->
<!--					text-align: center;margin-left: 10px;color: white">红</span>-->
<!--					<span>：30天以内</span>-->
<!--					<span style="background-color: gold;display: inline-block;width: 30px;-->
<!--					text-align: center;margin-left: 10px;color: white">金</span>-->
<!--					<span>：30-90</span>-->
<!--					<span style="background-color: orange;display: inline-block;width: 30px;-->
<!--					text-align: center;margin-left: 10px;color: white">橙</span>-->
<!--					<span>：90-180</span>-->
<!--					<span style="background-color: green;display: inline-block;width: 30px;-->
<!--					text-align: center;margin-left: 10px;color: white">绿</span>-->
<!--					<span>：180+</span>-->
<!--					</a-col>-->
<!--				</a-row>-->
<!--			</div>-->

			<!-- table区域-begin -->
			<div>
				<!--			新表格-->
				<query-vxe-grid
					class="xGrid-style"
					ref="xGrid"
					size="mini"
					height="500"
					:loading="loading"
					:gridOptions="gridOptions"
					:dataSource="dataSource"
					@checkbox-change="checkboxChangeEvent"
					@checkbox-all="checkboxChangeEvent"
					@cell-dblclick="cellDblclick"
					@page-change="handlePageChange"
				>
					<template v-slot:toolbar_buttons>
						<!-- 操作按钮区域 -->
						<div class="table-operator">
							<a-row>
								<a-col :span="15">
									<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
									<a-button @click="handleImport" type="primary" icon="upload">导入手册</a-button>
									<a-button type="primary" icon="sound" :loading='pushLoading' @click="handlePush">推送</a-button>
									<a-button size="small" @click="showOriginalEdi" type="primary" 
									icon="cloud-sync">回执系统处理信息</a-button>
									<a-button @click="handleSynEmsHandBook" :loading = 'synLoading' type="primary" icon="redo" >更新手册</a-button>
									<!--					<a-button @click="handleUpdateQty" type="primary" icon="upload">更新数量</a-button>-->
									<a-button
										@click="batchDel"
										v-if="selectedRowKeys.length > 0"
										ghost
										type="primary"
										icon="delete">批量删除
									</a-button>
								</a-col>
								<a-col :span="9" style="font-size: 10px;margin-top: 14px">
									<span>颜色标识：</span>
									<span style="background-color: darkgrey;display: inline-block;width: 30px;
					text-align: center;color: white">灰</span>
									<span>：过期</span>
									<span style="background-color: red;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">红</span>
									<span>：30天以内</span>
									<span style="background-color: gold;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">金</span>
									<span>：30-90</span>
									<span style="background-color: orange;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">橙</span>
									<span>：90-180</span>
									<span style="background-color: green;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">绿</span>
									<span>：180+</span>
								</a-col>
							</a-row>
						</div>
					</template>
					<template #action="{ row }">
						<a-dropdown>
							<a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
							<a-menu slot="overlay">
								<a-menu-item>
									<a @click="handleEdit(row)">编辑</a>
								</a-menu-item>
								<a-menu-item>
									<a @click="handleDetail(row)">详情</a>
								</a-menu-item>
								<a-menu-item>
									<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(row)">
										<a>删除</a>
									</a-popconfirm>
								</a-menu-item>
							</a-menu>
						</a-dropdown>

					</template>
					<template #emsNoSlot="{ row }">
						<a @click="handleEdit(row)">{{ row.emsNo }}</a>
					</template>
					<template #ediInfo="{ row }">
					<a @click="handleEditHis(row)">{{ row.ediInfo }}</a>
				</template>
				</query-vxe-grid>
<!--				<a-table-->
<!--					ref="table"-->
<!--					size="small"-->
<!--					:scroll="{ x: true }"-->
<!--					bordered-->
<!--					rowKey="id"-->
<!--					:columns="columns"-->
<!--					:dataSource="dataSource"-->
<!--					:pagination="ipagination"-->
<!--					:loading="loading"-->
<!--					class="j-table-force-nowrap"-->
<!--					:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio'}"-->
<!--					:rowClassName="getRowClassname"-->
<!--					@change="handleTableChange"-->
<!--					:customRow="rowEvent"-->
<!--				>-->
<!--					<template slot="emsNoSlot" slot-scope="text, record">-->
<!--						<a @click="handleEdit(record)">{{ text }}</a>-->
<!--					</template>-->

<!--					<span slot="action" slot-scope="text, record">-->
<!--          <a-dropdown>-->
<!--            <a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>-->
<!--            <a-menu slot="overlay">-->
<!--              <a-menu-item>-->
<!--                <a @click="handleEdit(record)">编辑</a>-->
<!--              </a-menu-item>-->
<!--              <a-menu-item>-->
<!--                <a @click="handleDetail(record)">详情</a>-->
<!--              </a-menu-item>-->
<!--              <a-menu-item>-->
<!--                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record)">-->
<!--                  <a>删除</a>-->
<!--                </a-popconfirm>-->
<!--              </a-menu-item>-->
<!--            </a-menu>-->
<!--          </a-dropdown>-->
<!--        </span>-->
<!--				</a-table>-->
			</div>
			<!-- 新增编辑详情账册 -->
			<add-trade-handbook-edit-modal ref='modalForm' @ok="modalFormOk" @close="forceRerender"/>
		</a-card>
		<!-- 料件、成品、损耗表体列表 -->
<!--		<add-trade-handbook-body-list ref='addTradeHandbookBodyListRef'/>-->
		<!--		手册导入-->
		<import-modal ref='importModal' :downLoadUrl='url.downLoadTempUrl' :importUrl='url.importExcelUrl'
									downLoadButtonText='下载手册导入模板' emsType="B" type="3"
									title='手册导入'
									@closeUploadModal='closeUploadModal' @loadData="loadData(1)"
		></import-modal>
				<!--        edi回执信息查询-->
		<original-edi ref="originalEdi" :visible.sync="recycleOriginalBinVisible" @ok="modalFormOk" />
		<edi-status-list ref="ediStatusList"></edi-status-list>
	</div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import { deleteAction,postAction,getAction,_postAction } from '@/api/manage'
import AddTradeHandbookEditModal from '@/views/AddTradeHandbook/modules/AddTradeHandbookEditModal.vue'
import AddTradeHandbookBodyList from '@/views/AddTradeHandbook/modules/AddTradeHandbookBodyList.vue'
import ImportModal from '@/components/ImportModal/ImportModal.vue'
import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid.vue'
import { MyXGridMixin } from '@/mixins/MyXGridMixin'
import OriginalEdi from "@/views/Business/customs-declaration/OriginalEdi.vue";
import EdiStatusList from '@/views/Business/component/modal/EdiStatusList.vue'
import Vue from 'vue'
import {TENANT_ID, UI_CACHE_DB_DICT_DATA, USER_INFO} from '@/store/mutation-types'
export default {
	name: 'AddTradeHandbookIndex',
	mixins: [MyXGridMixin, mixinDevice],
	components: {
		QueryVxeGrid,
		ImportModal,
		AddTradeHandbookEditModal,
		AddTradeHandbookBodyList,
		OriginalEdi,
		EdiStatusList
	},
	data() {
		return {
			recycleOriginalBinVisible: false,
			pushLoading:false,
			selectEndDate:'',
			selectInputDate:'',
			selectDeclareDate:'',
			dataSource:[],
			synLoading:false,
			queryParam: {
				menuType: '1',
				// status: '1'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			/* 分页参数 */
			ipagination: {
				total: 0,
				currentPage: 1,
				pageSize: 15,
				pageSizes: [15, 30, 50, 100, 200],
				perfect: true
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '手册编号',
					align: 'center',
					dataIndex: 'emsNo',
					scopedSlots: { customRender: 'emsNoSlot' }
				},
				{
					title: '序号',
					align: 'center',
					dataIndex: 'item'
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'tradeName'
				},
				{
					title: '类型',
					align: 'center',
					dataIndex: 'emsType',
					customRender: function(text) {
						if (text == 'B') {
							return 'B-来料加工'
						} else if (text == 'C') {
							return 'C-进料加工'
						}
					}
				},
				{
					title: '出口国',
					align: 'center',
					dataIndex: 'country'
				},
				{
					title: '有效日期',
					align: 'center',
					dataIndex: 'endDate',
					customCell: (record, index) => {
						if(record.endDate){
						const days=this.calculateDays('',record.endDate)
						if(days>180){
							//绿色大于180
							return {
								style: {
									'background-color': 'green'
								},
							}
						}else if(days>90&&days<=180){
							//橙色大于90小于180
							return {
								style: {
									'background-color': 'orange'
								},
							}
						}else if(days>30&&days<=90){
							//金色大于30小于90
							return {
								style: {
									'background-color': 'gold'
								},
							}
						}else if(days<=30&&days>=0){
							//红色大于30
							return {
								style: {
									'background-color': 'red'
								},
							}
						}else {
							//过期
							return {
								style: {
									'background-color': 'darkgrey'
								},
							}
						}
						}
					},
				},
				{
					title: '状态',
					align: 'center',
					dataIndex: 'status',
					customRender: function(text) {
						if (text == 'B') {
							return '海关终审通过'
						} else if (text == '0') {
							return '暂存'
						} else if (text == 'C') {
							return '海关退单'
						} else if (text == 'L') {
							return '结案'
						} else {
							return text
						}
					}
				},
				{
					title: '登记日期',
					align: 'center',
					dataIndex: 'byDate'
				},
				{
					title: '登记人',
					align: 'center',
					dataIndex: 'byName'
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				},
			],
			url: {
				list: '/business/ems/list',
				deleteBatch: '/business/ems/deleteBatch',
				importExcelUrl: '/business/ems/importEms',
				downLoadTempUrl: '/template/通用手册导入模版.xls',
				handleSynEmsHandBook:'/business/ems/synEmsHead',
				push: '/business/ems/handlePush',
			},
		}
	},
	computed: {
		gridOptions() {
			const gridOptions = {
				id: 'Table',
				pagerConfig: {
					currentPage:this.ipagination.currentPage,
					pageSize:this.ipagination.pageSize,
					pageSizes: [15, 30, 50, 100, 200],
					total:this.ipagination.total
				},
				toolbarConfig: {
					perfect: true,
					refresh: {
						query: () => this.loadData(1)
					},
					zoom: true,
					custom: true,
					slots: {
						buttons: 'toolbar_buttons'
					}
				},
				cellClassName({row,column}){
					if (column.property === 'endDate') {
						const start = new Date();
						const end = new Date(row.endDate);
						const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
						if(days>180){
							return 'greenClass'
						}else if(days>90&&days<=180){
							return 'goldClass'
						}else if(days>30&&days<=90){
							return 'orangeClass'
						}else if(days>0&&days<=30){
							return 'redClass'
						}else if(days<=0){
							return 'darkgreyClass'
						}
					}

				},
				columns: [
					{
						type: 'checkbox',
						field: 'checkbox',
						align: 'center',
						width: 50,
						fixed: 'left',
					},
					{
						title: '企业内部编码',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'copEmsNo',
					},
					{
						title: '预录入统一编号',
						align: 'center',
						sorter: false,
						width: 150,
						field: 'seqNo',
					},
					{
						title: '加工贸易手册编号',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'emsNo',
						slots: {
							default: 'emsNoSlot'
						}
					},
					{
						title: '数据状态',
						align: 'center',
						width: 100,
						field: 'status_dictText'
					},
					{
						title: '经营单位代码',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'tradeCode',
					},
					{
						title: '经营单位名称',
						align: 'center',
						sorter: false,
						width: 150,
						field: 'tradeName',
					},
					{
						title: '加工单位代码',
						align: 'center',
						sorter: false,
						width: 120,
						field: 'ownerCode',
					},
					{
						title: '加工单位名称',
						align: 'center',
						sorter: false,
						width: 150,
						field: 'ownerName',
					},
					{
						title: '申报类型',
						align: 'center',
						width: 80,
						field: 'dclTypeCd',
						formatter:function ({ cellValue, row, column }) {
							if (cellValue == '1') {
								return '备案'
							} else if (cellValue == '2') {
								return '变更'
							} else {
								return cellValue
							}
						}
					},
					{
						title: 'EDI状态信息',
						align: 'center',
						sorter: false,
						field: 'ediInfo',
						width: 120,
						slots: {
							default: 'ediInfo'
						}
					},
					{
						title: '有效期',
						align: 'center',
						width: 100,
						field: 'endDate',

					},
					{
						title: '申报日期',
						align: 'center',
						width: 100,
						field: 'declareDate'
					},
					{
						title: '重点标识',
						align: 'center',
						width: 100,
						field: 'col1',
						formatter:function ({ cellValue, row, column }) {
							if (cellValue == '1') {
								return '重点'
							} else {
								return ''
							}
						}
					},
					{
						title: '执行标志',
						align: 'center',
						width: 100,
						field: 'suspendChangeMark',
						formatter:function ({ cellValue, row, column }) {
							if (cellValue == '1') {
								return '正常执行'
							} else {
								return ''
							}
						}
					},
					{
						title: '手册变更次数',
						align: 'center',
						width: 100,
						field: 'chgTmsCnt'
					},
					{
						title: '录入日期',
						align: 'center',
						width: 100,
						field: 'inputDate'
					},
					{
						title: '登记日期',
						align: 'center',
						width: 100,
						field: 'byDate'
					},
					{
						title: '登记人',
						align: 'center',
						width: 100,
						field: 'byName'
					},


					],
			}
			return gridOptions
		}
		},
	created() {
		this.loadData(1)
	},
	mounted() {
		// 页面加载时检查字典缓存
		this.checkAndLoadDictCache()
	},
	methods: {
		handleEditHis(record){
			this.$refs.ediStatusList.visible= true;
			this.$nextTick(()=>{
				this.$refs.ediStatusList.getEdis(record.id,record.seqNo);
			})
		},
			//显示系统内部全部edi回执处理信息
			showOriginalEdi() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (this.selectedRowKeys.length != 1) {
				this.$message.warning('请选择最多一条记录！')
			} else {
				let row = this.selectionRows[0]
				this.recycleOriginalBinVisible = true
				this.$refs.originalEdi.show(this.recycleOriginalBinVisible, row.seqNo, row.id, 'relId')
			}
		},
			/**
		 * 推送报文
		 */
		 handlePush() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择至少一条记录！')
				return
			}
			let pushList = []
			for (let row of this.selectionRows){
				// if (row.appStatus && "0" != row.appStatus){
				// 	this.$message.warning('不允许暂存之后的商检单重新发送！')
				// 	return
				// }
				if (row.send == '1') {
					pushList.push(row.id)
				}
			}
			let msg = `确定要推送报文吗?`
			if (pushList.length > 0) {
				msg = `存在已推送数据，确定要重复推送吗?`
			}
			let that = this
			this.$confirm({
				title: '确认推送',
				content: msg,
				onOk: function() {
					that.pushLoading = true
					_postAction(that.url.push, {
						ids: that.selectedRowKeys.join(','),
						type:that.queryParam.menuType,
						flag:'S'
					})
						.then((res) => {
							if (res.success){
								that.$message.success("推送成功")
								that.$tabs.refresh()
							}else {
								that.$message.warning(res.message)
							}
						}).finally(() => {
						that.pushLoading = false
					})
				}
			})
		},
		handleAdd(){
			//新增，跳转新路由
			this.$router.push({
				path: '/AddTradeHandbook/AddTradeHandbookEdit',
				query: {

				}
			})
		},
		selectEndDateChange(value, dateString){
			this.queryParam.startEndDate = dateString[0].toString()
			this.queryParam.lastEndDate = dateString[1].toString()
		},
		selectInputDateChange(value, dateString){
			this.queryParam.inputStartDate = dateString[0].toString()
			this.queryParam.inputLastDate = dateString[1].toString()
		},
		selectDeclareDateChange(value, dateString){
			this.queryParam.startDeclareDate = dateString[0].toString()
			this.queryParam.lastDeclareDate = dateString[1].toString()
		},
		handleUpdateQty(){
			getAction('/business/ems/xxx', {
				emsNo:this.selectionRows[0].emsNo
			}).then((res) => {
				if (res.success) {

				} else {
				}
			})
				.finally(() => {
					this.synLoading = false
				})
		},
		//更新同步账册表头信息
		handleSynEmsHandBook(){
			if(!this.selectedRowKeys||this.selectedRowKeys.length==0){
				var that = this
			this.$confirm({
				title: '确认操作',
				content: '⚠️注意：当前未选中手册数据，是否全部更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook()
				}
			})



			}else{
				var that = this
this.$confirm({
				title: '确认操作',
				content: '是否对选中手册数据更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook(that.selectionRows[0].emsNo,that.selectionRows[0].seqNo)
				}
			})
			}

		},
		executeSynEmsHandBook(emsNo,seqNo){
			postAction(this.url.handleSynEmsHandBook, {
								type: 'SC',
								emsNo:emsNo,
								seqNo:seqNo
							}).then((res) => {
									if (res.success) {
										this.$message.success('更新成功！')
										this.loadData()
									} else {
										this.$message.warning(res.message || res)
									}
								})
								.finally(() => {
									this.synLoading = false
								})
		},

		handleImport() {
			this.$refs.importModal.fileList = []
			this.$refs.importModal.visible = true
		},
		closeUploadModal() {
			this.$refs.importModal.visible = false
			this.$refs.importModal.fileList = []
			this.loadData(1)
			this.onClearSelected()
		},
		calculateDays(endDate) {
			const start = new Date();
			const end = new Date(endDate);
			const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
			return days
		},
		handleEmptyIcon() {
			this.handleTableHeight('250px', '', '')
		},
		cellDblclick({ row }){
			this.handleEdit(row)
		},
		handleEdit(row){
			this.$router.push({
				path: '/AddTradeHandbook/AddTradeHandbookEdit',
				query: {
					id: row.id
				}
			})
		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
						this.$refs.addTradeHandbookBodyListRef.initHead(record)
					},
					dblclick: () => {
						this.handleEdit(record)
					},
					// ...
				}
			}
		},
		searchReset() {
			this.queryParam = {
				menuType: '1',
				// status: '1'
			}
			this.selectEndDate = ''
			this.selectInputDate = ''
			this.selectDeclareDate = ''
			// this.$refs.addTradeHandbookBodyListRef.clear()
			this.searchQuery()
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids })
							.then(res => {
								if (res.success) {
									//重新计算分页问题
									that.reCalculatePage(that.selectedRowKeys.length)
									that.$message.success(res.message)
									that.loadData()
									that.onClearSelected()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		handleDelete: function (record) {
			var that = this;
			deleteAction(that.url.deleteBatch, {
				ids: record.id
			}).then((res) => {
				if (res.success) {
					//重新计算分页问题
					that.reCalculatePage(1)
					that.$message.success(res.message)
					that.loadData()
					that.onClearSelected()
				} else {
					that.$message.warning(res.message)
				}
			});
		},
		forceRerender() {
			this.loadData()
			// this.onClearSelected()
		},
		// 增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '2') {
				return 'data-rule-invalid'
			}
		},
		/**
		 * 检查字典缓存是否存在，如果不存在则加载
		 */
		 checkAndLoadDictCache() {
			const dictCache = Vue.ls.get(UI_CACHE_DB_DICT_DATA)
			if (!dictCache || Object.keys(dictCache).length === 0) {
				console.log('字典缓存不存在，开始加载...')
				this.refleshCache()
			} else {
				console.log('字典缓存已存在')
			}
		},
			/**
		 * 刷新字典缓存
		 */
		 refleshCache() {
			getAction("sys/dict/refleshCache").then((res) => {
				if (res.success) {
					getAction("sys/dict/queryAllDictItems").then((res) => {
						if (res.success) {
							Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
							Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
							console.log('字典缓存加载完成')
						}
					})
					// this.$message.success("刷新缓存完成！")
				}
			}).catch(e => {
				// this.$message.warn("刷新缓存失败！")
				console.log("刷新失败", e)
			})
		},
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom:10px
}
/deep/ .table-page-search-wrapper .table-page-search-submitButtons{
	margin-bottom:16px
}
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}
/deep/ .vxe-grid--toolbar-wrapper{
	height: 34px;
}
/deep/ .ant-card-body{
	padding-top: 4px;
}
/deep/ .ant-card-body .table-operator{
	width: 100%;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column.greenClass {
	background-color: green;
	color: #ecd8d8;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column.orangeClass {
	background-color: orange;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column.goldClass {
	background-color: gold;
}
.xGrid-style /deep/ .vxe-table .vxe-body--column.redClass {
	background-color: red
}
.xGrid-style /deep/ .vxe-table .vxe-body--column.darkgreyClass {
	background-color: darkgrey;
}
</style>