<template>
	<a-spin :spinning="confirmLoading">
    <a-form-model ref="form" :model="record" :rules="rules" v-enterToNext>
        <table class="my-table">
					<tr>
						<input-item :readonly="true" label="预录入统一编号" iprop="seqNo" v-model="record.seqNo" />
						<input-item :readonly="true" label="加工贸易手册编号" v-model="record.emsNo" />
						<input-item :readonly="disableEdit" label="企业内部编号" required iprop="copEmsNo"
												v-model="record.copEmsNo"/>
					</tr>
					<tr>
						<select-item  ref="selectItem"
													dict-key="erp_customs_ports,name,customs_port_code"
												 label="主管海关"
												 required
												 iprop="masterCustoms"
												 v-model="record.masterCustoms"
						/>
						<select-item  ref="selectItem"
													:options="emsTypeOptions"
													:value-width="1"
													label="手册类型"
													required
													iprop="emsType"
													v-model="record.emsType"
						/>
						<select-item  ref="selectItem"
						:readonly="record.id?false:true"
													:options="dclTypeCdOptions"
													:value-width="1"
													label="申报类型"
													required
													iprop="dclTypeCd"
													v-model="record.dclTypeCd"
						/>


					</tr>
					<tr>
						<input-item :readonly="disableEdit" required iprop="tradeCode" @pressEnter="eventLogic(1)" label="经营单位编码" v-model="record.tradeCode"/>
						<input-item :readonly="disableEdit" required iprop="tradeName" @pressEnter="eventLogic(2)" label="经营单位名称" v-model="record.tradeName"/>
						<input-item :readonly="disableEdit" label="经营单位社会信用代码" @pressEnter="eventLogic(3)" v-model="record.tradeSccd"/>
					</tr>
					<tr>
						<input-item :readonly="disableEdit" required iprop="ownerCode" @pressEnter="eventLogic(4)" label="加工单位编码" v-model="record.ownerCode"/>
						<input-item :readonly="disableEdit" required iprop="ownerName" @pressEnter="eventLogic(5) "label="加工单位名称" v-model="record.ownerName"/>
						<input-item :readonly="disableEdit" label="加工单位社会信用代码" @pressEnter="eventLogic(6)" v-model="record.ownerSccd"/>
					</tr>
					<tr>
						<input-item :readonly="disableEdit" required iprop="declareCode" label="申报单位编码" @pressEnter="eventLogic(7)" v-model="record.declareCode"/>
						<input-item :readonly="disableEdit" required iprop="declareName" label="申报单位名称" @pressEnter="eventLogic(8)" v-model="record.declareName"/>
						<input-item :readonly="disableEdit" label="申报单位社会信用代码" @pressEnter="eventLogic(9)" v-model="record.declareSccd"/>
					</tr>
					<tr>
						<select-item  ref="selectItem"
													dict-key="erp_districts,name,code,1=1"
													label="加工企业地区代码"
													required
													iprop="regionCode"
													v-model="record.regionCode"
						/>
						<select-item  ref="selectItem"
													:options="declareTypeOptions"
													:value-width="1"
													label="申报企业类型"
													required
													iprop="declareType"
													v-model="record.declareType"
						/>
						<date-item label="申报日期" v-model="record.declareDate" :readonly="true"/>
					</tr>
					<tr>
						<select-item  ref="selectItem"
													dict-key="JGFS"
													label="监管方式"
													required
													iprop="tradeTypeCode"
													v-model="record.tradeTypeCode"
						/>
						<input-item :readonly="disableEdit" label="进口合同号" v-model="record.contractNoI"/>
						<input-item :readonly="disableEdit" label="出口合同号" v-model="record.contractNoE"/>
					</tr>
					<tr>
						<date-item required label="有效期" iprop="endDate" v-model="record.endDate"/>
						<select-item  ref="selectItem"
													dict-key="ZMXZ"
													label="征免性质"
													required
													iprop="taxTypeCode"
													v-model="record.taxTypeCode"
						/>
						<select-item  ref="selectItem"
													dict-key="JGZL"
													label="加工种类"
													required
													iprop="processingType"
													v-model="record.processingType"
						/>
					</tr>
					<tr>
						<select-item  ref="selectItem"
													dict-key="erp_customs_ports,name,customs_port_code"
													label="进出口岸"
													iprop="iePort"
													v-model="record.iePort"
						/>
						<select-item  ref="selectItem"
													dict-key="erp_currencies,name,code,currency,1=1"
													label="进口币制"
													required
													iprop="importCurrency"
													v-model="record.importCurrency"
						/>
						<select-item  ref="selectItem"
													dict-key="erp_currencies,name,code,currency,1=1"
													label="出口币制"
													required
													iprop="exportCurrency"
													v-model="record.exportCurrency"
						/>
					</tr>
					<tr>
						<select-item  ref="selectItem"
													:options="codeDeclarationUnitConsumptionOptions"
													:value-width="1"
													label="单耗申报环节代码"
													required
													iprop="codeDeclarationUnitConsumption"
													v-model="record.codeDeclarationUnitConsumption"
						/>
						<select-item  ref="selectItem"
													:options="manualPurposeOptions"
													:value-width="1"
													label="手册用途"
													iprop="manualPurpose"
													v-model="record.manualPurpose"
						/>
						<select-item  ref="selectItem"
													:readonly="true"
													:options="suspendIeOptions"
													:value-width="1"
													label="暂停进出口标记"
													iprop="suspendIe"
													v-model="record.suspendIe"
						/>
					</tr>
					<tr>
						<input-item :readonly="true" label="录入单位编码" v-model="record.inputCode"/>
						<input-item :readonly="true" label="录入单位名称" v-model="record.inputName"/>
						<input-item :readonly="true" label="录入单位社会信用代码" v-model="record.inputCreditCode"/>
					</tr>
					<tr>
						<date-item :readonly="true" label="录入日期" v-model="record.inputDate"/>
						<date-item :readonly="true" label="首次出口日期" v-model="record.firstExportDate"/>
						<select-item  ref="selectItem"
													:readonly="true"
													:options="suspendChangeMarkOptions"
													:value-width="1"
													label="暂停变更标记"
													iprop="suspendChangeMark"
													v-model="record.suspendChangeMark"
						/>
					</tr>
					<tr>
						<input-item :readonly="disableEdit" required iprop="associates" label="企业联系人" v-model="record.associates"/>
						<input-item :readonly="disableEdit" required iprop="telephone" label="联系人手机号" v-model="record.telephone"/>
						<select-item  ref="selectItem"
													:options="[{value: '0',text: '不具备'},{value: '1',text: '具备'}]"
													:value-width="1"
													label="自核资格标记"
													iprop="selfVerificationMark"
													v-model="record.selfVerificationMark"
						/>
					</tr>
					<tr>
						<select-item  ref="selectItem" :readonly="record.id?true:false"
													:options="[{value: '1',text: '重点'}]"
													:value-width="1"
													label="重点标识"
													iprop="col1"
													v-model="record.col1"
													placeholder="首次暂存后不允许修改"
						/>
						<input-item :readonly="true" label="手册变更次数" v-model="record.chgTmsCnt"/>
						<input-item :readonly="disableEdit" label="备注" @pressEnter="handleNoteEnter()"
												v-model="record.note" placeholder="按回车键保存"/>
					</tr>







				</table>

		</a-form-model>
	</a-spin>
</template>
<script>
import InputItem from '@views/declaration/component/m-table-input-item'
import SelectItem from '@views/declaration/component/m-table-select-item'
import DateItem from '@views/declaration/component/m-table-date-item'
import { getAction, httpAction, postAction } from '@/api/manage'
import { ajaxGetDictItems, duplicateCheck } from '@/api/api'
import Vue from "vue";
import {ENTERPRISE_INFO} from "@/store/mutation-types";
import moment from 'moment'

export default {
	name: 'listEmsHeader',
	components: {
		SelectItem,
		InputItem,
		DateItem
	},
	props: {
		disableEdit: {
			type: Boolean,
			default: false
		},
	},

	data() {
		return {
			confirmLoading:false,
			record:{},
			rules: {
				copEmsNo: [{ required: true,message: '请填写企业内部编号!' }],
				masterCustoms: [{ required: true,message: '请选择主管海关!' }],
				tradeCode: [{ checkName: '经营单位',required: true,max:10,validator: this.checkNo }],
				declareCode: [{ checkName: '申报单位',required: true,max:10,validator: this.checkNo }],
				tradeName: [{ required: true, message: '请填写经营单位名称!'}],
				declareName: [{ required: true, message: '请填写申报单位名称!' }],
				tradeSccd: [{ checkName: '经营单位社会信用代码',required: true,max:30,validator: this.checkNo }],
				ownerCode: [{ checkName: '加工单位',required: true,max:10,validator: this.checkNo }],
				ownerName: [{ required: true, message: '请填写加工单位名称!' }],
				ownerSccd: [{ checkName: '加工单位社会信用代码',required: true,max:30,validator: this.checkNo }],
				status: [{ required: true, message: '请填写状态!' }],
				emsType: [{ required: true, message: '请选择手册类型!' }],
				dclTypeCd: [{ required: true, message: '请选择申报类型!' }],
				endDate: [{ required: true, message: '请填写有效日期!' }],
				inputDate: [{ required: true, message: '请填写录入日期!' }],
				declareDate: [{ required: true, message: '请填写申报日期!' }],
				item: [{ type: 'number', message: '序号只能是数字!' }],
				regionCode: [{ required: true, message: '请选择加工企业地区代码!' }],
				declareType: [{ required: true, message: '请选择申报企业类型!' }],
				tradeTypeCode: [{ required: true, message: '请选择监管方式!' }],
				taxTypeCode: [{ required: true, message: '请选择征免性质 !' }],
				processingType: [{ required: true, message: '请选择加工种类!' }],
				importCurrency: [{ required: true, message: '请选择进口币种!' }],
				exportCurrency: [{ required: true, message: '请选择出口币种!' }],
				codeDeclarationUnitConsumption: [{ required: true, message: '请选择单耗申报环节代码!' }],
				associates: [{ required: true, message: '请填写联系人!' }],
				telephone: [{ required: true, message: '请填写联系人手机号!' }],
			},
			customsPortsOptions:[],
			emsTypeOptions: [
				{
					value: 'B',
					text: 'B-来料加工'
				},
				{
					value: 'C',
					text: 'C-进料加工'
				},
				{
					value: 'D',
					text: 'D-设备手册'
				}
			],
			declareTypeOptions:[
				{
					value: '1',
					text: '企业'
				},
				{
					value: '2',
					text: '代理公司'
				},
				{
					value: '3',
					text: '报关行'
				},
			],
			dclTypeCdOptions:[
				{
					value: '1',
					text: '备案'
				},
				{
					value: '2',
					text: '变更'
				},
			],
			codeDeclarationUnitConsumptionOptions:[
				{
					value: '1',
					text: '出口前'
				},
				{
					value: '2',
					text: '报核前'
				},

			],
			manualPurposeOptions:[
				{
					value: '1',
					text: '保税加工'
				},
				{
					value: '2',
					text: '特殊行业'
				},		{
					value: '3',
					text: '保税维修'
				},		{
					value: '4',
					text: '保税研发'
				},
				{
					value: '9',
					text: '其他'
				},



			],
			suspendIeOptions:[
				{
					value: '0',
					text: '未暂停'
				},
				{
					value: '1',
					text: '已暂停'
				},

			],
			suspendChangeMarkOptions:[
				{
					value: '1',
					text: '正常执行'
				},
			],
			entityMap: {
				1: {
					field: 'tradeCode',
					recordKeys: ['tradeName', 'tradeSccd'],
					rKeys: ['departName', 'socialCode'],
					name: '经营单位',
					title: '经营单位'
				},
				2: {
					field: 'tradeName',
					recordKeys: ['tradeCode', 'tradeSccd'],
					rKeys: ['departcd', 'socialCode'],
					name: '经营单位',
					title: '经营单位'
				},
				3: {
					field: 'tradeSccd',
					recordKeys: ['tradeCode', 'tradeName'],
					rKeys: ['departcd', 'departName'],
					name: '经营单位',
					title: '经营单位'
				},
				4: {
					field: 'ownerCode',
					recordKeys: ['ownerName', 'ownerSccd'],
					rKeys: ['departName', 'socialCode'],
					name: '加工单位',
					title: '加工单位'
				},
				5: {
					field: 'ownerName',
					recordKeys: ['ownerCode', 'ownerSccd'],
					rKeys: ['departcd', 'socialCode'],
					name: '加工单位',
					title: '加工单位'
				},
				6: {
					field: 'ownerSccd',
					recordKeys: ['ownerCode', 'ownerName'],
					rKeys: ['departcd', 'departName'],
					name: '加工单位',
					title: '加工单位'
				},
				7: {
					field: 'declareCode',
					recordKeys: ['declareName', 'declareSccd'],
					rKeys: ['departName', 'socialCode'],
					name: '申报单位',
					title: '申报单位'
				},
				8: {
					field: 'declareName',
					recordKeys: ['declareCode', 'declareSccd'],
					rKeys: ['departcd', 'socialCode'],
					name: '申报单位',
					title: '申报单位'
				},
				9: {
					field: 'declareSccd',
					recordKeys: ['declareCode', 'declareName'],
					rKeys: ['departcd', 'departName'],
					name: '申报单位',
					title: '申报单位'
				}
			},
			url: {
				save: '/business/ems/saveEmsHead',
				getById: '/business/ems/getEmsHeadById',
			}


		}

		},
	mounted() {
		this.initEmsHead()
		//海关
		// this.initDictData('erp_customs_ports,name,customs_port_code')
	},
	methods: {
		async eventLogic(data) {
			const entity = this.entityMap[data];
			if (entity && this.record[entity.field]) {
				// 构建请求参数
				const params = {
					socialCode: '',
					departcd: '',
					tradeCiqCode: '',
					customerName: ''
				};
				params[entity.field === 'tradeName' || entity.field === 'ownerName' || entity.field === 'declareName' 
					? 'customerName' 
					: entity.field === 'tradeCode' || entity.field === 'ownerCode' || entity.field === 'declareCode' 
					? 'departcd'
					: entity.field === 'tradeSccd' || entity.field === 'ownerSccd' || entity.field === 'declareSccd' 
					? 'socialCode' :entity.field] = this.record[entity.field];

				try {
					const res = await getAction('/EnterpriseInfo/enterpriseInfo/getCustomerEnterpriseByQueryParams', params);
					if (res.success) {
						// 填充数据
						entity.recordKeys.forEach((key, index) => {
							const resultKey = entity.rKeys[index];
							this.record[key] = res.result[resultKey];
						});
					} else {
						
					}
				} catch (error) {
					
				}
			}
		},
		initEmsHead(){
			if(this.$route.query.id){
				let params = {
					id: this.$route.query.id,
				}
				getAction(this.url.getById, params)
					.then((res) => {
						if (res.success) {
							let record = res.result.records || res.result
							this.record = record
							console.log('eneneneneneneennenenenenennenn')
							this.$emit("getEmsHead",this.record)
						} else {
							// 失败
							this.$message.warning(res.message || res)
						}
					})
					.finally(() => {
						this.confirmLoading = false
						this.$forceUpdate()
					})
			}else {
				// 新增
				this.confirmLoading = false
				//新增时候需要默认一些值 by20250625
				this.record.dclTypeCd = '1'; //默认备案
				const enterpriseInfo = Vue.ls.get(ENTERPRISE_INFO)
			if (enterpriseInfo) {
				this.record.inputCode = enterpriseInfo.customsDeclarationCode
				this.record.inputCreditCode = enterpriseInfo.unifiedSocialCreditCode
				this.record.inputName = enterpriseInfo.enterpriseFullName
				this.record.tradeCode = enterpriseInfo.customsDeclarationCode
				this.record.tradeName = enterpriseInfo.enterpriseFullName
				this.record.tradeSccd = enterpriseInfo.unifiedSocialCreditCode
				this.record.ownerCode = enterpriseInfo.customsDeclarationCode
				this.record.ownerName = enterpriseInfo.enterpriseFullName
				this.record.ownerSccd = enterpriseInfo.unifiedSocialCreditCode
				this.record.declareCode = enterpriseInfo.customsDeclarationCode
				this.record.declareName = enterpriseInfo.enterpriseFullName
				this.record.declareSccd = enterpriseInfo.unifiedSocialCreditCode
			}
				this.record.inputDate = moment(new Date()).format('YYYYMMDD')
				//有效期为 当前日期+1年
				this.record.endDate = moment(new Date()).add(1, 'years').format('YYYYMMDD')
				this.record.codeDeclarationUnitConsumption = '1' //默认出口前
				//手册变更次数为 0
				this.record.chgTmsCnt = '0'
				this.$forceUpdate()
			}
		},
		handleNoteEnter(){
			this.saveForm()
		},
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length > 0) {
				if (dictCode.includes('erp_customs_ports')) {
					let allOptions = []
					dictOptions.forEach(item => {
						allOptions.push({
							text: item.value + ' | ' + item.title,
							value: item.value
						})
					})
					this.customsPortsOptions = allOptions
				} else if (dictCode.includes('erp_countries')) {
					let allOptions = []
					dictOptions.forEach(item => {
						allOptions.push({
							text: item.value + ' | ' + item.title,
							value: item.value
						})
					})
					this.countries = allOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length > 0) {
							if (dictCode.includes('erp_customs_ports')) {
								let allOptions = []
								dictOptions.forEach(item => {
									allOptions.push({
										text: item.value + ' | ' + item.title,
										value: item.value
									})
								})
								this.customsPortsOptions = allOptions
							} else if (dictCode.includes('erp_countries')) {
								let allOptions = []
								dictOptions.forEach(item => {
									allOptions.push({
										text: item.value + ' | ' + item.title,
										value: item.value
									})
								})
								this.countries = allOptions
							}
						}
					}
				})
			}
		},
		checkNo (rule, value, callback) {
			if (rule.required) {
				if (this.isEmpty(value)) {
					callback(new Error(`请输入${rule.checkName}!`))
				}
			}
			if (!this.isEmpty(value)) {
				let reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/
				if (rule.checkNum) {
					if (!reg.test(value)) {
						callback(new Error('请输入数字!'))
					}
				}
				if (value < 0) {
					callback(new Error('不能输入负数!'))
				}
				if (!this.isEmpty(rule.max) && value.length > rule.max) {
					callback(new Error(`长度不能大于${rule.max}位!`))
				}
				if ((value.toString()).indexOf('.') != -1) {
					callback(new Error('不能含有小数点!'))
				}
			}
			callback()
		},
		handleSave() {
			this.saveForm()
		},
		async saveForm() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.confirmLoading = true
					console.log('最终保存的加贸账册数据：', this.record)
					httpAction(this.url.save, this.record, 'post')
						.then((res) => {
							if (res.success) {
								that.$message.success('保存成功！')
								if (res.result.id) {
									this.$tabs.close({
										to: `/AddTradeHandbook/AddTradeHandbookEdit?id=${res.result.id}`
									})
								}
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.confirmLoading = false
						})
				} else {
					this.$message.error('请填写表单必填项！')
				}
			})
		},

	}




}



</script>