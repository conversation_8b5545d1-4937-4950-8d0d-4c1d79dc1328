<template>
	<a-select
		ref="select"
		v-if="regularFlag"
		showSearch
		:disabled="disabled"
		:placeholder="placeholder"
		optionFilterProp="children"
		style="width: 100%"
		@change="handleChange"
		:filterOption="filterOption"
		v-model="selectedValue"
		allowClear
		:notFoundContent="loading ? undefined : null"
	>
		<a-spin v-if="loading" slot="notFoundContent" size="small" />
		<a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.text }}</a-select-option>
	</a-select>
  <a-select
		ref="select"
		v-else-if="async"
    showSearch
    labelInValue
    :disabled="disabled"
    :getPopupContainer="getParentContainer"
    @search="loadData"
    :placeholder="placeholder"
    v-model="selectedAsyncValue"
    style="width: 100%"
    :filterOption="false"
    @change="handleAsyncChange"
    allowClear
		:dropdownMatchSelectWidth="false"
    :notFoundContent="loading ? undefined : null"
  >
    <a-spin v-if="loading" slot="notFoundContent" size="small" />
    <a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.text }}</a-select-option>
  </a-select>

	<a-select
		ref="select"
		v-else-if="need === 'text'"
		:getPopupContainer="getParentContainer"
		showSearch
		:disabled="disabled"
		:placeholder="placeholder"
		optionFilterProp="children"
		style="width: 100%"
		@change="handleChange"
		:filterOption="filterOption"
		v-model="selectedValue"
		allowClear
		:notFoundContent="loading ? undefined : null"
	>
		<a-spin v-if="loading" slot="notFoundContent" size="small" />
		<a-select-option v-for="d in options" :key="d.value" :value="d.text">{{ d.text }}</a-select-option>
	</a-select>

	<a-select
		ref="select"
		v-else-if="need === 'value'"
		:getPopupContainer="getParentContainer"
		showSearch
		:disabled="disabled"
		:placeholder="placeholder"
		optionFilterProp="children"
		style="width: 100%"
		@change="handleChange"
		:filterOption="filterOption"
		v-model="selectedValue"
		allowClear
		:notFoundContent="loading ? undefined : null"
	>
		<a-spin v-if="loading" slot="notFoundContent" size="small" />
		<a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.value }}</a-select-option>
	</a-select>
<!--	报关单用到，显示形式：value-text-->
	<a-select
		ref="select"
		v-else-if="type==='dec'"
		showSearch
		labelInValue
		:disabled="disabled"
		:getPopupContainer="getParentContainer"
		@search="loadData"
		:placeholder="placeholder"
		v-model="selectedAsyncValue"
		style="width: 100%"
		:filterOption="false"
		@change="handleAsyncChange"
		allowClear
		:dropdownMatchSelectWidth="false"
		:notFoundContent="loading ? undefined : null"
	>
		<a-spin v-if="loading" slot="notFoundContent" size="small" />
		<a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.value }}-{{ d.text }}</a-select-option>
	</a-select>


  <a-select
		ref="select"
    v-else
    showSearch
    :disabled="disabled"
    :placeholder="placeholder"
    optionFilterProp="children"
    style="width: 100%"
    @change="handleChange"
    :filterOption="filterOption"
		class="bgcolour"
    v-model="selectedValue"
    allowClear
    :notFoundContent="loading ? undefined : null"
		option-label-prop="label"
  >
    <a-spin v-if="loading" slot="notFoundContent" size="small" />
    <a-select-option v-for="d in options" :key="d.value" :label="d.text" :value="d.value">{{d.value +'-'+ d.text }}</a-select-option>
  </a-select>
</template>

<script>
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import debounce from 'lodash/debounce'
import { getAction } from '../../api/manage'

export default {
  name: 'JSearchSelectTag',
  props: {
    disabled: Boolean,
    value: [String, Number],
    dictOptions: Array,
    async: Boolean,
    placeholder: {
      type: String,
      default: '请选择',
      required: false,
    },
    dict: {
      type: String,
      default: '',
      required: false,
    },
		need: {
      type: String,
      default: '',
      required: false,
    },
    popContainer: {
      type: String,
      default: '',
      required: false,
    },
		type: {
			type: String,
			default: '',
			required: false,
		},
    pageSize: {
      type: Number,
      default: 10,
      required: false,
    },
    getPopupContainer: {
      type: Function,
      default: null,
    },
    isCurrer: {
      type: Boolean,
      default: false,
    },
		//固定下拉位置标识
		regularFlag:{
			type: Boolean,
			default: false,
		}
  },
  data() {
    this.loadData = debounce(this.loadData, 800) //消抖
    this.lastLoad = 0
    return {
      loading: false,
      selectedValue: [],
      selectedAsyncValue: [],
      options: [],
      currenciesList: [],
    }
  },
  created() {
    this.initDictData()
  },
  // mounted() {
  //   if (this.isCurrer) {
  //     this.getCurrenciesInfo()
  //   }
  // },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (!val) {
          if (val == 0) {
            this.initSelectValue()
          } else {
            this.selectedValue = []
            this.selectedAsyncValue = []
          }
        } else {
          this.initSelectValue()
        }
      },
    },
    dict: {
      handler() {
        this.initDictData()
      },
    },
    dictOptions: {
      deep: true,
      handler(val) {
        if (val && val.length > 0) {
          this.options = [...val]
        }
      },
    },
  },
  methods: {
    initSelectValue() {
      if (this.async) {
        if (!this.selectedAsyncValue || !this.selectedAsyncValue.key || this.selectedAsyncValue.key != this.value) {
          console.log('这才请求后台')

          var keyArray = this.dict.split(',')
          var newValue = this.value
          var newDict = this.dict
          if (keyArray.length > 3) {
            newDict = keyArray[0]
            for (let i = 1; i < 3; i++) {
              newDict += ',' + keyArray[i]
            }
            newValue = this.value + ',' + keyArray[3]
          }
          // getAction(`/sys/dict/loadDictItem/${this.dict}`,{key:this.value}).then(res=>{
          getAction(`/sys/dict/loadDictItem/${newDict}`, { key: newValue }).then((res) => {
            if (res.success) {
              let obj = {
                key: this.value,
                label: res.result,
              }
              this.selectedAsyncValue = { ...obj }
            }
          })
        }
      } else {
        this.selectedValue = this.value.toString()
      }
    },
    loadData(value) {
      console.log('数据加载', value)
      this.lastLoad += 1
      const currentLoad = this.lastLoad
      this.options = []
      this.loading = true
      // 模糊查询用  当查询为一个字时，pagesize为设定的条数。当查询大于一个字时，不设定pagesize
      let pageSize = ''
      if(value){
        let arr = value.split('');
        if(arr.length > 1){
          pageSize = ''
        }else{
          pageSize = this.pageSize
        }
      }else{
        pageSize = this.pageSize
      }
      // 字典code格式：table,text,code
      getAction(`/sys/dict/loadDict/${this.dict}`, { keyword: value, pageSize: pageSize }).then((res) => {
        this.loading = false
        if (res.success) {
          if (currentLoad != this.lastLoad) {
            return
          }
          this.options = res.result
					//暂时处理，国家字典并且为全部的,并且搜索值是中文 需要去重
					if(this.dict.indexOf('erp_countries')>-1&&this.dict.indexOf('isenabled')<0&&this.containsChinese(value)){
						this.options = this.unique(this.options,'text')
					}

        } else {
          this.$message.warning(res.message)
        }
      })
    },
		unique(arr, key) {
			const res = new Map();
			return arr.filter((arr) => !res.has(arr[key]) && res.set(arr[key], 1));
		},

		containsChinese(str) {
	return /[\u4e00-\u9fff]/.test(str);
},
    getCurrenciesInfo() {
      // getAction('/dictionary/erpCurrencies/getCurrenciesList')
      //   .then((res) => {
      //     if (res.success) {
      //       this.currenciesList = res.result.list
      //       for (let i = 0; i < this.options.length; i++) {
      //         for (let j = 0; j < this.currenciesList.length; j++) {
      //           if (this.options[i].value == this.currenciesList[j].code) {
      //             this.options[i].text = this.options[i].text + ' ' + this.currenciesList[j].name
      //             console.log(this.options[i].text,'99')
      //           }
      //         }
      //       }
      //     }
      //   })
      //   .catch()
    },
    async initDictData() {
      let that = this
      if (!this.async) {
        //如果字典项集合有数据
        if (this.dictOptions && this.dictOptions.length > 0) {
          this.options = [...this.dictOptions]
        } else {
          //根据字典Code, 初始化字典数组
          let dictStr = ''
          if (this.dict) {
            let arr = this.dict.split(',')
            if (arr[0].indexOf('where') > 0) {
              let tbInfo = arr[0].split('where')
              dictStr = tbInfo[0].trim() + ',' + arr[1] + ',' + arr[2] + ',' + encodeURIComponent(tbInfo[1])
            } else {
              dictStr = this.dict
            }
            if (this.dict.indexOf(',') == -1) {
              //优先从缓存中读取字典配置
              if (getDictItemsFromCache(this.dictCode)) {
                this.options = getDictItemsFromCache(this.dictCode)
                return
              }
            }
            await ajaxGetDictItems(dictStr, null)
              .then((res) => {
                if (res.success) {
                  this.options = res.result
                }
              })
              .then(() => {
                if (this.isCurrer) {
                  getAction('/dictionary/erpCurrencies/getCurrenciesList')
                    .then((res) => {
                      if (res.success) {
                        this.currenciesList = res.result.list
                        for (let i = 0; i < this.options.length; i++) {
                          for (let j = 0; j < this.currenciesList.length; j++) {
                            if (this.options[i].value == this.currenciesList[j].code) {
                              this.options[i].text = this.options[i].text + ' ' + this.currenciesList[j].name
                            }
                          }
                        }
                      }
                    })
                    .catch()
                }
              })
          }
        }
      } else {
        if (!this.dict) {
          console.error('搜索组件未配置字典项')
        } else {
          //异步一开始也加载一点数据
          this.loading = true
					await getAction(`/sys/dict/loadDict/${this.dict}`, { pageSize: this.pageSize, keyword: '' }).then((res) => {
            this.loading = false
            if (res.success) {
              this.options = res.result
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleChange(selectedValue) {
      console.log('selectedValue', selectedValue)
      this.selectedValue = selectedValue
      let i = this.options.find(item => item.value === selectedValue)
      this.callback(i?i.text:'')
    },
    handleAsyncChange(selectedObj) {
      //update-begin-author:scott date:20201222 for:【搜索】搜索查询组件，删除条件，默认下拉还是上次的缓存数据，不好 JT-191
      if (selectedObj) {
        this.selectedAsyncValue = selectedObj
        this.selectedValue = selectedObj.key
      } else {
        this.selectedAsyncValue = null
        this.selectedValue = null
        this.options = null
        this.loadData('')
      }
      this.callback()
      //update-end-author:scott date:20201222 for:【搜索】搜索查询组件，删除条件，默认下拉还是上次的缓存数据，不好 JT-191
    },
    callback(text) {
      this.$emit('change', this.selectedValue,text)
    },
    setCurrentDictOptions(dictOptions) {
      this.options = dictOptions
    },
    getCurrentDictOptions() {
      return this.options
    },
    getParentContainer(node) {
      if (typeof this.getPopupContainer === 'function') {
        return this.getPopupContainer(node)
      } else if (!this.popContainer) {
        return node.parentNode
      } else {
        return document.querySelector(this.popContainer)
      }
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
}
</script>

<style scoped>
.bgcolour  >>>.ant-select-selection{
background:#FAFFBD
}
</style>