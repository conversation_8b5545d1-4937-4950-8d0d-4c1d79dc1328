package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 手账册表头
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@Accessors(chain = true)
@TableName("pts_ems_head")
public class PtsEmsHead implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人租户ID(登录方租户ID)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 授权使用租户ID(可以是多个,以逗号分开)
     */
    @TableField("AUTHORIZED_ID")
    private String authorizedId;

    /**
     * 手册编号
     */
    @TableField("EMS_NO")
    private String emsNo;

    /**
     * 序号(自定义序号,排序用)
     */
    @TableField("ITEM")
    private Integer item;

    /**
     * 企业内部编号
     */
    @TableField("COP_EMS_NO")
    private String copEmsNo;

    /**
     * 经营单位
     */
    @TableField("TRADE_CODE")
    private String tradeCode;

    /**
     * 经营单位社会信用代码
     */
    @TableField("TRADE_SCCD")
    private String tradeSccd;

    /**
     * 经营单位名称
     */
    @TableField("TRADE_NAME")
    private String tradeName;

    /**
     * 加工单位（物流账册为仓库代码）
     */
    @TableField("OWNER_CODE")
    private String ownerCode;

    /**
     * 加工单位名称（物流账册为仓库名称）
     */
    @TableField("OWNER_NAME")
    private String ownerName;

    /**
     * 加工单位社会信用代码
     */
    @TableField("OWNER_SCCD")
    private String ownerSccd;

    /**
     * 申报单位
     */
    @TableField("DECLARE_CODE")
    private String declareCode;

    /**
     * 申报单位社会信用代码
     */
    @TableField("DECLARE_SCCD")
    private String declareSccd;

    /**
     * 申报单位名称
     */
    @TableField("DECLARE_NAME")
    private String declareName;

    /**
     * 手账册类型(加贸手册类型：B-来料加工 C-进料加工;加贸账册类型：1-E账册 2-H账册 3-耗料 4-工单;物流账册类型:TW-账册;L-账册)
     */
    @TableField("EMS_TYPE")
    private String emsType;

    /**
     * 自定义用途(物流账册：M贸易 W 物流)
     */
    @TableField("USE_TYPE")
    private String useType;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("BEGIN_DATE")
    private Date beginDate;

    /**
     * 结束有效日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("END_DATE")
    private Date endDate;

    /**
     * 进口货物项数
     */
    @TableField("IMG_ITEMS")
    private Integer imgItems;

    /**
     * 出口货物项数
     */
    @TableField("EXG_ITEMS")
    private Integer exgItems;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 录入日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("INPUT_DATE")
    private Date inputDate;

    /**
     * 申报日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("DECLARE_DATE")
    private Date declareDate;

    /**
     * 展期
     */
    @TableField("MODIFY_MARK")
    private String modifyMark;

    /**
     * 主管海关
     */
    @Dict(dictTable = "erp_customs_ports", dicText = "name", dicCode = "customs_port_code")
    @TableField("MASTER_CUSTOMS")
    private String masterCustoms;

    /**
     * 自定义状态 详见字典
     */
    @Dict(dicCode = "SZC_STATUS")
    @TableField("STATUS")
    private String status;

    /**
     * 锁标识
     */
    @TableField("LOCK_TAG")
    private String lockTag;

    /**
     * 登记日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("BY_DATE")
    private Date byDate;

    /**
     * 登记人
     */
    @TableField("BY_NAME")
    private String byName;

    /**
     * 记账模式 (0不累计 1累计)
     */
    @TableField("KEEPING_MODE")
    private Integer keepingMode;

    /**
     * 出口国
     */
    @TableField("COUNTRY")
    private String country;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    /**
     * 预录入统一编号
     * 20240104
     */
    @TableField("SEQ_NO")
    private String seqNo;
    /**
     * 申报单位类型
     * 20240104
     */
    @TableField("DECLARE_TYPE")
    private String declareType;
    /**
     * 申报类型 1备案2变更
     * 20240104
     */
    @TableField("DCL_TYPECD")
    private String dclTypeCd;
    /**
     * 区域场所类型
     * 20240104
     */
    @Dict(dicCode = "QYCSLB")
    @TableField("REGIONAL_SITE_TYPE")
    private String regionalSiteType;
    /**
     * 仓库面积
     * 20240104
     */
    @TableField("AREA")
    private BigDecimal area;
    /**
     * 仓库容积
     * 20240104
     */
    @TableField("VOLUME")
    private String volume;
    /**
     * 仓库地址
     * 20240104
     */
    @TableField("ADDRESS")
    private String address;
    /**
     * 联系人
     * 20240104
     */
    @TableField("ASSOCIATES")
    private String associates;
    /**
     * 联系电话
     * 20240104
     */
    @TableField("TELEPHONE")
    private String telephone;
    /**
     * 企业类型
     * 20240104
     */
    @TableField("BUSINESS_TYPE")
    private String businessType;
    /**
     * 录入单位编码
     */
    @TableField("INPUT_CODE")
    private String inputCode;

    /**
     * 录入单位社会信用代码
     */
    @TableField("INPUT_CREDIT_CODE")
    private String inputCreditCode;

    /**
     * 录入单位名称
     */
    @TableField("INPUT_NAME")
    private String inputName;

    /**
     * 备案批准日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("RECORD_DATE")
    private Date recordDate;
    /**
     * 变更批准日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("CHANGE_DATE")
    private Date changeDate;

    /**
     * 初审状态
     */
    private String firstEStatus;

    /**
     * 复审状态
     */
    private String reEStatus;
    /**
     * 核销周期
     */
    private String verificationCycle;

    /**
     * 是否发送
     */
    @TableField("SEND")
    private String send;
    /**
     * 变更次数(首次申请备案时填0)
     */
    @TableField("CHG_TMS_CNT")
    private Integer chgTmsCnt;

    /**
     * 加工企业地区代码 20250414
     */
    private String regionCode;
    /**
     * 监管方式 20250414
     */
    private String tradeTypeCode;

    /**
     * 进口合同号 20250414
     */
    private String contractNoI;
    /**
     * 出口合同号 20250414
     */
    private String contractNoE;
    /**
     * 征免性质 20250414
     */
    private String taxTypeCode;

    /**
     * 加工种类	 20250414
     */
    private String processingType;

    /**
     * 进出口岸 20250414
     */
    private String iePort;
    /**
     * 进口币制 20250414
     */
    private String importCurrency;
    /**
     * 出口币制     20250414
     */
    private String exportCurrency;
    /**
     * 单耗申报环节代码 20250414
     */
    private String codeDeclarationUnitConsumption;
    /**
     * 手册用途 20250414
     */
    private String manualPurpose;

    /**
     * 暂停进出口标记	20250414
     */
    private String suspendIe;
    /**
     * 首次出口日期 20250414
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date firstExportDate;

    /**
     * 暂停变更标记	 20250414
     */
    private String suspendChangeMark;
    /**
     * 自核资格标记 20250414
     */
    private String selfVerificationMark;

    /**
     * 菜单类型(1:手册;2:加贸账册;3:物流账册)
     * 1加贸手册(B:来料加工;C:进料加工)
     * 2加贸账册(1:E账册;2:H账册;3:耗料;4:工单)
     * 3物流账册(TW:账册;L:账册)
     */
    @TableField(exist = false)
    private String menuType;

    /**
     * 手账册归并后料件集合
     */
    @TableField(exist = false)
    private List<PtsEmsAimg> emsAimgList;
    /**
     * 手账册归并后成品集合
     */
    @TableField(exist = false)
    private List<PtsEmsAexg> emsAexgList;
    /**
     * 手账册单损耗集合
     */
    @TableField(exist = false)
    private List<PtsEmsCm> emsCmList;

    @TableField(exist = false)
    private String inDateAimg1;
    @TableField(exist = false)
    private String inDateAimg2;
    @TableField(exist = false)
    private String outDateAexg1;
    @TableField(exist = false)
    private String outDateAexg2;
    @TableField(exist = false)
    private String actualInUsd;
    @TableField(exist = false)
    private String actualOutUsd;
    @TableField(exist = false)
    private Integer totalInList;
    @TableField(exist = false)
    private Integer totalOutList;
    /**
     * 重点标识 1-重点 账册类型为5-企业为单元不允许填写，不允许变更。
     */
    private String col1;
    @TableField(exist = false)
    private String startEndDate;
    @TableField(exist = false)
    private String lastEndDate;
    @TableField(exist = false)
    private String inputStartDate;
    @TableField(exist = false)
    private String inputLastDate;
    @TableField(exist = false)
    private String startDeclareDate;
    @TableField(exist = false)
    private String lastDeclareDate;
    @TableField(exist = false)
    private String ediInfo;


}
