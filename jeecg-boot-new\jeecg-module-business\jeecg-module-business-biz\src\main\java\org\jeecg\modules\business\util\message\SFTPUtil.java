package org.jeecg.modules.business.util.message;

import com.jcraft.jsch.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * sftp工具类
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/6/18 13:50
 */
public class SFTPUtil {
    private static final Logger logger = LoggerFactory.getLogger(SFTPUtil.class);

    private MsgFtpConfig ftpConfig;
    private ChannelSftp sftp;
    private Session session;

    public SFTPUtil(MsgFtpConfig ftpConfig) {
        this.ftpConfig = ftpConfig;
    }

    /**
     * [新增] 静态方法: 从指定路径下载文件
     * @param url FTP服务器hostname
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param remotePath FTP服务器上的相对路径
     * @param localPath 下载后保存到本地的路径
     * @param fileName 文件名称
     * @return boolean 下载是否成功
     */
    public static boolean downFileByPath(String url, int port, String username, String password, String remotePath, String localPath, String fileName) {
        Session staticSession = null;
        ChannelSftp staticSftp = null;
        try {
            JSch jsch = new JSch();
            staticSession = jsch.getSession(username, url, port);
            staticSession.setPassword(password);
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            staticSession.setConfig(config);
            staticSession.connect();

            Channel channel = staticSession.openChannel("sftp");
            channel.connect();
            staticSftp = (ChannelSftp) channel;

            // 切换到远程目录
            staticSftp.cd(remotePath);

            // 确保本地目录存在
            File path = new File(localPath);
            if (!path.exists()) {
                path.mkdirs();
            }

            File localFile = new File(localPath + File.separator + fileName);
            try (OutputStream os = Files.newOutputStream(localFile.toPath())) {
                staticSftp.get(fileName, os);
                logger.info("文件下载成功！: {}", fileName);
            }
            return true;
        } catch (Exception e) {
            logger.error("SFTP 下载异常！", e);
            return false;
        } finally {
            if (staticSftp != null && staticSftp.isConnected()) staticSftp.disconnect();
            if (staticSession != null && staticSession.isConnected()) staticSession.disconnect();
        }
    }

    /**
     * [新增] 静态方法: 删除指定路径的文件
     * @param url FTP服务器hostname
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param remotePath FTP服务器上的相对路径
     * @param fileName 文件名称
     * @return boolean 删除是否成功
     */
    public static boolean deleteFileByPath(String url, int port, String username, String password, String remotePath, String fileName) {
        Session staticSession = null;
        ChannelSftp staticSftp = null;
        try {
            JSch jsch = new JSch();
            staticSession = jsch.getSession(username, url, port);
            staticSession.setPassword(password);
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            staticSession.setConfig(config);
            staticSession.connect();

            Channel channel = staticSession.openChannel("sftp");
            channel.connect();
            staticSftp = (ChannelSftp) channel;

            String fullPath = remotePath + "/" + fileName;
            staticSftp.rm(fullPath);
            logger.info("远程文件删除成功！: {}", fullPath);
            return true;

        } catch (Exception e) {
            logger.error("SFTP 删除异常！", e);
            return false;
        } finally {
            if (staticSftp != null && staticSftp.isConnected()) staticSftp.disconnect();
            if (staticSession != null && staticSession.isConnected()) staticSession.disconnect();
        }
    }

    /**
     * 上传文件 (默认文件类型)
     * @param filename 文件名
     * @param input 文件输入流
     * @return boolean
     */
    public boolean upload(String filename, InputStream input) {
        return upload(filename, input, null);
    }

    /**
     * 上传文件 (指定文件类型)
     * @param filename 文件名
     * @param input 文件输入流
     * @param fileType 文件类型后缀, e.g., ".ok"
     * @return boolean
     */
    public boolean upload(String filename, InputStream input, String fileType) {
        try {
            if (!connect()) return false;

            // 确保远程目录存在
            sftp.cd("/"); // 回到根目录
            createAndChangeDir(ftpConfig.getRemoteSendPath());

            String targetName = filename + (fileType != null ? fileType : "");
            sftp.put(input, targetName);
            logger.info("SFTP 上传成功: {}", targetName);
            return true;
        } catch (Exception e) {
            logger.error("SFTP 上传失败: {}", e.getMessage(), e);
            return false;
        } finally {
            try {
                if (input != null) input.close();
            } catch (Exception ignored) {}
            disconnect();
        }
    }

    /**
     * 下载文件
     * 此方法会从配置的远程接收路径下载所有文件, 成功后删除远程文件.
     * @return 包含文件本地路径和修改日期的字符串列表 (e.g., ["/path/file1.txt,2025-06-18 10:00:00"])
     */
    public List<String> download() {
        List<String> list = new ArrayList<>();
        if (!connect()) {
            logger.error(">>>>>>>>> SFTP 连接失败...");
            return list;
        }

        try {
            // 切换到远程接收目录
            sftp.cd(ftpConfig.getRemoteReceiptPath());
            Vector<ChannelSftp.LsEntry> files = sftp.ls(".");
            logger.debug("方法[download]: 远程目录中的文件或文件夹个数是: {}", files.size());

            // 确保本地接收目录存在
            File localDir = new File(ftpConfig.getLocalReceiptPath());
            if (!localDir.exists()) {
                localDir.mkdirs();
            }

            for (ChannelSftp.LsEntry entry : files) {
                // 跳过目录和特殊链接
                if (entry.getAttrs().isDir() || entry.getAttrs().isLink() || ".".equals(entry.getFilename()) || "..".equals(entry.getFilename())) {
                    continue;
                }

                String filename = entry.getFilename();
                logger.info("扫描到远程文件: {}", filename);

                String localFilePath = ftpConfig.getLocalReceiptPath() + File.separator + filename;

                try (OutputStream os = new FileOutputStream(localFilePath)) {
                    // 下载文件
                    sftp.get(filename, os);

                    // 获取文件修改时间
                    SftpATTRS attrs = entry.getAttrs();
                    long modTime = (long)attrs.getMTime() * 1000L;
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String date = formatter.format(new Date(modTime));

                    list.add(localFilePath + "," + date);
                    logger.info("文件下载成功: {}", filename);

                    // 下载成功后删除远程文件
                    sftp.rm(filename);
                    logger.info("远程文件删除成功: {}", filename);

                } catch (Exception e) {
                    logger.error("文件下载或删除失败: {}", filename, e);
                    // 如果下载或删除过程中出错，清理本地可能已创建的不完整文件
                    new File(localFilePath).delete();
                }
            }
        } catch (SftpException e) {
            logger.error("SFTP 下载操作异常: {}", e.getMessage(), e);
        } finally {
            disconnect();
        }
        return list;
    }

    /**
     * 连接SFTP服务器
     * @return boolean
     */
    private boolean connect() {
        try {
            // 如果已有连接, 先断开
            if (session != null && session.isConnected()) {
                disconnect();
            }

            JSch jsch = new JSch();
            session = jsch.getSession(ftpConfig.getUsername(), ftpConfig.getUrl(), ftpConfig.getPort());
            session.setPassword(ftpConfig.getPassword());

            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);

            session.connect();
            Channel channel = session.openChannel("sftp");
            channel.connect();

            sftp = (ChannelSftp) channel;
            logger.info("SFTP 连接成功.");
            return true;
        } catch (Exception e) {
            logger.error("SFTP 连接失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 断开SFTP连接
     */
    private void disconnect() {
        if (sftp != null && sftp.isConnected()) {
            sftp.disconnect();
            logger.info("SFTP Channel断开连接.");
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
            logger.info("SFTP Session断开连接.");
        }
        sftp = null;
        session = null;
    }

    /**
     * 逐级创建远程目录并切换
     * @param dir 远程目录路径
     * @throws SftpException
     */
    private void createAndChangeDir(String dir) throws SftpException {
        String[] folders = dir.split("/");
        for (String folder : folders) {
            if (folder.trim().isEmpty()) continue;
            try {
                sftp.cd(folder);
            } catch (SftpException e) {
                // 如果目录不存在, 则创建
                sftp.mkdir(folder);
                sftp.cd(folder);
            }
        }
    }
}
