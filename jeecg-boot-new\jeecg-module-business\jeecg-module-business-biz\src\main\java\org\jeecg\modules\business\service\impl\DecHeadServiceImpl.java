package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.DecHeadFields;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.oss.OssBootUtil;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.dto.DecLogicVerificationDTO;
import org.jeecg.modules.business.entity.excel.ExportDecExcel;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.messages.decMessages.DecContainerType;
import org.jeecg.modules.business.messages.decMessages.DecHeadType;
import org.jeecg.modules.business.messages.decMessages.DecLicenseType;
import org.jeecg.modules.business.messages.decMessages.EcoRelation;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.ExcelToPdfUtil;
import org.jeecg.modules.business.util.FolderToZipUtil;
import org.jeecg.modules.business.util.IOCloseUtil;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.jeecg.modules.business.util.message.*;
import org.jeecg.modules.business.util.printing.QRCodeAndBarcodeTool;
import org.jeecg.modules.business.vo.DictModelVO;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;
import static org.jeecg.common.constant.CommonConstant.*;
import static org.jeecg.modules.business.service.impl.AiServiceImpl.isNotChinese;
import static org.jeecg.modules.business.util.ApiUtil.sendOpenApi;
import static org.jeecg.modules.business.util.JsonBackupService.saveJsonToFile;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Slf4j
@Service
public class DecHeadServiceImpl extends ServiceImpl<DecHeadMapper, DecHead> implements IDecHeadService {
    private static final String URL = "https://api.jgsoft.com.cn:15555/open-api/sm/GetManifestInfo"; // 舱单信息查询
    private static final String URL_SHIP = "https://api.jgsoft.com.cn:15555/open-api/sm/GetShipManifestInfo"; // 海运舱单数据查询
    private static final String SWID_XJA = "8930000026341"; // 迅吉安的卡
    private static final String SWID = "2100090037794";
    private static final String SWID_BH = "0000000180057"; // 博汇的卡
    private static final String URL_CUS_ERROR = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCusError"; // 企业报关差错记录
    private static final String URL_DEC_MOD = "https://api.jgsoft.com.cn:15555/open-api/sm/GetDecModList"; // 返回修撤单记录列表（GetDecModList）
    private static final String URL_DEC_MOD_DETAILS = "https://api.jgsoft.com.cn:15555/open-api/sm/GetDecModDetails"; // 返回修撤单记录详情（GetDecModDetails）
    private static final String URL_CD_TAX = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDTax"; // 报关单税单信息
    private static final String URL_CD_TAX_PDF = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDTaxPdf"; // 报关单税单信息
    private static final String URL_CD_RTX_PDF = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDPdfRtx"; // 报关单文件退税联(PDF)
    private static final String URL_CD_RELEASE_PDF = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDReleaseNote"; // 报关单放行通知(PDF)
    private static final String URL_CD_CHECK_PDF = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDCheckNote"; // 报关单查验通知(PDF)
    private static final String URL_SW_IE_STATUS = "https://api.jgsoft.com.cn:15555/open-api/sm/GetSwIEStatus"; // 获取进出口状态
    private static final String URL_GET_CD_QUERY = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDQuery"; // 报关单列表
    private static final String URL_GET_CD_DETAILS = "https://api.jgsoft.com.cn:15555/open-api/sm/GetCDDetails"; // 报关单列表

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    // 注入FTP配置类，用于管理多企业的FTP连接信息和路径配置
    @Autowired
    private FtpProperties ftpProperties;

//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteSendPath}")
//    private String remoteSendPath;
    //
    @Autowired
    private IDecListService decListService;
    @Autowired
    private IDecContainerService decContainerService;
    @Autowired
    private IDecLicenseDocusService decLicenseDocusesService;
    @Autowired
    private IDecAttachmentService decAttachmentService;
    @Autowired
    private DecListMapper decListMapper;
    @Autowired
    private IErpTransportTypesService erpTransportTypesService;
    @Autowired
    private IErpCustomsPortsService erpCustomsPortsService;
    @Autowired
    private IErpCityportsService erpCityportsService;
    @Autowired
    private IErpCountriesService erpCountriesService;
    @Autowired
    private IErpCurrenciesService erpCurrenciesService;
    @Autowired
    private IErpPackagesTypesService erpPackagesTypesService;
    @Autowired
    private IErpUnitsService erpUnitsService;
    @Autowired
    private IErpDistrictsService erpDistrictsService;
    @Autowired
    private SyncDecHeadMapper syncDecHeadMapper;
    //逻辑校验接口登录名
    private final String account = "jgtd";
    //逻辑校验接口登录密码
    private final String passWord = "jgtd123456";
    @Autowired
    private ISyncDecHeadService syncDecHeadService;
    @Autowired
    private ISyncDecListService syncDecListService;
    @Autowired
    private ISyncDecContainerService syncDecContainerService;
    @Autowired
    private ISyncDecLicenseDocusService syncDecLicenseDocusService;
    @Autowired
    private ISyncDecDocService syncDecDocService;
    @Autowired
    private DecShopsPurchaserRelMapper decShopsPurchaserRelMapper;
    @Autowired
    private ShopsInfoMapper shopsInfoMapper;
    @Autowired
    private PurchaserInfoMapper purchaserInfoMapper;
    @Autowired
    private TradeInfoMapper tradeInfoMapper;
    @Autowired
    private TradeGoodsListMapper tradeGoodsListMapper;
    @Autowired
    private GroupingInfoMapper groupingInfoMapper;
    @Autowired
    private GroupingGoodsListMapper groupingGoodsListMapper;
    @Autowired
    private NegativeProductListMapper negativeProductListMapper;
    @Autowired
    private CustomerEnterpriseMapper customerEnterpriseMapper;
    @Autowired
    private ErpReportElementsMapper erpReportElementsMapper;
    @Autowired
    private ErpHscodesMapper erpHscodesMapper;
    @Autowired
    private IErpChinaPortsService erpChinaPortsService;
    @Autowired
    private RepairCancellationOrdersMapper repairCancellationOrdersMapper;
    @Autowired
    private IRepairCancellationOrdersService repairCancellationOrdersService;
    @Autowired
    private INemsInvtHeadService invtHeadService;
    @Autowired
    private NemsInvtHeadMapper invtHeadMapper;
    @Autowired
    private INemsInvtListService invtListService;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ErpCountriesMapper erpCountriesMapper;
    @Autowired
    private ErpCurrenciesMapper erpCurrenciesMapper;
    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;
    @Autowired
    private IDecEcoRelationService decEcoRelationService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private AiRelTableMapper aiRelTableMapper;

    @Autowired
    private PtsEmsAimgMapper ptsEmsAimgMapper;
    @Autowired
    private PtsEmsAexgMapper ptsEmsAexgMapper;

    // 设置个限流的令牌桶
    private static final RateLimiter rateLimiter = RateLimiter.create(1.0); // 每秒1个令牌

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveDecHead(DecHead decHead) {
        boolean isAdd = false;
        //20240110报关制单保存时，校验报关单表体税号是否在负面清单中
        //可根据报关单关联商铺id是否存在，存在则是市场采购的报关单，否则是普通报关单
        if(isNotEmpty(decHead.getDecLists())&&isNotBlank(decHead.getShopsId())){
            //报关单表体的所有税号
            List<String> hscodeList=decHead.getDecLists().stream().map(DecList::getHscode)
                    .distinct().collect(Collectors.toList());
            List<String> negativeTaxNumber=new ArrayList<>();
            //根据申报海关进行判断
            switch (decHead.getDeclarePlace()){
                //烟台
                case "4201":
                    List<NegativeProductList> negativeProductListList=negativeProductListMapper
                            .selectList(new LambdaQueryWrapper<NegativeProductList>()
                            .eq(NegativeProductList::getDistrict,"烟台"));
                    negativeTaxNumber=negativeProductListList.stream().map(NegativeProductList::getHscode)
                            .collect(Collectors.toList());
                    break;
                    //青岛
                case "4200":
                    List<NegativeProductList> negativeProductListList2=negativeProductListMapper
                            .selectList(new LambdaQueryWrapper<NegativeProductList>()
                                    .eq(NegativeProductList::getDistrict,"即墨"));
                    negativeTaxNumber=negativeProductListList2.stream().map(NegativeProductList::getHscode)
                            .collect(Collectors.toList());
                    break;
                //临沂
                case "4213":
                    List<NegativeProductList> negativeProductListList3=negativeProductListMapper
                            .selectList(new LambdaQueryWrapper<NegativeProductList>()
                                    .eq(NegativeProductList::getDistrict,"临沂"));
                    negativeTaxNumber=negativeProductListList3.stream().map(NegativeProductList::getHscode)
                            .collect(Collectors.toList());
                    break;
            }
            //筛选出存在于负面清单的税号
            negativeTaxNumber.retainAll(hscodeList);
            if(isNotEmpty(negativeTaxNumber)){
                return "以下税号存在于负面清单，无法保存，请检查。【"+String.join(",",negativeTaxNumber)+"】";
            }

        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isBlank(decHead.getId())){
            isAdd = true;
            decHead.setId(IdWorker.getIdStr());
//            decHead.setSeqNo("BD"+ decHead.getId());
            decHead.setTenantId(isNotEmpty(decHead.getTenantId()) ? decHead.getTenantId() : Long.valueOf(TenantContext.getTenant()));
            decHead.setDclTenantId(String.valueOf(decHead.getTenantId()));
            decHead.setCustomsCode("D" + decHead.getId());
            decHead.setCreatePerson(isNotEmpty(loginUser) ? loginUser.getRealname() : null);
            decHead.setCreateTime(new Date());
            baseMapper.insert(decHead);
        } else {
            // 8. 境内收发货人、消费使用单位，编辑自动维护关系表。
            try {
                DecHead oldDecHead = baseMapper.selectById(decHead.getId());
                if (isNotEmpty(oldDecHead)) {
                    if (isNotBlank(decHead.getOptUnitName()) && isNotBlank(oldDecHead.getOptUnitName())
                            && !decHead.getOptUnitName().equals(oldDecHead.getOptUnitName())
                            && !Validator.hasChinese(oldDecHead.getOptUnitName())) {
                        AiRelTable aiRelTable = new AiRelTable();
                        aiRelTable.setType("1");
                        aiRelTable.setEnName(oldDecHead.getOptUnitName());
                        aiRelTable.setCnName(decHead.getOptUnitName());
                        aiRelTable.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                        aiRelTable.setCreateDate(new Date());
                        long count = aiRelTableMapper.selectCount(new QueryWrapper<AiRelTable>().lambda()
                                .eq(AiRelTable::getType, "1")
                                .eq(AiRelTable::getEnName, oldDecHead.getOptUnitName())
                                .eq(AiRelTable::getCnName, decHead.getOptUnitName()));
                        if  (count <= 0) {
                            aiRelTableMapper.insert(aiRelTable);
                        }
                    }
                    if (isNotBlank(decHead.getDeliverUnitName()) && isNotBlank(oldDecHead.getDeliverUnitName())
                            && !decHead.getDeliverUnitName().equals(oldDecHead.getDeliverUnitName())
                            && !Validator.hasChinese(oldDecHead.getDeliverUnitName())) {
                        AiRelTable aiRelTable = new AiRelTable();
                        aiRelTable.setType("1");
                        aiRelTable.setEnName(oldDecHead.getDeliverUnitName());
                        aiRelTable.setCnName(decHead.getDeliverUnitName());
                        aiRelTable.setCreateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
                        aiRelTable.setCreateDate(new Date());
                        long count = aiRelTableMapper.selectCount(new QueryWrapper<AiRelTable>().lambda()
                                .eq(AiRelTable::getType, "1")
                                .eq(AiRelTable::getEnName, oldDecHead.getDeliverUnitName())
                                .eq(AiRelTable::getCnName, decHead.getDeliverUnitName()));
                        if  (count <= 0) {
                            aiRelTableMapper.insert(aiRelTable);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("保存境内收发货人、消费使用单位出现异常：", e.getMessage());
            }
            decHead.setUpdateBy(isNotEmpty(loginUser) ? loginUser.getUsername() : null);
            decHead.setUpdateDate(new Date());
            baseMapper.updateById(decHead);
        }
        decListService.remove(new QueryWrapper<DecList>().lambda().eq(DecList::getDecId,decHead.getId()));
        if (isNotEmpty(decHead.getDecLists())){
            List<Integer> number = new ArrayList<>();
            number.add(1);
            //20240201处理保存当前税则的相关数据，校验用
            //全部税号
            List<String> hscodeList=decHead.getDecLists().stream().map(DecList::getHscode).collect(Collectors.toList());
            //根据申报税号获取申报规范
            List<ErpReportElements> erpReportElementsList = erpReportElementsMapper.selectList(
                    new QueryWrapper<ErpReportElements>().lambda()
                            .in(ErpReportElements::getHscode, hscodeList)
                            .orderByDesc(ErpReportElements::getHscode)
                            .orderByAsc(ErpReportElements::getNo));
            //根据申报税号获取税则库
            List<ErpHscodes> erpHscodesList=erpHscodesMapper.selectList(new LambdaQueryWrapper<ErpHscodes>()
            .in(ErpHscodes::getHscode,hscodeList));





            decHead.getDecLists().forEach(v->{
                v.setDecId(decHead.getId());
                v.setItem(number.get(0));
                v.setId(String.valueOf(IdWorker.getId()));
                number.set(0,number.get(0)+1);
                //处理申报规范
                if(isNotBlank(v.getHscode())){
                List<ErpReportElements> erpReportElementsListByHsCode=erpReportElementsList.stream().
                        filter(i->i.getHscode().equals(v.getHscode())).collect(Collectors.toList());
                List<String> historyDeclarationSpecificationList=new ArrayList<>();
                for(ErpReportElements erpReportElements:erpReportElementsListByHsCode){
                    historyDeclarationSpecificationList.add(erpReportElements.getNo()+":"+erpReportElements.getElement());
                }
                v.setHistoryDeclarationSpecification(String.join("|",historyDeclarationSpecificationList));
                //处理税则商品名,监管条件，检疫条件
                    List<ErpHscodes> erpHscodes=erpHscodesList.stream().filter(i->i.getHscode().equals(v.getHscode()))
                            .collect(Collectors.toList());
                    if(erpHscodes.size()>0){
                        v.setHistoryHsname(erpHscodes.get(0).getHsname());
                        v.setHistoryRegulatoryConditions(erpHscodes.get(0).getMonitorcondition());
                        v.setHistoryQuarantineConditions(erpHscodes.get(0).getIaqcategory());
                    }
                }
            });
            decListService.saveBatch(decHead.getDecLists());
        }
        decContainerService.remove(new QueryWrapper<DecContainer>().lambda()
                .eq(DecContainer::getDecId,decHead.getId()));
        if (decHead.getDecContainers() != null && !decHead.getDecContainers().isEmpty()){
            decHead.getDecContainers().forEach(v->{
                v.setDecId(decHead.getId());
                v.setId(String.valueOf(IdWorker.getId()));
            });
            decContainerService.saveBatch(decHead.getDecContainers());
        }
        decLicenseDocusesService.remove(new QueryWrapper<DecLicenseDocus>().lambda()
                .eq(DecLicenseDocus::getDecId,decHead.getId()));
        if (decHead.getDecLicenseDocuses() != null && !decHead.getDecLicenseDocuses().isEmpty()){
            decHead.getDecLicenseDocuses().forEach(v->{
                v.setDecId(decHead.getId());
                v.setId(String.valueOf(IdWorker.getId()));
            });
            decLicenseDocusesService.saveBatch(decHead.getDecLicenseDocuses());
        }
        if (isAdd) {
//            if (isNotBlank(decHead.getOrderProtocolNo()) || isNotBlank(decHead.getOrderId())) {
//                List<OrderInfo> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>()
//                        .eq(OrderInfo::getOrderProtocolNo, decHead.getOrderProtocolNo())
//                        .or().eq(OrderInfo::getId, decHead.getOrderId()));
//                if (isNotEmpty(orderInfoList)) {
//                    for (OrderInfo orderInfo : orderInfoList) {
//                        orderInfoMapper.update(null, new UpdateWrapper<OrderInfo>().lambda()
//                                .set(OrderInfo::getAddDecFlag, "1")
//                                .eq(OrderInfo::getId, orderInfo.getId()));
//                    }
//                }
//            }
            //处理市场采购的相关逻辑
            //20240110报关单新增时不再生成交易单，组货，装箱数据，推送时发送报关单相关数据，返回信息再填入三张表中。
            //processMarketProcurementLogic(decHead);
            /**
             * 20240108 如果是市场采购的报关单新增时，添加商铺，采购商，报关单的关联关系表 by zhengliansong
             */
            if(isNotBlank(decHead.getShopsId())&&isNotBlank(decHead.getPurchaserId())){
                DecShopsPurchaserRel decShopsPurchaserRel=new DecShopsPurchaserRel();
                decShopsPurchaserRel.setDecId(decHead.getId()).setShopsId(decHead.getShopsId())
                        .setPurchaserId(decHead.getPurchaserId());
                decShopsPurchaserRelMapper.insert(decShopsPurchaserRel);
            }

        }
        //录入的境内收发货人、生产销售单位、申报单位录入社会信用代码如果在海关注册企业库中不存在，需添加进去。
        if(isNotBlank(decHead.getOptUnitSocialCode())){
            List<CustomerEnterprise> customerEnterpriseList=customerEnterpriseMapper.selectList(
                    new LambdaQueryWrapper<CustomerEnterprise>()
                            .eq(CustomerEnterprise::getSocialCode,decHead.getOptUnitSocialCode()));
            if(customerEnterpriseList.size()==0){
                CustomerEnterprise customerEnterprise1=new CustomerEnterprise();
                customerEnterprise1.setSocialCode(decHead.getOptUnitSocialCode());
                customerEnterprise1.setDepartName(decHead.getOptUnitName());
                customerEnterprise1.setDepartcd(decHead.getOptUnitId());
                customerEnterprise1.setCiqCode(decHead.getTradeCiqCode());
                customerEnterpriseMapper.insert(customerEnterprise1);
            }
        }
        if(isNotBlank(decHead.getDeliverUnitSocialCode())){
            CustomerEnterprise customerEnterprise=customerEnterpriseMapper.selectOne(
                    new LambdaQueryWrapper<CustomerEnterprise>()
                            .eq(CustomerEnterprise::getSocialCode,decHead.getDeliverUnitSocialCode()).last("LIMIT 1"));
            if(isEmpty(customerEnterprise)){
                CustomerEnterprise customerEnterprise1=new CustomerEnterprise();
                customerEnterprise1.setSocialCode(decHead.getDeliverUnitSocialCode());
                customerEnterprise1.setDepartName(decHead.getDeliverUnitName());
                customerEnterprise1.setDepartcd(decHead.getDeliverUnit());
                customerEnterprise1.setCiqCode(decHead.getOwnerCiqCode());
                customerEnterpriseMapper.insert(customerEnterprise1);
            }
        }
        if(isNotBlank(decHead.getDeclareUnitSocialCode())){
            CustomerEnterprise customerEnterprise=customerEnterpriseMapper.selectOne(
                    new LambdaQueryWrapper<CustomerEnterprise>()
                            .eq(CustomerEnterprise::getSocialCode,decHead.getDeclareUnitSocialCode()));
            if(isEmpty(customerEnterprise)){
                CustomerEnterprise customerEnterprise1=new CustomerEnterprise();
                customerEnterprise1.setSocialCode(decHead.getDeclareUnitSocialCode());
                customerEnterprise1.setDepartName(decHead.getDeclareUnitName());
                customerEnterprise1.setDepartcd(decHead.getDeclareUnit());
                customerEnterprise1.setCiqCode(decHead.getDeclCiqCode());
                customerEnterpriseMapper.insert(customerEnterprise1);
            }
        }
        return decHead.getId();
    }

    /**
     * 处理市场采购的逻辑
     */
    private void processMarketProcurementLogic(DecHead decHead){
        /**
         * 20240108 如果是市场采购的报关单新增时，添加商铺，采购商，报关单的关联关系表 by zhengliansong
         */
        if(isNotBlank(decHead.getShopsId())&&isNotBlank(decHead.getPurchaserId())){
            DecShopsPurchaserRel decShopsPurchaserRel=new DecShopsPurchaserRel();
            decShopsPurchaserRel.setDecId(decHead.getId()).setShopsId(decHead.getShopsId())
                    .setPurchaserId(decHead.getPurchaserId());
            decShopsPurchaserRelMapper.insert(decShopsPurchaserRel);
        }
        //20240108 新增报关单 添加交易订单，组货，装箱的数据
        ShopsInfo shopsInfo=shopsInfoMapper.selectById(decHead.getShopsId());//商铺信息
        PurchaserInfo purchaserInfo=purchaserInfoMapper.selectById(decHead.getPurchaserId());//采购商信息
        //----------1添加交易订单表头数据
        TradeInfo tradeInfo=new TradeInfo();
        tradeInfo.setDecId(decHead.getId());
        tradeInfo.setAgentRecNo(decHead.getOptUnitSocialCode());//代理商备案编号（报关单境内收发货人18位社会信用代码）
        tradeInfo.setMarketPurchaseNo(shopsInfo!=null?shopsInfo.getMarketProcurementNumber():null);//商户市场采购号
        tradeInfo.setPurchaseDate(new Date());//采购日期，默认当天
        tradeInfo.setTaxpayerId(shopsInfo!=null?shopsInfo.getSocialCreditCode():null);//商户社会信用代码
        LocalDate date = LocalDate.now();
        // 加30天
        LocalDate nextDay = date.plusDays(30);
        // LocalDate转date
        Date dateNew = Date.from(nextDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        tradeInfo.setTradeConfirmFinaltime(dateNew);//要求最晚确认时间，默认采购日期+30天
        tradeInfo.setMerchantCompanyName(shopsInfo!=null?shopsInfo.getShopName():null);//商户企业名称
        tradeInfoMapper.insert(tradeInfo);//执行新增操作
        //----------2添加组货表头数据
        GroupingInfo groupingInfo=new GroupingInfo();
        groupingInfo.setDecId(decHead.getId());//关联报关单表头id
        groupingInfo.setPurchaserCertType(purchaserInfo!=null?purchaserInfo.getIdType():null);//采购商证件类型：0组织结构代码证1身份证2护照
        groupingInfo.setGroupingDate(new Date());//组货日期
        groupingInfo.setForwarderRecNo(decHead.getDeclareUnitSocialCode());//货代(18位社会信用代码)(报关单的申报单位)
        groupingInfo.setBrokerRecNo(decHead.getDeclareUnitSocialCode());//报关行(18位社会信用代码)(报关单的申报单位)
        groupingInfo.setDiMark("0");//是否报检
        groupingInfo.setPurchaserName(purchaserInfo!=null?purchaserInfo.getPurchaserName():null);//采购商名称
        groupingInfo.setPurchaserNumber(purchaserInfo!=null?purchaserInfo.getIdNumber():null);//采购商证件号码
        groupingInfo.setPurchaserPhone(purchaserInfo!=null?purchaserInfo.getPurchaserPhone():null);//采购商联系方式
        groupingInfo.setPurposeCountry(decHead.getArrivalArea());//目的国代码
        groupingInfoMapper.insert(groupingInfo);//新增操作
        //-----------处理表体数据
        if(decHead.getDecLists().size()>0){
            for(DecList decList:decHead.getDecLists()){
                //交易订单表体数据
                TradeGoodsList tradeGoodsList=new TradeGoodsList();
                tradeGoodsList.setTradeInfoId(tradeInfo.getId());//交易订单表头id
                tradeGoodsList.setGoodsNum(decList.getItem());//序号
                tradeGoodsList.setGoodsValueSum(decList.getTotal());//总价
                tradeGoodsList.setGoodsPrice(decList.getPrice());//单价
                tradeGoodsList.setGoodsNo(decList.getHscode());//税号
                tradeGoodsList.setGoodsName(decList.getHsname());//品名
                tradeGoodsList.setGoodsUnit(decList.getUnitCode());//成交单位
                tradeGoodsList.setGoodsUnitLegal(decList.getUnit1());//法一单位
                tradeGoodsList.setGoodsNumLegal(decList.getCount1());//法一数量
                tradeGoodsList.setGoodsValueUnit(decList.getCurrencyCode());//币制
                tradeGoodsListMapper.insert(tradeGoodsList);//执行新增操作
                //组货表体数据
                GroupingGoodsList groupingGoodsList=new GroupingGoodsList();
                groupingGoodsList.setGroupingInfoId(groupingInfo.getId());
                groupingGoodsList.setGoodsNo(decList.getHscode());//商品税号
                groupingGoodsList.setGoodsNum(decList.getItem());//序号
                groupingGoodsList.setGoodsUnit(decList.getUnitCode());//成交单位
                groupingGoodsList.setGoodsValueSum(decList.getTotal());//总价
                groupingGoodsList.setGoodsName(decList.getHsname());//品名
                groupingGoodsList.setGoodsPrice(decList.getPrice());//单价
                groupingGoodsList.setGoodsValueUnit(decList.getCurrencyCode());//币制
                groupingGoodsList.setGoodsUnitLegal(decList.getUnit1());//法一单位
                groupingGoodsList.setGoodsNumLegal(decList.getCount1());//法一数量
                groupingGoodsList.setMarketPurchaseNo(shopsInfo!=null?shopsInfo.getMarketProcurementNumber():null);//商户市场采购号
                groupingGoodsList.setIsChecked(true);//默认true
                groupingGoodsList.setGoodsPackNum(1);//包装数，暂且默认1
                groupingGoodsList.setGoodsKgs(decList.getNetWeight());//净重，默认1
                groupingGoodsListMapper.insert(groupingGoodsList);//执行新增操作
            }
        }

    }

    /**
     * @param decHeadList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveDecHeadBatch(List<DecHead> decHeadList) {
        if (isEmpty(decHeadList)) {
            return Result.error("报关单数据为空！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<DecHead> addHeads = new ArrayList<>();
        List<DecHead> updateHeads = new ArrayList<>();
        List<DecList> addLists = new ArrayList<>();
        List<DecContainer> addContainers = new ArrayList<>();
        List<DecLicenseDocus> addLicenseDocuss = new ArrayList<>();
        List<String> headIdList = decHeadList.stream().map(DecHead::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (isNotEmpty(headIdList)) {
            decListService.remove(new QueryWrapper<DecList>().lambda().in(DecList::getDecId, headIdList));
            decContainerService.remove(new QueryWrapper<DecContainer>().lambda().in(DecContainer::getDecId, headIdList));
            decLicenseDocusesService.remove(new QueryWrapper<DecLicenseDocus>().lambda().in(DecLicenseDocus::getDecId, headIdList));
        }
        for (DecHead decHead : decHeadList) {
            if (isBlank(decHead.getId())) {
                decHead.setId(IdWorker.getIdStr());
                decHead.setCustomsCode("D" + decHead.getId());
                addHeads.add(decHead);
            } else {
                updateHeads.add(decHead);
            }
            if (isNotEmpty(decHead.getDecLists())) {
                List<Integer> number = new ArrayList<>();
                number.add(1);
                decHead.getDecLists().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setItem(isNotEmpty(v.getItem()) ? v.getItem() : number.get(0));
                    v.setId(IdWorker.getIdStr());
                    number.set(0, number.get(0)+1);
                });
                addLists.addAll(decHead.getDecLists());
            }
            if (isNotEmpty(decHead.getDecContainers())) {
                decHead.getDecContainers().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setId(IdWorker.getIdStr());
                });
                addContainers.addAll(decHead.getDecContainers());
            }
            if (isNotEmpty(decHead.getDecLicenseDocuses())) {
                decHead.getDecLicenseDocuses().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setId(IdWorker.getIdStr());
                });
                addLicenseDocuss.addAll(decHead.getDecLicenseDocuses());
            }
        }
        log.info("addHeads:{}", addHeads);
        if (isNotEmpty(addHeads)) {
            log.info("走插入报关单条数:{}", addHeads.size());
            baseMapper.insertBatchSomeColumn(addHeads);
        }
        if (isNotEmpty(updateHeads)) {
            log.info("走编辑报关单条数:{}", updateHeads.size());
            // TODO: 2023/11/18 不支持批量编辑：sql injection violation, multi-statement not allow ！！待解决！！
//            baseMapper.updateBatchById(updateHeads);
            this.updateBatchById(updateHeads);
        }
        if (isNotEmpty(addLists)) {
            baseMapper.insertBatchLists(addLists);
        }
        if (isNotEmpty(addContainers)) {
            baseMapper.insertBatchContainers(addContainers);
        }
        if (isNotEmpty(addLicenseDocuss)) {
            baseMapper.insertBatchLicenseDocuses(addLicenseDocuss);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK("插入报关单数：" + addHeads.size() + "；" + "更新报关单数：" + updateHeads.size());
    }

    @Override
    public DecHead getDecHeadById(String decHeadId) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        DecHead decHead = baseMapper.selectById(decHeadId);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        if (decHead == null){
            return decHead;
        }
        List<DecList> decLists = decListService.list(new QueryWrapper<DecList>().lambda().eq(DecList::getDecId,decHeadId));
        if (decLists != null && !decLists.isEmpty()){
            //处理货物属性
            List<DictModelVO> goodsArrtDict = decListMapper.getDictItemByCode("HWSX");
            Map<String,DictModelVO> dictMap = goodsArrtDict.stream().collect(Collectors.toMap(DictModelVO::getValue,a->a,(k1,k2)->k1));

            decLists.forEach(v->{
                if (StringUtils.isNotBlank(v.getGoodsAttr())){
                    String goodsAttrStr = null;
                    for (String str : v.getGoodsAttr().split(",")){
                        DictModelVO  dict = dictMap.get(str);
                        if (goodsAttrStr ==null){
                            goodsAttrStr = new StringBuilder(dict.getValue()).append("|").append(dict.getText()).toString();
                        }else {
                            goodsAttrStr = new StringBuilder(goodsAttrStr).append(",")
                                    .append(dict.getValue()).append("|").append(dict.getText()).toString();
                        }

                    }
                    v.setGoodsAttrStr(goodsAttrStr);
                }

            });
        }
        List<DecContainer> decContainers = decContainerService.list(new QueryWrapper<DecContainer>().lambda()
                .eq(DecContainer::getDecId,decHeadId));
        if (decContainers != null && !decContainers.isEmpty()){
            //处理集装箱规
            List<DictModelVO> goodsArrtDict = decListMapper.getDictItemByCode("JZXGG");
            Map<String,DictModelVO> dictMap = goodsArrtDict.stream().collect(Collectors.toMap(DictModelVO::getValue,a->a,(k1,k2)->k1));
            decContainers.forEach(v->{
                DictModelVO  dict = dictMap.get(v.getContainerMd());
                v.setContainerMdName(isNotEmpty(dict) ? dict.getText() : "");
            });
        }
        List<DecLicenseDocus> decLicenseDocuses = decLicenseDocusesService.list(new QueryWrapper<DecLicenseDocus>().lambda()
                .eq(DecLicenseDocus::getDecId,decHeadId));
        if (decLicenseDocuses != null && !decLicenseDocuses.isEmpty()){
            //处理单证代码
            List<DictModelVO> goodsArrtDict = decListMapper.getDictItemByCode("SFDZDM");
            Map<String,DictModelVO> dictMap = goodsArrtDict.stream().collect(Collectors.toMap(DictModelVO::getValue,a->a,(k1,k2)->k1));
            decLicenseDocuses.forEach(v->{
                DictModelVO  dict = dictMap.get(v.getDocuCode());
                v.setDocuName(isNotEmpty(dict) ? dict.getText() : "");
            });
        }
        decHead.setDecLists(decLists);
        decHead.setDecContainers(decContainers);
        decHead.setDecLicenseDocuses(decLicenseDocuses);
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        //20240108 如果该报关单是采购的，需要查询出商铺和采购商信息返回
        DecShopsPurchaserRel decShopsPurchaserRel=decShopsPurchaserRelMapper.selectOne(
                new LambdaQueryWrapper<DecShopsPurchaserRel>()
                .eq(DecShopsPurchaserRel::getDecId,decHead.getId())
        );
        if(isNotEmpty(decShopsPurchaserRel)&&isNotBlank(decShopsPurchaserRel.getShopsId())
            &&isNotBlank(decShopsPurchaserRel.getPurchaserId())){
            ShopsInfo shopsInfo=shopsInfoMapper.selectById(decShopsPurchaserRel.getShopsId());
            PurchaserInfo purchaserInfo=purchaserInfoMapper.selectById(decShopsPurchaserRel.getPurchaserId());
            decHead.setShopName(shopsInfo.getShopName());
            decHead.setPurchaserName(purchaserInfo.getPurchaserName());
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return decHead;
    }
    static final String JZXGG = "JZXGG";//集装箱规格
    static final String SFDZDM = "SFDZDM";//随附单证代码
    /**
     * 根据id获取完整报关单信息
     *
     * @param id 表头id
     * @return 报关单信息
     */
    @Override
    public Result<DecHead> getDecById(Long id) {
        return Result.ok(getDecHeadById(String.valueOf(id)));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteDecHead(String id) {
        this.removeById(id);
        decListService.remove(new QueryWrapper<DecList>().lambda().eq(DecList::getDecId,id));
        decContainerService.remove(new QueryWrapper<DecContainer>().lambda()
                .eq(DecContainer::getDecId,id));
        decLicenseDocusesService.remove(new QueryWrapper<DecLicenseDocus>().lambda()
                .eq(DecLicenseDocus::getDecId,id));
        decAttachmentService.remove(new QueryWrapper<DecAttachment>().lambda()
                .eq(DecAttachment::getDclId,id));
    }

    @Override
    public DecHead queryPrintPdfList(String id) {
        DecHead decHead = this.getById(id);
        List<DecList> decLists = decListService.list(new QueryWrapper<DecList>().lambda().eq(DecList::getDecId,id));
        List<DecLicenseDocus> decLicenseDocuses = decLicenseDocusesService.list(new QueryWrapper<DecLicenseDocus>().lambda()
                .eq(DecLicenseDocus::getDecId,id));
        decHead.setDecLists(decLists);
        decHead.setDecLicenseDocuses(decLicenseDocuses);
        Map<String,Map<String,String>> dictMap = new HashMap<>();
        Map<String,String> headMap = new HashMap<>();
        //获取需要转换的字典
        //运输方式
        if (StringUtils.isNotEmpty(decHead.getShipTypeCode())){
            ErpTransportTypes shipTypeName = erpTransportTypesService.getOne(new QueryWrapper<ErpTransportTypes>().lambda()
                    .eq(ErpTransportTypes::getCode, decHead.getShipTypeCode()));
            headMap.put(decHead.getShipTypeCode(),isNotEmpty(shipTypeName)?shipTypeName.getName():"");
        }
        //申报地海关
        if (StringUtils.isNotEmpty(decHead.getDeclarePlace())){
            ErpCustomsPorts erpCustomsPorts = erpCustomsPortsService.getOne(new QueryWrapper<ErpCustomsPorts>().lambda()
                    .eq(ErpCustomsPorts::getCustomsPortCode, decHead.getDeclarePlace()));
            headMap.put(decHead.getDeclarePlace(),isNotEmpty(erpCustomsPorts)?erpCustomsPorts.getName():"");
        }
        //进出境关别
        if (StringUtils.isNotEmpty(decHead.getOutPortCode())){
            ErpCustomsPorts erpCustomsPorts = erpCustomsPortsService.getOne(new QueryWrapper<ErpCustomsPorts>().lambda()
                    .eq(ErpCustomsPorts::getCustomsPortCode, decHead.getOutPortCode()));
            headMap.put(decHead.getOutPortCode(),isNotEmpty(erpCustomsPorts)?erpCustomsPorts.getName():"");
        }
        //入境口岸/离境口岸
        if (StringUtils.isNotEmpty(decHead.getEntyPortCode())){
            ErpCityports erpCityports = erpCityportsService.getOne(new QueryWrapper<ErpCityports>().lambda()
                    .eq(ErpCityports::getCityportCode, decHead.getEntyPortCode()));
            headMap.put(decHead.getEntyPortCode(),isNotEmpty(erpCityports)?erpCityports.getCnname():"");
        }
        //处理国家代码
        List<ErpCountries> erpCountries = erpCountriesService.list();
        Map<String,String> erpCountriesMap = new HashMap<>();
        if (erpCountries != null && !erpCountries.isEmpty()){
            erpCountries.forEach(v->{
                erpCountriesMap.put(v.getCode(),v.getName());
            });
        }
        dictMap.put("countries",erpCountriesMap);
        //贸易国
        if (StringUtils.isNotEmpty(decHead.getTradeCountry())){
            headMap.put(decHead.getTradeCountry(),erpCountriesMap.get(decHead.getTradeCountry()));
        }
        //启运国
        if (StringUtils.isNotEmpty(decHead.getArrivalArea())){
            headMap.put(decHead.getArrivalArea(),erpCountriesMap.get(decHead.getArrivalArea()));
        }
        //启运港
        if (StringUtils.isNotEmpty(decHead.getDespPortCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("GKDM",decHead.getDespPortCode());
            headMap.put(decHead.getDespPortCode(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //经停港/指运港
        if (StringUtils.isNotEmpty(decHead.getDesPort())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("GKDM",decHead.getDesPort());
            headMap.put(decHead.getDesPort(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //成交方式
        if (StringUtils.isNotEmpty(decHead.getTermsTypeCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("trading_type",decHead.getTermsTypeCode());
            headMap.put(decHead.getTermsTypeCode(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //监管方式
        if (StringUtils.isNotEmpty(decHead.getTradeTypeCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("JGFS",decHead.getTradeTypeCode());
            headMap.put(decHead.getTradeTypeCode(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //征免性质
        if (StringUtils.isNotEmpty(decHead.getTaxTypeCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("ZMXZ",decHead.getTaxTypeCode());
            headMap.put(dictModelVO.getValue(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //运费代码
        if (StringUtils.isNotEmpty(decHead.getShipFeeCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("freight_amount_type",decHead.getShipFeeCode());
            headMap.put(dictModelVO.getValue(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //保费代码
        if (StringUtils.isNotEmpty(decHead.getInsuranceCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("premium_amount_type",decHead.getInsuranceCode());
            headMap.put(dictModelVO.getValue(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //杂费代码
        if (StringUtils.isNotEmpty(decHead.getExtrasCode())){
            DictModelVO dictModelVO = decListMapper.getDictItemByCodeSpecific("freight_amount_type",decHead.getExtrasCode());
            headMap.put(dictModelVO.getValue(),isNotEmpty(dictModelVO)?dictModelVO.getText():"");
        }
        //处理币制
        List<ErpCurrencies> erpCurrencies = erpCurrenciesService.list();
        Map<String,String> erpCurrenciesMap = new HashMap<>();
        if (erpCurrencies != null && !erpCurrencies.isEmpty()){
            erpCurrencies.forEach(v->{
                erpCurrenciesMap.put(v.getCurrency(),v.getName());
            });
        }
        dictMap.put("currencies",erpCurrenciesMap);
        //运费币制
        if (StringUtils.isNotEmpty(decHead.getShipCurrencyCode())){
            headMap.put(decHead.getShipCurrencyCode(),erpCurrenciesMap.get(decHead.getShipCurrencyCode()));
        }
        //保费币制
        if (StringUtils.isNotEmpty(decHead.getInsuranceCurr())){
            headMap.put(decHead.getInsuranceCurr(),erpCurrenciesMap.get(decHead.getInsuranceCurr()));
        }
        //杂费币制
        if (StringUtils.isNotEmpty(decHead.getOtherCurr())){
            headMap.put(decHead.getOtherCurr(),erpCurrenciesMap.get(decHead.getOtherCurr()));
        }
        //处理包装种类
        if (StringUtils.isNotEmpty(decHead.getPacksKinds())){
            ErpPackagesTypes erpPackagesTypes = erpPackagesTypesService.getOne(new QueryWrapper<ErpPackagesTypes>().lambda()
                    .eq(ErpPackagesTypes::getCode, decHead.getPacksKinds()));
            headMap.put(decHead.getPacksKinds(),erpPackagesTypes.getName());
        }
        //处理单位
        List<ErpUnits> erpUnits = erpUnitsService.list();
        Map<String,String> erpUnitsMap = new HashMap<>();
        if (erpUnits != null && !erpUnits.isEmpty()){
            erpUnits.forEach(v->{
                erpUnitsMap.put(v.getCode(),v.getName());
            });
        }
        dictMap.put("units",erpUnitsMap);
        //处理征免方式
        List<DictModelVO> dictModelVOs = decListMapper.getDictItemByCode("ZJMSFS");
        Map<String,String> faxTypeCodeMap = new HashMap<>();
        if (dictModelVOs != null && !dictModelVOs.isEmpty()){
            dictModelVOs.forEach(v->{
                faxTypeCodeMap.put(v.getValue(),v.getText());
            });
        }
        dictMap.put("faxTypeCode",faxTypeCodeMap);
        //处理境内目的地
        List<ErpDistricts> erpDistricts = erpDistrictsService.list();
        Map<String,String> erpDistrictsMap = new HashMap<>();
        if (erpDistricts != null && !erpDistricts.isEmpty()){
            erpDistricts.forEach(v->{
                erpDistrictsMap.put(v.getCode(),v.getName());
            });
        }
        dictMap.put("districts",erpDistrictsMap);
        dictMap.put("head",headMap);
        //处理随附单证
        List<DictModelVO> dictModelVOS = decListMapper.getDictItemByCode("SFDZDM");
        Map<String,String> sfdzMap = new HashMap<>();
        if (dictModelVOS != null && !dictModelVOS.isEmpty()){
            dictModelVOS.forEach(v->{
                sfdzMap.put(v.getValue(),v.getText());
            });
        }
        dictMap.put("sfdz",sfdzMap);
        //处理随附单据
        List<DictModelVO> sfdjs = decListMapper.getDictItemByCode("SFDJDM");
        Map<String,String> sfdjMap = new HashMap<>();
        if (sfdjs != null && !sfdjs.isEmpty()){
            sfdjs.forEach(v->{
                sfdjMap.put(v.getValue(),v.getText());
            });
        }
        dictMap.put("sfdj",sfdjMap);

        decHead.setDictMap(dictMap);
        return decHead;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDec(String customsCode, String seqNo, String decStatus, String entryId, Date dDate,Date releaseDate,Date finalDate) {
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        if (org.springframework.util.StringUtils.isEmpty(seqNo)) {
            return false;
        }
        List<DecHead> decHeads = baseMapper.selectList(new QueryWrapper<DecHead>().lambda()
                .eq(StringUtils.isNotEmpty(seqNo), DecHead::getSeqNo, seqNo).or()
                .eq(StringUtils.isNotEmpty(customsCode), DecHead::getCustomsCode, customsCode));
//                .eq(isNotEmpty(clientSeqNo), DecHead::getCustomsCode, clientSeqNo));
        if (decHeads == null || decHeads.isEmpty()) {
            return false;
        }
        if (decHeads.size() > 1) {
            log.info(String.format("报关单出现多条，数据为：%s", JSONObject.toJSONString(decHeads)));
            return false;
        }
        DecHead decHead = decHeads.get(0);

        if (dDate != null) {
            decHead.setAppDate(dDate);
        }
        if (releaseDate != null){
            decHead.setReleaseDate(releaseDate);
        }
        if (finalDate != null){
            decHead.setFinalDate(finalDate);
        }
        if (entryId != null){
            decHead.setClearanceNo(entryId);
        }
        /*
         * 申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，11查验通知，S公自用物品核准通过，T公自用物品退单，U公自用物品待核准）
         * 1.如果回执数据状态是6退单、8删单、10结关，则直接更新。
         * 2.如果回执数据状态是1保存、2已申报、4海关入库成功、7审结，则要判断数据库此条数据状态不能是6、8、9、10。
         * 3.如果回执数据状态是9，则要判断数据库此条数据状态不能是6、8、10。
         * 4.如果回执数据状态是11，则要判断数据库此条数据状态不是6、8、9、10。
         * 2022/6/9 13:21@ZHANGCHAO
         */
        if (isNotBlank(decStatus)) {
            if ("1".equals(decStatus) || "2".equals(decStatus)
                    || "4".equals(decStatus) || "7".equals(decStatus) || "11".equals(decStatus)) {
                if (!"6".equals(decHead.getDecStatus()) && !"8".equals(decHead.getDecStatus())
                        && !"9".equals(decHead.getDecStatus()) && !"10".equals(decHead.getDecStatus())) {
                    decHead.setDecStatus(decStatus);
                }
            } else if ("9".equals(decStatus)) {
                if (!"6".equals(decHead.getDecStatus()) && !"8".equals(decHead.getDecStatus())
                        && !"10".equals(decHead.getDecStatus())) {
                    decHead.setDecStatus(decStatus);
                }
            } else if ("6".equals(decStatus) || "8".equals(decStatus) || "10".equals(decStatus)) {
                decHead.setDecStatus(decStatus);
            }
        }

        decHead.setSeqNo(seqNo);
        int result = baseMapper.updateById(decHead);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return true;
    }

    /**
     * @param ids
     * @param initialReviewStatus
     * @param opinion
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleInitialReview(String ids, String initialReviewStatus, String opinion) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if ("1".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                    .set(DecHead::getInitialReviewStatus, initialReviewStatus)
                    .set(DecHead::getFirstTrialBy, loginUser.getUsername())
                    .set(DecHead::getFirstTrialDate, new Date())
                    .set(DecHead::getFirstOpinion, opinion)
                    .in(DecHead::getId, Arrays.asList(ids.split(","))));
        } else if ("2".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                    .set(DecHead::getInitialReviewStatus, initialReviewStatus)
                    .set(DecHead::getReviewBy, loginUser.getUsername())
                    .set(DecHead::getReviewDate, new Date())
                    .set(DecHead::getReviewOpinion, opinion)
                    .in(DecHead::getId, Arrays.asList(ids.split(","))));
        } else if ("1-1".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                    .set(DecHead::getInitialReviewStatus, "0")
                    .set(DecHead::getFirstTrialBy, null)
                    .set(DecHead::getFirstTrialDate, null)
                    .set(DecHead::getFirstOpinion, null)
                    .set(DecHead::getReviewBy, null)
                    .set(DecHead::getReviewDate, null)
                    .set(DecHead::getReviewOpinion, null)
                    .in(DecHead::getId, Arrays.asList(ids.split(","))));
        } else if ("2-1".equals(initialReviewStatus)) {
            baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                    .set(DecHead::getInitialReviewStatus, "1")
                    .set(DecHead::getReviewBy, null)
                    .set(DecHead::getReviewDate, null)
                    .set(DecHead::getReviewOpinion, null)
                    .in(DecHead::getId, Arrays.asList(ids.split(","))));
        }
        String msg = "";
        if ("1".equals(initialReviewStatus)) {
            msg = "初审";
        } else if ("2".equals(initialReviewStatus)) {
            msg = "复审";
        } else if ("1-1".equals(initialReviewStatus)) {
            msg = "取消初审";
        } else if ("2-1".equals(initialReviewStatus)) {
            msg = "取消复审";
        }
        return Result.OK(msg + "成功");
    }

    private boolean sendMesssage(String id){
        DecHead decHead = this.getDecHeadById(id);
        List<DecAttachment> decAttachments = decAttachmentService.list(new QueryWrapper<DecAttachment>().lambda().eq(DecAttachment::getDclId,id));

        // 文件上传
        boolean uploadFlag = false;
        String fileName = decHead.getId().toString() + "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xml";
        String zipPath = null;
        //判断随附单据信息中是否存在文件路径
        boolean choise = false;
        if (decAttachments != null && decAttachments.size() != 0) {
            decHead.setDecAttachments(decAttachments);
            for (DecAttachment decAttachment : decAttachments) {
                if (StringUtils.isNotEmpty(decAttachment.getPath())) {
                    choise = true;
                    break;
                }
            }
        }
        try {
//            MsgFtpConfig ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendPath);
            SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
            FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
            MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendPath());
            String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
            if (choise) {

                zipPath = generateMessasge(decHead, fileName, decAttachments);

                log.info("发送报关单报文之报文生成路径：" + zipPath);
//                uploadFlag = true;
                //发送报文放开
                if (SFTP.equals(ftpType)) {
                    uploadFlag = new SFTPUtil(ftpConfig).upload(fileName.replace(".xml", ""),
                            new FileInputStream(zipPath),".zip");
                } else {
                    uploadFlag = new FTPUtil(ftpConfig).upload(fileName.replace(".xml", ""),
                            new FileInputStream(zipPath),".zip");
                }
            } else {
                //发送报文注掉
//                generateMessasge(decHead, fileName);
//                uploadFlag = true;
                //发送报文放开
                if (SFTP.equals(ftpType)) {
                    uploadFlag = new SFTPUtil(ftpConfig).upload(fileName.replace(".xml", ""),
                            new ByteArrayInputStream(generateMessasge(decHead, fileName).toByteArray()),".xml");
                } else {
                    uploadFlag = new FTPUtil(ftpConfig).upload(fileName.replace(".xml", ""),
                            new ByteArrayInputStream(generateMessasge(decHead, fileName).toByteArray()),".xml");
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.info("发送报文出错："+e);
            throw new RuntimeException(e);
        }
        return uploadFlag;
    }
    /**
     * 生成报文带随附单据的ZIP格式
     *
     * @param decHead  报关单表头对象
     * @param fileName 文件名称
     * @return
     * @throws JAXBException
     */ //ByteArrayOutputStream
    public String generateMessasge(DecHead decHead, String fileName, List<DecAttachment> attachments) throws Exception {
        String xmlPath = null;
        File file = null;
        List<String> delFiles = new ArrayList<>();
        try {

            // 创建报关单报文表头对象
            DecHeadType decHeadType = new DecHeadType();
            String applyType = null;
            String imSign = null;
            List<DecLicenseType> decLicenseTypes = null;
            List<DecContainerType> decContainerTypes = null;
            List<EcoRelation> ecoRelations = null;

            // 获取随附单证信息集合
            if (decHead.getDecLicenseDocuses() != null && !decHead.getDecLicenseDocuses().isEmpty()) {
                decLicenseTypes = decLicenseByCustomsCode(decHead.getDecLicenseDocuses());
            }

            // 获取报关单集装箱信息
            if (decHead.getDecContainers() != null && !decHead.getDecContainers().isEmpty()) {
                decContainerTypes = decContainerByCustomsCode(decHead.getDecContainers());
            }

            int size = 0;
            for (DecAttachment attachment : attachments){
                if (isEmpty(attachment.getPath())){
                    continue;
                }
                size++;
            }
            /*
             * 获取随附单据的上传信息
             */
            /*
             * 将所附单据以及生成的报文放入文件流数组
             */
//            File srcFiles[] = new File[attachments.size() + 1];
            File srcFiles[] = new File[size+1];
            Map<String, Long> attachmentSize = new HashMap<>();

            int sequence = 0;
            String fileNameError = "";
            for (int number = 0; number < attachments.size(); number++) {
                if (isEmpty(attachments.get(number).getPath())){
                    continue;
                }
                String path = uploadpath + File.separator + attachments.get(number).getPath();
                // minio
                if (attachments.get(number).getPath().startsWith("http")) {
//                    InputStream inputStream = MinioUtil.getMinioFile(attachments.get(number).getPath().split("trade-service-platform")[1]);
                    InputStream inputStream;
                    if (attachments.get(number).getPath().contains("trade-service-platform")) {
                        inputStream = MinioUtil.getMinioFile(attachments.get(number).getPath().split("trade-service-platform")[1]);
                    } else {
                        inputStream = OssBootUtil.getOssFileByUrl(attachments.get(number).getPath());
                    }
                    // 本地文件路径
                    path = uploadpath + File.separator + attachments.get(number).getAttachmentName();
                    // 保存文件
                    try {
                        saveFileToLocal(inputStream, path);
                    } catch (Exception e) {
                        ExceptionUtil.getFullStackTrace(e);
                        throw new RuntimeException("未获取到随附单据文件，推送失败!");
                    }
                    delFiles.add(path);
                }
                //判断文件大小
                File filePath = new File(path);
                if (filePath.exists()){
                    log.info("judgeFileSize.文件大小："+filePath.length());
                    if (filePath.length()/1024/1024>4){
                        if (StringUtils.isNotEmpty(fileNameError)){
                            fileNameError = new StringBuffer(fileNameError).append(",文件").append(attachments.get(number).getAttachmentName()).append("超过4M").toString();
                        }else {
                            fileNameError = new StringBuffer().append("文件").append(attachments.get(number).getAttachmentName()).append("超过4M").toString();
                        }
                        continue;
                    }
                    PDDocument doc = PDDocument.load(filePath);
                    int page = doc.getNumberOfPages();
                    log.info("judgeFileSize.页数："+page);
                    if (filePath.length()/1024L/Long.valueOf(page)>400L){
                        if (StringUtils.isNotEmpty(fileNameError)){
                            fileNameError = new StringBuffer(fileNameError).append(",文件").append(attachments.get(number).getAttachmentName()).append("单页超过400k").toString();
                        }else {
                            fileNameError = new StringBuffer().append("文件").append(attachments.get(number).getAttachmentName()).append("单页超过400k").toString();
                        }
                        continue;
                    }
                }


                Boolean resultEdoc = sendEdocsMessage(decHead, attachments.get(number), path);

                srcFiles[sequence] = new File(path);
                attachmentSize.put(attachments.get(number).getPath(), srcFiles[sequence].length());
                sequence++;
            }
//            if (StringUtils.isNotEmpty(fileNameError)){
//                throw new YmException(fileNameError);
//            }
            List<DecList> decLists = decHead.getDecLists();
            /*if (decHead.getDecType() != null && decHead.getDecType().length() == 6 && "1".equals(decHead.getDecType().subSequence(2, 3))) {
                decHead = getTwoStep(decHead, decLists);
                decLists = decHead.getDecLists();
            }*/

            // 返回生成的报文
            ByteArrayOutputStream bos = MessageFileUtil.export(DeclarationMessageUtil.generateDecMessage(decHead, decLists, decHeadType,
                    decLicenseTypes, applyType, imSign, decContainerTypes, attachments, attachmentSize, ecoRelations), fileName);
            byte[] data = bos.toByteArray();

            /*
             * 将生成的xml上传到根目录备用，生成完zip再删除
             */
            xmlPath = new StringBuffer(uploadpath).append("/send/temporary").append(File.separator).append(fileName).toString();
            File directoryFile = new File(new StringBuffer(uploadpath).append("/send/temporary").toString());
            if (!directoryFile.exists()) {
                // 路径不存在则创建
                log.info("生成文件夹路径："+(new StringBuffer(uploadpath).append("/send/temporary").toString()));
                directoryFile.mkdirs();
            }
            file = new File(xmlPath);
            if (file.exists()) {//判断文件是否存在，存在就删除
                file.delete();
            }
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(data, 0, data.length);
            fos.flush();
            fos.close();

            /*
             * 将生成的报文放入文件流数组
             */
//            srcFiles[attachments.size()] = new File(xmlPath);
            srcFiles[size] = new File(xmlPath);

            String zipPath = xmlPath.replace("xml", "zip");
            File zipFile = new File(zipPath);
            MessageFileUtil.zipFiles(srcFiles, zipFile);

            if (isNotEmpty(delFiles)) {
                ExecutorService executor = ThreadUtil.newExecutor(1);
                executor.execute(() -> {
                    try {
                        Thread.sleep(60000); // 赶紧给我睡上1分钟！！
                        for (String delFile : delFiles) {
                            FileUtil.del(FileUtil.file(delFile));
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        log.error("删除文件失败", e);
                    }
                });
                executor.shutdown();
            }
            return zipPath;
        } finally {

        }
    }

    public static void saveFileToLocal(InputStream inputStream, String localFilePath) {
        // 确保输入流不为空
        if (inputStream == null) {
            throw new IllegalArgumentException("InputStream cannot be null");
        }

        // 创建文件输出流
        try (OutputStream outputStream = new FileOutputStream(new File(localFilePath))) {
            // 创建缓冲区
            byte[] buffer = new byte[1024];
            int bytesRead;

            // 读取输入流并写入文件
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭输入流
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 生成报文不带随附单据的XML格式
     *
     * @param decHead  报关单表头对象
     * @param fileName 文件名称
     * @return
     * @throws JAXBException
     */
    public ByteArrayOutputStream generateMessasge(DecHead decHead, String fileName) throws Exception {

        // 创建报关单报文表头对象
        DecHeadType decHeadType = new DecHeadType();
        // 获取业务信息
        String applyType = null;
        String imSign = null;


        List<DecList> decLists = decHead.getDecLists();
        List<DecLicenseType> decLicenseTypes = null;
        List<DecContainerType> decContainerTypes = null;
        List<EcoRelation> ecoRelations = null;

        // 获取随附单证信息集合
        if (decHead.getDecLicenseDocuses() != null && !decHead.getDecLicenseDocuses().isEmpty()) {
            decLicenseTypes = decLicenseByCustomsCode(decHead.getDecLicenseDocuses());
        }
        // 获取报关单集装箱信息
        if (decHead.getDecContainers() != null && !decHead.getDecContainers().isEmpty()) {
            decContainerTypes = decContainerByCustomsCode(decHead.getDecContainers());
        }
        //随附单据信息
        List<DecAttachment> decAttachments = decHead.getDecAttachments();

        /*if (decHead.getDecType() != null && decHead.getDecType().length() == 6 && "1".equals(decHead.getDecType().subSequence(2, 3))) {
            decHead = getTwoStep(decHead, decLists);
            decLists = decHead.getDecLists();
        }
        //原产地证明项号关联关系
        if (decHead.getDecEcoRelations() != null && !decHead.getDecEcoRelations().isEmpty()) {
            ecoRelations = getDecEcoRelationType(decHead.getDecEcoRelations());
        }*/

        // 返回生成的报文 -发送报文放开
        return MessageFileUtil.export(DeclarationMessageUtil.generateDecMessage(decHead, decLists, decHeadType,
                decLicenseTypes, applyType, imSign, decContainerTypes, decAttachments, null, ecoRelations), fileName);

        // 返回生成的报文 -之下发送报文注掉
        /*
        ByteArrayOutputStream bos = MessageFileUtil.export(DeclarationMessageUtil.generateDecMessage(decHead, decLists, decHeadType,
                decLicenseTypes, applyType, imSign, decContainerTypes, decAttachments, null, ecoRelations), fileName);
        byte[] data = bos.toByteArray();
        *//*
         * 将生成的xml上传到根目录备用，生成完zip再删除
         *//*
        String xmlPath = new StringBuffer(uploadpath).append("/send").append(File.separator).append(fileName).toString();
        File directoryFile = new File(new StringBuffer(uploadpath).append("/send").toString());
        if (!directoryFile.exists()) {//判断文件是否存在，存在就删除
            directoryFile.createNewFile();
        }
        File file = new File(xmlPath);
        if (file.exists()) {//判断文件是否存在，存在就删除
            file.delete();
        }
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data, 0, data.length);
        fos.flush();
        fos.close();
        return null;
        */
    }


    /**
     * 获取随附单证信息
     *
     * @param decLicenseDocus
     * @return 随附单证信息集合
     */
    private List<DecLicenseType> decLicenseByCustomsCode(List<DecLicenseDocus> decLicenseDocus) {

        List<DecLicenseType> decLicenseTypes = new ArrayList<>();
        for (DecLicenseDocus licenseDocus : decLicenseDocus) {
            if (isNotBlank(licenseDocus.getDocuCode())) {
                DecLicenseType decLicenseType = new DecLicenseType();
                decLicenseType.setCertCode(licenseDocus.getCertCode());
                decLicenseType.setDocuCode(licenseDocus.getDocuCode());
                decLicenseTypes.add(decLicenseType);
            }
        }

        return decLicenseTypes;
    }

    /**
     * 根据客户端编号获取报关单集装箱信息
     *
     * @return 报关单集装箱信息集合
     */
    private List<DecContainerType> decContainerByCustomsCode(List<DecContainer> decContainers) {

        List<DecContainerType> decContainerTypes = new ArrayList<>();
        DecContainerType decContainerType = null;
        for (DecContainer decContainer : decContainers) {
            decContainerType = new DecContainerType();
            decContainerType.setContainerId(decContainer.getContainerId());
            decContainerType.setContainerMd(decContainer.getContainerMd());
            decContainerType.setContainerWt(
                    decContainer.getGoodsContaWt() != null ? decContainer.getGoodsContaWt().toString() : null);
            decContainerType.setGoodsNo(decContainer.getGoodsNo());
            decContainerType.setLclFlag(decContainer.getLclFlag());
            decContainerTypes.add(decContainerType);
        }

        return decContainerTypes;
    }

    /**
     * 处理随附单据文件报文
     * @param decHead
     * @param attachment
     * @param filePath
     * @return
     */
    private Boolean sendEdocsMessage(DecHead decHead,DecAttachment attachment,String filePath) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = MessageFileUtil.export(EdocRealationMessage.generateDataInfo(decHead,attachment),
                    String.format("%s.xml", attachment.getId()));
            String xmlString = null;
            xmlString = new String(byteArrayOutputStream.toByteArray(), "utf-8");
            String xmlLength = xmlString;
            /*基本原理是将字符串中所有的非标准字符（双字节字符）替换成两个标准字符（**，或其他的也可以）。
            这样就可以直接例用length方法获得字符串的字节长度了*/
            xmlLength = xmlLength.replaceAll("[\\u4e00-\\u9fa5]", "**");
            int length = xmlLength.length();
            if (length < 2048) {
                String specStr = "";
                for (int i = 2048 - length; i > 0; i--) {
                    specStr = new StringBuilder(specStr).append(" ").toString();
                }
                xmlString = new StringBuilder(xmlString).append(specStr).toString();
            }
            writerPdf(filePath,xmlString);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("发送报关单报文=》处理随附单据XML出错：" + e.getMessage());
            return false;
        } catch (JAXBException e) {
            e.printStackTrace();
            log.info("发送报关单报文=》导出随附单据XML出错：" + e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 复制 文件文本追加
     * @param filePath
     * @param xmlString
     */
    private void writerPdf(String filePath,String xmlString) {

        try {
            RandomAccessFile accessFile = new RandomAccessFile(filePath, "rw");
            //获取文件长度
            long length = accessFile.length();
            //设置文件指针移动到文件末尾
            accessFile.seek(length);
//            accessFile.write(xmlString.getBytes());
            accessFile.write(xmlString.getBytes(StandardCharsets.UTF_8));
            accessFile.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("发送报关单报文=》写入xml内容出错：" + e.getMessage());
        }

    }

    @Override
    public void customExportDec(DecHead decHead, HttpServletRequest request, HttpServletResponse response) {
        long start = System.currentTimeMillis();
        decHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
        decHead.setOptUnitName(isNotBlank(decHead.getOptUnitName())?
                decHead.getOptUnitName().replaceAll("\\*",""):null);
        decHead.setSeqNo(isNotBlank(decHead.getSeqNo())?
                decHead.getSeqNo().replaceAll("\\*",""):null);
        decHead.setClearanceNo(isNotBlank(decHead.getClearanceNo())?
                decHead.getClearanceNo().replaceAll("\\*",""):null);
        decHead.setBillCode(isNotBlank(decHead.getBillCode())?
                decHead.getBillCode().replaceAll("\\*",""):null);
        decHead.setEtpsInnerInvtNo(isNotBlank(decHead.getEtpsInnerInvtNo())?
                decHead.getEtpsInnerInvtNo().replaceAll("\\*",""):null);
        decHead.setCreatePerson(isNotBlank(decHead.getCreatePerson())?
                decHead.getCreatePerson().replaceAll("\\*",""):null);
        decHead.setContract(isNotBlank(decHead.getContract())?
                decHead.getContract().replaceAll("\\*",""):null);
        decHead.setLicenceNumber(isNotBlank(decHead.getLicenceNumber())?
                decHead.getLicenceNumber().replaceAll("\\*",""):null);
        decHead.setDeliverUnitName(isNotBlank(decHead.getDeliverUnitName())?
                decHead.getDeliverUnitName().replaceAll("\\*",""):null);
        decHead.setRecordNumber(isNotBlank(decHead.getRecordNumber())?
                decHead.getRecordNumber().replaceAll("\\*",""):null);
        decHead.setDeclareUnitName(isNotBlank(decHead.getDeclareUnitName())?
                decHead.getDeclareUnitName().replaceAll("\\*",""):null);
        decHead.setIcNumber(isNotBlank(decHead.getIcNumber())?
                decHead.getIcNumber().replaceAll("\\*",""):null);
        decHead.setHsname(isNotBlank(decHead.getHsname())?
                decHead.getHsname().replaceAll("\\*",""):null);
        List<ExportDecExcel> exportDecExcelList=new ArrayList<>();
        //筛选出表体的字段  是否需要导出表体
        List<String> decListFields=new ArrayList<>();
        Field[] fields;
        fields = ExportDecExcel.class.getDeclaredFields();
        if (isNotEmpty(fields)) {
            for (Field field : fields) {
                if (null != field.getAnnotation(Excel.class)&&null == field.getAnnotation(DecHeadFields.class)) {
                    decListFields.add(field.getName());
                }
            }
        }
        decListFields.retainAll(Arrays.asList(decHead.getColumnList()));
        if(decListFields.isEmpty()){
            exportDecExcelList = baseMapper.customExportDecToDecHead(isNotBlank(decHead.getId())?
                    Arrays.asList(decHead.getId().split(",")):new ArrayList<>(), decHead);
        }else
        //20241224追加 页面不选择集装箱号则不需要以箱子分组
        if(!Arrays.asList(decHead.getColumnList()).contains("containerId")){
            exportDecExcelList = baseMapper.customExportDecToDecList(isNotBlank(decHead.getId())?
                    Arrays.asList(decHead.getId().split(",")):new ArrayList<>(), decHead);
        }else {
            exportDecExcelList = baseMapper.customExportDec(isNotBlank(decHead.getId())?
                    Arrays.asList(decHead.getId().split(",")):new ArrayList<>(), decHead);
        }
        log.info("======查询数据=====耗时:" + (System.currentTimeMillis() - start) + "毫秒");
        long start2 = System.currentTimeMillis();
        org.jeecgframework.poi.excel.entity.ExportParams exportParams = new org.jeecgframework.poi.excel.entity.ExportParams();
        exportParams.setSheetName("导出报关单详情");
        exportParams.setType(org.jeecgframework.poi.excel.entity.enmus.ExcelType.XSSF);
        exportParams.setAddIndex(true);
        //处理导出的数据
        processingExportData(exportDecExcelList);
        log.info("======处理数据=====耗时:" + (System.currentTimeMillis() - start2) + "毫秒");
        long start3 = System.currentTimeMillis();
        Workbook workbook = org.jeecgframework.poi.excel.ExcelExportUtil.exportExcel(exportParams,ExportDecExcel.class,
                exportDecExcelList,decHead.getColumnList());
        log.info("======生成数据=====耗时:" + (System.currentTimeMillis() - start3) + "毫秒");
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        // 下载文件能正常显示中文
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream fos = null;
        FileOutputStream FileFos = null;
        try {
            //普通下载
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (FileFos != null) {
                    FileFos.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    /*
    下载报关单excel或pdf
     */
    @Override
    public void exportDecComplete(String decId,String flag,
                                  HttpServletRequest request, HttpServletResponse response){
        List<String> decIdList = Arrays.asList(decId.split(","));
        // 批量获取数据
        Map<String, Object> data = fetchData(decIdList);

        response.setContentType("application/octet-stream");
        response.setHeader("content-type", "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        try {
            if (decIdList.size() == 1) {
                if ("EXCEL".equals(flag)) {
                    exportSingleDec(data, flag, response);
                } else if ("PDF".equals(flag) || "SHDPDF".equals(flag)) {
                    exportSingleDec(data, flag, response);
//                    exportPdf2(data, response);
                }
            } else {
                exportMultipleDecs(data, flag, response);
            }
        } catch (Exception e) {
            log.error("导出报关单失败", e);
        }
    }

    /**
     * 导出报关单PDF -- New 20250224
     *
     * @param data
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2025/2/24 13:33
     */
    private void exportPdf2(Map<String, Object> data, HttpServletResponse response) {
        HashMap<String, Object> map = new HashMap<>();


//        new PrintUtil().listPrintPdf(map,"customsDec","报关单",true,5,baseMapper.selectById(id),response);
    }

    private Map<String, Object> fetchData(List<String> decIdList) {
        Map<String, Object> data = new HashMap<>();
        data.put("decHeadList", baseMapper.selectBatchIds(decIdList));
        data.put("decListAll", decListMapper.selectList(Wrappers.lambdaQuery(DecList.class).in(DecList::getDecId, decIdList)));
        data.put("decLicenseDocusesAll", decLicenseDocusesService.list(Wrappers.lambdaQuery(DecLicenseDocus.class).in(DecLicenseDocus::getDecId, decIdList)));

        data.put("erpCustomsPortsList", erpCustomsPortsService.list());
        data.put("erpTransportTypesList", erpTransportTypesService.list());
        data.put("jgfs", decListMapper.getDictItemByCode("JGFS"));
        data.put("zmxz", decListMapper.getDictItemByCode("ZMXZ"));
        data.put("erpCountriesList", erpCountriesService.list());
        data.put("tradingTypeList", decListMapper.getDictItemByCode("trading_type"));
        data.put("erpPackagesTypesList", erpPackagesTypesService.list());
        data.put("erpChinaPortsList", erpChinaPortsService.list());
        data.put("erpUnitsList", erpUnitsService.list());
        data.put("erpCurrenciesList", erpCurrenciesService.list());
        data.put("zjmsfsList", decListMapper.getDictItemByCode("ZJMSFS"));
        data.put("erpDistrictsList", erpDistrictsService.list());
        data.put("erpCityportsList", erpCityportsService.list());
        data.put("xzqh", decListMapper.getDictItemByCode("XZQH"));

        return data;
    }

    private void exportSingleDec(Map<String, Object> data, String flag, HttpServletResponse response) throws Exception {
        DecHead decHead = ((List<DecHead>) data.get("decHeadList")).get(0);
        List<DecList> decLists = fetchDecLists(decHead.getId(), (List<DecList>) data.get("decListAll"));
        List<DecLicenseDocus> decLicenseDocuses = fetchDecLicenseDocuses(decHead.getId(), (List<DecLicenseDocus>) data.get("decLicenseDocusesAll"));
        HSSFWorkbook workbook = generateExcelDec(decHead, decLists, decLicenseDocuses, data, flag);
        if ("EXCEL".equals(flag)) {
//            StreamUtils.copy(workbook.getBytes(), response.getOutputStream());
            workbook.write(response.getOutputStream());
        } else if ("PDF".equals(flag) || "SHDPDF".equals(flag)) {
            byte[] pdfBytes = convertExcelToPdf(workbook, uploadpath);
            StreamUtils.copy(pdfBytes, response.getOutputStream());
        }
    }

    private void exportMultipleDecs(Map<String, Object> data, String flag, HttpServletResponse response) throws Exception {
        List<DecHead> decHeadList = (List<DecHead>) data.get("decHeadList");
        File exportDir = new File(uploadpath, "报关单批量导出");
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        for (DecHead decHead : decHeadList) {
            List<DecList> decLists = fetchDecLists(decHead.getId(), (List<DecList>) data.get("decListAll"));
            List<DecLicenseDocus> decLicenseDocuses = fetchDecLicenseDocuses(decHead.getId(), (List<DecLicenseDocus>) data.get("decLicenseDocusesAll"));
            HSSFWorkbook workbook = generateExcelDec(decHead, decLists, decLicenseDocuses, data, flag);
            String fileName = getFileName(decHead);
            File file = new File(exportDir, fileName + (flag.equals("EXCEL") ? ".xls" : ".pdf"));
            if ("EXCEL".equals(flag)) {
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    workbook.write(fos);
                    fos.flush();
                }
            } else if ("PDF".equals(flag) || "SHDPDF".equals(flag)) {
                byte[] pdfBytes = convertExcelToPdf(workbook, uploadpath);
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    fos.write(pdfBytes);
                    fos.flush();
                }
            }
        }
        log.info("导出文件目录：{}", exportDir.getAbsolutePath());
        downloadCompressedFile(exportDir, response);
//        IOCloseUtil.deleteAll(exportDir);
    }

    private List<DecList> fetchDecLists(String decId, List<DecList> decListAll) {
        return decListAll.stream().filter(dl -> dl.getDecId().equals(decId)).collect(Collectors.toList());
    }

    private List<DecLicenseDocus> fetchDecLicenseDocuses(String decId, List<DecLicenseDocus> decLicenseDocusesAll) {
        return decLicenseDocusesAll.stream().filter(dld -> dld.getDecId().equals(decId)).collect(Collectors.toList());
    }

    private HSSFWorkbook generateExcelDec(DecHead decHead, List<DecList> decLists, List<DecLicenseDocus> decLicenseDocuses, Map<String, Object> data, String flag) {
        return generateFileDec(decHead, decLists, decLicenseDocuses,
                (List<ErpCustomsPorts>) data.get("erpCustomsPortsList"),
                (List<ErpTransportTypes>) data.get("erpTransportTypesList"),
                (List<DictModelVO>) data.get("jgfs"),
                (List<DictModelVO>) data.get("zmxz"),
                (List<ErpCountries>) data.get("erpCountriesList"),
                (List<DictModelVO>) data.get("tradingTypeList"),
                (List<ErpPackagesTypes>) data.get("erpPackagesTypesList"),
                (List<ErpChinaPorts>) data.get("erpChinaPortsList"),
                (List<ErpUnits>) data.get("erpUnitsList"),
                (List<ErpCurrencies>) data.get("erpCurrenciesList"),
                (List<DictModelVO>) data.get("zjmsfsList"),
                (List<ErpDistricts>) data.get("erpDistrictsList"),
                (List<ErpCityports>) data.get("erpCityportsList"),
                (List<DictModelVO>) data.get("xzqh"),
                "SHDPDF".equals(flag) ? "1" : null);
    }

    private String getFileName(DecHead decHead) {
        // 获取合同号，如果为空则使用默认值 "无合同号"
//        String contractNo = isNotBlank(decHead.getContract()) ? decHead.getContract() : "无合同号";
        // 获取报关单号，如果为空则使用 decHead 的 ID
        String clearanceNo = isNotBlank(decHead.getClearanceNo()) ? decHead.getClearanceNo() : decHead.getId();
        // 拼接文件名，格式为 "合同号_报关单号"
        return clearanceNo;
    }

    private byte[] convertExcelToPdf(HSSFWorkbook workbook, String uploadPath) throws Exception {
        String tempFileName = "temp" + IdUtil.simpleUUID() + ".xls";
        File tempFile = new File(uploadPath, tempFileName);
        File parentDir = tempFile.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            workbook.write(fos);
            fos.flush();
        }
        ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf_(tempFile.getAbsolutePath(), "报关单导出", "");
        log.info("[入库单]生成文件成功：==> {}", tempFile.getAbsolutePath());
        return responseEntity.getBody();
    }

    private void downloadCompressedFile(File directory, HttpServletResponse response) throws Exception {
//        response.setHeader("content-type", "application/octet-stream");
//        response.setCharacterEncoding("utf-8");
//        response.setHeader("Content-disposition",
//                "attachment;filename=" + new String("报关单批量导出".getBytes("gbk"), "iso8859-1") + ".zip");
//
//        // 直接以流的方式进行压缩和传输
//        try (ZipOutputStream out = new ZipOutputStream(response.getOutputStream());
//             BufferedOutputStream bos = new BufferedOutputStream(out)) {
//            // 调用压缩函数
//            compressDirectory(directory, directory.getName(), out, bos);
//            bos.flush();
//        }
            //进行压缩，将指定文件夹下的文件进行压缩
            try {
                FolderToZipUtil.zip(directory.getAbsolutePath(), response);
            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                File file2 = new File(uploadpath + "/报关单批量导出");
                IOCloseUtil.deleteAll(file2);
            }
    }

    // 递归压缩目录内容
    private void compressDirectory(File source, String base, ZipOutputStream zos, BufferedOutputStream bos) throws IOException {
        // 如果是文件，直接压缩
        if (source.isFile()) {
            zos.putNextEntry(new ZipEntry(base));
            try (FileInputStream fis = new FileInputStream(source);
                 BufferedInputStream bis = new BufferedInputStream(fis)) {
                int read;
                byte[] buffer = new byte[8192];
                while ((read = bis.read(buffer)) != -1) {
                    bos.write(buffer, 0, read);
                }
            }
            zos.closeEntry();
            return;
        }

        // 如果是目录，递归处理
        File[] files = source.listFiles();
        if (files == null || files.length == 0) {
            // 空目录也要加入
            zos.putNextEntry(new ZipEntry(base + '/'));
            zos.closeEntry();
        } else {
            for (File file : files) {
                // 递归压缩
                compressDirectory(file, base + '/' + file.getName(), zos, bos);
            }
        }
    }

//    /*
//    下载报关单excel或pdf
//     */
//    @Override
//    public void exportDecComplete(String decId,String flag,
//                                  HttpServletRequest request, HttpServletResponse response){
//        List<String> decIdList=Arrays.asList(decId.split(","));
//        //全部表头信息
//        List<DecHead> decHeadList = baseMapper.selectBatchIds(decIdList);
//        List<DecList> decListAll = decListMapper.selectList(new LambdaQueryWrapper<DecList>()
//        .in(DecList::getDecId,decIdList));
//        //全部随附单据
//        List<DecLicenseDocus> decLicenseDocusesAll=decLicenseDocusesService.list(new LambdaQueryWrapper<DecLicenseDocus>()
//                .in(DecLicenseDocus::getDecId,decIdList));
////        需要用到的全部海关代码
//        //海关代码
//        List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list();
//        //运输方式
//        List<ErpTransportTypes> erpTransportTypesList = erpTransportTypesService.list();
//        //监管方式
//        List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
//        //征免性质
//        List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
//        //国家
//        List<ErpCountries> erpCountriesList = erpCountriesService.list();
//        //成交方式
//        List<DictModelVO> tradingTypeList = decListMapper.getDictItemByCode("trading_type");
//        //包装种类
//        List<ErpPackagesTypes> erpPackagesTypesList = erpPackagesTypesService.list();
//        //口岸数据
//        List<ErpChinaPorts> erpChinaPortsList = erpChinaPortsService.list();
//        //单位
//        List<ErpUnits> erpUnitsList = erpUnitsService.list();
//        //币制
//        List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list();
//        //征免方式
//        List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");
//        //货源地
//        List<ErpDistricts> erpDistrictsList = erpDistrictsService.list();
//        //港口
//        List<ErpCityports> erpCityportsList = erpCityportsService.list();
//        //行政区划
//        List<DictModelVO> xzqh = decListMapper.getDictItemByCode("XZQH");
//        response.setHeader("content-type", "application/octet-stream");
//        response.setContentType("application/octet-stream");
//        // 下载文件能正常显示中文
//        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
//        //只有一条记录直接导出excel或pdf
//        if(decHeadList.size()==1){
//            DecHead decHead=decHeadList.get(0);
//            //该报关单的表体数据
//            List<DecList> decLists=decListAll.stream().filter(i->i.getDecId().equals(decHead.getId()))
//                    .collect(Collectors.toList());
//            //随附单据
//            List<DecLicenseDocus> decLicenseDocuses=decLicenseDocusesAll.stream().filter(i->i.getDecId().equals(decHead.getId()))
//                    .collect(Collectors.toList());
//            HSSFWorkbook workbook = generateFileDec(decHead, decLists, decLicenseDocuses, erpCustomsPortsList, erpTransportTypesList,
//                    jgfs, zmxz, erpCountriesList, tradingTypeList, erpPackagesTypesList, erpChinaPortsList, erpUnitsList,
//                    erpCurrenciesList, zjmsfsList, erpDistrictsList, erpCityportsList, xzqh,"SHDPDF".equals(flag)? "1":null);
//            OutputStream fos = null;
//            InputStream in = null;
//            try {
//                //EXCEL普通下载
//                if("EXCEL".equals(flag)){
//                    fos = response.getOutputStream();
//                    workbook.write(fos);
//                }else if("PDF".equals(flag)||"SHDPDF".equals(flag)){
//                    //PDF下载，excel转pdf
//                    ByteArrayOutputStream out = new ByteArrayOutputStream();
//                    workbook.write(out);
//                    byte[] bookByte = out.toByteArray();
//                    in=new ByteArrayInputStream(bookByte);
//
//                    String fileName = "temp" + IdUtil.simpleUUID() + ".xlsx";
//                    String xmlPath = uploadpath + "/downloadPdf/" + fileName;
//                    File file1 = new File(xmlPath);
//                    // Create parent directories if they don't exist
//                    File parentDir1 = file1.getParentFile();
//                    if (!parentDir1.exists()) {
//                        parentDir1.mkdirs();  // Create the directory and any necessary but nonexistent parent directories
//                    }
//                    FileOutputStream foss;
//                    try {
//                        foss = new FileOutputStream(file1);
//                        foss.write(bookByte, 0, bookByte.length);
//                        foss.flush();
//                        foss.close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                    log.info("[入库单]生成文件成功：==> {}", xmlPath);
//
//                    ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf_(xmlPath, "报关单导出", "");
//                    byte[] bytes = responseEntity.getBody();
//                    fos = response.getOutputStream();
//                    fos.write(bytes);
//                }
//
//            } catch (Exception e) {
//                e.printStackTrace();
//            } finally {
//                try {
//                    if (fos != null) {
//                        fos.close();
//                    }
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }else {
//            //创建临时文件夹
//            File savefile = new File(uploadpath+"/报关单批量导出");
//            if (!savefile.exists()) {
//                savefile.mkdirs();
//            }
//            //多个文件打成压缩包下载
//            for(DecHead decHead:decHeadList){
//                //该报关单的表体数据
//                List<DecList> decLists=decListAll.stream().filter(i->i.getDecId().equals(decHead.getId()))
//                        .collect(Collectors.toList());
//                //随附单据
//                List<DecLicenseDocus> decLicenseDocuses=decLicenseDocusesAll.stream().filter(i->i.getDecId().equals(decHead.getId()))
//                        .collect(Collectors.toList());
//                HSSFWorkbook workbook = generateFileDec(decHead, decLists, decLicenseDocuses, erpCustomsPortsList, erpTransportTypesList,
//                        jgfs, zmxz, erpCountriesList, tradingTypeList, erpPackagesTypesList, erpChinaPortsList, erpUnitsList,
//                        erpCurrenciesList, zjmsfsList, erpDistrictsList, erpCityportsList, xzqh,"SHDPDF".equals(flag)? "1":null);
//                OutputStream fos = null;
//                InputStream in = null;
//                try {
//                    //EXCEL文件
//                    if("EXCEL".equals(flag)){
//                        fos=new FileOutputStream(uploadpath+"/报关单批量导出/报关单" + (isNotBlank(decHead.getClearanceNo())?
//                                decHead.getClearanceNo():decHead.getId()) + ".xlsx");
//                        workbook.write(fos);
//                    }else if("PDF".equals(flag)||"SHDPDF".equals(flag)){
//                        //PDF文件，excel转pdf
//                        ByteArrayOutputStream out = new ByteArrayOutputStream();
//                        workbook.write(out);
//                        byte[] bookByte = out.toByteArray();
//                        in=new ByteArrayInputStream(bookByte);
//                        ResponseEntity<byte[]> responseEntity = ExcelToPdfUtil.excel2pdf(in, "报关单导出", "");
//                        byte[] bytes = responseEntity.getBody();
//                        fos=new FileOutputStream(uploadpath+"/报关单批量导出/报关单" + (isNotBlank(decHead.getClearanceNo())?
//                                decHead.getClearanceNo():decHead.getId()) + ".pdf");
//                        fos.write(bytes);
//                    }
//
//                } catch (Exception e) {
//                    e.printStackTrace();
//                } finally {
//                    try {
//                        if (fos != null) {
//                            fos.close();
//                        }
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//
//            }
//            //进行压缩，将指定文件夹下的文件进行压缩
//            String zipPath = uploadpath + "/报关单批量导出";
//            log.info(zipPath);
//            File downLoadFile = new File(zipPath);
//            if (!downLoadFile.exists()) {
//                downLoadFile.mkdirs();
//            }
//            try {
//                FolderToZipUtil.zip(zipPath,response);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }finally {
//                File file2 = new File(uploadpath + "/报关单批量导出");
//                IOCloseUtil.deleteAll(file2);
//            }
//        }
//
//    }

    /**
     * 根据报关单信息返回舱单信息
     *
     * @param pc
     * @param tt
     * @param wb
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    @Override
    public Result<?> getManifestInfo(String id, String pc, String tt, String wb,String swid) {
        //如果操作员卡号空再取一次
        if(isBlank(swid)){
            //获取登录企业的报关员操作卡号
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                    .eq(EnterpriseInfo::getTenantId, Long.valueOf(TenantContext.getTenant())));
            if (isNotEmpty(enterpriseInfo) && isNotBlank(enterpriseInfo.getSwid())) {
                swid = enterpriseInfo.getSwid();
            }
        }
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", swid);
        jsonMap.put("pc", pc);
        jsonMap.put("tt", tt);
        jsonMap.put("mwb", "");
        jsonMap.put("wb", wb);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ob = new JSONArray();
        if (jsonObject.getBoolean("ok")) {
            try {
                ob = jsonObject.getJSONArray("data");
                if(ob.size()>0){
                    // 设置忽略租户插件
                    InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
                    //存在舱单信息
                    baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                            .set(DecHead::getHasCd, "1")
                            // 2025/4/9 17:07@ZHANGCHAO 追加/变更/完善：运抵状态由周哥推送箱货节点数据而来！！！
//                            .set("运抵正常".equals(ob.getJSONObject(0).getString("cargoArrivalState")),
//                                    DecHead::getHasYd,"1")
                            .eq(DecHead::getId, id));
                    // 关闭忽略策略
                    InterceptorIgnoreHelper.clearIgnoreStrategy();
                }

            } catch (Exception e) {
                log.error("转换数据时出现异常：{}", e.getMessage());
            }
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ob);
    }

    /**
     * 根据船名航次提单号返回海运舱单的信息
     *
     * @param iEFlag
     * @param trafName
     * @param cusVoyageNo
     * @param billNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/24 下午2:08
     */
    @Override
    public Result<?> getShipManifestInfo(String id, String iEFlag, String trafName, String cusVoyageNo, String billNo) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID);
        jsonMap.put("iEFlag", iEFlag);
        jsonMap.put("trafName", trafName);
        jsonMap.put("cusVoyageNo", cusVoyageNo);
        jsonMap.put("billNo", billNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_SHIP, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONObject ob = new JSONObject();
        if (jsonObject.getBoolean("ok")) {
            ob = jsonObject.getJSONObject("data");
            baseMapper.update(null, new UpdateWrapper<DecHead>().lambda()
                    .set(DecHead::getHasCd, "1")
                    .eq(DecHead::getId, id));
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ob);
    }

    /**
     * 企业报关差错记录
     * 返回企业报关差错记录和详细错误信息
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    @Override
    public Result<?> getCusError(String starDate, String lastDate) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("beginDate", starDate);
        jsonMap.put("endDate", lastDate);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CUS_ERROR, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ob = new JSONArray();
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")) && jsonObject.get("data") instanceof JSONArray) {
            ob = jsonObject.getJSONArray("data");
            log.info("获取到的差错数据条数是：{}", ob.size());
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ob);
    }

    /**
     * 企业修撤单记录
     * 根据时间返回报关单修撤单的记录列表
     *
     * @param starDate
     * @param lastDate
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/25 上午10:04
     */
    @Override
    public Result<?> getDecModList(String starDate, String lastDate) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("beginDate", starDate);
        jsonMap.put("endDate", lastDate);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_DEC_MOD, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ob = new JSONArray();
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")) && jsonObject.get("data") instanceof JSONArray) {
            ob = jsonObject.getJSONArray("data");
            log.info("获取到的修撤单数据条数是：{}", ob.size());
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ob);
    }

    /**
     * 同步修撤单数据
     * @param starDate
     * @param lastDate
     * @return
     */
    @Override
    public Result<?> syncDecMod(String starDate, String lastDate) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("beginDate", starDate);
        jsonMap.put("endDate", lastDate);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_DEC_MOD, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ob;
        List<RepairCancellationOrders> repairCancellationOrdersList = new ArrayList<>();
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")) && jsonObject.get("data") instanceof JSONArray) {
            ob = jsonObject.getJSONArray("data");
            log.info("获取到的修撤单数据条数是：{}", ob.size());
            ob.forEach(o -> {
                JSONObject jo = (JSONObject) o;
                repairCancellationOrdersList.add(setRepairCancellationOrdersByJson(jo));
            });
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        if (isNotEmpty(repairCancellationOrdersList)) {
            for (RepairCancellationOrders repairCancellationOrders : repairCancellationOrdersList) {
                Map<String, Object> jsonMap1 = new LinkedHashMap<>();
                jsonMap1.put("swid", SWID_XJA);
                jsonMap1.put("decModSeqNo", repairCancellationOrders.getDecModSeqNo());
                String result1 = sendOpenApi(URL_DEC_MOD_DETAILS, JSON.toJSONString(jsonMap1));
                log.info("请求结果1：{}", result1);
                JSONObject jsonObject1 = JSON.parseObject(result1);
                if (isNotEmpty(jsonObject1.get("data")) && isNotEmpty(((JSONObject) jsonObject1.get("data")).get("decModHeadVo"))) {
                    setRepairCancellationOrdersByJsonNext(repairCancellationOrders, (JSONObject) jsonObject1.get("data"));
                }
            }
            log.info("最终需要新增或更新的数据为：{}", repairCancellationOrdersList);
            repairCancellationOrdersService.saveOrUpdateBatch(repairCancellationOrdersList);
        }
        return Result.ok("同步成功");
    }

    /**
     * 报关单税单信息
     * 返回指定报关单的税单信息（含税单货物信息）
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @Override
    public Result<?> getCDTax(String entryId) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("entryId", entryId);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CD_TAX, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ja = new JSONArray();
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")) && jsonObject.get("data") instanceof JSONArray) {
           ja = jsonObject.getJSONArray("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 返回税单核对单PDF文件
     * 根据报关单税单号或报关单号来获取税单核对单PDF格式文件内容。
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/27 下午3:23
     */
    @Override
    public Result<?> getCDTaxPdf(String entryId) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("entryId", entryId);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CD_TAX_PDF, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        String ja = "";
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
            ja = jsonObject.getString("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 报关单文件退税联(PDF)
     * 根据报关单号来获取报关单退税联 PDF 格式文件内容。如果成功，直接返回 pdf 文件。否则返回 json 格式错误。
     *
     * @param entryId
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:36
     */
    @Override
    public Result<?> getCDPdfRtx(String entryId) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("entryId", entryId);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CD_RTX_PDF, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        String ja = "";
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
            ja = jsonObject.getString("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 报关单放行通知(PDF)
     * 根据报关单统一编号或报关单号来获取放行通知 PDF 格式文件内容。建议放行后再调取。
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午1:55
     */
    @Override
    public Result<?> getCDReleaseNote(String cusCiqNo) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("cusCiqNo", cusCiqNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CD_RELEASE_PDF, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        String ja = "";
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
            ja = jsonObject.getString("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 报关单查验通知(PDF)
     * 根据报关单统一编号或报关单号来获取查验通知 PDF 格式文件内容。如果没有查验通知，返回错误信息
     *
     * @param cusCiqNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/28 下午2:04
     */
    @Override
    public Result<?> getCDCheckNote(String cusCiqNo) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", SWID_XJA);
        jsonMap.put("cusCiqNo", cusCiqNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_CD_CHECK_PDF, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        String ja = "";
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
            ja = jsonObject.getString("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 获取进出口状态
     * 返回指定报关单或提单号的进出口状态。
     *
     * @param ieFlag
     * @param entryId
     * @param billNo
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/7/4 下午1:09
     */
    @Override
    public Result<?> getSwIEStatus(String ieFlag, String entryId, String billNo) {
        if (isBlank(billNo) && isBlank(entryId)) {
            return Result.error("请输入报关单号或提运单号!");
        }
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, Long.valueOf(TenantContext.getTenant())));
        String swid = SWID_XJA;
        if (isNotEmpty(enterpriseInfo) && isNotBlank(enterpriseInfo.getSwid())) {
            swid = enterpriseInfo.getSwid();
        }
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("swid", swid);
        jsonMap.put("ieFlag", ieFlag);
        jsonMap.put("entryId", entryId);
        jsonMap.put("billNo", billNo);
        log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
        String result = sendOpenApi(URL_SW_IE_STATUS, JSON.toJSONString(jsonMap));
        log.info("请求结果：{}", result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray ja = new JSONArray();
        if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data")) && jsonObject.get("data") instanceof JSONArray) {
            ja = jsonObject.getJSONArray("data");
        } else {
            log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
        }
        return Result.ok(ja);
    }

    /**
     * 进一步转换修撤单数据
     *
     * @param repairCancellationOrders
     * @param data
     * @return void
     * <AUTHOR>
     * @date 2024/6/25 下午5:12
     */
    private void setRepairCancellationOrdersByJsonNext(RepairCancellationOrders repairCancellationOrders, JSONObject data) {
        JSONObject jo = (JSONObject) data.get("decModHeadVo");
        if (isNotEmpty(jo)) {
            repairCancellationOrders.setAgentCode(isNotBlank(jo.getString("agentCode")) ? jo.getString("agentCode") : repairCancellationOrders.getAgentCode()); // 报关单申报单位海关编码
            repairCancellationOrders.setAgentName(isNotBlank(jo.getString("agentName")) ? jo.getString("agentName") : repairCancellationOrders.getAgentName());
            repairCancellationOrders.setAgentScc(isNotBlank(jo.getString("agentScc")) ? jo.getString("agentScc") : repairCancellationOrders.getAgentScc());
            repairCancellationOrders.setRcvgdTradeCode(isNotBlank(jo.getString("rcvgdTradeCode")) ? jo.getString("rcvgdTradeCode") : repairCancellationOrders.getRcvgdTradeCode());
            repairCancellationOrders.setRcvgdTradeScc(isNotBlank(jo.getString("rcvgdTradeScc")) ? jo.getString("rcvgdTradeScc") : repairCancellationOrders.getRcvgdTradeScc());
            repairCancellationOrders.setConsigneeCname(isNotBlank(jo.getString("consigneeCname")) ? jo.getString("consigneeCname") : repairCancellationOrders.getConsigneeCname());
            repairCancellationOrders.setConsignorCname(isNotBlank(jo.getString("consignorCname")) ? jo.getString("consignorCname") : repairCancellationOrders.getConsignorCname());
            repairCancellationOrders.setCnsnTradeCode(isNotBlank(jo.getString("cnsnTradeCode")) ? jo.getString("cnsnTradeCode") : repairCancellationOrders.getCnsnTradeCode());
            repairCancellationOrders.setCnsnTradeScc(isNotBlank(jo.getString("cnsnTradeScc")) ? jo.getString("cnsnTradeScc") : repairCancellationOrders.getCnsnTradeScc());
            repairCancellationOrders.setOwnerCode(isNotBlank(jo.getString("ownerCode")) ? jo.getString("ownerCode") : repairCancellationOrders.getOwnerCode());
            repairCancellationOrders.setOwnerName(isNotBlank(jo.getString("ownerName")) ? jo.getString("ownerName") : repairCancellationOrders.getOwnerName());
            repairCancellationOrders.setOwnerScc(isNotBlank(jo.getString("ownerScc")) ? jo.getString("ownerScc") : repairCancellationOrders.getOwnerScc());
            repairCancellationOrders.setDecModNote(isNotBlank(jo.getString("decModNote")) ? jo.getString("decModNote") : repairCancellationOrders.getDecModNote());
            repairCancellationOrders.setInputEtpsCode(isNotBlank(jo.getString("inputEtpsCode")) ? jo.getString("inputEtpsCode") : repairCancellationOrders.getInputEtpsCode());
            repairCancellationOrders.setInputEtpsName(isNotBlank(jo.getString("inputEtpsName")) ? jo.getString("inputEtpsName") : repairCancellationOrders.getInputEtpsName());
            repairCancellationOrders.setInputErName(isNotBlank(jo.getString("inputErName")) ? jo.getString("inputErName") : repairCancellationOrders.getInputErName());
            repairCancellationOrders.setInputTime(isNotBlank(jo.getString("inputTime")) ? jo.getString("inputTime") : repairCancellationOrders.getInputTime());
            repairCancellationOrders.setApplWkunitCode(isNotBlank(jo.getString("applWkunitCode")) ? jo.getString("applWkunitCode") : repairCancellationOrders.getApplWkunitCode());
            repairCancellationOrders.setApplWkunitName(isNotBlank(jo.getString("applWkunitName")) ? jo.getString("applWkunitName") : repairCancellationOrders.getApplWkunitName());
            repairCancellationOrders.setApplWkunitScc(isNotBlank(jo.getString("applWkunitScc")) ? jo.getString("applWkunitScc") : repairCancellationOrders.getApplWkunitScc());
            repairCancellationOrders.setAppllcCode(isNotBlank(jo.getString("applIcCode")) ? jo.getString("applIcCode") : repairCancellationOrders.getAppllcCode());
            repairCancellationOrders.setApplErName(isNotBlank(jo.getString("applErName")) ? jo.getString("applErName") : repairCancellationOrders.getApplErName());
            repairCancellationOrders.setApplTime(isNotBlank(jo.getString("applTime")) ? jo.getString("applTime") : repairCancellationOrders.getApplTime());
            repairCancellationOrders.setFeedDept(isNotBlank(jo.getString("feedDept")) ? jo.getString("feedDept") : repairCancellationOrders.getFeedDept());
        }
        JSONArray ja = (JSONArray) data.get("decModList");
        if (isNotEmpty(ja)) {
            repairCancellationOrders.setDecModList(JSON.toJSONString(ja));
        }
        JSONObject jo1 = (JSONObject) data.get("preDecDoc");
        if (isNotEmpty(jo1)) {
            repairCancellationOrders.setPreDecDoc(JSON.toJSONString(jo1));
        }
    }

    /**
     * 转换修撤单数据
     *
     * @param jo
     * @return org.jeecg.modules.business.entity.RepairCancellationOrders
     * <AUTHOR>
     * @date 2024/6/25 下午3:36
     */
    private RepairCancellationOrders setRepairCancellationOrdersByJson(JSONObject jo) {
        if (isEmpty(jo)) return null;
        String decModSeqNo = jo.getString("decModSeqNo");
        RepairCancellationOrders repairCancellationOrders = repairCancellationOrdersMapper.selectOne(new QueryWrapper<RepairCancellationOrders>().lambda()
                .eq(RepairCancellationOrders::getDecModSeqNo, decModSeqNo));
        if (isEmpty(repairCancellationOrders)) {
            repairCancellationOrders = new RepairCancellationOrders();
        }
        repairCancellationOrders.setDecModSeqNo(isNotBlank(decModSeqNo) ? decModSeqNo : repairCancellationOrders.getDecModSeqNo()); // 修撤申请单编号
        repairCancellationOrders.setDecModType(isNotBlank(jo.getString("decModType")) ? jo.getString("decModType") : repairCancellationOrders.getDecModType()); // 申请单类型代码
        repairCancellationOrders.setDecModTypeName(isNotBlank(jo.getString("decModTypeName")) ? jo.getString("decModTypeName") : repairCancellationOrders.getDecModTypeName()); // 申请单类型代码
        repairCancellationOrders.setEntryId(isNotBlank(jo.getString("entryId")) ? jo.getString("entryId") : repairCancellationOrders.getEntryId()); // 报关单编号
        repairCancellationOrders.setCusCiqNo(isNotBlank(jo.getString("cusCiqNo")) ? jo.getString("cusCiqNo") : repairCancellationOrders.getCusCiqNo()); // 报关单统一编号
        repairCancellationOrders.setDecModStatusName(isNotBlank(jo.getString("decModStatusName")) ? jo.getString("decModStatusName") : repairCancellationOrders.getDecModStatusName()); // 单据状态

        repairCancellationOrders.setCusIeFlag(isNotBlank(jo.getString("cusIEFlag")) ? jo.getString("cusIEFlag") : repairCancellationOrders.getCusIeFlag()); // 单据状态
        repairCancellationOrders.setCustomMaster(isNotBlank(jo.getString("customMaster")) ? jo.getString("customMaster") : repairCancellationOrders.getCustomMaster()); // 单据状态
        repairCancellationOrders.setCustomMasterName(isNotBlank(jo.getString("customMasterName")) ? jo.getString("customMasterName") : repairCancellationOrders.getCustomMasterName()); // 单据状态
        repairCancellationOrders.setUpdateTime1(isNotBlank(jo.getString("updateTime")) ? jo.getString("updateTime") : repairCancellationOrders.getUpdateTime1()); // 单据状态
        repairCancellationOrders.setEntOpName(isNotBlank(jo.getString("entOpName")) ? jo.getString("entOpName") : repairCancellationOrders.getEntOpName()); // 单据状态
        repairCancellationOrders.setEntOpTel(isNotBlank(jo.getString("entOpTel")) ? jo.getString("entOpTel") : repairCancellationOrders.getEntOpTel()); // 单据状态
        repairCancellationOrders.setDecModStatus(isNotBlank(jo.getString("decModStatus")) ? jo.getString("decModStatus") : repairCancellationOrders.getDecModStatus()); // 单据状态
        repairCancellationOrders.setModCusNo(isNotBlank(jo.getString("modCusNo")) ? jo.getString("modCusNo") : repairCancellationOrders.getModCusNo()); // 单据状态

        return repairCancellationOrders;
    }

    //生成报关单excel或pdf
    private HSSFWorkbook generateFileDec(DecHead decHead,List<DecList> decLists,List<DecLicenseDocus> decLicenseDocuses,
                                   List<ErpCustomsPorts> erpCustomsPortsList, List<ErpTransportTypes> erpTransportTypesList,
                                   List<DictModelVO> jgfs,List<DictModelVO> zmxz,
                                   List<ErpCountries> erpCountriesList,List<DictModelVO> tradingTypeList,
                                   List<ErpPackagesTypes> erpPackagesTypesList,List<ErpChinaPorts> erpChinaPortsList,
                                   List<ErpUnits> erpUnitsList,List<ErpCurrencies> erpCurrenciesList,List<DictModelVO> zjmsfsList,
                                   List<ErpDistricts> erpDistrictsList,List<ErpCityports> erpCityportsList,
                                         List<DictModelVO> xzqh,String shdFlag){
        List<DecContainer> decContainers = decContainerService.list(new QueryWrapper<DecContainer>().lambda()
                .eq(DecContainer::getDecId,decHead.getId()));
        DecimalFormat df = new DecimalFormat("###.####");
        DecimalFormat dft = new DecimalFormat("0.0000");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileName = "报关单"+(isNotBlank(decHead.getClearanceNo())?decHead.getClearanceNo():decHead.getId());
        int count = (decLists.size() - 6) / 14 + ((decLists.size() - 6) % 14 > 0 ? 1 : 0);
        ResponseEntity<byte[]> resp = null;
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            HSSFWorkbook book = null;
            //获得Excel格式合同模板
            if (decHead.getIeFlag().equals("I")) {
                book = new HSSFWorkbook(this.getClass().getResourceAsStream("/templates/xls/decExcelModelIn.xls"));
            } else if (decHead.getIeFlag().equals("E")) {
                book = new HSSFWorkbook(this.getClass().getResourceAsStream("/templates/xls/decExcelModelOut.xls"));
            } else {
                return null;
            }

            HSSFSheet sheet = book.getSheetAt(0);
            HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
            byte[] bytes1 = QRCodeAndBarcodeTool.createQRCode((!ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "*"+decHead.getClearanceNo()+"*" : "*xxxxxxxxxxxxxxxxxx*"));
            byte[] bytes2 = QRCodeAndBarcodeTool.createEan13((!ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "*"+decHead.getClearanceNo()+"*" : "*xxxxxxxxxxxxxxxxxx*"));
            HSSFClientAnchor anchor1 = new HSSFClientAnchor(300, 70, 450, 200, (short) 27, 0, (short) 29, 2);
            HSSFClientAnchor anchor2 = new HSSFClientAnchor(0, 40, 1000, 250, (short) 20, 0, (short) 26, 0);
            //插入图片
            patriarch.createPicture(anchor1, book.addPicture(bytes1, HSSFWorkbook.PICTURE_TYPE_PNG));
            patriarch.createPicture(anchor2, book.addPicture(bytes2, HSSFWorkbook.PICTURE_TYPE_PNG));

            HSSFRow row = null;
            HSSFCell cell = null;

            row = sheet.getRow(1);
            cell = row.getCell(20);
            cell.setCellValue((!ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "*" + decHead.getClearanceNo() + "*" : "*xxxxxxxxxxxxxxxxxx*").replaceAll("", " ").trim());

            row = sheet.getRow(3);
            cell = row.getCell(5);
            cell.setCellValue(decHead.getClearanceNo());
            cell = row.getCell(10);
            cell.setCellValue(decHead.getClearanceNo());
            cell = row.getCell(14);
            if(isNotBlank(decHead.getDeclarePlace())){
                List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                        .equals(decHead.getDeclarePlace()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
            }

            cell = row.getCell(28);
            cell.setCellValue("1/" + (count + 1));

            row = sheet.getRow(5);
            cell = row.getCell(4);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getOptUnitId()) ? "(" + decHead.getOptUnitId() + ")" : "");
            cell = row.getCell(10);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getOutPortCode()) ? "(" + decHead.getOutPortCode() + ")" : "");

            row = sheet.getRow(6);
            cell = row.getCell(1);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getOptUnitId()) ? decHead.getOptUnitName() : "");
            cell = row.getCell(9);
            if(isNotBlank(decHead.getOutPortCode())){
                List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                        .equals(decHead.getOutPortCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
            }
            cell = row.getCell(13);
            cell.setCellValue(decHead.getOutDate());
            cell = row.getCell(18);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getAppDate()) ? sdf.format(decHead.getAppDate()) : "");
            cell = row.getCell(22);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getRecordNumber()) ? decHead.getRecordNumber() : "");

            row = sheet.getRow(7);
            cell = row.getCell(10);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getShipTypeCode()) ? "(" + decHead.getShipTypeCode() + ")" : "");

            row = sheet.getRow(8);
            cell = row.getCell(1);
            if (decHead.getIeFlag().equals("I")) {
                cell.setCellValue(!ObjectUtils.isEmpty(decHead.getOverseasConsignorEname()) ? decHead.getOverseasConsignorEname() : "");
            } else {
                cell.setCellValue(!ObjectUtils.isEmpty(decHead.getOverseasConsigneeEname()) ? decHead.getOverseasConsigneeEname() : "");
            }

            cell = row.getCell(9);
            //运输方式
            if(StringUtils.isNotBlank(decHead.getShipTypeCode())){
                List<ErpTransportTypes> erpTransportTypes=erpTransportTypesList.stream().filter(i->i.getCode()
                        .equals(decHead.getShipTypeCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpTransportTypes.size()>0?erpTransportTypes.get(0).getName():"");
            }
            cell = row.getCell(13);//运输工具名称及航次号
            cell.setCellValue((ObjectUtils.isEmpty(decHead.getShipName()) ? "" : decHead.getShipName()) +"/"+ (ObjectUtils.isEmpty(decHead.getVoyage()) ? "" : decHead.getVoyage()));
            cell = row.getCell(18);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getBillCode()) ? decHead.getBillCode() : "");
            cell = row.getCell(22);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getGoodsPlace()) ? decHead.getGoodsPlace() : "");

            row = sheet.getRow(9);
            cell = row.getCell(6);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDeliverUnit()) ? "(" + decHead.getDeliverUnit() + ")" : "");
            cell = row.getCell(10);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getTradeTypeCode()) ? "(" + decHead.getTradeTypeCode() + ")" : "");
            cell = row.getCell(14);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getTaxTypeCode()) ? "(" + decHead.getTaxTypeCode() + ")" : "");
            cell = row.getCell(23);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDespPortCode()) ? "(" + decHead.getDespPortCode() + ")" : "");

            row = sheet.getRow(10);
            cell = row.getCell(1);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDeliverUnitName()) ? decHead.getDeliverUnitName().split(":")[0] : "");
            cell = row.getCell(9);
            //监管方式
            if(StringUtils.isNotBlank(decHead.getTradeTypeCode())){
                List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getValue()
                        .equals(decHead.getTradeTypeCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }

            cell = row.getCell(13);
            //征免性质
            if(StringUtils.isNotBlank(decHead.getTaxTypeCode())){
                List<DictModelVO> dictModelVO1=zmxz.stream().filter(i->i.getValue()
                        .equals(decHead.getTaxTypeCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }

            cell = row.getCell(18);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getLicenceNumber()) ? decHead.getLicenceNumber() : "");
            cell = row.getCell(22);
            //启运港
            if(StringUtils.isNotBlank(decHead.getDespPortCode())){
                List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                        .equals(decHead.getDespPortCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
            }

            row = sheet.getRow(11);
            cell = row.getCell(11);     //贸易国(地区)
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getTradeCountry()) ? "(" + decHead.getTradeCountry() + ")" : "");
            cell = row.getCell(15);     //启运国(地区)
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getArrivalArea()) ? "(" + decHead.getArrivalArea() + ")" : "");
            cell = row.getCell(19);     //经停港
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDesPort()) ? "(" + decHead.getDesPort() + ")" : "");
            cell = row.getCell(24);     //入境口岸
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getEntyPortCode()) ? "(" + decHead.getEntyPortCode() + ")" : "");

            row = sheet.getRow(12);
            cell = row.getCell(1);     //合同协议号
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getContract()) ? decHead.getContract() : "");
            cell = row.getCell(9);     //贸易国(地区)
            //贸易国
            if(StringUtils.isNotBlank(decHead.getTradeCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                        .equals(decHead.getTradeCountry()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
            }
            cell = row.getCell(13);     //启运国(地区)
            //启运国/运抵国
            if(StringUtils.isNotBlank(decHead.getArrivalArea())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                        .equals(decHead.getArrivalArea()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
            }
            cell = row.getCell(18);     //经停港
            //经停港
            if(StringUtils.isNotBlank(decHead.getDesPort())){
                List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                        .equals(decHead.getDesPort()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
            }
            cell = row.getCell(22);     //入境口岸
            //离境入境口岸名称
            if(StringUtils.isNotBlank(decHead.getEntyPortCode())){
                List<ErpChinaPorts> erpChinaPorts=erpChinaPortsList.stream().filter(i->i.getChinaPortCode()
                        .equals(decHead.getEntyPortCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(erpChinaPorts.size()>0?erpChinaPorts.get(0).getName():"");
            }

            row = sheet.getRow(13);
            cell = row.getCell(3);     //包装种类
            String types = "";
            if (!ObjectUtils.isEmpty(decHead.getPackType())) {
                for (String t : decHead.getPackType().split(",")) {
                    t = "/" + t;
                    types += t;
                }
            }
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getPacksKinds()) ? "(" + decHead.getPacksKinds() + types + ")" : "");
            cell = row.getCell(17);     //成交方式
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getTermsTypeCode()) ? "(" + decHead.getTermsTypeCode() + ")" : "");

            row = sheet.getRow(14);
            cell = row.getCell(1);
            String[] typeArr = types.split("/");
            String typesArr1 = "";
            for (String s : typeArr) {
                    List<ErpPackagesTypes> erpPackagesTypes=erpPackagesTypesList.stream().filter(i->i.getCode()
                            .equals(s))
                            .collect(Collectors.toList());

                typesArr1 += !ObjectUtils.isEmpty(erpPackagesTypes) ? erpPackagesTypes.get(0).getName() + " " : "";
            }
            List<ErpPackagesTypes> erpPackagesTypes=erpPackagesTypesList.stream().filter(i->i.getCode()
                    .equals(decHead.getPacksKinds()))
                    .collect(Collectors.toList());
            String typesArr2 = !ObjectUtils.isEmpty(erpPackagesTypes) ? erpPackagesTypes.get(0).getName() + " " : "";
            cell.setCellValue((typesArr2 + typesArr1).trim().replaceAll(" ", "/"));
            cell = row.getCell(9);     //件数
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getPacks()) ? decHead.getPacks().toString() : "");
            cell = row.getCell(12);     //毛重
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getGrossWeight()) ? df.format(decHead.getGrossWeight()) : "");
            cell = row.getCell(13);     //净重
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getNetWeight()) ? df.format(decHead.getNetWeight()) : "");
            cell = row.getCell(16);     //成交方式
            //成交方式名称
            if(StringUtils.isNotBlank(decHead.getTermsTypeCode())){
                List<DictModelVO> dictModelVO1=tradingTypeList.stream().filter(i->i.getValue()
                        .equals(decHead.getTermsTypeCode()))
                        .collect(Collectors.toList());
                cell.setCellValue(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }

            cell = row.getCell(18);     //运费ShipCurrencyCodeAndFeeAndFeeCode
            cell.setCellValue(((!ObjectUtils.isEmpty(decHead.getShipCurrencyCode()) ? decHead.getShipCurrencyCode() + " " : "") + (!ObjectUtils.isEmpty(decHead.getShipFee()) ? df.format(decHead.getShipFee()) + " " : "") + (!ObjectUtils.isEmpty(decHead.getShipFeeCode()) ? decHead.getShipFeeCode() : "")).trim().replaceAll(" ", "/"));
            cell = row.getCell(21);     //保费insuranceCurrAndinsuranceAndinsuranceCode
            cell.setCellValue(((!ObjectUtils.isEmpty(decHead.getInsuranceCurr()) ? decHead.getInsuranceCurr() + " " : "") + (!ObjectUtils.isEmpty(decHead.getInsurance()) ? df.format(decHead.getInsurance()) + " " : "") + (!ObjectUtils.isEmpty(decHead.getInsuranceCode()) ? decHead.getInsuranceCode() : "")).trim().replaceAll(" ", "/"));
            cell = row.getCell(26);     //杂费
            cell.setCellValue(((!ObjectUtils.isEmpty(decHead.getOtherCurr()) ? decHead.getOtherCurr() + " " : "") + (!ObjectUtils.isEmpty(decHead.getExtras()) ? df.format(decHead.getExtras()) + " " : "") + (!ObjectUtils.isEmpty(decHead.getExtrasCode()) ? decHead.getExtrasCode() : "")).trim().replaceAll(" ", "/"));
            row = sheet.getRow(16);//随附单证及编号
            cell = row.getCell(1);
            cell.setCellValue((!ObjectUtils.isEmpty(decLicenseDocuses) ? decLicenseDocuses.get(0).getCertCode() : ""));
            row = sheet.getRow(18);
            cell = row.getCell(1);
            StringBuilder str18=new StringBuilder();
            str18.append((!(ObjectUtils.isEmpty(decHead.getMarkNumber()) && ObjectUtils.isEmpty(decHead.getMarkNo())&& ObjectUtils.isEmpty(decHead.getRelId())) ? "备注：" +
                    (!ObjectUtils.isEmpty(decHead.getMarkNumber()) ? decHead.getMarkNumber() : "") + " " +
                    (!ObjectUtils.isEmpty(decHead.getMarkNo()) ? decHead.getMarkNo() : "")+ " " +
                    (!ObjectUtils.isEmpty(decHead.getRelId()) ? "关联报关单号:"+decHead.getRelId(): ""): ""));
            for (int i = 0; i < decContainers.size(); i++) {
                if(i==0){
                    str18.append("集装箱标箱数及号码:");
                }
                if(decContainers.get(i).getContainerMd().equals("21")
                        || decContainers.get(i).getContainerMd().equals("22")
                        || decContainers.get(i).getContainerMd().equals("23")
                        || decContainers.get(i).getContainerMd().equals("31")){
                    str18.append("1;"+decContainers.get(i).getContainerId());
                }else {
                    str18.append("2;"+decContainers.get(i).getContainerId());
                }
                if(i!=decContainers.size()-1){
                    str18.append(";");
                }
            }

            cell.setCellValue(str18.toString());
            int dls;
            if (decLists.size() <= 6) {
                dls = decLists.size();
            } else {
                dls = 6;
            }
            for (int i = 0; i < dls; i++) {
                DecList decList = decLists.get(i);
                row = sheet.getRow(22 + i * 3);
                cell = row.getCell(1);     //项号
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getItem()) ? decList.getItem().toString() : "");
                cell = row.getCell(2);     //商品编号
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getHscode()) ? decList.getHscode() : "");
                cell = row.getCell(7);     //商品名称
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getHsname()) ? decList.getHsname() : "");
                cell = row.getCell(12);     //数量及单位
                //法定第一单位
                if(StringUtils.isNotBlank(decList.getUnit1())){
                    List<ErpUnits> erpUnits=erpUnitsList.stream().filter(j->j.getCode()
                                    .equals(decList.getUnit1()))
                            .collect(Collectors.toList());
                    cell.setCellValue((!ObjectUtils.isEmpty(decList.getCount1()) ? df.format(decList.getCount1()).toString() : "") +
                            (erpUnits.size()>0?erpUnits.get(0).getName():""));
                }

                cell = row.getCell(15);     //单价
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getPrice()) ? dft.format(decList.getPrice()).toString() : "");
                cell = row.getCell(18);     //原产国desCountry
                //原产国
                if(StringUtils.isNotBlank(decList.getDesCountry())){
                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(j->j.getCode()
                            .equals(decList.getDesCountry()))
                            .collect(Collectors.toList());
                    cell.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
                }
                cell = row.getCell(20);     //最终目的国地区
                if(StringUtils.isNotBlank(decList.getDestinationCountry())){
                    List<ErpCountries> erpCountries=erpCountriesList.stream().filter(j->j.getCode()
                            .equals(decList.getDestinationCountry()))
                            .collect(Collectors.toList());
                    cell.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
                }

                cell = row.getCell(22);     //境内目的地
                List<ErpDistricts> list4 = new ArrayList<>();
                //境内货源地名称
                if(StringUtils.isNotBlank(decList.getDistrictCode())){
                    list4=erpDistrictsList.stream().filter(j->j.getCode()
                            .equals(decList.getDistrictCode()))
                            .collect(Collectors.toList());
                }

                List<DictModelVO> list9 = new ArrayList<>();
                //行政区划
                if(StringUtils.isNotBlank(decList.getDestCode())){
                    list9=xzqh.stream().filter(j->j.getValue()
                            .equals(decList.getDestCode()))
                            .collect(Collectors.toList());
                }

                String val = "";
                if (!ObjectUtils.isEmpty(list4) && !ObjectUtils.isEmpty(list9)) {
                    val = "/";
                }
                if (ObjectUtils.isEmpty(list4) && ObjectUtils.isEmpty(list9)) {
                    cell.setCellValue("");
                } else {
                    cell.setCellValue("(" + (!ObjectUtils.isEmpty(list4) ? list4.get(0).getName() : "") + val +
                            (!ObjectUtils.isEmpty(list9) ? list9.get(0).getText() : "") + ")" +
                            (!ObjectUtils.isEmpty(list4) ? list4.get(0).getCode() : "") + val +
                            (!ObjectUtils.isEmpty(list9) ? list9.get(0).getValue(): ""));

                }
//                cell.setCellValue("("+(!isEmpty(list4)?list4.get(0).get("name"):"")+val+(!isEmpty(list9)?list9.get(0).get("name"):"")+")"+(!isEmpty(list4)?list4.get(0).get("code"):"")+val+(!isEmpty(list9)?list9.get(0).get("code"):""));
                cell = row.getCell(27);     //征免
                //征免方式
                if(StringUtils.isNotBlank(decList.getFaxTypeCode())){
                    List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(j->j.getValue()
                            .equals(decList.getFaxTypeCode()))
                            .collect(Collectors.toList());
                    cell.setCellValue(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
                }

                row = sheet.getRow(23 + i * 3);
                cell = row.getCell(7);     //规格型号

                if(decList.getHsmodel() != null && decList.getHsmodel().length()>40){
                    // 每超过30个字符，加240高度
                    int extraChars = decList.getHsmodel().length() - 40;
                    int additionalBlocks = (int) Math.ceil(extraChars / 30.0);
                    int  rowHeight =  additionalBlocks * 240;
                    row.setHeight((short) (row.getHeight() + rowHeight));
                }
//                cell.setCellValue(!ObjectUtils.isEmpty(decList.getHsmodel()) ? decList.getHsmodel().length() > 50 ? decList.getHsmodel().substring(0, 50) : decList.getHsmodel() : "");
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getHsmodel()) ? decList.getHsmodel() : "");
                cell = row.getCell(12);     //数量及单位
                //法二单位名称
                if(StringUtils.isNotBlank(decList.getUnit2())){
                    List<ErpUnits> erpUnits=erpUnitsList.stream().filter(j->j.getCode()
                                    .equals(decList.getUnit2()))
                            .collect(Collectors.toList());
                    cell.setCellValue((!ObjectUtils.isEmpty(decList.getCount2()) ? df.format(decList.getCount2()).toString() : "") +
                            (erpUnits.size()>0?erpUnits.get(0).getName():""));
                }


                cell = row.getCell(15);     //总价
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getTotal()) ? dft.format(decList.getTotal()).toString() : "");
                cell = row.getCell(18);     //原产国desCountry
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getDesCountry()) ? "(" + decList.getDesCountry() + ")" : "");
                cell = row.getCell(20);     //最终目的国地区
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getDestinationCountry()) ? "(" + decList.getDestinationCountry() + ")" : "");
                cell = row.getCell(27);     //征免
                cell.setCellValue(!ObjectUtils.isEmpty(decList.getFaxTypeCode()) ? "(" + decList.getFaxTypeCode() + ")" : "");

                row = sheet.getRow(24 + i * 3);
                cell = row.getCell(12);     //数量及单位
                //成交单位名称
                if(StringUtils.isNotBlank(decList.getUnitCode())){
                    List<ErpUnits> erpUnits=erpUnitsList.stream().filter(j->j.getCode()
                                    .equals(decList.getUnitCode()))
                            .collect(Collectors.toList());
                    cell.setCellValue((!ObjectUtils.isEmpty(decList.getGoodsCount()) ? df.format(decList.getGoodsCount()).toString() : "") +
                            (erpUnits.size()>0?erpUnits.get(0).getName():""));
                }

                cell = row.getCell(15);     //币制
                //币制
                if(StringUtils.isNotBlank(decList.getCurrencyCode())){
                    List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(j->j.getCurrency()
                            .equals(decList.getCurrencyCode()))
                            .collect(Collectors.toList());
                    cell.setCellValue(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
                }

            }

            //特殊关系确认 价格影响确认 支付特许权使用费确认 自报自缴
            row = sheet.getRow(40);
            if (!ObjectUtils.isEmpty(decHead.getPromiseItmes())) {
                String[] promiseItmesArr = decHead.getPromiseItmes().split("\\|");
                cell = row.getCell(8);
                cell.setCellValue(promiseItmesArr[0].equals("1") ? "是" : "否");
                cell = row.getCell(13);
                cell.setCellValue(promiseItmesArr[1].equals("1") ? "是" : "否");
                cell = row.getCell(20);
                cell.setCellValue(promiseItmesArr[2].equals("1") ? "是" : "否");
            }
            cell = row.getCell(26);
            cell.setCellValue("Z".equals(decHead.getDecType()) ? "是" : "否");

            row = sheet.getRow(42);
            //报关员
            cell = row.getCell(5);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDeclarant())?decHead.getDeclarant():"");
            //报关人员证号
            cell = row.getCell(8);
            cell.setCellValue(!ObjectUtils.isEmpty(decHead.getDeclarantNo())?decHead.getDeclarantNo():"");
            //申报单位
            row = sheet.getRow(44);
            cell = row.getCell(5);
            cell.setCellValue((!ObjectUtils.isEmpty(decHead.getDeclareUnitSocialCode()) ? "(" + decHead.getDeclareUnitSocialCode() + ")" : "") + (!ObjectUtils.isEmpty(decHead.getDeclareUnitName()) ? decHead.getDeclareUnitName() : ""));
            //逻辑校验
            row = sheet.getRow(46);
            cell = row.getCell(1);

            StringBuilder tips=new StringBuilder();
            BigDecimal totalSum = decLists.stream()
                    .map(DecList::getTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            tips.append("总价:"+totalSum.setScale(2,BigDecimal.ROUND_HALF_UP)
                    +(decLists.size()==0?"":decLists.get(0).getCurrencyCode()));
            // 根据单位分组并求和
            Map<String, BigDecimal> countByBnit = decLists.stream()
                    .collect(Collectors.groupingBy(
                            DecList::getUnit1,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    dec -> dec.getCount1() == null ? BigDecimal.ZERO : dec.getCount1(),
                                    (a, b) -> a.add(b).setScale(2, RoundingMode.HALF_UP)
                            )
                    ));

            tips.append("   总数量:");
            countByBnit.forEach((unit,sum)->{
                List<ErpUnits> erpUnits=erpUnitsList.stream().filter(j->j.getCode()
                                .equals(unit))
                        .collect(Collectors.toList());;
                tips.append(sum.stripTrailingZeros().toPlainString() + ( erpUnits.size() == 0?"":erpUnits.get(0).getName())+" ");
            });

            BigDecimal addWeightNum=BigDecimal.ZERO;;
            for (DecList item : decLists ){
                if (item.getUnit1().equals("035") || item.getUnit1().equals("036")  || item.getUnit1().equals("070")) {

                    if (!ObjectUtils.isEmpty(item.getCount1())) {
                        if (item.getUnit1().equals("036") ) {
                            addWeightNum=addWeightNum.add(item.getCount1().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
                        } else if (item.getUnit1().equals("035") ) {
                            addWeightNum=addWeightNum.add(item.getCount1());
                        } else if (item.getUnit1().equals("070") ) {
                            addWeightNum=addWeightNum.add(item.getCount1().multiply(new BigDecimal("1000")));
                        }
                    }

                } else if (item.getUnit2().equals("035") || item.getUnit2().equals("036")  || item.getUnit2().equals("070")) {

                    if (!ObjectUtils.isEmpty(item.getCount2()))  {
                        if (item.getUnit2().equals("036") ) {
                            addWeightNum=addWeightNum.add(item.getCount2().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
                        } else if (item.getUnit2().equals("035") ) {
                            addWeightNum=addWeightNum.add(item.getCount2());
                        } else if (item.getUnit2().equals("070")) {
                            addWeightNum=addWeightNum.add(item.getCount2().multiply(new BigDecimal("1000")));
                        }
                    }

                } else if (item.getUnitCode().equals("035") || item.getUnitCode().equals("036")  || item.getUnitCode().equals("070")) {
                    if (!ObjectUtils.isEmpty(item.getGoodsCount())) {
                        if (item.getUnitCode().equals("036") ) {
                            addWeightNum=addWeightNum.add(item.getGoodsCount().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
                        } else if (item.getUnitCode().equals("035")) {
                            addWeightNum=addWeightNum.add(item.getGoodsCount());
                        } else if (item.getUnitCode().equals("070")) {
                            addWeightNum=addWeightNum.add(item.getGoodsCount().multiply(new BigDecimal("1000")));
                        }
                    }
                }
            }
            tips.append("   总净重:"+addWeightNum.stripTrailingZeros().toPlainString() +"千克");
            String response = this.Etg_validate(decHead.getId());
            JSONArray jsonArray=JSON.parseArray(response);
            tips.append("   "+jsonArray.stream()
                    .map(item -> {
                        JSONObject obj = (JSONObject) item;
                        return obj.getString("type") + ":" + obj.getString("reason");
                    })
                    .collect(Collectors.joining(";")));

            cell.setCellValue(tips.toString());

            for (int i = 0; i < count; i++) {

                HSSFSheet sheet1 = book.getSheetAt(i + 1);
                HSSFPatriarch patriarch1 = sheet1.createDrawingPatriarch();
                //插入图片
                patriarch1.createPicture(anchor1, book.addPicture(bytes1, HSSFWorkbook.PICTURE_TYPE_PNG));
                patriarch1.createPicture(anchor2, book.addPicture(bytes2, HSSFWorkbook.PICTURE_TYPE_PNG));


                HSSFRow rowN = null;
                HSSFCell cellN = null;

                rowN = sheet1.getRow(1);
                cellN = rowN.getCell(20);
                cellN.setCellValue((!ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "*" + decHead.getClearanceNo() + "*" : "*xxxxxxxxxxxxxxxxxx*").replaceAll("", " ").trim());

                rowN = sheet1.getRow(3);
                cellN = rowN.getCell(5);
                cellN.setCellValue(decHead.getClearanceNo());
                cellN = rowN.getCell(10);
                cellN.setCellValue(ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "" : decHead.getClearanceNo());
                cellN = rowN.getCell(14);
                if(isNotBlank(decHead.getDeclarePlace())){
                    List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(j->j.getCustomsPortCode()
                            .equals(decHead.getDeclarePlace()))
                            .collect(Collectors.toList());
                    cellN.setCellValue(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
                }


                int n = 14;
                if (i == count - 1) {
                    n = (decLists.size() - 6) % 14;
                    if(n==0){
                        n=14;
                    }
                }
                for (int j = 1; j <= n; ++j) {
                    DecList decListN = decLists.get(i * 14 + 6 + j - 1);

                    rowN = sheet1.getRow(3);
                    cellN = rowN.getCell(28);
                    cellN.setCellValue((i + 2) + "/" + (count + 1));

                    rowN = sheet1.getRow(6 + (j - 1) * 3);
                    cellN = rowN.getCell(1);     //项号
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getItem()) ? decListN.getItem().toString() : "");
                    cellN = rowN.getCell(2);     //商品编号
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getHscode()) ? decListN.getHscode() : "");
                    cellN = rowN.getCell(7);     //商品名称
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getHsname()) ? decListN.getHsname() : "");
                    cellN = rowN.getCell(12);     //数量及单位
                    //法定第一单位
                    if(StringUtils.isNotBlank(decListN.getUnit1())){
                        List<ErpUnits> erpUnits=erpUnitsList.stream().filter(g->g.getCode()
                                        .equals(decListN.getUnit1()))
                                .collect(Collectors.toList());
                        cellN.setCellValue((!ObjectUtils.isEmpty(decListN.getCount1()) ?
                                df.format(decListN.getCount1()).toString() : "") +
                                (erpUnits.size()>0?erpUnits.get(0).getName():""));
                    }

                    cellN = rowN.getCell(15);     //单价
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getPrice()) ? dft.format(decListN.getPrice()).toString() : "");
                    cellN = rowN.getCell(18);     //原产国desCountry

                    if(StringUtils.isNotBlank(decListN.getDesCountry())){
                        List<ErpCountries> erpCountries=erpCountriesList.stream().filter(g->g.getCode()
                                .equals(decListN.getDesCountry()))
                                .collect(Collectors.toList());
                        cellN.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
                    }
                    cellN = rowN.getCell(20);     //最终目的国地区
                    if(StringUtils.isNotBlank(decListN.getDestinationCountry())){
                        List<ErpCountries> erpCountries=erpCountriesList.stream().filter(g->g.getCode()
                                .equals(decListN.getDestinationCountry()))
                                .collect(Collectors.toList());
                        cellN.setCellValue(erpCountries.size()>0?erpCountries.get(0).getName():"");
                    }

                    cellN = rowN.getCell(22);     //境内目的地
                    List<ErpDistricts> list4 = new ArrayList<>();
                    //境内货源地名称
                    if(StringUtils.isNotBlank(decListN.getDistrictCode())){
                        list4=erpDistrictsList.stream().filter(g->g.getCode()
                                .equals(decListN.getDistrictCode()))
                                .collect(Collectors.toList());
                    }

                    List<DictModelVO> list9 = new ArrayList<>();
                    //行政区划
                    if(StringUtils.isNotBlank(decListN.getDestCode())){
                        list9=xzqh.stream().filter(g->g.getValue()
                                .equals(decListN.getDestCode()))
                                .collect(Collectors.toList());
                    }
                    String val = "";
                    if (!ObjectUtils.isEmpty(list4) && !ObjectUtils.isEmpty(list9)) {
                        val = "/";
                    }
                    if (ObjectUtils.isEmpty(list4) && ObjectUtils.isEmpty(list9)) {
                        cellN.setCellValue("");
                    } else {
                        cellN.setCellValue("(" + (!ObjectUtils.isEmpty(list4) ? list4.get(0).getName() : "") +
                                val + (!ObjectUtils.isEmpty(list9) ? list9.get(0).getText() : "") + ")" +
                                (!ObjectUtils.isEmpty(list4) ? list4.get(0).getCode() : "") + val +
                                (!ObjectUtils.isEmpty(list9) ? list9.get(0).getValue() : ""));

                    }
                    cellN = rowN.getCell(27);     //征免
                    //征免方式
                    if(StringUtils.isNotBlank(decListN.getFaxTypeCode())){
                        List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(g->g.getValue()
                                .equals(decListN.getFaxTypeCode()))
                                .collect(Collectors.toList());
                        cellN.setCellValue(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
                    }



                    rowN = sheet1.getRow(7 + (j - 1) * 3);
                    cellN = rowN.getCell(7);     //规格型号
                    if(decListN.getHsmodel() != null && decListN.getHsmodel().length()>40){
                        // 每超过30个字符，加240高度
                        int extraChars = decListN.getHsmodel().length() - 40;
                        int additionalBlocks = (int) Math.ceil(extraChars / 30.0);
                        int  rowHeight =  additionalBlocks * 240;
                        rowN.setHeight((short) (rowN.getHeight() + rowHeight));
                    }
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getHsmodel()) ?  decListN.getHsmodel() : "");
                    cellN = rowN.getCell(12);     //数量及单位
                    //法二单位名称
                    if(StringUtils.isNotBlank(decListN.getUnit2())){
                        List<ErpUnits> erpUnits=erpUnitsList.stream().filter(g->g.getCode()
                                        .equals(decListN.getUnit2()))
                                .collect(Collectors.toList());
                        cellN.setCellValue((!ObjectUtils.isEmpty(decListN.getCount2()) ?
                                df.format(decListN.getCount2()).toString() : "") +
                                (erpUnits.size()>0?erpUnits.get(0).getName():""));
                    }
                    cellN = rowN.getCell(15);     //总价
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getTotal()) ? dft.format(decListN.getTotal()).toString() : "");
                    cellN = rowN.getCell(18);     //原产国desCountry
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getDesCountry()) ? "(" + decListN.getDesCountry() + ")" : "");
                    cellN = rowN.getCell(20);     //最终目的国地区
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getDestinationCountry()) ? "(" + decListN.getDestinationCountry() + ")" : "");
                    cellN = rowN.getCell(27);     //征免
                    cellN.setCellValue(!ObjectUtils.isEmpty(decListN.getFaxTypeCode()) ? "(" + decListN.getFaxTypeCode() + ")" : "");

                    rowN = sheet1.getRow(8 + (j - 1) * 3);
                    cellN = rowN.getCell(12);     //数量及单位
                    //成交单位名称
                    if(StringUtils.isNotBlank(decListN.getUnitCode())){
                        List<ErpUnits> erpUnits=erpUnitsList.stream().filter(g->g.getCode()
                                        .equals(decListN.getUnitCode()))
                                .collect(Collectors.toList());
                        cellN.setCellValue((!ObjectUtils.isEmpty(decListN.getGoodsCount()) ?
                                df.format(decListN.getGoodsCount()).toString() : "") +
                                (erpUnits.size()>0?erpUnits.get(0).getName():""));
                    }


                    cellN = rowN.getCell(15);     //币制
                    //币制
                    if(StringUtils.isNotBlank(decListN.getCurrencyCode())){
                        List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(g->g.getCurrency()
                                .equals(decListN.getCurrencyCode()))
                                .collect(Collectors.toList());
                        cellN.setCellValue(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
                    }
                }

            }

            //审核的操作处理
            if("1".equals(shdFlag)){
                HSSFSheet sheet1 = book.getSheetAt(6);
                HSSFPatriarch patriarch1 = sheet1.createDrawingPatriarch();
                //插入图片
                patriarch1.createPicture(anchor1, book.addPicture(bytes1, HSSFWorkbook.PICTURE_TYPE_PNG));
                patriarch1.createPicture(anchor2, book.addPicture(bytes2, HSSFWorkbook.PICTURE_TYPE_PNG));
                HSSFRow rowN = null;
                HSSFCell cellN = null;

                rowN = sheet1.getRow(1);
                cellN = rowN.getCell(20);
                cellN.setCellValue((!ObjectUtils.isEmpty(decHead.getClearanceNo()) ? "*" + decHead.getClearanceNo() + "*" : "*xxxxxxxxxxxxxxxxxx*").replaceAll("", " ").trim());

                rowN = sheet1.getRow(3);
                cellN = rowN.getCell(5);
                cellN.setCellValue(decHead.getClearanceNo());
                cellN = rowN.getCell(14);
                if(isNotBlank(decHead.getDeclarePlace())){
                    List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(j->j.getCustomsPortCode()
                                    .equals(decHead.getDeclarePlace()))
                            .collect(Collectors.toList());
                    cellN.setCellValue(erpCustomsPorts.size()>0?("("+erpCustomsPorts.get(0).getName()+")"):"");
                }
                //初复审相关
                rowN = sheet1.getRow(6);
                cellN = rowN.getCell(14);

                cellN.setCellValue(isNotBlank(decHead.getFirstTrialBy())?decHead.getFirstTrialBy():"");//初审人
                cellN = rowN.getCell(20);
                cellN.setCellValue(null!=decHead.getFirstTrialDate()?
                        DateUtil.format(decHead.getFirstTrialDate(),"yyyy/MM/dd HH:mm:ss"):"");//初审时间

                rowN = sheet1.getRow(7);
                cellN = rowN.getCell(14);
                cellN.setCellValue(isNotBlank(decHead.getReviewBy())?decHead.getReviewBy():"");//复审人
                cellN = rowN.getCell(20);
                cellN.setCellValue(null!=decHead.getReviewDate()?
                        DateUtil.format(decHead.getReviewDate(),"yyyy/MM/dd HH:mm:ss"):"");//复审时间

            }


            for (int del = count + 1; del <= ("1".equals(shdFlag)?5:6); del++) {
                book.removeSheetAt(count + 1);
            }

//            book.write(os);

//            switch (type) {
//                case "pdf":
//                    InputStream sbs = new ByteArrayInputStream(os.toByteArray());
//                    resp = Doc2HtmlUtil.file2pdf(sbs, fileName, token);
////                    resp = OpenOfficePdfConvert.convert2PDF(sbs, fileName, token);
//                    break;
//                case "excel":
//                    resp = ExportToFile.export2File(fileName + ".xls", token, os);
//                    break;
//                default:
//                    break;
//            }
            return book;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 逻辑校验
     * @param id
     * @return
     */
    public String Etg_validate(String id){
        DecHead decHead=this.getDecHeadById(id);
        //获取请求接口的token
        SysConfig sysConfig=sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey,"DEC_LOGIC_VERIFICATION"));
        //如果数据库没有这个配置
        if(isEmpty(sysConfig)){
            sysConfig=new SysConfig();
            String token = loginInvoiceApi();
            sysConfig.setConfigValue(token);
        }
        //组装数据
        String requestParam=setPreDecFrom(decHead);
        String encodedParam="";
        try {
            encodedParam = URLEncoder.encode(requestParam, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
//        String requestParam="";
        log.info("requestParam==="+requestParam);

        //执行请求
        String response= HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/Etg_validate")
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("Token",sysConfig.getConfigValue())
                .body("param="+ encodedParam)
                .timeout(30000)//超时，毫秒
                .execute().body();
        log.info("response==="+response);

        return response;
    }

    //将本地报关单数据组装成请求的数据
    private String setPreDecFrom(DecHead decHead){
        //全部数据的dto
        DecLogicVerificationDTO decLogicVerificationDTO=new DecLogicVerificationDTO();
        //报关单表头
        JgVFlyBgDechead preDecHeadVo=new JgVFlyBgDechead();
        //报关单表体
        List<JgVFlyBgDeclist> preDecListVo=new ArrayList<>();
        //报关单集装箱
        List<JgVFlyBgDeccontainer> preDecContainerVo=new ArrayList<>();
        //报关单随附单证
        List<JgVFlyBgDeclicense> preDecLicenseVo=new ArrayList<>();
        //报关单随附单据
        List<JgVFlyBgDecdocvo> preDecDocVo=new ArrayList<>();
//        表头数据处理
        setPreDecHead(decHead,preDecHeadVo);
        //表体数据处理
        setPreDecList(decHead.getDecLists(),preDecListVo);
        //集装箱处理
        setPreDecContainerVo(decHead.getDecContainers(),preDecContainerVo);

//        setPreDecLicenseVo(decHead.getDecLicenseDocuses(),)


        decLogicVerificationDTO.setPreDecHeadVo(preDecHeadVo);
        decLogicVerificationDTO.setPreDecListVo(preDecListVo);
        decLogicVerificationDTO.setPreDecContainerVo(preDecContainerVo);
        decLogicVerificationDTO.setPreDecLicenseVo(preDecLicenseVo);
        decLogicVerificationDTO.setPreDecDocVo(preDecDocVo);
        return JSONObject.toJSONString(decLogicVerificationDTO);

    }
    //        表头数据处理
    private void setPreDecHead(DecHead decHead,JgVFlyBgDechead preDecHeadVo){
        List<DictModelVO> bgdlxList = decListMapper.getDictItemByCode("BGDLX");//报关单类型字典
        List<ErpCustomsPorts> erpCustomsPortsList=erpCustomsPortsService.list();
        List<ErpTransportTypes> erpTransportTypesList=erpTransportTypesService.list();
        List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
        List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
        List<ErpCountries> erpCountriesList=erpCountriesService.list();
        List<DictModelVO> tradingTypeList = decListMapper.getDictItemByCode("trading_type");
        List<ErpPackagesTypes> erpPackagesTypesList=erpPackagesTypesService.list();
        List<ErpCityports> erpCityportsList=erpCityportsService.list();
        List<ErpCurrencies> erpCurrenciesList=erpCurrenciesService.list();
        List<ErpChinaPorts> erpChinaPortsList=erpChinaPortsService.list();
        List<DictModelVO> jyjg = decListMapper.getDictItemByCode("JYJG");
        preDecHeadVo.setCustomMaster(decHead.getDeclarePlace());// 申报地海关
        //申报地海关名称
        if(StringUtils.isNotBlank(decHead.getDeclarePlace())){
            List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                            .equals(decHead.getDeclarePlace()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCustomMasterName(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
        }
        preDecHeadVo.setCusIEFlag(decHead.getIeFlag());// 进出口标识
        preDecHeadVo.setCusCiqNo(decHead.getSeqNo()); // 统一编号
        preDecHeadVo.setIEPort(decHead.getOutPortCode()); // 进出境关别
        //出境关别名称
        if(StringUtils.isNotBlank(decHead.getOutPortCode())){
            List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                            .equals(decHead.getOutPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setIEPortName(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
        }
        preDecHeadVo.setContrNo(decHead.getContract()); // 合同协议号
        preDecHeadVo.setIEDate(decHead.getOutDate()); // 进出日期
        preDecHeadVo.setRcvgdTradeCode(decHead.getOptUnitId()); // 境内收发货人海关编码
        preDecHeadVo.setConsigneeCname(decHead.getOptUnitName()); // 境内收发货人名称
        preDecHeadVo.setAgentCode(decHead.getDeclareUnit()); // 申报单位海关代码
        preDecHeadVo.setCusTrafMode(decHead.getShipTypeCode()); // 运输方式
        //运输方式名称
        if(StringUtils.isNotBlank(decHead.getShipTypeCode())){
            List<ErpTransportTypes> erpTransportTypes=erpTransportTypesList.stream().filter(i->i.getCode()
                            .equals(decHead.getShipTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCusTrafModeName(erpTransportTypes.size()>0?erpTransportTypes.get(0).getName():"");
        }
        preDecHeadVo.setTrafName(decHead.getShipName()); // 运输工具名称
        preDecHeadVo.setCusVoyageNo(decHead.getVoyage()); // 航次号
        preDecHeadVo.setBillNo(decHead.getBillCode()); // 提运单号
        preDecHeadVo.setSupvModeCdde(decHead.getTradeTypeCode()); // 监管方式
        //监管方式名称
        if(StringUtils.isNotBlank(decHead.getTradeTypeCode())){
            List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getValue()
                            .equals(decHead.getTradeTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setSupvModeCddeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }

        preDecHeadVo.setCutMode(decHead.getTaxTypeCode()); // 征免性质
        //征免性质名称
        if(StringUtils.isNotBlank(decHead.getTaxTypeCode())){
            List<DictModelVO> dictModelVO1=zmxz.stream().filter(i->i.getValue()
                            .equals(decHead.getTaxTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCutModeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }
        preDecHeadVo.setCusTradeCountry(decHead.getArrivalArea()); // 启运国
        //启运国名称
        if(StringUtils.isNotBlank(decHead.getArrivalArea())){
            List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                            .equals(decHead.getArrivalArea()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCusTradeCountryName(erpCountries.size()>0?erpCountries.get(0).getName():"");
        }

        preDecHeadVo.setTransMode(decHead.getTermsTypeCode()); // 成交方式
        //成交方式名称
        if(StringUtils.isNotBlank(decHead.getTermsTypeCode())){
            List<DictModelVO> dictModelVO1=tradingTypeList.stream().filter(i->i.getValue()
                            .equals(decHead.getTermsTypeCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setTransModeName(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
        }
        preDecHeadVo.setFeeCurr(decHead.getShipCurrencyCode()); // 运费币制
        // 运费币制名称
        if(isNotBlank(decHead.getShipCurrencyCode())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                            .equals(decHead.getShipCurrencyCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setFeeCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }

        preDecHeadVo.setFeeMark(decHead.getShipFeeCode()); // 运费代码 setShipFee
        if(isNotBlank(decHead.getShipFeeCode())){
            if("1".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("率"); // 运费代码名称 setShipFee
            }else if("2".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("单价"); // 运费代码名称 setShipFee
            }else if("3".equals(decHead.getShipFeeCode())){
                preDecHeadVo.setFeeMarkName("总价"); // 运费代码名称 setShipFee
            }
        }
        preDecHeadVo.setFeeRate(null!=decHead.getShipFee()?String.valueOf(decHead.getShipFee()):""); // 运费值
        preDecHeadVo.setInsurCurr(decHead.getInsuranceCurr()); // 保费币制
        // 保费币制名称
        if(isNotBlank(decHead.getInsuranceCurr())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                            .equals(decHead.getInsuranceCurr()))
                    .collect(Collectors.toList());
            preDecHeadVo.setInsurCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }
        preDecHeadVo.setInsurMark(decHead.getInsuranceCode()); // 保费代码 setInsurance
        if(isNotBlank(decHead.getInsuranceCode())){
            if("1".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("率"); // 保费代码名称 setShipFee
            }else if("2".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("单价"); // 保费代码名称 setShipFee
            }else if("3".equals(decHead.getInsuranceCode())){
                preDecHeadVo.setInsurMarkName("总价"); // 保费代码名称 setShipFee
            }
        }
        preDecHeadVo.setInsurRate(null!=decHead.getInsurance()?String.valueOf(decHead.getInsurance()):""); // 保费值
        preDecHeadVo.setOtherCurr(decHead.getOtherCurr()); // 杂费币制
        // 杂费币制名称
        if(isNotBlank(decHead.getOtherCurr())){
            List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                            .equals(decHead.getOtherCurr()))
                    .collect(Collectors.toList());
            preDecHeadVo.setOtherCurrName(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
        }
        preDecHeadVo.setOtherMark(decHead.getExtrasCode()); // 杂费代码
        if(isNotBlank(decHead.getExtrasCode())){
            if("1".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("率"); // 杂费代码名称 setShipFee
            }else if("2".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("单价"); // 杂费代码名称 setShipFee
            }else if("3".equals(decHead.getExtrasCode())){
                preDecHeadVo.setOtherMarkName("总价"); // 杂费代码名称 setShipFee
            }
        }
        preDecHeadVo.setOtherRate(null!=decHead.getExtras()?String.valueOf(decHead.getExtras()):""); // 杂费值

        preDecHeadVo.setPackNo(null!=decHead.getPacks() ? String.valueOf(decHead.getPacks()) : null); // 件数
        preDecHeadVo.setWrapType(decHead.getPacksKinds()); // 包装种类
        //包装名称
        if(StringUtils.isNotBlank(decHead.getPacksKinds())){
            List<ErpPackagesTypes> erpPackagesTypes=erpPackagesTypesList.stream().filter(i->i.getCode()
                            .equals(decHead.getPacksKinds()))
                    .collect(Collectors.toList());
            preDecHeadVo.setWrapTypeName(erpPackagesTypes.size()>0?erpPackagesTypes.get(0).getName():"");
        }


        if(StringUtils.isNotBlank(decHead.getPacksKinds())){
            List<ErpPackagesTypes> erpPackagesTypes = erpPackagesTypesService.list(new LambdaQueryWrapper<ErpPackagesTypes>()
                    .eq(ErpPackagesTypes::getCode, decHead.getPacksKinds()));
            preDecHeadVo.setWrapTypeName(erpPackagesTypes.size()>0?erpPackagesTypes.get(0).getName():""); // 包装种类名称
        }
        preDecHeadVo.setGrossWt(null!=decHead.getGrossWeight() ? String.valueOf(decHead.getGrossWeight()) : null); // 毛重
        preDecHeadVo.setNetWt(null!=decHead.getNetWeight() ? String.valueOf(decHead.getNetWeight()) : null); // 净重
        preDecHeadVo.setDespPortCode(decHead.getDespPortCode()); // 启运港代码--进口
        //启运港代码名称
        if(StringUtils.isNotBlank(decHead.getDespPortCode())){
            List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                            .equals(decHead.getDespPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setDespPortCodeName(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
        }


        preDecHeadVo.setGoodsPlace(decHead.getGoodsPlace()); // 货物存放地点
        preDecHeadVo.setEntryType(decHead.getClearanceType()); // 报关单类型
        if(isNotBlank(decHead.getClearanceType())){
            List<DictModelVO> dictModelVOS=bgdlxList.stream().filter(j->j.getValue()
                            .equals(decHead.getClearanceType()))
                    .collect(Collectors.toList());
            preDecHeadVo.setEntryTypeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setNoteS(decHead.getMarkNumber());
        preDecHeadVo.setPromiseItems(isNotBlank(decHead.getPromiseItmes()) ?
                decHead.getPromiseItmes().replaceAll("\\|","") : null); // 特殊关系/价格说明
        preDecHeadVo.setMarkNo(decHead.getMarkNo()); // 标记唛码
        //业务事项
        if(isNotBlank(decHead.getDecType())&&decHead.getDecType().contains("Z")){
            preDecHeadVo.setCusRemark("100000000000000");//自报自缴，待完善
        }
        preDecHeadVo.setOrgCode(decHead.getOrgCode()); // 检验检疫受理机关 商检信息
        // 检验检疫受理机关名称
        if(isNotBlank(decHead.getOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                            .equals(decHead.getOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setVsaOrgCode(decHead.getVsaOrgCode()); // 领证机关 商检信息
        // 领证机关名称
        if(isNotBlank(decHead.getVsaOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                            .equals(decHead.getVsaOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setVsaOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setInspOrgCode(decHead.getInspOrgCode()); // 口岸检验检疫机关 商检信息
        // 口岸检验检疫机关名称
        if(isNotBlank(decHead.getInspOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                            .equals(decHead.getInspOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setInspOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }
        preDecHeadVo.setDespDate(decHead.getDespDate()); // 启运日期 格式为：yyyyMMdd 商检信息
        preDecHeadVo.setPurpOrgCode(decHead.getPurpOrgCode()); // 目的地检验检疫机关 商检信息
        // 目的地检验检疫机关名称
        if(isNotBlank(decHead.getPurpOrgCode())){
            List<DictModelVO> dictModelVOS=jyjg.stream().filter(j->j.getValue()
                            .equals(decHead.getPurpOrgCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setPurpOrgCodeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
        }

        preDecHeadVo.setCiqEntyPortCode(decHead.getEntyPortCode()); // 入境口岸
        //入境口岸名称
        if(StringUtils.isNotBlank(decHead.getEntyPortCode())){
            List<ErpChinaPorts> erpChinaPorts=erpChinaPortsList.stream().filter(i->i.getChinaPortCode()
                            .equals(decHead.getEntyPortCode()))
                    .collect(Collectors.toList());
            preDecHeadVo.setCiqEntyPortCodeName(erpChinaPorts.size()>0?erpChinaPorts.get(0).getName():"");
        }
        preDecHeadVo.setEntQualifTypeCodeSName("[]".equals(decHead.getCopLimitType())?null:decHead.getCopLimitType());//企业资质
        preDecHeadVo.setManualNo(decHead.getRecordNumber());//备案号
    }
    //表体数据处理
    private void setPreDecList(List<DecList> decList,List<JgVFlyBgDeclist> preDecListVo){
        //需要用到字典
        List<ErpUnits> erpUnitsList=erpUnitsService.list();//成交单位
        List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");//征免方式
        List<DictModelVO> yt = decListMapper.getDictItemByCode("YT");//用途
        List<DictModelVO> hwsx = decListMapper.getDictItemByCode("HWSX");//用途
        List<ErpCountries> erpCountriesList=erpCountriesService.list();//国家地区
        for(DecList decListObj:decList){
            JgVFlyBgDeclist jgVFlyBgDeclist=new JgVFlyBgDeclist();
            jgVFlyBgDeclist.setGNo(String.valueOf(decListObj.getItem())); // 项号
            jgVFlyBgDeclist.setCodeTs(decListObj.getHscode()); // 商品编码 税号
            jgVFlyBgDeclist.setGModel(decListObj.getHsmodel()); // 规格型号 申报要素
            jgVFlyBgDeclist.setGQty(String.valueOf(decListObj.getGoodsCount())); // 成交数量
            jgVFlyBgDeclist.setGUnit(decListObj.getUnitCode()); // 成交单位
            if(isNotBlank(decListObj.getUnitCode())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnitCode())).collect(Collectors.toList());
                jgVFlyBgDeclist.setGUnitName(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//成交单位名称
            }
            jgVFlyBgDeclist.setDeclPrice(String.valueOf(decListObj.getPrice())); // 单价
            jgVFlyBgDeclist.setDeclTotal(String.valueOf(decListObj.getTotal())); // 总价
            jgVFlyBgDeclist.setTradeCurr(decListObj.getCurrencyCode()); // 币制
            jgVFlyBgDeclist.setQty1(String.valueOf(decListObj.getCount1())); // 法定数量
            jgVFlyBgDeclist.setUnit1(decListObj.getUnit1()); // 法定单位
            if(isNotBlank(decListObj.getUnit1())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnit1())).collect(Collectors.toList());
                jgVFlyBgDeclist.setUnit1Name(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//法定单位名称
            }
            jgVFlyBgDeclist.setDestinationCountry(decListObj.getDestinationCountry()); // 最终目的国
            //最终目的国名称
            if(StringUtils.isNotBlank(decListObj.getDestinationCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                                .equals(decListObj.getDestinationCountry()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setDestinationCountryName(!erpCountries.isEmpty() ?erpCountries.get(0).getName():"");
            }

            jgVFlyBgDeclist.setQty2(String.valueOf(decListObj.getCount2())); // 法定数量2
            jgVFlyBgDeclist.setUnit2(decListObj.getUnit2()); // 法定单位2
            if(isNotBlank(decListObj.getUnit2())){
                List<ErpUnits> erpUnitsList2=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(decListObj.getUnit2())).collect(Collectors.toList());
                jgVFlyBgDeclist.setUnit2Name(erpUnitsList2.size()>0?erpUnitsList2.get(0).getName():"");//法定单位2名称
            }
            jgVFlyBgDeclist.setCusOriginCountry(decListObj.getDesCountry()); // 原产国
            //原产国名称
            if(StringUtils.isNotBlank(decListObj.getDesCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                                .equals(decListObj.getDesCountry()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setCusOriginCountryName(!erpCountries.isEmpty() ?erpCountries.get(0).getName():"");
            }
            jgVFlyBgDeclist.setDutyMode(decListObj.getFaxTypeCode()); // 征免方式
            if(isNotBlank(decListObj.getFaxTypeCode())){
                List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(j->j.getValue()
                                .equals(decListObj.getFaxTypeCode()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setDutyModeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
            }
            jgVFlyBgDeclist.setGoodsAttr(decListObj.getGoodsAttr()); // 货物属性代码
            //货物属性名称
            if(isNotBlank(decListObj.getGoodsAttr())){
                List<String> goodsAttrNameList=new ArrayList<>();
                for(String s:Arrays.asList(decListObj.getGoodsAttr().split(","))){
                    List<DictModelVO> dictModelVOS=hwsx.stream().filter(j->j.getValue()
                                    .equals(s))
                            .collect(Collectors.toList());
                    goodsAttrNameList.add(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
                }
                jgVFlyBgDeclist.setGoodsAttrName(String.join(",",goodsAttrNameList));
            }
            jgVFlyBgDeclist.setGName(decListObj.getHsname()); // 商品名称 申报品名
            jgVFlyBgDeclist.setDistrictCode(decListObj.getDistrictCode()); // 境内目的地/境内货源地
            jgVFlyBgDeclist.setCiqName(decListObj.getCiqName());
            jgVFlyBgDeclist.setPurpose(decListObj.getPurpose()); // 用途代码
            if(isNotBlank(decListObj.getPurpose())){
                // 用途名称
                List<DictModelVO> dictModelVOS=yt.stream().filter(j->j.getValue()
                                .equals(decListObj.getPurpose()))
                        .collect(Collectors.toList());
                jgVFlyBgDeclist.setPurposeName(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
            }
            //许可证信息
            if(isNotBlank(decListObj.getGoodsLimitType())){
                String decRequCertList = decListObj.getGoodsLimitType()
                        .replaceAll("LicenceNo", "licenceNo")
                        .replaceAll("LicTypeCode", "licTypeCode")
                        .replaceAll("LicWrtofDetailNo", "licWrtofDetailNo")
                        .replaceAll("LicWrtofQty", "licWrtofQty")
                        .replaceAll("LicWrtofQtyUnit", "licWrtofQtyUnit")
                        .replaceAll("LicTypeName", "licTypeName");
                jgVFlyBgDeclist.setPreDecCiqGoodsLimit(decRequCertList);
            }
            preDecListVo.add(jgVFlyBgDeclist);
        }

    }
    //集装箱处理
    private void setPreDecContainerVo(List<DecContainer> decContainerList,List<JgVFlyBgDeccontainer> preDecContainerVoList){
        for(DecContainer decContainer:decContainerList){
            JgVFlyBgDeccontainer preDecContainerVo=new JgVFlyBgDeccontainer();
            preDecContainerVo.setContainerNo(decContainer.getContainerId());
            preDecContainerVo.setContainerMdCode(decContainer.getContainerMd());
            preDecContainerVo.setContainerWt(String.valueOf(decContainer.getGoodsContaWt()));
            preDecContainerVo.setLclFlag(decContainer.getLclFlag());
            preDecContainerVo.setGoodsNo(decContainer.getGoodsNo());
            preDecContainerVoList.add(preDecContainerVo);
        }

    }

    @Override
    public Result<DecLicenseDocus> addDecLicenseDocus(String etpsNo, String docuCode, String certCode) {
        if (StringUtils.isEmpty(etpsNo)){
            return Result.error("企业内部编号为空");
        }
        if (StringUtils.isEmpty(docuCode)){
            return Result.error("随附单证代码为空");
        }
        if (StringUtils.isEmpty(certCode)){
            return Result.error("随附单证编号为空");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        DecLicenseDocus licenseDocus = decLicenseDocusesService.getOne(new QueryWrapper<DecLicenseDocus>().lambda()
                .eq(DecLicenseDocus::getCertCode,certCode));
        if (licenseDocus != null){
            return Result.ok("已存在相同清单编号的随附单证信息");
        }
        NemsInvtHead invtHead = invtHeadMapper.selectOne(new QueryWrapper<NemsInvtHead>().lambda().eq(NemsInvtHead::getEtpsInnerInvtNo,etpsNo));
        if (invtHead == null){
            return Result.error("未找到对应的核注单信息");
        }
        DecHead decHead = baseMapper.selectOne(new QueryWrapper<DecHead>().lambda().eq(DecHead::getInvId,invtHead.getId()));
        DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
        decLicenseDocus.setDecId(decHead.getId());
        decLicenseDocus.setDocuCode(docuCode);
        decLicenseDocus.setCertCode(certCode);
        decLicenseDocusesService.save(decLicenseDocus);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(decLicenseDocus);
    }

    /**
     * 处理导出的报关单数据,转换各种代码
     */
    private void processingExportData(List<ExportDecExcel> exportDecExcelList){
        //海关代码
        List<ErpCustomsPorts> erpCustomsPortsList = erpCustomsPortsService.list();
        //运输方式
        List<ErpTransportTypes> erpTransportTypesList = erpTransportTypesService.list();
        //监管方式
        List<DictModelVO> jgfs = decListMapper.getDictItemByCode("JGFS");
        //征免性质
        List<DictModelVO> zmxz = decListMapper.getDictItemByCode("ZMXZ");
        //国家
        List<ErpCountries> erpCountriesList = erpCountriesService.list();
        //成交方式
        List<DictModelVO> tradingTypeList = decListMapper.getDictItemByCode("trading_type");
        //包装种类
        List<ErpPackagesTypes> erpPackagesTypesList = erpPackagesTypesService.list();
        //口岸数据
        List<ErpChinaPorts> erpChinaPortsList = erpChinaPortsService.list();
        //单位
        List<ErpUnits> erpUnitsList = erpUnitsService.list();
        //币制
        List<ErpCurrencies> erpCurrenciesList = erpCurrenciesService.list();
        //征免方式
        List<DictModelVO> zjmsfsList = decListMapper.getDictItemByCode("ZJMSFS");
        //货源地
        List<ErpDistricts> erpDistrictsList = erpDistrictsService.list();
        //港口
        List<ErpCityports> erpCityportsList = erpCityportsService.list();
        //=====开始转换赋值
        for(ExportDecExcel exportDecExcel:exportDecExcelList){
            //申报地海关
            if(isNotBlank(exportDecExcel.getDeclarePlace())){
                List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                        .equals(exportDecExcel.getDeclarePlace()))
                        .collect(Collectors.toList());
                exportDecExcel.setDeclarePlace(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
            }
            //出入境关别
            if(StringUtils.isNotBlank(exportDecExcel.getOutPortCode())){
                List<ErpCustomsPorts> erpCustomsPorts=erpCustomsPortsList.stream().filter(i->i.getCustomsPortCode()
                        .equals(exportDecExcel.getOutPortCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setOutPortCode(erpCustomsPorts.size()>0?erpCustomsPorts.get(0).getName():"");
            }
            //运输方式
            if(StringUtils.isNotBlank(exportDecExcel.getShipTypeCode())){
                List<ErpTransportTypes> erpTransportTypes=erpTransportTypesList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getShipTypeCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setShipTypeCode(erpTransportTypes.size()>0?erpTransportTypes.get(0).getName():"");
            }
            //监管方式
            if(StringUtils.isNotBlank(exportDecExcel.getTradeTypeCode())){
                List<DictModelVO> dictModelVO1=jgfs.stream().filter(i->i.getValue()
                        .equals(exportDecExcel.getTradeTypeCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setTradeTypeCode(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }
            //征免性质
            if(StringUtils.isNotBlank(exportDecExcel.getTaxTypeCode())){
                List<DictModelVO> dictModelVO1=zmxz.stream().filter(i->i.getValue()
                        .equals(exportDecExcel.getTaxTypeCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setTaxTypeCode(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }
            //贸易国
            if(StringUtils.isNotBlank(exportDecExcel.getTradeCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getTradeCountry()))
                        .collect(Collectors.toList());
                exportDecExcel.setTradeCountry(erpCountries.size()>0?erpCountries.get(0).getName():"");
            }
            //成交方式名称
            if(StringUtils.isNotBlank(exportDecExcel.getTermsTypeCode())){
                List<DictModelVO> dictModelVO1=tradingTypeList.stream().filter(i->i.getValue()
                        .equals(exportDecExcel.getTermsTypeCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setTermsTypeCode(dictModelVO1.size()>0?dictModelVO1.get(0).getText():"");
            }
            //包装名称
            if(StringUtils.isNotBlank(exportDecExcel.getPacksKinds())){
                List<ErpPackagesTypes> erpPackagesTypes=erpPackagesTypesList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getPacksKinds()))
                        .collect(Collectors.toList());
                exportDecExcel.setPacksKinds(erpPackagesTypes.size()>0?erpPackagesTypes.get(0).getName():"");
            }
            //离境入境口岸名称
            if(StringUtils.isNotBlank(exportDecExcel.getEntyPortCode())){
                List<ErpChinaPorts> erpChinaPorts=erpChinaPortsList.stream().filter(i->i.getChinaPortCode()
                        .equals(exportDecExcel.getEntyPortCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setEntyPortCode(erpChinaPorts.size()>0?erpChinaPorts.get(0).getName():"");
            }
            //成交单位名称
            if(StringUtils.isNotBlank(exportDecExcel.getUnitCode())){
                List<ErpUnits> erpUnits=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getUnitCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setUnitCode(erpUnits.size()>0?erpUnits.get(0).getName():"");
            }
            //币制
            if(StringUtils.isNotBlank(exportDecExcel.getCurrencyCode())){
                List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                        .equals(exportDecExcel.getCurrencyCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setCurrencyCode(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
            }
            //法定第一单位
            if(StringUtils.isNotBlank(exportDecExcel.getUnit1())){
                List<ErpUnits> erpUnits=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getUnit1()))
                        .collect(Collectors.toList());
                exportDecExcel.setUnit1(erpUnits.size()>0?erpUnits.get(0).getName():"");
            }
            //法二单位名称
            if(StringUtils.isNotBlank(exportDecExcel.getUnit2())){
                List<ErpUnits> erpUnits=erpUnitsList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getUnit2()))
                        .collect(Collectors.toList());
                exportDecExcel.setUnit2(erpUnits.size()>0?erpUnits.get(0).getName():"");
            }
            //原产国
            if(StringUtils.isNotBlank(exportDecExcel.getDesCountry())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getDesCountry()))
                        .collect(Collectors.toList());
                exportDecExcel.setDesCountry(erpCountries.size()>0?erpCountries.get(0).getName():"");
            }
            //征免方式
            if(StringUtils.isNotBlank(exportDecExcel.getFaxTypeCode())){
                List<DictModelVO> dictModelVOS=zjmsfsList.stream().filter(i->i.getValue()
                        .equals(exportDecExcel.getFaxTypeCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setFaxTypeCode(dictModelVOS.size()>0?dictModelVOS.get(0).getText():"");
            }
            //境内货源地名称
            if(StringUtils.isNotBlank(exportDecExcel.getDistrictCode())){
                List<ErpDistricts> erpDistricts=erpDistrictsList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getDistrictCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setDistrictCode(erpDistricts.size()>0?erpDistricts.get(0).getName():"");
            }
            //启运国/运抵国
            if(StringUtils.isNotBlank(exportDecExcel.getArrivalArea())){
                List<ErpCountries> erpCountries=erpCountriesList.stream().filter(i->i.getCode()
                        .equals(exportDecExcel.getArrivalArea()))
                        .collect(Collectors.toList());
                exportDecExcel.setArrivalArea(erpCountries.size()>0?erpCountries.get(0).getName():"");
            }
            //启运港
            if(StringUtils.isNotBlank(exportDecExcel.getDespPortCode())){
                List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                        .equals(exportDecExcel.getDespPortCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setDespPortCode(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
            }
            //经停港
            if(StringUtils.isNotBlank(exportDecExcel.getDesPort())){
                List<ErpCityports> erpCityports=erpCityportsList.stream().filter(i->i.getCityportCode()
                        .equals(exportDecExcel.getDesPort()))
                        .collect(Collectors.toList());
                exportDecExcel.setDesPort(erpCityports.size()>0?erpCityports.get(0).getCnname():"");
            }
            //运费币制名称
            if(StringUtils.isNotBlank(exportDecExcel.getShipCurrencyCode())){
                List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                        .equals(exportDecExcel.getShipCurrencyCode()))
                        .collect(Collectors.toList());
                exportDecExcel.setShipCurrencyCode(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
            }
            //保费币制名称
            if(StringUtils.isNotBlank(exportDecExcel.getInsuranceCurr())){
                List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                        .equals(exportDecExcel.getInsuranceCurr()))
                        .collect(Collectors.toList());
                exportDecExcel.setInsuranceCurr(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
            }
            //杂费币种名称
            if(StringUtils.isNotBlank(exportDecExcel.getOtherCurr())){
                List<ErpCurrencies> erpCurrencies=erpCurrenciesList.stream().filter(i->i.getCurrency()
                        .equals(exportDecExcel.getOtherCurr()))
                        .collect(Collectors.toList());
                exportDecExcel.setOtherCurr(erpCurrencies.size()>0?erpCurrencies.get(0).getName():"");
            }

        }
    }

    /**
     * 获取需要同步舱单的报关单数据
     * @return
     */
    @Override
    public Result<?> getDecHeadByManifestInfo(String tenandId) {
        String[] decStatus = {"1"};
        List<DecHead> decHeadList=baseMapper.getDecHeadByManifestInfo(Arrays.asList(decStatus),tenandId);
        if(isEmpty(decHeadList)){
            return Result.error("未获取到需要同步舱单数据的报关单");
        }
        List<String> decHeadId = new ArrayList<>();
        //获取登录企业的报关员操作卡号
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, Long.valueOf(TenantContext.getTenant())));
        String swid = SWID_XJA;
        if (isNotEmpty(enterpriseInfo) && isNotBlank(enterpriseInfo.getSwid())) {
            swid = enterpriseInfo.getSwid();
        }

        for(DecHead decHead:decHeadList){
            Result<?> manifestInfo = getManifestInfo(decHead.getId(), decHead.getOutPortCode(),
                    decHead.getShipTypeCode(), decHead.getBillCode(),swid);
            if(manifestInfo.isSuccess()){
                decHeadId.add(decHead.getId());
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return Result.OK(decHeadId);
    }

    /**
     * 保存申报草单
     *
     * @param apply 委托单信息
     * @return com.yorma.entity.YmMsg<com.yorma.apply.entity.Apply> 成功为 true+委托信息 失败为 false+失败原因
     * @apiNote <pre>
     *   保存申报草单,根据委托单进行核注单和报关单的添加,在保存报关单时会重新获取委托中的包装种类、其他包装、进出境关别、申报地海关等字段重新赋值
     * </pre>
     * <AUTHOR> 2021/9/9 15:33
     * @version 1.0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveDecAndInvt(OrderInfo apply) {
        if (isEmpty(apply)) {
            return Result.error("业务信息为空");
        }
//        String customerId = RequestKit.getRequestIn().getHeader(CUSTOMER_ID);
        List<DecHead> decHeads = new ArrayList<>();
        List<DecList> decLists = new ArrayList<>();
        List<NemsInvtList> invtLists = new ArrayList<>();
//        List<PubOperateHistory> historyList = new ArrayList<>();
//        Map<String, CustomsDict> gbdzMap = getDictMap(GBDQ);//国别地区
//        Map<String, CustomsDict> bzdmMap = getDictMap(BZDM);//币制
//        RequestKit.copyHeaders(false, XID, TENANT_ID, HEADER_TOKEN, TENANT_TYPE, CUSTOMER_ID);
//        String token = RequestKit.getRequestIn().getHeader(HEADER_TOKEN);
        // 从redis获取当前登录用户名
//        String username = (String) ((HashMap<String, Object>) redisUtil.get(PREFIX_USER_TOKEN_INFO + token)).get(USERNAME);

//        List<WarehouseOutDto> warehouseOutDtoList = new ArrayList<>();
        int number = 1;
        //核注单也报关单的关联集合
        Map<String, NemsInvtHead> relationMap = new HashMap<>();
//        String rbacIds = apply.getRbacIds();
//        String customsName = null;
//        if (apply.getNemsInvtHeads() != null && !apply.getNemsInvtHeads().isEmpty()) {
//            customsName = apply.getNemsInvtHeads().get(0).getDclEtpsNm();
//        } else {
//            customsName = apply.getDecHeads().get(0).getDeclareUnitName();
//        }
        //申报企业
//        Customer dclEtps = RpcKit.get(customerApi, CustomerApi::getCustomerByName, customsName, "-1").getData();
//        if (dclEtps != null) {
//            rbacIds = new StringBuilder(rbacIds).append(",").append(dclEtps.getId()).toString();
//        }
        // ZHANGCHAO@2021/6/21 18:00 追加/变更/完善：权限字段当前登录货代被冲掉！！
//        if (isNotBlank(rbacIds) && !rbacIds.contains(customerId)) {
//            rbacIds = rbacIds + "," + customerId;
//        }
        //TODO 补充 RBAC
//        rbacIds = DecServiceImpl.supplyRbac(rbacIds,dclEtps!= null ? dclEtps.getId().toString() :"");

//        List<Account> accounts = decHeadMapper.listAccountByApplyId(apply.getId());
//        Account account = accounts != null && !accounts.isEmpty() ? accounts.get(0) : null;
//        List<AccountDecinvt> accountDecinvts = new ArrayList<>();
        Map<String,String> supvModecdMap = new HashMap<>();
        if (apply.getNemsInvtHeads() != null && !apply.getNemsInvtHeads().isEmpty()) {

            for (NemsInvtHead v : apply.getNemsInvtHeads()) {

//                String rbacIdInvt = apply.getRbacIds();
//                if (dclEtps != null && !rbacIds.contains(dclEtps.getId().toString())) {
//                    rbacIds = new StringBuilder(rbacIds).append(",").append(dclEtps.getId()).toString();
//                }
//                if (isNotBlank(rbacIds) && !rbacIds.contains(customerId)) {
//                    rbacIds = rbacIds + "," + customerId;
//                }
//                v.setRbacIds(rbacIdInvt);

                //生成报关单所使用的分票序号
//                String partId = apply.getHbl() + "_" + String.format(new StringBuffer("%0").append(3).append("d").toString(), number);

                if (isNotEmpty(v.getVid())) {
                    v.setId(IdWorker.getId());
                }
                v.setCreatePassPort(false);

                //重新赋值部分核注单值
                v.setImpexpPortcd(apply.getExitClearance());//进出关别

//                List<EmsAimg> emsAimgs = new ArrayList<>();
//                Map<Integer, EmsAimg> emsAimgMap = new HashedMap();
//                v.getNemsInvtLists().forEach(lv -> {
//                    lv.setEtpsInnerInvtNo(v.getEtpsInnerInvtNo());
//                    lv.setInvId(v.getId());
//                    if (isNotEmpty(lv.getSupvModecd())) {
//                        supvModecdMap.put(lv.getGoodsId(), lv.getSupvModecd());
//                    }
//
//                    if (((v.getNonBusiness() != null && v.getNonBusiness())
//                            || (isNotEmpty(apply.getApplyType()) && ApplyType.BWS.equals(apply.getApplyType()) && "E".equals(apply.getImSign())))
//                            && isNotEmpty(apply.getOutZoneMode()) && "1".equals(apply.getOutZoneMode())) {
//                        if (emsAimgMap.containsKey(lv.getPutrecSeqno())) {
//                            EmsAimg emsAimg = emsAimgMap.get(lv.getPutrecSeqno());
//                            emsAimg.setOccupyQty(emsAimg.getOccupyQty().add(lv.getDclQty()));
//                            emsAimgMap.put(lv.getPutrecSeqno(), emsAimg);
//                        } else {
//                            EmsAimg emsAimg = new EmsAimg();
//                            emsAimg.setgNo(lv.getPutrecSeqno());
//                            emsAimg.setOccupyQty(lv.getDclQty());
//                            emsAimg.setEmsNo(v.getPutrecNo());
//                            emsAimgMap.put(lv.getPutrecSeqno(), emsAimg);
//                        }
//                    }
//
//                });
//                emsAimgs.addAll(emsAimgMap.values());
                for (NemsInvtList nemsInvtList : v.getNemsInvtLists()) {
                    nemsInvtList.setInvId(v.getId());
                }
                invtLists.addAll(v.getNemsInvtLists());
                if (isNotEmpty(v.getVid())) {
                    relationMap.put(v.getVid(), v);
                }
//                PubOperateHistory operateHistory = new PubOperateHistory();
//                operateHistory.setLogTime(new Date());
//                operateHistory.setUser(username);
//                operateHistory.setType("Invt");
//                operateHistory.setNote(Thread.currentThread().getStackTrace()[1].getMethodName());
//                operateHistory.setContent("核注单【提交分票保存】：" + v.getId());
//                operateHistory.setSourceId(v.getId().toString());
//                String tenantId = RequestKit.getRequestIn().getHeader(TENANT_ID);
//                historyList.add(operateHistory);

//                if (((v.getNonBusiness() != null && v.getNonBusiness())
//                        || (isNotEmpty(apply.getApplyType()) && ApplyType.BWS.equals(apply.getApplyType()) && "E".equals(apply.getImSign())))
//                        && isNotEmpty(apply.getOutZoneMode()) && "1".equals(apply.getOutZoneMode())
//                        && isNotEmpty(v.getPutrecNo()) && "L/T".contains(v.getPutrecNo().substring(0, 1))) {
//                    WarehouseOutDto warehouse = new WarehouseOutDto();
//                    warehouse.setInvtNo(v.getId().toString());
//                    warehouse.setEmsNo(v.getPutrecNo());
//                    warehouse.setEmsAimgList(emsAimgs);
//                    warehouseOutDtoList.add(warehouse);
//                }
//                if (account != null){
//                    AccountDecinvt accountDecinvt = new AccountDecinvt();
//                    accountDecinvt.setId(IdWorker.getId());
//                    accountDecinvt.setAccountNo(account.getAccountNo());
//                    accountDecinvt.setDecinvtId(v.getId());
//                    accountDecinvt.setType(2);
//                    accountDecinvt.setCreateBy(username);
//                    accountDecinvt.setCreateDate(new Date());
//                    accountDecinvt.setTenantId(Long.valueOf(tenantId));
//                    accountDecinvts.add(accountDecinvt);
//                }
            }
        }
        //处理数量
//        if (!warehouseOutDtoList.isEmpty() && !"0844".equals(apply.getTradeType())) {
//            /*
//             * 重构：保税物流商品出库占用！ 批量提交到ems统一处理？
//             * 2021/5/11 10:43
//             * ZHANGCHAO
//             */
//            Long start = System.currentTimeMillis();
//            /*
//            RequestKit.copyHeaders(false, XID, TENANT_ID, HEADER_TOKEN,CUSTOMER_ID);
//            List<StockHandleResultDto> stockHandleResults = new ArrayList<>();
//            for (WarehouseOutDto warehouseOutDto : warehouseOutDtoList) {
//                YmMsg<String> occupyGoodsYmMsg = emsStockApi.occupyGoods(warehouseOutDto.getEmsNo(), warehouseOutDto.getInvtNo(),
//                        warehouseOutDto.getEmsAimgList(), null);
//                if (!occupyGoodsYmMsg.isSuccess()) {
//                    throw new YmException(occupyGoodsYmMsg.getMessage());
//                }
//                StockHandleResultDto resultDto = new StockHandleResultDto();
//                resultDto.setEmsNo(warehouseOutDto.getEmsNo());
//                resultDto.setInvtNo(warehouseOutDto.getInvtNo());
//                resultDto.setEmsFlowsId(Long.parseLong(occupyGoodsYmMsg.getData()));
//                stockHandleResults.add(resultDto);
//            }
//            */
//            YmMsg<StockHandleResultDto> occupyGoodsYmMsg = emsStockApi.occupyGoodsBatch(warehouseOutDtoList);
//            if (!occupyGoodsYmMsg.isSuccess()) {
//                throw new YmException(occupyGoodsYmMsg.getMessage());
//            }
//            List<StockHandleResultDto> stockHandleResults = occupyGoodsYmMsg.getList();
//            Long end = System.currentTimeMillis();
//            System.out.println("时间：" + (end - start));
//
//            Map<String, Long> emsFlowsIdMap = new HashedMap();
//            stockHandleResults.forEach(v -> {
//                emsFlowsIdMap.put(v.getInvtNo(), v.getEmsFlowsId());
//            });
//            apply.getNemsInvtHeads().forEach(v -> {
//                if (emsFlowsIdMap.containsKey(v.getId().toString())) {
//                    v.setEmsFlowsId(emsFlowsIdMap.get(v.getId().toString()));
//                }
//            });
////            invtHeadService.updateBatchById(apply.getNemsInvtHeads());
//        }

        List<Long> decHeadId = new ArrayList<>();
        if (apply.getDecHeads() != null && !apply.getDecHeads().isEmpty()) {
            List<Boolean> choiseMark = new ArrayList<>();
//            Map<String,CustomsTariff> tariffMap = new HashMap<>();
//            Map<String,ErpHscodes> tariffMap = new HashMap<>();

            List<Integer> numberDec = new ArrayList<>();
            numberDec.add(1);
//            String letterA = "A";
//            char[] letterChar = letterA.toCharArray();

//            String rbacId = rbacIds;
//            List<String> hscodes = new ArrayList<>();
            apply.getDecHeads().forEach(v -> {
                //针对集中申报 getTaxSign
//                if ("山东太古飞机工程有限公司".equals(v.getOptUnitName()) &&
//                        (
//                                ((isNotEmpty(v.getTradeTypeCode()) && v.getTradeTypeCode().contains("1300") && "I".equals(v.getIeFlag()))
//                                        || (isNotEmpty(v.getTaxSign()) && v.getTaxSign())
//                                        || (isNotEmpty(apply.getGoodsType()) && "106".equals(apply.getGoodsType())))
//                        )){
//                    if(choiseMark.isEmpty()){
//                        choiseMark.add(true);
//                    }else {
//                        choiseMark.set(0,true);
//                    }
//                    v.getDecLists().forEach(lv -> {
//                        if (!hscodes.contains(lv.getHscode())){
//                            hscodes.add(lv.getHscode());
//                        }
//                    });
//                    List<CustomsTariff> tariffList = customsTariffMapper.selectList(new QueryWrapper<CustomsTariff>().lambda()
//                            .in(CustomsTariff::getCodeTs,hscodes));
//                    tariffList.forEach(lv->{
//                        tariffMap.put(lv.getCodeTs(),lv);
//                    });
//                }

//                v.setRbacIds(rbacId);

//                String partId = apply.getHbl() + "_"+String.format(new StringBuffer("%0").append(3).append("d").toString(), numberDec.get(0));
                if (apply.getDecHeads().size() > 1 && !"111".equals(apply.getGoodsType())) {//2022-05-20 修改 YTG-GW-3128
                    String partId = String.format(new StringBuffer("%0").append(3).append("d").toString(), numberDec.get(0));
                    numberDec.set(0, numberDec.get(0) + 1);
                    v.setPartId(partId);
                }
//                v.setAccountNo(isBlank(v.getAccountNo()) ? apply.getAccountNo() : v.getAccountNo());
                v.setId(IdWorker.getIdStr());
                v.setCustomsCode("D" + v.getId());

                //非二线分拨出区的重新赋值
//                if (!((("2".equals(apply.getApplyKind()) && "E".equals(apply.getIeFlag())) || "2E".equals(apply.getApplyKind()))
//                        && isNotBlank(apply.getOutZoneMode()) && "1".equals(apply.getOutZoneMode()))) {
//                    v.setPacks(isNotEmpty(apply.getPacks()) ? apply.getPacks() : v.getPacks());//件数
//                    v.setGrossWeight(isNotEmpty(apply.getGw()) ? apply.getGw() : v.getGrossWeight());//毛重
//                    v.setShipFeeCode(isNotEmpty(apply.getShipFeeCode()) ? apply.getShipFeeCode() : v.getShipFeeCode());//运费代码
//                    v.setShipFee(isNotEmpty(apply.getFreightAmount()) ? apply.getFreightAmount() : v.getShipFee());//运费
//                    v.setShipCurrencyCode(isNotEmpty(apply.getShipCurrencyCode()) ? apply.getShipCurrencyCode() : v.getShipCurrencyCode());//运费币制
//                    v.setInsuranceCode(isNotEmpty(apply.getInsuranceCode()) ? apply.getInsuranceCode() : v.getInsuranceCode());//保费代码
//                    v.setInsurance(isNotEmpty(apply.getPremiumAmount()) ? apply.getPremiumAmount() : v.getInsurance());//保费
//                    v.setInsuranceCurr(isNotEmpty(apply.getInsuranceCurr()) ? apply.getInsuranceCurr() : v.getInsuranceCurr());//保费
//                    v.setOtherCurr(isNotEmpty(apply.getOtherCurr()) ? apply.getOtherCurr() : v.getOtherCurr());//杂费币制
//                    v.setExtras(isNotEmpty(apply.getMiscellaneousAmount()) ? apply.getMiscellaneousAmount() : v.getExtras());//杂费
//                    v.setExtrasCode(isNotEmpty(apply.getExtrasCode()) ? apply.getExtrasCode() : v.getExtrasCode());//杂费代码
////                    v.setTermsTypeCode(isNotEmpty(apply.getTransMode()) ? apply.getTransMode() : v.getTermsTypeCode());//成交方式
//                }

//                v.setPacksKinds(isNotEmpty(apply.getPacksKinds()) ? apply.getPacksKinds() : v.getPacksKinds());//包装种类
//                v.setPackType(isNotEmpty(apply.getOtherPack()) ? apply.getOtherPack() : v.getPackType());//其他包装
                v.setOutPortCode(isNotEmpty(apply.getExitClearance()) ? apply.getExitClearance() : v.getOutPortCode());//进出境关别
//                v.setDeclarePlace(isNotEmpty(apply.getExitClearance())
//                        ? apply.getExitClearance() : v.getDeclarePlace());// 申报地海关
                v.setEntyPortCode(isNotEmpty(apply.getDeparturePort())
                        ? apply.getDeparturePort() : v.getEntyPortCode());// 入境口岸/离境口岸

                if ("I".equals(v.getIeFlag())) {//if ("I".equals(apply.getImSign())) {
                    v.setDespPortCode(isNotEmpty(apply.getPortDestination()) ? apply.getPortDestination()
                            : v.getDespPortCode());//启运港
                    v.setDesPort(isNotEmpty(apply.getDesPort()) ? apply.getDesPort()
                            : v.getDesPort());// 经停港
                } else if ("E".equals(v.getIeFlag())) {//if ("E".equals(apply.getImSign()))
                    v.setDesPort(isNotEmpty(apply.getDesPort()) ? apply.getDesPort()
                            : v.getDesPort());// 目的港(指运港)
                }
                if ("I".equals(v.getIeFlag())) {
                    v.setOutDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
                }
//                List<Integer> items = new ArrayList<>();
                v.getDecLists().forEach(lv -> {
                    lv.setDecId(v.getId());
                    if (isNotEmpty(lv.getSupvModecd())) {
                        supvModecdMap.put(lv.getGoodsId(), lv.getSupvModecd());
                    }
//                    if (choiseMark != null && !choiseMark.isEmpty() && choiseMark.get(0)//判断符合条件可以执行
//                            && tariffMap.containsKey(lv.getHscode())){
//                        CustomsTariff tariff = tariffMap.get(lv.getHscode());
//                        if (tariff != null && isNotEmpty(tariff.getSpecialCertificateTax())
//                                && (tariff.getSpecialCertificateTax().contains("5") || tariff.getSpecialCertificateTax().contains("6")
//                                || tariff.getSpecialCertificateTax().contains("7") || tariff.getSpecialCertificateTax().contains("8"))){
//                            items.add(lv.getItem());
//                        }
//                    }

                });
//                String markNumber = v.getMarkNumber();
//                if (items != null && !items.isEmpty()){
//                    StringBuffer markStr = new StringBuffer("第");
//                    Integer num = 1;//获取item计数
//                    Integer num1 = 1;//记录符合条件时循环多少次
//                    Integer num2 = 1;//记录一共循环多少次
//                    Integer lastTimeNum = 0;//记录上次开始循环是多少
//                    for(Integer item : items){
//                        if (num == 1){
//                            if(items.get(0) != 1){
//                                num = item;
//                            }
//                            lastTimeNum = num;
//                            markStr = markStr.append(num);
//                        }else if (num == item && num2 != items.size()){
//                            num++;
//                            num1++;
//                            num2++;
//                            continue;
//                        }else {
//                            if(num2 == items.size()){
//                                if (num == item){
//                                    markStr = markStr.append("-").append(item);
//                                }else {
//                                    if (lastTimeNum != num-1){
//                                        markStr = markStr.append("-").append(num-1).append(",").append(item);
//                                    }else {
//                                        markStr = markStr.append(",").append(item);
//                                    }
//                                }
//                            }else if (num1-1 !=1){
//                                markStr = markStr.append("-").append(num-1);
//                                num  = item;
//                                lastTimeNum = num;
//                                markStr = markStr.append(",").append(num);
//                            }else {
//                                num  = item;
//                                lastTimeNum = num;
//                                markStr = markStr.append(",").append(num);
//                            }
//
//                            num1 = 1;
//                        }
//                        num++;
//                        num1++;
//                        num2++;
//                    }
//                    markStr = markStr.append("项商品为新件");
//                    markNumber = isNotEmpty(markNumber) ? new StringBuffer(markNumber).append(";").append(markStr).toString() : markStr.toString();
//                    v.setMarkNumber(markNumber);
//
//                }
                if (!choiseMark.isEmpty()){
                    choiseMark.set(0,false);
                }

//                if (relationMap.containsKey(v.getVid())) {
//                    NemsInvtHead invtHead = relationMap.get(v.getVid());
////                    v.setEtpsInnerInvtNo(invtHead.getEtpsInnerInvtNo());
//                    v.setInvId(String.valueOf(invtHead.getId()));
//
//                }else {//2023-02-20 因只有报关单时生成草单复制报关单会赋值，影响之后太古的推送（根据此值判断有无核注单），故做处理
//                    v.setInvId(null);
//                }

                decHeads.add(v);
                decLists.addAll(v.getDecLists());

//                PubOperateHistory operateHistory = new PubOperateHistory();
//                operateHistory.setLogTime(new Date());
//                operateHistory.setUser(username);
//                operateHistory.setType("Dec");
//                operateHistory.setNote(Thread.currentThread().getStackTrace()[1].getMethodName());
//                operateHistory.setContent("报关单【提交分票保存】：" + v.getId());
//                operateHistory.setSourceId(v.getId().toString());
//                String tenantId = RequestKit.getRequestIn().getHeader(TENANT_ID);
//                historyList.add(operateHistory);

//                if (account != null){
//                    AccountDecinvt accountDecinvt = new AccountDecinvt();
//                    accountDecinvt.setId(IdWorker.getId());
//                    accountDecinvt.setAccountNo(account.getAccountNo());
//                    accountDecinvt.setDecinvtId(v.getId());
//                    accountDecinvt.setType(1);
//                    accountDecinvt.setCreateBy(username);
//                    accountDecinvt.setCreateDate(new Date());
//                    accountDecinvt.setTenantId(Long.valueOf(tenantId));
//                    accountDecinvts.add(accountDecinvt);
//                }
                decHeadId.add(Long.valueOf(v.getId()));
            });
        }
        for (DecHead decHead : decHeads){
            decHead.setOrderProtocolNo(apply.getOrderProtocolNo());
//            decHead.setOrderId(apply.getId());
            decHead.setApplyNumber(apply.getId());
            decHead.setTenantId(Long.valueOf(TenantContext.getTenant()));
            decHead.setDclTenantId(TenantContext.getTenant());
            if (StrUtil.isNotEmpty(decHead.getMarkNumber())){
                String markNumber = decHead.getMarkNumber();
                String lengthStr = markNumber.replaceAll("[\\u4e00-\\u9fa5]", "**");
                int length = lengthStr.length();
                if (length>255){
//                    throw new RuntimeException("提交的报关单中存在备注超长的数据");
                }
            }
            // 自动初复审
            dealDecAutoAudit(decHead);
        }
        for (DecList decList : decLists){
            if (isNotEmpty(decList.getHsmodel())) {
                String hsmodel = decList.getHsmodel();
                String lengthStr = hsmodel.replaceAll("[\\u4e00-\\u9fa5]", "**");
                int length = lengthStr.length();
                if (length > 255) {
//                    throw new RuntimeException("提交的报关单中存在规格型号超长的数据");
                }
            }
        }
        this.saveBatch(decHeads);
        decListService.saveBatch(decLists);
        invtHeadService.saveBatch(apply.getNemsInvtHeads());
        invtListService.saveBatch(invtLists);
//        if ("山东太古飞机工程有限公司".equals(apply.getConsignor())
//                && (isNotEmpty(apply.getmBillNo()) || isNotEmpty(apply.getHbl()))){
//            handlePushFile(apply,decHeadId);
//        }
//        RequestKit.copyHeaders(false, XID, TENANT_ID, HEADER_TOKEN, TENANT_TYPE, CUSTOMER_ID);
//        commonApi.saveHistoryBatch(historyList);
//        if (!accountDecinvts.isEmpty()){
//            decHeadMapper.saveBatchAccountDecinvt(accountDecinvts);
//        }
        if (!supvModecdMap.isEmpty()){
            baseMapper.updateApplyInvoiceSupvModecd(supvModecdMap, apply.getId());
        }

        return Result.ok(apply);
    }

    /**
     * 自动审核功能
     *
     * @param decHead
     * @return void
     * <AUTHOR>
     * @date 2025/4/15 15:12
     */
    private void dealDecAutoAudit(DecHead decHead) {
        if (decHead == null) {
            return;
        }
        String username = decHead.getCreatePerson();
        // 获取当前登录用户
        LoginUser loginUser;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            username = loginUser.getUsername();
        } catch (Exception e) {
            log.error("获取当前登录用户失败", e);
        }
        String tenantId = isBlank(TenantContext.getTenant()) ? (isNotEmpty(decHead.getTenantId()) ? decHead.getTenantId().toString() : null) : TenantContext.getTenant();
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "IS_DEC_AUTO_AUDIT")
                .eq(SysConfig::getTenantId, tenantId));
        if (isNotEmpty(sysConfigs) && "1".equals(sysConfigs.get(0).getConfigValue())) {
            Date date = new Date();
            log.info("【dealDecAutoAudit】启用自动初复审！！");
            decHead.setInitialReviewStatus("2");
            decHead.setFirstTrialBy(username);
            decHead.setFirstTrialDate(date);
            decHead.setFirstOpinion("同意");
            decHead.setReviewBy(username);
            decHead.setReviewDate(DateUtil.offsetMinute(date, 12));
            decHead.setReviewOpinion("同意");
        }
    }

    /**
     * 报关单生成核注单
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/30 10:15
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleCreateInvtByDec(String id,String sysId){
        DecHead decHead = baseMapper.selectById(id);
        if(isEmpty(decHead)){
            return Result.error("报关单不存在！");
        }
        List<DecList> decLists = decListMapper.selectList(new LambdaQueryWrapper<DecList>()
                .eq(DecList::getDecId, id));
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //报关单生成核注单姑且按照固定的  报关，对应报关
        NemsInvtHead nemsInvtHead=new NemsInvtHead();
        //处理表头部分
        nemsInvtHead.setSysId(sysId);
        nemsInvtHead.setDclcusTypecd("2");
        BeanUtil.copyProperties(decHead,nemsInvtHead);
        nemsInvtHead.setId(Long.valueOf(IdWorker.getIdStr()));
        nemsInvtHead.setImpexpMarkcd(decHead.getIeFlag());
        nemsInvtHead.setInvtType("0"); // 2025/7/2 16:54@ZHANGCHAO 追加/变更/完善：清单类型默认普通清单
        nemsInvtHead.setPutrecNo(decHead.getRecordNumber());//备案号
        /*
         * 备案号首字母为T，生成的核注清单上料件成品标志都为料件，
         * 备案号首字母不为T时，进口报关单监管方式为 4400、4600、5100时，
         * 生成核注清单上料件成品标志为成品，其他监管方式时为料件。
         * 出口报关单监管方式代码 是为0265、0300、0664、0700、1233、5000时，
         * 生成核注清单上料件成品标志为料件，其他监管方式时为成品
         * 2025/7/2 17:08@ZHANGCHAO
         */
        String mtpckEndprdMarkcd = "";
        if (isNotBlank(nemsInvtHead.getPutrecNo())) {
            if (nemsInvtHead.getPutrecNo().startsWith("T")) {
                mtpckEndprdMarkcd = "I";
            } else {
                if (I.equals(decHead.getIeFlag())) {
                    if ("4400".equals(decHead.getTradeTypeCode()) || "4600".equals(decHead.getTradeTypeCode()) || "5100".equals(decHead.getTradeTypeCode())) {
                        mtpckEndprdMarkcd = "E";
                    } else {
                       mtpckEndprdMarkcd = "I";
                    }
                } else {
                    if ("0265".equals(decHead.getTradeTypeCode()) || "0300".equals(decHead.getTradeTypeCode()) || "0664".equals(decHead.getTradeTypeCode())
                            || "0700".equals(decHead.getTradeTypeCode()) || "1233".equals(decHead.getTradeTypeCode()) || "5000".equals(decHead.getTradeTypeCode())) {
                        mtpckEndprdMarkcd = "I";
                    } else {
                        mtpckEndprdMarkcd = "E";
                    }
                }
            }
            // 手册核注清单上的主管海关逻辑是取报关单备案号字段的2-5位数字
            nemsInvtHead.setDclplcCuscd(nemsInvtHead.getPutrecNo().substring(1, 5));
        }
        nemsInvtHead.setMtpckEndprdMarkcd(mtpckEndprdMarkcd);
        nemsInvtHead.setAudited(false);
        nemsInvtHead.setSend(false);
        nemsInvtHead.setCreateDate(new Date());
        nemsInvtHead.setCreatePerson(loginUser.getRealname());
        nemsInvtHead.setUpdateDate(null);
        nemsInvtHead.setUpdateBy(null);
        nemsInvtHead.setImportSeqNo(decHead.getSeqNo());
        nemsInvtHead.setDclTenantId(Long.valueOf(TenantContext.getTenant()));// 申请人租户ID
        nemsInvtHead.setBizopEtpsno(decHead.getOptUnitId());//境内收发货人（经营单位）
        nemsInvtHead.setBizopEtpsNm(decHead.getOptUnitName());
        nemsInvtHead.setBizopEtpsSccd(decHead.getOptUnitSocialCode());
        nemsInvtHead.setRcvgdEtpsno(decHead.getDeliverUnit());//消费使用单位（加工单位）
        nemsInvtHead.setRcvgdEtpsNm(decHead.getDeliverUnitName());
        nemsInvtHead.setRvsngdEtpsSccd(decHead.getDeliverUnitSocialCode());
        nemsInvtHead.setCorrEntryDclEtpsno(decHead.getDeclareUnit());
        nemsInvtHead.setCorrEntryDclEtpsNm(decHead.getDeclareUnitName());
        nemsInvtHead.setCorrEntryDclEtpsSccd(decHead.getDeclareUnitSocialCode());
        //申报单位信息
        nemsInvtHead.setDclEtpsno(decHead.getDeclareUnit());
        nemsInvtHead.setDclEtpsNm(decHead.getDeclareUnitName());
        nemsInvtHead.setDclEtpsSccd(decHead.getDeclareUnitSocialCode());
        //录入单位信息，取登录的租户信息
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getOne(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getTenantId, Long.valueOf(TenantContext.getTenant())));
        if(isNotEmpty(enterpriseInfo)){
            nemsInvtHead.setInputName(enterpriseInfo.getEnterpriseFullName());
            nemsInvtHead.setInputCode(enterpriseInfo.getCustomsDeclarationCode());
            nemsInvtHead.setInputCreditCode(enterpriseInfo.getUnifiedSocialCreditCode());
        }

//        nemsInvtHead.setDclplcCuscd(decHead.getDeclarePlace());//申报地海关
        nemsInvtHead.setDclcusFlag("1");
        nemsInvtHead.setEntryNo(isNotBlank(decHead.getClearanceNo())?decHead.getClearanceNo():null);
        nemsInvtHead.setEtpsInnerInvtNo(decHead.getEtpsInnerInvtNo());
        nemsInvtHead.setTrspModecd(decHead.getShipTypeCode());//运输方式
//        nemsInvtHead.setPutrecNo(decHead.getRecordNumber());//备案号
        nemsInvtHead.setImpexpPortcd(decHead.getOutPortCode());//进出境关别
        nemsInvtHead.setSupvModecd(decHead.getTradeTypeCode());//监管方式
        //转换字母到数字
        if(isNotBlank(decHead.getArrivalArea())){
            List<ErpCountries> erpCountries = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                    .eq(ErpCountries::getCode, decHead.getArrivalArea()));
            if(erpCountries.size()>0){
                List<ErpCountries> erpCountries2 = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                        .eq(ErpCountries::getName, erpCountries.get(0).getName())
                        .eq(ErpCountries::getIsenabled,"0"));
                nemsInvtHead.setStshipTrsarvNatcd(erpCountries2.size()>0?erpCountries2.get(0).getCode():"");//运抵国
            }
        }
        //贸易国
        if(isNotBlank(decHead.getTradeCountry())){
            List<ErpCountries> erpCountries = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                    .eq(ErpCountries::getCode, decHead.getTradeCountry()));
            if(erpCountries.size()>0){
                List<ErpCountries> erpCountries2 = erpCountriesMapper.selectList(new LambdaQueryWrapper<ErpCountries>()
                        .eq(ErpCountries::getName, erpCountries.get(0).getName())
                        .eq(ErpCountries::getIsenabled,"0"));
                nemsInvtHead.setTradeCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():"");//贸易国
            }
        }


        if("I".equals(decHead.getIeFlag())&&"0".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("1");
        }
        if("E".equals(decHead.getIeFlag())&&"0".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("2");
        }
        if("I".equals(decHead.getIeFlag())&&"2".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("3");
        }
        if("E".equals(decHead.getIeFlag())&&"2".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("4");
        }
        if("I".equals(decHead.getIeFlag())&&"1".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("9");
        }
        if("E".equals(decHead.getIeFlag())&&"1".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("A");
        }
        if("I".equals(decHead.getIeFlag())&&"3".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("B");
        }
        if("E".equals(decHead.getIeFlag())&&"3".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("C");
        }
        if("E".equals(decHead.getIeFlag())&&"4".equals(decHead.getDclTrnRelFlag())){
            nemsInvtHead.setDecType("F");
        }
        //处理表体
        //全部币制信息
        List<ErpCurrencies> erpCurrencies = erpCurrenciesMapper.selectList(null);
        List<ErpCountries> erpCountries = erpCountriesMapper.selectList(null);

        Map<Integer, NemsInvtList> nemsInvtListMap = new HashMap<>();
        int i = 1;
        for (DecList v : decLists) {
            if (isEmpty(v.getItem())) {
                return Result.error("报关单表体项的「商品项号」有空值，请检查！");
            }
            if (nemsInvtListMap.containsKey(v.getItem())) {
                //申报数量
                if (nemsInvtListMap.get(v.getItem()).getDclQty() == null || v.getGoodsCount() == null){
                    return Result.error("报关单表体项的「申报数量」有空值，请检查！");
                } else {
                    nemsInvtListMap.get(v.getItem())
                            .setDclQty(nemsInvtListMap.get(v.getItem()).getDclQty() != null
                                    ? nemsInvtListMap.get(v.getItem()).getDclQty().add(v.getGoodsCount()) : v.getGoodsCount());
                }

                //法定第一数量
                if (nemsInvtListMap.get(v.getItem()).getLawfQty() == null || v.getCount1() == null){
                    return Result.error("报关单表体项的「法定第一数量」有空值，请检查！");
                } else {
                    nemsInvtListMap.get(v.getItem())
                            .setLawfQty(nemsInvtListMap.get(v.getItem()).getLawfQty().add(v.getCount1()));
                }

                //法定第二数量
                nemsInvtListMap.get(v.getItem())
                        .setSecdLawfQty(nemsInvtListMap.get(v.getItem()).getSecdLawfQty() != null
                                ? nemsInvtListMap.get(v.getItem()).getSecdLawfQty().add(v.getCount2()) : v.getCount2());
                //总价
                if (nemsInvtListMap.get(v.getItem()).getDclTotalamt() == null || v.getTotal() == null){
                    return Result.error("报关单表体项的「总价」有空值，请检查！");
                } else {
                    nemsInvtListMap.get(v.getItem())
                            .setDclTotalamt(nemsInvtListMap.get(v.getItem()).getDclTotalamt().add(v.getTotal()));
                    //单价
                    nemsInvtListMap.get(v.getItem()).setDclUprcamt(
                            nemsInvtListMap.get(v.getItem()).getDclTotalamt()
                                    .divide(nemsInvtListMap.get(v.getItem()).getDclQty(), 4, RoundingMode.HALF_UP));
                }

                //净重
                if (nemsInvtListMap.get(v.getItem()).getNetWt() != null && v.getNetWeight() != null){
                    nemsInvtListMap.get(v.getItem())
                            .setNetWt(nemsInvtListMap.get(v.getItem()).getNetWt().add(v.getNetWeight()));
                    decHead.setNetWeight(decHead.getNetWeight().add(v.getNetWeight()));
                }
                nemsInvtListMap.get(v.getItem()).setPutrecSeqno(v.getRecordItem());
            }else {
                NemsInvtList nemsInvtList = new NemsInvtList();
                nemsInvtList.setId(null);
                nemsInvtList.setGdsseqNo(i);
                nemsInvtList.setEntryGdsSeqno(i);
                toNemsInvtList(v, nemsInvtList, nemsInvtHead, decHead);
                //转换币值
                if(isNotBlank(v.getCurrencyCode())){
                    List<ErpCurrencies> erpCurrencies1=erpCurrencies.stream().filter(s->s.getCurrency().equals(v.getCurrencyCode()))
                            .collect(Collectors.toList());
                    nemsInvtList.setDclCurrcd(erpCurrencies1.size()>0?erpCurrencies1.get(0).getCode():null);
                }
                //转换国别地区
                if(isNotBlank(v.getDestinationCountry())){
                    List<ErpCountries> erpCountries1= erpCountries.stream().filter(z->z.getCode().equals(v.getDestinationCountry()))
                            .collect(Collectors.toList());
                    if(erpCountries1.size()>0){
                        List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                        &&o.getIsenabled()==0)
                                .collect(Collectors.toList());
                        nemsInvtList.setNatcd(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                    }
                }
                if(isNotBlank(v.getDesCountry())){
                    List<ErpCountries> erpCountries1= erpCountries.stream().filter(p->p.getCode().equals(v.getDesCountry()))
                            .collect(Collectors.toList());
                    if(erpCountries1.size()>0){
                        List<ErpCountries> erpCountries2= erpCountries.stream().filter(o->o.getName().equals(erpCountries1.get(0).getName())
                                        &&o.getIsenabled()==0)
                                .collect(Collectors.toList());
                        nemsInvtList.setOriginCountry(erpCountries2.size()>0?erpCountries2.get(0).getCode():null);
                    }
                }



                if (nemsInvtHead.getNetWeight() == null) {
                    nemsInvtHead.setNetWeight(String.valueOf(nemsInvtList.getNetWt()));
                } else {
                    BigDecimal netWeight = nemsInvtList.getNetWt() == null ? BigDecimal.ZERO.stripTrailingZeros() : nemsInvtList.getNetWt();
                    nemsInvtHead.setNetWeight(String.valueOf(new BigDecimal(nemsInvtHead.getNetWeight()).add(netWeight)));
                }
                nemsInvtList.setPutrecSeqno(v.getRecordItem());
                nemsInvtListMap.put(v.getItem(),nemsInvtList);
                i++;

            }



        }

        List<NemsInvtList> nemsInvtListList=new ArrayList<>(nemsInvtListMap.values());
        nemsInvtHead.setNemsInvtLists(nemsInvtListList);
        //执行添加操作
        invtHeadService.save(nemsInvtHead);
        invtListService.saveBatch(nemsInvtListList);
        //更新报关单的核注单id
        baseMapper.update(null,new LambdaUpdateWrapper<DecHead>()
                .set(DecHead::getInvId,nemsInvtHead.getId()).eq(DecHead::getId,id));

        return Result.ok(nemsInvtHead);
    }

    /**
     * 获取核注单表体
     *
     * @param nemsInvtList
     * @param decList
     */
    private void toNemsInvtList(DecList decList,NemsInvtList nemsInvtList,NemsInvtHead nemsInvtHead,DecHead decHead){
        //        decList.setApplyNumber(nemsInvtList.getApplyNumber());
        nemsInvtList.setGoodsId(decList.getGoodsId());
        nemsInvtList.setInvId(nemsInvtHead.getId());
        nemsInvtList.setCiqCode(decList.getCiqCode());
        nemsInvtList.setCiqName(decList.getCiqName());
        nemsInvtList.setLawfQty(decList.getCount1());
        nemsInvtList.setSecdLawfQty(decList.getCount2());
        nemsInvtList.setDclCurrcd(decList.getCurrencyCode());
        nemsInvtList.setOriginCountry(decList.getDesCountry());//原产国
        nemsInvtList.setNatcd(decList.getDestinationCountry());//最终目的国
        nemsInvtList.setDclQty(decList.getGoodsCount());//申报数量
        nemsInvtList.setHscode(decList.getHscode());//税号
        nemsInvtList.setHsmodel(decList.getHsmodel());//申报要素
        nemsInvtList.setHsname(decList.getHsname());//品名
        nemsInvtList.setHstype(decList.getHstype());//法捡类型
        nemsInvtList.setNetWt(decList.getNetWeight());//净重
        nemsInvtList.setDclUprcamt(decList.getPrice());//单价
        nemsInvtList.setSupvModecd(decList.getSupvModecd());
        nemsInvtList.setDclTotalamt(decList.getTotal());//总价
        nemsInvtList.setLawfUnitcd(decList.getUnit1());//法定单位
        nemsInvtList.setSecdlawfUnitcd(decList.getUnit2());//法定第二单位
        nemsInvtList.setDclUnitcd(decList.getUnitCode());//计量单位
        nemsInvtList.setDclCurrcd(decList.getCurrencyCode());//币制
        nemsInvtList.setEntryGdsSeqno(isNotEmpty(decList.getItem()) ? decList.getItem() :nemsInvtList.getEntryGdsSeqno());//序号
        //关联取模板，对应取核注单
        nemsInvtList.setLvyrlfModecd(decList.getFaxTypeCode());//征免方式
        nemsInvtList.setUcnsVerno(decList.getExgVersion());//单耗版本号
        // 2025/7/25 17:57@ZHANGCHAO 追加/变更/完善：手册取商品料号！！
        if (isNotBlank(decHead.getRecordNumber()) && isNotEmpty(decList.getRecordItem()) && "B1".equals(nemsInvtHead.getSysId())) {
            if (I.equals(decHead.getIeFlag())) {
                PtsEmsAimg ptsEmsAimg = ptsEmsAimgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAimg>()
                        .eq(PtsEmsAimg::getEmsNo, decHead.getRecordNumber())
                        .eq(PtsEmsAimg::getGNo, decList.getRecordItem()).last("limit 1"));
                nemsInvtList.setGdsMtno(isNotEmpty(ptsEmsAimg) ? ptsEmsAimg.getCopGno() : null);
            } else {
                PtsEmsAexg ptsEmsAexg = ptsEmsAexgMapper.selectOne(new LambdaQueryWrapper<PtsEmsAexg>()
                        .eq(PtsEmsAexg::getEmsNo, decHead.getRecordNumber())
                        .eq(PtsEmsAexg::getGNo, decList.getRecordItem()).last("limit 1"));
                nemsInvtList.setGdsMtno(isNotEmpty(ptsEmsAexg) ? ptsEmsAexg.getCopGno() : null);
            }
        }
    }

    /**
     * 报关单列表
     * 根据最近操作时间，返回报关单列表。
     * 注意：最多只能查询 7 天的数据（使用 entryId 或 billNo 查询时，不受此限制）
     *
     * @param dclTrnRelFlag 报关单类型（空表示全部） 0 – 一般报关单 1 – 转关提前报关单 2 – 备案清单 3 – 转关提前备案清单 4 – 出口二次转关
     * @param etpsCategory  企业类别（空表示全部） A- 报关申报单位 B- 销售使用/生产销售单位 C - 报关收发货人 D – 报关录入单位
     * @param cusIEFlag     进出口标志（进口-I，出口-E， 全部-空串）
     * @param tableFlag     是否结关（未结关-0， 已结关-1， 全部-空串）
     * @param cnsnTradeCode 境内收发货人（18 位或 10 位）
     * @param entryId       报关单号或统一编号
     * @param billNo        提运单号
     * @param beginTime     起始时间(格式 yyyy-MM-dd)
     * @param endTime       结束时间(格式 yyyy-MM-dd)
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/6/26 16:02
     */
    @Override
    public Result<?> GetCDQuery(String dclTrnRelFlag, String etpsCategory, String cusIEFlag, String tableFlag,
                                String cnsnTradeCode, String entryId, String billNo, String beginTime, String endTime, String swid) {
        if (isBlank(swid)) {
            return Result.error("未知的操作员卡号！");
        }
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            log.info("系统内未获取到卡号[{}]持有人的信息！", swid);
            return Result.error("系统内未获取到卡号[" + swid + "]持有人的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        if (isBlank(beginTime)) {
            beginTime = DateUtil.format(DateUtil.offsetDay(new Date(), -3), DatePattern.NORM_DATE_PATTERN);
        }
        if (isBlank(endTime)) {
            endTime = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        }
        log.info("beginTime：{} endTime：{}", beginTime, endTime);
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<String> dataArr = new ArrayList<>();
        // 获取日期分组
        List<WeekGroup> weekGroups = getWeekGroups(DateUtil.parseDate(beginTime), DateUtil.parseDate(endTime));
        StringBuilder msg = new StringBuilder();
        int totalCount = 0;
        List<String> errorList = new ArrayList<>();
        // 处理每个分组
        for (WeekGroup weekGroup : weekGroups) {
            // 记录处理信息
            String processInfo = String.format("处理时间段: %s 至 %s",
                    weekGroup.getStartDateStr(),
                    weekGroup.getEndDateStr());
            dataArr.add(processInfo);
            log.info("处理日期段开始：{}", processInfo);
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("swid", swid); // 博汇用
            jsonMap.put("dclTrnRelFlag", dclTrnRelFlag);
            jsonMap.put("etpsCategory", etpsCategory);
            jsonMap.put("cusIEFlag", cusIEFlag);
            jsonMap.put("tableFlag", tableFlag);
            jsonMap.put("cnsnTradeCode", cnsnTradeCode);
            jsonMap.put("entryId", entryId);
            jsonMap.put("billNo", billNo);
            jsonMap.put("beginTime", weekGroup.getStartDateStr());
            jsonMap.put("endTime", weekGroup.getEndDateStr());
            log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
            // 等待直到获取到令牌
            rateLimiter.acquire();
            String result = sendOpenApi(URL_GET_CD_QUERY, JSON.toJSONString(jsonMap));
            String ja;
            try {
                JSONObject jsonObject = JSON.parseObject(result);
                // 2025/2/13 10:26@ZHANGCHAO 追加/变更/完善：另一种形式的返回结果!!!
                if (isNotEmpty(jsonObject.getBoolean("success"))
                        && !jsonObject.getBoolean("success") && isNotEmpty(jsonObject.getString("message"))) {
                    log.info("{}，获取报关单列表失败：{}", processInfo, jsonObject.getString("message"));
                    errorList.add(processInfo + "，获取报关单列表失败：" + jsonObject.getString("message"));
                    break;
                }
                if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
                    ja = jsonObject.getString("data");
                    JSONArray jsonArray = JSON.parseArray(ja);
                    log.info("【{}】返回的报关单列表个数：{}", processInfo, jsonArray.size());
                    totalCount += jsonArray.size();

                    // 1. 先保存报关单列表
                    List<SyncDecHead> syncDecHeads = new ArrayList<>();
                    for (Object item : jsonArray) {
                        JSONObject jo = (JSONObject) item;
                        SyncDecHead syncDecHead = JSONObject.parseObject(jo.toString(), SyncDecHead.class);
                        SyncDecHead one = syncDecHeadMapper.selectOne(new LambdaQueryWrapper<SyncDecHead>()
                                .eq(SyncDecHead::getCusCiqNo, syncDecHead.getCusCiqNo())
                                .eq(SyncDecHead::getTenantId, tenantId));
                        if (isEmpty(one)) {
                            syncDecHead.setTenantId(Long.valueOf(tenantId));
                            syncDecHeads.add(syncDecHead);
                        } else {
                            log.info("报关单统一编号：{} 已存在，跳过！", syncDecHead.getCusCiqNo());
                        }
                    }
                    // 1. 先保存报关单列表
                    saveSyncDec(syncDecHeads);
                } else {
                    log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                }
            } catch (Exception e) {
                e.printStackTrace();
                ExceptionUtil.getFullStackTrace(e);
                log.error("获取报关单异常：{}", e.getMessage());
            }
            log.info("处理日期段结束：{}", processInfo);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        // 2025/2/13 10:34@ZHANGCHAO 追加/变更/完善：优化下返回逻辑！！
        if (totalCount <= 0) {
            if (isNotEmpty(errorList)) {
                return Result.error("下行报关单列表出现异常：" + CollUtil.join(errorList, ";"));
            }
            msg.append("本次共获取报关单：").append(totalCount).append("个");
            return Result.ok(msg.toString());
        } else {
            msg.append("本次共获取报关单：").append(totalCount).append("个");
            return Result.ok(msg.toString());
        }
    }

    /**
     * 报关单全量数据(JSON)
     * 根据报关单统一编号或报关单号来获取报关单 Json 格式全部内容。
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/29 16:02
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> GetCDDetails(String cusCiqNo, String swid) {
        if (isBlank(swid)) {
            return Result.error("未知的操作员卡号！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        StringBuilder msg = new StringBuilder();
        DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
        // 如果未传报关单统一编号，则同步未同步的数据
        if (isBlank(cusCiqNo)) {
            // 还未同步的数据
            List<SyncDecHead> syncDecHeadList = syncDecHeadMapper.selectList(new LambdaQueryWrapper<SyncDecHead>()
                    .eq(SyncDecHead::getTenantId, tenantId)
                    .eq(SyncDecHead::getIsSync, "0"));
            if (isNotEmpty(syncDecHeadList)) {
                msg.append("未同步报关单数据：").append(syncDecHeadList.size());
                currentProxy.processSyncDecHeadList(syncDecHeadList, swid, msg);
            } else {
                msg.append("不存在未同步的报关单数据！");
            }
            // 如果传了报关单统一编号，则同步单个数据
        } else {
            currentProxy.processSyncDecHeadOne(cusCiqNo, swid, tenantId, msg);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(msg.toString());
    }

    /**
     * 读取JSON文件获取报关单全量数据(JSON)
     *
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/11/25 17:39
     */
    @Override
    public Result<?> GetCDDetailsByJsonFiles(String directoryPath, String swid) {
        if (isBlank(swid)) {
            log.info("未知的操作员卡号！");
            return Result.error("未知的操作员卡号！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            log.info("系统内未获取到卡号[" + swid + "]持有人的信息！");
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }

        if (isBlank(tenantId)) {
            log.info("租户ID为空！");
            return Result.error("租户ID为空！");
        }

        // 创建目录对象
        Path directory = Paths.get(directoryPath);
        if (!Files.exists(directory)) {
            log.info("文件目录不存在: {}", directoryPath);
            return Result.error("文件目录不存在: " + directoryPath);
        }

        // 创建finish_read目录
        Path finishDirectory = directory.resolve("finish_read");
        if (!Files.exists(finishDirectory)) {
            try {
                Files.createDirectory(finishDirectory);
            } catch (IOException e) {
                log.error("无法创建finish_read目录: {}", e.getMessage());
                return Result.error("无法创建finish_read目录");
            }
        }

        // 获取所有.json文件
        List<Path> jsonFiles = null;
        try {
            jsonFiles = Files.list(directory)
                    .filter(path -> path.toString().endsWith(".json"))
                    .filter(path -> !path.startsWith(finishDirectory)) // 排除finish_read目录中的文件
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("无法列出目录下的文件: {}", e.getMessage());
            return Result.error("无法列出目录下的文件");
        }

        if (isEmpty(jsonFiles)) {
            log.error("不存在需要处理的文件！");
            return Result.error("不存在需要处理的文件！");
        }

        List<SyncDecHead> syncDecHeadList = new ArrayList<>();
        // 处理每个JSON文件
        for (Path jsonFile : jsonFiles) {
            try {
                // 读取文件内容
                String content = new String(Files.readAllBytes(jsonFile));
                // 解析JSON
                JSONObject response = JSON.parseObject(content);

                // 检查响应是否正常且数据不为空
                if (Boolean.TRUE.equals(response.get("ok")) && response.get("data") != null) {
                    // 将data部分转换为SyncDecHead对象
                    SyncDecHead syncDecHead = response.getObject("data", SyncDecHead.class);
                    syncDecHead.setTenantId(Long.valueOf(tenantId));
                    syncDecHeadList.add(syncDecHead);
                } else {
                    System.err.println("Invalid data in file: " + jsonFile +
                            ". ok=" + response.get("ok") +
                            ", errors=" + response.get("errors"));
                    continue;
                }

                // 移动文件到finish_read目录
                Path targetPath = finishDirectory.resolve(jsonFile.getFileName());
                Files.move(jsonFile, targetPath, StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException e) {
                // 记录错误但继续处理其他文件
                log.info("处理文件出现异常: {}，异常信息: {}", jsonFile, e.getMessage());
            }
        }
        StringBuilder msg = new StringBuilder();
        if (isEmpty(syncDecHeadList)) {
            msg.append("不存在需要处理的报关单数据！");
            log.error("不存在需要处理的报关单数据！");
            return Result.ok(msg);
        }
        List<DecHead> decHeadList = new ArrayList<>();
        for (SyncDecHead syncDecHead : syncDecHeadList) {
            // 更新同步报关单表数据补充数据
            updateSyncDec(syncDecHead);
            DecHead decHead = baseMapper.selectOne(new LambdaQueryWrapper<DecHead>()
                    .eq(DecHead::getSeqNo, syncDecHead.getCusCiqNo()));
            if (null == decHead) {
                decHead = new DecHead();
            } else {
                List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
                        .eq(DecList::getDecId, decHead.getId()));
                decHead.setDecLists(decLists);
            }
            // 转换报关单数据
            setDecHeadFromJson(syncDecHead, decHead);
            decHeadList.add(decHead);
        }
        if (isNotEmpty(decHeadList)) {
            DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
            Result<?> result = currentProxy.saveDecHeadBatch_(decHeadList);
            msg.append(result.getMessage());
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(msg.toString());
    }

    /**
     * 报关单列表单独查询某一条数据
     *
     * @param swid
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/10/31 09:39
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> syncDecClearance(String swid) {
        if (isBlank(swid)) {
            return Result.error("未知的操作员卡号！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        String customerName = "";
        if (isBlank(customerName)) {
            EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoBySwid(swid);
            if (isNotEmpty(enterpriseInfo)) {
                customerName = enterpriseInfo.getEnterpriseFullName();
            }
        }
        EnterpriseInfo enterpriseInfo = commonMapper.getEnterpriseInfoByCond(customerName);
        String tenantId = "";
        if (isEmpty(enterpriseInfo)) {
            return Result.error("系统内未获取到卡号[" + swid + "]持有者的信息！");
        } else {
            if (isNotEmpty(enterpriseInfo)) {
                tenantId = String.valueOf(enterpriseInfo.getTenantId());
            }
        }
        StringBuilder msg = new StringBuilder();
        // 获取未结关的数据 申报状态不是8或者10
        // 申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，
        // 11查验通知，S公自用物品核准通过，T公自用物品退单，U公自用物品待核准）
        List<SyncDecHead> syncDecHeadList = syncDecHeadMapper.listUnclosedDec(tenantId);
        List<DecHead> decHeadList = syncDecHeadMapper.listUnclosedDecLocal(tenantId);
        if (isEmpty(syncDecHeadList) && isEmpty(decHeadList)) {
            return Result.ok("不存在未结关的报关单数据！");
        }
        // 创建一个 Set 用来存放统一编号
        Set<String> unifiedIds = new HashSet<>();
        if (isNotEmpty(syncDecHeadList)) {
            for (SyncDecHead syncDecHead : syncDecHeadList) {
                if (isNotBlank(syncDecHead.getCusCiqNo())) {
                    unifiedIds.add(syncDecHead.getCusCiqNo());
                }
            }
        }
        if (isNotEmpty(decHeadList)) {
            for (DecHead decHead : decHeadList) {
                if (isNotBlank(decHead.getSeqNo())) {
                    unifiedIds.add(decHead.getSeqNo());
                }
            }
        }
        if (isEmpty(unifiedIds)) {
            return Result.ok("不存在未结关的报关单数据！");
        }
        List<String> unifiedIdList = new ArrayList<>(unifiedIds);
        log.info("获取未结关报关单数据：{}", unifiedIdList.size());
        DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
        for (String seqNo : unifiedIdList) {
            // 等待直到获取到令牌
            rateLimiter.acquire();
            log.info("本次处理开始，当前时间：{}", DateUtil.formatDateTime(new Date()));
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("swid", swid); // 博汇用
            jsonMap.put("cusIEFlag", "");
            jsonMap.put("tableFlag", "");
            jsonMap.put("entryId", seqNo);
            jsonMap.put("beginTime", "2000-01-01");
            jsonMap.put("endTime", "2099-12-31");
            log.info("组装的请求参数：{}", JSON.toJSONString(jsonMap));
            String result = sendOpenApi(URL_GET_CD_QUERY, JSON.toJSONString(jsonMap));
            try {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("ok") && isNotEmpty(jsonObject.get("data"))) {
                    JSONArray jsonArray = JSON.parseArray(jsonObject.getString("data"));
                    JSONObject jo = (JSONObject) jsonArray.get(0);
                    // 不是删单也不是结关的，就过滤掉
//                    if (!"8".equals(jo.getString("cusDecStatus")) && !"10".equals(jo.getString("cusDecStatus"))) {
//                        log.info("报关单 {} 不是删单也不是结关的，过滤掉：{}", seqNo, jo.getString("cusDecStatus"));
//                        continue;
//                    }
                    try {
                        // 已结关或已删单的，就去调取详情接口去更新本地数据
                        //0716变更，客户总是说更新不及时，暂时更新全部吧
                        SyncDecHead sd = JSONObject.parseObject(jo.toString(), SyncDecHead.class);
                        currentProxy.processSyncDecHeadOne(sd.getCusCiqNo(), swid, tenantId, msg);
                    } catch (Exception e) {
                        log.error("处理单个报关单出现异常：{}", e.getMessage());
                    }
                } else {
                    log.info("返回错误信息了：：：{}", jsonObject.getString("errors"));
                }
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("获取报关单异常：{}", e.getMessage());
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.ok(msg.toString());
    }

    /**
     * 同步单个数据
     *
     * @param swid
     * @param msg
     * @return void
     * <AUTHOR>
     * @date 2024/10/30 16:04
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processSyncDecHeadOne(String cusCiqNo, String swid, String tenantId, StringBuilder msg) {
        try {
            // 设置忽略租户插件
            InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
            Map<String, Object> jm = new LinkedHashMap<>();
            jm.put("swid", swid);
            jm.put("cusCiqNo", cusCiqNo);
            String r = sendOpenApi(URL_GET_CD_DETAILS, JSON.toJSONString(jm));
            // 保存文件
            saveJsonToFile(r, "DEC", "SYNC_DEC_" + cusCiqNo);
            JSONObject ro = JSON.parseObject(r);
            if (isNotEmpty(ro.getBoolean("success")) && !ro.getBoolean("success")) {
                log.info("获取报关单详情失败：{}", ro.getString("message"));
                throw new RuntimeException(ro.getString("message"));
            }
            if (isNotEmpty(ro.getBoolean("ok")) && !ro.getBoolean("ok")) {
                log.info("获取报关单详情失败：{}", ro.getString("errors"));
                throw new RuntimeException(ro.getString("errors"));
            }
            DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
            JSONObject j = ro.getJSONObject("data");
            SyncDecHead syncDecHead = JSONObject.parseObject(j.toString(), SyncDecHead.class);
            SyncDecHead one = syncDecHeadMapper.selectOne(new LambdaQueryWrapper<SyncDecHead>()
                    .eq(SyncDecHead::getCusCiqNo, cusCiqNo)
                    .eq(SyncDecHead::getTenantId, tenantId));
            SyncDecHead insertDecHead = new SyncDecHead();
            if (isNotEmpty(one)) {
                BeanUtil.copyProperties(syncDecHead, one, CopyOptions.create().ignoreNullValue());
                insertDecHead = one;
                insertDecHead.setDecMergeListVo(syncDecHead.getDecMergeListVo());
            } else {
                BeanUtil.copyProperties(syncDecHead, insertDecHead, CopyOptions.create().ignoreNullValue());
            }
            insertDecHead.setTenantId(isNotEmpty(insertDecHead.getTenantId()) ? insertDecHead.getTenantId() : Long.valueOf(tenantId));
            // 1. 保存或更新同步报关单
            SyncDecHead ret = currentProxy.saveSyncDec(insertDecHead);
            // 转换报关单数据
//            List<DecHead> decHeadList = new ArrayList<>();
            // 2024/12/14 11:15@ZHANGCHAO 追加/变更/完善：可能有多个报关单？
//            List<DecHead> decHeads = baseMapper.selectList(new LambdaQueryWrapper<DecHead>()
//                    .eq(DecHead::getSeqNo, syncDecHead.getCusCiqNo())
//                    .eq(DecHead::getTenantId, insertDecHead.getTenantId()));
            Long tenantIdLong = isNotEmpty(insertDecHead.getTenantId()) ? insertDecHead.getTenantId() : ret.getTenantId();
            List<DecHead> decHeads = baseMapper.listDecHeadsByCond(syncDecHead.getCusCiqNo(), String.valueOf(tenantIdLong));
            if (isEmpty(decHeads)) {
                decHeads = new ArrayList<>();
                DecHead decHead = new DecHead();
                decHeads.add(decHead);
            } else {
                decHeads.forEach(decHead -> {
//                    List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
//                            .eq(DecList::getDecId, decHead.getId()));
                    List<DecList> decLists = decListMapper.listDecListsByCond(decHead.getId());
                    decHead.setDecLists(decLists);
                });
            }
            // 转换报关单数据
            for (DecHead decHead : decHeads) {
                setDecHeadFromJson(ret, decHead);
            }
            if (isNotEmpty(decHeads)) {
                Result<?> result = saveDecHeadBatch_(decHeads);
                msg.append(result.getMessage());
            }
//            decHeadList.add(decHead);
//            if (isNotEmpty(decHeadList)) {
//                DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
//                Result<?> result = currentProxy.saveDecHeadBatch_(decHeadList);
//                msg.append(result.getMessage());
//            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("获取报关单详情异常：{}", e.getMessage());
            msg.append("获取报关单详情异常：").append(e.getMessage());
            // 重新抛出异常，确保事务回滚
            throw new RuntimeException("处理报关单详情失败", e);
        } finally {
            // 关闭忽略策略
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }
    }

    /**
     * 保存单个同步报关单数据
     *
     * @param syncDecHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/30 16:20
     */
    @Transactional(rollbackFor = Exception.class)
    public SyncDecHead saveSyncDec(SyncDecHead syncDecHead) {
        if (isEmpty(syncDecHead)) {
            return new SyncDecHead();
        }
        // 新增
        if (isBlank(syncDecHead.getId())) {
            syncDecHead.setCreateBy("TASK");
            syncDecHead.setCreateDate(new Date());
            syncDecHeadMapper.insert(syncDecHead);
            if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
                syncDecHead.getDecMergeListVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecListService.saveBatch(syncDecHead.getDecMergeListVo());
            }
            // 集装箱
            if (isNotEmpty(syncDecHead.getPreDecContainerVo())) {
                syncDecHead.getPreDecContainerVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecContainerService.saveBatch(syncDecHead.getPreDecContainerVo());
            }
            // 随附单证
            if (isNotEmpty(syncDecHead.getCusLicenseListVo())) {
                syncDecHead.getCusLicenseListVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecLicenseDocusService.saveBatch(syncDecHead.getCusLicenseListVo());
            }
            // 随附单据
            if (isNotEmpty(syncDecHead.getPreDecDocVo())) {
                syncDecHead.getPreDecDocVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecDocService.saveBatch(syncDecHead.getPreDecDocVo());
            }
            // 编辑
        } else {
            syncDecHead.setUpdateBy("TASK");
            syncDecHead.setUpdateDate(new Date());
            syncDecHeadMapper.updateById(syncDecHead);
            syncDecListService.remove(new LambdaQueryWrapper<SyncDecList>()
                    .eq(SyncDecList::getHeadId, syncDecHead.getId()));
            syncDecContainerService.remove(new LambdaQueryWrapper<SyncDecContainer>()
                    .eq(SyncDecContainer::getHeadId, syncDecHead.getId()));
            syncDecLicenseDocusService.remove(new LambdaQueryWrapper<SyncDecLicenseDocus>()
                    .eq(SyncDecLicenseDocus::getHeadId, syncDecHead.getId()));
            syncDecDocService.remove(new LambdaQueryWrapper<SyncDecDoc>()
                    .eq(SyncDecDoc::getHeadId, syncDecHead.getId()));
            if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
                syncDecHead.getDecMergeListVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecListService.saveBatch(syncDecHead.getDecMergeListVo());
            }
            // 集装箱
            if (isNotEmpty(syncDecHead.getPreDecContainerVo())) {
                syncDecHead.getPreDecContainerVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecContainerService.saveBatch(syncDecHead.getPreDecContainerVo());
            }
            // 随附单证
            if (isNotEmpty(syncDecHead.getCusLicenseListVo())) {
                syncDecHead.getCusLicenseListVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecLicenseDocusService.saveBatch(syncDecHead.getCusLicenseListVo());
            }
            // 随附单据
            if (isNotEmpty(syncDecHead.getPreDecDocVo())) {
                syncDecHead.getPreDecDocVo().forEach(item -> {
                    item.setHeadId(syncDecHead.getId());
                    item.setTenantId(syncDecHead.getTenantId());
                    item.setCreateBy("TASK");
                    item.setCreateDate(new Date());
                });
                syncDecDocService.saveBatch(syncDecHead.getPreDecDocVo());
            }
        }
        SyncDecHead ret = syncDecHeadMapper.selectById(syncDecHead.getId());
        ret.setDecMergeListVo(syncDecListService.list(new LambdaQueryWrapper<SyncDecList>()
                .eq(SyncDecList::getHeadId, syncDecHead.getId())));
        ret.setPreDecContainerVo(syncDecContainerService.list(new LambdaQueryWrapper<SyncDecContainer>()
                .eq(SyncDecContainer::getHeadId, syncDecHead.getId())));
        ret.setCusLicenseListVo(syncDecLicenseDocusService.list(new LambdaQueryWrapper<SyncDecLicenseDocus>()
                .eq(SyncDecLicenseDocus::getHeadId, syncDecHead.getId())));
        ret.setPreDecDocVo(syncDecDocService.list(new LambdaQueryWrapper<SyncDecDoc>()
                .eq(SyncDecDoc::getHeadId, syncDecHead.getId())));
        return ret;
    }

    /**
     * 处理未同步报关单数据
     *
     * @param syncDecHeadList
     * @param msg
     * @return void
     * <AUTHOR>
     * @date 2024/10/30 09:40
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processSyncDecHeadList(List<SyncDecHead> syncDecHeadList, String swid, StringBuilder msg) {
        int chunkSize = 10;
        List<List<SyncDecHead>> syncDecHeadListList = CollUtil.split(syncDecHeadList, chunkSize);
        log.info("未同步的数据分片，每片{}条，共{}片", chunkSize, syncDecHeadListList.size());
        // 不用多线程了，树毛接口屁事太多，限制太多，很不稳定，还是慢慢来吧
//        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(syncDecHeadListList.size(), 10));
//        CountDownLatch countDownLatch = new CountDownLatch(syncDecHeadListList.size());
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
        for (List<SyncDecHead> syncDecHeads : syncDecHeadListList) {
//            executorService.submit(() -> {
                try {
//                    List<DecHead> decHeadList = new ArrayList<>();
                    for (SyncDecHead syncDecHead : syncDecHeads) {
                        try {
                            Map<String, Object> jm = new LinkedHashMap<>();
                            jm.put("swid", swid); // 博汇
                            jm.put("cusCiqNo", syncDecHead.getCusCiqNo());
                            // 等待直到获取到令牌
                            rateLimiter.acquire();
                            String r = sendOpenApi(URL_GET_CD_DETAILS, JSON.toJSONString(jm));
                            // 保存文件
                            saveJsonToFile(r, "DEC", "SYNC_DEC_" + syncDecHead.getCusCiqNo());
//                        log.info("【r】：{}", r);
                            JSONObject ro = JSON.parseObject(r);
                            if (isNotEmpty(ro.getBoolean("success")) && !ro.getBoolean("success")) {
                                log.info("获取报关单详情失败：{}", ro.getString("message"));
                                continue;
                            }
                            if (isNotEmpty(ro.getBoolean("ok")) && !ro.getBoolean("ok")) {
                                log.info("获取报关单详情失败：{}", ro.getString("errors"));
                                continue;
                            }
                            JSONObject j = ro.getJSONObject("data");
                            SyncDecHead syncDecHead_new = JSONObject.parseObject(j.toString(), SyncDecHead.class);
                            BeanUtil.copyProperties(syncDecHead_new, syncDecHead, CopyOptions.create().ignoreNullValue());
                            // 更新同步报关单表数据补充数据
                            currentProxy.updateSyncDec(syncDecHead);
                            // 2024/12/14 11:15@ZHANGCHAO 追加/变更/完善：可能有多个报关单？
//                            List<DecHead> decHeads = baseMapper.selectList(new LambdaQueryWrapper<DecHead>()
//                                    .eq(DecHead::getSeqNo, syncDecHead.getCusCiqNo())
//                                    .eq(DecHead::getTenantId, syncDecHead.getTenantId()));
                            List<DecHead> decHeads = baseMapper.listDecHeadsByCond(syncDecHead.getCusCiqNo(), String.valueOf(syncDecHead.getTenantId()));
                            if (isEmpty(decHeads)) {
                                decHeads = new ArrayList<>();
                                DecHead decHead = new DecHead();
                                decHeads.add(decHead);
                            } else {
                                decHeads.forEach(decHead -> {
//                                    List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
//                                            .eq(DecList::getDecId, decHead.getId()));
                                    List<DecList> decLists = decListMapper.listDecListsByCond(decHead.getId());
                                    decHead.setDecLists(decLists);
                                });
                            }
                            // 转换报关单数据
                            for (DecHead decHead : decHeads) {
                                setDecHeadFromJson(syncDecHead, decHead);
                            }
//                                Result<?> result = currentProxy.saveDecHeadBatch_(decHeads);
                            Result<?> result = saveDecHeadBatch_(decHeads); // todo! Lock wait timeout exceeded; try restarting transaction
                            msg.append(result.getMessage());
                        } catch (Exception e) {
                            ExceptionUtil.getFullStackTrace(e);
                            log.error("获取报关单详情异常：{}", e.getMessage());
                        }
//                        decHeadList.add(decHead);
                    }
                    // 2024/12/14 11:10@ZHANGCHAO 追加/变更/完善：改为一个一个的保存！！
//                    if (isNotEmpty(decHeadList)) {
//                        DecHeadServiceImpl currentProxy = (DecHeadServiceImpl) AopContext.currentProxy();
//                        Result<?> result = currentProxy.saveDecHeadBatch_(decHeadList);
//                        msg.append(result.getMessage());
//                    }
                } catch (Exception e) {
                    ExceptionUtil.getFullStackTrace(e);
                    log.error("获取报关单详情异常：{}", e.getMessage());
                } finally {
//                    countDownLatch.countDown();
                }
//            });
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
//        try {
//            countDownLatch.await(30, TimeUnit.SECONDS);
//        } catch (InterruptedException e) {
//            log.error(e.getMessage(), e);
//        } finally {
//            executorService.shutdown();
//        }
    }

    /**
     * 更新同步报关单表的数据
     *
     * @param syncDecHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/29 17:41
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSyncDec(SyncDecHead syncDecHead) {
        if (isBlank(syncDecHead.getId())) {
            return;
        }
        syncDecHead.setUpdateBy("TASK");
        syncDecHead.setUpdateDate(new Date());
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        syncDecHeadMapper.updateById(syncDecHead);
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        this.saveSyncList(syncDecHead);
    }

    /**
     * @param decHeadList
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Result<?> saveDecHeadBatch_(List<DecHead> decHeadList) {
        if (isEmpty(decHeadList)) {
            return Result.error("报关单数据为空！");
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<DecHead> addHeads = new ArrayList<>();
        List<DecHead> updateHeads = new ArrayList<>();
        List<DecList> addLists = new ArrayList<>();
        List<DecContainer> addContainers = new ArrayList<>();
        List<DecLicenseDocus> addLicenseDocuss = new ArrayList<>();
        List<DecAttachment> addDecAttachmentDocs = new ArrayList<>();
        List<DecEcoRelation> addDecEcoRelations = new ArrayList<>();
        List<String> headIdList = decHeadList.stream().map(DecHead::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (isNotEmpty(headIdList)) {
//            decListService.remove(new QueryWrapper<DecList>().lambda().in(DecList::getDecId, headIdList));
            decContainerService.remove(new QueryWrapper<DecContainer>().lambda().in(DecContainer::getDecId, headIdList));
            decLicenseDocusesService.remove(new QueryWrapper<DecLicenseDocus>().lambda().in(DecLicenseDocus::getDecId, headIdList));
            decAttachmentService.remove(new QueryWrapper<DecAttachment>().lambda().in(DecAttachment::getDclId, headIdList));
            decEcoRelationService.remove(new QueryWrapper<DecEcoRelation>().lambda().in(DecEcoRelation::getDecId, headIdList));
        }
        for (DecHead decHead : decHeadList) {
            if (isBlank(decHead.getId())) {
                decHead.setId(IdWorker.getIdStr());
                decHead.setCustomsCode("D" + decHead.getId());
                addHeads.add(decHead);
            } else {
                updateHeads.add(decHead);
            }
            if (isNotEmpty(decHead.getDecLists())) {
                List<Integer> number = new ArrayList<>();
                number.add(1);
                decHead.getDecLists().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setItem(isNotEmpty(v.getItem()) ? v.getItem() : number.get(0));
                    number.set(0, number.get(0)+1);
                    // 处理下协定享惠
                    if (isNotEmpty(v.getDecEcoRelation())) {
                        if (isBlank(v.getDecEcoRelation().getDecId())) {
                            v.getDecEcoRelation().setDecId(decHead.getId());
                        }
                        if (isBlank(v.getDecEcoRelation().getDecGno())) {
                            v.getDecEcoRelation().setDecGno(String.valueOf(v.getItem()));
                        }
                        addDecEcoRelations.add(v.getDecEcoRelation());
                    }
                });
                addLists.addAll(decHead.getDecLists());
            }
            if (isNotEmpty(decHead.getDecContainers())) {
                decHead.getDecContainers().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setId(IdWorker.getIdStr());
                });
                addContainers.addAll(decHead.getDecContainers());
            }
            if (isNotEmpty(decHead.getDecLicenseDocuses())) {
                decHead.getDecLicenseDocuses().forEach(v->{
                    v.setDecId(decHead.getId());
                    v.setId(IdWorker.getIdStr());
                });
                addLicenseDocuss.addAll(decHead.getDecLicenseDocuses());
            }
            if (isNotEmpty(decHead.getDecAttachments())) {
                decHead.getDecAttachments().forEach(v->{
                    v.setDclId(decHead.getId());
                    v.setId(IdWorker.getIdStr());
                });
                addDecAttachmentDocs.addAll(decHead.getDecAttachments());
            }
        }
        log.info("addHeads:{}", addHeads);
        if (isNotEmpty(addHeads)) {
            log.info("走插入报关单条数:{}", addHeads.size());
//            baseMapper.insertBatchSomeColumn(addHeads);
            for (DecHead addHead : addHeads) {
                int insert = baseMapper.insert(addHead);
                if (insert > 0) {
                    syncDecHeadMapper.update(null, new LambdaUpdateWrapper<SyncDecHead>()
                            .set(SyncDecHead::getIsSync, "1")
                            .eq(SyncDecHead::getId, addHead.getFlyId())
                            .eq(SyncDecHead::getTenantId, addHead.getTenantId()));
                }
            }
        }
        if (isNotEmpty(updateHeads)) {
            log.info("走编辑报关单条数:{}", updateHeads.size());
            // TODO: 2023/11/18 不支持批量编辑：sql injection violation, multi-statement not allow ！！待解决！！
//            baseMapper.updateBatchById(updateHeads);
//            this.updateBatchById(updateHeads);
            for (DecHead updateHead : updateHeads) {
                int update = baseMapper.updateById(updateHead);
                if (update > 0) {
                    syncDecHeadMapper.update(null, new LambdaUpdateWrapper<SyncDecHead>()
                            .set(SyncDecHead::getIsSync, "1")
                            .eq(SyncDecHead::getId, updateHead.getFlyId())
                            .eq(SyncDecHead::getTenantId, updateHead.getTenantId()));
                }
            }
        }
        if (isNotEmpty(addLists)) {
//            baseMapper.insertBatchLists(addLists);
            decListService.saveOrUpdateBatch(addLists);
        }
        if (isNotEmpty(addContainers)) {
            baseMapper.insertBatchContainers(addContainers);
        }
        if (isNotEmpty(addLicenseDocuss)) {
            baseMapper.insertBatchLicenseDocuses(addLicenseDocuss);
        }
        if (isNotEmpty(addDecAttachmentDocs)) {
            decAttachmentService.saveBatch(addDecAttachmentDocs);
        }
        if (isNotEmpty(addDecEcoRelations)) {
            decEcoRelationService.saveBatch(addDecEcoRelations);
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
        return Result.OK("插入报关单数：" + addHeads.size() + "；" + "更新报关单数：" + updateHeads.size());
    }

    /**
     * 保存同步报关单数据
     *
     * @param syncDecHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/28 16:22
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSyncList(SyncDecHead syncDecHead) {
        if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
            List<SyncDecList> syncDecListList = new ArrayList<>();
            syncDecHead.getDecMergeListVo().forEach(item -> {
                item.setHeadId(syncDecHead.getId());
                item.setTenantId(syncDecHead.getTenantId());
                item.setCreateBy("TASK");
                item.setCreateDate(new Date());
                syncDecListList.add(item);
            });
            if (isNotEmpty(syncDecListList)) {
                syncDecListService.remove(new QueryWrapper<SyncDecList>().lambda()
                        .eq(SyncDecList::getHeadId, syncDecHead.getId()));
                syncDecListService.saveBatch(syncDecListList);
            }
        }
        if (isNotEmpty(syncDecHead.getPreDecContainerVo())) {
            List<SyncDecContainer> syncDecListList = new ArrayList<>();
            syncDecHead.getPreDecContainerVo().forEach(item -> {
                item.setHeadId(syncDecHead.getId());
                item.setTenantId(syncDecHead.getTenantId());
                item.setCreateBy("TASK");
                item.setCreateDate(new Date());
                syncDecListList.add(item);
            });
            if (isNotEmpty(syncDecListList)) {
                syncDecContainerService.remove(new QueryWrapper<SyncDecContainer>().lambda()
                        .eq(SyncDecContainer::getHeadId, syncDecHead.getId()));
                syncDecContainerService.saveBatch(syncDecListList);
            }
        }
        if (isNotEmpty(syncDecHead.getCusLicenseListVo())) {
            List<SyncDecLicenseDocus> syncDecListList = new ArrayList<>();
            syncDecHead.getCusLicenseListVo().forEach(item -> {
                item.setHeadId(syncDecHead.getId());
                item.setTenantId(syncDecHead.getTenantId());
                item.setCreateBy("TASK");
                item.setCreateDate(new Date());
                syncDecListList.add(item);
            });
            if (isNotEmpty(syncDecListList)) {
                syncDecLicenseDocusService.remove(new QueryWrapper<SyncDecLicenseDocus>().lambda()
                        .eq(SyncDecLicenseDocus::getHeadId, syncDecHead.getId()));
                syncDecLicenseDocusService.saveBatch(syncDecListList);
            }
        }
        if (isNotEmpty(syncDecHead.getPreDecDocVo())) {
            List<SyncDecDoc> syncDecListList = new ArrayList<>();
            syncDecHead.getPreDecDocVo().forEach(item -> {
                item.setHeadId(syncDecHead.getId());
                item.setTenantId(syncDecHead.getTenantId());
                item.setCreateBy("TASK");
                item.setCreateDate(new Date());
                syncDecListList.add(item);
            });
            if (isNotEmpty(syncDecListList)) {
                syncDecDocService.remove(new QueryWrapper<SyncDecDoc>().lambda()
                        .eq(SyncDecDoc::getHeadId, syncDecHead.getId()));
                syncDecDocService.saveBatch(syncDecListList);
            }
        }
    }

    /**
     * 保存同步报关单数据
     *
     * @param syncDecHeads
     * @return void
     * <AUTHOR>
     * @date 2024/10/28 16:22
     */
    private void saveSyncDec(List<SyncDecHead> syncDecHeads) {
        if (isEmpty(syncDecHeads)) {
            return;
        }
        // 设置忽略租户插件
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
        List<SyncDecHead> syncDecHeadList = new ArrayList<>();
        syncDecHeads.forEach(syncDecHead -> {
            SyncDecHead one = syncDecHeadMapper.selectOne(new LambdaQueryWrapper<SyncDecHead>()
                    .eq(SyncDecHead::getCusCiqNo, syncDecHead.getCusCiqNo())
                    .eq(SyncDecHead::getTenantId, syncDecHead.getTenantId()));
            if (isEmpty(one)) {
                syncDecHead.setId(IdWorker.getIdStr());
                syncDecHead.setCreateBy("TASK");
                syncDecHead.setCreateDate(new Date());
                if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
                    syncDecHead.getDecMergeListVo().forEach(item -> {
                        item.setHeadId(syncDecHead.getId());
                        item.setTenantId(syncDecHead.getTenantId());
                        item.setCreateBy("TASK");
                        item.setCreateDate(new Date());
                    });
                }
                syncDecHeadList.add(syncDecHead);
            }
        });
        syncDecHeadService.saveBatch(syncDecHeadList);
        for (SyncDecHead syncDecHead : syncDecHeadList) {
            if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
                syncDecListService.saveBatch(syncDecHead.getDecMergeListVo());
            }
        }
        // 关闭忽略策略
        InterceptorIgnoreHelper.clearIgnoreStrategy();
    }

    /**
     * 转换报关单数据
     *
     * @param syncDecHead
     * @param decHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/28 14:33
     */
    private void setDecHeadFromJson(SyncDecHead syncDecHead, DecHead decHead) {
        if (null == syncDecHead) {
            return;
        }
//        DecHead decHead = baseMapper.selectOne(new LambdaQueryWrapper<DecHead>()
//                .eq(DecHead::getSeqNo, syncDecHead.getCusCiqNo()));
//        if (null == decHead) {
//            decHead = new DecHead();
//        } else {
//            List<DecList> decLists = decListMapper.selectList(new QueryWrapper<DecList>().lambda()
//                    .eq(DecList::getDecId, decHead.getId()));
//            decHead.setDecLists(decLists);
//        }
        decHead.setTenantId(syncDecHead.getTenantId());
        decHead.setDclTenantId(String.valueOf(syncDecHead.getTenantId()));
        // 报关单类型 0 – 一般报关单 1 – 转关提前报关单 2 – 备案清单 3 – 转关提前备案清单 4 – 出口二次转关
        decHead.setDclTrnRelFlag(syncDecHead.getDclTrnRelFlag());
        // 申报状态（1保存，2已申报，4海关入库成功，6退单，7审结，8删单，9放行，10结关，11查验通知，S公自用物品核准通过，T公自用物品退单，U公自用物品待核准）
        decHead.setDecStatus(syncDecHead.getCusDecStatus());
        decHead.setCustomsCode(syncDecHead.getCusCiqNo()); // 海关统一编号
        decHead.setSeqNo(syncDecHead.getCusCiqNo()); // 统一编号
        decHead.setClearanceNo(syncDecHead.getEntryId()); // 报关单号
        decHead.setRecordNumber(syncDecHead.getManualNo()); // 备案号
        decHead.setContract(syncDecHead.getContrNo()); // 合同协议号
        decHead.setIeFlag(syncDecHead.getCusIeFlag());
        decHead.setOutDate(syncDecHead.getIeDate());
        // 申报日期改为取dDate!!!!!
        decHead.setAppDate(isNotBlank(syncDecHead.getDDate()) ? DateUtil.parseDate(syncDecHead.getDDate()) : (isNotBlank(syncDecHead.getDeclDate()) ? DateUtil.parseDate(syncDecHead.getDeclDate()) : null));
        decHead.setShipTypeCode(syncDecHead.getCusTrafMode());
        decHead.setShipName(syncDecHead.getTrafName());
        decHead.setVoyage(syncDecHead.getCusVoyageNo());
        decHead.setBillCode(syncDecHead.getBillNo());
        decHead.setDeclarePlace(syncDecHead.getCustomMaster());
        decHead.setOutPortCode(syncDecHead.getIePort());
        decHead.setEntyPortCode(syncDecHead.getCiqEntyPortCode());
        if ("I".equals(decHead.getIeFlag())) {
            decHead.setOverseasConsignorCode(syncDecHead.getConsignorCode()); // 境外收发货人代码
            decHead.setOverseasConsignorEname(syncDecHead.getConsignorEname()); // 境外收发货人英文名称
            decHead.setOptUnitSocialCode(syncDecHead.getRcvgdTradeScc()); // 境内收发货人社会统一信用代码
            decHead.setOptUnitId(syncDecHead.getRcvgdTradeCode()); // 境内收发货人海关编码
            decHead.setTradeCiqCode(syncDecHead.getConsigneeCode()); // 境内收发货人检验检疫代码
            decHead.setOptUnitName(syncDecHead.getConsigneeCname()); // 境内收发货人名称

            decHead.setEntyPortCode(syncDecHead.getCiqEntyPortCode()); // 入境口岸
            decHead.setDespPortCode(syncDecHead.getDespPortCode()); // 启运港代码--进口
            decHead.setDesPort(syncDecHead.getDistinatePort()); // 经停港--进口
        } else {
            decHead.setOverseasConsigneeCode(syncDecHead.getConsigneeCode()); // 境外收发货人代码
            decHead.setOverseasConsigneeEname(syncDecHead.getConsigneeEname()); // 境外收发货人名称
            decHead.setOptUnitSocialCode(syncDecHead.getCnsnTradeScc()); // 境内收发货人社会统一信用代码
            decHead.setOptUnitId(syncDecHead.getCnsnTradeCode()); // 境内收发货人海关编码
            decHead.setTradeCiqCode(syncDecHead.getConsignorCode()); // 境内收发货人检验检疫代码
            decHead.setOptUnitName(syncDecHead.getConsignorCname()); // 境内收发货人名称

            decHead.setEntyPortCode(syncDecHead.getDespPortCode()); // 离境口岸
            decHead.setDesPort(syncDecHead.getDistinatePort()); // 指运港--出口
        }
        decHead.setDeliverUnitSocialCode(syncDecHead.getOwnerScc());
        decHead.setDeliverUnit(syncDecHead.getOwnerCode()); // 消费使用单位海关代码
        decHead.setOwnerCiqCode(syncDecHead.getOwnerCiqCode());
        decHead.setDeliverUnitName(syncDecHead.getOwnerName()); // 消费使用单位名称
        decHead.setDeclareUnitSocialCode(syncDecHead.getAgentScc()); // 申报单位社会统一信用代码
        decHead.setDeclareUnit(syncDecHead.getAgentCode()); // 申报单位海关代码
        decHead.setDeclCiqCode(syncDecHead.getDeclRegNo()); // 申报单位检验检疫编码
        decHead.setDeclareUnitName(syncDecHead.getAgentName()); // 申报单位名称
        decHead.setTradeCountry(syncDecHead.getCusTradeNationCode()); // 贸易国
        decHead.setArrivalArea(syncDecHead.getCusTradeCountry()); // 启运国
        decHead.setTermsTypeCode(syncDecHead.getTransMode()); // 成交方式
        decHead.setTradeTypeCode(syncDecHead.getSupvModeCdde()); // 监管方式
        decHead.setTaxTypeCode(syncDecHead.getCutMode()); // 征免性质
        decHead.setShipFeeCode(syncDecHead.getFeeMark()); // 运费代码
        decHead.setShipFee(isNotBlank(syncDecHead.getFeeRate()) ? new BigDecimal(syncDecHead.getFeeRate()) : null); // 运费值
        decHead.setShipCurrencyCode(syncDecHead.getFeeCurr()); // 运费币制
        decHead.setInsuranceCode(syncDecHead.getInsurMark()); // 保费代码
        decHead.setInsurance(isNotBlank(syncDecHead.getInsurRate()) ? new BigDecimal(syncDecHead.getInsurRate()) : null); // 保费值
        decHead.setInsuranceCurr(syncDecHead.getInsurCurr()); // 保费币制
        decHead.setExtrasCode(syncDecHead.getOtherMark()); // 杂费代码
        decHead.setExtras(isNotBlank(syncDecHead.getOtherRate()) ? new BigDecimal(syncDecHead.getOtherRate()) : null); // 杂费值
        decHead.setOtherCurr(syncDecHead.getOtherCurr()); // 杂费币制
        decHead.setPacks(isNotBlank(syncDecHead.getPackNo()) ? Integer.valueOf(syncDecHead.getPackNo()) : null); // 件数
        decHead.setGrossWeight(isNotBlank(syncDecHead.getGrossWt()) ? new BigDecimal(syncDecHead.getGrossWt()) : null); // 毛重
        decHead.setNetWeight(isNotBlank(syncDecHead.getNetWt()) ? new BigDecimal(syncDecHead.getNetWt()) : null); // 净重
        decHead.setLicenceNumber(syncDecHead.getLicenseNo()); // 许可证号
        decHead.setPacksKinds(syncDecHead.getWrapType()); // 包装种类
        decHead.setPackType(syncDecHead.getDecOtherPacksVo()); // 其他包装
        if (isNotBlank(syncDecHead.getDecOtherPacksVo())) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(syncDecHead.getDecOtherPacksVo());
                List<Map<String, String>> strList = JSONArray.parseObject(jsonArray.toString(), List.class);
                String packType = "";
                for (Map<String, String> map : strList) {
                    for (Map.Entry<String, String> entryMap : map.entrySet()) {
                        if ("packType".equals(entryMap.getKey())) {
                            if ("".equals(packType)) {
                                packType = entryMap.getValue();
                            } else {
                                packType = new StringBuffer().append(packType).append(',').append(entryMap.getValue()).toString();
                            }
                        }
                    }
                }
                decHead.setPackType(packType);
            } catch (Exception e) {
                log.error("处理[其他包装]出现异常：{}", e.getMessage());
            }
        }
        decHead.setContainerNum(syncDecHead.getContaCount()); // 集装箱数
        decHead.setClearanceType(syncDecHead.getEntryType()); // 报关单类型
        decHead.setListType("");
        decHead.setGoodsPlace(syncDecHead.getGoodsPlace()); // 货物存放地点
        decHead.setContractAtt(syncDecHead.getAttaDocuCdstr()); // ？随附单证
        decHead.setMarkNumber(syncDecHead.getNoteS());
        decHead.setMarkNo(syncDecHead.getMarkNo()); // 标记唛码
        decHead.setSuretyFlag("");
        decHead.setChkSurety(false);
        // 业务事项(单据类型) TODO:有问题,根据报关单类型判断
        String decType = "";
        Boolean chkSurety = false;
        String value = syncDecHead.getCusRemark();
        if (isNotBlank(value)) {
            if (value.toString().length() < 12) {
                value = value + String.format(
                        new StringBuffer("%0").append(12 - value.toString().length()).append("d").toString(), 0);
            }
            if ("1".equals(value.toString().substring(0, 1))) {
                decType = "Z ";// 自报自缴
            } else if ("1".equals(value.toString().substring(1, 2))) {
                decType = "CL";// 汇总征税
            } else if ("1".equals(value.toString().substring(3, 4))) {
                decType = "SZ";// 水运中转一般报关单
                if ("2".equals(syncDecHead.getDclTrnRelFlag())) {
                    decType = "SM";// 水运中转保税区进出境备案清单
                }
            } else if ("1".equals(value.toString().substring(5, 6))) {
                chkSurety = true;// 担保验放
            } else if ("1".equals(value.toString().substring(11, 12))) {
                // decType = decType + "CL";//跨境电商海外仓
            }
            if ("11".equals(value.toString().subSequence(0, 2))) {
                decType = "ZC";// 自报自缴，汇总征税报关单
            }
            decHead.setDecType(decType);
            decHead.setChkSurety(chkSurety);// 担保验放
        }
        try {
            StringBuilder promiseItmes = new StringBuilder();
            if (isNotBlank(syncDecHead.getPromiseItems1())) {
                promiseItmes.append(syncDecHead.getPromiseItems1()).append("|");
            }
            if (isNotBlank(syncDecHead.getPromiseItems2())) {
                promiseItmes.append(syncDecHead.getPromiseItems2()).append("|");
            }
            if (isNotBlank(syncDecHead.getPromiseItems3())) {
                promiseItmes.append(syncDecHead.getPromiseItems3()).append("|");
            }
            if (isNotBlank(syncDecHead.getPromiseItems4())) {
                promiseItmes.append(syncDecHead.getPromiseItems4()).append("|");
            }
            if (isNotBlank(syncDecHead.getPromiseItems5())) {
                promiseItmes.append(syncDecHead.getPromiseItems5()).append("|");
            }
            if (isNotBlank(syncDecHead.getPromiseItems6())) {
                promiseItmes.append(syncDecHead.getPromiseItems6());
            }
            log.info("promiseItmes:{}", promiseItmes);
            decHead.setPromiseItmes(String.valueOf(promiseItmes)); // 特殊关系/价格说明
        } catch (Exception e) {
            log.error("处理特殊关系/价格说明出现异常：" + e.getMessage());
        }
        decHead.setBillType(syncDecHead.getBillType()); // 备案清单类型
        decHead.setRelId(syncDecHead.getRelativeId()); // 关联报关单号
        decHead.setRelManNo(syncDecHead.getRelmanNo()); // 关联备案号
        decHead.setBonNo(syncDecHead.getBonNo()); // 保税监管场地
        decHead.setCusFie(syncDecHead.getCustomsField()); // 场地代码
        decHead.setVolume(new BigDecimal("0"));
        decHead.setTotal(new BigDecimal("0"));
        decHead.setCurrency("");
        decHead.setGoodsCount(new BigDecimal("0"));
        decHead.setOrgCode(syncDecHead.getOrgCode()); // 检验检疫受理机关 商检信息
        decHead.setInspOrgCode(syncDecHead.getInspOrgCode()); // 口岸检验检疫机关 商检信息
        decHead.setPurpOrgCode(syncDecHead.getPurpOrgCode()); // 目的地检验检疫机关 商检信息
        decHead.setVsaOrgCode(syncDecHead.getVsaOrgCode()); // 领证机关 商检信息
        decHead.setBlNo(syncDecHead.getCiqBillNo()); // B/LNO 商检信息
        try {
            StringBuilder specDeclFlag = new StringBuilder();
            if (isNotBlank(syncDecHead.getSpecDeclFlag1())) {
                specDeclFlag.append(syncDecHead.getPromiseItems1());
            }
            if (isNotBlank(syncDecHead.getPromiseItems2())) {
                specDeclFlag.append(syncDecHead.getPromiseItems2());
            }
            if (isNotBlank(syncDecHead.getPromiseItems3())) {
                specDeclFlag.append(syncDecHead.getPromiseItems3());
            }
            if (isNotBlank(syncDecHead.getPromiseItems4())) {
                specDeclFlag.append(syncDecHead.getPromiseItems4());
            }
            log.info("specDeclFlag:{}", specDeclFlag);
            decHead.setSpecDeclFlag(specDeclFlag.toString()); // 特殊业务标识 商检信息
        } catch (Exception e) {
            log.error("处理特殊业务标识出现异常：{}", e.getMessage());
        }
        decHead.setDespDate(syncDecHead.getDespDate()); // 启运日期 格式为：yyyyMMdd 商检信息
        decHead.setCmplDschrgDt(syncDecHead.getCmplDschrgDt()); // 卸毕日期 格式为：yyyyMMdd ？？？？？？
        decHead.setCorrelationNo(syncDecHead.getCorrelationDeclNo()); // 关联号码 商检信息
        decHead.setCorrelationReasonFlag(syncDecHead.getCorrelationReasonFlag()); // 关联理由 商检信息
        decHead.setOrigBoxFlag(syncDecHead.getOrigBoxFlag()); // 原集装箱标识 商检信息
        // 企业资质信息 商检信息[{"EntQualifNo":"123","EntQualifTypeCode":"456"}]
        if (isNotBlank(syncDecHead.getPreDecEntQualifListVo())) {
            String coplimittype = syncDecHead.getPreDecEntQualifListVo()
                    .replaceAll("entQualifSeqNo", "EntQualifSeqNo")
                    .replaceAll("entOrgCode", "EntOrgCode")
                    .replaceAll("entName", "EntName")
                    .replaceAll("entQualifTypeCode", "EntQualifTypeCode")
                    .replaceAll("entQualifTypeCodeName", "EntQualifTypeCodeName")
                    .replaceAll("entQualifNo", "EntQualifNo")
                    .replaceAll("entQualifTypeName", "EntQualifTypeName");
            decHead.setCopLimitType(coplimittype);
        }
        // 使用人信息表 商检信息[{"UseOrgPersonCode":"123","UseOrgPersonTel":"456"}]
        if (isNotBlank(syncDecHead.getPreDecUserList())) {
            String decuserlist = syncDecHead.getPreDecUserList()
                    .replaceAll("consumerUsrSeqNo", "ConsumerUsrSeqNo")
                    .replaceAll("consumerUsrCode", "ConsumerUsrCode")
                    .replaceAll("userName", "UserName")
                    .replaceAll("useOrgPersonCode", "UseOrgPersonCode")
                    .replaceAll("useOrgPersonTel", "UseOrgPersonTel");
            decHead.setDecUserType(decuserlist);
        }
        // 检验检疫签证申报要素 商检信息[{"AppCertCode":"123","ApplOri","456","ApplCopyQuan":"789"}]
        if (isNotBlank(syncDecHead.getPreDecRequCertList())) {
            String requcertlist = syncDecHead.getPreDecRequCertList()
                    .replaceAll("requCertSeqNo", "RequCertSeqNo")
                    .replaceAll("appCertName", "AppCertName")
                    .replaceAll("appCertCode", "AppCertCode")
                    .replaceAll("applOri", "ApplOri")
                    .replaceAll("applCopyQuan", "ApplCopyQuan");
            decHead.setRequestCertType(requcertlist);
        }
//        decHead.setNoOtherPack("");
        decHead.setIcNumber(syncDecHead.getTypistNo()); // Ic卡号
        decHead.setDeclarantNo(syncDecHead.getDclrNo()); // 报关人员证号
        if (isNotBlank(syncDecHead.getCusDecStatus())) {
            Integer status = null;
            if ("10".equals(syncDecHead.getCusDecStatus())){
                status = 49;
            }else if ("1".equals(syncDecHead.getCusDecStatus())){
                status = 10;
            }else if ("2".equals(syncDecHead.getCusDecStatus()) || "4".equals(syncDecHead.getCusDecStatus())){
                status = 44;
            }else if ("9".equals(syncDecHead.getCusDecStatus()) || "7".equals(syncDecHead.getCusDecStatus())){
                status = 48;
            }
            decHead.setStatus(status);
        }
//        decHead.setTranferType("");
        try {
            StringBuilder clearanceMode = new StringBuilder();
            if (isNotBlank(syncDecHead.getSpecPassFlag1())) {
                clearanceMode.append(syncDecHead.getPromiseItems1());
            }
            if (isNotBlank(syncDecHead.getSpecPassFlag2())) {
                clearanceMode.append(syncDecHead.getPromiseItems2());
            }
            if (isNotBlank(syncDecHead.getSpecPassFlag3())) {
                clearanceMode.append(syncDecHead.getPromiseItems3());
            }
            if (isNotBlank(syncDecHead.getSpecPassFlag4())) {
                clearanceMode.append(syncDecHead.getPromiseItems4());
            }
            log.info("clearanceMode:{}", clearanceMode);
            decHead.setClearanceMode(String.valueOf(clearanceMode)); // 通关模式 报文用
        } catch (Exception e) {
            log.error("处理通关模式出现异常：{}", e.getMessage());
        }
//        decHead.setEdiId(syncDecHead.getEdiId()); // 报关标志 1：普通报关 3：北方转关提前 5：南方转关提前 6：普通报关，运输工具名称以‘◎’开头，南方H2000直转 报文用
//        decHead.setAudited(isNotEmpty(syncDecHead.getIsAudit()) && 1 == syncDecHead.getIsAudit()); // 是否审核
        decHead.setInputId(decHead.getDclTenantId());
        Date date;
        try {
            date = isNotBlank(syncDecHead.getUpdateTime()) ? DateUtil.parse(syncDecHead.getUpdateTime(), DatePattern.NORM_DATE_PATTERN) : new Date();
        } catch (Exception e) {
            try {
                date = isNotBlank(syncDecHead.getUpdateTime()) ? DateUtil.parse(syncDecHead.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) : new Date();
            } catch (Exception ex) {
                date = new Date();
            }
        }

        if (isNotBlank(decHead.getId())) {
            decHead.setUpdateBy("admin");
            decHead.setUpdateDate(date);
        } else {
            decHead.setCreatePerson(isNotBlank(syncDecHead.getInputErName()) ? syncDecHead.getInputErName() : "admin");
            decHead.setInputId(decHead.getDclTenantId());
            decHead.setCreateTime(date);
        }

        decHead.setSend("0");
        decHead.setPushStatus("1");
        decHead.setFlyId(syncDecHead.getId());
        decHead.setTenantName("");
        decHead.setDownLinkMark(true);
        decHead.setSynchronism(false);
        decHead.setSynchronismDate(new Date());
        decHead.setInputErName(syncDecHead.getInputErName()); // 录入人

        /*
         * 转换报关单表体
         */
        setDecListFromSync(decHead, syncDecHead);
        /*
         * 转换报关单集装箱
         */
        setDecContainerFromSync(decHead, syncDecHead);
        /*
         * 转换报关单随附单证
         */
        setDecLicenseDocusFromSync(decHead, syncDecHead);
        /*
         * 转换报关单随附单据
         */
        setDecDocvoFromSync(decHead, syncDecHead);

        decHead.setCurrency(isNotEmpty(decHead.getDecLists())?decHead.getDecLists().get(0).getCurrencyCode():null);
    }

    /**
     * 转换随附单据
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2024/11/8 17:00
     */
    private void setDecDocvoFromSync(DecHead decHead, SyncDecHead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getPreDecDocVo())) {
            List<DecAttachment> decAttachmentList = new ArrayList<>(16);
            for (SyncDecDoc jgVFlyBgDecdocvo : jgVFlyBgDechead.getPreDecDocVo()) {
                DecAttachment decAttach = new DecAttachment();
                decAttach.setAttachmentName(jgVFlyBgDecdocvo.getEntOrigFileName());
                decAttach.setAttachmentNo(jgVFlyBgDecdocvo.getAttEdocNo());
                decAttach.setAttachmentType(jgVFlyBgDecdocvo.getAttTypeCode());
//                decAttach.setRelatedId(jgVFlyBgDecdocvo.getCusCiqNo());
//                decAttach.setEdocFomatType(jgVFlyBgDecdocvo.getAttFmtTypeCode());
//                decAttach.setRemark(jgVFlyBgDecdocvo.getBelongWkunitCode());
                try {
                    decAttach.setUploadTime(isNotBlank(jgVFlyBgDecdocvo.getUpdateTime()) ? DateUtil.parse(jgVFlyBgDecdocvo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss") : null);
                } catch (Exception e) {
                    log.error("转换上传日期出现异常：" + e.getMessage());
                }
//                decAttach.setUser(jgVFlyBgDecdocvo.getUpdateUser());
                decAttach.setGNoStr(jgVFlyBgDecdocvo.getGNoStr());
                decAttach.setDclType("DEC");
                if(decAttach.getAttachmentName()!=null) {
                    decAttach.setSuffix(decAttach.getAttachmentName().replaceFirst(".*\\.(.+)$","$1"));
                }
                decAttachmentList.add(decAttach);
            }
            decHead.setDecAttachments(decAttachmentList);
        }
    }

    /**
     * 转换随附单证
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2024/11/8 16:58
     */
    private void setDecLicenseDocusFromSync(DecHead decHead, SyncDecHead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getCusLicenseListVo())) {
            List<DecLicenseDocus> decLicenseDocusList = new ArrayList<>(16);
            for (SyncDecLicenseDocus jgVFlyBgDeclicense : jgVFlyBgDechead.getCusLicenseListVo()) {
                DecLicenseDocus decLicenseDocus = new DecLicenseDocus();
                decLicenseDocus.setDecId(decHead.getId());
                decLicenseDocus.setCustomsCode(decHead.getCustomsCode());
                decLicenseDocus.setDocuCode(jgVFlyBgDeclicense.getAcmpFormCode());
                decLicenseDocus.setCertCode(jgVFlyBgDeclicense.getAcmpFormNo());
                decLicenseDocusList.add(decLicenseDocus);
            }
            decHead.setDecLicenseDocuses(decLicenseDocusList);
        }
    }

    /**
     * 转换集装箱
     *
     * @param decHead
     * @param jgVFlyBgDechead
     * @return void
     * <AUTHOR>
     * @date 2024/11/8 16:54
     */
    private void setDecContainerFromSync(DecHead decHead, SyncDecHead jgVFlyBgDechead) {
        if (isNotEmpty(jgVFlyBgDechead.getPreDecContainerVo())) {
            List<DecContainer> decContainerList = new ArrayList<>(16);
            for (SyncDecContainer jgVFlyBgDeccontainer : jgVFlyBgDechead.getPreDecContainerVo()) {
                DecContainer decContainer = new DecContainer();
                decContainer.setDecId(decHead.getId());
                decContainer.setCustomsCode(decHead.getCustomsCode());
                decContainer.setContainerId(jgVFlyBgDeccontainer.getContainerNo());
                decContainer.setContainerMd(jgVFlyBgDeccontainer.getContainerMdCode());
                decContainer.setGoodsNo(jgVFlyBgDeccontainer.getGoodsNo());
                decContainer.setLclFlag(jgVFlyBgDeccontainer.getLclFlag());
                decContainer.setGoodsContaWt(isNotBlank(jgVFlyBgDeccontainer.getContainerWt()) ? new BigDecimal(jgVFlyBgDeccontainer.getContainerWt()) : null);
                decContainerList.add(decContainer);
            }
            decHead.setDecContainers(decContainerList);
        }
    }

    /**
     * 转换报关单表体
     *
     * @param decHead
     * @param syncDecHead
     * @return void
     * <AUTHOR>
     * @date 2024/10/29 10:45
     */
    private void setDecListFromSync(DecHead decHead, SyncDecHead syncDecHead) {
        if (isNotEmpty(syncDecHead.getDecMergeListVo())) {
            Map<String, DecList> decListMap = new HashMap<>(16);
            if (isNotEmpty(decHead.getId())) {
                List<DecList> decLists = decHead.getDecLists();
                if (isNotEmpty(decLists)) {
                    for (DecList decList : decLists) {
                        decListMap.put(decList.getDecId() + "|" + decList.getItem(), decList);
                    }
                }
            }
            List<DecList> decListList = new ArrayList<>(16);
            for (SyncDecList jgVFlyBgDeclist : syncDecHead.getDecMergeListVo()) {
                DecList decList = isNotEmpty(decListMap.get(decHead.getId()+"|"+jgVFlyBgDeclist.getGNo())) ? decListMap.get(decHead.getId()+"|"+jgVFlyBgDeclist.getGNo()) : new DecList();
                String origId = decList.getId();
                BeanUtil.copyProperties(jgVFlyBgDeclist, decList, CopyOptions.create().ignoreNullValue());
                decList.setId(origId);
                decList.setDecId(isNotEmpty(decList.getDecId()) ? decList.getDecId() : decHead.getId());
                decList.setCustomsCode(decHead.getCustomsCode());
                decList.setItem(isNotBlank(jgVFlyBgDeclist.getGNo()) ? Integer.valueOf(jgVFlyBgDeclist.getGNo()) : null); // 项号
                decList.setRecordItem(isNotBlank(jgVFlyBgDeclist.getContrItem()) ? Integer.valueOf(jgVFlyBgDeclist.getContrItem()) : null); // 备案序号
                decList.setHscode(jgVFlyBgDeclist.getCodeTs()); // 商品编码 税号
                decList.setHsname(jgVFlyBgDeclist.getGName()); // 商品名称 申报品名
                decList.setHsmodel(jgVFlyBgDeclist.getGModel()); // 规格型号 申报要素
                decList.setGoodsCount(isNotBlank(jgVFlyBgDeclist.getGQty()) ? new BigDecimal(jgVFlyBgDeclist.getGQty()) : null); // 成交数量
                decList.setUnitCode(jgVFlyBgDeclist.getGUnit()); // 成交单位
                decList.setPrice(isNotBlank(jgVFlyBgDeclist.getDeclPrice()) ? new BigDecimal(jgVFlyBgDeclist.getDeclPrice()) : null); // 单价
                decList.setTotal(isNotBlank(jgVFlyBgDeclist.getDeclTotal()) ? new BigDecimal(jgVFlyBgDeclist.getDeclTotal()) : null); // 总价
                decList.setCurrencyCode(jgVFlyBgDeclist.getTradeCurr()); // 币制
                decList.setFaxTypeCode(jgVFlyBgDeclist.getDutyMode()); // 征免方式
                decList.setUnit1(jgVFlyBgDeclist.getUnit1()); // 法定单位
                decList.setCount1(isNotBlank(jgVFlyBgDeclist.getQty1()) ? new BigDecimal(jgVFlyBgDeclist.getQty1()) : null); // 法定数量
                decList.setUnit2(jgVFlyBgDeclist.getUnit2()); // 第二法定单位
                decList.setCount2(isNotBlank(jgVFlyBgDeclist.getQty2()) ? new BigDecimal(jgVFlyBgDeclist.getQty2()) : null); // 第二法定数量
                // cusOriginCountry": "CHN", //原产国地区（进口）
                // "destinationCountry": "CHN", //最终目的国地区（进口）
//                if (I.equals(decList.getIeFlag())) {
                    decList.setDestinationCountry(jgVFlyBgDeclist.getDestinationCountry()); // 最终目的国
                    decList.setDesCountry(jgVFlyBgDeclist.getCusOriginCountry()); // 原产国
//                } else {
//                    decList.setDestinationCountry(jgVFlyBgDeclist.getCusOriginCountry()); // 最终目的国
//                    decList.setDesCountry(jgVFlyBgDeclist.getDestinationCountry()); // 原产国
//                }

                // 协定享惠
                if (isNotBlank(jgVFlyBgDeclist.getPreDecCiqXiangHui())) {
                    JSONObject o = JSONObject.parseObject(jgVFlyBgDeclist.getPreDecCiqXiangHui());
                    if (isNotEmpty(o)) {
                        DecEcoRelation decEcoRelation = new DecEcoRelation();
                        decEcoRelation.setDecId(decHead.getId());
                        decEcoRelation.setDecGno(decList.getItem().toString());
                        // 原产地证明编号
                        decEcoRelation.setEcoCertNo(isNotEmpty(o.get("certOriCode")) ? o.get("certOriCode").toString() : null);
                        // 优惠贸易协定代码
                        decEcoRelation.setCertType(isNotEmpty(o.get("preTradeAgreeCode")) ? o.get("preTradeAgreeCode").toString() : null);
                        // 优惠贸易协定项下原产地
                        decList.setRcepOrigPlaceCode(isNotEmpty(o.get("rcepOrigPlaceDocCode")) ? o.get("rcepOrigPlaceDocCode").toString() : null);
                        // 原产地证明商品项号
                        decEcoRelation.setEcoGno(isNotEmpty(o.get("certOriModItemNum")) ? o.get("certOriModItemNum").toString() : null);
                        // 原产地证明类型
                        decEcoRelation.setEcoCertCode(isNotEmpty(o.get("oriCertType")) ? o.get("oriCertType").toString() : null);
                        decEcoRelation.setCertCode("Y");
                        // 判断decEcoRelation里所有属性除了certCode是否为空，为空则不保存
                        if (areAllFieldsNotNullExceptCertCode(decEcoRelation)) {
                            decList.setDecEcoRelation(decEcoRelation);
                        }
                    }
                }
                decList.setOrigPlaceCode(jgVFlyBgDeclist.getOrigPlaceCode()); // 原产地区代码
                decList.setDestCode(jgVFlyBgDeclist.getCiqDestCode()); // 目的地代码 境内目的地/境内货源地辅助字段
                decList.setDistrictCode(jgVFlyBgDeclist.getDistrictCode()); // 境内目的地/境内货源地
                decList.setGoodsAttr(jgVFlyBgDeclist.getGoodsAttr()); // 货物属性代码
                decList.setPurpose(jgVFlyBgDeclist.getPurpose()); // 用途代码
                decList.setGoodsLimitType(jgVFlyBgDeclist.getPreDecCiqGoodsLimit()); // 许可证信息
                if (isNotBlank(jgVFlyBgDeclist.getPreDecCiqGoodsLimit())) {
                    String decRequCertList = jgVFlyBgDeclist.getPreDecCiqGoodsLimit()
                            .replaceAll("licenceNo", "LicenceNo")
                            .replaceAll("licTypeCode", "LicTypeCode")
                            .replaceAll("licWrtofDetailNo", "LicWrtofDetailNo")
                            .replaceAll("licWrtofQty", "LicWrtofQty")
                            .replaceAll("licWrtofQtyUnit", "LicWrtofQtyUnit")
                            .replaceAll("licTypeName", "LicTypeName");
                    decList.setGoodsLimitType(decRequCertList);
                }

                // 检验检疫货物规格
                if (isNotBlank(jgVFlyBgDeclist.getGoodsTargetInput())) {
                    try {
                        JSONObject o = JSONObject.parseObject(jgVFlyBgDeclist.getGoodsTargetInput());
                        if (isNotEmpty(o)) {
                            // 成分/原料/组分
                            decList.setStuff(isNotEmpty(o.get("stuff")) ? o.get("stuff").toString() : null);
                            // 产品有效期
                            decList.setProdValidDt(isNotEmpty(o.get("prodValidDt")) ? o.get("prodValidDt").toString() : null);
                            // 产品保质期（天）
                            decList.setProdQgp(isNotEmpty(o.get("prodQgp")) ? o.get("prodQgp").toString() : null);
                            // 生产单位名称 检验检疫货物规格
                            decList.setMnufctrRegName(isNotEmpty(o.get("engManEntCnm")) ? o.get("engManEntCnm").toString() : null);
                            // 境外生产企业名称 检验检疫货物规格
                            decList.setEngManEntCnm(isNotEmpty(o.get("engManEntCnm")) ? o.get("engManEntCnm").toString() : null);
                            // 货物规格
                            decList.setGoodsSpec(isNotEmpty(o.get("goodsSpec")) ? o.get("goodsSpec").toString() : null);
                            // 货物型号
                            decList.setGoodsModel(isNotEmpty(o.get("goodsModel")) ? o.get("goodsModel").toString() : null);
                            // 货物品牌
                            decList.setGoodsBrand(isNotEmpty(o.get("goodsBrand")) ? o.get("goodsBrand").toString() : null);
                            // 生产批次
                            decList.setProdBatchNo(isNotEmpty(o.get("prodBatchNo")) ? o.get("prodBatchNo").toString() : null);

                        }
                    } catch (Exception e) {
                        log.error("检验检疫货物规格goodsTargetInput解析异常", e.getMessage());
                    }
                }
                if (isNotBlank(jgVFlyBgDeclist.getDangerInfo())) {
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(jgVFlyBgDeclist.getDangerInfo());
                        decList.setNoDangFlag(jsonObject.getString("noDangFlag")); // 非危险化学品 危险货物信息
                        decList.setUncode(jsonObject.getString("unCode")); // UN编码 危险货物信息
                        decList.setDangName(jsonObject.getString("dangName")); // 危险货物名称 危险货物信息
                        decList.setDangPackType(jsonObject.getString("packType")); // 危包类别 危险货物信息
                        decList.setDangPackSpec(jsonObject.getString("packSpec")); // 危包规格 危险货物信息
                    } catch (Exception e) {
                        log.error("解析危险货物信息异常", e.getMessage());
                    }
                }

                decList.setSupvModecd(jgVFlyBgDeclist.getCusSupvDmd()); // 监管条件 系统用
//                decList.setNetWeight(isNotBlank(jgVFlyBgDeclist.getCiqWeight()) ? new BigDecimal(jgVFlyBgDeclist.getCiqWeight()) : null); // 净重 系统用
//                decList.setDgFlag("Y".equals(jgVFlyBgDeclist.getDangerFlag())); // 危险品标志
                // 2023/7/20 16:31@ZHANGCHAO 追加/变更/完善：检验检疫名称
                decList.setCiqCode(jgVFlyBgDeclist.getCiqCode());
                decList.setCiqName(jgVFlyBgDeclist.getCiqName());
                decListList.add(decList);
            }
            decHead.setDecLists(decListList);
        }
    }

    private boolean areAllFieldsNotNullExceptCertCode(DecEcoRelation decEcoRelation) {
        return isNotBlank(decEcoRelation.getEcoCertNo()) &&
                isNotBlank(decEcoRelation.getCertType()) &&
                isNotBlank(decEcoRelation.getEcoGno()) &&
                isNotBlank(decEcoRelation.getEcoCertCode());
    }

    private List<WeekGroup> getWeekGroups(Date startDate, Date endDate) {
        List<WeekGroup> weekGroups = new ArrayList<>();

        // 如果开始日期和结束日期是同一天，直接创建一个组
        if (isSameDay(startDate, endDate)) {
            weekGroups.add(new WeekGroup(startDate, endDate));
            return weekGroups;
        }

        // 如果结束日期晚于今天，则用今天替换结束日期
        Date today = new Date(); // 获取当前日期
        if (endDate.after(today)) {
            endDate = today;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);

        // 设置时分秒为0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        while (calendar.getTime().before(endDate)) {
            // 获取这一周的开始日期
            Date groupStart = calendar.getTime();

            // 计算这一周的结束日期
            calendar.add(Calendar.DATE, 6);

            // 如果超过了结束日期，就使用结束日期
            Date groupEnd;
            if (calendar.getTime().after(endDate)) {
                groupEnd = endDate;
            } else {
                groupEnd = calendar.getTime();
            }

            // 添加到分组列表
            weekGroups.add(new WeekGroup(groupStart, groupEnd));

            // 移动到下一周的开始
            calendar.add(Calendar.DATE, 1);
        }

        return weekGroups;
    }

    @Override
    public Result<?> Ydt_batchExtractReportKey(String goods) {
        Result<String> result =new Result<>();
        Map<String, Object> param=new HashMap<>();
        param.put("goods",goods);
        String resp= HttpUtil.post("0",param);
        result.setResult(resp);
        return result;
    }
    @Override
    public Result<?> Ydt_batchQueryGoodsList(String goods) {
        Result<String> result =new Result<>();
        Map<String, Object> param=new HashMap<>();
        param.put("goods",goods);
        // 创建请求头
        Map<String, String> headers = new HashMap<>();
        // 添加自定义的请求头
        headers.put("Token", "Hz9/k8KP8n42YuKPwkd0k1KG8fI94IHPezW/L9xFn3Qm6Wbp7Izn5Z9qNGqN+HtTruuP5yNMZWIrxMAmIVhNWRCeQjnYUGfkV2tJsKqh5wWPKqhKuygz81BJse8JJPR3NwAWui14pRDlEO4u1xVD7Um9LvGoR1lowyo+D7XXPK8=");
        // 发起POST请求，同时传入请求头
        String resp = HttpUtil.createPost("http://auth.feiliankeji.cn/Fly_BgApi/Ydt_batchQueryGoodsList")
                .addHeaders(headers)
                .form("goodsHsCodes", goods)
                .execute()
                .body();
        result.setResult(resp);
        return result;
    }

    @Override
    public String Ydt_validate(String param){
        //获取请求接口的token
        SysConfig sysConfig=sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey,"DEC_LOGIC_VERIFICATION"));
        //如果数据库没有这个配置
        if(isEmpty(sysConfig)){
            sysConfig=new SysConfig();
            String token = loginInvoiceApi();
            sysConfig.setConfigValue(token);
        }
//        String requestParam="";
        log.info("requestParam==="+param);

        //执行请求
        String response= HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/Ydt_validate")
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("Token",sysConfig.getConfigValue())
                .body("param="+param)
                .timeout(30000)//超时，毫秒
                .execute().body();
        log.info("response==="+response);

        return response;
    }
    //登录报关单校验接口
    private String loginInvoiceApi(){
        String response= HttpRequest.post("http://auth.feiliankeji.cn/Fly_BgApi/GetToken")
                .header("Content-Type","application/x-www-form-urlencoded")
                .body("Account="+account+"&PassWord="+passWord)//表单内容
                .timeout(30000)//超时，毫秒
                .execute().body();
        JSONObject jsonObject=JSON.parseObject(response);
        if(200==jsonObject.getInteger("code")){
            //先删除再添加
            sysConfigMapper.delete(new LambdaQueryWrapper<SysConfig>()
                    .eq(SysConfig::getConfigKey,"DEC_LOGIC_VERIFICATION"));
            SysConfig sysConfig=new SysConfig();
            sysConfig.setConfigKey("DEC_LOGIC_VERIFICATION");
            sysConfig.setConfigName("报关单逻辑校验请求token");
            sysConfig.setConfigValue(jsonObject.getString("data"));
            sysConfigMapper.insert(sysConfig);
            return jsonObject.getString("data");
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updatePushStatusById(String pushStatus, String id) {

        boolean messsage = sendMesssage(id);
        if (!messsage)
            return messsage;
        /*
         * 订阅信息
         * 2025/4/2 15:01@ZHANGCHAO
         */
//        DecHead decHead = this.getDecHeadById(id);
//        ThreadUtil.execAsync(() -> commonService.subscribeSchedule(decHead, 3));
        boolean update = this.update(new UpdateWrapper<DecHead>().lambda()
                .set(DecHead::getPushStatus, pushStatus)
//                .set("1".equals(pushStatus), DecHead::getDecStatus, "2")
                .eq(DecHead::getId, id));
        return update;
    }

    private boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    class WeekGroup {
        private Date startDate;
        private Date endDate;
        private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        public WeekGroup(Date startDate, Date endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public Date getStartDate() {
            return startDate;
        }

        public Date getEndDate() {
            return endDate;
        }

        public String getStartDateStr() {
            return dateFormat.format(startDate);
        }

        public String getEndDateStr() {
            return dateFormat.format(endDate);
        }
    }

    @Override
    public Result<?> listOverseasConsignorHistory(String ieFlag){
        List<DecHead> decHeadList = this.list(new LambdaQueryWrapper<DecHead>()
                .eq(DecHead::getIeFlag, ieFlag));
        if(decHeadList.isEmpty()){
            return Result.OK();
        }
        List<String> list = new ArrayList<>();
        //进口的话 筛选overseasConsignorEname字段排除空的  list接收
        if("I".equals(ieFlag)){
            list = decHeadList.stream()
                    .map(DecHead::getOverseasConsignorEname)
                   .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                   .collect(Collectors.toList());
        }else if("E".equals(ieFlag)){
            list = decHeadList.stream()
                    .map(DecHead::getOverseasConsigneeEname)
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return Result.OK(list);
    }
}
