package org.jeecg.modules.business.entity.messages.emlMessages;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * ImportInfo
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>  2025/6/24 10:32
 * @version 1.0
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class ImportInfo {
    @XmlElement(name = "MessageType")
    private String messageType;

    @XmlElement(name = "OpType")
    private String opType;

    @XmlElement(name = "HostId")
    private String hostId;

    @XmlElement(name = "FileSize")
    private String fileSize;

    @XmlElement(name = "Sign")
    private String sign;

    // Getters and Setters
    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
