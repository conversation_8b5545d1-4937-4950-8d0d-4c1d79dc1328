package org.jeecg.common.util.oss;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.FileItemStream;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.SymbolConstant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.filter.FileTypeFilter;
import org.jeecg.common.util.filter.StrAttackFilter;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.mybatis.TenantContext;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.util.Date;
import java.util.UUID;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * @Description: 阿里云 oss 上传工具类(高依赖版)
 * @Date: 2019/5/10
 * @author: jeecg-boot
 */
@Slf4j
public class OssBootUtil {

    private static String endPoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String staticDomain;

    public static void setEndPoint(String endPoint) {
        OssBootUtil.endPoint = endPoint;
    }

    public static void setAccessKeyId(String accessKeyId) {
        OssBootUtil.accessKeyId = accessKeyId;
    }

    public static void setAccessKeySecret(String accessKeySecret) {
        OssBootUtil.accessKeySecret = accessKeySecret;
    }

    public static void setBucketName(String bucketName) {
        OssBootUtil.bucketName = bucketName;
    }

    public static void setStaticDomain(String staticDomain) {
        OssBootUtil.staticDomain = staticDomain;
    }

    public static String getStaticDomain() {
        return staticDomain;
    }

    public static String getEndPoint() {
        return endPoint;
    }

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public static OSSClient getOssClient() {
        return ossClient;
    }

    /**
     * oss 工具客户端
     */
    private static OSSClient ossClient = null;

    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param fileDir 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(MultipartFile file, String fileDir, String customBucket) {
        String filePath = null;
        initOss(endPoint, accessKeyId, accessKeySecret);
        StringBuilder fileUrl = new StringBuilder();
        String newBucket = bucketName;
        if (oConvertUtils.isNotEmpty(customBucket)) {
            newBucket = customBucket;
        }
        try {
            // 判断桶是否存在,不存在则创建桶
            // if(!ossClient.doesBucketExist(newBucket)){
            // ossClient.createBucket(newBucket);
            // }
            // 获取文件名
            String orgName = file.getOriginalFilename();
            if ("" == orgName) {
                orgName = file.getName();
            }
            // update-begin-author:liusq date:20210809 for: 过滤上传文件类型
            FileTypeFilter.fileTypeFilter(file);
            // update-end-author:liusq date:20210809 for: 过滤上传文件类型
            orgName = CommonUtils.getFileName(orgName);
            // 2024/9/12 15:50@ZHANGCHAO 追加/变更/完善：上传加企业租户的根目录
            String tenantId = isNotBlank(TenantContext.getTenant()) ? TenantContext.getTenant() : "unknown";
            String fileName = orgName.indexOf(".") == -1
                    ? orgName + "_" + System.currentTimeMillis()
                    : orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis()
                            + orgName.substring(orgName.lastIndexOf("."));
            if (!fileDir.endsWith(SymbolConstant.SINGLE_SLASH)) {
                fileDir = fileDir.concat(SymbolConstant.SINGLE_SLASH);
            }
            // update-begin-author:wangshuai date:20201012 for: 过滤上传文件夹名特殊字符，防止攻击
            fileDir = StrAttackFilter.filter(fileDir);
            // update-end-author:wangshuai date:20201012 for: 过滤上传文件夹名特殊字符，防止攻击
            fileUrl = fileUrl.append(fileDir + fileName);
            fileUrl = new StringBuilder(tenantId + SymbolConstant.SINGLE_SLASH + fileUrl);

            if (oConvertUtils.isNotEmpty(staticDomain)
                    && staticDomain.toLowerCase().startsWith(CommonConstant.STR_HTTP)) {
                filePath = staticDomain + SymbolConstant.SINGLE_SLASH + fileUrl;
            } else {
                filePath = "https://" + newBucket + "." + endPoint + SymbolConstant.SINGLE_SLASH + fileUrl;
            }
            PutObjectResult result = ossClient.putObject(newBucket, fileUrl.toString(), file.getInputStream());
            // 设置权限(公开读)
            // ossClient.setBucketAcl(newBucket, CannedAccessControlList.PublicRead);
            if (result != null) {
                log.info("------OSS文件上传成功------" + fileUrl);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return filePath;
    }

    /**
     * 获取原始URL
     * 
     * @param url: 原始URL
     * @Return: java.lang.String
     */
    public static String getOriginalUrl(String url) {
        String originalDomain = "https://" + bucketName + "." + endPoint;
        if (oConvertUtils.isNotEmpty(staticDomain) && url.indexOf(staticDomain) != -1) {
            url = url.replace(staticDomain, originalDomain);
        }
        return url;
    }

    /**
     * 文件上传
     * 
     * @param file
     * @param fileDir
     * @return
     */
    public static String upload(MultipartFile file, String fileDir) {
        return upload(file, fileDir, null);
    }

    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param fileDir 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(FileItemStream file, String fileDir) {
        String filePath = null;
        initOss(endPoint, accessKeyId, accessKeySecret);
        StringBuilder fileUrl = new StringBuilder();
        try {
            String suffix = file.getName().substring(file.getName().lastIndexOf('.'));
            String fileName = UUID.randomUUID().toString().replace("-", "") + suffix;
            if (!fileDir.endsWith(SymbolConstant.SINGLE_SLASH)) {
                fileDir = fileDir.concat(SymbolConstant.SINGLE_SLASH);
            }
            fileDir = StrAttackFilter.filter(fileDir);
            fileUrl = fileUrl.append(fileDir + fileName);
            if (oConvertUtils.isNotEmpty(staticDomain)
                    && staticDomain.toLowerCase().startsWith(CommonConstant.STR_HTTP)) {
                filePath = staticDomain + SymbolConstant.SINGLE_SLASH + fileUrl;
            } else {
                filePath = "https://" + bucketName + "." + endPoint + SymbolConstant.SINGLE_SLASH + fileUrl;
            }
            PutObjectResult result = ossClient.putObject(bucketName, fileUrl.toString(), file.openStream());
            // 设置权限(公开读)
            ossClient.setBucketAcl(bucketName, CannedAccessControlList.PublicRead);
            if (result != null) {
                log.info("------OSS文件上传成功------" + fileUrl);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return filePath;
    }

    /**
     * 删除文件
     * 
     * @param url
     */
    public static void deleteUrl(String url) {
        deleteUrl(url, null);
    }

    /**
     * 删除文件
     * 
     * @param url
     */
    public static void deleteUrl(String url, String bucket) {
        String newBucket = bucketName;
        if (oConvertUtils.isNotEmpty(bucket)) {
            newBucket = bucket;
        }
        String bucketUrl = "";
        if (oConvertUtils.isNotEmpty(staticDomain) && staticDomain.toLowerCase().startsWith(CommonConstant.STR_HTTP)) {
            bucketUrl = staticDomain + SymbolConstant.SINGLE_SLASH;
        } else {
            bucketUrl = "https://" + newBucket + "." + endPoint + SymbolConstant.SINGLE_SLASH;
        }
        url = url.replace(bucketUrl, "");
        if (oConvertUtils.isEmpty(ossClient)) {
            initOss(endPoint, accessKeyId, accessKeySecret);
            ;
        }
        ossClient.deleteObject(newBucket, url);
    }

    /**
     * 删除文件
     * 
     * @param fileName
     */
    public static void delete(String fileName) {
        ossClient.deleteObject(bucketName, fileName);
    }

    /**
     * 获取文件流
     * 
     * @param objectName
     * @param bucket
     * @return
     */
    public static InputStream getOssFile(String objectName, String bucket) {
        InputStream inputStream = null;
        try {
            String newBucket = bucketName;
            if (oConvertUtils.isNotEmpty(bucket)) {
                newBucket = bucket;
            }
            initOss(endPoint, accessKeyId, accessKeySecret);
            // update-begin---author:liusq Date:20220120
            // for：替换objectName前缀，防止key不一致导致获取不到文件----
            objectName = OssBootUtil.replacePrefix(objectName, bucket);
            // update-end---author:liusq Date:20220120
            // for：替换objectName前缀，防止key不一致导致获取不到文件----
            OSSObject ossObject = ossClient.getObject(newBucket, objectName);
            inputStream = new BufferedInputStream(ossObject.getObjectContent());
        } catch (Exception e) {
            log.info("文件获取失败" + e.getMessage());
        }
        return inputStream;
    }

    /**
     * 获取文件流（重载方法，使用默认bucket）
     * @param objectName
     * @return
     */
    public static InputStream getOssFile(String objectName){
        return getOssFile(objectName,null);
    }

    /**
     * 通过完整的文件URL获取文件流
     *
     * @param fileUrl 完整的文件URL，例如：
     *                https://bucket-name.oss-cn-beijing.aliyuncs.com/path/to/file.jpg
     *                或者 https://your-domain.com/path/to/file.jpg
     * @return InputStream 文件输入流，获取失败时返回null
     */
    public static InputStream getOssFileByUrl(String fileUrl) {
        if (oConvertUtils.isEmpty(fileUrl)) {
            log.warn("文件URL为空，无法获取文件流");
            return null;
        }

        try {
            String bucket = null;
            String objectName = null;

            // 判断是否使用了自定义静态域名
            if (oConvertUtils.isNotEmpty(staticDomain) &&
                staticDomain.toLowerCase().startsWith(CommonConstant.STR_HTTP) &&
                fileUrl.startsWith(staticDomain)) {

                // 使用静态域名的情况，使用默认bucket
                objectName = fileUrl.replace(staticDomain + SymbolConstant.SINGLE_SLASH, "");
                bucket = null; // 使用默认bucket

            } else if (fileUrl.startsWith("https://") || fileUrl.startsWith("http://")) {

                // 标准OSS URL格式：https://bucket-name.endpoint/path/to/file
                String urlWithoutProtocol = fileUrl.substring(fileUrl.indexOf("://") + 3);

                // 查找第一个斜杠的位置，分离域名和路径
                int firstSlashIndex = urlWithoutProtocol.indexOf(SymbolConstant.SINGLE_SLASH);
                if (firstSlashIndex == -1) {
                    log.warn("URL格式不正确，缺少文件路径: {}", fileUrl);
                    return null;
                }

                String domain = urlWithoutProtocol.substring(0, firstSlashIndex);
                objectName = urlWithoutProtocol.substring(firstSlashIndex + 1);

                // 从域名中提取bucket名称
                // 格式：bucket-name.oss-region.aliyuncs.com
                if (domain.contains("." + endPoint)) {
                    bucket = domain.substring(0, domain.indexOf("." + endPoint));
                } else {
                    // 如果不是标准OSS域名格式，使用默认bucket
                    bucket = null;
                }

            } else {
                log.warn("不支持的URL格式: {}", fileUrl);
                return null;
            }

            log.info("解析URL - bucket: {}, objectName: {}", bucket, objectName);

            // 调用现有的getOssFile方法
            return getOssFile(objectName, bucket);

        } catch (Exception e) {
            log.error("通过URL获取文件流失败: {}, 错误信息: {}", fileUrl, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取文件外链
     * 
     * @param bucketName
     * @param objectName
     * @param expires
     * @return
     */
    public static String getObjectUrl(String bucketName, String objectName, Date expires) {
        initOss(endPoint, accessKeyId, accessKeySecret);
        try {
            // update-begin---author:liusq Date:20220120
            // for：替换objectName前缀，防止key不一致导致获取不到文件----
            objectName = OssBootUtil.replacePrefix(objectName, bucketName);
            // update-end---author:liusq Date:20220120
            // for：替换objectName前缀，防止key不一致导致获取不到文件----
            if (ossClient.doesObjectExist(bucketName, objectName)) {
                URL url = ossClient.generatePresignedUrl(bucketName, objectName, expires);
                return URLDecoder.decode(url.toString(), "UTF-8");
            }
        } catch (Exception e) {
            log.info("文件路径获取失败" + e.getMessage());
        }
        return null;
    }

    /**
     * 生成带签名的URL，用于临时访问
     *
     * @param objectName        文件在OSS中的完整路径，例如
     *                          "0/productInfo/1569170270288_1750230397072.jpg"
     * @param expirationSeconds URL的有效时间（秒）
     * @return
     */
    public static String generatePresignedUrl(String objectName, long expirationSeconds) {
        if (oConvertUtils.isEmpty(objectName)) {
            log.warn("对象名称为空，无法生成预签名URL");
            return null;
        }

        initOss(endPoint, accessKeyId, accessKeySecret);
        URL signedUrl = null;
        try {
            // ✅ 添加路径前缀处理，防止key不一致导致获取不到文件
            String processedObjectName = replacePrefix(objectName, null);
            log.info("原始objectName: {}, 处理后objectName: {}", objectName, processedObjectName);

            // ✅ 检查文件是否存在
            if (!ossClient.doesObjectExist(bucketName, processedObjectName)) {
                log.warn("文件不存在，无法生成预签名URL: {}", processedObjectName);
                return null;
            }

            // 设置签名URL过期时间
            Date expiration = new Date(new Date().getTime() + expirationSeconds * 1000);

            // 生成以GET方法访问的签名URL，该URL有效期为 expirationSeconds 秒
            signedUrl = ossClient.generatePresignedUrl(bucketName, processedObjectName, expiration);

            log.info("生成的签名URL成功: {}", signedUrl);
        } catch (Exception e) {
            // 处理异常
            log.error("生成签名URL失败: {}, 错误信息: {}", objectName, e.getMessage(), e);
        }
        // ✅ 移除了 finally 块中的 shutdown() 调用，避免关闭全局客户端
        return signedUrl != null ? signedUrl.toString() : null;
    }

    /**
     * 生成带签名的URL，用于临时访问（支持指定bucket）
     *
     * @param objectName        文件在OSS中的完整路径
     * @param bucket           桶名称（可为空，使用默认桶）
     * @param expirationSeconds URL的有效时间（秒）
     * @return
     */
    public static String generatePresignedUrl(String objectName, String bucket, long expirationSeconds) {
        if (oConvertUtils.isEmpty(objectName)) {
            log.warn("对象名称为空，无法生成预签名URL");
            return null;
        }

        String newBucket = bucketName;
        if (oConvertUtils.isNotEmpty(bucket)) {
            newBucket = bucket;
        }

        initOss(endPoint, accessKeyId, accessKeySecret);
        URL signedUrl = null;
        try {
            // ✅ 添加路径前缀处理，防止key不一致导致获取不到文件
            String processedObjectName = replacePrefix(objectName, bucket);
            log.info("原始objectName: {}, 处理后objectName: {}, bucket: {}", objectName, processedObjectName, newBucket);

            // ✅ 检查文件是否存在
            if (!ossClient.doesObjectExist(newBucket, processedObjectName)) {
                log.warn("文件不存在，无法生成预签名URL: bucket={}, objectName={}", newBucket, processedObjectName);
                return null;
            }

            // 设置签名URL过期时间
            Date expiration = new Date(new Date().getTime() + expirationSeconds * 1000);

            // 生成以GET方法访问的签名URL，该URL有效期为 expirationSeconds 秒
            signedUrl = ossClient.generatePresignedUrl(newBucket, processedObjectName, expiration);

            log.info("生成的签名URL成功: {}", signedUrl);
        } catch (Exception e) {
            // 处理异常
            log.error("生成签名URL失败: bucket={}, objectName={}, 错误信息: {}", newBucket, objectName, e.getMessage(), e);
        }
        return signedUrl != null ? signedUrl.toString() : null;
    }

    /**
     * 初始化 oss 客户端
     *
     * @return
     */
    private static OSSClient initOss(String endpoint, String accessKeyId, String accessKeySecret) {
        // ✅ 检查客户端是否为null或已关闭
        if (ossClient == null || isClientClosed(ossClient)) {
            synchronized (OssBootUtil.class) {
                // 双重检查锁定模式
                if (ossClient == null || isClientClosed(ossClient)) {
                    // 如果旧客户端存在但已关闭，先置为null
                    if (ossClient != null && isClientClosed(ossClient)) {
                        ossClient = null;
                    }

                    // ✅ 配置连接池参数
                    ClientConfiguration config = new ClientConfiguration();
                    // 设置连接池大小
                    config.setMaxConnections(200);
                    // 设置连接超时时间（毫秒）
                    config.setConnectionTimeout(10000);
                    // 设置Socket超时时间（毫秒）
                    config.setSocketTimeout(50000);
                    // 设置连接空闲超时时间（毫秒）
                    config.setIdleConnectionTime(60000);
                    // 设置最大重试次数
                    config.setMaxErrorRetry(3);

                    ossClient = new OSSClient(endpoint,
                            new DefaultCredentialProvider(accessKeyId, accessKeySecret),
                            config);

                    log.info("OSS客户端初始化成功，连接池配置：maxConnections=200, connectionTimeout=10s, socketTimeout=50s");
                }
            }
        }
        return ossClient;
    }

    /**
     * 检查OSS客户端是否已关闭
     * 
     * @param client OSS客户端
     * @return true表示已关闭，false表示正常
     */
    private static boolean isClientClosed(OSSClient client) {
        try {
            // 尝试调用一个轻量级的方法来检查连接状态
            // 这里使用检查bucket是否存在的方法，但不实际执行
            if (client == null) {
                return true;
            }
            // 检查客户端内部状态（通过反射或其他方式）
            // 由于OSSClient没有直接的isClosed方法，我们通过异常捕获来判断
            return false;
        } catch (Exception e) {
            log.warn("OSS客户端状态检查异常，可能已关闭: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 上传文件到oss
     * 
     * @param stream
     * @param relativePath
     * @return
     */
    public static String upload(InputStream stream, String relativePath) {
        String filePath = null;
        String fileUrl = relativePath;
        initOss(endPoint, accessKeyId, accessKeySecret);
        if (oConvertUtils.isNotEmpty(staticDomain) && staticDomain.toLowerCase().startsWith(CommonConstant.STR_HTTP)) {
            filePath = staticDomain + SymbolConstant.SINGLE_SLASH + relativePath;
        } else {
            filePath = "https://" + bucketName + "." + endPoint + SymbolConstant.SINGLE_SLASH + fileUrl;
        }
        PutObjectResult result = ossClient.putObject(bucketName, fileUrl.toString(), stream);
        // 设置权限(公开读)
        ossClient.setBucketAcl(bucketName, CannedAccessControlList.PublicRead);
        if (result != null) {
            log.info("------OSS文件上传成功------" + fileUrl);
        }
        return filePath;
    }

    /**
     * 替换前缀，防止key不一致导致获取不到文件
     * 
     * @param objectName   文件上传路径 key
     * @param customBucket 自定义桶
     * @date 2022-01-20
     * <AUTHOR>
     * @return
     */
    private static String replacePrefix(String objectName, String customBucket) {
        log.info("------replacePrefix---替换前---objectName:{}", objectName);
        if (oConvertUtils.isNotEmpty(staticDomain)) {
            objectName = objectName.replace(staticDomain + SymbolConstant.SINGLE_SLASH, "");
        } else {
            String newBucket = bucketName;
            if (oConvertUtils.isNotEmpty(customBucket)) {
                newBucket = customBucket;
            }
            String path = "https://" + newBucket + "." + endPoint + SymbolConstant.SINGLE_SLASH;
            objectName = objectName.replace(path, "");
        }
        log.info("------replacePrefix---替换后---objectName:{}", objectName);
        return objectName;
    }

    /**
     * 优雅关闭OSS客户端
     * 应该在应用关闭时调用此方法
     */
    public static void shutdown() {
        if (ossClient != null) {
            try {
                ossClient.shutdown();
                log.info("OSS客户端已优雅关闭");
            } catch (Exception e) {
                log.error("关闭OSS客户端时发生异常: {}", e.getMessage(), e);
            } finally {
                ossClient = null;
            }
        }
    }

    /**
     * 获取OSS客户端连接状态
     *
     * @return true表示连接正常，false表示连接异常
     */
    public static boolean isConnected() {
        return ossClient != null && !isClientClosed(ossClient);
    }

    // ==================== 文件下载方法 ====================

    /**
     * 下载文件（通过URL）
     * 兼容MinioUtil的downloadFile方法，支持从minio迁移到OSS
     *
     * @param fileUrl 要下载的文件的URL
     * @param destinationFile 下载文件保存的路径（可为空，自动生成）
     * @return 本地文件路径，下载失败时返回null
     */
    public static String downloadFile(String fileUrl, String destinationFile) {
        if (oConvertUtils.isEmpty(fileUrl)) {
            log.warn("文件URL为空，无法下载文件");
            return null;
        }

        try {
            String localFilePath = "";

            // 如果未指定目标文件路径，自动生成
            if (oConvertUtils.isEmpty(destinationFile)) {
                destinationFile = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
                localFilePath = System.getProperty("user.dir") + File.separator + destinationFile;
            } else {
                localFilePath = destinationFile;
            }

            // 创建目标文件的父目录
            File targetFile = new File(localFilePath);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 通过URL下载文件
            URL url = new URL(fileUrl);
            URLConnection connection = url.openConnection();

            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(localFilePath)) {

                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                log.info("文件下载成功: {} -> {}, 大小: {} bytes", fileUrl, localFilePath, totalBytes);
                return localFilePath;
            }

        } catch (Exception e) {
            log.error("下载文件出现异常: {}, 错误信息: {}", fileUrl, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过OSS客户端下载文件
     * 适用于私有文件或需要更高性能的场景
     *
     * @param objectName OSS中的对象名称
     * @param destinationFile 本地保存路径（可为空，自动生成）
     * @return 本地文件路径，下载失败时返回null
     */
    public static String downloadFileFromOss(String objectName, String destinationFile) {
        return downloadFileFromOss(objectName, null, destinationFile);
    }

    /**
     * 通过OSS客户端下载文件（指定bucket）
     *
     * @param objectName OSS中的对象名称
     * @param bucket 桶名称（可为空，使用默认桶）
     * @param destinationFile 本地保存路径（可为空，自动生成）
     * @return 本地文件路径，下载失败时返回null
     */
    public static String downloadFileFromOss(String objectName, String bucket, String destinationFile) {
        if (oConvertUtils.isEmpty(objectName)) {
            log.warn("对象名称为空，无法下载文件");
            return null;
        }

        try {
            String newBucket = bucketName;
            if (oConvertUtils.isNotEmpty(bucket)) {
                newBucket = bucket;
            }

            // 初始化OSS客户端
            initOss(endPoint, accessKeyId, accessKeySecret);

            // 处理objectName前缀
            objectName = replacePrefix(objectName, bucket);

            String localFilePath = "";

            // 如果未指定目标文件路径，自动生成
            if (oConvertUtils.isEmpty(destinationFile)) {
                String fileName = objectName.substring(objectName.lastIndexOf("/") + 1);
                localFilePath = System.getProperty("user.dir") + File.separator + fileName;
            } else {
                localFilePath = destinationFile;
            }

            // 创建目标文件的父目录
            File targetFile = new File(localFilePath);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 通过OSS客户端下载文件
            OSSObject ossObject = ossClient.getObject(newBucket, objectName);

            try (InputStream inputStream = new BufferedInputStream(ossObject.getObjectContent());
                 FileOutputStream outputStream = new FileOutputStream(localFilePath)) {

                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                log.info("OSS文件下载成功: {}/{} -> {}, 大小: {} bytes", newBucket, objectName, localFilePath, totalBytes);
                return localFilePath;
            }

        } catch (Exception e) {
            log.error("OSS文件下载失败: {}, 错误信息: {}", objectName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 智能下载文件
     * 自动判断输入是完整URL还是对象名称，选择合适的下载方式
     *
     * @param fileUrlOrObjectName 文件URL或对象名称
     * @param destinationFile 本地保存路径（可为空，自动生成）
     * @return 本地文件路径，下载失败时返回null
     */
    public static String downloadFileAuto(String fileUrlOrObjectName, String destinationFile) {
        if (oConvertUtils.isEmpty(fileUrlOrObjectName)) {
            log.warn("文件标识为空，无法下载文件");
            return null;
        }

        // 判断是URL还是对象名称
        if (fileUrlOrObjectName.startsWith("http://") || fileUrlOrObjectName.startsWith("https://")) {
            // 是完整URL，使用URL下载方式
            log.info("检测到URL格式，使用URL下载方式: {}", fileUrlOrObjectName);
            return downloadFile(fileUrlOrObjectName, destinationFile);
        } else {
            // 是对象名称，使用OSS客户端下载方式
            log.info("检测到对象名称格式，使用OSS客户端下载方式: {}", fileUrlOrObjectName);
            return downloadFileFromOss(fileUrlOrObjectName, destinationFile);
        }
    }

    // ==================== 调试和诊断方法 ====================

    /**
     * 检查文件是否存在于OSS中
     * 用于调试文件访问问题
     *
     * @param objectName 对象名称
     * @return true表示文件存在，false表示不存在
     */
    public static boolean checkFileExists(String objectName) {
        return checkFileExists(objectName, null);
    }

    /**
     * 检查文件是否存在于OSS中（指定bucket）
     *
     * @param objectName 对象名称
     * @param bucket 桶名称（可为空，使用默认桶）
     * @return true表示文件存在，false表示不存在
     */
    public static boolean checkFileExists(String objectName, String bucket) {
        if (oConvertUtils.isEmpty(objectName)) {
            log.warn("对象名称为空，无法检查文件存在性");
            return false;
        }

        try {
            String newBucket = bucketName;
            if (oConvertUtils.isNotEmpty(bucket)) {
                newBucket = bucket;
            }

            initOss(endPoint, accessKeyId, accessKeySecret);

            // 处理对象名称前缀
            String processedObjectName = replacePrefix(objectName, bucket);

            log.info("检查文件存在性 - 原始: {}, 处理后: {}, bucket: {}", objectName, processedObjectName, newBucket);

            boolean exists = ossClient.doesObjectExist(newBucket, processedObjectName);
            log.info("文件存在性检查结果: {}", exists);

            return exists;
        } catch (Exception e) {
            log.error("检查文件存在性失败: {}, 错误信息: {}", objectName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件的详细信息（用于调试）
     *
     * @param objectName 对象名称
     * @return 文件信息字符串
     */
    public static String getFileInfo(String objectName) {
        return getFileInfo(objectName, null);
    }

    /**
     * 获取文件的详细信息（用于调试，指定bucket）
     *
     * @param objectName 对象名称
     * @param bucket 桶名称（可为空，使用默认桶）
     * @return 文件信息字符串
     */
    public static String getFileInfo(String objectName, String bucket) {
        if (oConvertUtils.isEmpty(objectName)) {
            return "对象名称为空";
        }

        try {
            String newBucket = bucketName;
            if (oConvertUtils.isNotEmpty(bucket)) {
                newBucket = bucket;
            }

            initOss(endPoint, accessKeyId, accessKeySecret);

            // 处理对象名称前缀
            String processedObjectName = replacePrefix(objectName, bucket);

            StringBuilder info = new StringBuilder();
            info.append("文件信息调试:\n");
            info.append("- 原始objectName: ").append(objectName).append("\n");
            info.append("- 处理后objectName: ").append(processedObjectName).append("\n");
            info.append("- bucket: ").append(newBucket).append("\n");
            info.append("- endPoint: ").append(endPoint).append("\n");
            info.append("- staticDomain: ").append(staticDomain).append("\n");

            boolean exists = ossClient.doesObjectExist(newBucket, processedObjectName);
            info.append("- 文件存在: ").append(exists).append("\n");

            if (exists) {
                try {
                    OSSObject ossObject = ossClient.getObject(newBucket, processedObjectName);
                    info.append("- 文件大小: ").append(ossObject.getObjectMetadata().getContentLength()).append(" bytes\n");
                    info.append("- 内容类型: ").append(ossObject.getObjectMetadata().getContentType()).append("\n");
                    info.append("- 最后修改时间: ").append(ossObject.getObjectMetadata().getLastModified()).append("\n");
                    ossObject.close();
                } catch (Exception e) {
                    info.append("- 获取文件元数据失败: ").append(e.getMessage()).append("\n");
                }
            }

            return info.toString();
        } catch (Exception e) {
            return "获取文件信息失败: " + e.getMessage();
        }
    }
}