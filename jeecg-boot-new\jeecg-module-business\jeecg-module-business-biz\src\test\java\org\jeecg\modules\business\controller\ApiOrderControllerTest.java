package org.jeecg.modules.business.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import icu.develop.apiwrap.WrapRequest;
import icu.develop.apiwrap.client.WrapClient;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.business.entity.DefaultWrapData;
import org.jeecg.modules.business.entity.Order;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.jeecg.modules.business.util.PreApiSample.*;

@Slf4j
class ApiOrderControllerTest {

    @Test
    void importPurchaseOrders() {
//        DefaultWrapData wrapData = new DefaultWrapData();
        String jsonText = "{\n" +
                "    \"orderNo\": \"SF2502888\",\n" +
                "    \"contractNo\": \"X-Pc-TR-5-20240808-03\",\n" +
                "    \"invoiceNo\": \"\",\n" +
                "    \"ieFlag\": \"E\",\n" +
                "    \"orderType\": \"3\",\n" +
                "    \"buyerName\": \"华熙生物科技股份有限公司\",\n" +
                "    \"overseasPayerInfoName\": \"荷兰 DuSart Pharma b.v (Euro)\",\n" +
                "    \"sellerAddress\": \"Njiverheidstraat 15, 2222 AV Katwijk, Netherlands   \",\n" +
                "    \"transMode\": \"CIP\",\n" +
                "    \"paymentClause\": \"E55\",\n" +
                "    \"signDate\": \"2024-08-12\",\n" +
                "    \"orderDetailList\": [\n" +
                "        {\n" +
                "            \"copGno\": \"1011100003\",\n" +
                "            \"pn\": \"化妆品级低分子透明质酸钠[HA-TLM]\",\n" +
                "            \"pnEn\": \"\",\n" +
                "            \"qty\": 31.0,\n" +
                "            \"qunit\": \"KG\",\n" +
                "            \"currency\": \"EUR\",\n" +
                "            \"price\": 550.0,\n" +
                "            \"amount\": 17050.0,\n" +
                "            \"batchNo\": \"J201240402\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
//        wrapData.setData(jsonText);
//        WrapClient wrapClient = WrapClient.create("", "SecretKey@2024!#JHsa");
//        WrapRequest<DefaultWrapData> request = wrapClient.wrap(wrapData);
        System.out.println(jsonText);

        String accessKey = "q!$7k=P~^dh0tuRM";
        String secretKey = "FsnpA1aD^e*t3UlX%wjBT6RZ^wXOV$Ez&3&7.XHoss0q%P";
        String timestamp = getTime();
        String nonce = getNonce();

        Map<String, String> headers = new HashMap<>();
        headers.put("accessKey", accessKey);
        headers.put("timestamp", timestamp);
        headers.put("nonce", nonce);
        String sign = null;
        try {
            sign = getLocalSign(null, timestamp, nonce, secretKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        headers.put("sign", sign);

//        String result = HttpUtil.createPost("http://*********:3001/jgsoft/open-api/order/v1/importOrders")
        String result = HttpUtil.createPost("http://localhost:9999/open-api/order/v1/importOrders")
                .addHeaders(headers)
                .body(jsonText)
                .execute()
                .body();
        log.info("[result]:{}", result);
//        String httpResponse = HttpUtil.post("http://***********:3001/jeecg-boot/open-api/order/v1/importOrders", JSON.toJSONString(request));

    }

    @Test
    void importGoods() {
        DefaultWrapData wrapData = new DefaultWrapData();
        String jsonText = "[\n" +
                "\t{\n" +
                "\t\t\"pn\": \"\",\n" +
                "\t\t\"chineseName\": \"糕点店1\",\n" +
                "\t\t\"englishName\": \"gaded1\",\n" +
                "\t\t\"originCountry\": \"CHN\",\n" +
                "\t\t\"chineseDescribe\": \"ddddd\",\n" +
                "\t\t\"englishDescribe\": \"fffff\",\n" +
                "\t\t\"barCode\": \"vvvvv\",\n" +
                "\t\t\"remarks\": \"nnnnnn\",\n" +
                "\t\t\"customsCodeInfoCode\": \"0101210090\",\n" +
                "\t\t\"customsCodeInfoId\": \"0101210090\",\n" +
                "\t\t\"tariffsName\": \"改良种用马（濒危野马除外）\",\n" +
                "\t\t\"hsname\": \"改良种用马（濒危野马除外）\",\n" +
                "\t\t\"customsDeclarationElements\": \"1|1|个梵蒂冈|都是|gg|gggg|hhhhhhhhhh\",\n" +
                "\t\t\"monitorcondition\": \"AB\",\n" +
                "\t\t\"iaqcategory\": \"P/Q\",\n" +
                "\t\t\"addedTaxRate\": \"1\",\n" +
                "\t\t\"taxRebateRate\": \"9.00\",\n" +
                "\t\t\"impcustomrate\": \"1\",\n" +
                "\t\t\"salesGuidancePrice\": \"2\",\n" +
                "\t\t\"productSpecificationModel\": \"3\",\n" +
                "\t\t\"legalUnitCode\": \"035\",\n" +
                "\t\t\"legalUnit\": \"千克\",\n" +
                "\t\t\"secondUnitCode\": \"009\",\n" +
                "\t\t\"secondUnit\": \"头\",\n" +
                "\t\t\"price\": \"7\",\n" +
                "\t\t\"priceFluctuationRatio\": \"8\",\n" +
                "\t\t\"netWeight\": \"5\",\n" +
                "\t\t\"netWeightFluctuationRatio\": \"6\"\n" +
                "\t},\n" +
                "\t{\n" +
                "\t\t\"pn\": \"\",\n" +
                "\t\t\"chineseName\": \"糕点店2\",\n" +
                "\t\t\"englishName\": \"gaded2\",\n" +
                "\t\t\"originCountry\": \"CHN\",\n" +
                "\t\t\"chineseDescribe\": \"ddddd\",\n" +
                "\t\t\"englishDescribe\": \"fffff\",\n" +
                "\t\t\"barCode\": \"vvvvv\",\n" +
                "\t\t\"remarks\": \"nnnnnn\",\n" +
                "\t\t\"customsCodeInfoCode\": \"0101210090\",\n" +
                "\t\t\"customsCodeInfoId\": \"0101210090\",\n" +
                "\t\t\"tariffsName\": \"改良种用马（濒危野马除外）\",\n" +
                "\t\t\"hsname\": \"改良种用马（濒危野马除外）\",\n" +
                "\t\t\"customsDeclarationElements\": \"1|1|个梵蒂冈|都是|gg|gggg|hhhhhhhhhh\",\n" +
                "\t\t\"monitorcondition\": \"AB\",\n" +
                "\t\t\"iaqcategory\": \"P/Q\",\n" +
                "\t\t\"addedTaxRate\": \"1\",\n" +
                "\t\t\"taxRebateRate\": \"9.00\",\n" +
                "\t\t\"impcustomrate\": \"1\",\n" +
                "\t\t\"salesGuidancePrice\": \"2\",\n" +
                "\t\t\"productSpecificationModel\": \"3\",\n" +
                "\t\t\"legalUnitCode\": \"035\",\n" +
                "\t\t\"legalUnit\": \"千克\",\n" +
                "\t\t\"secondUnitCode\": \"009\",\n" +
                "\t\t\"secondUnit\": \"头\",\n" +
                "\t\t\"price\": \"7\",\n" +
                "\t\t\"priceFluctuationRatio\": \"8\",\n" +
                "\t\t\"netWeight\": \"5\",\n" +
                "\t\t\"netWeightFluctuationRatio\": \"6\"\n" +
                "\t}\n" +
                "]";
        wrapData.setData(jsonText);
        WrapClient wrapClient = WrapClient.create("", "SecretKey@2024!#JHsa");
        WrapRequest<DefaultWrapData> request = wrapClient.wrap(wrapData);
        System.out.println(JSON.toJSONString(request));
        String httpResponse = HttpUtil.post("http://localhost:9999/open-api/order/v1/importGoods", JSON.toJSONString(request));
        System.out.println(httpResponse);
    }

    @Test
    void getBondedMaintenance() {
//        DefaultWrapData wrapData = new DefaultWrapData();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("startDate", "2024-08-01");
//        jsonObject.put("endDate", "2024-08-13");
//        wrapData.setData(JSON.toJSONString(jsonObject));
        WrapClient wrapClient = WrapClient.create("", "SecretKey@2024!#JHsa");
        WrapRequest<DefaultWrapData> request = wrapClient.wrap(new DefaultWrapData());
        String httpResponse = HttpUtil.post("http://localhost:9999/open-api/repair/v1/getBondedMaintenance", JSON.toJSONString(request));
//        String httpResponse = HttpRequest.post("http://***************:5200/jeecg-boot/open-api/repair/v1/getBondedMaintenance")
//                .header("Tenant-Id", "56")
//                .body(JSON.toJSONString(request))
//                .execute().body();
        log.info(httpResponse);
    }
}