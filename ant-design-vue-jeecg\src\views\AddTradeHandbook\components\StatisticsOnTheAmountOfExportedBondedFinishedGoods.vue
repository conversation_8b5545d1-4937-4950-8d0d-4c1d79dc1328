<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="成品序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入成品序号" v-model="queryParam.gNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="成品料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入成品料号" v-model="queryParam.copGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入商品名称" v-model="queryParam.gName"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div style="margin-top: -15px;">
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="gNo"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				class="j-table-force-nowrap"
				@change="handleTableChange"
			>

			</a-table>
		</div>
	</a-card>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import {filterDictTextByCache} from "@/components/dict/JDictSelectUtil";
import {getAction} from "@/api/manage";
import { floatAdd, subStrForColumns } from '@/utils/util'
import {ajaxGetDictItems} from "@/api/api";
const DICT_erp_units = 'erp_units,name,code'
export default {
	name: "StatisticsOnTheAmountOfExportedBondedFinishedGoods",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			units: [],
			queryParam: {
				type: '2', // 1料件 2成品 3损耗
				emsId: '999999999'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			emsHead: {},
			labelCol: {
				xs: {span: 5},
				// sm: { span: 7 },
				xxl: {span: 5},
				xl: {span: 9}
			},
			wrapperCol: {
				xs: {span: 16},
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '成品序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '成品料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '计量单位',
					align: 'center',
					dataIndex: 'unit',
					customRender: this.showQunitText
				},
				{
					title: '备案金额',
					align: 'center',
					dataIndex: 'decPrice'
				},
				{
					title: '直接出口金额合计(A)',
					align: 'center',
					dataIndex: 'zjcksl'
				},
				{
					title: '深加工结转出口金额合计(B)',
					align: 'center',
					dataIndex: 'sjgjzcksl'
				},
				{
					title: '成品退换出口金额合计(C)',
					align: 'center',
					dataIndex: 'cpthcksl'
				},
				{
					title: '成品退换进口金额合计(D)',
					align: 'center',
					dataIndex: 'cpthjksl'
				},
				{
					title: '实际出口金额合计(E=A+B+C-D)',
					align: 'center',
					dataIndex: 'sjckjehj'
				},{
					title: '加权平均单价',
					align: 'center',
					dataIndex: 'jqpjdj'
				},
			],
			url: {
				list: '/business/ems/listEmsDetailAmountByReport'
			}
		}
	},
	created() {
		this.initDictData(DICT_erp_units)
	},
	methods: {
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length>0) {
				if (dictCode == DICT_erp_units) {
					this.units = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode,JSON.stringify(res.result))
						this.initDictData(dictCode)
					}
				})
			}
		},
		subStrForColumns,
		init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						let data = res.result.records || res.result
						let newDataSource = data
						if (data.length > 0) {
							let item = {}
							item.unit = 'Total:'
							let amount = 0
							let zjcksl = 0
							let sjgjzcksl = 0
							let cpthcksl = 0
							let cpthjksl = 0
							let sjckjehj =0
							let jqpjdj = 0
							for (let i = 0; i < data.length; i++) {
								amount = floatAdd(amount,data[i].decPrice ? Number(data[i].decPrice) : 0)
								zjcksl = floatAdd(zjcksl,data[i].zjcksl ? Number(data[i].zjcksl) : 0)
								sjgjzcksl = floatAdd(sjgjzcksl,data[i].sjgjzcksl ? Number(data[i].sjgjzcksl) : 0)
								cpthcksl = floatAdd(cpthcksl,data[i].cpthcksl ? Number(data[i].cpthcksl) : 0)
								cpthjksl = floatAdd(cpthjksl,data[i].cpthjksl ? Number(data[i].cpthjksl) : 0)
								sjckjehj =  floatAdd(sjckjehj,data[i].sjckjehj ? Number(data[i].sjckjehj) : 0)
								jqpjdj = floatAdd(jqpjdj,data[i].jqpjdj ? Number(data[i].jqpjdj) : 0)
							}
							item.decPrice = amount
							item.zjcksl = zjcksl
							item.sjgjzcksl = sjgjzcksl
							item.cpthcksl = cpthcksl
							item.cpthjksl = cpthjksl
							item.sjckjehj = sjckjehj
							item.jqpjdj = jqpjdj
							newDataSource.push(item)
						}
						this.dataSource = newDataSource
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				type: '2', // 1料件 2成品 3损耗
				emsId: this.emsHead.id ? this.emsHead.id : '999999999',
				emsNo : this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			}
			this.loadData(1)
			this.onClearSelected()
		},
		showQunitText(text, record, index) {
			return this.getText(text, this.units)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}else {
						text = value
					}
				}
			}
			return text
		},
	}
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.table-page-search-wrapper {
	margin-top: -16px;
	margin-bottom: 16px;
}
.textGreen {
	color: darkgreen;
	font-weight: bold;
}
/deep/  .ant-table-expand-icon-th {
	background-color: #eaebed !important;
	border: 1px solid #d7dbe4 !important;
}
</style>