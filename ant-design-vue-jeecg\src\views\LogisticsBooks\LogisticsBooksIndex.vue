<template>
	<div>
		<a-card :bordered="false">
			<!-- 查询区域 -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline" @keyup.enter.native="searchQuery">
					<a-row :gutter="24">
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="账册编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入账册编号" v-model="queryParam.emsNo"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="经营单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入经营单位" v-model="queryParam.tradeName"></a-input>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-select placeholder="请选择状态" allowClear showSearch v-model="queryParam.status">
									<a-select-option value="1">正在使用</a-select-option>
									<a-select-option value="2">暂停使用</a-select-option>
									<a-select-option value="3">已核销</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 查询区域-END -->

			<!-- 操作按钮区域 -->
			<div class="table-operator">
				<a-row>
					<a-col :span="6">
						<a-button v-has="'ems:edit'" @click="handleAdd" type="primary" icon="plus">新增</a-button>
						<a-button v-has="'ems:edit'" @click="handleImport" type="primary" icon="upload">导入账册</a-button>
						<a-button v-has="'ems::syn'" :loading = 'synLoading' icon="redo" type="primary" @click="handleSynEmsBook">更新账册</a-button>
<!--						<a-button @click="handleUpdateQty" type="primary" icon="upload">更新数量</a-button>-->
						<a-button v-has="'ems:edit'"
							@click="batchDel"
							v-if="selectedRowKeys.length > 0"
							ghost
							type="primary"
							icon="delete">批量删除
						</a-button>
					</a-col>
					<a-col :span="18">
						<span>颜色标识：</span>
						<span style="background-color: darkgrey;display: inline-block;width: 30px;
					text-align: center;color: white">灰</span>
						<span>：过期</span>
						<span style="background-color: red;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">红</span>
						<span>：30天以内</span>
						<span style="background-color: gold;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">金</span>
						<span>：30-90</span>
						<span style="background-color: orange;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">橙</span>
						<span>：90-180</span>
						<span style="background-color: green;display: inline-block;width: 30px;
					text-align: center;margin-left: 10px;color: white">绿</span>
						<span>：180+</span>
					</a-col>
				</a-row>
			</div>

			<!-- table区域-begin -->
			<div>
				<a-table
					ref="table"
					size="small"
					:scroll="{ x: true }"
					bordered
					rowKey="id"
					:columns="columns"
					:dataSource="dataSource"
					:pagination="ipagination"
					:loading="loading"
					class="j-table-force-nowrap"
					:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio'}"
					:rowClassName="getRowClassname"
					@change="handleTableChange"
					:customRow="rowEvent"
				>
					<template slot="emsNoSlot" slot-scope="text, record">
						<a @click="handleEdit(record)">{{ text }}</a>
					</template>

					<span slot="action" slot-scope="text, record">
          <a-dropdown>
            <a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
            <a-menu slot="overlay">
              <a-menu-item v-has="'ems:edit'">
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item  v-has="'ems:edit'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
				</a-table>
			</div>
			<!-- 新增编辑详情账册 -->
			<logistics-books-edit-modal ref='modalForm' @ok="modalFormOk" @close="forceRerender"/>
		</a-card>
		<!-- 料件、成品、损耗表体列表 -->
		<logistics-body-list ref='logisticsBodyListRef'/>
		<!--		账册导入-->
		<import-modal ref='importModal' :downLoadUrl='url.downLoadTempUrl' :importUrl='url.importExcelUrl'
									downLoadButtonText='下载物流账册导入模板' emsType="TW" type="3"
									title='账册导入'
									@closeUploadModal='closeUploadModal' @loadData="loadData(1)"
		></import-modal>
	</div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import LogisticsBodyList from '@/views/LogisticsBooks/modules/LogisticsBodyList.vue'
import { deleteAction, getAction,postAction } from '@/api/manage'
import LogisticsBooksEditModal from '@/views/LogisticsBooks/modules/LogisticsBooksEditModal.vue'
import ImportModal from '@/components/ImportModal/ImportModal.vue'

export default {
	name: 'LogisticsBooksIndex',
	mixins: [JeecgListMixin, mixinDevice],
	components: {
		ImportModal,
		LogisticsBooksEditModal,
		LogisticsBodyList
	},
	data() {
		return {
			synLoading:false,
			queryParam: {
				menuType: '3',
				status: '1'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			/* 分页参数 */
			ipagination: {
				current: 1,
				pageSize: 5,
				pageSizeOptions: ['5', '10', '15'],
				showTotal: (total, range) => {
					return range[0] + '-' + range[1] + ' 共' + total + '条'
				},
				showQuickJumper: true,
				showSizeChanger: true,
				total: 0
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '账册编号',
					align: 'center',
					dataIndex: 'emsNo',
					scopedSlots: { customRender: 'emsNoSlot' }
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'tradeName'
				},
				{
					title: '类型',
					align: 'center',
					dataIndex: 'emsType',
					customRender: function(text) {
						if (text == 'TW') {
							return 'TW-账册'
						} else if (text == 'L') {
							return 'L-账册'
						} else {
							return text
						}
					}
				},
				{
					title: '有效日期',
					align: 'center',
					dataIndex: 'endDate',
					customCell: (record, index) => {
						if(record.endDate){
							const days=this.calculateDays('',record.endDate)
							if(days>180){
								//绿色大于180
								return {
									style: {
										'background-color': 'green'
									},
								}
							}else if(days>90&&days<=180){
								//橙色大于90小于180
								return {
									style: {
										'background-color': 'orange'
									},
								}
							}else if(days>30&&days<=90){
								//金色大于30小于90
								return {
									style: {
										'background-color': 'gold'
									},
								}
							}else if(days<=30&&days>=0){
								//红色大于30
								return {
									style: {
										'background-color': 'red'
									},
								}
							}else {
								//过期
								return {
									style: {
										'background-color': 'darkgrey'
									},
								}
							}
						}
					},
				},
				{
					title: '状态',
					align: 'center',
					dataIndex: 'status',
					customRender: function(text) {
						if (text == '1') {
							return '正在使用'
						} else if (text == '2') {
							return '暂停使用'
						} else if (text == '3') {
							return '已核销'
						} else {
							return text
						}
					}
				},
				{
					title: '登记日期',
					align: 'center',
					dataIndex: 'byDate'
				},
				{
					title: '登记人',
					align: 'center',
					dataIndex: 'byName'
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 50,
					scopedSlots: { customRender: 'action' }
				},
			],
			url: {
				list: '/business/ems/list',
				deleteBatch: '/business/ems/deleteBatch',
				downLoadTempUrl: '/template/物流账册导入模版.xls',
				importExcelUrl: '/business/ems/importEms',
				handleSynEmsHandBook:'/business/ems/synEmsHead'
			},
		}
	},
	created() {
		this.handleEmptyIcon()
	},
	methods: {
		handleUpdateQty(){
			getAction('/business/ems/xxx', {
				emsNo:this.selectionRows[0].emsNo
			}).then((res) => {
				if (res.success) {

				} else {
				}
			})
				.finally(() => {
					this.synLoading = false
				})
		},
					//更新同步账册表头信息
		handleSynEmsBook(){
			if(!this.selectedRowKeys||this.selectedRowKeys.length==0){
				var that = this
			this.$confirm({
				title: '确认操作',
				content: '⚠️注意：当前未选中账册数据，是否全部更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook()
				}
			})
			}else{
				var that = this
this.$confirm({
				title: '确认操作',
				content: '是否对选中账册数据更新同步?',
				onOk: function () {
					that.synLoading = true
				  that.executeSynEmsHandBook(that.selectionRows[0].emsNo,that.selectionRows[0].seqNo)
				}
			})
			}

		},
				executeSynEmsHandBook(emsNo,seqNo){
			postAction(this.url.handleSynEmsHandBook, {
								type: 'WL',
								emsNo:emsNo,
								seqNo:seqNo
							}).then((res) => {
									if (res.success) {
										this.$message.success('更新成功！')
										this.loadData()
									} else {
										this.$message.warning(res.message || res)
									}
								})
								.finally(() => {
									this.synLoading = false
								})
		},
		handleImport() {
			this.$refs.importModal.fileList = []
			this.$refs.importModal.visible = true
		},
		closeUploadModal() {
			this.$refs.importModal.visible = false
			this.$refs.importModal.fileList = []
			this.loadData(1)
			this.onClearSelected()
		},
		calculateDays(startDate,endDate) {
			const start = new Date();
			const end = new Date(endDate);
			const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
			return days
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						let keys = []
						this.selectionRows = []
						keys.push(this.dataSource[0].id)
						this.selectedRowKeys = keys
						this.selectionRows.push(this.dataSource[0])
						this.$nextTick(() => {
							this.$refs.logisticsBodyListRef.initHead(this.dataSource[0])
						})
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		handleEmptyIcon() {
			this.handleTableHeight('250px', '', '')
		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
						this.$refs.logisticsBodyListRef.initHead(record)
					},
					dblclick: () => {
						this.handleEdit(record)
					},
					// ...
				}
			}
		},
		searchReset() {
			this.queryParam = {
				menuType: '3',
				status: '1'
			}
			this.$refs.logisticsBodyListRef.clear()
			this.searchQuery()
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids })
							.then(res => {
								if (res.success) {
									//重新计算分页问题
									that.reCalculatePage(that.selectedRowKeys.length)
									that.$message.success(res.message)
									that.loadData()
									that.onClearSelected()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		handleDelete: function (record) {
			var that = this;
			deleteAction(that.url.deleteBatch, {
				ids: record.id
			}).then((res) => {
				if (res.success) {
					//重新计算分页问题
					that.reCalculatePage(1)
					that.$message.success(res.message)
					that.loadData()
					that.onClearSelected()
				} else {
					that.$message.warning(res.message)
				}
			});
		},
		forceRerender() {
			this.loadData()
			// this.onClearSelected()
		},
		// 增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '2') {
				return 'data-rule-invalid'
			}
		},
	}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}
</style>