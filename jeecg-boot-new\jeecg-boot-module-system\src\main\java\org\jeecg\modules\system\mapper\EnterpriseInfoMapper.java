package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.common.system.vo.CustomsBrokerInfo;
import org.jeecg.modules.system.entity.EnterpriseInfo;

import java.util.List;

/**
 * @Description: 企业信息表
 * @Author: jeecg-boot
 * @Date:   2022-02-18
 * @Version: V1.0
 */
public interface EnterpriseInfoMapper extends BaseMapper<EnterpriseInfo> {
    List<EnterpriseInfo> getCollectionEnterpriseList(EnterpriseInfo enterpriseInfo) ;
    String getTenantNameById(Long id);
    void updateTenantName(Long id, String name);

    @InterceptorIgnore(tenantLine = "true")
    int insertBrokerInfo(CustomsBrokerInfo customsBrokerInfo);
    @InterceptorIgnore(tenantLine = "true")
    CustomsBrokerInfo getBrokerInfo(String enterpriseName);
}
