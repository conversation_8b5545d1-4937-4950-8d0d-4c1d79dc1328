package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.base.entity.SysAnnouncement;
import org.jeecg.modules.business.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.business.entity.excel.ExportDecExcel;
import org.jeecg.modules.business.vo.ChartDecStatisticsVO;
import org.jeecg.modules.business.vo.DecStatisticsVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
public interface DecHeadMapper extends BaseMapper<DecHead> {

    void insertBatchSomeColumn(@Param("addHeads") List<DecHead> addHeads);

    void updateBatchById(@Param("updateHeads") List<DecHead> updateHeads);

    void insertBatchLists(@Param("addLists") List<DecList> addLists);

    void insertBatchContainers(@Param("addContainers") List<DecContainer> addContainers);

    void insertBatchLicenseDocuses(@Param("addLicenseDocuss") List<DecLicenseDocus> addLicenseDocuss);

    @InterceptorIgnore(tenantLine = "true")
    List<DecHead> listBySeqNos(@Param("cusCiqNoList") List<String> cusCiqNoList);

    @InterceptorIgnore(tenantLine = "true")//这种方式忽略多租户需要拥有mapper层的方法
    DecHead GetDecHeadById(String id);

    //查询报关单票量
    List<DecStatisticsVO> exportDecStatisticsListTicketQty(@Param("field")String field,
                                                  @Param("ieFlag")String ieFlag,
                                                  @Param("declarationDateStart")String declarationDateStart,
                                                  @Param("declarationDateEnd")String declarationDateEnd);
    //查询报关单贸易额
    List<DecStatisticsVO> exportDecStatisticsListTradeVolume(@Param("field")String field,
                                                           @Param("ieFlag")String ieFlag,
                                                           @Param("declarationDateStart")String declarationDateStart,
                                                           @Param("declarationDateEnd")String declarationDateEnd);
    //查询报关单箱量
    List<DecStatisticsVO> exportDecStatisticsListTeu(@Param("field")String field,
                                                  @Param("ieFlag")String ieFlag,
                                                  @Param("declarationDateStart")String declarationDateStart,
                                                  @Param("declarationDateEnd")String declarationDateEnd);

    List<ExportDecExcel> customExportDec(@Param("ids") List<String> ids,DecHead decHead);

    List<ExportDecExcel> customExportDecToDecList(@Param("ids") List<String> ids,DecHead decHead);

    List<ExportDecExcel> customExportDecToDecHead(@Param("ids") List<String> ids,DecHead decHead);

    List<ChartDecStatisticsVO> getChartDec(@Param("year")String year,
                                           @Param("ieFlag")String ieFlag,
                                           @Param("tenantId")String tenantId);

    /**
     * 手动新增系统公告
     * @param sysAnnouncement
     */
    int saveSysAnnouncement(SysAnnouncement sysAnnouncement);
    /**
     * 手动新增系统公告接收
     * @param
     */
    int saveSysAnnouncementSend(@Param("id")String id,
                                 @Param("anntId")String anntId,
                                 @Param("userIds")String userIds,
                                 @Param("createBy")String createBy,
                                    @Param("createTime") Date createTime,
                                         @Param("msgCategory")String msgCategory);

    List<ChartDecStatisticsVO> getChartDecBonded(String year, String ieFlag);

    List<ChartDecStatisticsVO> getChartInvtBonded(String year, String ieFlag);

    List<ChartDecStatisticsVO> getChartPassBonded(String year, String ieFlag);

    @InterceptorIgnore(tenantLine = "true")
    List<DecHead> getDecHeadByManifestInfo(@Param("decStatus")List<String> decStatus,@Param("tenandId")String tenandId);

    Integer updateApplyInvoiceSupvModecd(@Param("supvModecdMap") Map<String,String> supvModecdMap, @Param("applyNumber") String applyNumber);

    @InterceptorIgnore(tenantLine = "true")
    List<DecHead> listDecHeadsByCond(String seqNo, String tenantId);
    @InterceptorIgnore(tenantLine = "true")
    IPage<DecHead> queryDecHeadPageList(Page<DecHead> page,
                                        @Param(Constants.WRAPPER) Wrapper<DecHead> wrapper);
	@InterceptorIgnore(tenantLine = "true")
    String getDistrictCodeByDeliverUnitName(String deliverUnitName);
    @InterceptorIgnore(tenantLine = "true")
    List<DecHead> getDecByContainerAndBillCode(String cTdh, String cXh);
 	@InterceptorIgnore(tenantLine = "true")
    String getDistrictCodeByOptUnitName(String optUnitName);
    @InterceptorIgnore(tenantLine = "true")
    String getHscodeByHistory(String hsname);
    @InterceptorIgnore(tenantLine = "true")
    DecList queryListForCheckHsmodel(String optUnitName, String deliverUnitName, String hscode, String hsname, @Param("hsmodelarrays") List<String> hsmodelarrays);
    @InterceptorIgnore(tenantLine = "true")
    DecList queryListForDestCode(String optUnitName, String deliverUnitName, String hscode, String hsname, String districtCode);
    @InterceptorIgnore(tenantLine = "true")
    DecList queryListForDistrictCode(String optUnitName, String deliverUnitName, String hscode, String hsname, String districtCode);
    @InterceptorIgnore(tenantLine = "true")
    DecList queryListForCheckHsmodel_(String declareUnitName, String hscode, String hsname, @Param("hsmodelarrays") List<String> hsmodelarrays);
}
