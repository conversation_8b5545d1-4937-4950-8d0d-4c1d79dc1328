<template>
    <!--其它包装种类-->
    <a-modal :centered='true'
             :visible='showModal'
             :maskClosable= "maskClosable"
             @cancel='showModal = !showModal'
             @ok='handleOk'
             cancel-text=''
             title='编辑其他包装信息'
             width='60%'>
        <!--列表-->
        <a-table :bordered='true'
                 :columns='columns'
                 :dataSource='dataSource'
                 :loading='loading'
                 :pagination='false'
                 :rowSelection="{type:'checkbox', selectedRowKeys: selectedRowKeys,onChange:handleTableSelectionChange}"
                 :scroll='{ x:false, y: 280,scrollToFirstRowOnChange:true,}'
								 class="j-table-force-nowrap"
                 rowKey='code'>
        </a-table>
    </a-modal>
</template>

<script>
import {getBZZL} from "@/api/dec/dec"


export default {
    name   : "pack-type",
    data() {
        return {
            // 已选的键
            selectedRowKeys: [],
            // 已选的内容
            selectedRows   : [],
            // value副本
            val            : '',
            // show副本
            showModal      : false,
            dataSource     : [],
            columns        : [
                {title: '序号', key: 'rowIndex', width: 80, align: 'center', customRender: (t, r, i) => 1 + i},
                {title: '包装材料种类代码', width: 160, dataIndex: 'code', key: 'code', ellipsis: true},
                {title: '包装材料种类名称', dataIndex: 'name', key: 'name', ellipsis: true},
            ],
            loading        : false,
            maskClosable:false,
        }
    },
    created() {
        this.initData()
    },
    props  : {
        'value': {
            type   : String,
            require: true
        },
        'show' : {
            type: Boolean,
        }
    },
    model  : {
        prop : 'value',
        event: 'change'
    },
    watch  : {
        value: {
            handler(val) {
                if(!!val){
                    let sd = val.split(',').filter(item => item !== '')
                    this.selectedRowKeys = val.split(',').filter(item => item !== '')
                }


            }
        },
        val  : {
            handler() {
                this.$emit('change', this.val.join(','))
            },
        },
        show : {
            handler(val) {
                this.showModal = !this.showModal
            }
        }
    },
    methods: {
        async initData() {
            this.loading    = true
            const res       = await getBZZL().finally(() => this.loading = false)

            res.result.sort((a,b)=>{
                return a.code -  b.code
            });
            this.dataSource = res.result
        },
        /**
         * 取消修改
         */
        handleCancel() {
        },
        /**
         * 确认修改
         */
        handleOk() {
          console.log("其他包装选中===》",this.selectedRowKeys)
            this.val       = this.selectedRowKeys
            this.showModal = !this.showModal
        },
        /**
         * 列选择变更
         * @param selectedRowKeys 选中列的rowKey
         */
        handleTableSelectionChange(selectedRowKeys) {
          console.log("选中其他包装==》",selectedRowKeys)
            this.selectedRowKeys = selectedRowKeys
        },
    }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>