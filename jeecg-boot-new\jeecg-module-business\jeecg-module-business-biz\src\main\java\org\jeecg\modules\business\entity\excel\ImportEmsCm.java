package org.jeecg.modules.business.entity.excel;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelVerify;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ImportEmsCm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成品序号
     */
    @Excel(name = "成品序号")
    @TableField("EXG_NO")
    private Integer exgNo;

    /**
     * 料件序号
     */
    @Excel(name = "料件序号")
    @TableField("IMG_NO")
    private Integer imgNo;

    /**
     * 单耗/净耗
     */
    @Excel(name = "净耗")
    @TableField("DEC_CM")
    private BigDecimal decCm;

    /**
     * 损耗率
     */
    @Excel(name = "有形损耗率（%）")
    @ExcelVerify(notNull = true)
    @TableField("DEC_DM")
    private BigDecimal decDm;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @TableField("NOTE")
    private String note;

    /**
     * 单耗版本号
     */
    @Excel(name = "单耗版本号")
    @ExcelVerify(notNull = true)
    @TableField("UCNSVERNO")
    private String ucnsverno;
    /**
     * 单耗申报状态
     */
    @Excel(name = "单耗申报状态")
    private String unitConsumptionStatus;

    /**
     * 无形损耗率 %
     */
    @Excel(name = "无形损耗率（%）")
    private BigDecimal intangibleLossRate;

    /**
     * 保税料件比例%
     */
    @Excel(name = "保税料件比例（%）")
    private BigDecimal proportionOfBondedMaterials;
    /**
     * 修改标志
     */
    @Excel(name = "修改标志")
    private String modifyFlag;
    /**
     * 企业执行标志
     */
    @Excel(name = "企业执行标志")
    private String enterpriseExecutionFlag;


}
