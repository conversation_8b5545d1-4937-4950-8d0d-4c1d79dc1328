package org.jeecg.modules.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.jeecg.modules.business.entity.AiConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * AI配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AiConfigMapper extends BaseMapper<AiConfig> {
    @InterceptorIgnore(tenantLine = "true")
    List<AiConfig> listAiSettings(AiConfig aiConfig);
}
