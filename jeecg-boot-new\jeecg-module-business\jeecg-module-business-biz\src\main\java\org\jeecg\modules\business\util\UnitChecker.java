package org.jeecg.modules.business.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 单位判断
 *
 * @author: ZHANGCHAO
 * @version: 1.0
 * @date: 2025/5/22 18:54
 */
public class UnitChecker {
    // 重量级单位映射表：CODE 和 NAME 都作为键
    private static final Map<String, String> WEIGHT_UNITS = new HashMap<>();

    static {
        // 初始化重量级单位
        addWeightUnit("035", "千克");
        addWeightUnit("036", "克");
        addWeightUnit("070", "吨");
        addWeightUnit("071", "长吨");
        addWeightUnit("072", "短吨");
        addWeightUnit("073", "司马担");
        addWeightUnit("074", "司马斤");
        addWeightUnit("075", "斤");
        addWeightUnit("076", "磅");
        addWeightUnit("077", "担");
        addWeightUnit("078", "英担");
        addWeightUnit("079", "短担");
        addWeightUnit("080", "两");
        addWeightUnit("081", "市担");
        addWeightUnit("083", "盎司");
        addWeightUnit("084", "克拉");
    }

    private static void addWeightUnit(String code, String name) {
        WEIGHT_UNITS.put(code, name);
        WEIGHT_UNITS.put(name, name); // 支持通过中文名查询
    }

    /**
     * 判断输入的 unit 是否是一个重量级单位
     * @param unit 输入的单位，可以是 CODE（如 "035"） 或 NAME（如 "千克"）
     * @return 如果是重量级单位返回 true，否则返回 false
     */
    public static boolean isWeightUnit(String unit) {
        return WEIGHT_UNITS.containsKey(unit);
    }

    /**
     * 获取匹配到的重量级单位名称（可选）
     * @param unit 输入的单位
     * @return 匹配到的中文单位名称，如果没有匹配则返回 null
     */
    public static String getWeightUnitName(String unit) {
        return WEIGHT_UNITS.get(unit);
    }

    // 示例 main 方法测试
    public static void main(String[] args) {
        System.out.println(isWeightUnit("035")); // true
        System.out.println(isWeightUnit("千克")); // true
        System.out.println(isWeightUnit("米"));   // false
        System.out.println(isWeightUnit("084")); // true
        System.out.println(isWeightUnit("克拉")); // true
    }
}
