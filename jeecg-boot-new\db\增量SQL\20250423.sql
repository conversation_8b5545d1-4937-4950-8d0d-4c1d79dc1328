ALTER TABLE accounts_receivable_payable
    ADD COLUMN type VARCHAR(5) DEFAULT NULL COMMENT '收支方向（1收方向 2支方向）',
     ADD COLUMN settlement_id bigint(20) DEFAULT NULL COMMENT '结算单位id',
     ADD COLUMN settlement_name VARCHAR(200) DEFAULT NULL COMMENT '结算单位名称',
     ADD COLUMN fee_id bigint(20) DEFAULT NULL COMMENT '关联费用id'

CREATE TABLE SETTLEMENT_INFO (
                                 ID BIGINT PRIMARY KEY COMMENT '主键ID',
                                 SETTLEMENT_TYPE VARCHAR(20) NOT NULL COMMENT '结算单位类型(应收单位/应付单位)',
                                 SETTLEMENT_NAME VARCHAR(100) NOT NULL COMMENT '结算单位名称',
                                 REMARK VARCHAR(500) COMMENT '备注',
                                 CREATOR VARCHAR(50) NOT NULL COMMENT '创建人',
                                 CREATE_TIME DATETIME NOT NULL COMMENT '创建时间',
                                 UPDATER VARCHAR(50) COMMENT '更新人',
                                 UPDATE_TIME DATETIME COMMENT '更新时间',
                                 TENANT_ID VARCHAR(50) NOT NULL COMMENT '租户ID'
) COMMENT '结算单位信息';
