package org.jeecg.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.Tenant;
import org.jeecg.config.mybatis.TenantContext;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.enums.StockTypeEnum;
import org.jeecg.modules.business.mapper.StorageTransferMapper;
import org.jeecg.modules.business.service.*;
import org.jeecg.modules.business.util.exception.ExceptionUtil;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.E;
import static org.jeecg.common.constant.CommonConstant.I;

/**
 * <p>
 * 调拨单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Service
public class StorageTransferServiceImpl extends ServiceImpl<StorageTransferMapper, StorageTransfer> implements IStorageTransferService {
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private ISerialNumberService serialNumberService;
    @Autowired
    private ICommissionerService commissionerService;
    @Autowired
    private IStoreStocksService storeStocksService;
    @Autowired
    private IStorageTransferDetailService storageTransferDetailService;

    /**
     * @param storageTransfer
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @Override
    public Result<?> queryPageList(StorageTransfer storageTransfer, Integer pageNo, Integer pageSize, HttpServletRequest req) {
//        Date appDate = storageTransfer.getAppDate();
//        storageTransfer.setAppDate(null);
//        QueryWrapper<StorageTransfer> queryWrapper = QueryGenerator.initQueryWrapper(storageTransfer, req.getParameterMap());
//        if (isNotEmpty(appDate)) {
//            queryWrapper.lambda().apply("date_format(APP_DATE,'%Y-%m-%d') = '" + DateUtil.format(appDate, "yyyy-MM-dd") + "'");
//        }
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (isNotEmpty(loginUser) && isNotBlank(loginUser.getUserType())) {
//            queryWrapper.lambda().eq(StorageTransfer::getCustomer, loginUser.getUserTypeRel());
            storageTransfer.setCustomer(loginUser.getUserTypeRel());
        }
        storageTransfer.setTenantId(Long.valueOf(TenantContext.getTenant()));
        Page<StorageTransfer> page = new Page<>(pageNo, pageSize);
        IPage<StorageTransfer> pageList = baseMapper.queryPageList(page, storageTransfer);
        if (isNotEmpty(pageList.getRecords())) {
            Map<String, List<Commissioner>> commissionerMap = new HashMap<>();
            List<String> customerList = pageList.getRecords().stream().map(StorageTransfer::getCustomer).filter(CharSequenceUtil::isNotBlank).distinct().collect(java.util.stream.Collectors.toList());
            if (isNotEmpty(customerList)) {
                List<Commissioner> commissionerList = commissionerService.listByIds(customerList);
                commissionerMap = commissionerList.stream().collect(Collectors.groupingBy(Commissioner::getId));
            }
            List<String> transferNos = pageList.getRecords().stream().map(StorageTransfer::getTransferNo).distinct().collect(java.util.stream.Collectors.toList());
            List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                    .in(StorageTransferDetail::getTransferNo, transferNos));
            Map<String, List<StorageTransferDetail>> storageTransferDetailMap = storageTransferDetailList.stream().collect(java.util.stream.Collectors.groupingBy(StorageTransferDetail::getTransferNo));
            for (StorageTransfer transfer : pageList.getRecords()) {
                if (isNotBlank(transfer.getCustomer())) {
                    String tenantName = null;
                    try {
                        Result<Tenant> tenant = sysBaseApi.getTenantById(transfer.getCustomer());
                        if (isNotEmpty(tenant.getResult())) {
                            tenantName = tenant.getResult().getName();
                        }
                    } catch (Exception e) {
                        log.error("获取租户名出现异常：{}", e.getMessage());
                    }
                    Commissioner commissioner = isNotEmpty(commissionerMap) && isNotEmpty(commissionerMap.get(transfer.getCustomer())) ? commissionerMap.get(transfer.getCustomer()).get(0) : null;
                    if (isNotEmpty(commissioner)) {
                        transfer.setCustomerStr(commissioner.getCommissionerFullName());
                    } else {
                        transfer.setCustomerStr(tenantName);
                    }
                }
                List<StorageTransferDetail> transferDetailList = storageTransferDetailMap.get(transfer.getTransferNo());
                if (isNotEmpty(transferDetailList)) {
                    transferDetailList.forEach(detail -> {
                        detail.setCustomer(transfer.getCustomer());
                        Result<StoreStocks> result = storeStocksService.getStockByTransferDetail(detail);
                        StoreStocks storeStocks = null;
                        if (result.isSuccess()) {
                            storeStocks = result.getResult();
                        }
                        if (isNotEmpty(storeStocks)) {
                            BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                            BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                            detail.setStockQty(beginQty); // 实际库存数量
                            detail.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
                            detail.setOccupyQty(occupyQty); // 占用数量
                        }
                    });
                }
                transfer.setStorageTransferDetailList(transferDetailList);
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 根据ID查询出调拨单信息
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/26 13:23
     */
    @Override
    public Result<?> getTransferById(String id) {
        StorageTransfer storageTransfer = baseMapper.selectById(id);
        if (isEmpty(storageTransfer)) {
            return Result.error("未找到ID为" + id + "的调拨单信息，请刷新页面重试！");
        }
        if (isNotBlank(storageTransfer.getCustomer())) {
            String tenantName = null;
            try {
                Result<Tenant> tenant = sysBaseApi.getTenantById(storageTransfer.getCustomer());
                if (isNotEmpty(tenant.getResult())) {
                    tenantName = tenant.getResult().getName();
                }
            } catch (Exception e) {
                log.error("获取租户名出现异常：{}", e.getMessage());
            }
            Commissioner commissioner = commissionerService.getById(storageTransfer.getCustomer());
            if (isNotEmpty(commissioner)) {
                storageTransfer.setCustomerStr(commissioner.getCommissionerFullName());
            } else {
                storageTransfer.setCustomerStr(tenantName);
            }
        }
        List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
        if (isNotEmpty(storageTransferDetailList)) {
            storageTransferDetailList.forEach(item -> {
                item.setCustomer(storageTransfer.getCustomer());
                Result<StoreStocks> result = storeStocksService.getStockByTransferDetail(item);
                StoreStocks storeStocks = null;
                if (result.isSuccess()) {
                    storeStocks = result.getResult();
                }
                if (isNotEmpty(storeStocks)) {
                    BigDecimal beginQty = isNotEmpty(storeStocks.getBeginQty()) ? storeStocks.getBeginQty() : BigDecimal.ZERO;
                    BigDecimal occupyQty = isNotEmpty(storeStocks.getOccupyQty()) ? storeStocks.getOccupyQty() : BigDecimal.ZERO;
                    item.setStockQty(beginQty); // 实际库存数量
                    item.setAvailableQty(beginQty.subtract(occupyQty)); // 可用库存数量
                }
            });
        }
        storageTransfer.setStorageTransferDetailList(storageTransferDetailList);
        return Result.ok(storageTransfer);
    }

    /**
     * 保存调拨单信息
     *
     * @param storageTransfer
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/26 13:31
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveStorageTransfer(StorageTransfer storageTransfer) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(storageTransfer.getId())) {
            String transferNo = serialNumberService.getSerialnumberByCustomerCode("DBD", 4);
            storageTransfer.setTransferNo(transferNo);
            storageTransfer.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageTransfer.setCreateDate(new Date());
            storageTransfer.setTenantId(Long.valueOf(TenantContext.getTenant()));
            baseMapper.insert(storageTransfer);
            // 编辑
        } else {
            storageTransfer.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            storageTransfer.setUpdateDate(new Date());
            baseMapper.updateById(storageTransfer);
        }
        if (isNotEmpty(storageTransfer.getStorageTransferDetailList())) {
            List<StorageTransferDetail> oldTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                    .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
            if (isNotEmpty(oldTransferDetailList)) {
                // 使用 Stream 进行过滤
                List<StorageTransferDetail> dels = oldTransferDetailList.stream()
                        .filter(item -> storageTransfer.getStorageTransferDetailList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    storageTransferDetailService.removeBatchByIds(dels.stream().map(StorageTransferDetail::getId).collect(Collectors.toList()));
                }
            }
            for (StorageTransferDetail storageTransferDetail : storageTransfer.getStorageTransferDetailList()) {
                if (isBlank(storageTransferDetail.getId())) {
                    storageTransferDetail.setTransferNo(storageTransfer.getTransferNo());
                    storageTransferDetail.setStoreCode(storageTransfer.getStoreCode());
                    storageTransferDetail.setAreaCodeBefore(storageTransfer.getAreaCodeBefore());
                    storageTransferDetail.setAreaNameBefore(storageTransfer.getAreaNameBefore());
                    storageTransferDetail.setAreaCodeAfter(storageTransfer.getAreaCodeAfter());
                    storageTransferDetail.setAreaNameAfter(storageTransfer.getAreaNameAfter());
                    storageTransferDetail.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageTransferDetail.setCreateDate(new Date());
                    storageTransferDetail.setTenantId(Long.valueOf(TenantContext.getTenant()));
                    storageTransferDetailService.save(storageTransferDetail);
                } else {
                    storageTransferDetail.setStoreCode(storageTransfer.getStoreCode());
                    storageTransferDetail.setAreaCodeBefore(storageTransfer.getAreaCodeBefore());
                    storageTransferDetail.setAreaNameBefore(storageTransfer.getAreaNameBefore());
                    storageTransferDetail.setAreaCodeAfter(storageTransfer.getAreaCodeAfter());
                    storageTransferDetail.setAreaNameAfter(storageTransfer.getAreaNameAfter());
                    storageTransferDetail.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    storageTransferDetail.setUpdateDate(new Date());
                    storageTransferDetailService.updateById(storageTransferDetail);
                }
            }
        } else {
            storageTransferDetailService.remove(new LambdaQueryWrapper<StorageTransferDetail>()
                    .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
        }
        StorageTransfer resultTransfer = baseMapper.selectById(storageTransfer.getId());
        resultTransfer.setStorageTransferDetailList(storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                .eq(StorageTransferDetail::getTransferNo, resultTransfer.getTransferNo())));
        return Result.ok(resultTransfer);
    }

    /**
     * 删除调拨单信息
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/26 13:41
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        for (String id : ids.split(",")) {
            StorageTransfer storageTransfer = baseMapper.selectById(id);
            if (isEmpty(storageTransfer)) {
                continue;
            }
            if ("1".equals(storageTransfer.getStatus())) {
                return Result.error("已确认的数据无法删除!");
            }
            List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                    .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
            if (isNotEmpty(storageTransferDetailList)) {
                for (StorageTransferDetail storageTransferDetail : storageTransferDetailList) {
                    storageTransferDetailService.removeById(storageTransferDetail.getId());
                }
            }
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 调拨单确认操作
     *
     * @param ids
     * @param type
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/8/26 17:38
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleConfirm(String ids, String type) {
        String[] idList = ids.split(",");
        String status = "1".equals(type) ? "1" : "0"; // 1 已确认

        for (String id : idList) {
            StorageTransfer storageTransfer = baseMapper.selectById(id);
            if (isEmpty(storageTransfer)) {
                continue;
            }
            if ("2".equals(storageTransfer.getStep()) || "3".equals(storageTransfer.getStep())) {
                List<StorageTransfer> storageTransferList = baseMapper.selectList(new LambdaQueryWrapper<StorageTransfer>()
                        .eq(StorageTransfer::getRelRepairNo, storageTransfer.getRelRepairNo())
                        .eq(StorageTransfer::getStep, "1")
                        .ne(StorageTransfer::getStatus, "1")
                        .eq(StorageTransfer::getTenantId, TenantContext.getTenant()));
                if (isNotEmpty(storageTransferList)) {
                    return Result.error("调拨单[" + storageTransfer.getTransferNo() + "]所属维修单的上一步调拨单还未确认，请先将调拨单[" + storageTransferList.stream().map(StorageTransfer::getTransferNo).collect(Collectors.joining(",")) + "]执行确认！");
                }
            }
            // 2024/8/26 17:41@ZHANGCHAO 追加/变更/完善：确认操作，移库操作！
            if ("1".equals(type)) {
                List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                        .eq(StorageTransferDetail::getTransferNo, storageTransfer.getTransferNo()));
                if (isNotEmpty(storageTransferDetailList)) {
                    for (StorageTransferDetail storageTransferDetail : storageTransferDetailList) {
                        log.info("调拨单[{}]商品：维修货物：[{}-{}-{}-{}] => 调拨前库区储位：[{}-{}] => 调拨后库区储位：[{}-{}]",
                                storageTransfer.getTransferNo(), storageTransferDetail.getCopGno(),
                                storageTransferDetail.getItemNumber(), storageTransferDetail.getBatchNo(),
                                storageTransferDetail.getPn(), storageTransferDetail.getAreaCodeBefore(),
                                storageTransferDetail.getSpaceCodeBefore(), storageTransferDetail.getAreaCodeAfter(),
                                storageTransferDetail.getSpaceCodeAfter());

                        // 处理库存的减少与增加
                        handleStockChange(storageTransfer, storageTransferDetail, true);
                        handleStockChange(storageTransfer, storageTransferDetail, false);
                    }
                }
            }
            baseMapper.update(null, new LambdaUpdateWrapper<StorageTransfer>()
                    .set(StorageTransfer::getStatus, status)
                    .set(isBlank(storageTransfer.getRelRepairNo()), StorageTransfer::getAppDate, new Date())
                    .eq(StorageTransfer::getId, id));
        }
        return Result.ok("操作成功！");
    }

    /**
     * 取消确认
     *
     * @param ids
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/11 16:06
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> handleSubmitBack(String ids) {
        List<StorageTransfer> storageTransferList = baseMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        if (isEmpty(storageTransferList)) {
            return Result.error("未获取到调拨单数据！");
        }
        // 需已确认的才能操作
        boolean isSubmit = storageTransferList.stream().allMatch(i -> "1".equals(i.getStatus()));
        if (!isSubmit) {
            return Result.error("请选择已确认的调拨单！");
        }
        List<String> transferNos = storageTransferList.stream().map(StorageTransfer::getTransferNo).distinct().collect(java.util.stream.Collectors.toList());
        List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailService.list(new LambdaQueryWrapper<StorageTransferDetail>()
                .in(StorageTransferDetail::getTransferNo, transferNos));
        Map<String, List<StorageTransferDetail>> storageTransferDetailMap = new HashMap<>();
        if (isNotEmpty(storageTransferDetailList)) {
            storageTransferDetailMap = storageTransferDetailList.stream().collect(java.util.stream.Collectors.groupingBy(StorageTransferDetail::getTransferNo));
        }
        StringBuilder sb = new StringBuilder();
        Set<String> successIds = new HashSet<>(16);
        Set<String> errorIds = new HashSet<>(16);
        StorageTransferServiceImpl currentProxy = (StorageTransferServiceImpl) AopContext.currentProxy();
        for (StorageTransfer storageTransfer : storageTransferList) {
            try {
                Result<?> result = currentProxy.processRepairOrderOne(storageTransfer, storageTransferDetailMap);
                if (result.isSuccess()) {
                    log.info("[取消确认]处理结果：{}", result.getMessage());
                    successIds.add(storageTransfer.getTransferNo());
                } else {
                    throw new RuntimeException(result.getMessage());
                }
            } catch (Exception e) {
                ExceptionUtil.getFullStackTrace(e);
                log.error("[取消确认] 取消确认出现异常，原因：{}", e.getMessage());
                errorIds.add(storageTransfer.getTransferNo());
                sb.append("[").append(storageTransfer.getTransferNo()).append("]取消确认出现异常：").append(e.getMessage()).append(";|");
            }
        }
        String msg = "共" + ids.split(",").length + "票，成功数："
                + successIds.size() + (isNotEmpty(errorIds) ? ("，失败数："
                + errorIds.size() + "，原因：" + sb) : "");
        log.info(msg);
        return Result.ok(msg);
    }

    /**
     * 处理单个调拨单
     *
     * @param storageTransfer
     * @param storageTransferDetailMap
     * @return org.jeecg.common.api.vo.Result<?>
     * <AUTHOR>
     * @date 2024/12/11 16:13
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<?> processRepairOrderOne(StorageTransfer storageTransfer, Map<String, List<StorageTransferDetail>> storageTransferDetailMap) {
        try {
            List<StorageTransferDetail> storageTransferDetailList = storageTransferDetailMap.get(storageTransfer.getTransferNo());
            if (isNotEmpty(storageTransferDetailList)) {
                for (StorageTransferDetail storageTransferDetail : storageTransferDetailList) {
                    log.info("[取消确认]调拨单[{}]商品：[{}-{}-{}-{}] => 调拨前库区储位：[{}-{}] => 调拨后库区储位：[{}-{}]",
                            storageTransfer.getTransferNo(), storageTransferDetail.getCopGno(),
                            storageTransferDetail.getItemNumber(), storageTransferDetail.getBatchNo(),
                            storageTransferDetail.getPn(), storageTransferDetail.getAreaCodeAfter(),
                            storageTransferDetail.getSpaceCodeAfter(), storageTransferDetail.getAreaCodeBefore(),
                            storageTransferDetail.getSpaceCodeBefore());

                    // 处理库存的减少与增加
                    handleStockChangeRectify(storageTransfer, storageTransferDetail, true); // ---
                    handleStockChangeRectify(storageTransfer, storageTransferDetail, false); // +++
                }
            }
        } catch (Exception e) {
            ExceptionUtil.getFullStackTrace(e);
            log.error("[取消确认]取消确认出现异常，原因：{}", e.getMessage());
            throw new RuntimeException("[取消确认]取消确认出现异常：" + e.getMessage());
        }
        // 都成功后，更新状态
        baseMapper.update(null, new LambdaUpdateWrapper<StorageTransfer>()
                .set(StorageTransfer::getStatus, "0") // 0 未确认
                .eq(StorageTransfer::getId, storageTransfer.getId()));
        return Result.ok("操作成功！");
    }

    /**
     * 处理库存增减
     *
     * @param storageTransfer
     * @param storageTransferDetail
     * @param isReduce
     * @return void
     * <AUTHOR>
     * @date 2024/8/27 09:36
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleStockChangeRectify(StorageTransfer storageTransfer, StorageTransferDetail storageTransferDetail, boolean isReduce) {
        StorageDetail storageDetail = new StorageDetail();
        BeanUtil.copyProperties(storageTransferDetail, storageDetail, CopyOptions.create().ignoreNullValue());
        // 2024/9/14 11:50@ZHANGCHAO 追加/变更/完善：针对维修完成的调拨单，批次号可能会修改！！
        if (isNotBlank(storageTransferDetail.getBatchNoNew())) {
            storageDetail.setBatchNo(isReduce ? storageTransferDetail.getBatchNo() : storageTransferDetail.getBatchNoNew());
        }
        storageDetail.setIeFlag(isReduce ? E : I);
        storageDetail.setStockTypeEnum(isReduce ? StockTypeEnum.GOODS_REDUCE : StockTypeEnum.GOODS_ADD);
        storageDetail.setCustomer(storageTransfer.getCustomer());
        storageDetail.setSpaceName(isReduce ? storageTransferDetail.getSpaceNameBefore() : storageTransferDetail.getSpaceNameAfter());
        storageDetail.setSpaceCode(isReduce ? storageTransferDetail.getSpaceCodeBefore() : storageTransferDetail.getSpaceCodeAfter());
        storageDetail.setAreaCode(isReduce ? storageTransferDetail.getAreaNameBefore() : storageTransferDetail.getAreaNameAfter());
        storageDetail.setAreaName(isReduce ? storageTransferDetail.getAreaCodeBefore() : storageTransferDetail.getAreaCodeAfter());
        storageDetail.setStorageNo(storageTransferDetail.getTransferNo());

        Result<?> stockHandleResult = storeStocksService.rectify(storageDetail); // 冲正
        if (!stockHandleResult.isSuccess()) {
            throw new RuntimeException(stockHandleResult.getMessage());
        }
    }

    /**
     * 处理库存增减
     *
     * @param storageTransfer
     * @param storageTransferDetail
     * @param isReduce
     * @return void
     * <AUTHOR>
     * @date 2024/8/27 09:36
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleStockChange(StorageTransfer storageTransfer, StorageTransferDetail storageTransferDetail, boolean isReduce) {
        StorageDetail storageDetail = new StorageDetail();
        BeanUtil.copyProperties(storageTransferDetail, storageDetail, CopyOptions.create().ignoreNullValue());
        // 2024/9/14 11:50@ZHANGCHAO 追加/变更/完善：针对维修完成的调拨单，批次号可能会修改！！
        if (isNotBlank(storageTransferDetail.getBatchNoNew())) {
            storageDetail.setBatchNo(isReduce ? storageTransferDetail.getBatchNo() : storageTransferDetail.getBatchNoNew());
        }
        storageDetail.setIeFlag(isReduce ? "E" : "I");
        storageDetail.setCustomer(storageTransfer.getCustomer());
        storageDetail.setSpaceName(isReduce ? storageTransferDetail.getSpaceNameBefore() : storageTransferDetail.getSpaceNameAfter());
        storageDetail.setSpaceCode(isReduce ? storageTransferDetail.getSpaceCodeBefore() : storageTransferDetail.getSpaceCodeAfter());
        storageDetail.setAreaCode(isReduce ? storageTransferDetail.getAreaNameBefore() : storageTransferDetail.getAreaNameAfter());
        storageDetail.setAreaName(isReduce ? storageTransferDetail.getAreaCodeBefore() : storageTransferDetail.getAreaCodeAfter());
        storageDetail.setStorageNo(storageTransferDetail.getTransferNo());
        // 2025/4/24 15:45@ZHANGCHAO 追加/变更/完善：下面4个是后来加的！！
        storageDetail.setBondInvtNo(storageTransferDetail.getBondInvtNo());
        storageDetail.setHscode(storageTransferDetail.getHscode());
        storageDetail.setExpirationDate(storageTransferDetail.getExpirationDate());
        storageDetail.setManufactureDate(storageTransferDetail.getManufactureDate());

        Result<?> stockHandleResult = isReduce ? storeStocksService.reduceStockHandle(storageDetail) : storeStocksService.addStockHandle(storageDetail);
        if (!stockHandleResult.isSuccess()) {
            throw new RuntimeException(stockHandleResult.getMessage());
        }
    }
}
