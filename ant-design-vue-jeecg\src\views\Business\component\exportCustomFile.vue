<template>
    <a-modal
        :title="title"
        :visible="visible"
        @cancel="handleCancel"
        @ok="handleOk"
        :confirm-loading="exportLoading"
        :width="1200"
        ref="valueModal"
    >
        <div :style="{ borderBottom: '1px solid #E9E9E9' }">
            <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
               全选
            </a-checkbox>
        </div>
        <br />
        <a-checkbox-group
            @change="onColSettingsChange"
            v-model="settingColumns"
            :default-value="settingColumns"
        >
            <a-row>
                <template v-for="item in defColumns">
                    <template>
                        <a-col :span="6">
                            <a-checkbox :value="item.field">{{ item.fieldName }}</a-checkbox>
                        </a-col>
                    </template>
                </template>
            </a-row>
        </a-checkbox-group>
    </a-modal>
</template>

<script>
import { downFile, downCustomFile, downFileNext,downloadFile } from '@api/manage'
import Vue from 'vue'
import {
    exportDecList, exportNemsList, previewDec,getFilePrint
} from '@/api/dec/dec'
export default {
    name: "exportCustomFile",
    props:{
        visible:{
            type: Boolean,
            default: false
        },
        /**
         * 默认循环字段的数组
         */
        defColumns:{
            type: Array,
            default: ''
        },
        title:{
            type: String,
            default: ''
        },
        type:{
            type: String,
            default: ''
        },
        url:{
            type: String,
            default: ''
        },
        ids:{
            type: Array,
            default: []
        },
        name:{
            type: String,
            default: ''
        },
        queryParam:{
            type: Object,
        },
        handleExportXlskey:{
            type: Number,
        },
    },
    data () {
        return{
            settingColumns:[],
            plainOptions: [],
            indeterminate: false,
            checkAll: false,
            exportLoading:false,
            exportType:''
        }
    },
    watch:{
        visible (bol) {
            if (bol){
                var key = this.$route.name + ':colsettings'
                let colSettings = Vue.ls.get(key)
                if (colSettings == null || colSettings == undefined) {
                    let allSettingColumns = []
                    this.defColumns.forEach(function(item, i, array) {
                        allSettingColumns.push(item.field)
                    })
                    this.settingColumns = allSettingColumns
                    this.plainOptions = allSettingColumns
                } else {
                    this.settingColumns = colSettings
                    this.plainOptions = colSettings
                }
            }
            this.indeterminate = !!this.settingColumns.length && this.settingColumns.length < this.defColumns.length;
            this.checkAll = this.settingColumns.length === this.defColumns.length
        }
    },
    methods:{
        async handleOk () {
            	this.exportLoading=true
                let params={}
                if('Q'==this.exportType){
                    const queryParam=JSON.parse(JSON.stringify(this.queryParam))
				    params=queryParam
                }
			params.columnList=this.settingColumns.join(',')
            if('S'==this.exportType){
                params.ids=this.ids.join(",")
            }
			

			downloadFile('/dcl/invt/exportInvtHeadByFieldsBatch', `核注单列表导出.xlsx`, params)
				.finally(() => {
					setTimeout(() => {
						this.exportLoading = false
					}, 500)
				})


        //    if(this.type=='nems'){//核注单
        //        this.exportLoading = true
        //        await exportNemsList(this.queryParam, this.ids.join(","), this.settingColumns).then((res) => {

        //            if (res.success) {
        //                let list = res.data
        //                getFilePrint(list,this,1,'fileServerURL')
        //                // window.open(list)


        //            } else {
        //                console.log(res)
        //                this.exportLoading = false
        //                this.$message.warn(`导出失败!${res.msg}`)
        //            }
        //        })
        //    }else {
        //        this.exportLoading = true
        //        let queryParams = {}
        //        if(this.handleExportXlskey== 1){//列表
        //            queryParams = null
        //        }else if(this.handleExportXlskey== 1.5){//查询
        //            queryParams = this.queryParam
        //        }
        //        await exportDecList(queryParams, this.ids.join(","), this.settingColumns).then((res) => {

        //            if (res.success) {
        //                let list = res.data
        //                getFilePrint(list,this,1,'fileServerURL')
        //                // window.open(list)


        //            } else {
        //                this.exportLoading = false
        //                this.$message.warn(`导出失败!${res.msg}`)
        //            }
        //        })
        //    }
        },
        handleCancel () {
            this.$emit('update:visible',false)
        },
        onColSettingsChange (checkedValues) {
            var key = this.$route.name + ':colsettings'
            Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
            this.settingColumns = checkedValues
            this.indeterminate = !!this.settingColumns.length && this.settingColumns.length < this.defColumns.length;
            this.checkAll = this.settingColumns.length === this.defColumns.length;
        },
        onCheckAllChange(e) {
            var key = this.$route.name + ':colsettings'
            if (e.target.checked) {
                let allSettingColumn = []
                this.defColumns.forEach(function(item, i, array) {
                    allSettingColumn.push(item.field)
                })
                this.settingColumns = allSettingColumn
                Vue.ls.set(key, this.settingColumns, 7 * 24 * 60 * 60 * 1000)
            } else {
                this.settingColumns = []
                Vue.ls.set(key, this.settingColumns, 7 * 24 * 60 * 60 * 1000)
            }
            Object.assign(this, {
                settingColumns: e.target.checked ? this.settingColumns : [],
                indeterminate: false,
                checkAll: e.target.checked,
            });
        }
    }
}
</script>

<style scoped>

</style>