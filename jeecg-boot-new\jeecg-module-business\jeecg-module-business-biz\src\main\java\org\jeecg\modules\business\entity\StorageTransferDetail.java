package org.jeecg.modules.business.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 调拨单商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@Accessors(chain = true)
@TableName("storage_transfer_detail")
public class StorageTransferDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调拨单号
     */
    @TableField("TRANSFER_NO")
    private String transferNo;

    /**
     * 仓库编码
     */
    @TableField("STORE_CODE")
    private String storeCode;

    /**
     * 调拨前库区编码
     */
    @TableField("AREA_CODE_BEFORE")
    private String areaCodeBefore;

    /**
     * 调拨前库区名称
     */
    @TableField("AREA_NAME_BEFORE")
    private String areaNameBefore;

    /**
     * 调拨前储位编码
     */
    @TableField("SPACE_CODE_BEFORE")
    private String spaceCodeBefore;

    /**
     * 调拨前储位名称
     */
    @TableField("SPACE_NAME_BEFORE")
    private String spaceNameBefore;

    /**
     * 调拨后库区编码
     */
    @TableField("AREA_CODE_AFTER")
    private String areaCodeAfter;

    /**
     * 调拨后库区名称
     */
    @TableField("AREA_NAME_AFTER")
    private String areaNameAfter;

    /**
     * 调拨后储位编码
     */
    @TableField("SPACE_CODE_AFTER")
    private String spaceCodeAfter;

    /**
     * 调拨后储位名称
     */
    @TableField("SPACE_NAME_AFTER")
    private String spaceNameAfter;

    /**
     * 项号
     */
    @TableField("ITEM_NUMBER")
    private String itemNumber;

    /**
     * 批次号
     */
    @TableField("BATCH_NO")
    private String batchNo;

    /**
     * 新批次号
     */
    @TableField("BATCH_NO_NEW")
    private String batchNoNew;

    /**
     * 物料号
     */
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 品名
     */
    @TableField("PN")
    private String pn;

    /**
     * 英文品名
     */
    @TableField("PN_EN")
    private String pnEn;

    /**
     * SN号
     */
    @TableField("SN")
    private String sn;

    /**
     * 运单号
     */
    @TableField("BILL_NO")
    private String billNo;

    /**
     * 规格型号
     */
    @TableField("MODEL")
    private String model;

    /**
     * 调拨数量
     */
    @TableField("ACTUAL_QTY")
    private BigDecimal actualQty;

    /**
     * 单位
     */
    @TableField("QUNIT")
    private String qunit;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 租户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 最后更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 最后更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    /**
     * 入库单表体料件类型：1维修货物、2维修用保税料件、3旧件/坏件
     */
    @TableField("DETAIL_TYPE")
    private String detailType;

    @TableField(exist = false)
    private String customer;

    /**
     * 库存数量
     */
    @TableField(exist = false)
    private BigDecimal stockQty;
    /**
     * 可用库存数量
     */
    @TableField(exist = false)
    private BigDecimal availableQty = BigDecimal.ZERO;
    /**
     * 占用数量
     */
    @TableField(exist = false)
    private BigDecimal occupyQty = BigDecimal.ZERO;

    /**
     * 清单编号 （多个逗号分割）
     */
    @Excel(name = "清单编号", width = 20)
    private String bondInvtNo;
    /**
     * 商品编码
     */
    @Excel(name = "商品编码", width = 18)
    private String hscode;
    /**
     *生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date manufactureDate;
    /**
     *有效到期日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;
}
