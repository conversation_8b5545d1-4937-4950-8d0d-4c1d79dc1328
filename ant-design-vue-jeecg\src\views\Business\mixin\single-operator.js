import moment from "moment";
import {decTeriff} from "@/api/dec/dec"
import {getDictItemsFromCache} from "@/api/api";
import {GetManualItem} from '@/api/dec/dec'
import {GetManualItemLocal} from '@/api/dec/dec'
import { postAction } from '@/api/manage'
import jexcel from 'jexcel'
import 'jexcel/dist/jexcel.css'
import { ajaxGetDictItems } from '@/api/api'
import { Mul } from '@/utils/util'
/**
 * 用于生成新列的id, 后端需要重新为其配置id
 * @type {number}
 */
let idGenerator = -1
export const singleOperator = {

    methods: {
        /**
         * 清空已选信息
         */
        resetTableSelection() {
            this.selectedRowKeys = []
        },
        /**
         * 重新排序
         */
        sortDataSource(index) {
            // 排序
            for (let i = 0; i < this.dataSource.length; i++) {
                let indexStr = String(index)
                if (indexStr != "") {
                    if (i > index && this.dataSource[i].id > 0) {
                        this.dataSource[i].opt = 'U'
                    } else if (this.dataSource[i].id < 0) {
                        this.dataSource[i].opt = 'I'
                    }
                }
                this.dataSource[i].item = i + 1
            }
        },
        defineTableEvent(record, index) {
            let that = this
            return {
                on: {
                    click: () => {
                        this.record = record
                        //当前检测table点击为通用方法此处判断是否是点击报关单表体
                        if (!!this.decRecord) {
                            this.hsmodelMethod()
                        }


                    },
                    dblclick: () => {
                    },
                },
            }
        },
        //双击操作
        cellDBLClickEvent({row}) {

        },
        // headerCellDBLClickEvent({column}) {
        //     console.log(`表头单元格双击${column.title}`)
        // },
        // headerCellContextMenuEvent({column}) {
        //     console.log(`右键列 ${column.title}`)
        // },
        cellClickEvent({row}) {
            this.record = row

					if (row.price) {
						try {
							this.record.price = this.record.price.toFixed(4)
						} catch (error) {
							console.warn('格式化价格时出现异常:', error.message)
							this.record.price = row.price
						}
					}
					if (row.total) {
						try {
							this.record.total = this.record.total.toFixed(4)
						} catch (error) {
							console.warn('格式化总价时出现异常:', error.message)
							this.record.total = row.total
						}
					}
            //检验检疫规格型号显示
            this.GoodsSpecStrMethod()
            this.hsmodelMethod()
            //法定第二单位为空置灰时，法定第二数量也要置灰
            if(this.record.unit2==""||this.record.unit2==null){
                this.count2Readonly = true
            }else{
                this.count2Readonly = false
            }
            console.log('单元格被点击时会触发该事件')
        },
        GoodsSpecStrMethod() {
            let sd = !!this.record.stuff ? this.record.stuff + ";" : ""
            //时间格式化

            let sd1 = !!this.record.prodValidDt ? this.record.prodValidDt + ";" : ""
            let sd2 = !!this.record.prodQgp ? this.record.prodQgp + ";" : ""
            let sd3 = !!this.record.mnufctrRegName ? this.record.mnufctrRegName + ";" : ""
            let sd4 = !!this.record.goodsSpec ? this.record.goodsSpec + ";" : ""
            let sd5 = !!this.record.goodsModel ? this.record.goodsModel + ";" : ""
            let sd6 = !!this.record.goodsBrand ? this.record.goodsBrand + ";" : ""
            let sd7 = !!this.record.produceDate ? this.record.produceDate + ";" : ""
            let sd8 = !!this.record.prodBatchNo ? this.record.prodBatchNo + ";" : ""
            let txtGoodsSpecStr = sd + sd1 + sd2 + sd3 + sd4 + sd5 + sd6 + sd7 + sd8
            this.record.txtGoodsSpecStr = txtGoodsSpecStr
        },
        decGoodsSpecClick() {
            this.focusIndex = 86
            this.show.decGoodsSpec = !this.show.decGoodsSpec
        },
        // cellMouseenterEvent({column}) {
        //     console.log(`鼠标进入单元格${column.title}`)
        // },
        // cellMouseleaveEvent({column}) {
        //     console.log(`鼠标离开单元格${column.title}`)
        // },
        // cellContextMenuEvent({row}) {
        //     console.log(`右键行 ${row.name}`)
        // },
        // scrollEvent({scrollTop, scrollLeft}) {
        //     console.log(`滚动事件scrollTop=${scrollTop} scrollLeft=${scrollLeft}`)
        // },
        cellSelected(row) {

        },
        // 当手动勾选全选时触发的事件
        handleVxeCheckboxAll(event) {
            this.selectedRowKeys(event.rowIndex)
        },
        // // 当手动勾选并且值发生改变时触发的事件
        handleVxeCheckboxChange(event) {
            // if (this.selectedRowKeys.indexOf(this.dataSource[event.rowIndex].id) < 0) {
            //     this.selectedRowKeys.push(this.dataSource[event.rowIndex].id)
            //
            //     this.selectedRows.push(this.dataSource[event.rowIndex])
            // } else {
            //     let index = this.selectedRowKeys.indexOf(this.dataSource[event.rowIndex].id);
            //     this.selectedRowKeys.splice(index, 1);
            //
            //     let recordIndex = this.selectedRows.indexOf(this.dataSource[event.rowIndex]);
            //     this.selectedRows.splice(recordIndex, 1);
            // }
        },
        checkboxRangeChange(records, reserves, $event) {
        },
        /**
         * 确认编辑(在表单内的)
         * @param e     event
         */
        handleEnter(e) {

            // todo: 参数校验
            let find = !!this.dataSource && this.dataSource.find(item => item.id === this.record.id)
            //表体新增数据验证
            if (e != 1) {
                //验证商品编码验证商品名称是否null
                /*const decListType = this.decListVerify()
                if (decListType) {
                    return;
                }*/
            }
            let handleEnterType = true
            // 修改
            if (!!find) {
                this.handleEnterUpdate(e, find)
            } else {
                //新增数据
                handleEnterType = this.handleEnterNew(e)
            }
            if (handleEnterType) {
                this.record = {}
                this.selectedRowKeys
                //等于1说明不是报关单表体的回车
                if (e == 1) {
                    this.handleAdd(1)
                }
            }
        },
        handleEnterUpdate(e, find) {
            let sd = this.value
            for (let findKey in find) {
                //集装箱  编辑操作
                if (!!this.countryList) {
                    for (let i = 0; i < this.countryList.length; i++) {
                        let sd = this.countryList[i].value
                        console.log(sd + "11111")
                        if (sd === this.record.containerMd) {
                            console.log(sd + "2222")
                            this.record.containerMdName = this.countryList[i].text
                            break
                        }
                    }
                }
                //随附单证 编辑操作
                if (!!this.docuCodeList) {
                    for (let i = 0; i < this.docuCodeList.length; i++) {
                        let docuCodemode = this.docuCodeList[i].value
                        if (docuCodemode === this.record.docuCode) {
                            this.record.docuName = this.docuCodeList[i].text
                            if(!!this.record.docuCode&&(this.record.certCode==""||typeof(this.record.certCode)=="undefined")&&this.focusIndex==91){
                                this.record.certCode = "NA"
                            }
                            break
                        }
                    }
                }


                find[findKey] = this.record[findKey]
                if (this.record.id < 0) {//小于0则是新增
                    find["opt"] = 'I'
                } else {
                    find["opt"] = 'U'
                }
            }
        },
        handleEnterNew(e) {
           if(!!this.countryList && !this.record.containerId && 1==e){
                  this.$message.warning('集装箱号不能为空')
                  return false;
            }
            if (this.record.id == undefined || this.record.id == "") {
                this.record.id = -1
            }

            //集装箱
            if (!!this.countryList && this.record.containerMd) {
                for (let i = 0; i < this.countryList.length; i++) {
                    let sd = this.countryList[i].value
                    if (this.record.containerId) {
                        if (sd === this.record.containerMd) {
                            console.log(sd + "2222")
                            this.record.containerMdName = this.countryList[i].text
                            break
                        }
                    } else {
                        this.$message.warning('集装箱号不能为空')
                        return false;
                    }

                }
            }

            //随附单证
            if (!!this.docuCodeList && this.record.docuCode) {
                for (let i = 0; i < this.docuCodeList.length; i++) {
                    let docuCodemode = this.docuCodeList[i].value

                    if (this.record.docuCode) {
                        if (docuCodemode === this.record.docuCode) {
                            this.record.docuName = this.docuCodeList[i].text
                            if(!!this.record.docuCode&&(this.record.certCode==""||typeof(this.record.certCode)=="undefined")&&this.focusIndex==91){
                                this.record.certCode = "NA"
                            }
                            break
                        }
                    } else {
                        this.$message.warning('随附单证代码不能为空')
                        return false;
                    }
                }
            }
            // 新增  判断是新增还是修改
            if (this.record.id < 0) {//小于0则是新增
                this.record.opt = 'I'
            } else {
                this.record.opt = 'U'
            }
            //表体新增数据验证
            if (e != 1) {
                const decListType = this.decListVerify()
                if (decListType) {
                    return false;
                }
            }
            this.record.item = this.dataSource.length+1
            this.dataSource.push(this.record)
            return true
        },
        //报关单单价总价联动
        totalEnter() {
            // //新增的不触发修改的触发record.price
            let goodsCount = (this.record.price * this.record.goodsCount).toFixed(2)
            let totalInt = parseFloat(this.record.total)
            let goodsCountInt = parseFloat(goodsCount)
            console.log(this.record.price * this.record.goodsCount == this.record.total)
            if ((!!this.record.price && !!this.record.goodsCount && !!this.record.total) &&
                (!(goodsCountInt == totalInt))) {
                this.show.unitTotal = true
                setTimeout(() => {
                    this.$refs.totalButtonRef.$refs.buttonNode.focus()
                }, 200)

            } else {
                this.focusIndex = this.focusIndex + 2
            }
        },
        /**
         * 左移
         */
        keyupLeft(){
            this.$refs.totalButton1Ref.$refs.buttonNode.focus()
        },
        /**
         * 右移
         */
        keyupright(){
            this.$refs.totalButtonRef.$refs.buttonNode.focus()
        },
        /**
         * 批量修改
         */
        batchUpdateClick(){
            if(this.$refs.xTable2.getCheckboxRecords().length>0){
                this.batchRecord={}
                this.show.batchUpdate = !this.show.batchUpdate
            }else {
                this.$message.error("请至少选择一条表体进行操作")
            }

        },
        // 根据下标替换字符
        replaceStr (str, index, char)  {
            const strAry = str.split('');
            strAry[index] = char;
            return strAry.join('');
        },
        replaceStrForVerticalBar (str, index, char)  {

            const strAry = str.split('|');
            strAry[index] = char;
            return strAry.join('|');
        },

        /**
         * 批量修改操作确认
         */
        batchUpdateCancel(){
            let checkboxList = this.$refs.xTable2.getCheckboxRecords()
            for (let i = 0; i < checkboxList.length>0; i++) {
                let find = this.dataSource.find(item => item.item === checkboxList[i].item)
                if(!!this.batchRecord.currencyCode){//币制
                    find.currencyCode = this.batchRecord.currencyCode
                    checkboxList[i].currencyCode = this.batchRecord.currencyCode
                    find.opt='U'
                }
                if(!!this.batchRecord.desCountry){//原产国(地区)
                    find.desCountry = this.batchRecord.desCountry
                    find.opt='U'
                }
                if(!!this.batchRecord.districtCode){//境内目的地
                    find.districtCode = this.batchRecord.districtCode
                    find.opt='U'
                }
                if(!!this.batchRecord.destCode){//目的地代码
                    find.destCode = this.batchRecord.destCode
                    find.opt='U'
                }
                if(!!this.batchRecord.faxTypeCode){//征免方式
                    find.faxTypeCode = this.batchRecord.faxTypeCode
                    find.opt='U'
                }
                if(!!this.batchRecord.destinationCountry){//征免方式
                    find.destinationCountry = this.batchRecord.destinationCountry
                    find.opt='U'
                }
                let hsmodelList =  find.hsmodel.split("|")
                if(!!hsmodelList){

                    if(!isNaN(parseFloat(hsmodelList[0]))){
                        if(!!this.batchRecord.destinationPPLX){
                            // find.hsmodel = this.replaceStr (find.hsmodel,0,this.batchRecord.destinationPPLX)
                            find.hsmodel = this.replaceStrForVerticalBar(find.hsmodel,0,this.batchRecord.destinationPPLX)
                            find.hsmodel = find.hsmodel
                            find.opt='U'
                        }
                        if(!!this.batchRecord.destinationXHQK){
                            // find.hsmodel = this.replaceStr (find.hsmodel,2,this.batchRecord.destinationXHQK)
                            find.hsmodel = this.replaceStrForVerticalBar (find.hsmodel,1,this.batchRecord.destinationXHQK)
                            find.hsmodel = find.hsmodel
                            find.opt='U'
                        }
                    }else{
                        if(!!this.batchRecord.destinationPPLX&&!!this.batchRecord.destinationXHQK){
                            find.hsmodel = this.batchRecord.destinationPPLX +"|"+this.batchRecord.destinationXHQK+"|"+find.hsmodel
                            find.opt='U'
                        }
                    }
                }

                if(!!this.batchRecord.goodsAttr){//货物属性
                    find.goodsAttr = this.batchRecord.goodsAttr
                    find.opt='U'
                }
                if(!!this.batchRecord.purpose){//用途
                    find.purpose = this.batchRecord.purpose
                    find.opt='U'
                }


            }
            this.$refs.xTable2.updateData(this.dataSource)
            this.show.batchUpdate = !this.show.batchUpdate
            this.$forceUpdate()
        },
        /**
         * 多选
         */
        containerIdOnChange(e) {
            let str = this.record.containerId.split('');
            for (var i = 0; i < str.length; i++) {
                if (str[i].charCodeAt() >= 65 && str[i].charCodeAt() <= 90 && !e.target.checked) {
                    str[i] = str[i].toLowerCase();
                } else if (e.target.checked) {
                    str[i] = str[i].toUpperCase();
                }
            }
            let containerIdStr = str.toString().replace(/,/g, "");//取消字符串中出现的所有逗号
            this.record.containerId = containerIdStr
        },
        /**
         * 总价失焦
         */
        totalBlur() {

            this.record.total = (this.record.price * this.record.goodsCount).toFixed(2)

        },
        /**
         * 成交数量回车模态窗
         */
        handleCancel() {
            this.show.unitTotal = false
            setTimeout(() => {
                this.focusIndex = this.focusIndex + 1
            }, 20)
        },
        decSourceClick() {
            if (this.selectedRowKeys.length == 1) {
                let option = this.dataSource.filter(el => {
                    return el.docuCode == this.selectedRowKeys
                })
                let recordData = option[0]
                let strDocuCode = "YERFJkd"
                if (strDocuCode.indexOf(recordData.docuCode) != -1) {
                    this.show.decsource = !this.show.decsource
                } else {
                    this.$message.warning('当前随附单证代码不可录入原产地！');
                }
            } else {
                this.$message.warning('请选择一条记录！');
            }

        },
        priceEnter() {

        },
        /**
         * 修改单价
         */
        unitButton(e) {
            if (!!this.record.goodsCount && !!this.record.total) {
                this.record.price = this.record.total / this.record.goodsCount
                this.record.price = this.record.price.toFixed(4);
            }

            // this.$refs.totalButtonRef.$refs.buttonNode.preventDefault()
            this.show.unitTotal = false
            // 模态窗需要手动加1
            if (e == 1) {
                setTimeout(() => {
                    // this.$forceUpdate()
                    this.focusIndex = 70//成交计量单位
                }, 200)
            }

        },
        /**
         * 修改总价
         */
        totalButton(e) {
            if (!!this.record.goodsCount && !!this.record.price) {
                this.record.total = this.record.goodsCount * this.record.price
                this.record.total = this.record.total.toFixed(2);
            }
            this.show.unitTotal = false
            //模态窗需要手动加1
            if (e == 1) {
                setTimeout(() => {
                    this.focusIndex = 70//成交计量单位
                }, 20)
            }

        },

        decListVerify() {
            if (this.record['hscode'] == '' || this.record['hscode'] == null || this.record['hscode'] === undefined) {
                this.$message.warning("商品编号为空不允许保存！")
                return true;
            } else if (this.record['hsname'] == '' || this.record['hsname'] == null || this.record['hsname'] === undefined) {
                this.$message.warning("商品名称为空不允许保存！")
                return true;
            }
        },
        listEmpty() {
            this.record = {}
            this.resetTableSelection()
        },
        /**
         * 新增模板信息
         */
        templeteHandleAdd(e) {
            if (!!this.handleAdd) {
                // todo: add前已有在编辑的内容, 先做验证
                if (this.dataSource.length < 1) {
                    this.record = {
                        id: idGenerator--,
                        item: this.dataSource.length + 1,
                        hscode: '',
                        ciqName: '',
                        //集装箱
                        goodsNo: ''
                    }
                    if (e != 1) {
                        //新增的刷新静态界面
                        this.$refs.decCiqName.dataSource = []
                        this.$refs.decGoodsLimittype.dataSource = []
                        this.$refs.decGoodsAttrs.selectedRows = []
                        this.$refs.decGoodsAttrs.selectedRowKeys = []
                        //规格型号处理
                        this.decHsmodels.decHsmodelDialog = ''
                        this.$refs.decHsmodel.dataSource = []
                        this.$refs.decHsmodel.hsModelStr = ''
                        this.$refs.decHsmodel.hsModelZj = "(0/255字节)"
                        this.show.decHsmodelPage = false
                    }
                    //判断备案序号是否可编辑
                    this.recordItemMethod()
                } else {
                    this.$message.error('只允许新增一条报关单模板表体')
                }
            } else {
                this.record = {
                    id: idGenerator--,
                    item: 1,
                }
            }
        },
        handleAdd(e) {
            // todo: add前已有在编辑的内容, 先做验证
            this.dataSource
            this.record = {
                id: idGenerator--,
                item: this.dataSource.length + 1,
                hscode: '',
                ciqName: '',
                //集装箱
                goodsNo: '',
                //拼箱标识
                lclFlag: '',
                //集装箱规格
                containerMd: ''
            }
            //等于1说明是表体新增数据
            if (e != 1) {
                //新增的刷新静态界面
                this.$refs.decCiqName.dataSource = []
                this.$refs.decGoodsLimittype.dataSource = []
                this.$refs.decGoodsAttrs.selectedRows = []
                this.$refs.decGoodsAttrs.selectedRowKeys = []
                //规格型号处理
                this.decHsmodels.decHsmodelDialog = ''
                this.$refs.decHsmodel.dataSource = []
                this.$refs.decHsmodel.hsModelStr = ''
                this.$refs.decHsmodel.hsModelZj = "(0/255字节)"
                this.show.decHsmodelPage = false
                //判断备案序号是否可编辑
                this.recordItemMethod()
                //法定第二单位为空置灰时，法定第二数量也要置灰
                if(this.record.unit2==""||this.record.unit2==null||typeof(this.record.unit2)==undefined){
                    this.count2Readonly = true
                }else{
                    this.count2Readonly = false
                }

            }

            if(e){
                this.record.destinationCountry='CHN'
            }
            // this.count2Readonly = false
					//如果新增报关单并且报关制单地区为烟台新增默认表体的境内货源地37069-烟台其他
					if (this.$route.query.decNew&&this.$route.query.district==1) {
						this.record.districtCode = '37069'
					}
					//新增商品，焦点聚焦到商品名称
					this.focusIndex=64
        },

        /**
         * 监听规格型号
         */
        hsmodelChange(e) {
            this.hsmodelMethod()
        },
        getContainerData(e) {
            if (!!e) {
                for (let i = 0; i < e.length; i++) {
                    let eId = 0 - this.dataSource.length - i
                    if (eId == 0) {
                        eId = -1
                    }
                    e[i].id = eId
                    e[i].opt = 'I'
                    this.dataSource.push(e[i])
                }
            }
        },
        /**
         * 商品编码
         */
        hscodeChange(newValue, oldValue) {
            if (!!newValue) {
                this.focusIndex = 65
                if (this.record.hscode.length > 10) {
                    this.$message.error("商品编号不可大于10位")
                }
                this.hsmodelMethod()
            }
        },
        /**
         * 规格型号的删除
         */
        hsmodelMethod() {
            //判断备案序号是否可编辑
            this.recordItemMethod()
            //规格型号的传递
            let hsmodelStr = '';
            // if (!!this.record.hsmodel) {
            //     hsmodelStr = this.record.hsmodel.toString().split("|")
            // } else {
            this.$refs.decHsmodel.decHsCodeMessage(this.record.hscode, hsmodelStr)
            // }
        },
        /**
         * 批量导入
         */
        importTemplate() {
            this.$refs.modalForm.add()
            this.$refs.modalForm.title = '新增'
            this.$refs.modalForm.disableSubmit = false
        },
        //保存暂时有问题
        getDataSource(data) {
            for (let i = 0; i < data.length; i++) {
                let arr = {path: "", attachmentName: "", attachmentId: "", dclId: ""};
                arr.path = data[i].path
                arr.attachmentName = data[i].fileName
                arr.attachmentId = this.record.attachmentId
                arr.dclId = this.record.id
                this.dataSource.push(arr)
                this.dataSourcess.push(arr)
            }

        },
        /**
         * 删除
         */
        async handleDocuCodeDelete(e) {
            if (!this.selectedRows[0]) {
                return this.$message.warning('请至少选择一条记录！');
            }

            let _this = this
            // 删除
            this.$confirm({
                title: "确认删除",
                content: "是否删除选中数据?",
                onOk() {
                    if(e==1){
                        _this.dataSource = _this.dataSource.filter(d => !~_this.selectedRowKeys.indexOf(d.docuCode))
                    }else{
                        //去除_this.dataSource里containerId为空的
                        _this.dataSource = _this.dataSource.filter(d => d.containerId!=null)
                        _this.dataSource = _this.dataSource.filter(d => !~_this.selectedRowKeys.indexOf(d.containerId))
                    }
                    _this.record = {}
                    _this.sortDataSource()
                    _this.$message.info('已删除')
                    _this.resetTableSelection()
                    _this.selectedRows = []
                    _this.sortDataSource()
                }
            })
        },
        /**
         * 删除
         */
        async handleDelete() {
            this.$refs.xTable2.removeCheckboxRow();
            this.fulltableData = this.$refs.xTable2.getTableData();

            let temp = this.$refs.xTable2.getTableData().fullData;
            for (let i = 0; i < temp.length; i++) {
                // 这里的i是代表数组的下标
                if (!temp[i].opt | (temp[i].opt == "U")) {
                    temp[i].opt = "U";
                    temp[i].item = i + 1;
                }

                if (temp[i].opt == "I") {
                    temp[i].opt = "I";
                    temp[i].item = i + 1;
                }
            }
            await this.$refs.xTable2.reloadData(temp);
            this.fulltableData = this.$refs.xTable2.getTableData();
            this.dataSource = this.fulltableData.fullData
        },
        /**
         * 列选择变更
         * @param selectedRowKeys 选中列的rowKey
         * @param selectedRows 选中的列
         */
        handleTableSelectionChange(selectedRowKeys, selectedRows) {
            if (selectedRowKeys.length == 1) {
                this.dataSource
                let option = this.dataSource.filter(el => {
                    return el.containerId == selectedRowKeys[0]
                })
                this.record = option[0]
            }

            this.selectedRowKeys = selectedRowKeys
            this.selectedRows = selectedRows
        },

        handDocuCodeChange(selectedRowKeys, selectedRows) {
            if (selectedRowKeys.length == 1) {
                this.dataSource
                let option = this.dataSource.filter(el => {
                    return el.docuCode == selectedRowKeys[0]
                })
                this.record = option[0]
            }

            this.selectedRowKeys = selectedRowKeys
            this.selectedRows = selectedRows
        },
        /**
         * 总价失焦
         */
        unitButtonBlur(e) {
            //总价失焦
            if (e == 3) {
                if (!!this.record.goodsCount && !!this.record.price) {
                    this.record.price = this.record.total / this.record.goodsCount
                    this.record.price = this.record.price.toFixed(4);
                }
            } else if (e == 2) { //单价失焦
                if (!!this.record.goodsCount && !!this.record.price) {
                    this.record.total = this.record.goodsCount * this.record.price
                    this.record.total = this.record.total.toFixed(2)
                }
            } else if (e == 1) { //数量失焦
                // //新增的不触发修改的触发record.price
                //
                let goodsCount = (this.record.price * this.record.goodsCount).toFixed(2)
                let totalInt = parseFloat(this.record.total)
                let goodsCountInt = parseFloat(goodsCount)
                console.log(this.record.price * this.record.goodsCount == this.record.total)
                if ((!!this.record.price && !!this.record.goodsCount && !!this.record.total) &&
                    (!(goodsCountInt == totalInt))) {
                    this.show.unitTotal = true
                }
                // if(!!this.record.goodsCount&&!!this.record.price&& !!this.record.total){
                //     this.show.unitTotal = true
                // }
            }
        },
        returnBackFnCount(){
            let count = this.unit1unit2Qk()
            let countSplit = count.split(',')
            let countSplit1 = countSplit[0]
            let countSplit2 = countSplit[1]

            if(this.record.unitCode==this.record.unit1&&countSplit1.length==0){
                this.record.count1=this.record.goodsCount
            }else if(this.record.unitCode==this.record.unit2&&countSplit2.length==0){
                this.record.count2=this.record.goodsCount
            }
            //3.除上面1和2的逻辑，其他都默认法定第一/二数量=成交数量。
            let bioList =['007','054','044']
            let gramList =['036','035']
            if(bioList.indexOf(this.record.unitCode)> -1){
                if(bioList.indexOf(this.record.unit1)== -1&&!this.isEmpty(this.record.unit1)&&countSplit1.length==0){
                    this.record.count1=this.record.goodsCount
                }
                if(bioList.indexOf(this.record.unit2)== -1&&!this.isEmpty(this.record.unit2)&&countSplit2.length==0){
                    this.record.count2=this.record.goodsCount
                }
            }else if(gramList.indexOf(this.record.unitCode)> -1){
                if(gramList.indexOf(this.record.unit1)== -1&&!this.isEmpty(this.record.unit1)&&countSplit1.length==0){
                    this.record.count1=this.record.goodsCount
                }
                if(gramList.indexOf(this.record.unit2)== -1&&!this.isEmpty(this.record.unit2)&&countSplit2.length==0){
                    this.record.count2=this.record.goodsCount
                }
            }else{
                if(!this.isEmpty(this.record.unit1)&&countSplit1.length==0){
                    this.record.count1=this.record.goodsCount
                }
                if(!this.isEmpty(this.record.unit2)&&countSplit2.length==0){
                    this.record.count2=this.record.goodsCount
                }
            }
            this.transitionUnit()
        },
        lawfUnitcdDlur(){

            this.transitionUnit()
        },
        transitionUnit(){
            let count = this.unit1unit2Qk()
            let countSplit = count.split(',')
            let countSplit1 = countSplit[0]
            let countSplit2 = countSplit[1]
            if(this.record.unitCode=='007'){//个
                if(this.record.unit1=='054'){//千个
                    this.record.count1=this.record.goodsCount/1000
                }
                if(this.record.unit2=='054'){//千个
                    this.record.count2=this.record.goodsCount/1000
                }
                if(this.record.unit1=='044'){//百片
                    this.record.count1=this.record.goodsCount/100
                }
                if(this.record.unit2=='044'){//百片
                    this.record.count2=this.record.goodsCount/100
                }
            }else if(this.record.unitCode=='054'){//千个
                if(this.record.unit1=='007'){//个
                    this.record.count1=this.record.goodsCount*1000
                }
                if(this.record.unit2=='007'){//个
                    this.record.count2=this.record.goodsCount*1000
                }
            }else if(this.record.unitCode=='044'){//百片
                if(this.record.unit1=='007'){//个
                    this.record.count1=this.record.goodsCount*100
                }
                if(this.record.unit2=='007'){//个
                    this.record.count2=this.record.goodsCount*100
                }
            }
            if(this.record.unitCode=='036'){//克
                if(this.record.unit1=='035'&&countSplit1.length==0){//千克
                    this.record.count1=this.record.goodsCount/1000
                }
                if(this.record.unit2=='035'&&countSplit2.length==0){//千克
                    this.record.count2=this.record.goodsCount/1000
                }
            }else if(this.record.unitCode=='035'){
                if(this.record.unit1=='036'&&countSplit1.length==0){//克
                    this.record.count1=this.record.goodsCount*1000
                }
                if(this.record.unit2=='036'&&countSplit2.length==0){//千克
                    this.record.count2=this.record.goodsCount*1000
                }
            }
            //法定第一计量单位unit1 法定第二unit2 申报计量单位unitCode
            //成交计量单位是件 法一法二是千个
            if(this.record.unitCode=='011'){//件
                if(this.record.unit1=='054'){//千个
                    this.record.count1=this.record.goodsCount/1000
                }
                if(this.record.unit2=='054'){//千个
                    this.record.count2=this.record.goodsCount/1000
                }
            }else if(this.record.unitCode=='054'){//千个
                if(this.record.unit1=='011'){//件
                    this.record.count1=this.record.goodsCount*1000
                }
                if(this.record.unit2=='011'){//件
                    this.record.count2=this.record.goodsCount*1000
                }
            }
        },
        /**
         * 复制
         */
        async handleCopy() {
            let that = this;
            //获取选择数据
            let selectRecords = this.$refs.xTable2.getCheckboxRecords();

            if (selectRecords.length < 1) {
                that.$message.warning("请勾选要复制的数据");
            }
            if (selectRecords.length) {
                let row = JSON.parse(JSON.stringify(selectRecords[0]));
                // Object.keys(row).forEach((key) => (row[key] = ''))
                let num = await this.$refs.xTable2.getTableData().fullData.length + 1;
                row.item = num;
                row.decId = this.$route.query.id;
                row.opt = "I";
                if(!!row.decEcoRelation){
                    row.decEcoRelation.opt = "I"
                    row.decEcoRelation.id = ''
                    row.decEcoRelation.decId =  row.decId
                    row.decEcoRelation.ecoGno=  row.item
                    row.decEcoRelation.decGno = row.item
                }
                row.id = idGenerator--;
                that.record = row;


                let {row: newRow} = await that.$refs.xTable2.insertAt(this.record, -1);

                that.record = newRow;
                this.$refs.xTable2.scrollTo(0, num * 36);


                that.dataSource = that.$refs.xTable2.getTableData().tableData;
                this.SortingNo();
            }
        },
        //自动排序
        SortingNo() {
            this.$refs.xTable2.sort("item", "asc");
        },


        async handleMoveUp() {
            if(this.$refs.xTable2.getCheckboxRecords().length==0){
                this.$message.warning("请选中一条数据！")
            }
            let selectedrow = this.$refs.xTable2.getCheckboxRecords()[0];

            let allSelectedrow = this.$refs.xTable2.getTableData().visibleData;

            let index = allSelectedrow.indexOf(selectedrow);
            if (index > 0 && index < this.$refs.xTable2.getTableData().fullData.length) {
                let ini = index - 1;
                let tempss = allSelectedrow[ini];
                let temp2 = allSelectedrow[index];
                if (this.isEmpty(tempss.opt) || tempss.opt == "U") {
                    tempss.opt = "U";
                    tempss.item = index + 1;
                }

                if (this.isEmpty(temp2.opt) || temp2.opt == "U") {
                    temp2.opt = "U";
                    temp2.item = index;
                }
                if (tempss.opt == "I") {
                    tempss.opt = "I";
                    tempss.item = index + 1;
                }
                if (temp2.opt == "I") {
                    temp2.opt = "I";
                    temp2.item = index;
                }

                allSelectedrow[index - 1] = temp2;
                allSelectedrow[index] = tempss;
                await this.$refs.xTable2.reloadData(allSelectedrow);
                this.dataSource = allSelectedrow;

            }
        },

        async handleMoveDown() {
            if(this.$refs.xTable2.getCheckboxRecords().length==0){
                this.$message.warning("请选中一条数据！")
            }
            let selectedrow = this.$refs.xTable2.getCheckboxRecords()[0];

            let allSelectedrow = this.$refs.xTable2.getTableData().fullData;
            let index = allSelectedrow.indexOf(selectedrow);
            if (index < allSelectedrow.length) {
                let temp = allSelectedrow[index];
                let temp2 = allSelectedrow[index + 1];
                if (this.isEmpty(temp.opt) | (temp.opt == "U")) {
                    temp.opt = "U";
                    temp.item = index + 2;
                }
                if (this.isEmpty(temp2.opt) | (temp.opt == "U")) {
                    temp2.opt = "U";
                    temp2.item = index + 1;
                }
                if (temp.opt == "I") {
                    temp.opt = "I";
                    temp.item = index + 2;
                }
                if (temp2.opt == "I") {
                    temp2.opt = "I";
                    temp2.item = index + 1;
                }

                // this.dataSource[index] = temp2;
                // this.dataSource[index + 1] = temp;
                allSelectedrow[index] = temp2;
                allSelectedrow[index + 1] = temp;
                await this.$refs.xTable2.reloadData(allSelectedrow);
                this.dataSource = allSelectedrow;
            }
        },
        /**
         * 插入
         */
        async handleInsert() {

            let checkboxRecords = this.$refs.xTable2.getCheckboxRecords()
            if (checkboxRecords.length != 1) {
                return this.$message.warning('请选择一行数据')
            }
            // let selectedrowCheckbox = this.$refs.xTable2.getCheckboxRecords()[0];
            // let allSelectedrow = this.$refs.xTable2.getTableData().fullData;
            // let selectedrow = allSelectedrow.indexOf(selectedrowCheckbox);
            // let record = {
            //     id: idGenerator--,
            //     item: selectedrow+2
            // }
            // if(allSelectedrow.length==selectedrow+1){
            //     let { row: newRow } = await this.$refs.xTable2.insertAt(record, allSelectedrow[-1])
            //     await this.$refs.xTable2.setActiveCell(newRow, 'sex')
            // }else{
            //     let { row: newRow } = await this.$refs.xTable2.insertAt(record, allSelectedrow[selectedrow+1])
            //     await this.$refs.xTable2.setActiveCell(newRow, 'sex')
            // }

            let selectedrowCheckbox = this.$refs.xTable2.getCheckboxRecords()[0];
            let allSelectedrow = this.$refs.xTable2.getTableData().fullData;
            let selectedrow = allSelectedrow.indexOf(selectedrowCheckbox);
            let index = this.dataSource.findIndex(item => item.id === selectedrowCheckbox.id);
            let newRecord = {
                id: idGenerator--,
                item: index + 1
            }
            this.dataSource.splice(index + 1, 0, newRecord)
            this.record = newRecord
            this.sortDataSource(index)
            this.$forceUpdate()
        },
        decCiqNameClick(e) {
        },
			handleHsmodelEdit(){
					this.$refs.decHsmodel.title = '重新归类'
				this.$refs.decHsmodel.disabled = false
				this.show.decHsmodel = !this.show.decHsmodel
				//传递进出口
				this.imSignShows = this.imSignShowList
				this.focusIndex = 67
			},
			/**
			 * 批量修改
			 */
			handleBatchEdit(){
				this.$refs.decListBatchEditRef.add()
				this.$refs.decListBatchEditRef.title = '批量修改报关单表体信息'
				this.$refs.decListBatchEditRef.disableSubmit = false
			},
			handleHsmodelDetail(){
				this.$refs.decHsmodel.title = '归类查看'
				this.$refs.decHsmodel.disabled = true
				this.show.decHsmodel = !this.show.decHsmodel
				//传递进出口
				this.imSignShows = this.imSignShowList
				this.focusIndex = 67
			},
			async handleBulkEdit(){
				var that=this;
				this.show.bulkEdit = true
				this.$nextTick(async () => {
					if(this.spreadsheet===null){
						const enumList=['erp_units,name,code,1=1 order by code asc'
							,'erp_currencies,name,code,currency,1=1'
							,'GBDQ-DEC','ZJMSFS','erp_units,name,code,1=1'
							,'erp_districts,name,code,del_Flag=0','YT','XZQH']
						//forEach 不支持 async/await 的并发控制 mlf
						//使用Promise.all
						await Promise.all(enumList.map(async enumitem => {
							const res = await ajaxGetDictItems(enumitem, null);
							if (res.success) {
								res.result.forEach(item => {
									switch (enumitem) {
										case 'erp_units,name,code,1=1 order by code asc':
											this.unitList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'erp_currencies,name,code,currency,1=1':
											this.currencieList.push({ id: item.name, name: item.label, synonym: [item.name + '-' + item.label] });
											break;
										case 'GBDQ-DEC':
											this.GBDQDECList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'ZJMSFS':
											this.GZJMSFSList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'erp_units,name,code,1=1':
											this.erpunitList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'erp_districts,name,code,del_Flag=0':
											this.districtList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'YT':
											this.YTList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
										case 'XZQH':
											this.XZQHList.push({ id: item.value, name: item.label, synonym: [item.value + '-' + item.label] });
											break;
									}
								});
							}
						}));
						let List = [];
						let gQty_sum = 0;
						let declTotal_sum = 0;
						let qty1_sum = 0;
						let qty2_sum = 0;
						let NetWeight_sum = 0;
						for (let index = 0; index < this.dataSource.length; index++) {
							let _List = that.conversion(this.dataSource[index]);
							gQty_sum = this.add(gQty_sum, that.numFilter(this.dataSource[index].goodsCount));
							declTotal_sum = this.add(declTotal_sum, that.numFilter(this.dataSource[index].total));
							qty1_sum = this.add(qty1_sum, that.numFilter(this.dataSource[index].count1));
							qty2_sum = this.add(qty2_sum, that.numFilter(this.dataSource[index].count2));
							if (this.dataSource[index].unit1 == "千克") {
								NetWeight_sum = this.add(NetWeight_sum, that.numFilter(this.dataSource[index].count1));
							}
							else if (this.dataSource[index].unit1 == "克") {
								NetWeight_sum = this.add(NetWeight_sum, that.numFilter(this.dataSource[index].count1) / 1000);
							}
							else if (this.dataSource[index].unit2 == "千克") {
								NetWeight_sum = this.add(NetWeight_sum, that.numFilter(this.dataSource[index].count2));
							}
							else if (this.dataSource[index].unit2 == "克") {
								NetWeight_sum = this.add(NetWeight_sum, that.numFilter(this.dataSource[index].count2) / 1000);
							}
							List.push(_List);
						}
						that.gQty_sum=gQty_sum;
						that.declTotal_sum=declTotal_sum;
						that.qty1_sum=qty1_sum;
						that.qty2_sum=qty2_sum;
						that.NetWeight_sum=NetWeight_sum;
						that.rowNumber_sum=this.dataSource.length;
						var option= {
							data: List,
							tableOverflow: true,
							allowDeletingAllRows: true,//允许删除所有行
							defaultColAlign: 'left',
							autoIncrement: false,//自动增量
							about: false,//关于

							allowToolbar: false,
							minDimensions: [10, 30],
							// lazyLoading: true,
							overflow:'scroll',
							tableWidth: "1300px",
							tableHeight: '560px',
							freezeColumns: 3,
							columns: [
								{ type: 'checkbox', title: ' ', width: 20 },
								{ type: 'text', title: '商品编码', width: 120 },
								{ type: 'text', title: '商品名称', width: 120 },
								{ type: 'dropdown', title: '品牌类型', width: 120, source: this.BrandType, autocomplete: true },
								{ type: 'dropdown', title: '享惠情况', width: 120,  source:this.BenefitSituation, autocomplete: true },
								{ type: 'text', title: '规格型号（申报要素）', width: 200 },
								{ type: 'text', title: '成交数量', width: 120 },
								{ type: 'dropdown', title: '成交单位', width: 120, source: this.unitList, autocomplete: true },
								{ type: 'text', title: '单价', width: 120 },
								{ type: 'text', title: '总价', width: 120 },
								{ type: 'dropdown', title: '币制', width: 120, source: this.currencieList, autocomplete: true },
								{ type: 'dropdown', title: '原产国', width: 120, source: this.GBDQDECList, autocomplete: true },
								{ type: 'dropdown', title: '最终目的国', width: 120, source: this.GBDQDECList, autocomplete: true },
								{ type: 'dropdown', title: '征免方式', width: 120, source: this.GZJMSFSList, autocomplete: true },
								{ type: 'text', title: '净重（可选）', width: 120 },
								{ type: 'text', title: '法一数量', width: 120 },
								{ type: 'dropdown', title: '法一单位', width: 120, source: this.erpunitList , readOnly: true},
								{ type: 'text', title: '法二数量', width: 120 },
								{ type: 'dropdown', title: '法二单位', width: 120, source: this.unitList , readOnly: true},
								{ type: 'dropdown', title: '境内货源地', width: 120, source: this.districtList, autocomplete: true },
								{ type: 'dropdown', title: '产地代码', width: 120, source: this.XZQHList,  autocomplete: true },
								{ type: 'text', title: '单耗版本号', width: 120 },
								{ type: 'text', title: '货号', width: 120 },
								{ type: 'text', title: '备案序号', width: 120 },
								{ type: 'dropdown', title: '用途', width: 120, source: this.YTList,  autocomplete: true },
								{ type: 'text', title: '检验检疫名称', width: 120, readOnly: true },
								{ type: 'text', title: '检验检疫代码', width: 120, readOnly: true },
								{ type: 'text', title: '检疫货物规格', width: 120, readOnly: true },
								{ type: 'text', title: '货物属性', width: 120, readOnly: true },
								{ type: 'text', title: '产品资质', width: 120, readOnly: true },
								{ type: 'text', title: '危险货物信息', width: 120, readOnly: true },
								{ type: 'text', title: '申报要素OCR识别原文', width: 120, readOnly: true }
							],
							//加载完成事件
							onload: function () {
								//渲染统一复选框
								// console.log(that.$refs.spreadsheet.getElementsByClassName('jexcel_content').querySelector('table[class="jexcel jexcel_overflow"]'))
								var td = that.$refs.spreadsheet.getElementsByClassName('jexcel_content')[0].querySelector('table[class="jexcel jexcel_overflow"]').querySelector('thead[class="resizable"]').querySelectorAll("tr")[0].querySelectorAll('td[data-x="0"]')[0];
								if (td) {
									var inp = document.createElement('input');
									inp.setAttribute('type', 'checkbox');
									inp.setAttribute('id', 'j_checkbox');
									inp.setAttribute('ref', 'j_checkbox');
									inp.style.marginLeft = '1px';
									inp.onclick = function () {
										var checked = this.checked;
										var ColumnData = that.spreadsheet.getColumnData(0);
										if (ColumnData.length > 0) {
											ColumnData.forEach(function (item, index) {
												ColumnData[index] = checked;
											})
											that.spreadsheet.setColumnData(0, ColumnData);
										}
									}
									td.appendChild(inp);
								}
							},
							//单元格值改变之前
							onbeforechange: function (el, cell, x, y, value, opType) {
								if (value) {
									//去除首位空格
									value = that.trimThree(value);
									console.log(x)
									//商品编码
									if (x == 1) {
										var vallist = value.match(/\d/g);
										if (vallist && vallist.length > 0) {
											var sliceVallist = vallist.slice(0, 10);
											value = sliceVallist.join('');
											if (sliceVallist.length < 10) {
												that.spreadsheet.setComments(jexcel.getColumnNameFromId([x, y]), '请输入合法的税号');
												// $(cell).css("border", "1px solid red");
												console.log(cell)
												cell.style.border = '1px solid red';
											}
											else {
												that.spreadsheet.setComments(jexcel.getColumnNameFromId([x, y]), '');
												cell.style.border = '';
											}
										}
										else {
											value = "";
										}
									}
									//申报要素
									else if (x == 5) {
										if (value.length > 255) {
											that.spreadsheet.setComments(jexcel.getColumnNameFromId([x, y]), '申报要素长度不能超过255');
											cell.style.border = '1px solid red';
										}
										else {
											that.spreadsheet.setComments(jexcel.getColumnNameFromId([x, y]), '');
											cell.style.border = '';
										}
										if (opType != "copyData" && opType !='setValueFromCoords' && that.setValueFlg == false) {
											//智能识别申报要素
											var hsCode = that.spreadsheet.getValueFromCoords(1, y);
											if (hsCode && value && value.length > 10) {
												// postAction('/DecHead/dec-head/Ydt_batchExtractReportKey', {
												// 	goods:JSON.stringify([{ reportKeyOriginalText: value, hsCode: hsCode, position: 0 }])
												// }).then(res => {
												// 	if (res.success) {
												// 		value = res[0];
												// 	}
												// }).finally(()=>{
												// })
											}
										}
									}
									//成交数量、单价、总价、净重、法定第一数量、法定第二数量
									else if (x == 6 || x == 8 || x == 9 || x == 14 || x == 15 || x == 17) {
										value = value.replace(" ", "");
										var vallist = value.match(/([0-9]*)+(\.[0-9])?/g);
										if (vallist && vallist.length > 0) {
											value = vallist.join('');

										}
										else {
											value = "";
										}
									}
									that.setValueFlg = false;
									return value;
								}
							},
							//单元格值改变
							onchange: async function (instance, cell, x, y, value, oldValue, opType) {
								//商品编码
								if (x == 1) {
									if (value) {
										const auditRess = await decTeriff(value).catch(reason => this.$message.error(reason));
										if(auditRess.code==200){
											const res=auditRess.result[0];
											console.log(res.qtyunit)
											console.log(jexcel.getColumnNameFromId([16, y]))
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([16, y]), res.qtyunit, true);
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([18, y]), res.qtcunit, true);
											if (!res.qtyunit) {
												that.spreadsheet.setValue(jexcel.getColumnNameFromId([15, y]), "", true);
											}
											if (!res.qtcunit) {
												that.spreadsheet.setValue(jexcel.getColumnNameFromId([17, y]), "", true);
												that.spreadsheet.getCellFromCoords(17, y).className = "readonly";
											}
											else {
												that.spreadsheet.getCellFromCoords(17, y).className = "";
											}
										}
										else {
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([15, y]), "", true);
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([16, y]), "", true);
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([17, y]), "", true);
											that.spreadsheet.setValue(jexcel.getColumnNameFromId([18, y]), "", true);
											that.spreadsheet.getCellFromCoords(17, y).className = "readonly";
										}
									}
								}
								//品牌类型
								else if (x == 3) {
									let gModel = that.spreadsheet.getValue(jexcel.getColumnNameFromId([5, y]));
									if (value || gModel) {
										let gModelList = gModel.split('|');
										if (gModelList[0] != value) {
											gModelList[0] = value;
											gModel = gModelList.join('|');
											//table.setValue(jexcel.getColumnNameFromId([5, y]), gModel);
											that.spreadsheet.setValueFromCoords(5, y, gModel);
										}
									}
								}
								//享惠情况
								else if (x == 4) {
									let gModel = that.spreadsheet.getValue(jexcel.getColumnNameFromId([5, y]));
									if (value || gModel) {
										let gModelList = gModel.split('|');
										if (gModelList[1] != value) {
											gModelList[1] = value;
											gModel = gModelList.join('|');
											//table.setValue(jexcel.getColumnNameFromId([5, y]), gModel);
											that.spreadsheet.setValueFromCoords(5, y, gModel);
										}
									}
								}
								//规格型号
								else if (x == 5) {
									if (value) {
										const type = that.spreadsheet.getValueFromCoords(3, y);
										const type1 = that.spreadsheet.getValueFromCoords(4, y);
										let gModelList = value.split('|');
										if (gModelList[0] != type) {
											that.spreadsheet.setValueFromCoords(3, y, gModelList[0]);
										}
										if (gModelList[1] != type1) {
											that.spreadsheet.setValueFromCoords(4, y, gModelList[1], true);
										}
									}
									else {
										that.spreadsheet.setValueFromCoords(3, y, "");
										that.spreadsheet.setValueFromCoords(4, y, "", true);
									}
								}
								//成交数量
								else if (x == 6) {
									//阻止其余改变
									if (!that.stopChange) {
										that.stopChange = true;
									}
									else if (that.stopChange) {
										that.stopChange = false;
										return;
									}
									//单价
									var UnitPrice = that.numFilter(that.spreadsheet.getValueFromCoords(8, y));
									var _value = that.multiply(that.numFilter(value), UnitPrice, 2);
									that.spreadsheet.setValueFromCoords(9, y, _value);
									//在批量编辑模式下，输入成交数量后，只要法定第二单位不是千克，法定第二数量自动带出和成交数量一致。 2023.10.09 mlf
									if (!!that.spreadsheet.getValueFromCoords(18, y) &&that.spreadsheet.getValueFromCoords(18, y) != '035') {
										that.spreadsheet.setValue(jexcel.getColumnNameFromId([17, y]), value, true);
									}

									//汇总
									var gQty_sum = 0;
									var declTotal_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 6) {
												gQty_sum = that.add(gQty_sum, that.numFilter(SheetData[i][j]));
											}
											else if (j == 9) {
												declTotal_sum = that.add(declTotal_sum, that.numFilter(SheetData[i][j]));
											}
										}
									}
									that.gQty_sum=gQty_sum;
									that.declTotal_sum=declTotal_sum;
								}
								//单价
								else if (x == 8) {
									//阻止其余改变
									if (!that.stopChange) {
										that.stopChange = true;
									}
									else if (that.stopChange) {
										that.stopChange = false;
										return;
									}
									//成交数量
									var quantity = that.numFilter(that.spreadsheet.getValueFromCoords(6, y));
									var _value = that.multiply(that.numFilter(value), quantity, 2);
									that.spreadsheet.setValueFromCoords(9, y, _value);
									//汇总
									var declTotal_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 9) {
												declTotal_sum = that.add(declTotal_sum, that.numFilter(SheetData[i][j]));
											}
										}
									}
									that.declTotal_sum=declTotal_sum;
								}
								//总价
								else if (x == 9) {
									//阻止其余改变
									if (!that.stopChange) {
										that.stopChange = true;
									}
									else if (that.stopChange) {
										that.stopChange = false;
										return;
									}
									//都会触发改变事件 待研究
									//成交数量
									var quantity = that.numFilter(that.spreadsheet.getValueFromCoords(6, y));
									that.spreadsheet.setValueFromCoords(8, y, that.numFilter(that.divide(that.numFilter(value), quantity, 4)));
									//汇总
									var declTotal_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 9 && SheetData[i][j]) {
												declTotal_sum = that.add(parseFloat(declTotal_sum), parseFloat(SheetData[i][j]));
											}
										}
									}
									that.declTotal_sum=declTotal_sum;
								}
								//净重
								else if (x == 14) {
									//汇总
									var NetWeight_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 14 && SheetData[i][j]) {
												NetWeight_sum = that.add(NetWeight_sum, that.numFilter(SheetData[i][j]));
											}
										}
									}
									that.NetWeight_sum=NetWeight_sum;
									var unit1 = that.spreadsheet.getValue(jexcel.getColumnNameFromId([16, y]));
									var unit2 = that.spreadsheet.getValue(jexcel.getColumnNameFromId([18, y]));
									if (unit1 == "035") {
										that.spreadsheet.setValue(jexcel.getColumnNameFromId([15, y]), value);
									}
									else if (unit1 == "036") {
										that.spreadsheet.setValue(jexcel.getColumnNameFromId([15, y]), that.numFilter(value) / 1000);
									}
									else if (unit2 == "035") {
										that.spreadsheet.setValue(jexcel.getColumnNameFromId([17, y]), value);
									}
									else if (unit2 == "036") {
										that.spreadsheet.setValue(jexcel.getColumnNameFromId([17, y]), that.numFilter(value) / 1000);
									}
								}
								//法一数量
								else if (x == 15) {
									//汇总
									var qty1_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 15) {
												qty1_sum = that.add(qty1_sum, that.numFilter(SheetData[i][j]));
											}
										}
									}
									that.qty1_sum=qty1_sum;
								}
								//法二数量
								else if (x == 17) {
									//汇总
									var qty2_sum = 0;
									var SheetData = that.spreadsheet.getData();
									for (var i = 0; i < SheetData.length; i++) {
										for (var j = 0; j < SheetData[i].length; j++) {
											if (j == 17) {
												qty2_sum = that.add(qty2_sum, that.numFilter(SheetData[i][j]));
											}
										}
									}
									that.qty2_sum=qty2_sum;
								}
							},
							//插入行
							oninsertrow: function (el, rowNumber, numOfRows, rowRecords, insertBefore) {
								that.rowNumber_sum += 1;
								if (!insertBefore) {
									rowNumber += 1;
								}
								that.GoodList.splice(rowNumber, 0, {});
							},
							//删除行
							ondeleterow: function (el, rowNumber, numOfRows, rowRecords, insertBefore) {
								that.rowNumber_sum -= 1;
								that.GoodList.splice(rowNumber, 1);
							},
							updateTable: function (instance, cell, col, row, val, label, cellName) {
								//法二数量
								if (col == 17) {
									if (val) {
										cell.className = '';
									}
								}
							},
						}

						const spreadsheet = jexcel(this.$refs.spreadsheet, option);
						Object.assign(this, { spreadsheet })
						spreadsheet.refresh()
						//移除数据为空的数据
						for (var i = that.spreadsheet.getData().length - 1; i >= 1; i--) {
							if (that.spreadsheet.getData()[i][1]=='') {
								that.spreadsheet.deleteRow(i, 1);
							}
						}
						this.rowNumber_sum = that.spreadsheet.getData().length;
					}
				})
			},
			clearChineseName(){
				var SheetData = this.spreadsheet.getData();
					for (var i = 0; i < SheetData.length; i++) {
							for (var j = 0; j < SheetData[i].length; j++) {
									if (j == 2 && SheetData[i][j]) {
											var regex = /[\u4e00-\u9fa5]/g;
											var value = SheetData[i][j].match(regex).join('');
											this.spreadsheet.setValue(jexcel.getColumnNameFromId([j, i]), value);
									}
							}
					}
			},
			deleterow(){
				var ColumnData = this.spreadsheet.getColumnData(0);
				if (ColumnData.length > 0) {
					const isFullSelect = ColumnData.every(cell => cell === true);
					for (var i = ColumnData.length - 1; i >= 0; i--) {
						if (ColumnData[i]) {
							this.spreadsheet.deleteRow(i, 1);
						}
					}
					// 如果是“全选删除”，但还剩最后一行，清空它
					// if (isFullSelect && this.spreadsheet.getData().length === 1) {
					// 	const colCount = this.spreadsheet.getHeaders().length;
					// 	for (let j = 0; j < colCount; j++) {
					// 		this.spreadsheet.setValueFromCoords(j, 0, '');
					// 	}
					// }
				}
			},
			addrow(){
                if (this.insertrow) {
                    for (var i = 0; i < this.insertrow; i++) {
											this.spreadsheet.insertRow([], this.spreadsheet.getData().length)
                    }
                    this.spreadsheet.orderBy(1, true)
                }
			}
			,
        showClick() {
					this.$refs.decHsmodel.title = '编辑规格型号信息'
					this.$refs.decHsmodel.disabled = false
            // if (!this.show.decHsmodelPage || this.record.hsmodel == '' || this.record.hsmodel == null) {
            this.show.decHsmodel = !this.show.decHsmodel
            //传递进出口
            this.imSignShows = this.imSignShowList
            this.focusIndex = 67

            // } else {
            //     let con = '规格型号与申报规范不相符,申报规范如下:' + this.decHsmodels.decHsmodelDialog
            //
            //     let that = this
            //     this.$confirm({
            //         title: '提示信息',
            //         content: con,
            //         onOk: function () {
            //         },
            //         onCancel: function () {
            //             // that.saveSubmitLoading = false
            //         }
            //     })
            // this.$message.error("规格型号与申报规范不相符")
            // }

        },
        /**
         * 判断备案序号是否可编辑
         */
        recordItemMethod() {
            if (!!this.decRecord.recordNumber) {
                this.recordItemType = false
            } else {
                this.recordItemType = true
            }
        },
        nextFocus(index) {
            return this.focusIndex = index + 1;
        },
        keyFrom(e) {
            return this.focusIndex = this.focusIndex + 1;
        },
				//根据实体数据返回数组
				conversion(item) {
					let _List = [];
					_List[0] = false;
					_List[1] = item.hscode;//商品编码
					_List[2] = item.hsname;//商品名称
					//品牌类型
					if (item.hsmodel) {
							let gModelList = item.hsmodel.split('|');
							_List[3] = gModelList[0];
							//享惠情况
							_List[4] = gModelList[1];
					}
					_List[5] = item.hsmodel;//规格型号（申报要素）
					_List[6] = item.goodsCount;//成交数量
					_List[7] = item.unitCode;//成交单位
					_List[8] = item.price;//单价
					_List[9] = item.total;//总价
					_List[10] = item.currencyCode;//币制
					_List[11] = item.desCountry;//原产国(地区)
					_List[12] = item.destinationCountry;//最终目的国
					_List[13] = item.faxTypeCode;//征免方式
					//净重
					if (item.unit1 == "千克" && item.count1) {
							_List[14] = parseFloat(item.count1);
					}
					else if (item.unit1 == "克" && item.count1) {
							_List[14] = parseFloat(item.count1) / 1000;
					}
					else if (item.unit2 == "千克" && item.count2) {
							_List[14] = parseFloat(item.count2);
					}
					else if (item.unit2 == "克" && item.count2) {
							_List[14] = parseFloat(item.count2) / 1000;
					}
					_List[15] = item.count1;//法一数量
					_List[16] = item.unit1;//法一单位
					_List[17] = item.count2;//法二数量
					_List[18] = item.unit2;//法二单位
					_List[19] = item.districtCode;//境内货源地
					_List[20] = item.desCountry;//产地代码
					_List[21] = item.exgVersion;//单耗版本号
					_List[22] = item.tmp002;//货号
					_List[23] = item.recordItem;//备案序号
					_List[24] = item.purpose;//用途
					_List[28] = item.goodsAttr;//货物属性
					return _List;
			}
				,

        // ghostssdd() {
        //     this.$refs.selectItemGndq.focus()
        // },
        handleChangedd(value) {
            this.dataSourceType = []
            for (let option of this.dataSourcedd) {
                if (this.optionContain(option, value)) {
                    this.dataSourceType.push(option);
                }
            }
        },
        /**
         * 判断 option 是否包含value(忽略option大小写)
         * @param option   this.options.item
         * @param value    子串
         * @returns {boolean} 为 true 代表包含
         */
        optionContain(option, value) {
            // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug start
            // return !!~option.title.toLowerCase().indexOf(value) || !!~option.value.toLowerCase().indexOf(value)
            return (
                !!~option.text.toLowerCase().indexOf(value.toLowerCase()) ||
                !!~option.value.toLowerCase().indexOf(value.toLowerCase())
            );
            // 2020 12 7 ljy 解决因用户输入大写字母与渲染数据出现重复 导致antd渲染select出现的bug end
        },
        /**
         * 组件回车的回调函数
         * @param data
         */
        focusIndexMethod(data) {

            if (data >= 666) {//666算是个标识 再点击的时候加上现在减去data
                data = data - 666
            }

            if (data === 86 && !this.listBodyOther) { //征免方式回车
                setTimeout(() => {
                    this.focusIndex = 64
                    this.handleEnter()
                }, 10)
            } else if (data === 89) { //用途回车
                setTimeout(() => {
                    this.focusIndex = 64
                    this.handleEnter()
                }, 10)
            } else if (data === 92) { //随附单证代码
                // if(!!this.record.docuCode&&this.record.certCode==""){
                //     this.record.certCode = "NA"
                // }
                this.focusIndex = 90
            } else if (data === 53) { //集装箱回车
                this.focusIndex = 48
            }else if(data==63){//备案序号回车
                if(!this.decRecord.declareUnit){
                    this.$message.error("申报单位不可为空")
                    return
                }
                if(!this.decRecord.recordNumber){
                    this.$message.error("备案号不可为空")
                    return
                }
								//20250612暂时取本地手帐册数据
							let emstype = ''
							//0:料件;1:成品;2:单损耗"
							if (this.decRecord.ieFlag == 'I') {
								emstype = '1'
							}
							if (this.decRecord.ieFlag == 'E') {
								emstype = '2'
							}
							GetManualItemLocal(this.decRecord.recordNumber,this.record.recordItem,emstype)
								.then(res=>{
									if(!res.success){
                     this.$message.error(res.message)
										return
                 }
									if (!this.isEmpty(res.result.records)) {
										//提示信息
										this.$message.success('已自动带取手账册备案信息。')
										let newEms = res.result.records[0]
										    this.record.hscode=newEms.codet
										    this.record.hsname=newEms.gName
										    this.record.hsmodel=newEms.gModel
										    this.record.desCountry=newEms.countryCode
										    this.record.unitCode=newEms.unit
										    this.record.unit1=newEms.unit1
										    this.record.unit2=newEms.unit2
										    this.record.price=newEms.decPrice?newEms.decPrice:0
										    this.record.currencyCode=newEms.curr
										    this.record.goodsCount=newEms.qty?newEms.qty:0
										    this.record.total=Mul(this.record.price,this.record.goodsCount)
										    this.record.faxTypeCode=newEms.dutyMode
									}else {
										this.$message.warn('账册号'+this.decRecord.recordNumber+'下查询不到备案序号'+
										this.record.recordItem+'的备案信息')
									}
							})
							//--------先不调用树毛数据了
                // GetManualItem(this.decRecord.declareUnit,this.decRecord.recordNumber,this.record.recordItem).then(res=>{
                //     console.log(res)
                //     if(!res.success){
                //         this.$message.error(res.message)
                //         return
                //     }
                //     if(res.result.length==0){
                //         return
                //     }
                //     const data=res.result[0]
                //     this.record.hscode=data.gdecd
                //     this.record.hsname=data.gdsNm
                //     this.record.hsmodel=data.endprdGdsSpcfModelDesc
                //     this.record.desCountry=data.natcd
                //     this.record.unitCode=data.dclUnitcd
                //     this.record.unit1=data.lawfUnitcd
                //     this.record.unit2=data.secdLawfUnitcd
                //     this.record.price=data.dclUprcAmt
                //     this.record.currencyCode=data.dclCurrcd
                //     this.record.goodsCount=data.dclQty
                //     this.record.total=data.dclTotalAmt
                //     this.record.faxTypeCode=data.lvyrlfModecd
                // })
            }
            else if (data === 66) {//商品编号回车带回商品编码商品名称
                if (this.record.hscode.length <= 3) {
                    this.$message.error("商品编号不可小于4位")
                } else {
                    this.show.decTeriff = !this.show.decTeriff
                }

            } else if (data === 49) {//集装箱回车

                if (this.dataSource.length > 0) {
                    setTimeout(() => {
                        let option1 = this.dataSource.filter(el => {
                            return el.id == this.record.id
                        })
                        if (option1.length > 0) {
                            this.focusIndex = data
                            return
                        }
                        let option = this.dataSource.filter(el => {
                            return el.containerId == this.record.containerId
                        })
                        if (option.length > 0) {
                            this.$message.warning("重复的集装箱号！")
                        } else {
                            this.focusIndex = data
                        }
                    }, 200)
                } else {
                    this.focusIndex = data
                }

            } else if (data === 75) { //法定第一计量单位置灰
                this.focusIndex = 76
            }
            // else if (data === 80) {//法定第er计量单位置灰
            //     this.focusIndex = 81
            // }
						// else if (data === 79) {//最终目的国
            //     this.focusIndex = 81
            // }
            else if (data === 82&&!this.imSignShowList) {//出口没有原产地区
                this.focusIndex = 83
            }else if (data === 91) {//随附单证代码回车
                if (this.dataSource.length > 0) {
                    setTimeout(() => {
                        let option1 = this.dataSource.filter(el => {
                            return el.id == this.record.id
                        })
                        if (option1.length > 0) {
                            this.focusIndex = data
                            return
                        }
                        let option = this.dataSource.filter(el => {
                            return el.docuCode == this.record.docuCode
                        })
                        if (option.length > 0) {
                            this.$message.warning("重复的随附单证代码！")
                        } else {
                            this.focusIndex = data
                        }
                    }, 200)

                } else {
                    this.focusIndex = data
                }

            } else if(data === 645){//商品名称回车展示历史申报商品信息 -选择
							if (!this.record.hsname) {
								this.$message.error("请输入商品名称后进行代取历史申报信息")
							}else if(!this.$refs.decHsmodel.decHsmodelOk){
								this.$refs.DecHsnameChoose.showModal=true
								this.$refs.DecHsnameChoose.hscodeFocus=true//商品税号自动焦点
								this.$refs.DecHsnameChoose.selectedRowKeys=[]
								this.$refs.DecHsnameChoose.queryParam={hscode: '', hsname: this.record.hsname}
								this.$refs.DecHsnameChoose.listDecListByTenant()
								this.$nextTick(()=>{
									// console.log(this.$refs.DecHsnameChoose.getElementByClassName('ant-radio-input'))
								})

							}else {
								this.$refs.decHsmodel.decHsmodelOk=false
							}
						} else {
                this.focusIndex = data
            }
        }
				,
        unitCodeSearch(e){

            this.record.unitCode = e
            if(this.record.unit2&&e==this.record.unit2){
                this.record.count2 = this.record.goodsCount
                this.$forceUpdate()
            }else if(this.record.unit1&&e==this.record.unit1){
                this.record.count1 = this.record.goodsCount
                this.$forceUpdate()
            }
            this.lawfUnitcdDlur()
        },
			keyFromPromise1(val) {
					this.record.hsmodel = val
			},
        /**
         * 光标落点 模态窗回调
         * @param e
         * @returns {number}
         */
        keyFromPromise(e) {
         if (e === "decteriff") {
                //光标移位
                this.focusIndex = this.focusIndex + 1;
                //重新加载规格型号表
                let hsmodelList =  this.record.hsmodel?this.record.hsmodel.split("|"):''
                this.$refs.decHsmodel.decHsCodeMessage(this.record.hscode,hsmodelList)
                setTimeout(() => {
                    if(this.record.unit2==""||this.record.unit2==null){
                        this.count2Readonly = true
                    }else{
                        this.count2Readonly = false
                    }
                    this.$refs.refButton.$refs.buttonNode.click()
                }, 200)
            }else{
							//手动单独处理商品回车后的申报要素后的确认
							//商品名称focusIndex为644
							if(644==this.focusIndex||67==this.focusIndex){
								this.$refs.decHsmodel.decHsmodelOk=false
								this.$refs.decTeriff.showModal=false
								return this.focusIndex=68
							}
                return this.focusIndex = this.focusIndex + 1;
            }
        },
        /**
         * 光标落点 模态窗回调
         * @param e
         * @returns {number}
         */
        bulkkeyFromPromise(e) {
					this.spreadsheet.setValueFromCoords(2, this.selectedCell[1], this.bulkEditHsmodel.split('|')[0]);
          this.spreadsheet.setValueFromCoords(5, this.selectedCell[1], this.bulkEditHsmodel);
        //  if (e === "decteriff") {
        //         //光标移位
        //         this.focusIndex = this.focusIndex + 1;
        //         //重新加载规格型号表
        //         let hsmodelList =  this.record.hsmodel?this.record.hsmodel.split("|"):''
        //         this.$refs.decHsmodel.decHsCodeMessage(this.record.hscode,hsmodelList)
        //         setTimeout(() => {
        //             if(this.record.unit2==""||this.record.unit2==null){
        //                 this.count2Readonly = true
        //             }else{
        //                 this.count2Readonly = false
        //             }
        //             this.$refs.refButton.$refs.buttonNode.click()
        //         }, 200)
        //     }else{
				// 			//手动单独处理商品回车后的申报要素后的确认
				// 			//商品名称focusIndex为644
				// 			if(644==this.focusIndex||67==this.focusIndex){
				// 				this.$refs.decHsmodel.decHsmodelOk=false
				// 				this.$refs.decTeriff.showModal=false
				// 				return this.focusIndex=68
				// 			}
        //         return this.focusIndex = this.focusIndex + 1;
        //     }
        },
        focusButton(){

        },
        /*details-info-left----------------*/
        /**
         * 判断进出口标识
         */
        inOutFlag() {
            this.isIn = this.$route.query.ieFlag === 'I'
        },
        /**
         * 企业资质查看
         */
        copLimitPlus() {
            if (this.copLimitIndex < this.copLimitTypeList.length - 1) {
                this.copLimitIndex++
                this.record.txtEntQualifNo = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeCode
                this.record.txtEntQualifTypeCode = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeName
                this.$forceUpdate()
            }
        },
        /**
         * 企业资质查看
         */
        copLimitSubtract() {
            if (this.copLimitIndex > 0) {
                this.copLimitIndex--
                this.record.txtEntQualifNo = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeCode
                this.record.txtEntQualifTypeCode = this.copLimitTypeList[this.copLimitIndex].EntQualifTypeName
                this.$forceUpdate()
            }
        },
        /**
         * 表体回传下标值
         * @param data
         */
        focusIndexsListUpadte(data) {
            this.focusIndex = data
        },
        /**
         * 件数回调
         * @param e
         */
        packsBackFn(e) {
            let y = String(e).indexOf(".")
            if (y > -1) {
                this.record.packs = ''
            }
        },
        /**
         * 检测备案号
         */
        recordNumberChange() {
            if (this.record.recordNumber == '') {
                let that = this
                this.$confirm({
                    title: '操作确认',
                    content: '是否清空报关单表体?',
                    onOk: function () {
                        that.record.decLists = []
                        that.record = JSON.parse(JSON.stringify(that.record))
                    },
                    onCancel: function () {
                    }
                })

                this.$refs.derailsInfoLeftBotton.recordItemType = false
            }
        },
        //所需单证回调赋值
        requestCertType(requestCertTypeJson) {
            let decCertTypeStr = ''
            for (let i = 0; i < requestCertTypeJson.length; i++) {
                for (let j = 0; j < this.decCertTypeList.length; j++) {
                    if (this.decCertTypeList[j].AppCertCode === requestCertTypeJson[i].AppCertCode) {
                        decCertTypeStr = decCertTypeStr + this.decCertTypeList[j].AppCertName + ","
                    }
                }
            }
            this.record.txtrequestCertType = decCertTypeStr
        },
        /**
         * 申报日期赋空
         */
        changeDate() {
            if (this.isEmpty(this.record.appDate)) {
                this.record.appDate = ''
            }
        },
        /**
         * 起运时间赋空
         */
        changeDespDate() {
            if (this.isEmpty(this.record.despDate)) {
                this.record.despDate = ''
            } else {
                this.record.despDate = moment(this.record.despDate).format('YYYY-MM-DD')
            }
        },
        formatterlvyrlfModecd({ cellValue }) {
            return  this.formatHeader(cellValue,'ZJMSFS')
        },
        formatDestinationCountryName({ cellValue }) {
            return this.formatHeader(cellValue,'GBDQ-DEC')
        },
        formatDesCountry({ cellValue }){
            return this.formatHeader(cellValue,'GBDQ-DEC')
        },
        formatCurrencyCode({ cellValue }){
            // return this.formatHeader(cellValue,'erp_currencies,name,currency,1=1 order by currency_order desc')
            return this.formatHeader(cellValue,'BZDM')
        },
        formatUnitCode({ cellValue }){
            return this.formatHeader(cellValue,'erp_units,name,code,1=1')
        },
			formatHeader(cellValue, key) {
					let getLocalData1 = sessionStorage.getItem(key);
					if (getLocalData1 != 'undefined' && getLocalData1) {
						if (getLocalData1 != null) {
							let jsonData = JSON.parse(getLocalData1);
							let item = jsonData.find(item => item.value == cellValue)
							if (!!item) {
								let reg = new RegExp('[\u4e00-\u9fa5]+$', 'g');
								let strVal = (item.title.match(/[\u4e00-\u9fa5]/g) || []).join("");
								return strVal
							} else {
								return ""
							}
						}
					} else {
						let getLocalData2 = getDictItemsFromCache(key);
						if (getLocalData2 && Array.isArray(getLocalData2)) {
							for (let item of getLocalData2) {
								if (cellValue == item.value) {
									return item.text;
								}
							}
						}
					}

				},
        //商品库查询回调
        async  goodsPnVerifyReply(e){
            this.record.hsname = e.hsname
            this.record.hscode = e.hscode
            this.record.hsmodel = e.hsmodel
            this.record.ciqCode = e.ciqCode
            this.record.ciqName = e.ciqName
            const auditRess = await decTeriff(e.hscode).catch(reason => this.$message.error(reason));
            if (auditRess.success) {
                let tariffList = auditRess.list
                if(tariffList.length>0){
                    //成交单位默认与法定第一计量单位一致。
                    this.record.unitCode = tariffList[0].unit1
                    this.record.unit1 = tariffList[0].unit1
                    this.record.unit2 = tariffList[0].unit2
                    this.$message.success('操作成功!')
                }else{
                    this.$message.error("未找到相应的税则信息")
                }

            } else {
                this.$message.error("带取税则信息失败")
            }
            this.returnBackFnCount()
            this.unit1unit2Qk()
            this.$refs.decCiqName.decCiqNameMessage(this.record.hscode)

        },
        //如果法定第一数量或法定第二数量是千克的就取净重
        unit1unit2Qk(){
            let count = ''
            if(!this.isEmpty(this.record.netWeight)&&this.record.netWeight!=0){
                if('035'==this.record.unit1){
                    this.record.count1=this.record.netWeight
                    count = count +'unit1,'
                }else if('036'==this.record.unit1){
                    this.record.count1=this.record.netWeight*100000/100000000
                    count = count +'unit1,'
                }

                if('035'==this.record.unit2){
                    this.record.count2=this.record.netWeight
                    count = count +(count.length==0 ? ',unit2':'unit2')
                }else if('036'==this.record.unit2){
                    this.record.count2=this.record.netWeight*100000/100000000
                    count = count +(count.length==0 ? ',unit2':'unit2')
                }
            }
            return count

        },
        //点击商品库
        searchingGoodspnClick(e){
            this.$refs.pubOperateHistoryList.initDatas()
        },
        copLimitTypeRecord(val){
            this.record = val
        },

        goodsAttrStr(goodsAttr){

            if(!!goodsAttr){
                this.record.goodsAttrStr = goodsAttr
            }

        },
        concertedReciprocal(){//协定享惠
            this.focusIndex = 81
            if(!!this.record.hscode&&!!this.record.hsname){
                if(!!this.record.decEcoRelation){
                    if(this.isEmpty(this.record.decEcoRelation.id)&&this.isEmpty(this.record.decEcoRelation.ecoGno)){//没id没item说明是空
                        if(this.record.item!=1&&!!this.dataSource[0].decEcoRelation.ecoGno){
                            this.record.decEcoRelation.ecoCertNo = this.dataSource[0].decEcoRelation.ecoCertNo
                            this.record.decEcoRelation.certType = this.dataSource[0].decEcoRelation.certType
                            this.record.decEcoRelation.ecoCertCode = this.dataSource[0].decEcoRelation.ecoCertCode
                            let  decEcoRelationJson = JSON.parse(JSON.stringify( this.record.decEcoRelation))
                            this.record.decEcoRelation = decEcoRelationJson
                            this.$forceUpdate()
                        }
                    }
                }else{
                    if(this.record.item!=1&&!!this.dataSource[0].decEcoRelation){
                        if(!!this.dataSource[0].decEcoRelation.ecoGno){
                            this.record.decEcoRelation = {}
                            this.record.decEcoRelation.ecoCertNo = this.dataSource[0].decEcoRelation.ecoCertNo
                            this.record.decEcoRelation.certType = this.dataSource[0].decEcoRelation.certType
                            this.record.decEcoRelation.ecoCertCode = this.dataSource[0].decEcoRelation.ecoCertCode
                            let  decEcoRelationJson = JSON.parse(JSON.stringify( this.record.decEcoRelation))
                            this.record.decEcoRelation = decEcoRelationJson
                            this.$forceUpdate()
                        }
                    }
                }
                this.concertedReciprocalType = !this.concertedReciprocalType
            }else{
                this.$message.error("商品编码或者商品名称不能为空")
            }

        },
        txtRecordTxtChange(e){

            this.record.decEcoRelation =e
            for (let i = 0; i < this.dataSource.length; i++) {
                if(!!this.dataSource[i].decEcoRelation){
                    if(!!this.dataSource[i].decEcoRelation.decGno&&!!this.record.decEcoRelation){
                        this.dataSource[i].decEcoRelation.ecoCertNo = this.record.decEcoRelation.ecoCertNo
                        this.dataSource[i].decEcoRelation.certType = this.record.decEcoRelation.certType
                        this.dataSource[i].decEcoRelation.ecoCertCode = this.record.decEcoRelation.ecoCertCode
                        if(!!this.dataSource[i].decEcoRelation.id){
                            this.dataSource[i].decEcoRelation.opt = 'U'
                        }else{
                            this.dataSource[i].decEcoRelation.opt = 'I'
                        }
                    }
                }

            }
            this.record.opt ='U'
            this.focusIndex = 82
        },
        //归类先例
        classifyPrecedent(){
            this.$refs.classifyPrecedentList.initDatas()
        },
        classifyPromise(e){

            this.record.hsname = e.hsname
            this.record.hscode = e.hscode
            this.record.hsmodel = e.hsmodel
            this.record.ciqCode = e.ciqCode
            this.record.ciqName = e.ciqName
            this.$refs.decCiqName.decCiqNameMessage(this.record.hscode)
            let hsmodelList =  e.hsmodel.splice("|")
            this.$refs.decHsmodel.decHsCodeMessage(this.record.hscode, hsmodelList)
            this.$forceUpdate()
        },
			//选择历史的申报商品后回调
			//如一致，直接带取品名、税号、申报要素、成交单位、法一单位、法二单位等信息，焦点直接跳到征免方式 ，
			decHsnameChooseOk(rows){
				//焦点直接跳到成交数量 （之前逻辑）
				// this.focusIndex=68
				const row = rows[0]
				this.record.hsname=!this.record.hsname?row.hsname:this.record.hsname
				this.record.hscode=!this.record.hscode?row.hscode:this.record.hscode
				this.record.hsmodel=row.hsmodel
				this.record.unitCode=!this.record.unitCode?row.unitCode:this.record.unitCode
				this.record.unit1=!this.record.unit1?row.unit1:this.record.unit1
				this.record.unit2=!this.record.unit2?row.unit2:this.record.unit2
				this.record.desCountry=!this.record.desCountry?row.desCountry:this.record.desCountry
				this.record.destinationCountry=!this.record.destinationCountry?row.destinationCountry:this.record.destinationCountry
				this.record.districtCode=!this.record.districtCode?row.districtCode:this.record.districtCode
				this.record.faxTypeCode=!this.record.faxTypeCode?row.faxTypeCode:this.record.faxTypeCode

				this.$refs.decHsmodel.title = '编辑规格型号信息'
				this.$refs.decHsmodel.disabled = false
				this.show.decHsmodel=!this.show.decHsmodel
				this.$refs.decHsmodel.hsModelStr = row.hsmodel
				this.focusIndex=644

			},
			//如不一致，则提示用户申报规范有变化，需重新录入申报要素，
			// 并弹出规格型号弹窗，让用户录入，然后再继续规格型号录入回车的逻辑；成交单位，法一单位，法二单位直接用税则库的
			decHsnameChooseNo(row){
       if(row){
				this.record.hscode=!this.record.hscode?row.hscode:this.record.hscode
				this.record.unitCode=!this.record.unitCode?row.qtyunit:this.record.unitCode
				this.record.unit1=!this.record.unit1?row.qtyunit:this.record.unit1
				this.record.unit2=!this.record.unit2?row.qtcunit:this.record.unit2
			}

				this.show.decHsmodel=!this.show.decHsmodel
				this.$refs.decHsmodel.hsModelStr = row.hsmodel
				this.focusIndex=644
			},
			//价格检查(商品库检查)
			handleCheckForGoods(){
				if(this.dataSource.length==0){
					this.$message.error('不存在表体项，无法进行价格检查')
					return
				}
				let decLists=[]
				for(let a of this.dataSource){
					let decList={productInfoId:a.productInfoId,price:a.price,item:a.item}
					decLists.push(decList)
				}
				this.checkForGoodsLoading=true
				postAction('/DecHead/dec-head/checkDecListForGoods', {
					decLists: decLists,decHeadId: this.decHeadId
				}).then(res => {
					if (res.success) {
						this.$message.success(res.message)
					} else {
						this.$message.error(res.message)
					}
				}).finally(()=>{
					this.checkForGoodsLoading=false
				})
			},
			//净重检查(商品库检查)
			handleCheckNetWeightForGoods(){
										if(this.dataSource.length==0){
							this.$message.error('不存在表体项，无法进行净重检查')
							return
						}
						let decLists=[]
						for(let a of this.dataSource){
							let decList={productInfoId:a.productInfoId,netWeight:a.netWeight,item:a.item}
							decLists.push(decList)
						}
						this.checkForGoodsLoading=true
						postAction('/DecHead/dec-head/checkDecListNetWeightForGoods', {
							decLists: decLists,decHeadId: this.decHeadId
						}).then(res => {
							if (res.success) {
								this.$message.success(res.message)
							} else {
								this.$message.error(res.message)
							}
						}).finally(()=>{
							this.checkForGoodsLoading=false
						})
			},
			//价格检查(申报历史检查)
			handleCheckForHistory(){
        	if(this.dataSource.length==0){
        		this.$message.error('不存在表体项，无法进行价格检查')
						return
					}
        	let decLists=[]
				for(let a of this.dataSource){
					let decList={hsname:a.hsname,price:a.price}
					decLists.push(decList)
				}
				this.checkForHistoryLoading=true
				postAction('/DecHead/dec-head/checkDecListForHistory', {
					decLists: decLists,decHeadId: this.decHeadId
				}).then(res => {
					if (res.success) {
						this.$message.success(res.message)
					} else {
						this.$message.error(res.message)
					}
				}).finally(()=>{
					this.checkForHistoryLoading=false
				})
			},
			//净重检查(申报历史检查)
			handleCheckNetWeightForHistory(){
				if(this.dataSource.length==0){
					this.$message.error('不存在表体项，无法进行净重检查')
					return
				}
				let decLists=[]
				for(let a of this.dataSource){
					let decList={hsname:a.hsname,netWeight:a.netWeight}
					decLists.push(decList)
				}
				this.checkForHistoryLoading=true
				postAction('/DecHead/dec-head/checkDecListNetWeightForHistory', {
					decLists: decLists,decHeadId: this.decHeadId
				}).then(res => {
					if (res.success) {
						this.$message.success(res.message)
					} else {
						this.$message.error(res.message)
					}
				}).finally(()=>{
					this.checkForHistoryLoading=false
				})
			},
			//海关知识产权备案检验
			handleCheckForIntellectualProperty(){
				if(this.dataSource.length==0){
					this.$message.error('不存在表体项，无法进行海关知识产权备案检验')
					return
				}
				let decLists=[]
				for(let a of this.dataSource){
					if(a.hsmodel&&a.historyDeclarationSpecification){
						let decList={hsmodel:a.hsmodel,historyDeclarationSpecification:a.historyDeclarationSpecification}
						decLists.push(decList)
					}
				}
				if(decLists.length==0){
					this.$message.warning('暂时无法进行海关知识产权备案检验，请手动保存后再进行尝试')
					return
				}

				this.checkForIntellectualPropertyLoading=true
				postAction('/DecHead/dec-head/checkDecListForIntellectualProperty', {
					decLists: decLists,optUnitName: this.value.optUnitName
				}).then(res => {
					if (res.success) {
						this.$message.success('检验通过')
					} else {
						this.$message.error(res.message)
					}
				}).finally(()=>{
					this.checkForIntellectualPropertyLoading=false
				})

			},
			trimThree(value){
				return value.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '');
			},
			isInteger(obj){
				return Math.floor(obj) === obj
			},
			toInteger(floatNum) {
        var ret = { times: 1, num: 0 }
        if (this.isInteger(floatNum)) {
            ret.num = floatNum
            return ret
        }
        var strfi = floatNum + ''
        var dotPos = strfi.indexOf('.')
        var len = strfi.substr(dotPos + 1).length
        var times = Math.pow(10, len)
        var intNum = Number(floatNum.toString().replace('.', ''))
        ret.times = times
        ret.num = intNum
        return ret
    	},
			operation(a, b, digits, op) {
        var o1 = this.toInteger(a)
        var o2 = this.toInteger(b)
        var n1 = o1.num
        var n2 = o2.num
        var t1 = o1.times
        var t2 = o2.times
        var max = t1 > t2 ? t1 : t2
        var result = null
        switch (op) {
            case 'add':
                if (t1 === t2) { // 两个小数位数相同
                    result = n1 + n2
                } else if (t1 > t2) { // o1 小数位 大于 o2
                    result = n1 + n2 * (t1 / t2)
                } else { // o1 小数位 小于 o2
                    result = n1 * (t2 / t1) + n2
                }
                if (digits) {
                    return parseFloat((result / max).toFixed(digits))
                }
                else {
                    return parseFloat(result / max)
                }
            case 'subtract':
                if (t1 === t2) {
                    result = n1 - n2
                } else if (t1 > t2) {
                    result = n1 - n2 * (t1 / t2)
                } else {
                    result = n1 * (t2 / t1) - n2
                }
                if (digits) {
                    return parseFloat((result / max).toFixed(digits))
                }
                else {
                    return parseFloat(result / max)
                }
            case 'multiply':
                result = (n1 * n2) / (t1 * t2)
                if (digits) {
                    return parseFloat(result.toFixed(digits))
                }
                else {
                    return parseFloat(result)
                }
            case 'divide':
                result = (n1 / n2) * (t2 / t1)
                if (digits) {
                    return parseFloat(result.toFixed(digits))
                }
                else {
                    return parseFloat(result)
                }
        }
    	},
			add(a, b, digits) {
					return this.operation(a, b, digits, 'add')
			},
			subtract(a, b, digits) {
        return this.operation(a, b, digits, 'subtract')
    	},
			multiply(a, b, digits) {
        return this.operation(a, b, digits, 'multiply')
    	},
			divide(a, b, digits) {
        return this.operation(a, b, digits, 'divide')
    	},
			numFilter(value) {
				let realVal = ''
				if (!isNaN(value) && value !== '' && value !== 0 && value != null) {
						// 截取当前数据到小数点后两位
						realVal = parseFloat(value)
				} else {
						realVal = '0'
				}
				return realVal
			},
			historicalselection(){
				this.$refs.DecProductInformation.showModal=true;

			},
			handleKeydown(event){
				var that=this;
				if (event.ctrlKey && event.keyCode == 49) {
					//阻止浏览器默认行为
					event.preventDefault();//
					event.cancelBubble = true;//IE
					if (this.show.bulkEdit == true) {
							if (that.spreadsheet.getData().filter(p => p[1]).length > 0) {
								that.$refs.DecListBulkEdit.showModal = true;
								that.$nextTick(async () => {
								  await that.$refs.DecListBulkEdit.handinit(that.spreadsheet.getData());
								})
							}
							else {
								this.$message.warning('缺少有效的商品编码');
							}
					}
				}//ctrl+2
				else if (event.ctrlKey && event.keyCode == 50) {
					//阻止浏览器默认行为
					event.preventDefault();//
					event.cancelBubble = true;//IE
					//#region 申报要素
					var codeTs = that.spreadsheet.getValueFromCoords(1, that.spreadsheet.selectedCell[1]);
					if (!codeTs) {
						that.$message.warning('缺少有效的商品编码');
						return;
				}
					this.$refs.bulkEditDecHsmodel.title = "第【" + (parseFloat(that.spreadsheet.selectedCell[1]) + 1) + "】项 " + codeTs + " 规格型号"

         this.selectedCell=that.spreadsheet.selectedCell;
         let hsmodel= that.spreadsheet.getValueFromCoords(5, that.spreadsheet.selectedCell[1]);
          this.$refs.bulkEditDecHsmodel.disabled = false
          this.$refs.bulkEditDecHsmodel.showModal = true
					let hsmodelList =  hsmodel.split("|")
					this.$refs.bulkEditDecHsmodel.decHsCodeMessage(codeTs,hsmodelList)
				}
				else if (event.ctrlKey && event.keyCode == 51) {
					//阻止浏览器默认行为
					event.preventDefault();//
					event.cancelBubble = true;//IE
					//#region 申报要素
					var codeTs = that.spreadsheet.getValueFromCoords(1, that.spreadsheet.selectedCell[1]);
					var hsname = that.spreadsheet.getValueFromCoords(2, that.spreadsheet.selectedCell[1]);
					if (!codeTs) {
						that.$message.warning('缺少有效的商品编码');
						return;
					}
					this.selectedCell=that.spreadsheet.selectedCell;
					this.decListqueryParam.hscode=codeTs;
					this.decListqueryParam.hsname=hsname;
					this.$refs.DecProductInformation.showModal=true;
					this.$refs.DecProductInformation.listDecListByTenant(this.decListqueryParam);
				}
			},
			savemore(SheetData,reportKeyList,unionReportKeyList){
				var _this=this;
				SheetData.forEach(function (item, index) {
					let gModelList = [];
					//商品名称
					_this.spreadsheet.setValueFromCoords(2, index, item[1]);
					const reportKeys = reportKeyList.filter(p => p.goodsHsCode == item[0])[0].reportKeys.map(p => p.decFacName);
					reportKeys.forEach(function (item1, index1) {
							const _Index =unionReportKeyList.map(p => p.decFacName).indexOf(item1) ;
							gModelList.push(item[_Index + 2]);
					})
					//申报要素
					const haveValueIndex = _this.QueryHaveValueIndex(gModelList);
					const gModel = gModelList.slice(0, haveValueIndex + 1).join('|');

					_this.spreadsheet.setValue(jexcel.getColumnNameFromId([5, index]), gModel);
				})
			},
			QueryHaveValueIndex(array) {
				let _Index = 0;
				if (array && array.length > 0) {
						array.forEach(function (item, index) {
								if (item) {
										_Index = index;
								}
						})
				}
				return _Index;
			},
			decBuckHsnameChooseOk(row){
				console.log(row)
				if (row && row.length > 0) {
					let insertRow = this.conversion(row[0]);
					console.log(insertRow)
					console
					this.spreadsheet.setRowData(this.selectedCell[1], insertRow);
			}
			},
			acceptClick(){
				var List = [];
        var SheetData = this.spreadsheet.getData().filter(p => p.filter(t => t).length > 0);
        for (var i = 0; i < SheetData.length; i++) {
            var Entity = {};
            if (this.dataSource[i]) {
                Entity = this.dataSource[i];
            }else{
							Entity.id=-1;
						}
            Entity.item = i + 1;
            for (var j = 0; j < SheetData[i].length; j++) {
                switch (j) {
                    //商品编码
                    case 1:
                        Entity.hscode = SheetData[i][j];
                        break;
                    //商品名称
                    case 2:
                        Entity.hsname = SheetData[i][j];
                        break;
                    //品牌类型
                    case 3:
                        //Entity.gModel = SheetData[i][j];
                        break;
                    //享惠情况
                    case 4:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //规格型号（申报要素）
                    case 5:
                        Entity.hsmodel = SheetData[i][j];
                        break;
                    //成交数量
                    case 6:
                        Entity.goodsCount = SheetData[i][j];
                        break;
                    //成交单位
                    case 7:
                        Entity.unitCode = SheetData[i][j];
                        break;
                    //单价
                    case 8:
                        Entity.price = SheetData[i][j];
                        break;
                    //总价
                    case 9:
                        Entity.total = SheetData[i][j];
                        break;
                    //币制
                    case 10:
                        Entity.currencyCode = SheetData[i][j];
                        break;
                    //原产国
                    case 11:
                        Entity.desCountry = SheetData[i][j];
                        break;
                    //最终目的国
                    case 12:
                        Entity.destinationCountry = SheetData[i][j];
                        break;
                    //征免方式
                    case 13:
                        Entity.faxTypeCode = SheetData[i][j];
                        break;
                    //净重（可选）
                    case 14:
                        // Entity.NetWeight = SheetData[i][j];
                        break;
                    //法一数量
                    case 15:
                        Entity.count1 = SheetData[i][j];
                        break;
                    //法一单位
                    case 16:
                        Entity.unit1 = SheetData[i][j];
                        break;
                    //法二数量
                    case 17:
                        Entity.count2 = SheetData[i][j];
                        break;
                    //法二单位
                    case 18:
                        Entity.unit2 = SheetData[i][j];
                        break;
                    //境内货源地
                    case 19:
                        Entity.districtCode = SheetData[i][j];
                        break;
                    //产地代码
                    case 20:
                        Entity.desCountry = SheetData[i][j];
                        break;
                    //单耗版本号
                    case 21:
                        Entity.exgVersion = SheetData[i][j];
                        break;
                    //货号
                    case 22:
                        Entity.tmp002 = SheetData[i][j];
                        break;
                    //备案序号
                    case 23:
                        Entity.recordItem = SheetData[i][j];
                        break;
                    //用途
                    case 24:
                        Entity.purpose = SheetData[i][j];
                        break;
                    //检验检疫名称
                    case 25:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //检验检疫代码
                    case 26:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //检疫货物规格
                    case 27:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //货物属性
                    case 28:
                        //Entity.goodsAttrName = SheetData[i][j];
                        break;
                    //产品资质
                    case 29:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //危险货物信息
                    case 30:
                        //Entity.codeTs = SheetData[i][j];
                        break;
                    //申报要素OCR识别原文
                    case 31:
                        //Entity.codeTs = SheetData[i][j];

                        break;
                }
            }
            List.push(Entity);
        }
				this.dataSource=List;
				this.show.bulkEdit = false;
			}
    }

}
