<template>
	<div>
		<a-descriptions
			title=""
			bordered
			:column="2"
			size="small"
		>
			<a-descriptions-item label="手册编号">
				<div class="manual-number">
					<a v-if="model.emsNo" class="left" @click="showModal">{{model.emsNo}}</a>
					<div v-else class="left" @click="showModal">
						请选择
					</div>
					<div class="right">
						<button v-if="model.emsNo" class="clear-btn" @click="clearManualNumber">清空</button>
					</div>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="有效日期">
				<div class="manual-number">
					<span class="left">{{ model.endDate }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="经营单位">
				<div class="manual-number">
					<span class="left">{{ model.tradeName }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="加工单位">
				<div class="manual-number">
					<span class="left">{{ model.ownerName }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="料件首次进口日期">
				<div class="manual-number">
					<span class="left">{{ model.inDateAimg1 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="料件末次进口日期">
				<div class="manual-number">
					<span class="left">{{ model.inDateAimg2 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="成品首次出口日期">
				<div class="manual-number">
					<span class="left">{{ model.outDateAexg1 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="成品末次出口日期">
				<div class="manual-number">
					<span class="left">{{ model.outDateAexg2 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="实际进口金额(美金)">
				<div class="manual-number">
					<span class="left">{{ model.actualInUsd ? model.actualInUsd : 0 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="实际出口金额(美金)">
				<div class="manual-number">
					<span class="left">{{ model.actualOutUsd ? model.actualOutUsd : 0 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="进口清单总数">
				<div class="manual-number">
					<span class="left">{{ model.totalInList ? model.totalInList : 0 }}</span>
				</div>
			</a-descriptions-item>
			<a-descriptions-item label="出口清单总数">
				<div class="manual-number">
					<span class="left">{{ model.totalOutList ? model.totalOutList : 0 }}</span>
				</div>
			</a-descriptions-item>
		</a-descriptions>

		<!-- 选择手册 -->
		<j-modal
			title="请选择手册"
			:width="980"
			:visible="visible"
			@ok="handleOk"
			@cancel="handleCancel"
			cancelText="关闭"
		>
			<a-card :bordered="false">
				<!-- 查询区域 -->
				<div class="table-page-search-wrapper">
					<a-form layout="inline" @keyup.enter.native="searchQuery">
						<a-row :gutter="24">
							<a-col :xl="8" :sm="24" :xxl="8" :md="12">
								<a-form-item label="手册编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入手册编号" v-model="queryParam.emsNo"></a-input>
								</a-form-item>
							</a-col>
							<a-col :xl="8" :sm="24" :xxl="8" :md="12">
								<a-form-item label="经营单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入经营单位" v-model="queryParam.tradeName"></a-input>
								</a-form-item>
							</a-col>
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</a-col>
						</a-row>
					</a-form>
				</div>
				<!-- table区域-begin -->
				<div>
					<a-table
						ref="table"
						size="small"
						:scroll="{ x: true }"
						bordered
						rowKey="id"
						:columns="columns"
						:dataSource="dataSource"
						:pagination="ipagination"
						:loading="loading"
						class="j-table-force-nowrap"
						:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio'}"
						@change="handleTableChange"
						:customRow="rowEvent"
					>
					</a-table>
				</div>
			</a-card>
		</j-modal>
	</div>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";

export default {
	name: "OverviewHandbook",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			searchText: '',
			visible: false,
			model: {
				emsNo: '',
				inDateAimg1: '',
				inDateAimg2: '',
				outDateAexg1: '',
				outDateAexg2: '',
				actualInUsd: 0,
				actualOutUsd: 0,
				totalInList: 0,
				totalOutList: 0,
			},
			queryParam: {
				menuType: '1',
				// status: '1'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			columns: [
				{
					title: '序号',
					key: 'rowIndex',
					width: 80,
					align: 'center',
					customRender: (t, r, i) => 1 + i,
				},
				{
					title: '手册编号',
					align: 'center',
					dataIndex: 'emsNo'
				},
				{
					title: '有效日期',
					align: 'center',
					dataIndex: 'endDate',
					customCell: (record, index) => {
						if(record.endDate){
							const days=this.calculateDays('',record.endDate)
							if(days>180){
								//绿色大于180
								return {
									style: {
										'background-color': 'green'
									},
								}
							}else if(days>90&&days<=180){
								//橙色大于90小于180
								return {
									style: {
										'background-color': 'orange'
									},
								}
							}else if(days>30&&days<=90){
								//金色大于30小于90
								return {
									style: {
										'background-color': 'gold'
									},
								}
							}else if(days<=30&&days>=0){
								//红色大于30
								return {
									style: {
										'background-color': 'red'
									},
								}
							}else {
								//过期
								return {
									style: {
										'background-color': 'darkgrey'
									},
								}
							}
						}
					},
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'tradeName'
				},
				{
					title: '加工单位',
					align: 'center',
					dataIndex: 'ownerName'
				},
			],
			url: {
				list: '/business/ems/listForReport'
			},
		}
	},
	methods: {
		handleOk() {
			this.model = Object.assign({}, this.selectionRows[0])
			this.$emit('setModel', this.model)
			this.handleCancel()
		},
		showModal() {
			this.visible = true
		},
		/**
		 * 点击表格行触发
		 * @param {Object} record - 行数据
		 * @param {Number} index - 索引值
		 * @return Function
		 */
		rowEvent: function(record, index) {
			return {
				on: {
					click: async () => {
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
					},
					dblclick: () => {
						this.handleOk()
					},
					// ...
				}
			}
		},
		searchReset() {
			this.queryParam = {
				menuType: '1',
				status: '1'
			}
			this.searchQuery()
		},
		calculateDays(startDate,endDate) {
			const start = new Date();
			const end = new Date(endDate);
			const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
			return days
		},
		clearManualNumber() {
			this.model = {
					emsNo: '',
					inDateAimg1: '',
					inDateAimg2: '',
					outDateAexg1: '',
					outDateAexg2: '',
					actualInUsd: 0,
					actualOutUsd: 0,
					totalInList: 0,
					totalOutList: 0,
			}
			this.$emit('setModel', this.model)
		},
		handleCancel() {
			this.visible = false
		}
	}
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
/deep/ [data-v-7778fe99] td {
	border: 1px solid #e8e8e8 !important;
	font: 12px/150% Arial, verdana, "\5FAE\8F6F\96C5\9ED1";
}
/deep/ td {
	border: 1px solid #e8e8e8 !important;
	font:12px/150% Arial,verdana,"微软雅黑"
}

.manual-number {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.left {
	cursor: pointer;
	width: 90%;
	text-align: center;
}
.right {
	width: 10%;
}
.clear-btn {
	top: 0;
	right: 0;
	height: 100%;
	width: 40px;
	background-color: #ccc;
	text-align: center;
}

/deep/ .ant-modal-body {
	padding: 0 5px
}

/deep/ .ant-descriptions-item-label{
	width: 250px;
}
</style>