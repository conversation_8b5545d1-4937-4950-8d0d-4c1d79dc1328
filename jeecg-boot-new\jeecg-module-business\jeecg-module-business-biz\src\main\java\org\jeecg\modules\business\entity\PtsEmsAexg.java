package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;

/**
 * <p>
 * 手账册归并后成品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@Accessors(chain = true)
@TableName("pts_ems_aexg")
public class PtsEmsAexg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 手册流水
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("EMS_ID")
    private Long emsId;
    /**
     * 统一编号
     */
    @TableField("SEQ_NO")
    private String seqNo;

    /**
     * 手册/账册号
     */
    @TableField("EMS_NO")
    private String emsNo;

    /**
     * 序号（报关用）(备案号)
     */
    @JsonProperty("gNo")
    @TableField("G_NO")
    private Integer gNo;

    /**
     * 货号(归并后可以没有货号)
     */
    @TableField("COP_GNO")
    private String copGno;

    /**
     * 商品编码(税号)
     */
    @TableField("CODET")
    private String codet;

    /**
     * 商品名称
     */
    @JsonProperty("gName")
    @TableField("G_NAME")
    private String gName;

    /**
     * 规格型号(申报要素)
     */
    @JsonProperty("gModel")
    @TableField("G_MODEL")
    private String gModel;

    /**
     * 计量单位
     */
    @Dict(dictTable = "erp_units", dicText = "name", dicCode = "code")
    @TableField("UNIT")
    private String unit;

    /**
     * 法定计量单位
     */
    @Dict(dictTable = "erp_units", dicText = "name", dicCode = "code")
    @TableField("UNIT1")
    private String unit1;

    /**
     * 第二法定计量单位
     */
    @TableField("UNIT2")
    private String unit2;

    /**
     * 产销国
     */
    @Dict(dictTable = "erp_countries", dicText = "name", dicCode = "code")
    @TableField("COUNTRY_CODE")
    private String countryCode;

    /**
     * 自定义净重(系统用,物流账册取值)
     */
    @TableField("NET_WEIGHT")
    private BigDecimal netWeight;

    /**
     * 自定义所属企业(系统用,物流账册取值)(TENANT_ID)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("CUSTOMER_ID")
    private Long customerId;

    /**
     * 申报单价
     */
    @TableField("DEC_PRICE")
    private BigDecimal decPrice;

    /**
     * 币制
     */
    @Dict(dictTable = "erp_currencies", dicText = "currency", dicCode = "code")
    @TableField("CURR")
    private String curr;

    /**
     * 备案数量/申报数量(加贸手册用)
     */
    @TableField("QTY")
    private BigDecimal qty;

    /**
     * 已出口数量(加贸手/账册用)
     */
    @TableField("EXPORTED_QTY")
    private BigDecimal exportedQty;

    /**
     * 库存数量
     */
    @TableField("STOCK_QTY")
    private BigDecimal stockQty;

    @TableField("USE_TYPE")
    private String useType;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 处理标志
     */
    @TableField("MODIFY_MARK")
    private String modifyMark;

    /**
     * 申报总价
     */
    @TableField("APPR_AMT")
    private BigDecimal apprAmt;

    /**
     * 征免方式
     */
    @Dict(dicCode = "ZJMSFS")
    @TableField("DUTY_MODE")
    private String dutyMode;

    /**
     * 版本号(乐观锁)
     */
    @TableField("VERSION")
    private Integer version;

    /**
     * 记账清单编号(海关要求回填值(可累计的无意义))
     */
    @TableField("BOND_INVT_NO")
    private String bondInvtNo;

    /**
     * 记账清单序号(海关要求回填值(可累计的无意义))
     */
    @TableField("BOND_INVT_ITEM")
    private String bondInvtItem;

    /**
     * 企业内部编号(统计需要(可累计的无意义))
     */
    @TableField("INTERNAL_NO")
    private String internalNo;

    /**
     * 是否开通 1:已开通 空或0：未开通
     */
    @TableField("DREDGE")
    private String dredge;

    /**
     * 重点商品标识
     */
    private String keyProductIdentification;
    /**
     * 商品属性
     */
    private String attributes;
    /**
     * 修改标志
     */
    private String modifyFlag;

    /**
     * 海关执行标志
     */
    private String customsEnforcementMark;

    /**
     * 单耗质疑标志
     */
    private String unitConsumptionQueryFlag;

    /**
     * 磋商标志
     */
    private String negotiationSymbol;

    /**
     * 是否外发 1:已外发 空或0：未外发
     */
    @TableField("OUTSOURCE")
    private String outsource;
    @TableField(exist = false)
    private String type;
    @TableField(exist = false)
    private String zjcksl;
    @TableField(exist = false)
    private String scyl;
    @TableField(exist = false)
    private String ylbl;
    @TableField(exist = false)
    private String sjgjzcksl;
    @TableField(exist = false)
    private String cpthcksl;
    @TableField(exist = false)
    private String cpthjksl;
    @TableField(exist = false)
    private String sjcksl;
    @TableField(exist = false)
    private BigDecimal jqpjdj;
    @TableField(exist = false)
    private BigDecimal zjckslQty;
    @TableField(exist = false)
    private BigDecimal sjckjehj;
    /**
     * 申报总价
     */
    private BigDecimal dclTotalAmt;

}
