package org.jeecg.modules.business.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.modules.business.config.message.FtpProperties;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.mapper.*;
import org.jeecg.modules.business.messages.config.MsgFtpConfig;
import org.jeecg.modules.business.service.IItfDclIoDeclService;
import org.jeecg.modules.business.util.message.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBException;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.HAS_OWN_FTP;
import static org.jeecg.common.constant.CommonConstant.SFTP;
import static org.jeecg.modules.business.util.message.CiqMessageUtil.validateXMLSchema;

/**
 * <p>
 * 出入境检验检疫申请基本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Slf4j
@Service
public class ItfDclIoDeclServiceImpl extends ServiceImpl<ItfDclIoDeclMapper, ItfDclIoDecl> implements IItfDclIoDeclService {
    @Lazy
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private ItfDclIoDeclGoodsMapper itfDclIoDeclGoodsMapper;
    @Autowired
    private ItfDclIoDeclContDetailMapper itfDclIoDeclContDetailMapper;
    @Autowired
    private ItfDclIoDeclAttMapper itfDclIoDeclAttMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private FtpProperties ftpProperties;
//    @Value(value = "${ftp.url}")
//    private String url;
//    @Value(value = "${ftp.port}")
//    private Integer port;
//    @Value(value = "${ftp.username}")
//    private String username;
//    @Value(value = "${ftp.password}")
//    private String password;
//    @Value(value = "${ftp.remoteCiqSendPath}")
//    private String remoteSendPath;
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    private static String myStaticProperty;

    @PostConstruct
    public void init() {
        myStaticProperty = uploadpath;
    }

    /**
     * 保存出入境检验检疫申请基本信息
     *
     * @param itfDclIoDecl 接口声明和输入/输出声明的实例，包含需要保存的详细信息。
     * @return 返回操作结果，可能包含操作是否成功、错误信息等。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> saveItfDclIoDecl(ItfDclIoDecl itfDclIoDecl) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 新增
        if (isBlank(itfDclIoDecl.getId())) {
            itfDclIoDecl.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            itfDclIoDecl.setCreateDate(new Date());
            baseMapper.insert(itfDclIoDecl);
            // 编辑
        } else {
            itfDclIoDecl.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
            itfDclIoDecl.setUpdateDate(new Date());
            baseMapper.updateById(itfDclIoDecl);
        }
        // 处理商品信息
        if (isNotEmpty(itfDclIoDecl.getItfDclIoDeclGoodsList())) {
            List<ItfDclIoDeclGoods> oldItfDclIoDeclGoodsList = itfDclIoDeclGoodsMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclGoods>()
                    .eq(ItfDclIoDeclGoods::getHeadId, itfDclIoDecl.getId()));
            if (isNotEmpty(oldItfDclIoDeclGoodsList)) {
                // 使用 Stream 进行过滤
                List<ItfDclIoDeclGoods> dels = oldItfDclIoDeclGoodsList.stream()
                        .filter(item -> itfDclIoDecl.getItfDclIoDeclGoodsList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (ItfDclIoDeclGoods itfDclIoDeclGoods : dels) {
                        itfDclIoDeclGoodsMapper.deleteById(itfDclIoDeclGoods.getId());
                    }
                }
            }
            for (ItfDclIoDeclGoods itfDclIoDeclGoods : itfDclIoDecl.getItfDclIoDeclGoodsList()) {
                if (isBlank(itfDclIoDeclGoods.getId())) {
                    itfDclIoDeclGoods.setHeadId(itfDclIoDecl.getId());
                    itfDclIoDeclGoods.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    itfDclIoDeclGoods.setCreateDate(new Date());
                    itfDclIoDeclGoodsMapper.insert(itfDclIoDeclGoods);
                    // 编辑时，需要解除占用，重新占用
                } else {
                    itfDclIoDeclGoods.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    itfDclIoDeclGoods.setUpdateDate(new Date());
                    itfDclIoDeclGoodsMapper.updateById(itfDclIoDeclGoods);
                }
            }
        }
        // 处理集装箱
        if (isNotEmpty(itfDclIoDecl.getItfDclIoDeclContDetailList())) {
            List<ItfDclIoDeclContDetail> oldItfDclIoDeclContDetailList = itfDclIoDeclContDetailMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclContDetail>()
                    .eq(ItfDclIoDeclContDetail::getHeadId, itfDclIoDecl.getId()));
            if (isNotEmpty(oldItfDclIoDeclContDetailList)) {
                // 使用 Stream 进行过滤
                List<ItfDclIoDeclContDetail> dels = oldItfDclIoDeclContDetailList.stream()
                        .filter(item -> itfDclIoDecl.getItfDclIoDeclGoodsList().stream().filter(i -> isNotBlank(i.getId())).noneMatch(item2 -> item2.getId().equals(item.getId())))
                        .collect(Collectors.toList());
                if (isNotEmpty(dels)) {
                    for (ItfDclIoDeclContDetail itfDclIoDeclContDetail : dels) {
                        itfDclIoDeclContDetailMapper.deleteById(itfDclIoDeclContDetail.getId());
                    }
                }
            }
            for (ItfDclIoDeclContDetail itfDclIoDeclContDetail : itfDclIoDecl.getItfDclIoDeclContDetailList()) {
                if (isBlank(itfDclIoDeclContDetail.getId())) {
                    itfDclIoDeclContDetail.setHeadId(itfDclIoDecl.getId());
                    itfDclIoDeclContDetail.setCreateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    itfDclIoDeclContDetail.setCreateDate(new Date());
                    itfDclIoDeclContDetailMapper.insert(itfDclIoDeclContDetail);
                    // 编辑时，需要解除占用，重新占用
                } else {
                    itfDclIoDeclContDetail.setUpdateBy(isNotEmpty(sysUser) ? sysUser.getUsername() : null);
                    itfDclIoDeclContDetail.setUpdateDate(new Date());
                    itfDclIoDeclContDetailMapper.updateById(itfDclIoDeclContDetail);
                }
            }
        }
        ItfDclIoDecl resultItfDclIoDecl = baseMapper.selectById(itfDclIoDecl.getId());
        resultItfDclIoDecl.setItfDclIoDeclGoodsList(itfDclIoDeclGoodsMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclGoods>()
                .eq(ItfDclIoDeclGoods::getHeadId, resultItfDclIoDecl.getId())));
        resultItfDclIoDecl.setItfDclIoDeclContDetailList(itfDclIoDeclContDetailMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclContDetail>()
                .eq(ItfDclIoDeclContDetail::getHeadId, resultItfDclIoDecl.getId())));
        return Result.ok(resultItfDclIoDecl);
    }

    /**
     * 根据ID获取接口声明的输入/输出声明。
     *
     * @param id 用于查找特定接口声明的唯一标识符。
     * @return 返回一个Result对象，该对象可能包含找到的接口声明的输入/输出信息，或者在未找到时包含相应的错误信息。
     */
    @Override
    public Result<?> getItfDclIoDeclById(String id) {
        ItfDclIoDecl itfDclIoDecl = baseMapper.selectById(id);
        if (isEmpty(itfDclIoDecl)) {
            return Result.error("未找到ID为" + id + "的检验检疫信息，请刷新页面重试！");
        }
        List<ItfDclIoDeclGoods> itfDclIoDeclGoodsList = itfDclIoDeclGoodsMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclGoods>()
                .eq(ItfDclIoDeclGoods::getHeadId, id));
        itfDclIoDecl.setItfDclIoDeclGoodsList(itfDclIoDeclGoodsList);
        itfDclIoDecl.setItfDclIoDeclContDetailList(itfDclIoDeclContDetailMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclContDetail>()
                .eq(ItfDclIoDeclContDetail::getHeadId, id)));
        List<ItfDclIoDeclAtt> itfDclIoDeclAtts = itfDclIoDeclAttMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclAtt>()
                .eq(ItfDclIoDeclAtt::getHeadId, id));
        if (isNotEmpty(itfDclIoDeclAtts)) {
            // 收集itfAttrs中的attDocName并用,拼起来，去掉重复项
            itfDclIoDecl.setAttsStr(itfDclIoDeclAtts.stream().map(ItfDclIoDeclAtt::getAttDocName).distinct().reduce((a, b) -> a + "," + b).orElse(""));
        }
        // 处理货物属性
        if (isNotEmpty(itfDclIoDeclGoodsList)) {
            List<DictModel> dictModels = sysBaseApi.getDictItems("HWSX");
            Map<String, String> dictMap = new HashMap<>();
            if (isNotEmpty(dictModels)) {
                dictModels.forEach(dictModel -> {
                    dictMap.put(dictModel.getValue(), dictModel.getText());
                });
            }
            itfDclIoDeclGoodsList.forEach(item -> {
                if (isNotBlank(item.getGoodsAttr())) {
                    String goodsAttrStr = null;
                    for (String value : item.getGoodsAttr().split(",")) {
                        String text = dictMap.get(value);
                        if (goodsAttrStr == null) {
                            goodsAttrStr = new StringBuilder(value).append("|").append(text).toString();
                        } else {
                            goodsAttrStr = new StringBuilder(goodsAttrStr).append(",")
                                    .append(value).append("|").append(text).toString();
                        }
                    }
                    item.setGoodsAttrStr(goodsAttrStr);
                }
            });
        }
        return Result.ok(itfDclIoDecl);
    }

    /**
     * 批量删除方法
     *
     * @param ids 需要删除的实体的ID集合，以字符串形式表示
     * @return 返回操作的结果，具体类型依赖于实际的业务逻辑实现
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteBatch(String ids) {
        for (String id : ids.split(",")) {
//            ItfDclIoDeclXml itfDclIoDecl = baseMapper.selectById(id);
            itfDclIoDeclGoodsMapper.delete(new LambdaQueryWrapper<ItfDclIoDeclGoods>()
                    .eq(ItfDclIoDeclGoods::getHeadId, id));
            itfDclIoDeclContDetailMapper.delete(new LambdaQueryWrapper<ItfDclIoDeclContDetail>()
                    .eq(ItfDclIoDeclContDetail::getHeadId, id));
            itfDclIoDeclAttMapper.delete(new LambdaQueryWrapper<ItfDclIoDeclAtt>()
                    .eq(ItfDclIoDeclAtt::getHeadId, id));
            baseMapper.deleteById(id);
        }
        return Result.ok("删除成功！");
    }

    /**
     * 处理推送请求。
     *
     * @param ids 以字符串形式表示的一组ID，这些ID代表需要进行推送的目标对象。
     * @return 返回一个Result对象，该对象包含了操作的结果信息，例如操作是否成功，如果有错误则包含错误信息。
     */
    @Override
    public Result<?> handlePush(String ids) {
        List<ItfDclIoDecl> itfDclIoDeclList = baseMapper.selectList(new QueryWrapper<ItfDclIoDecl>().lambda()
                .in(ItfDclIoDecl::getId, Arrays.asList(ids.split(","))));
        if (itfDclIoDeclList == null || itfDclIoDeclList.isEmpty()) {
            return Result.error("未找到对应商检单,请核实数据");
        }
        String errorId = "";
        for (String id : ids.split(",")) {
            Result<ItfDclIoDecl> itfDclIoDeclResultMsg = this.sendMessageById(id);
            if (!itfDclIoDeclResultMsg.isSuccess()) {
                if (isBlank(errorId)) {
                    errorId = itfDclIoDeclResultMsg.getMessage() + ",商检单流水号：" + id;
                } else {
                    errorId = errorId + ";" + itfDclIoDeclResultMsg.getMessage() + ",商检单流水号：" + id;
                }

            }
        }
        if (isNotBlank(errorId)) {
            return Result.error(errorId);
        }
        return Result.ok("发送成功");
    }

    /**
     * 单个发送报文
     *
     * @param id
     * @return org.jeecg.common.api.vo.Result<org.jeecg.modules.business.entity.StockHeadType>
     * <AUTHOR>
     * @date 2024/3/13 13:05
     */
    public Result<ItfDclIoDecl> sendMessageById(String id) {
        ItfDclIoDecl itfDclIoDecl = baseMapper.selectById(id);
        if (isEmpty(itfDclIoDecl)) {
            return Result.error(String.format("流水号%s:%s", id, "未找到对应商检单"));
        }
        itfDclIoDecl.setItfDclIoDeclGoodsList(itfDclIoDeclGoodsMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclGoods>()
                .eq(ItfDclIoDeclGoods::getHeadId, id)));
        itfDclIoDecl.setItfDclIoDeclContDetailList(itfDclIoDeclContDetailMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclContDetail>()
                .eq(ItfDclIoDeclContDetail::getHeadId, id)));
        // 随附单据
        List<ItfDclIoDeclAtt> itfDclIoDeclAtts = itfDclIoDeclAttMapper.selectList(new LambdaQueryWrapper<ItfDclIoDeclAtt>()
                .eq(ItfDclIoDeclAtt::getHeadId, id));

//        String fileName = itfDclIoDecl.getId() + ".xml";

        // 判断随附单据信息中是否存在文件路径
        boolean hasAttrs = false;
        if (isNotEmpty(itfDclIoDeclAtts)) {
            itfDclIoDecl.setItfDclIoDeclAttList(itfDclIoDeclAtts);
            for (ItfDclIoDeclAtt itfDclIoDeclAtt : itfDclIoDeclAtts) {
                if (isNotBlank(itfDclIoDeclAtt.getPath())) {
                    hasAttrs = true;
                    break;
                }
            }
        }

        boolean uploadFlag = false;
        try {
//            MsgFtpConfig ftpConfig = new MsgFtpConfig(url, port, username, password, remoteSendPath);
            SysConfig sysConfig = sysConfigMapper.getConfigByConfigKey(HAS_OWN_FTP);
            FtpProperties.FtpConnection conn = ftpProperties.getConnection(isNotEmpty(sysConfig) ? sysConfig.getConfigValue() : null);
            MsgFtpConfig ftpConfig = new MsgFtpConfig(conn.getUrl(), conn.getPort(), conn.getUsername(), conn.getPassword(), ftpProperties.getPaths().getRemoteSendSasPath());
            String ftpType = isNotEmpty(sysConfig) && isNotBlank(sysConfig.getConfigValue()) ? sysConfig.getConfigValue().split("\\|")[1] : SFTP;
            // 临时测试用！！！
//            MsgFtpConfig ftpConfig = new MsgFtpConfig("*************",
//                    21,
//                    "admin",
//                    "Expresafe2017",
//                    "/ImpPath/Decciq001/OutBox/");
            // 带随附单据的，打成zip包
//            if (hasAttrs) {
//                String zipPath = generateXmlWithZip(itfDclIoDecl);
//                log.info("发送商检单报文之报文生成路径：{}", zipPath);
//                uploadFlag = new FTPUtil(ftpConfig).upload(itfDclIoDecl.getId(),
//                        Files.newInputStream(Paths.get(zipPath)), ".zip");
//            } else {
            // 将ByteArrayOutputStream转换为File对象
            ByteArrayOutputStream byteArrayOutputStream = generateXml(itfDclIoDecl);
            File file = byteArrayOutputStreamToFile(byteArrayOutputStream);

            // 输出文件路径
            System.out.println("File path: " + file.getAbsolutePath());
            String xsdPath = uploadpath + File.separator + "DecCiqMessage.xsd";
            if (validateXMLSchema(file.getAbsolutePath(), xsdPath)) {
                System.out.println("XML文件验证成功");
            } else {
                System.out.println("XML文件验证失败");
            }

            if (SFTP.equals(ftpType)) {
                uploadFlag = new SFTPUtil(ftpConfig).upload(itfDclIoDecl.getId(),
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), ".xml");
            } else {
                uploadFlag = new FTPUtil(ftpConfig).upload(itfDclIoDecl.getId(),
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), ".xml");
            }

//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        if (!uploadFlag) {
            return Result.error("发送失败");
        }

        this.update(new UpdateWrapper<ItfDclIoDecl>().lambda()
                .set(ItfDclIoDecl::getSend, "1")
                .eq(ItfDclIoDecl::getId, itfDclIoDecl.getId()));

        return Result.ok("发送成功!");
    }

    /**
     * 将ByteArrayOutputStream转换为File对象
     *
     * @param byteArrayOutputStream 要转换的ByteArrayOutputStream对象
     * @return 转换后的File对象
     * @throws IOException 如果发生I/O错误
     */
    public static File byteArrayOutputStreamToFile(ByteArrayOutputStream byteArrayOutputStream) throws IOException {
        // 创建一个临时文件
        File tempFile = File.createTempFile(myStaticProperty, ".xml");
        // 将ByteArrayOutputStream的内容写入到临时文件中
        try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
            byteArrayOutputStream.writeTo(fileOutputStream);
        }
        // 返回临时文件
        return tempFile;
    }

    /**
     * 生成报文不带随附单据的XML格式
     *
     * @param itfDclIoDecl
     * @return java.io.ByteArrayOutputStream
     * <AUTHOR>
     * @date 2024/4/28 上午11:34
     */
    private ByteArrayOutputStream generateXml(ItfDclIoDecl itfDclIoDecl) throws Exception {
        // 返回生成的报文
        return MessageFileUtil.export(CiqMessageUtil.generateDecMessage(itfDclIoDecl), null);
    }

    /**
     * 生成带随附单据的xml文件
     *
     * @param itfDclIoDecl
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/4/28 上午11:28
     */
    private String generateXmlWithZip(ItfDclIoDecl itfDclIoDecl) throws Exception {
        List<ItfDclIoDeclAtt> itfDclIoDeclAtts = itfDclIoDecl.getItfDclIoDeclAttList();
        // 将所附单据以及生成的报文放入文件流数组
        File srcFiles[] = new File[itfDclIoDeclAtts.size()+1];
        // 获取随附单据文件
        List<String> delFiles = new ArrayList<>();
        int sequence = 0;
        for (int i = 0; i < itfDclIoDeclAtts.size(); i++) {
            String path = uploadpath + File.separator + itfDclIoDeclAtts.get(i).getPath();
            // minio
            if (itfDclIoDeclAtts.get(i).getPath().startsWith("http")) {
                path = MinioUtil.downloadFile(itfDclIoDeclAtts.get(i).getPath(), null);
                delFiles.add(path);
            }
            srcFiles[sequence] = new File(path);
            sequence++;
        }

        // 返回生成的报文
        ByteArrayOutputStream bos = MessageFileUtil.export(CiqMessageUtil.generateDecMessage(itfDclIoDecl), null);
        byte[] data = bos.toByteArray();
        /*
         * 将生成的xml上传到根目录备用，生成完zip再删除
         */
        String xmlPath = uploadpath + "/send/temporary" + File.separator + itfDclIoDecl.getId() + ".xml";
        File directoryFile = new File(uploadpath + "/send/temporary");
        if (!directoryFile.exists()) {
            directoryFile.mkdirs();
        }
        File file = new File(xmlPath);
        if (file.exists()) {//判断文件是否存在，存在就删除
            file.delete();
        }
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data, 0, data.length);
        fos.flush();
        fos.close();

        // 2024/4/29 下午1:11@ZHANGCHAO 追加/变更/完善：验证XML
//        if (!validateXMLSchema(pathAll, getXsd383())) {
//            log.info("报文XSD验证失败！");
//        }
//        log.info("报文XSD验证成功！");

        /*
         * 将生成的报文放入文件流数组
         */
        srcFiles[itfDclIoDeclAtts.size()] = new File(xmlPath);

        String zipPath = xmlPath.replace("xml", "zip");
        File zipFile = new File(zipPath);
        MessageFileUtil.zipFiles(srcFiles, zipFile);

        if (isNotEmpty(delFiles)) {
            ExecutorService executor = ThreadUtil.newExecutor(1);
            executor.execute(() -> {
                try {
                    Thread.sleep(60000); // 赶紧给我睡上1分钟！！
                    for (String delFile : delFiles) {
                        FileUtil.del(FileUtil.file(delFile));
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    log.error("删除文件失败", e);
                }
            });
            executor.shutdown();
        }
        return zipPath;
    }
}
