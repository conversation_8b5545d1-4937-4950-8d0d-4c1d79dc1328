<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter='24'>
					<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
						<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='订单号'>
							<a-input v-model='queryParam.orderNo' placeholder='请输入订单号'></a-input>
						</a-form-item>
					</a-col>
					<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
						<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='委托方'>
							<j-search-select-tag_ v-model='queryParam.buyer' :dict-options='buyerOptions' placeholder='请选择委托方' />
						</a-form-item>
					</a-col>
					<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
						<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='境外付款方'>
							<j-search-select-tag_ v-model='queryParam.overseasPayerInfoId' :dict='dictCodePayerName'
								placeholder='请选择境外付款方' />
						</a-form-item>
					</a-col>

					<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
						<span class='table-page-search-submitButtons' style='float: left;overflow: hidden;'>
							<a-button icon='search' type='primary' @click='searchQuery'>查询</a-button>
							<a-button icon='reload' style='margin-left: 8px' type='primary' @click='searchReset'>重置</a-button>
							<a style='margin-left: 8px' @click='handleToggleSearch'>
								{{ toggleSearchStatus ? '收起' : '展开' }}
								<a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
							</a>
						</span>
					</a-col>

					<template v-if='toggleSearchStatus'>
						<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
							<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='中文品名'>
								<a-input v-model='queryParam.pn' placeholder='请输入中文品名'></a-input>
							</a-form-item>
						</a-col>
						<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
							<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='英文品名'>
								<a-input v-model='queryParam.pnEn' placeholder='请输入英文品名'></a-input>
							</a-form-item>
						</a-col>
						<a-col :md='12' :sm='24' :xl='6' :xxl='6'>
							<a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='合同号'>
								<a-input v-model='queryParam.contractNo' placeholder='请输入合同号'></a-input>
							</a-form-item>
						</a-col>
					</template>

				</a-row>
			</a-form>
		</div>

		<!-- table区域-begin -->
		<div>
			<!--			新表格-->
			<query-vxe-grid ref="xGrid" :dataSource="dataSource" :gridOptions="gridOptions" :loading="loading" class="xGrid-style"
				height="500" size="mini" @cell-click="cellClick"
				@checkbox-change="checkboxChangeEvent" @checkbox-all="checkboxChangeEvent" @page-change="handlePageChange"
				@cell-dblclick="cellDblclick">
				<template v-slot:toolbar_buttons>
					<!-- 操作按钮区域 -->
					<div class="table-operator">
						<a-button @click="handleAdd" type="primary" v-has="'xsorder:edit'" icon="plus">新增</a-button>
						<a-button @click="handleImport" type="primary" v-has="'xsorder:edit'" icon="import">导入</a-button>
						<a-dropdown size="small">
							<a-menu slot="overlay" @click="handleExport" size="small">
								<a-menu-item key="1">
									列表查询导出
								</a-menu-item>
								<a-menu-item key="2">
									列表选择导出
								</a-menu-item>
							</a-menu>
							<a-button v-has="'xsorder:down'" size="small" icon="export" type="primary" :loading="exportLoading">
								导出
								<a-icon type="down" />
							</a-button>
						</a-dropdown>
						<a-button v-has="'xsorder:scckyw'" :loading='submitLoading' icon='select' type='primary'
							@click='handleSubmit'>生成出口业务</a-button>
						<a-button v-if="selectedRowKeys.length > 0" v-has="'xsorder:edit'" ghost icon="delete" type="primary"
							@click="batchDel">批量删除
						</a-button>
					</div>
				</template>
				<template #action="{ row }">
					<a-dropdown>
						<a class='ant-dropdown-link' @click.stop=''> <a-icon type='setting' /></a>
						<a-menu slot='overlay'>
							<a-menu-item v-has="'xsorder:edit'">
								<a @click='handleEdit(row)'>编辑</a>
							</a-menu-item>
							<a-menu-item v-has="'xsorder:edit'">
								<a @click='handleCopy(row.id)'>拷贝新增</a>
							</a-menu-item>
							<a-menu-item v-has="'xsorder:edit'">
								<a-popconfirm v-if='row.status != "-1"' title='确定操作失效吗?'
									@confirm='() => handleLose(row, "-1")'>操作失效</a-popconfirm>
								<a-popconfirm v-else title='确定解除失效吗?' @confirm='() => handleLose(row, "0")'>解除失效</a-popconfirm>
							</a-menu-item>
							<a-menu-item v-has="'xsorder:edit'">
								<a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(row)'>
									<a>删除</a>
								</a-popconfirm>
							</a-menu-item>
						</a-menu>
					</a-dropdown>
				</template>
				<template #orderNo="{ row }">
					<a @click="handleEdit(row)">{{ subStrForColumns(row.orderNo, 25) }}</a>
				</template>
			</query-vxe-grid>
		</div>

		<ExportOrderModal_New ref="modalForm" @close="forceRerender" @ok="modalFormOk" />

		<import-modal ref="importModal" :downLoadUrl="'青岛亚是加食品有限公司' ==tenantName?url.downLoadTempUrlByYSJ:url.downLoadTempUrl" 
		:importUrl="'青岛亚是加食品有限公司' ==tenantName?url.importExcelUrlByYSJ: url.importExcelUrl" 
		downLoadButtonText="下载订单导入模板"
		ie-flag="E" title="出口订单导入" @closeUploadModal="closeUploadModal" @loadData="loadData(1)">
		</import-modal>

		<!-- 生成出口业务 -->
		<shipment-modal ref="importApplyRef" @ok="modalFormOk" @close="forceRerender" />

		<under-detail-list ref="underDetailRef" />

	</a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { generateTransactionNo, number_format } from '@/utils/util'
import { subStrForAddr, subStrForColumns } from '@/utils/util'
import { deleteAction, putAction, downFile, _postAction, getAction, downloadFile } from '@/api/manage'
import ImportModal from '@/components/ImportModal/ImportModal'
import UnderDetailList from "@/views/exportOrder/modules/UnderDetailList.vue";
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types'
import Vue from 'vue'
import store from '@/store'
import ExportOrderModal_New from '@/views/exportOrder/modules/ExportOrderModal_New.vue'
import { ajaxGetDictItems } from '@/api/api'
import ShipmentModal from "@/views/exportOrder/modules/business/ShipmentModal.vue";
import QueryVxeGrid from '@/components/yorma/xTable/QueryVxeGrid.vue'
import { MyXGridMixin } from '@/mixins/MyXGridMixin'
export default {
	name: 'ExportOrderList',
	mixins: [MyXGridMixin, mixinDevice],
	components: {
		UnderDetailList,
		QueryVxeGrid,
		ShipmentModal,
		ExportOrderModal_New,
		ImportModal
	},
	data() {
		return {
			dataSource: [],
			/* 分页参数 */
			ipagination: {
				total: 0,
				currentPage: 1,
				pageSize: 15,
				pageSizes: [15, 30, 50, 100, 200],
				perfect: true
			},
			statusOptions: [],
			buyerOptions: [],
			overseasPayerInfoAll: [],
			dictCodeJiaCustomer: `commissioner,COMMISSIONER_FULL_NAME,id,tenant_id='${store.getters.tenantId}' and del_flag=0`,
			dictCodePayerName: '',
			submitLoading: false,
			auditLoading: false,
			noAuditLoading: false,
			exportLoading: false,
			importModalVisible: false,
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl: { span: 5 },
				xl: { span: 9 }
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			description: '订单信息表管理页面',
			// 表头
			columns: [
				{
					title: '订单号',
					align: 'center',
					sorter: false,
					dataIndex: 'orderNo',
					scopedSlots: { customRender: 'orderNo' },
					// customRender: this.showNo,
				},
				{
					title: '订单类型',
					align: 'center',
					sorter: false,
					dataIndex: 'orderType',
					customRender: function (text) {
						if (text == '1') {
							return '国内采购'
						} else if (text == '2') {
							return '进口采购'
						} else if (text == '3') {
							return '外销订单'
						} else if (text == '4') {
							return '内销订单'
						} else if (text == '5') {
							return '二线出区'
						} else if (text == '6') {
							return '非保入区'
						} else {
							return text
						}
					}
				},
				// {
				//   title: '订单状态',
				//   align: 'center',
				//   sorter: false,
				//   dataIndex: 'status',
				// 	customRender: this.orderStatusFormat
				// },
				{
					title: '进出口',
					align: 'center',
					sorter: false,
					dataIndex: 'ieFlag',
					customRender: function (text) {
						if (text == 'I') {
							return "进口";
						} else if (text == 'E') {
							return "出口";
						} else {
							return text;
						}
					}
				},
				{
					title: '委托方',
					align: 'center',
					sorter: false,
					dataIndex: 'buyer',
				},
				{
					title: '境外付款方',
					align: 'left',
					sorter: false,
					dataIndex: 'overseasPayerInfoId'
				},
				// {
				// 	title: '币制',
				// 	align: 'center',
				// 	dataIndex: 'currency_dictText',
				// 	scopedSlots: { customRender: 'currency_dictText' }
				// },
				{
					title: '成交方式',
					align: 'center',
					dataIndex: 'transMode_dictText',
					scopedSlots: { customRender: 'transMode_dictText' }
				},
				{
					title: '货值总金额',
					align: 'center',
					dataIndex: 'totalAmount'
				},
				{
					title: '合同编号',
					dataIndex: 'contractNo',
					align: 'center',
					scopedSlots: { customRender: 'contractNo' }
				},
				{
					title: '是否已生成业务',
					align: 'center',
					dataIndex: '',
					customRender: (text, record, index) => {
						if (record.relBusinessNo) {
							return '是'
						} else {
							return '否'
						}
					}
				},
				{
					title: '关联业务编号',
					dataIndex: 'relBusinessNo',
					align: 'center',
					scopedSlots: { customRender: 'relBusinessNo' }
				},
				{
					title: '创建时间',
					align: 'center',
					dataIndex: 'createDate',
					scopedSlots: { customRender: 'createDate' },
				},
				{
					title: '操作',
					dataIndex: 'action',
					align: 'center',
					fixed: 'right',
					width: 20,
					scopedSlots: { customRender: 'action' },
				},
			],
			queryParam: {
				ieFlag: 'E',
				orderTypes: '3,4,5'
			},
			tenantName:"",
			url: {
				list: '/business/order/list',
				getOrderById: '/business/order/getOrderById',
				deleteBatch: '/business/order/deleteBatch',
				handleLose: '/business/order/handleLose',
				importExcelUrl: '/business/order/importOrder',
				importExcelUrlByYSJ: '/business/order/importOrderByYSJ',
				exportXlsUrl: '/business/order/exportOrderXls',
				downLoadTempUrl: '/template/订单导入模板.xlsx',
				downLoadTempUrlByYSJ: '/template/亚是加销售订单导入模板.xlsx'
			},
		}
	},
	computed: {
		gridOptions() {
			const gridOptions = {
				id: 'Table',
				pagerConfig: {
					currentPage: this.ipagination.currentPage,
					pageSize: this.ipagination.pageSize,
					pageSizes: [15, 30, 50, 100, 200],
					total: this.ipagination.total
				},
				toolbarConfig: {
					perfect: true,
					refresh: {
						query: () => this.loadData(1)
					},
					zoom: true,
					custom: true,
					slots: {
						buttons: 'toolbar_buttons'
					}
				},
				columns: [
					{
						type: 'checkbox',
						field: 'checkbox',
						align: 'center',
						width: 50,
						fixed: 'left',
					},
					{
						title: '订单号',
						align: 'center',
						sorter: false,
						field: 'orderNo',
						width: 180,
						slots: {
							default: 'orderNo'
						}
					},
					{
						title: '订单类型',
						align: 'center',
						sorter: false,
						field: 'orderType',
						width: 100,
						formatter: function ({ cellValue, row, column }) {
							if (cellValue == '1') {
								return '国内采购'
							} else if (cellValue == '2') {
								return '进口采购'
							} else if (cellValue == '3') {
								return '外销订单'
							} else if (cellValue == '4') {
								return '内销订单'
							} else if (cellValue == '5') {
								return '二线出区'
							} else if (cellValue == '6') {
								return '非保入区'
							} else {
								return cellValue
							}
						}
					},
					// {
					//   title: '订单状态',
					//   align: 'center',
					//   sorter: false,
					//   dataIndex: 'status',
					// 	customRender: this.orderStatusFormat
					// },
					{
						title: '进出口',
						align: 'center',
						sorter: false,
						field: 'ieFlag',
						width: 100,
						formatter: function ({ cellValue, row, column }) {
							if (cellValue == 'I') {
								return "进口";
							} else if (cellValue == 'E') {
								return "出口";
							} else {
								return cellValue;
							}
						}
					},
					{
						title: '委托方',
						align: 'center',
						sorter: false,
						field: 'buyer',
						width: 180,
					},
					{
						title: '境外付款方',
						align: 'left',
						sorter: false,
						width: 180,
						field: 'overseasPayerInfoId'
					},
					// {
					// 	title: '币制',
					// 	align: 'center',
					// 	dataIndex: 'currency_dictText',
					// 	scopedSlots: { customRender: 'currency_dictText' }
					// },
					{
						title: '成交方式',
						align: 'center',
						field: 'transMode_dictText',
						width: 100,
					},
					{
						title: '货值总金额',
						align: 'center',
						width: 100,
						field: 'totalAmount'
					},
					{
						title: '合同编号',
						field: 'contractNo',
						align: 'center',
						width: 120,
					},
					{
						title: '是否已生成业务',
						align: 'center',
						field: '',
						width: 120,
						formatter: function ({ cellValue, row, column }) {
							if (row.relBusinessNo) {
								return '是'
							} else {
								return '否'
							}
						}
					},
					{
						title: '关联业务编号',
						field: 'relBusinessNo',
						align: 'center',
						width: 100,
					},
					{
						title: '创建时间',
						align: 'center',
						width: 160,
						field: 'createDate',
					},
					{
						title: '操作',
						field: 'action',
						align: 'center',
						fixed: 'right',
						width: 80,
						slots: {
							default: 'action'
						}
					},
				],




			}
			return gridOptions
		},
		tokenHeader() {
			let head = { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) }
			let tenantid = Vue.ls.get(TENANT_ID)
			if (!tenantid) {
				tenantid = 0
			}
			head['tenant-id'] = tenantid
			return head
		},
		importExcelUrl: function () {
			return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
		},
	},
	created() {
		const tenantInfo = store.getters.tenantInfo
		if (tenantInfo) {
			this.tenantName = tenantInfo.name
		}
		console.log(this.tenantName)
		this.loadDictOptions(tenantInfo)
		this.dictCodePayerName =
			'overseas_payer_info,overseas_payer_name,id,tenant_id=' + tenantInfo.id + 'and is_effective_flag=1 and del_flag=0'
		this.initDictData('order_status_out')
		this.loadData(1)
	},
	methods: {
		// 高级查询
		searchQuery() {
			this.loadData(1)
			// this.initSelects()
			this.$refs.underDetailRef.header = '请选择一票订单...'
			this.$refs.underDetailRef.dataSource = []
			this.$refs.underDetailRef.selectionRows = []
		},
		async checkboxChangeEvent () {
			this.selectionRows = await this.$refs.xGrid.getCheckboxRecords()
			let newselectedRowKeys = []
			for (let i = 0; i < this.selectionRows.length; i++) {
				newselectedRowKeys.push(this.selectionRows[i].id)
			}
			this.selectedRowKeys = newselectedRowKeys
			if (this.selectionRows.length === 1) {
				this.$refs.underDetailRef.header = '【' + this.selectionRows[0].orderNo + '】商品信息'
				this.$refs.underDetailRef.dataSource = this.selectionRows[0].orderDetailList
				await getAction(this.url.getOrderById, { id: this.selectionRows[0].id }).then(res => {
					if (res && res.success) {
						this.$refs.underDetailRef.dataSource = res.result.orderDetailList
					} else {
						this.$refs.underDetailRef.dataSource = this.selectionRows[0].orderDetailList
					}
				})
				this.$refs.underDetailRef.selectionRows = []
			} else {
				this.$refs.underDetailRef.header = '请选择一票订单...'
				this.$refs.underDetailRef.dataSource = []
				this.$refs.underDetailRef.selectionRows = []
			}
		},
		// 多选行被单击
		async cellClick({ row, column }) {
			this.selectedRowKeys.splice(0, this.selectedRowKeys.length)
			if (column.type !== 'checkbox') {
				this.$refs.xGrid.clearCheckboxRow()
				this.$refs.xGrid.setCheckboxRow(row, true)
			}
			this.selectionRows = await this.$refs.xGrid.getCheckboxRecords()
			let selectedRowKeys = await this.$refs.xGrid.getCheckboxRecords()
			selectedRowKeys.forEach((selectedRowKey, index) => {
				this.$set(this.selectedRowKeys, index, selectedRowKey.id)
			})
			this.$refs.underDetailRef.header = '【' + row.orderNo + '】商品信息'
			this.$refs.underDetailRef.dataSource = row.orderDetailList
			await getAction(this.url.getOrderById, { id: row.id }).then(res => {
				if (res && res.success) {
					this.$refs.underDetailRef.dataSource = res.result.orderDetailList
				} else {
					this.$refs.underDetailRef.dataSource = row.orderDetailList
				}
			})
			this.$refs.underDetailRef.selectionRows = []
		},
		cellDblclick({ row }) {
			this.handleEdit(row)
		},
		handleAdd: function () {
			this.$refs.modalForm.add()
			this.$refs.modalForm.title = '新增'
			this.$refs.modalForm.disableSubmit = false
		},
		closeUploadModal() {
			this.$refs.importModal.visible = false
			this.$refs.importModal.fileList = []
			this.loadData(1)
			this.onClearSelected()
		},
		/**
		 * 提交
		 */
		handleSubmit() {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择至少一条记录！')
				return false
			}
			let arr = []
			let hasSC = []
			this.selectionRows.forEach(item => {
				if (item.status == '-1') {
					arr.push(item)
				}
				if (item.isCannotBeginBusiness) {
					hasSC.push(item.orderNo)
				}
			})
			if (arr.length > 0) {
				this.$message.warning('失效的订单无法生成业务，请重新选择！')
				return false
			}
			if (hasSC.length > 0) {
				this.$message.warning(`订单[${hasSC.join(',')}]可执行数量不足，无法生成！`)
				return false
			}
			if (this.selectionRows.length > 1) {
				const areJYEqual = this.areAllElementsEqual(this.selectionRows, 'buyer')
				if (!areJYEqual) {
					this.$message.warning('请选择相同「委托方」的订单!')
					return
				}
				const areJGEqual = this.areAllElementsEqual(this.selectionRows, 'overseasPayerInfoId')
				if (!areJGEqual) {
					this.$message.warning('请选择相同「供应商」的订单!')
					return
				}
			}
			var that = this
			this.$confirm({
				title: '操作确认',
				content: '是否根据选择的' + that.selectedRowKeys.length + '票订单生成出口业务?',
				onOk: function () {
					that.$refs.importApplyRef.add({
						ids: that.selectedRowKeys.join(','),
						ieFlag: 'E',
						type: '1'
					})
					that.$refs.importApplyRef.title = '新增 出口业务'
					that.$refs.importApplyRef.disableSubmit = false
				}
			})
		},
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length > 0) {
				if (dictCode == 'order_status_out') {
					this.statusOptions = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						// this.initDictData(dictCode)
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length > 0) {
							if (dictCode == 'order_status_out') {
								this.statusOptions = dictOptions
							}
						}
					}
				})
			}
		},
		// 查询委托方数据
		async loadDictOptions(tenantInfo) {
			this.dictOptions = []
			await getAction(`/sys/dict/getDictItems/${this.dictCodeJiaCustomer}`, {}).then(res => {
				if (res.success) {
					this.buyerOptions = res.result.map(item => ({ value: item.value, text: item.text }))
				} else {
					this.buyerOptions = []
				}
				if (tenantInfo) {
					this.buyerOptions.push({ value: String(tenantInfo.id), text: tenantInfo.name })
				}
			})
			this.overseasPayerInfoAll = []
			await getAction(`/sys/dict/getDictItems/${this.dictCodePayerName}`, {}).then(res => {
				if (res.success) {
					this.overseasPayerInfoAll = res.result.map(item => ({ value: item.value, text: item.text }))
				} else {
					console.error('getDictItems error: : ', res)
					this.overseasPayerInfoAll = []
				}
			})
		},
		handleEdit: function (record) {
			this.$refs.modalForm.edit(record)
			this.$refs.modalForm.title = '编辑'
			this.$refs.modalForm.orderType = record.orderType
			this.$refs.modalForm.status = record.status
			this.$refs.modalForm.statusOptions = this.statusOptions
			this.$refs.modalForm.disableSubmit = false
		},
		/**
		 * 审核
		 */
		handleAudits(type) {
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return false
			}
			let arr0 = []
			let arr1 = []
			this.selectionRows.forEach(item => {
				if (item.isAudits == '1') {
					arr1.push(item)
				} else {
					arr0.push(item)
				}
			})
			if (type === 0 && arr0.length > 0) {
				this.$message.warning('存在未审核的数据，请重新选择！')
				return false
			}
			if (type === 1 && arr1.length > 0) {
				this.$message.warning('存在已审核的数据，请重新选择！')
				return false
			}
			var that = this
			this.$confirm({
				title: '操作确认',
				content: type === 0 ? '确认取消审核?' : '确认审核?',
				onOk: function () {
					type === 0 ? that.noAuditLoading = true : that.auditLoading = true
					_postAction(that.url.audits, {
						ids: that.selectedRowKeys.join(','),
						type: type
					}).then(res => {
						if (res.success) {
							that.$message.success(res.message || res)
							that.loadData()
							that.onClearSelected()
						} else {
							that.$message.warning(res.message || res)
						}
					})
						.finally(() => {
							that.handleEmptyIcon(that.getQueryParams().pageSize)
							type === 0 ? that.noAuditLoading = false : that.auditLoading = false
						})
				}
			})
		},
		handleOverview(record, index) {
			return {
				on: {
					click: () => {
						// this.$refs.modalOverview.overview(record)
						// this.$refs.modalOverview.title = '概览'
						let keys = []
						this.selectionRows = []
						keys.push(record.id)
						this.selectedRowKeys = keys
						this.selectionRows.push(record)
					},
					dblclick: () => {
						this.handleEdit(record)
					}
				}
			}
		},
		//增加样式方法返回值
		getRowClassname(record) {
			if (record.status == '-1') {
				return 'data-rule-invalid'
			}
		},
		handleImport() {
			this.$refs.importModal.fileList = []
			this.$refs.importModal.visible = true
		},
		handleEditByView(model) {
			this.handleEdit(model)
		},
		initDictConfig() { },
		handleCopy: function (record) {
			this.$refs.modalForm.copy(record)
			this.$refs.modalForm.title = '拷贝新增'
			this.$refs.modalForm.orderType = record.orderType
			this.$refs.modalForm.status = record.status
			this.$refs.modalForm.disableSubmit = false
		},
		forceRerender(isChange) {
			if (isChange) {
				this.onClearSelected()
				this.loadData()
			}
		},
		orderStatusFormat(text, record, index) {
			return this.getText(text, this.statusOptions)
		},
		totalPrictFormat(text, record, index) {
			return number_format(record.totalAmount, 2, '.', ',')
		},
		subStrForAddr,
		subStrForColumns,
		handleDelete: function (record) {
			if (!this.url.deleteBatch) {
				this.$message.error("请设置url.delete属性!")
				return
			}
			if (record.status != '-1') {
				this.$message.error('该订单非失效订单，无法删除!')
				return
			}
			var that = this;
			deleteAction(that.url.deleteBatch, { ids: record.id }).then((res) => {
				if (res.success) {
					//重新计算分页问题
					that.reCalculatePage(1)
					that.$message.success(res.message);
					that.loadData();
				} else {
					that.$message.warning(res.message);
				}
			});
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				for (let a of this.selectionRows) {
					if (a.status != '-1') {
						this.$message.error('该订单非失效订单，无法删除!')
						return
					}
				}
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids })
							.then(res => {
								if (res.success) {
									//重新计算分页问题
									that.reCalculatePage(that.selectedRowKeys.length)
									that.$message.success(res.message)
									that.loadData()
									// that.onClearSelected()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		reCalculatePage(count) {
			//总数量-count
			let total = this.ipagination.total - count
			//获取删除后的分页数
			let currentIndex = Math.ceil(total / this.ipagination.pageSize)
			//删除后的分页数<所在当前页
			if (currentIndex < this.ipagination.current) {
				this.ipagination.current = currentIndex
			}
			console.log('currentIndex', currentIndex)
		},
		handleLose(record, status) {
			_postAction(this.url.handleLose, { id: record.id, status: status })
				.then(res => {
					if (res.success) {
						this.$message.success(res.message || res)
						this.searchReset()
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				// orderStatus: 0,
				ieFlag: 'E',
				orderTypes: '3,4,5'
			}
			this.loadData(1)
			this.onClearSelected()
		},
		onClearSelected() {
			this.selectedRowKeys = []
			this.selectionRows = []
			this.$refs.underDetailRef.header = '请选择一票订单...'
			this.$refs.underDetailRef.dataSource = []
			this.$refs.underDetailRef.selectionRows = []
			this.$refs.xGrid.clearCheckboxRow()
		},
		//重写导出方法
		handleExport(key) {
			console.log(key.key)
			// 查询条件导出
			if (key.key === '1') {
				const that = this
				this.queryParam.ids = ''
				this.$confirm({
					title: '确认导出',
					content: '是否根据查询条件导出订单?',
					onOk: () => {
						that.exportLoading = true
						downloadFile(that.url.exportXlsUrl, `销售订单导出_${generateTransactionNo('')}.xlsx`, that.queryParam)
							.finally(() => {
								setTimeout(() => {
									that.exportLoading = false
								}, 500)
							})
					}
				})
				// 选择导出
			} else if (key.key === '2') {
				if (this.selectedRowKeys.length == 0) {
					this.$message.warning('请至少选择一条记录！')
					return false
				}
				const that = this
				this.queryParam.ids = (that.selectedRowKeys && that.selectedRowKeys.length > 0) ? that.selectedRowKeys.join(',') : ''
				this.$confirm({
					title: '确认导出',
					content: '是否导出选中的' + that.selectedRowKeys.length + '票订单?',
					onOk: () => {
						that.exportLoading = true
						downloadFile(that.url.exportXlsUrl, `销售订单导出_${generateTransactionNo('')}.xlsx`, that.queryParam)
							.finally(() => {
								setTimeout(() => {
									that.exportLoading = false
								}, 500)
							})
					}
				})
			}
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text ? text : value
		},
		areAllElementsEqual(arr, propertyName) {
			return arr.every((element) => element[propertyName] === arr[0][propertyName])
		},
	},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin-bottom: 10px
}

/deep/ .table-page-search-wrapper .table-page-search-submitButtons {
	margin-bottom: 16px
}

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}

/deep/ .vxe-grid--toolbar-wrapper {
	height: 34px;
}

/deep/ .ant-card-body {
	padding-top: 4px;

}
</style>