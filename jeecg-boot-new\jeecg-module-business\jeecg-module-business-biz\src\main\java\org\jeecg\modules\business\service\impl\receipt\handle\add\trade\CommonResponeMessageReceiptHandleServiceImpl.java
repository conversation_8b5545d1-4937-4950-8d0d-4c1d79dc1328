package org.jeecg.modules.business.service.impl.receipt.handle.add.trade;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.business.entity.*;
import org.jeecg.modules.business.entity.receipt.DTO.HandleResultDTO;
import org.jeecg.modules.business.entity.receipt.DTO.ReceiptHandleDTO;
import org.jeecg.modules.business.entity.receipt.DTO.Resp;
import org.jeecg.modules.business.entity.receipt.bo.ReceiptHandleDtoToEdiReceipt;
import org.jeecg.modules.business.entity.receipt.bo.ReceiptHandleDtoToEdiStatusHistory;
import org.jeecg.modules.business.entity.receipt.vo.trade.CommonResponeMessageToEdiReceipt;
import org.jeecg.modules.business.entity.receipt.vo.trade.CommonResponeMessageToEdiStatusHistory;
import org.jeecg.modules.business.entity.receipt.vo.trade.ImportResponseMessage;
import org.jeecg.modules.business.service.INemsInvtHeadService;
import org.jeecg.modules.business.service.IPassPortHeadService;
import org.jeecg.modules.business.service.IPtsEmsHeadService;
import org.jeecg.modules.business.service.IStockHeadTypeService;
import org.jeecg.modules.business.service.impl.receipt.AbstractReceiptHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static org.jeecg.common.constant.CommonConstant.SC_OK_200;
import static org.jeecg.modules.business.util.exception.ExceptionUtil.ResExHandle;

/**
 * 核放单、核注清单技术回执
 *
 * <AUTHOR>
 *
 * @date 2020年6月11日 下午4:51:09
 */
@Service
public class CommonResponeMessageReceiptHandleServiceImpl extends
		AbstractReceiptHandleService<ImportResponseMessage, CommonResponeMessageToEdiReceipt, CommonResponeMessageToEdiStatusHistory, ReceiptHandleDtoToEdiReceipt, ReceiptHandleDtoToEdiStatusHistory> {

	/**
	 * 申报表、出入库单接口服务
	 */
	@Autowired
	private IStockHeadTypeService stockTypeApi;

	/**
	 * 核注单接口服务
	 */
	@Autowired
	private INemsInvtHeadService nemsInvtApi;

	/**
	 * 核放单接口服务
	 */
	@Autowired
	private IPassPortHeadService passApi;

	/**
	 * 账册
	 */
	@Autowired
	private IPtsEmsHeadService ptsEmsHeadService;

	@Override
	public HandleResultDTO handleReceipt(ReceiptHandleDTO receiptHandle) {

		ImportResponseMessage commonResponeMessage = (ImportResponseMessage) receiptHandle.getReceiptMessage();
		// 存储异常信息
		List<Resp> respList = new ArrayList<>(16);
		int index = 0;

		// 根据单一的技术回执类型回填统一编号 ZJ@2022-09-13
		if(receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-SAS\\d+_.+") && isNotEmpty(commonResponeMessage.getSeqNo())){
			String type = ((ImportResponseMessage) receiptHandle.getReceiptMessage()).getType();
			String typeName = type.equalsIgnoreCase("SAS001") ? "申报表" : type.equalsIgnoreCase("SAS002") ? "出入库单" : "集报核注单";
			Result<String> ymMsg = stockTypeApi.queryAndSetSeqNo(commonResponeMessage.getRelatedId(),
					commonResponeMessage.getSeqNo(), commonResponeMessage.getType());

			Resp resp = new Resp();
			if (!(ymMsg.getCode().equals(SC_OK_200) || ymMsg.isSuccess() )) {
				logger.info("[{}技术回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", typeName, receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), ymMsg.getMessage());
				resp.setMsg(++index + ".变更" + typeName + "数据：").setStatus("Failed").setFailureStr(ResExHandle(ymMsg.getMessage()));
				respList.add(resp);
				return new HandleResultDTO(0, String.format("[%s技术回执,变更核注清单数据处理失败;]%s", typeName, ymMsg.getMessage()), respList);
			} else {
				resp.setMsg(++index + ".变更" + typeName + "数据：").setStatus("Ok");
				respList.add(resp);
			}

			logger.info("[核注清单技术回执]回执文件名称:{},回执来源类型:{},统一编号:{}," + receiptHandle.getFileName(),
					receiptHandle.getSourceType(), commonResponeMessage.getSeqNo());
			return new HandleResultDTO(1, ymMsg.getResult(), respList);
		}

		// 变更核注清单数据
		/*
		 * 1.核注单技术回执Message_Type == INV000。之前逻辑是根据回执内容的企业内部编号回填核注单的统一编号。现在要调整一下。
		 * 核注单发送报文，发送文件名为核注单流水号.zip，技术回执的文件名也是核注单流水号.zip。所以回执添加一个逻辑，先根据文件名流水号回填核注单的统一编号，
		 * 如果能查到，则回填统一编号。如果根据文件名查不到核注单再根据企业内部编号去查。
		 *（查不到是怕回执文件名将来会变更逻辑，所以保留企业内部编号的逻辑）
		 * 2.技术回执，变更业务状态逻辑去掉（如果此处只变更业务状态的话）。因为生成草单时，业务状态就变更成4了，此处无需再变更。
		 * 2021/9/30 10:05@ZHANGCHAO
		 */
		// TODO 根据企业内部标号preent查询业务并保存SeqNo
		//

		/*if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+_.+")){
			if (receiptHandle.getFileName().contains("Failed")){
				id  =receiptHandle.getFileName().substring(7,26);
			}else if (receiptHandle.getFileName().contains("Successed")){
				id  =receiptHandle.getFileName().substring(10,29);
			}
		}else if (receiptHandle.getFileName().contains(".zip")){
			id = receiptHandle.getFileName().replaceAll(".zip","");
		}*/
		String message = "";
		if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-INVT\\d+_.+") || receiptHandle.getFileName().contains("-INVT001")){
			String id  = null;
			if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-INVT\\d+_.+")){
				if (receiptHandle.getFileName().contains("Failed")){
					id  =receiptHandle.getFileName().substring(7,26);
				}else if (receiptHandle.getFileName().contains("Successed")){
					id  =receiptHandle.getFileName().substring(10,29);
				}
			}else if (receiptHandle.getFileName().contains(".zip")){
//				id = receiptHandle.getFileName().replaceAll(".zip","");
				id = receiptHandle.getFileName().substring(0,19);
			}
			Result<NemsInvtHead> nemsInvtHeadYmMsg = nemsInvtApi.setSeqNoByEtpsNoOrIdForRecepit(id, commonResponeMessage.getEtpsPreentNo(),
					commonResponeMessage.getSeqNo());
			message = "[核注清单技术回执变更核注清单数据成功;]变更核注清单数据成功";
			if (!(nemsInvtHeadYmMsg.getCode().equals(SC_OK_200) || nemsInvtHeadYmMsg.isSuccess() )) {
				logger.info("[核注清单技术回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), nemsInvtHeadYmMsg.getMessage());
				Resp resp = new Resp();
				resp.setMsg(++index + ".变更核注清单数据：").setStatus("Failed").setFailureStr(ResExHandle(nemsInvtHeadYmMsg.getMessage()));
				respList.add(resp);
				return new HandleResultDTO(0, String.format("[核注清单技术回执,变更核注清单数据处理失败;]%s", nemsInvtHeadYmMsg.getMessage()), respList);
			} else {
				Resp resp = new Resp();
				if (isBlank(commonResponeMessage.getSeqNo())){
					resp.setMsg(++index + ".变更核注清单数据：").setStatus("Failed").setFailureStr(commonResponeMessage.getCheckInfo());
				}else {
					resp.setMsg(++index + ".变更核注清单数据：").setStatus("Ok");
				}

				respList.add(resp);
			}
			logger.info("[核注清单技术回执]回执文件名称:{},回执来源类型:{},统一编号:{}," + message, receiptHandle.getFileName(),
					receiptHandle.getSourceType(), commonResponeMessage.getSeqNo());
			return new HandleResultDTO(1, message, respList);
		}

		if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-PASS\\d+_.+") || receiptHandle.getFileName().contains("-PASS001"))  {
			if (isBlank(commonResponeMessage.getSeqNo())){
				logger.info("[核放单技术回执，变更核放单数据处理失败]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), "统一编号为空");
				Resp resp = new Resp();
				resp.setMsg(++index + ".变更核放单数据：").setStatus("Failed").setFailureStr(commonResponeMessage.getCheckInfo());
				respList.add(resp);
				return new HandleResultDTO(0, String.format("[核放单技术回执,变更核放单数据处理失败;]%s", commonResponeMessage.getCheckInfo()), respList);
			}else {
				String id  = null;
				if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-PASS\\d+_.+")){
					if (receiptHandle.getFileName().contains("Failed")){
						id  =receiptHandle.getFileName().substring(7,26);
					}else if (receiptHandle.getFileName().contains("Successed")){
						id  =receiptHandle.getFileName().substring(10,29);
					}
				}else if (receiptHandle.getFileName().contains(".zip")){
//				id = receiptHandle.getFileName().replaceAll(".zip","");
					id = receiptHandle.getFileName().substring(0,19);
				}
				Result<PassPortHead> passPortHeadYmMsg =passApi.setSeqNoByEtpsNoOrId(id, commonResponeMessage.getEtpsPreentNo(),
						commonResponeMessage.getSeqNo());
				message = "[核放单技术回执变更核放单数据成功;]变更核放单数据成功";
				if (!(passPortHeadYmMsg.getCode().equals(SC_OK_200) || passPortHeadYmMsg.isSuccess() )){
					logger.info("[核放单技术回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
							receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), passPortHeadYmMsg.getMessage());
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更核放单数据：").setStatus("Failed").setFailureStr(ResExHandle(passPortHeadYmMsg.getMessage()));
					respList.add(resp);
					return new HandleResultDTO(0, String.format("[核放单技术回执,变更核放单数据处理失败;]%s", passPortHeadYmMsg.getMessage()), respList);
				}else {
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更核放单数据：").setStatus("Ok");
					respList.add(resp);
				}
				logger.info("[核放单技术回执]回执文件名称:{},回执来源类型:{},统一编号:{}," + message, receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo());
			}
		}
		// ZHANGCHAO@2021/7/8 13:33 追加/变更/完善：无需验证是否存在委托流水！
		// ZHANGCHAO@2021/7/8 13:34 追加/变更/完善：有委托流水号则去变更，没有就不用处理正常返回！
		// ZHANGCHAO@2021/7/8 13:54 追加/变更/完善：变更业务状态成功与否不影响整个回执处理！

//		if (null != nemsInvtHeadYmMsg.getData().getApplyNumber()) {
//			// 变更业务状态
//			YmMsg<Apply> applyYmMsg = applyApi.setApplyStatusByApplyNumberAndStatus(nemsInvtHeadYmMsg.getData().getApplyNumber(),
//					ApplyState.PRERECORDING);
//			Resp resp = new Resp();
//			if (applyYmMsg.isSuccess()) {
//				resp.setMsg(++index + ".变更业务状态：").setStatus("Failed").setFailureStr(ResExHandle(applyYmMsg.getMessage()));
//				message = "[核注清单技术回执变更核注清单数据、变更业务状态成功;]变更核注清单数据、变更业务状态成功";
//			} else {
//				resp.setMsg(++index + ".变更业务状态：").setStatus("Ok");
//			}
//			respList.add(resp);
//		} else {
//			Resp resp = new Resp();
//			resp.setMsg(++index + ".变更业务状态：").setStatus("No");
//			respList.add(resp);
//		}
		/**
		 * 处理账册回执 20250122
		 */
		if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-EMS\\d+_.+") || receiptHandle.getFileName().contains("-EMS111"))  {
			if (isBlank(commonResponeMessage.getSeqNo())){
				logger.info("[账册技术回执，变更账册数据处理失败]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), "统一编号为空");
				Resp resp = new Resp();
				resp.setMsg(++index + ".变更账册数据：").setStatus("Failed").setFailureStr(commonResponeMessage.getCheckInfo());
				respList.add(resp);
				return new HandleResultDTO(0, String.format("[账册技术回执,变更账册数据处理失败;]%s", commonResponeMessage.getCheckInfo()), respList);
			} else {
				String id  = null;
				if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-EMS\\d+_.+")){
					if (receiptHandle.getFileName().contains("Failed")){
						id  =receiptHandle.getFileName().substring(7,26);
					}else if (receiptHandle.getFileName().contains("Successed")){
						id  =receiptHandle.getFileName().substring(10,29);
					}
				}else if (receiptHandle.getFileName().contains(".zip")){
					id = receiptHandle.getFileName().substring(0,19);
				}
				Result<PtsEmsHead> ptsEmsHeadResult = ptsEmsHeadService.setSeqNoByEtpsNoOrId(id, commonResponeMessage.getEtpsPreentNo(),
						commonResponeMessage.getSeqNo());
				message = "[账册技术回执变更账册数据成功;]变更账册数据成功，回填统一编号成功";
				if (!(ptsEmsHeadResult.getCode().equals(SC_OK_200) || ptsEmsHeadResult.isSuccess() )){
					logger.info("[账册技术回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
							receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), ptsEmsHeadResult.getMessage());
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更账册数据：").setStatus("Failed").setFailureStr(ResExHandle(ptsEmsHeadResult.getMessage()));
					respList.add(resp);
					return new HandleResultDTO(0, String.format("[账册技术回执,变更账册数据处理失败;]%s", ptsEmsHeadResult.getMessage()), respList);
				}else {
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更账册数据：").setStatus("Ok");
					respList.add(resp);
				}
				logger.info("[账册技术回执]回执文件名称:{},回执来源类型:{},统一编号:{}," + message, receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo());
			}
		}
		/**
		 * 处理手册回执 20250625 by 郑连松
		 */
		if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-NPTS\\d+_.+") || receiptHandle.getFileName().contains("-NPTS001"))  {
			if (isBlank(commonResponeMessage.getSeqNo())){
				logger.info("[手册技术回执，变更手册数据处理失败]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), "统一编号为空");
				Resp resp = new Resp();
				resp.setMsg(++index + ".变更手册数据：").setStatus("Failed").setFailureStr(commonResponeMessage.getResponseMessage());
				respList.add(resp);
				return new HandleResultDTO(0, String.format("[手册技术回执,变更账册数据处理失败;]%s", commonResponeMessage.getResponseMessage()), respList);
			} else {
				String id  = null;
				if (receiptHandle.getFileName().matches("((Successed)|(Failed))_\\d+-NPTS\\d+_.+")){
					if (receiptHandle.getFileName().contains("Failed")){
						id  =receiptHandle.getFileName().substring(7,26);
					}else if (receiptHandle.getFileName().contains("Successed")){
						id  =receiptHandle.getFileName().substring(10,29);
					}
				}else if (receiptHandle.getFileName().contains(".zip")){
					id = receiptHandle.getFileName().substring(0,19);
				}
				Result<PtsEmsHead> ptsEmsHeadResult = ptsEmsHeadService.setSeqNoByEtpsNoOrId(id, commonResponeMessage.getEtpsPreentNo(),
						commonResponeMessage.getSeqNo());
				message = "[手册技术回执变更账册数据成功;]变更手册数据成功，回填统一编号成功";
				if (!(ptsEmsHeadResult.getCode().equals(SC_OK_200) || ptsEmsHeadResult.isSuccess() )){
					logger.info("[手册技术回执]回执文件名称:{},回执来源类型:{},统一编号:{},{}", receiptHandle.getFileName(),
							receiptHandle.getSourceType(), commonResponeMessage.getSeqNo(), ptsEmsHeadResult.getMessage());
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更手册数据：").setStatus("Failed").setFailureStr(ResExHandle(ptsEmsHeadResult.getMessage()));
					respList.add(resp);
					return new HandleResultDTO(0, String.format("[手册技术回执,变更手册数据处理失败;]%s", ptsEmsHeadResult.getMessage()), respList);
				}else {
					Resp resp = new Resp();
					resp.setMsg(++index + ".变更手册数据：").setStatus("Ok");
					respList.add(resp);
				}
				logger.info("[手册技术回执]回执文件名称:{},回执来源类型:{},统一编号:{}," + message, receiptHandle.getFileName(),
						receiptHandle.getSourceType(), commonResponeMessage.getSeqNo());
			}
		}

		return new HandleResultDTO(1, message, respList);

	}

	/**
	 * 设置回执类型
	 * FIXME 不对
	 *
	 * @param receiptMessage 回执报文对象
	 * @param receiptHandle  回执处理DTO对象
	 * @return
	 */
	@Override
	protected EdiReceipt buildEdiReceipt(ImportResponseMessage receiptMessage, ReceiptHandleDTO receiptHandle) {
		EdiReceipt ediReceipt = super.buildEdiReceipt(receiptMessage, receiptHandle);

//		if(receiptMessage.getType()==null) {
		if(true) {
			try {
				String[] rt = getReceiptTypeAndRelatedId(receiptHandle.getSourceType(), receiptHandle.getFileName());
				ediReceipt.setReceiptType(rt[0]);
				ediReceipt.setRelatedId(isNotBlank(rt[1]) ? Long.valueOf(rt[1]) : null);
				receiptMessage.setType(rt[0]);
				receiptMessage.setRelatedId(rt[1]);
			} catch (Exception e) {
				logger.error("回执文件名称:{},回执来源类型:{},显示设置回执报文处理记录数据时出现异常,异常信息是:", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), e);
			}
		}

		return ediReceipt;
	}

	@Override
	protected EdiStatusHistory buildEdiStatusHistory(ImportResponseMessage receiptMessage, ReceiptHandleDTO receiptHandle) {
		EdiStatusHistory ediStatusHistory = super.buildEdiStatusHistory(receiptMessage, receiptHandle);

//		if(receiptMessage.getType()==null) {
		if(true) {
			try {
				String[] rt = getReceiptTypeAndRelatedId(receiptHandle.getSourceType(), receiptHandle.getFileName());
				ediStatusHistory.setType(rt[0]);
				ediStatusHistory.setRelatedId(rt[1]);
				receiptMessage.setType(rt[0]);
				receiptMessage.setRelatedId(rt[1]);
			} catch (Exception e) {
				logger.error("回执文件名称:{},回执来源类型:{},显示设置Edi历史数据时出现异常,异常信息是:", receiptHandle.getFileName(),
						receiptHandle.getSourceType(), e);
			}
		}

		return ediStatusHistory;
	}

	/**
	 * 获取并设置 回执类型&关联ID
	 *
	 * 暂存回执类型根据回执文件后缀确定( SAS001:申报表; SAS002:出入库单; SAS003:核注单; )
	 * 如：          记录ID     报文类型
	 * Successed_123456778890-SAS001_20220913105408523050123.xml.temp
	 *
	 * @param sourceType 来源类型
	 * @param fileName   文件名称
	 * @return 回执类型
	 */
	private String[] getReceiptTypeAndRelatedId(Integer sourceType, String fileName) {
		// @ZJ@2022-09-13 单一窗口回执文件名格式
		String[] rt = new String[2];
		if(fileName != null && fileName.matches("(Successed|Failed)_\\d+-SAS\\d+_.+")){
			String id = fileName.replaceAll("(Successed|Failed)_(\\d+)-SAS\\d+_.+", "$2");
			rt[1] = id;
			rt[0] = fileName.replaceAll("(Successed|Failed)_\\d+-(SAS\\d+)_.+", "$2");
			rt[0] = isBlank(rt[0]) ? "SAS000" : rt[0];

		}else if(fileName != null && fileName.matches("(Successed|Failed)_\\d+-INVT\\d+_.+")){
			String id = fileName.replaceAll("(Successed|Failed)_(\\d+)-INVT\\d+_.+", "$2");
			rt[1] = id;
			rt[0] = fileName.replaceAll("(Successed|Failed)_\\d+-(INVT\\d+)_.+", "$2");
			rt[0] = isBlank(rt[0]) ? "INV000" : rt[0];

		}else if(fileName != null && fileName.matches("(Successed|Failed)_\\d+-EMS\\d+_.+")){
			String id = fileName.replaceAll("(Successed|Failed)_(\\d+)-EMS\\d+_.+", "$2");
			rt[1] = id;
			rt[0] = fileName.replaceAll("(Successed|Failed)_\\d+-(EMS\\d+)_.+", "$2");
			rt[0] = isBlank(rt[0]) ? "EMS111" : rt[0];

		}else if(fileName != null && fileName.matches("(Successed|Failed)_\\d+-NPTS\\d+_.+")){
			String id = fileName.replaceAll("(Successed|Failed)_(\\d+)-NPTS\\d+_.+", "$2");
			rt[1] = id;
			rt[0] = fileName.replaceAll("(Successed|Failed)_\\d+-(NPTS\\d+)_.+", "$2");
			rt[0] = isBlank(rt[0]) ? "NPTS001" : rt[0];

		}else if (sourceType == 1) {
			// 海关直连 .ZIP
			rt[0] = "INV000";
		} else if (fileName.endsWith("")) {
			rt[0] = "";
		} else {
			rt[0] = "";
		}

		return rt;
	}
}
