/** init domain config */
import './config'

// Core libraries
import Vue from 'vue'
import App from './App.vue'
import Storage from 'vue-ls'
import router from './router'
import store from './store/'
import { VueAxios } from '@/utils/request'

// UI libraries and components - 按需导入关键组件
import { version } from 'ant-design-vue'
import '@/plugins/antd' // 按需导入ant-design-vue组件
import RouterTab from 'vue-router-tab'
// 动态加载工具
import { preloadCriticalComponents } from '@/utils/dynamicLoader'
import JDictSelectTag from './components/dict/index.js'
import JeecgComponents from '@/components/jeecg/index'

// Utilities and plugins
import JSONbig from 'json-bigint'
import SSO from '@/cas/sso.js'
import config from '@/defaultSettings'
import hasPermission from '@/utils/hasPermission'
import vueBus from '@/utils/vueBus'
import VueAreaLinkage from 'vue-area-linkage'
import queryCondOption from '@/query/query'
import regex from '@/utils/regex.js'
import types from '@/types/types'
import { rules } from '@/utils/rules'
import { isEmpty, removeSessionStorageItemsWithPrefix, captureTitle, deepClone } from '@/utils/util'
import {
	ACCESS_TOKEN,
	DEFAULT_COLOR,
	DEFAULT_THEME,
	DEFAULT_LAYOUT_MODE,
	DEFAULT_COLOR_WEAK,
	SIDEBAR_TYPE,
	DEFAULT_FIXED_HEADER,
	DEFAULT_FIXED_HEADER_HIDDEN,
	DEFAULT_FIXED_SIDEMENU,
	DEFAULT_CONTENT_WIDTH_TYPE,
	DEFAULT_MULTI_PAGE
} from '@/store/mutation-types'

// Stylesheets
import 'default-passive-events'

import 'vue-router-tab/dist/lib/vue-router-tab.css'
import 'vue-photo-preview/dist/skin.css'
import 'viewerjs/dist/viewer.css'
import '@/assets/less/JAreaLinkage.less'
import 'ant-design-vue/dist/antd.less' // ant-design-vue style

// Project-specific modules
import '@/permission' // permission control
import '@/utils/filter' // base filter
import '@/components/jeecg/JVxeTable/install'
import '@/components/JVxeCells/install'

// Jeecg Online Mini
require('@jeecg/antd-online-mini')
require('@jeecg/antd-online-mini/dist/OnlineForm.css')

console.log('ant-design-vue version:', version)

// --- Vue Prototypes ---
Vue.prototype.rules = rules
Vue.prototype.isEmpty = isEmpty
Vue.prototype.removeSessionStorageItemsWithPrefix = removeSessionStorageItemsWithPrefix
Vue.prototype.captureTitle = captureTitle
Vue.prototype.JSONbig = JSONbig
Vue.prototype.deepClone = deepClone

// --- Vue Configuration ---
Vue.config.productionTip = false
// 注册 DragModal 组件 - JModal 需要立即使用
import DragModal from '@/views/DragModal'
Vue.component('DragModal', DragModal)

// --- Vue Plugins ---
Vue.use(Storage, config.storageOptions)
// Vue.use(Antd) // 已改为按需导入
Vue.use(VueAxios, router)
// Vue.use(Viser) // 延迟加载
Vue.use(RouterTab)
Vue.use(hasPermission)
Vue.use(JDictSelectTag)
// Vue.use(Print) // 延迟加载
// Vue.use(preview) // 延迟加载
Vue.use(vueBus)
Vue.use(JeecgComponents)
Vue.use(VueAreaLinkage)
Vue.use(regex)
Vue.use(JSONbig)
Vue.use(types)
Vue.use(queryCondOption)
// Vue.use(Viewer) // 延迟加载

// Viewer配置将在动态加载时设置
// Viewer.setDefaults({
// 	Options: {
// 		inline: true,
// 		button: true,
// 		navbar: true,
// 		title: true,
// 		toolbar: true,
// 		tooltip: true,
// 		movable: true,
// 		zoomable: true,
// 		rotatable: true,
// 		scalable: true,
// 		transition: true,
// 		fullscreen: true,
// 		keyboard: true,
// 		url: 'data-source'
// 	}
// })

SSO.init(() => {
	main()
})

function main() {
	new Vue({
		router,
		store,
		mounted() {
			store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true))
			store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme))
			store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout))
			store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader))
			store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar))
			store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth))
			store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader))
			store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak))
			store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor))
			store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN))
			store.commit('SET_MULTI_PAGE', Vue.ls.get(DEFAULT_MULTI_PAGE, config.multipage))

			// 在应用启动后预加载关键组件
			preloadCriticalComponents()
		},
		render: h => h(App)
	}).$mount('#app')
}
