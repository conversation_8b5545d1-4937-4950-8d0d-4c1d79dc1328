<template>
	<a-modal
		:maskClosable="false"
		:title="title"
		:visible="visible"
		:width="1200"
		cancelText="关闭"
		@cancel="handleCancel"
	>
		<template slot="footer">
			<a-button icon="close" size="small" @click="handleCancel">关闭</a-button>
		</template>

		<!-- 基础信息区域 -->
		<div class="ship-info-header">
			<div class="info-row">
				<span class="info-label">基础信息</span>
			</div>
			<div class="info-row">
				<span class="info-item"
					>中文船名：<span class="info-value">{{ model.zwcm || '' }}</span></span
				>
				<span class="info-item"
					>英文船名：<span class="info-value">{{ model.ywcm || '' }}</span></span
				>
				<span class="info-item"
					>航次：<span class="info-value">{{ model.voyage || '' }}</span></span
				>
				<span v-if="model.ieFlag === 'E'" class="info-item"
					>装箱单：<span class="info-value">{{
						model.zxl === '1' ? '完成' : model.zxl === '0' ? '未完成' : ''
					}}</span></span
				>
				<span v-if="model.ieFlag === 'E'" class="info-item"
					>收箱开始时间：<span class="info-value">{{ model.sxkssj || '' }}</span></span
				>
				<span v-if="model.ieFlag === 'E'" class="info-item"
					>收箱结束时间：<span class="info-value">{{ model.sxjssj || '' }}</span></span
				>
			</div>
		</div>

		<!-- 表格区域 -->
		<div class="ship-tables-container">
			<a-spin :spinning="loading">
				<!-- 码头信息 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-码头信息</div>
					<vxe-grid
						:columns="exportPortColumns"
						:data="exportPortData"
						:empty-text="'暂无码头信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportPortData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 装箱单信息 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-装箱单信息</div>
					<vxe-grid
						:columns="exportContainerColumns"
						:data="exportContainerData"
						:empty-text="'暂无装箱单信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportContainerData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 运抵报告 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-运抵报告</div>
					<vxe-grid
						:columns="exportArrivalColumns"
						:data="exportArrivalData"
						:empty-text="'暂无运抵报告'"
						:loading="loading"
						:max-height="calculateTableHeight(exportArrivalData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 在途监管信息 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-在途监管信息</div>
					<vxe-grid
						:columns="exportSupervisionColumns"
						:data="exportSupervisionData"
						:empty-text="'暂无在途监管信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportSupervisionData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 海关报关单放行 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-海关报关单放行</div>
					<vxe-grid
						:columns="exportCustomsReleaseColumns"
						:data="exportCustomsReleaseData"
						:empty-text="'暂无海关报关单放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportCustomsReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 海关报关单放行 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-海关报关单放行</div>
					<vxe-grid
						:columns="exportCustomsDeclarationReleaseColumns"
						:data="exportCustomsDeclarationReleaseData"
						:empty-text="'暂无海关报关单放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportCustomsDeclarationReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 装载放行 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-装载放行</div>
					<vxe-grid
						:columns="exportLoadingReleaseColumns"
						:data="exportLoadingReleaseData"
						:empty-text="'暂无装载放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportLoadingReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 外理审核放行 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-外理审核放行</div>
					<vxe-grid
						:columns="exportExternalAuditReleaseColumns"
						:data="exportExternalAuditReleaseData"
						:empty-text="'暂无外理审核放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportExternalAuditReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 外理理货报告 -->
				<div v-if="model.ieFlag === 'E'" class="table-section">
					<div class="section-title">出口-外理理货报告</div>
					<vxe-grid
						:columns="exportExternalTallyReportColumns"
						:data="exportExternalTallyReportData"
						:empty-text="'暂无外理理货报告信息'"
						:loading="loading"
						:max-height="calculateTableHeight(exportExternalTallyReportData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<!-- 进口相关表格 -->
				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-码头信息</div>
					<vxe-grid
						:columns="importPortColumns"
						:data="importPortData"
						:empty-text="'暂无码头信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importPortData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-原始舱单</div>
					<vxe-grid
						:columns="importManifestColumns"
						:data="importManifestData"
						:empty-text="'暂无原始舱单信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importManifestData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-处理理货报告</div>
					<vxe-grid
						:columns="importTallyColumns"
						:data="importTallyData"
						:empty-text="'暂无处理理货报告'"
						:loading="loading"
						:max-height="calculateTableHeight(importTallyData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-查验委托信息</div>
					<vxe-grid
						:columns="importInspectionColumns"
						:data="importInspectionData"
						:empty-text="'暂无查验委托信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importInspectionData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-在途监管信息</div>
					<vxe-grid
						:columns="importSupervisionColumns"
						:data="importSupervisionData"
						:empty-text="'暂无在途监管信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importSupervisionData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-疏港分流申请</div>
					<vxe-grid
						:columns="importPortEvacuationAndDiversionColumns"
						:data="importPortEvacuationAndDiversionData"
						:empty-text="'暂无疏港分流申请信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importPortEvacuationAndDiversionData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-分拨分流放行</div>
					<vxe-grid
						:columns="importDistributionDiversionReleaseColumns"
						:data="importDistributionDiversionReleaseData"
						:empty-text="'暂无分拨分流放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importDistributionDiversionReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-分拨分流运抵</div>
					<vxe-grid
						:columns="importDistributionDiversionArrivalColumns"
						:data="importDistributionDiversionArrivalData"
						:empty-text="'暂无分拨分流运抵信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importDistributionDiversionArrivalData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-海关报关单放行</div>
					<vxe-grid
						:columns="importCustomsDeclarationReleaseColumns"
						:data="importCustomsDeclarationReleaseData"
						:empty-text="'暂无海关报关单放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importCustomsDeclarationReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>

				<div v-if="model.ieFlag === 'I'" class="table-section">
					<div class="section-title">进口-提单放行（码头进口放行的依据）</div>
					<vxe-grid
						:columns="importBillOfLadingReleaseColumns"
						:data="importBillOfLadingReleaseData"
						:empty-text="'暂无提单放行信息'"
						:loading="loading"
						:max-height="calculateTableHeight(importBillOfLadingReleaseData)"
						border
						size="mini"
						highlightHoverRow
						keep-source
						resizable
						show-overflow="tooltip"
					>
					</vxe-grid>
				</div>
			</a-spin>
		</div>
	</a-modal>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
	name: 'ShipInfoModal',
	data() {
		return {
			title: '单票信息查看',
			visible: false,
			loading: false,
			model: {
				id: '',
				shipName: '',
				voyage: '',
				zwcm: '',
				ywcm: '',
				ieFlag: '',
				zxl: '',
				sxkssj: '',
				sxjssj: ''
			},
			// 出口相关数据
			exportPortData: [],
			exportContainerData: [],
			exportArrivalData: [],
			exportSupervisionData: [],
			exportCustomsReleaseData: [],
			exportCustomsDeclarationReleaseData: [],
			exportLoadingReleaseData: [],
			exportExternalAuditReleaseData: [],
			exportExternalTallyReportData: [],
			// 进口相关数据
			importPortData: [],
			importManifestData: [],
			importTallyData: [],
			importInspectionData: [],
			importSupervisionData: [],
			importPortEvacuationAndDiversionData: [],
			importDistributionDiversionReleaseData: [],
			importDistributionDiversionArrivalData: [],
			importCustomsDeclarationReleaseData: [],
			importBillOfLadingReleaseData: [],

			// 出口表格列定义
			exportPortColumns: [
				{ title: '序号', field: 'serialNumber', width: 60, ellipsis: true, align: 'center' },
				{ title: '码头名称', field: 'MTMC', width: 120, ellipsis: true, align: 'center' },
				{ title: '提单号', field: 'TDH', width: 150, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '流向类别', field: 'LXLB', width: 150, align: 'center' },
				{ title: '箱属', field: 'XSGSM', width: 100, align: 'center' },
				{ title: '尺寸', field: 'CC', width: 100, align: 'center' },
				{ title: '箱型', field: 'XX', width: 100, align: 'center' },
				{ title: '铅封号', field: 'QFH1', width: 100, align: 'center' },
				{ title: '整箱重', field: 'MZ', width: 100, align: 'center' },
				{ title: '空重', field: 'KZ', width: 80, align: 'center' },
				{ title: '箱站代码', field: 'XZDM', width: 100, align: 'center' },
				{ title: '码头放行', field: 'FXBZ', width: 100, align: 'center' },
				{ title: '入港类别', field: 'RGLB', width: 100, align: 'center' },
				{ title: '入港/单据录入时间', field: 'SJRGSJ', width: 150, align: 'center' },
				{ title: '当前状态', field: 'DQZTMC', width: 100, align: 'center' },
				{ title: '出港类别', field: 'CGLB', width: 100, align: 'center' },
				{ title: '出港时间', field: 'SJCGSJ', width: 100, align: 'center' },
				{ title: '堆存天数', field: 'DCT', width: 100, align: 'center' },
				{ title: '地理位置', field: 'GLDWMC', width: 100, align: 'center' },
				{ title: '装货港', field: 'ZHGM', width: 100, align: 'center' },
				{ title: '卸货港', field: 'XHGM', width: 100, align: 'center' },
				{ title: '目的港', field: 'MDGM', width: 100, align: 'center' },
				{ title: '特殊装卸需求', field: 'TSZZXQMC', width: 100, align: 'center' },
				{ title: '内外贸', field: 'NWM', width: 100, align: 'center' },
				{ title: '危品IMO', field: 'IMO', width: 100, align: 'center' },
				{ title: '冷冻箱标志', field: 'LDXBZ', width: 100, align: 'center' },
				{ title: '冷冻温度', field: 'LDWD', width: 100, align: 'center' },
				{ title: '湿度', field: '', width: 100, align: 'center' },
				{ title: '通风', field: '', width: 100, align: 'center' },
				{ title: '危品标志', field: 'WPBZ', width: 100, align: 'center' },
				{ title: '国际危规', field: 'GJWGH', width: 100, align: 'center' },
				{ title: '入港明细', field: 'RGMX', width: 100, align: 'center' },
				{ title: '出港明细', field: 'CGMX', width: 100, align: 'center' },
				{ title: '锁箱', field: 'KYYY', width: 100, align: 'center' },
				{ title: '超高', field: 'CGCM', width: 100, align: 'center' },
				{ title: '左超', field: 'ZCCM', width: 100, align: 'center' },
				{ title: '右超', field: 'YCCM', width: 100, align: 'center' },
				{ title: '前超', field: 'QCCM', width: 100, align: 'center' },
				{ title: '后超', field: 'HCCM', width: 100, align: 'center' }
			],
			exportContainerColumns: [
				{ title: '码头名称', field: 'mtmc', width: 120, align: 'center' },
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '空箱出场时间', field: 'zxcbsj', width: 150, align: 'center' },
				{ title: '重箱回场时间', field: 'zxcbsj2', width: 150, align: 'center' },
				{ title: '卸货港', field: 'fhdm', width: 100, align: 'center' },
				{ title: '箱站代码', field: 'hwdm', width: 100, align: 'center' },
				{ title: '箱号', field: 'ch', width: 100, align: 'center' },
				{ title: '分单号', field: 'ftdh', width: 150, align: 'center' },
				{ title: '内外贸', field: 'nl', width: 80, align: 'center' },
				{ title: '尺寸', field: 'cc', width: 100, align: 'center' },
				{ title: '箱型', field: 'hw', width: 100, align: 'center' },
				{ title: '箱属', field: 'cb', width: 100, align: 'center' },
				{ title: '称重方式', field: 'zzfs', width: 100, align: 'center' },
				{ title: '回执状态', field: 'hdzt', width: 100, align: 'center' },
				{ title: '回执描述', field: 'hdsj', width: 150, align: 'center' },
				{ title: '提前申报标志', field: 'gqzz', width: 100, align: 'center' }
			],
			exportArrivalColumns: [
				{ title: '码头名称', field: 'FSDW', width: 120, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 120, align: 'center' },
				{ title: '提单号', field: 'TDH', width: 150, align: 'center' },
				{ title: '英文船名', field: 'YWCM', width: 150, align: 'center' },
				{ title: '航次', field: 'HCHBBH', width: 100, align: 'center' },
				{ title: '箱站代码', field: 'XZDM', width: 100, align: 'center' },
				{ title: '关区代码', field: 'XHDGQDM', width: 100, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '海关回执描述', field: 'HZMS', width: 150, align: 'center' },
				{ title: '发送时间', field: 'IMPORT_TIME', width: 150, align: 'center' },
				{ title: '海关回执时间', field: 'HZSJ', width: 150, align: 'center' },
				{ title: '整箱重', field: 'zxd', width: 100, align: 'center' },
				{ title: '货重', field: 'hw', width: 100, align: 'center' },
				{ title: '体积', field: 'tj', width: 100, align: 'center' },
				{ title: '件数', field: 'cb', width: 100, align: 'center' }
			],
			exportSupervisionColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '箱号', field: 'hh', width: 100, align: 'center' },
				{ title: '启运地', field: 'hyd', width: 120, align: 'center' },
				{ title: '目的地', field: 'mdd', width: 120, align: 'center' },
				{ title: '海关回执', field: 'cysj', width: 150, align: 'center' },
				{ title: '回执时间', field: 'hzsj', width: 150, align: 'center' }
			],
			exportCustomsReleaseColumns: [
				{ title: '报关单号', field: 'ENTRY_ID', width: 150, align: 'center' },
				{ title: '分提单号', field: 'BILL_NO', width: 150, align: 'center' },
				{ title: '英文船名', field: 'FLAGHT_NO', width: 150, align: 'center' },
				{ title: '航次', field: 'VOYAGE_NO', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 120, align: 'center' },
				{ title: '关区代码', field: 'I_E_PORT', width: 100, align: 'center' },
				{ title: '放行时间', field: 'R_DATE', width: 150, align: 'center' },
				{ title: '放行件数', field: 'PASS_PACK_NO', width: 150, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '净重', field: 'NET_WT', width: 100, align: 'center' },
				{ title: '毛重', field: 'GROS_WT', width: 100, align: 'center' },
				{ title: '海关状态', field: 'WAREHOUSE_CODE', width: 100, align: 'center' },
				{ title: '放行类别', field: 'FXLBMC', width: 100, align: 'center' }
			],
			exportCustomsDeclarationReleaseColumns: [
				{ title: '报关单号', field: 'bgdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'imoNo', width: 120, align: 'center' },
				{ title: '英文船名', field: 'ywcm2', width: 150, align: 'center' },
				{ title: '放行时间', field: 'fxsj', width: 150, align: 'center' },
				{ title: '箱号', field: 'xh', width: 100, align: 'center' },
				{ title: '毛重', field: 'mz', width: 100, align: 'center' },
				{ title: '净重', field: 'jz', width: 100, align: 'center' },
				{ title: '海关状态', field: 'hgzt', width: 100, align: 'center' }
			],
			exportLoadingReleaseColumns: [
				{ title: '提单号', field: 'TDH', width: 150, align: 'center' },
				{ title: '英文船名', field: 'YWCM', width: 150, align: 'center' },
				{ title: '航次', field: 'HCHC', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 120, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '放行时间', field: 'RDATE', width: 150, align: 'center' },
				{ title: '海关指令', field: 'SCBZMC', width: 150, align: 'center' },
				{ title: '接收时间', field: 'IMPORT_TIME', width: 150, align: 'center' }
			],
			exportExternalAuditReleaseColumns: [
				{ title: '提单号', field: 'MAIN_BILL_NO', width: 150, align: 'center' },
				{ title: '英文船名', field: 'YWCM', width: 150, align: 'center' },
				{ title: '航次', field: 'HC', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 120, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '分单号', field: 'BILL_NO', width: 150, align: 'center' }
			],
			exportExternalTallyReportColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'imoNo', width: 120, align: 'center' },
				{ title: '英文船名', field: 'ywcm2', width: 150, align: 'center' },
				{ title: '箱号', field: 'xh', width: 100, align: 'center' },
				{ title: '件数', field: 'js', width: 80, align: 'center' },
				{ title: '体积', field: 'tj', width: 100, align: 'center' },
				{ title: '理货', field: 'lh', width: 100, align: 'center' }
			],
			// 进口表格列定义
			importPortColumns: [
				{ title: '序号', field: 'serialNumber', width: 60, ellipsis: true, align: 'center' },
				{ title: '码头名称', field: 'MTMC', width: 120, ellipsis: true, align: 'center' },
				{ title: '提单号', field: 'TDH', width: 150, align: 'center' },
				{ title: '箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '流向类别', field: 'LXLB', width: 150, align: 'center' },
				{ title: '箱属', field: 'XSGSM', width: 100, align: 'center' },
				{ title: '尺寸', field: 'CC', width: 100, align: 'center' },
				{ title: '箱型', field: 'XX', width: 100, align: 'center' },
				{ title: '铅封号', field: 'QFH1', width: 100, align: 'center' },
				{ title: '整箱重', field: 'MZ', width: 100, align: 'center' },
				{ title: '空重', field: 'KZ', width: 80, align: 'center' },
				{ title: '箱站代码', field: 'XZDM', width: 100, align: 'center' },
				{ title: '码头放行', field: 'FXBZ', width: 100, align: 'center' },
				{ title: '入港类别', field: 'RGLB', width: 100, align: 'center' },
				{ title: '入港/单据录入时间', field: 'SJRGSJ', width: 150, align: 'center' },
				{ title: '当前状态', field: 'DQZTMC', width: 100, align: 'center' },
				{ title: '出港类别', field: 'CGLB', width: 100, align: 'center' },
				{ title: '出港时间', field: 'SJCGSJ', width: 100, align: 'center' },
				{ title: '堆存天数', field: 'DCT', width: 100, align: 'center' },
				{ title: '地理位置', field: 'GLDWMC', width: 100, align: 'center' },
				{ title: '装货港', field: 'ZHGM', width: 100, align: 'center' },
				{ title: '卸货港', field: 'XHGM', width: 100, align: 'center' },
				{ title: '目的港', field: 'MDGM', width: 100, align: 'center' },
				{ title: '特殊装卸需求', field: 'TSZZXQMC', width: 100, align: 'center' },
				{ title: '内外贸', field: 'NWM', width: 100, align: 'center' },
				{ title: '危品IMO', field: 'IMO', width: 100, align: 'center' },
				{ title: '冷冻箱标志', field: 'LDXBZ', width: 100, align: 'center' },
				{ title: '冷冻温度', field: 'LDWD', width: 100, align: 'center' },
				{ title: '湿度', field: '', width: 100, align: 'center' },
				{ title: '通风', field: '', width: 100, align: 'center' },
				{ title: '危品标志', field: 'WPBZ', width: 100, align: 'center' },
				{ title: '国际危规', field: 'GJWGH', width: 100, align: 'center' },
				{ title: '入港明细', field: 'RGMX', width: 100, align: 'center' },
				{ title: '出港明细', field: 'CGMX', width: 100, align: 'center' },
				{ title: '锁箱', field: 'KYYY', width: 100, align: 'center' },
				{ title: '超高', field: 'CGCM', width: 100, align: 'center' },
				{ title: '左超', field: 'ZCCM', width: 100, align: 'center' },
				{ title: '右超', field: 'YCCM', width: 100, align: 'center' },
				{ title: '前超', field: 'QCCM', width: 100, align: 'center' },
				{ title: '后超', field: 'HCCM', width: 100, align: 'center' }
			],
			importManifestColumns: [
				{ title: '提单号', field: 'MAIN_BILL_NO', width: 150, align: 'center' },
				{ title: '分提单号', field: 'BILL_OF_LADING_NO', width: 150, align: 'center' },
				{ title: '中文船名', field: 'VESSEL_NAME_CN', width: 150, align: 'center' },
				{ title: '英文船名', field: 'VESSEL_NAME_EN', width: 150, align: 'center' },
				{ title: '航次', field: 'VOYAGE_NUMBER', width: 100, align: 'center' },
				{ title: '空重', field: 'ky', width: 80, align: 'center' },
				{ title: '箱号', field: 'CONTAINER_NO', width: 100, align: 'center' },
				{ title: '尺寸', field: 'CONTAINER_SIZE', width: 100, align: 'center' },
				{ title: '箱型', field: 'CONTAINER_TYPE', width: 100, align: 'center' },
				{ title: '铅封号', field: 'SEAL_NO', width: 100, align: 'center' },
				{ title: '件数', field: 'CARGO_QUANTITY', width: 80, align: 'center' },
				{ title: '货重', field: 'TOTAL_GROSS_WEIGHT', width: 80, align: 'center' },
				{ title: '体积', field: 'TOTAL_VOLUMN', width: 80, align: 'center' }
			],
			importTallyColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'imoNo', width: 120, align: 'center' },
				{ title: '关区代码', field: 'fsdm', width: 100, align: 'center' },
				{ title: '箱号', field: 'xh', width: 100, align: 'center' },
				{ title: '件数', field: 'js', width: 80, align: 'center' },
				{ title: '重数', field: 'tj', width: 100, align: 'center' },
				{ title: '体积', field: 'tj2', width: 100, align: 'center' },
				{ title: '发送时间', field: 'fssj', width: 150, align: 'center' },
				{ title: '回执说明', field: 'hzsj', width: 150, align: 'center' },
				{ title: '回执时间', field: 'hzsj2', width: 150, align: 'center' },
				{ title: '删除标志', field: 'cycbys', width: 120, align: 'center' }
			],
			importInspectionColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '箱号', field: 'hh', width: 100, align: 'center' },
				{ title: '船名', field: 'cm', width: 120, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: '单证号码', field: 'cysj', width: 150, align: 'center' },
				{ title: '检查地点', field: 'cywt', width: 120, align: 'center' },
				{ title: '是否自备车', field: 'cyjg', width: 120, align: 'center' }
			],
			importSupervisionColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '箱号', field: 'hh', width: 100, align: 'center' },
				{ title: '启运地', field: 'hyd', width: 120, align: 'center' },
				{ title: '目的地', field: 'mdd', width: 120, align: 'center' },
				{ title: '海关回执', field: 'cysj', width: 150, align: 'center' },
				{ title: '回执时间', field: 'hzsj', width: 150, align: 'center' }
			],
			importPortEvacuationAndDiversionColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'imoNo', width: 120, align: 'center' },
				{ title: '卸货地代码', field: 'fsdm', width: 100, align: 'center' },
				{ title: '分流目的地', field: 'fxsj', width: 150, align: 'center' },
				{ title: '目的关区代码', field: 'fxdh', width: 150, align: 'center' },
				{ title: '地理位置', field: 'hh', width: 100, align: 'center' },
				{ title: '回执说明', field: 'hzsj', width: 150, align: 'center' },
				{ title: '回执时间', field: 'hzsj2', width: 150, align: 'center' }
			],
			importDistributionDiversionReleaseColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'imoNo', width: 120, align: 'center' },
				{ title: '箱号', field: 'hh', width: 100, align: 'center' },
				{ title: '放行时间', field: 'hh', width: 100, align: 'center' },
				{ title: '放行类别', field: 'hzsj', width: 150, align: 'center' },
				{ title: '业务类型说明', field: '', width: 150, align: 'center' }
			],
			importDistributionDiversionArrivalColumns: [
				{ title: '提单号', field: 'tdh', width: 150, align: 'center' },
				{ title: '英文船名', field: 'ywcm', width: 150, align: 'center' },
				{ title: '航次', field: 'hc', width: 100, align: 'center' },
				{ title: '箱号', field: 'hh', width: 100, align: 'center' },
				{ title: '卸货地代码', field: 'fsdm', width: 100, align: 'center' },
				{ title: '卸货地关区', field: 'hzsj', width: 150, align: 'center' },
				{ title: '发送单位', field: '', width: 150, align: 'center' },
				{ title: '回执说明', field: '', width: 150, align: 'center' },
				{ title: '回执时间', field: '', width: 150, align: 'center' }
			],
			importCustomsDeclarationReleaseColumns: [
				{ title: '报关单号', field: 'ENTRY_ID', width: 150, align: 'center' },
				{ title: '分提单号', field: 'BILL_NO', width: 150, align: 'center' },
				{ title: '英文船名', field: 'FLAGHT_NO', width: 100, align: 'center' },
				{ title: '航次', field: 'VOYAGE_NO', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 100, align: 'center' },
				{ title: '关区代码', field: 'I_E_PORT', width: 150, align: 'center' },
				{ title: '放行时间', field: 'R_DATE', width: 150, align: 'center' },
				{ title: '放行件数', field: 'PASS_PACK_NO', width: 150, align: 'center' },
				{ title: '箱号', field: 'XH', width: 150, align: 'center' },
				{ title: '净重', field: 'NET_WT', width: 150, align: 'center' },
				{ title: '毛重', field: 'GROS_WT', width: 150, align: 'center' },
				{ title: '海关状态', field: 'WAREHOUSE_CODE', width: 150, align: 'center' },
				{ title: '放行类别', field: 'FXLBMC', width: 150, align: 'center' }
			],
			importBillOfLadingReleaseColumns: [
				{ title: '提单号', field: 'BILL_NO', width: 150, align: 'center' },
				{ title: '英文船名', field: 'FLAGHT_NO', width: 150, align: 'center' },
				{ title: '航次', field: 'VOYAGE_NO', width: 100, align: 'center' },
				{ title: 'IMO号', field: 'CIMO', width: 120, align: 'center' },
				{ title: '支线英文船名', field: 'YWCM2', width: 100, align: 'center' },
				{ title: '支线航次', field: 'HCHC2', width: 150, align: 'center' },
				{ title: '报关单号', field: 'ENTRY_ID', width: 100, align: 'center' },
				{ title: '放行时间', field: 'R_DATE', width: 100, align: 'center' },
				{ title: '放行件数', field: 'PASS_PACK_NO', width: 100, align: 'center' },
				{ title: '放行箱号', field: 'XH', width: 100, align: 'center' },
				{ title: '净重', field: 'NET_WT', width: 100, align: 'center' },
				{ title: '毛重', field: 'GROS_WT', width: 100, align: 'center' },
				{ title: '海关状态', field: 'WAREHOUSE_CODE', width: 100, align: 'center' },
				{ title: '海关状态放行类别', field: 'FXLBMC', width: 100, align: 'center' }
			]
		}
	},
	computed: {
		formDisabled() {
			return this.disabled
		}
	},
	methods: {
		async open(record) {
			if (this.isEmpty(record)) {
				this.$message.warning('报关数据未找到!')
				return false
			}

			// 立即显示模态框，提升用户体验
			this.model = Object.assign({}, record)
			this.model.ywcm = record.shipName
			this.visible = true

			// 加载数据
			await this.loadDataInternal(false)
		},
		/**
		 * 内部数据加载方法
		 * @param showSuccessMessage 是否显示刷新成功消息
		 */
		async loadDataInternal(showSuccessMessage = false) {
			if (!this.model || !this.model.id) {
				console.warn('没有可用的记录ID，无法加载数据')
				return
			}

			this.loading = true
			this.clearAllData() // 清空当前数据

			try {
				// 这里调用后端接口获取船信息数据
				const res = await getAction('/business/shipPlan/getShipInfoByDecId', {
					decId: this.model.id
				})

				if (res.success) {
					// 根据进出口标识加载不同的数据
					this.loadShipData(res.result || {})
					if (showSuccessMessage) {
						// this.$message.success('数据刷新成功')
					}
				} else {
					this.clearAllData()
					// this.$message.warning(res.message || '查询船信息数据失败')
				}
			} catch (error) {
				console.error('查询船信息数据异常：', error)
				this.clearAllData()
				this.$message.error('查询船信息数据异常，请稍后重试')
			} finally {
				this.loading = false
			}
		},
		loadShipData(data) {
			// 加载基础信息
			if (data.baseInfo) {
				this.model = Object.assign(this.model, data.baseInfo)
			}

			// 根据进出口标识加载对应数据
			if (this.model.ieFlag === 'E') {
				// 出口数据
				this.exportPortData = data.portInfo || []
				this.exportContainerData = data.containerInfo || []
				this.exportArrivalData = data.arrivalInfo || []
				this.exportSupervisionData = data.supervisionInfo || []
				this.exportCustomsReleaseData = data.customsReleaseInfo || []
				this.exportCustomsDeclarationReleaseData = data.customsDeclarationReleaseInfo || []
				this.exportLoadingReleaseData = data.loadingReleaseInfo || []
				this.exportExternalAuditReleaseData = data.externalAuditReleaseInfo || []
				this.exportExternalTallyReportData = data.externalTallyReportInfo || []
			} else if (this.model.ieFlag === 'I') {
				// 进口数据
				this.importPortData = this.addSerialNumber(data.portInfo || [])
				this.importManifestData = data.manifestInfo || []
				this.importTallyData = data.tallyInfo || []
				this.importInspectionData = data.inspectionInfo || []
				this.importSupervisionData = data.supervisionInfo || []
				this.importPortEvacuationAndDiversionData = data.portEvacuationAndDiversionInfo || []
				this.importDistributionDiversionReleaseData = data.distributionDiversionReleaseInfo || []
				this.importDistributionDiversionArrivalData = data.distributionDiversionArrivalInfo || []
				this.importCustomsDeclarationReleaseData = data.customsDeclarationReleaseInfo || []
				this.importBillOfLadingReleaseData = data.billOfLadingReleaseInfo || []
			}
		},
		clearAllData() {
			// 清空所有数据
			this.exportPortData = []
			this.exportContainerData = []
			this.exportArrivalData = []
			this.exportSupervisionData = []
			this.exportCustomsReleaseData = []
			this.exportCustomsDeclarationReleaseData = []
			this.exportLoadingReleaseData = []
			this.exportExternalAuditReleaseData = []
			this.exportExternalTallyReportData = []
			this.importPortData = []
			this.importManifestData = []
			this.importTallyData = []
			this.importInspectionData = []
			this.importSupervisionData = []
			this.importPortEvacuationAndDiversionData = []
			this.importDistributionDiversionReleaseData = []
			this.importDistributionDiversionArrivalData = []
			this.importCustomsDeclarationReleaseData = []
			this.importBillOfLadingReleaseData = []
		},
		close() {
			this.clearAllData()
			this.visible = false
		},
		handleCancel() {
			this.close()
		},
		isEmpty(obj) {
			return obj == null || obj === undefined || obj === ''
		},
		/**
		 * 为数组数据添加序号字段
		 * @param dataArray 原始数据数组
		 * @returns {Array} 添加了序号的数据数组
		 */
		addSerialNumber(dataArray) {
			if (!Array.isArray(dataArray)) {
				return []
			}
			return dataArray.map((item, index) => {
				return {
					...item,
					serialNumber: index + 1
				}
			})
		},
		/**
		 * 动态计算表格高度
		 * @param dataArray 数据数组
		 * @param minHeight 最小高度
		 * @param maxHeight 最大高度
		 * @param rowHeight 每行高度
		 * @returns {number} 计算后的表格高度
		 */
		calculateTableHeight(dataArray, minHeight = 80, maxHeight = 300, rowHeight = 28) {
			if (!Array.isArray(dataArray) || dataArray.length === 0) {
				return minHeight
			}

			// 计算所需高度：表头高度(32) + 数据行高度 + 边框等额外高度(8)
			const headerHeight = 32
			const extraHeight = 8
			const dataHeight = dataArray.length * rowHeight
			const totalHeight = headerHeight + dataHeight + extraHeight

			// 确保在最小和最大高度范围内
			return Math.min(Math.max(totalHeight, minHeight), maxHeight)
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
.ship-info-header {
	margin-bottom: 16px;
	padding: 12px;
	background: #f5f5f5;
	border-radius: 4px;

	.info-row {
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.info-label {
		font-weight: bold;
		font-size: 14px;
		color: #333;
	}

	.info-item {
		display: inline-block;
		margin-right: 24px;
		font-size: 13px;
		color: #666;

		.info-value {
			color: #333;
			font-weight: 500;
		}
	}
}

.ship-tables-container {
	max-height: 600px;
	overflow-y: auto;
}

.table-section {
	margin-bottom: 16px;

	.section-title {
		font-weight: bold;
		font-size: 14px;
		color: #333;
		margin-bottom: 8px;
		padding: 8px 12px;
		background: #e6f7ff;
		border-left: 4px solid #1890ff;
	}
}

/deep/ .ant-modal-body {
	padding: 16px;
}

/deep/ .vxe-grid {
	.vxe-table .vxe-header--column {
		height: 32px;
		background: #fafafa;
	}

	.vxe-table .vxe-body--column {
		height: 28px;
	}
}
</style>
