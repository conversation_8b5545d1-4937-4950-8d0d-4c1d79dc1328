package org.jeecg.config.oss;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * 云存储 配置
 * 
 * @author: jeecg-boot
 */
@Slf4j
@Configuration
public class OssConfiguration {

    @Value("${jeecg.oss.endpoint}")
    private String endpoint;
    @Value("${jeecg.oss.accessKey}")
    private String accessKeyId;
    @Value("${jeecg.oss.secretKey}")
    private String accessKeySecret;
    @Value("${jeecg.oss.bucketName}")
    private String bucketName;
    @Value("${jeecg.oss.staticDomain:}")
    private String staticDomain;

    @Bean
    public String initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);

        log.info("OSS配置初始化完成 - endpoint: {}, bucketName: {}", endpoint, bucketName);
        return "ossConfigInitialized";
    }

    /**
     * 应用关闭时优雅关闭OSS客户端
     */
    @PreDestroy
    public void destroy() {
        log.info("应用关闭，开始释放OSS连接资源...");
        OssBootUtil.shutdown();
    }
}