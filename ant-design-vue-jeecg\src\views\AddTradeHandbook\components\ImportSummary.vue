<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件序号" v-model="queryParam.gNo"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="料件料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入料件料号" v-model="queryParam.copGno"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
						<a-form-item label="商品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入商品名称" v-model="queryParam.gName"></a-input>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :sm="24" :xxl="6" :md="12">
							<span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
								<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
								<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							</span>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- table区域-begin -->
		<div style="margin-top: -15px;">
			<a-table
				ref="table"
				size="small"
				:scroll="{ x: true }"
				bordered
				rowKey="gNo"
				:columns="columns"
				:dataSource="dataSource"
				:pagination="ipagination"
				:loading="loading"
				:expandIconAsCell="true"
				:expandIconColumnIndex="0"
				class="j-table-force-nowrap"
				@change="handleTableChange"
				@expand="expandEvent"
			>
				<span slot="gModelSlots" slot-scope="text, record" :title="record.gModel">
          {{subStrForColumns(record.gModel, 25)}}
        </span>
				<template slot="qty" slot-scope="text, record">
					<span :class="{ textGreen: record.qty > 0 }">
						{{ record.qty }}
					</span>
				</template>
				<template slot="expandedRowRender" slot-scope="record" style="margin: 0">
					<div class="table-operator">
						<a-row :gutter="24">
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<div style="font-weight: bold;margin-bottom: 5px">报关数据</div>
							</a-col>
						</a-row>
					</div>
					<a-table
						ref="table1"
						size="small"
						:scroll="{ x: true }"
						bordered
						rowKey="id"
						:columns="decColumns"
						:dataSource="record.dataSourceDec"
						:pagination="false"
						:loading="loadingDec"
						class="j-table-force-nowrap"
						@change="handleTableChange"
					></a-table>
					<div class="table-operator">
						<a-row :gutter="24">
							<a-col :xl="6" :sm="24" :xxl="6" :md="12">
								<div style="font-weight: bold;margin-bottom: 5px;margin-top: 15px">清单数据</div>
							</a-col>
						</a-row>
					</div>
					<a-table
						ref="table2"
						size="small"
						:scroll="{ x: true }"
						bordered
						rowKey="id"
						:columns="invtColumns"
						:dataSource="record.dataSourceInvt"
						:pagination="false"
						:loading="loadingInvt"
						class="j-table-force-nowrap"
						@change="handleTableChange"
					></a-table>
				</template>
			</a-table>
		</div>
	</a-card>
</template>
<script>
import {JeecgListMixin} from "@/mixins/JeecgListMixin";
import {mixinDevice} from "@/utils/mixin";
import {getAction} from "@/api/manage";
import {subStrForColumns} from "@/utils/util";
import {filterDictTextByCache} from "@/components/dict/JDictSelectUtil";

export default {
	name: "ImportSummary",
	mixins: [JeecgListMixin, mixinDevice],
	data() {
		return {
			disableMixinCreated: true,
			loadingInvt: false,
			loadingDec: false,
			queryParam: {
				type: '1', // 1料件 2成品 3损耗
				emsId: '999999999'
			},
			/* 排序参数 */
			isorter: {
				column: 'createDate',
				order: 'desc'
			},
			emsHead: {},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl:{ span: 5},
				xl:{ span: 9}
			},
			wrapperCol: {
				xs: { span: 16 },
				// sm: { span: 16 },
			},
			// 表头
			columns: [
				{
					title: '料件序号',
					align: 'center',
					dataIndex: 'gNo'
				},
				{
					title: '料件料号',
					align: 'center',
					dataIndex: 'copGno'
				},
				{
					title: '商品名称',
					align: 'center',
					dataIndex: 'gName'
				},
				{
					title: '商品编码',
					align: 'center',
					dataIndex: 'codet'
				},
				{
					title: '规格型号',
					align: 'center',
					dataIndex: 'gModel',
					scopedSlots: { customRender: 'gModelSlots' }
				},
				{
					title: '备案数量',
					align: 'center',
					dataIndex: 'qty',
					scopedSlots: { customRender: 'qty' }
				},
				{
					title: '手册余量',
					align: 'center',
					dataIndex: 'scyl'
				},
				{
					title: '余量比例%',
					align: 'center',
					dataIndex: 'ylbl'
				},
				{
					title: '直接进口数量(A)',
					align: 'center',
					dataIndex: 'zjjksl'
				},
				{
					title: '深加工结转进口数量(B)',
					align: 'center',
					dataIndex: 'sjgjzjksl'
				},
				{
					title: '余料结转进口数量(C)',
					align: 'center',
					dataIndex: 'yljzjksl'
				},
				{
					title: '料件退换进口数量(D)',
					align: 'center',
					dataIndex: 'ljthjksl'
				},
				{
					title: '料件退换出口数量(E)',
					align: 'center',
					dataIndex: 'ljthcksl'
				},
				{
					title: '内销征税数量(F)',
					align: 'center',
					dataIndex: 'nxzssl'
				},
				{
					title: '转复出数量(G)',
					align: 'center',
					dataIndex: 'zfcsl'
				},
				{
					title: '余料出口结转数量(H)',
					align: 'center',
					dataIndex: 'ylckjzsl'
				},
				{
					title: '销毁数量(I)',
					align: 'center',
					dataIndex: 'xhsl'
				},
				{
					title: '实际进口数量(J=A+B+C+D-E-F-G-H-I)',
					align: 'center',
					dataIndex: 'sjjksl'
				},
			],
			decColumns: [
				{
					title: '统一编号',
					align: 'center',
					sorter: false,
					dataIndex: 'seqNo',
					// scopedSlots: { customRender: 'seqNo' },
				},
				{
					title: '报关单号',
					align: 'center',
					sorter: false,
					dataIndex: 'clearanceNo',
				},
				{
					title: '提运单号',
					align: 'center',
					sorter: false,
					dataIndex: 'billCode',
				},
				{
					title: '申报状态',
					align: 'center',
					sorter: false,
					dataIndex: 'decStatus',
					customRender: (text) => {
						return this.formatterDecStatus(text)
					}
				},
				{
					title: '境内收发货人',
					align: 'center',
					sorter: false,
					dataIndex: 'optUnitName',
				},
				{
					title: '进出口',
					align: 'center',
					sorter: false,
					dataIndex: 'ieFlag',
					customRender:function (text) {
						if(text=='I'){
							return "进口";
						}else if(text=='E'){
							return "出口";
						}else{
							return text;
						}
					}
				},
				{
					title: '报关单类型',
					align: 'center',
					sorter: false,
					dataIndex: 'dclTrnRelFlag',
					customRender:function (text) {
						if(text=='0'){
							return "一般报关单";
						}else if(text=='1'){
							return "转关提前报关单";
						}else if(text=='2'){
							return "备案清单";
						}else if(text=='3'){
							return "转关提前备案清单";
						}else if(text=='4'){
							return "出口二次转关";
						}else{
							return text;
						}
					}
				},
				{
					title: '备案号',
					align: 'center',
					sorter: false,
					dataIndex: 'recordNumber',
				},
				{
					title: '监管方式',
					align: 'center',
					sorter: false,
					dataIndex: 'tradeTypeCode',
					customRender: (text) => {
						//字典值替换通用方法
						return filterDictTextByCache('JGFS', text);
					}
				},
				{
					title: '消费使用单位',
					align: 'center',
					sorter: false,
					dataIndex: 'deliverUnitName',
				},
				{
					title: '申报日期',
					align: 'center',
					sorter: false,
					dataIndex: 'appDate',
				},
				{
					title: '订单号',
					align: 'center',
					sorter: false,
					dataIndex: 'orderProtocolNo',
				},
				{
					title: '是否有舱单',
					align: 'center',
					dataIndex: 'hasCd',
					customRender: function (text) {
						if (text == '1') {
							return "是";
						} else {
							return '否';
						}
					}
				},
				{
					title: '运输方式',
					align: 'center',
					sorter: false,
					dataIndex: 'shipTypeCode_dictText',
				},
				{
					dataIndex: 'initialReviewStatus',
					title: '初复审状态',
					align: 'center',
					width: 120,
					customRender: function (cellValue) {
						if (cellValue=='0'){
							return '未审核'
						}else if(cellValue=='1'){
							return "已初审/未复审";
						}else if(cellValue=='2'){
							return '已复审'
						}else {
							return '未审核'
						}
					},
				},
				{
					dataIndex: 'firstTrialBy',
					align: 'center',
					title: '初审人'
				},
				{
					dataIndex: 'firstTrialDate',
					align: 'center',
					title: '初审时间'
				},
				{
					dataIndex: 'firstOpinion',
					align: 'center',
					title: '初审意见',
					scopedSlots: { customRender: 'firstOpinion' }
				},
				{
					dataIndex: 'reviewBy',
					align: 'center',
					title: '复审人'
				},
				{
					dataIndex: 'reviewDate',
					align: 'center',
					width: 200,
					title: '复审时间'
				},
				{
					dataIndex: 'reviewOpinion',
					align: 'center',
					width: 200,
					title: '复审意见',
					scopedSlots: { customRender: 'reviewOpinion' }
				}
			],
			invtColumns: [
				{
					title: '统一编号',
					align: 'center',
					dataIndex: 'seqNo'
				},
				{
					title: '清单编号',
					align: 'center',
					dataIndex: 'bondInvtNo',
				},
				{
					title: '企业内部编号',
					align: 'center',
					dataIndex: 'etpsInnerInvtNo',
				},
				{
					title: '手账册编号',
					align: 'center',
					dataIndex: 'putrecNo',
				},
				{
					title: '经营单位',
					align: 'center',
					dataIndex: 'bizopEtpsNm',
				},
				{
					title: '进出口',
					align: 'center',
					dataIndex: 'impexpMarkcd',
					customRender:function (text) {
						if(text=='I'){
							return "进口"
						}else if(text=='E'){
							return "出口"
						}else{
							return text
						}
					}
				},
				{
					title: '核扣标志',
					align: 'center',
					dataIndex: 'vrfdedMarkcd',
					customRender: function (text) {
						if (text == '0') {
							return "未核扣"
						} else if (text == '1'){
							return "预核扣"
						} else if (text == '2'){
							return "已核扣"
						} else {
							return text
						}
					}
				},
				{
					title: '已核扣日期',
					align: 'center',
					dataIndex: 'warehousingDate'
				},
				{
					title: '申报日期',
					align: 'center',
					dataIndex: 'invtDclTime'
				},
				{
					title: '创建人',
					align: 'center',
					dataIndex: 'createPerson'
				},
				{
					title: '创建日期',
					align: 'center',
					dataIndex: 'createDate'
				},
				{
					dataIndex: 'firstTrialBy',
					align: 'center',
					title: '初审人'
				},
				{
					dataIndex: 'firstTrialDate',
					align: 'center',
					title: '初审时间'
				},
				{
					dataIndex: 'firstOpinion',
					align: 'center',
					title: '初审意见',
					scopedSlots: { customRender: 'firstOpinion' }
				},
				{
					dataIndex: 'reviewBy',
					align: 'center',
					title: '复审人'
				},
				{
					dataIndex: 'reviewDate',
					align: 'center',
					width: 200,
					title: '复审时间'
				},
				{
					dataIndex: 'reviewOpinion',
					align: 'center',
					width: 200,
					title: '复审意见',
					scopedSlots: { customRender: 'reviewOpinion' }
				},
				{
					title: '是否生成入库单',
					dataIndex: 'hasSC',
					align: 'center',
					customRender: (text, record, index) => {
						if (record.impexpMarkcd === 'I' && text === '1') {
							return '是'
						} else if (record.impexpMarkcd === 'E') {
							return '-'
						} else {
							return '否'
						}
					}
				},
				{
					title: '关联出入库单编号',
					dataIndex: 'storageNos',
					align: 'center',
					scopedSlots: { customRender: 'storageNos' }
				},
				{
					title: '是否推送',
					dataIndex: 'send',
					align: 'center',
					customRender: (text, record, index) => {
						if (record.send === true) {
							return '是'
						} else {
							return '否'
						}
					}
				}
			],
			url: {
				list: '/business/ems/listEmsDetailByReport',
				listDec: '/dcl/invt/listDecByEmsNo',
				listInvt: '/dcl/invt/listInvtByEmsNo',
			}
		}
	},
	methods: {
		async expandEvent(expanded, record) {
			console.log(expanded)
			console.log(record)
			if (expanded) {
				this.loadingDec = true
				await getAction(this.url.listDec, {
					emsNo: record.emsNo,
					putrecSeqno: record.gNo,
					impexpMarkcd: 'I'
				})
					.then(res => {
						if (res.success) {
							record.dataSourceDec = res.result.records || res.result
						} else {
							this.$message.warning(res.message || res)
						}
					})
					.finally(() => {
						this.loadingDec = false
					})

				this.loadingInvt = true
				await getAction(this.url.listInvt, {
					emsNo: record.emsNo,
					putrecSeqno: record.gNo,
					impexpMarkcd: 'I'
				})
					.then(res => {
						if (res.success) {
							record.dataSourceInvt = res.result.records || res.result
						} else {
							this.$message.warning(res.message || res)
						}
					})
					.finally(() => {
						this.loadingInvt = false
					})
			}
		},
		subStrForColumns,
		init(record) {
			this.emsHead = Object.assign({}, record)
			this.queryParam.emsId = this.emsHead.id ? this.emsHead.id : '999999999'
			this.queryParam.emsNo = this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			this.onClearSelected()
			this.loadData(1)
		},
		loadData(arg) {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.ipagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(this.url.list, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result
						if (this.dataSource.length > 0) {
							this.dataSource.forEach(item => {
								item.dataSourceDec = []
								item.dataSourceInvt = []
							})
						}
						if (res.result.total) {
							this.ipagination.total = res.result.total
						} else {
							this.ipagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					// this.handleEmptyIcon(params.pageSize)
					this.loading = false
				})
		},
		searchReset() {
			this.queryParam = {
				type: '1', // 1料件 2成品 3损耗
				emsId: this.emsHead.id ? this.emsHead.id : '999999999',
				emsNo : this.emsHead.emsNo ?  this.emsHead.emsNo : '999999999'
			}
			this.loadData(1)
			this.onClearSelected()
		},
				formatterDecStatus(text) {
			let list = [
				{ title: '保存', value: '1' },
				{ title: '结关', value: '10' },
				{ title: '查验通知', value: '11' },
				{ title: '已申报', value: '2' },
				{ title: '海关入库成功', value: '4' },
				{ title: '退单', value: '6' },
				{ title: '审结', value: '7' },
				{ title: '删单', value: '8' },
				{ title: '放行', value: '9' },
				{ title: '公自用物品核准通过', value: 'S' },
				{ title: '公自用物品退单', value: 'T' },
				{ title: '公自用物品待核准', value: 'U' },
			]
			let item = list.find(item => item.value === text)
			return item ? item.title : ""
		},
	}
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.table-page-search-wrapper {
	margin-top: -16px;
	margin-bottom: 16px;
}
.textGreen {
	color: darkgreen;
	font-weight: bold;
}
/deep/  .ant-table-expand-icon-th {
	background-color: #eaebed !important;
	border: 1px solid #d7dbe4 !important;
}
</style>