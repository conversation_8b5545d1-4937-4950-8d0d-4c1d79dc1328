package org.jeecg.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * AI用对应关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@Accessors(chain = true)
@TableName("ai_rel_table")
public class AiRelTable implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联类型（1企业中英文名称 2监管方式征免性质征免方式 3关别和口岸）
     */
    @TableField("TYPE")
    private String type;

    /**
     * 企业中文名称
     */
    @TableField("CN_NAME")
    private String cnName;

    /**
     * 企业英文名称
     */
    @TableField("EN_NAME")
    private String enName;

    /**
     * 监管方式
     */
    @TableField("TRADE_TYPE_CODE")
    private String tradeTypeCode;

    /**
     * 监管方式名称
     */
    @TableField("TRADE_TYPE_NAME")
    private String tradeTypeName;

    /**
     * 征免性质
     */
    @TableField("TAX_TYPE_CODE")
    private String taxTypeCode;

    /**
     * 征免性质名称
     */
    @TableField("TAX_TYPE_NAME")
    private String taxTypeName;

    /**
     * 征免方式
     */
    @TableField("FAX_TYPE_CODE")
    private String faxTypeCode;

    /**
     * 征免方式名称
     */
    @TableField("FAX_TYPE_NAME")
    private String faxTypeName;

    /**
     * 进出境关别
     */
    @TableField("OUT_PORT_CODE")
    private String outPortCode;

    /**
     * 入境口岸/离境口岸
     */
    @TableField("ENTY_PORT_CODE")
    private String entyPortCode;

    /**
     * 报关单Id
     */
    @TableField("DEC_ID")
    private String decId;

    /**
     * AI审单结果
     */
    @TableField("CHECK_CONTENT")
    private String checkContent;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_DATE")
    private Date updateDate;
}
