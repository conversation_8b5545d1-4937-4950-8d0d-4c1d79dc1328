<template>
	<div>
		<!-- 模态框 -->
		<a-modal
			:title="title"
			:visible="visible"
			@ok="handleOk"
			:width="450"
			@cancel="handleCancel"
		>
		<template slot="footer">
			<a-button type="default" @click="handleCancel">关闭</a-button>
			<a-button type="primary" :loading="saveLoading" @click="handleOk">保存</a-button>
		</template>
		<!-- 表单内容 -->
		<a-form-model ref="form" layout="inline" :rules="validatorRules" :model="model">
			<!-- 登记类型 -->
			<a-form-model-item label="登记类型" class="formItemClass" prop="registrationType">
				<a-select
					v-decorator="[
              'registrationType',
              { rules: [{ required: true, message: '请选择登记类型' }] }
            ]"
					style="width: 286px"
					placeholder="请选择登记类型"
					v-model="model.registrationType"
					@change="registrationTypeChange"
				>
					<a-select-option :value="1">根据业务</a-select-option>
					<a-select-option :value="2">根据报关单</a-select-option>
					<a-select-option :value="3">根据集装箱</a-select-option>
				</a-select>
			</a-form-model-item>

			<!-- 集装箱号/业务单号/报关单号 -->
			<a-form-model-item label="箱号/单号" class="formItemClass" prop="documentNumber">
				<a-input
					v-decorator="[
              'documentNumber',
              { rules: [{ required: true, message: '请输入集装箱号/业务单号/报关单号' }] }
            ]"
					placeholder="请输入集装箱号/业务单号/报关单号"
					style="width: 232px"
					v-model="model.documentNumber"
				/>
				<a-button type="primary" @click="handleChoose">选择</a-button>
			</a-form-model-item>

			<!-- 费用名称 -->
			<a-form-model-item label="费用名称" class="formItemClass" prop="feeName">
													<j-search-select-tag :regularFlag='true' placeholder="请选择" v-model="model.feeId"
                style="width: 286px" :dict="dictCodeFee" @change="(val,val2) => feeChange(val,val2, model)" />

			</a-form-model-item>

			<!-- 费用金额 -->
			<a-form-model-item label="费用金额" class="formItemClass" prop="feeAmount">
				<a-input-number
					v-decorator="[
              'feeAmount',
              { rules: [{ required: true, message: '请输入费用金额' }] }
            ]"
					placeholder="请输入费用金额"
					style="width: 286px"
					v-model="model.feeAmount"
				/>
			</a-form-model-item>

			<!-- 币制 -->
			<a-form-model-item label="币制" class="formItemClass" prop="currency">
				<j-search-select-tag
					v-decorator="[
				              'currency',
				              { rules: [{ required: true, message: '请输入币制' }] }
				            ]"
					dict="erp_currencies,currency,currency,1=1 order by currency_order desc"
					style="width: 310px"
					placeholder="请选择币制"
					:regularFlag="true"
					v-model="model.currency"
				/>
			</a-form-model-item>
<!-- 应收应付 -->
	<a-form-model-item label="应收/应付" class="formItemClass" prop="type">
			<a-select placeholder="请选择收支" v-model="model.type" 	v-decorator="[
                            `fees[${index}].type`,
                            { rules: [{ required: true, message: '请选择应收/应付' }] }
                          ]" style="width: 292px">
								<a-select-option value="1">应收</a-select-option>
								<a-select-option value="2">应付</a-select-option>
							</a-select>
	</a-form-model-item>
<!-- 结算单位 -->
		<a-form-model-item label="结算单位" class="formItemClass" prop="settlementId">
				<j-search-select-tag :regularFlag='true' placeholder="请选择" v-model="model.settlementId"
												@change="(val,val2) => settlementChange(val,val2, fee)" style="width: 297px"
                :dict="dictCodeS" />
		</a-form-model-item>
			<a-form-model-item label="" class="formItemClass" prop="">
			<attachments-info label="附件上传" style="margin-left: 10px" ref="attachments"/>

			</a-form-model-item>
		</a-form-model>
<!--			选择箱号/单号模态框-->
			<a-modal
				title="选择箱号/单号"
				:visible="chooseNoVisible"
				:width="550"
				@cancel="chooseNoVisible=false"
			>
				<template slot="footer">
					<a-button type="default" @click="chooseNoVisible=false">关闭</a-button>
				</template>
				<a-form layout="inline" @keyup.enter.native="searchQuery">
					<a-row :gutter="24">
						<a-col :xl="12" :sm="24" :xxl="6" :md="12" v-if="model.registrationType == 3">
							<a-form-item label="箱号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input :type="'no'" placeholder="请输入箱号" v-model="queryParam.containerId"></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="12" :sm="24" :xxl="6" :md="12" v-if="model.registrationType == 2">
							<a-form-item label="报关单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input :type="'no'" placeholder="请输入报关单号" v-model="queryParam.clearanceNo"></j-input>
							</a-form-item>
						</a-col>
						<a-col :xl="12" :sm="24" :xxl="6" :md="12" v-if="model.registrationType == 1">
							<a-form-item label="业务编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<j-input :type="'no'" placeholder="请输入业务编号" v-model="queryParam.orderProtocolNo"></j-input>
							</a-form-item>
						</a-col>

						<a-col :xl="12" :sm="24" :xxl="6" :md="12" style="margin-top: 4px">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
							<span style="margin-left: 4px;font-size: 12px">(双击进行回填)</span>
            </span>
						</a-col>
					</a-row>
				</a-form>
				<a-table
					:columns="model.registrationType == 1 ? orderInfoColumns : model.registrationType == 2 ?decColumns : containerColumns "
					:data-source="containerData"
					:pagination="containerPagination"
					rowKey="id"
					:loading="loading"
					:customRow="handleOverview"
					@change="handleTableChange"
				>
				</a-table>
			</a-modal>
		</a-modal>
	</div>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import AttachmentsInfo from '../component/AttachmentsInfo.vue'
import { filterObj } from '@/utils/util'
import Vue from 'vue'
import { TENANT_ID } from "@/store/mutation-types"

export default {
	components :{
		AttachmentsInfo
	},
	data() {
		return {
			dictCodeFee:'',
			dictCodeS:'',
			labelCol: {
				xs: { span: 6 },
				xxl:{ span: 6},
				xl:{ span: 6}
			},
			wrapperCol: {
				xs: { span: 18 },
			},
			queryParam:{},
			loading: false,
			containerColumns: [
				{
					title: '报关单号',
					dataIndex: 'clearanceNo',
					key: 'clearanceNo'
				},
				{
					title: '箱号',
					dataIndex: 'containerId',
					key: 'containerId'
				}
			],
			decColumns: [
				{
					title: '报关单号',
					dataIndex: 'clearanceNo',
					key: 'clearanceNo'
				},
				{
					title: '提运单号',
					dataIndex: 'billCode',
					key: 'billCode'
				}
			],
			orderInfoColumns: [
				{
					title: '业务编号',
					dataIndex: 'orderProtocolNo',
					key: 'orderProtocolNo'
				},
				{
					title: '提运单号',
					dataIndex: 'deliveryNumbers',
					key: 'deliveryNumbers'
				}
			],
			containerData: [
			],
			containerPagination: {
				total: 0,
				current: 1,
				pageSize: 10,
				showSizeChanger: true,
				size:"small",
				pageSizeOptions: [ '10', '20', '50'],
			},
			chooseNoVisible:false,
			saveLoading:false,
			title:'',
			visible: false, // 控制模态框显示
			form: this.$form.createForm(this), // 表单实例
			model:{},
			validatorRules: {
				registrationType: [{ required: true, message: '请选择登记类型' }],
				documentNumber: [{ required: true, message: '请输入集装箱号/业务单号/报关单号' }],
				feeName: [{ required: true, message: '请输入费用名称' }],
				feeAmount: [{ required: true, message: '请输入费用金额' }],
				currency: [{ required: true, message: '请选择币制' }]
			},
			url: {
				listDecContainer:'/DecHead/dec-head/listDecContainer',
				listDecHead:'/DecHead/dec-head/listDecHead',
				listOrderInfo:'/export/orderInfo/listOrderInfo',
				save: '/business/accountsReceivablePayable/save',
			},
		};
	},
	created(){
   // 查询当前租户id
        let tenantId = Vue.ls.get(TENANT_ID)
        if (!tenantId) {
          tenantId = 0;
        }
		this.dictCodeFee = 'fee_item,FEE_NAME,id,tenant_id='+ tenantId+' and del_flag=0 and DISABLE_FLAG=0';
		this.dictCodeS = 'settlement_info,SETTLEMENT_NAME,id,tenant_id='+ tenantId;
	},
	methods: {
			feeChange(e,e2,e3){
			e3.feeName = e2
			e3.feeId = e
		},
		settlementChange(e,e2,e3){
			e3.settlementName = e2
			e3.settlementId = e
		},
		searchQuery() {
			console.log(this.model.registrationType)
			switch (this.model.registrationType) {
				case 3:
					this.loadData(this.url.listDecContainer,1)
					break;
				case 2:
					this.loadData(this.url.listDecHead,1)
					break;
				case 1:
					this.loadData(this.url.listOrderInfo,1)
					break;
				default:
					break;
			}
		},
		getQueryParams() {
			var param = Object.assign({}, this.queryParam)
			param.pageNo = this.containerPagination.current
			param.pageSize = this.containerPagination.pageSize
			return filterObj(param)
		},
		searchReset() {
			this.queryParam={}
			switch (this.model.registrationType) {
				case 3:
					this.loadData(this.url.listDecContainer,1)
					break;
				case 2:
					this.loadData(this.url.listDecHead,1)
					break;
				case 1:
					this.loadData(this.url.listOrderInfo,1)
					break;
				default:
					break;
			}
		},
		loadData(url,arg) {
			let isEmpty = false
			if (!url) {
				this.$message.error('请设置url.list属性!')
				return
			}
			//加载数据 若传入参数1则加载第一页的内容
			if (arg === 1) {
				this.containerPagination.current = 1
			}
			var params = this.getQueryParams() //查询条件
			this.loading = true
			getAction(url, params)
				.then(res => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.containerData = res.result.records || res.result
						if (res.result.total) {
							this.containerPagination.total = res.result.total
						} else {
							this.containerPagination.total = 0
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		handleChoose(){
			this.chooseNoVisible = true
			if(this.model.registrationType == 1){
				this.loadData(this.url.listOrderInfo)
			}else if(this.model.registrationType == 2){
				this.loadData(this.url.listDecHead)
			}else if(this.model.registrationType == 3){
				this.loadData(this.url.listDecContainer)
			}
		},
		handleOverview(record, index) {
			return {
				on: {
					click: () => {
					},
					dblclick: () => {
						if(this.model.registrationType == 1){
							this.model.documentNumber = record.orderProtocolNo
						}else if(this.model.registrationType == 2){
							this.model.documentNumber = record.clearanceNo
						}else if(this.model.registrationType == 3){
							this.model.documentNumber = record.containerId
						}
						this.chooseNoVisible = false
					}
				}
			}
		},
		handleTableChange(pagination, filters, sorter) {
			// //分页、排序、筛选变化时触发
			// //TODO 筛选
			console.log(pagination)
			// if (!sorter.order) {
			// 	sorter.field = this.isorter.column
			// 	sorter.order = 'desc'
			// }
			// if (Object.keys(sorter).length > 0) {
			// 	this.isorter.column = sorter.field
			// 	this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
			// }
			this.containerPagination = pagination
			switch (this.model.registrationType) {
				case 3:
					this.loadData(this.url.listDecContainer)
					break;
				case 2:
					this.loadData(this.url.listDecHead)
					break;
				case 1:
					this.loadData(this.url.listOrderInfo)
					break;
				default:
					break;
			}
		},
		// 显示模态框
		showModal() {
			this.visible = true;
		},
		// 处理保存
		handleOk() {
			const that = this
			// 触发表单验证
			this.$refs.form.validate(valid => {
				if (valid) {
					that.saveLoading = true
					//赋值附件信息
					let attachmentList = this.$refs.attachments.getModel().attachmentList
					if (attachmentList) {
						this.model.attachmentList = this.$refs.attachments.getModel().attachmentList
					}

					console.log('最终保存的费用数据：', this.model)
					let accountsReceivablePayableList = []
					accountsReceivablePayableList.push(this.model)
					httpAction(this.url.save, accountsReceivablePayableList, 'post')
						.then((res) => {
							if (res.success) {
								that.visible=false
								that.$emit('loadData')
								that.$message.success('保存成功！')
							} else {
								that.$message.error(res.message)
							}
						})
						.finally(() => {
							that.saveLoading = false
						})
				} else {
					this.$message.error('表单校验失败！')
				}
			})
		},
		// 处理关闭
		handleCancel() {
			this.visible = false; // 关闭模态框
			this.$emit('loadData')
		},
		registrationTypeChange(e){
			this.model.registrationType = e
			this.$forceUpdate()
		}

	},
};
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
.formItemClass{
	margin-bottom: 10px;

}
/deep/ .ant-table-tbody .ant-table-row td {
	padding-top: 10px;
	padding-bottom: 10px;
	text-align: center
}
/deep/ .ant-table-thead > tr > th {
	padding: 8px;
}
/deep/ .ant-table-thead th {
	text-align: center !important;
}
/* 自定义样式 */
</style>