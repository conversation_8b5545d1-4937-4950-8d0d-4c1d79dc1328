<template>
  <j-form-container :disabled="formDisabled" style="height: auto">
    <a-form-model ref="form" :model="model" style="height: auto" slot="detail" class="attachmentsInfoLeft">
      <a-row>
        <a-col :span="8" style="height:auto">
          <a-form-model-item label="箱单模版" prop="attachmentsFileName">
            <j-upload
              bizPath="template"
              v-model="receiptName"
              style="margin-bottom: -20px;"
              @change="changeFile('receipt')"
							@remove="removeFile"
							type="receipt"
              :disabled="formDisabled"
              :buttonVisible="receiptName == ''"
              :maskClosable="false"
              :keyboard="false"
            />
          </a-form-model-item>
        </a-col>
				<a-col :span="8" style="height:auto">
					<a-form-model-item label="发票模版" prop="attachmentsFileName">
						<j-upload
							bizPath="template"
							v-model="releaseName"
							style="margin-bottom: -20px;"
							@change="changeFile('release')"
							@remove="removeFile"
							type="release"
							:disabled="formDisabled"
							:buttonVisible="releaseName == ''"
							:maskClosable="false"
							:keyboard="false"
						/>
					</a-form-model-item>
				</a-col>
<!--				外销合同模板-->
				<a-col :span="8" style="height:auto">
					<a-form-model-item label="外销合同模板" prop="attachmentsFileName">
						<j-upload
							bizPath="template"
							v-model="salescontractName"
							style="margin-bottom: -20px;"
							@change="changeFile('salescontract')"
							@remove="removeFile"
							type="release"
							:disabled="formDisabled"
							:buttonVisible="salescontractName == ''"
							:maskClosable="false"
							:keyboard="false"
						/>
					</a-form-model-item>
				</a-col>
      </a-row>
    </a-form-model>
  </j-form-container>
</template>

<script>
import {_postAction, downloadFile} from '@/api/manage'
import {saveDec} from "@/api/dec/dec";

export default {
  name: 'AttachmentsInfo',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    }
  },
  data() {
    return {
      model: {
        attachmentList: [],
      },
      receiptName: '', //入货通知书
      releaseName: '', //放行通知书
			salescontractName:'',//外销合同模板
      otherName: '', //其他
      modelChangeTimes: 0, //model更新次数
      confirmLoading: false,
			url: {
				updateFileTempl: '/EnterpriseInfo/enterpriseInfo/updateFileTempl',
				removeFileTempl: '/EnterpriseInfo/enterpriseInfo/removeFileTempl',
			}
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  updated() {
    this.modelChangeTimes = this.modelChangeTimes + 1
  },
  methods: {
		async removeFile(file, type) {
			console.log(type)
			console.log(file)
			const res = await _postAction(this.url.removeFileTempl, {
				templ: file.url,
				type: type
			}).catch(reason => this.$message.error(reason));
			console.log(res)
		},
    initModel(value) {
      let model = value
      if (model.attachmentList == undefined || model.attachmentList == null) {
        model.attachmentList = []
      }
      let attachments = model.attachmentList
      if (attachments != null && attachments.length > 0) {
        for (let i = 0; i < attachments.length; i++) {
          if (attachments[i].attachmentsFileType == 'receipt') {
            this.receiptName = attachments[i].attachmentsFileName
          } else if (attachments[i].attachmentsFileType == 'customs') {
            this.customsName = attachments[i].attachmentsFileName
          } else if (attachments[i].attachmentsFileType == 'release') {
            this.releaseName = attachments[i].attachmentsFileName
          } else if (attachments[i].attachmentsFileType == 'consignment') {
            this.consignmentName = attachments[i].attachmentsFileName
          } else if (attachments[i].attachmentsFileType == 'other') {
            this.otherName = attachments[i].attachmentsFileName
          }else if (attachments[i].attachmentsFileType =='salescontract') {
            this.salescontractName = attachments[i].attachmentsFileName
          }
        }
      }
      this.model = model
      this.modelChangeTimes--
    },
    modelChange() {
      let flag = false
      if (this.modelChangeTimes > 0) {
        this.modelChangeTimes = 0
        flag = true
      }
      return flag
    },
    async changeFile(fileType) {
			let fileName = ''
			if (fileType == 'receipt') {
				fileName = this.receiptName
			} else if (fileType == 'customs') {
				fileName = this.customsName
			} else if (fileType == 'release') {
				fileName = this.releaseName
			} else if (fileType == 'consignment') {
				fileName = this.consignmentName
			} else if (fileType == 'other') {
				fileName = this.otherName
			}else if (fileType == 'salescontract') {
				fileName = this.salescontractName
			}

			let attachments = this.model.attachmentList
			if (attachments == null || attachments.length == 0) {
				let attachment = {
					attachmentsFileName: fileName,
					attachmentsFileType: fileType,
				}
				this.model.attachmentList.push(attachment)
			} else {
				let editFlag = false
				for (let i = 0; i < attachments.length; i++) {
					if (attachments[i].attachmentsFileType == fileType) {
						editFlag = true
						attachments[i].attachmentsFileName = fileName
						break
					}
				}
				if (!editFlag) {
					let attachment = {
						attachmentsFileName: fileName,
						attachmentsFileType: fileType,
					}
					this.model.attachmentList.push(attachment)
				}
			}
			console.log(this.model)
			if (this.model.attachmentList && this.model.attachmentList.length > 0) {
				for (const item of this.model.attachmentList) {
					// 箱单
					// if (item.attachmentsFileType == 'receipt') {
						const res = await _postAction(this.url.updateFileTempl, {
							templ: item.attachmentsFileName,
							type: item.attachmentsFileType
						}).catch(reason => this.$message.error(reason));
						console.log(res)
						// 发票
					// } else if (item.attachmentsFileType == 'release') {
					//
					// }
				}
			}
		},
    getModel() {
      return this.model
    },
  },
}
</script>