package org.jeecg.modules.business.vo;

import lombok.Data;
import org.hibernate.procedure.spi.ParameterRegistrationImplementor;

import java.math.BigDecimal;

/**
 * 加贸手册 重量统计 进口保税科件vo
 */
@Data
public class PtsEmsAimgWeightStatisticsVO {

    /**
     * 直接进口重量(A)
     */
    private BigDecimal zjjkWeight;
    /**
     * 余料结转进口重量(B)
     */
    private BigDecimal yljzjkWeight;

    /**
     * 深加工结转进口重量(C)
     */
    private BigDecimal sjgjzjkWeight;

    /**
     * 料件退换进口重量(D)
     */
    private BigDecimal ljthjkWeight;
    /**
     * 料件内销重量(E)
     */
    private BigDecimal ljnxWeight;
    /**
     * 余量结转出口重量(F)
     */
    private BigDecimal yljzckWeight;
    /**
     * 料件复出重量(G)
     */
    private BigDecimal ljfcWeight;
    /**
     * 料件退换出口重量(H)
     */
    private BigDecimal ljthckWeight;
    /**
     * 边角料内销重量(I)
     */
    private BigDecimal bjlnxWeight;
    /**
     * 边角料复出重量(J)
     */
    private BigDecimal bjlfcWeight;
    /**
     * 边角料销毁重量(K)
     */
    private BigDecimal bjlxhWeight;
    /**
     * 料件销毁重量(L)
     */
    private BigDecimal ljxhWeight;
    /**
     * 进口保税料件重量合计(M) 进口保税料件重量合计=
     * 直接进口重量+余料结转进口重量+深加工结转进口重量+料件退换进口重量
     * -料件内销重量-余料结转出口重量-料件复出重量-料件退换出口重量-边角料内销重量-边角料复出重量-边角料销毁重量-料件销毁重量
     */
    private BigDecimal jkbsljWeightSum;


}
