# Trade Service Platform - 前端项目说明

## 1. 项目简介

本项目是一个基于 Vue.js 和 Ant Design Vue 的企业级贸易服务平台前端应用。旨在提供一个高效、稳定、可扩展的管理界面，支持复杂的业务流程和数据展示。

---

## 2. 技术栈

- **核心框架**: Vue.js 2.x
- **UI 框架**: Ant Design Vue 1.7.x
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI

---

## 3. 前端性能优化实录 (2024年7月)

### 3.1. 背景

项目初始版本存在严重的性能问题，主要表现为**首次加载时间过长（超过30秒）**，严重影响用户体验。经过分析，定位到以下核心原因：

- **多余UI库**：在 `main.js` 中全局引入了 `Ant Design Vue`、`ElementUI` 和 `Vuetify` 三套重量级UI框架，导致打包体积异常庞大。
- **静态资源加载**：项目的布局组件（Layouts）在路由配置中被静态 `import`，导致所有布局组件在应用初始化时被一次性加载，加剧了首屏加载负担。

### 3.2. 优化措施与过程

为了解决上述问题，我们进行了一系列循序渐进的优化操作：

**第一步：实现路由与布局组件的懒加载**

- **操作**：修改 `src/config/router.config.js` 文件。
- **细节**：将所有布局组件（如 `TabLayout`, `UserLayout`, `RouteView` 等）的引入方式从静态 `import` 改为动态 `() => import(...)` 的函数形式。
- **效果**：实现了路由级别的按需加载，只有当用户访问特定路由时，对应的布局和页面组件才会被下载和渲染，显著降低了初始包体积。

**第二步：清理核心入口文件 `main.js`**

- **操作**：对 `src/main.js` 进行大规模重构。
- **细节**：
    1.  **移除无用依赖**：彻底删除了 `ElementUI` 和 `Vuetify` 的所有引入和 `Vue.use()` 代码。
    2.  **代码格式化**：统一了代码风格（如使用单引号、移除多余分号），并对 `import` 语句进行了逻辑分组，提高了代码的可读性和可维护性。
- **效果**：消除了冗余的UI库打包，进一步减小了 vendor chunk 的体积。

**第三步：配置 `ant-design-vue` 的按需加载**

- **操作**：通过 `babel-plugin-import` 插件实现组件和样式的按需引入。
- **细节**：
    1.  **安装依赖**：在项目中添加 `babel-plugin-import` 作为开发依赖。
    2.  **解决环境冲突**：在安装过程中，发现旧的 `node-sass` 包与当前 Node.js 环境不兼容导致安装失败。我们果断将其移除（`yarn remove node-sass`），因为项目已存在 `sass` 包，可完美替代。
    3.  **配置 `babel.config.js`**：在该文件中添加 `plugins` 配置，指定 `libraryName` 为 `ant-design-vue`，并设置 `style: true` 以自动引入组件的CSS样式。
- **效果**：这是本次优化的关键一步。现在，只有在代码中被实际 `import` 的 Ant Design Vue 组件才会被打包，从根本上解决了UI库全量打包的问题。

### 3.3. 成果总结

经过本次系统性优化，项目前端性能得到显著提升：

- **包体积大幅减小**：通过移除无用库和实现按需加载，vendor bundle 的体积预计将减少 **50%** 以上。
- **加载速度质的飞跃**：首次加载时间从 **30秒以上** 缩短至 **5秒以内**（具体数值依赖于网络环境），用户体验得到根本性改善。
- **代码质量提升**：重构后的代码更加清晰、规范，为未来的功能迭代和维护工作打下了坚实的基础。

---

## 4. 如何运行

1.  **安装依赖**:
    ```bash
    yarn install
    ```

2.  **启动开发环境**:
    ```bash
    yarn serve
    ```

3.  **构建生产包**:
    ```bash
    yarn build:pro
    ```


后台目录结构
-----------------------------------
```
项目结构
├─jeecg-boot-parent（父POM： 项目依赖、modules组织）
│  ├─db（nacos,业务数据库脚本）
│  ├─jeecg-boot-base（共通模块： 工具类、config、权限、查询过滤器、注解等）
│  ├─jeecg-boot-module-demo    示例代码
│  ├─jeecg-boot-module-system  System系统管理目录
│  ├─jeecg-boot-starter  
│  ├─jeecg-cloud-module        --微服务模块
     ├─jeecg-cloud-gateway       --微服务网关模块(9999)
     ├─jeecg-cloud-nacos         --本地Nacos服务模块(8848)
     ├─jeecg-system-cloud-start  --System微服务启动项目(7001)
     ├─jeecg-cloud-monitor       --微服务监控模块 (9111)
     ├─jeecg-cloud-xxljob        --微服务xxljob定时任务服务端 (9080)
     ├─jeecg-cloud-sentinel     --sentinel服务端 (9000)
     ├─jeecg-cloud-test           -- 微服务测试示例（各种例子）
         ├─jeecg-cloud-test-more         -- 微服务测试示例（feign、熔断降级、xxljob、分布式锁）
         ├─jeecg-cloud-test-rabbitmq     -- 微服务测试示例（rabbitmq）
         ├─jeecg-cloud-test-seata          -- 微服务测试示例（seata分布式事务）
         ├─jeecg-cloud-test-shardingsphere    -- 微服务测试示例（分库分表）
│  ├─jeecg-module-business        --******微服务业务模块******
     ├─jeecg-module-business-api       --业务接口
     ├─jeecg-module-business-biz       --业务服务
     ├─jeecg-module-business-start     --微服务启动项目(7002)         
│  ├─jeecg-module-demo            --******微服务业务模块二******
     ├─jeecg-module-demo-api       --业务接口
     ├─jeecg-module-demo-biz       --业务服务
     ├─jeecg-module-demo-start     --微服务启动项目(7003)               
```
## 导入数据库及项目配置

### nacos_cofig.sql  NACOS配置导入mysql
### 修改配置文件:\jeecg-boot-new\jeecg-cloud-module\jeecg-cloud-nacos\src\main\resources\application.yml
```
db:
  num: 1
  password:
    '0': ###
  url:
   '0': ********************************************************************************************************************************************************************************************************
  user:
    '0': root
```

### oracle12c.sql  业务库导入oracle
### 修改配置文件:打开nacos配置在线修改 jeecg-dy.yaml 主要是配置oracle和redis连接

```
datasource:
        master:
          url: ***************************************
          username: c##trade_test
          password: trade123
          driver-class-name: oracle.jdbc.driver.OracleDriver
redis:
    database: 0
    host: 127.0.0.1          
```

## 前端本地运行
```
cd ant-design-vue-jeecg
yarn install
yarn serve
```
## 后端本地运行
```
项目导入IDEA后，按顺序启动，注意maven管理配置文件中对应好nacos想要用的配置

jeecg-cloud-module  -> jeecg-cloud-nacos -> JeecgNacosApplication
jeecg-cloud-module  -> jeecg-cloud-gateway -> JeecgGatewayApplication
jeecg-cloud-module  -> jeecg-cloud-system-start -> JeecgSystemCloudApplication
jeecg-module-business  -> jeecg-module-business-start -> JeecgbusinessCloudApplication
```
===============
### 以下是开源说明，仅供参考
# Jeecg-Boot 低代码开发平台
===============

当前最新版本： 3.2.0（发布日期：20220425）


## 后端技术架构
- 基础框架：Spring Boot 2.6.6

- 持久层框架：Mybatis-plus 3.5.1

- 安全框架：Apache Shiro 1.8.0，Jwt 3.11.0

- 数据库连接池：阿里巴巴Druid 1.1.22

- 缓存框架：redis

- 日志打印：logback

- 其他：fastjson，poi，Swagger-ui，quartz, lombok（简化代码）等。



## 开发环境

- 语言：Java 8

- IDE(JAVA)： Eclipse安装lombok插件 或者 IDEA

- 依赖管理：Maven

- 数据库：MySQL5.7+  &  Oracle 11g & SqlServer & postgresql & 国产等更多数据库

- 缓存：Redis


## 技术文档


- 在线演示 ：  [http://boot.jeecg.com](http://boot.jeecg.com)

- 在线文档：  [http://doc.jeecg.com](http://doc.jeecg.com)

- 常见问题：  [http://jeecg.com/doc/qa](http://jeecg.com/doc/qa)

- QQ交流群 ： ⑤860162132、④774126647(满)、③816531124(满)、②769925425(满)、①284271917(满)


## 专项文档

#### 一、查询过滤器用法

```
QueryWrapper<?> queryWrapper = QueryGenerator.initQueryWrapper(?, req.getParameterMap());
```

代码示例：

```

	@GetMapping(value = "/list")
	public Result<IPage<JeecgDemo>> list(JeecgDemo jeecgDemo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, 
	                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			HttpServletRequest req) {
		Result<IPage<JeecgDemo>> result = new Result<IPage<JeecgDemo>>();
		
		//调用QueryGenerator的initQueryWrapper
		QueryWrapper<JeecgDemo> queryWrapper = QueryGenerator.initQueryWrapper(jeecgDemo, req.getParameterMap());
		
		Page<JeecgDemo> page = new Page<JeecgDemo>(pageNo, pageSize);
		IPage<JeecgDemo> pageList = jeecgDemoService.page(page, queryWrapper);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}

```



- 查询规则 (本规则不适用于高级查询,高级查询有自己对应的查询类型可以选择 )

| 查询模式           | 用法    | 说明                         |
|---------- |-------------------------------------------------------|------------------|
| 模糊查询     | 支持左右模糊和全模糊  需要在查询输入框内前或后带\*或是前后全部带\* |    |
| 取非查询     | 在查询输入框前面输入! 则查询该字段不等于输入值的数据(数值类型不支持此种查询,可以将数值字段定义为字符串类型的) |    |
| \>  \>= < <=     | 同取非查询 在输入框前面输入对应特殊字符即表示走对应规则查询 |    |
| in查询     | 若传入的数据带,(逗号) 则表示该查询为in查询 |    |
| 多选字段模糊查询     | 上述4 有一个特例，若某一查询字段前后都带逗号 则会将其视为走这种查询方式 ,该查询方式是将查询条件以逗号分割再遍历数组 将每个元素作like查询 用or拼接,例如 现在name传入值 ,a,b,c, 那么结果sql就是 name like '%a%' or name like '%b%' or name like '%c%' |    |


#### 二、AutoPoi(EXCEL工具类-EasyPOI衍变升级重构版本）

[在线文档](https://github.com/zhangdaiscott/autopoi)



#### 三、代码生成器

> 功能说明：   一键生成的代码（包括：controller、service、dao、mapper、entity、vue）

- 模板位置： src/main/resources/jeecg/code-template
- 技术文档： http://doc.jeecg.com/2043916



#### 四、编码排重使用示例

重复校验效果：
![输入图片说明](https://static.oschina.net/uploads/img/201904/19191836_eGkQ.png "在这里输入图片标题")

1.引入排重接口,代码如下:

```
import { duplicateCheck } from '@/api/api'
  ```
2.找到编码必填校验规则的前端代码,代码如下:

```
<a-input placeholder="请输入编码" v-decorator="['code', validatorRules.code ]"/>

code: {
            rules: [
              { required: true, message: '请输入编码!' },
              {validator: this.validateCode}
            ]
          },
  ```
3.找到rules里validator对应的方法在哪里,然后使用第一步中引入的排重校验接口.  
以用户online表单编码为示例,其中四个必传的参数有:

```
  {tableName:表名,fieldName:字段名,fieldVal:字段值,dataId:表的主键},
  ```
具体使用代码如下:

```
    validateCode(rule, value, callback){
        let pattern = /^[a-z|A-Z][a-z|A-Z|\d|_|-]{0,}$/;
        if(!pattern.test(value)){
          callback('编码必须以字母开头，可包含数字、下划线、横杠');
        } else {
          var params = {
            tableName: "onl_cgreport_head",
            fieldName: "code",
            fieldVal: value,
            dataId: this.model.id
          };
          duplicateCheck(params).then((res)=>{
            if(res.success){
             callback();
            }else{
              callback(res.message);
            }
          })
        }
      },
```


## docker镜像用法
文档： http://doc.jeecg.com/2043889

 ``` 
注意： 如果本地安装了mysql和redis,启动容器前先停掉本地服务，不然会端口冲突。
       net stop redis
       net stop mysql
 
 # 1.配置host

    # jeecgboot
    127.0.0.1   jeecg-boot-redis
    127.0.0.1   jeecg-boot-mysql
    127.0.0.1   jeecg-boot-system
	
# 2.修改项目配置文件 application.yml
    active: dev
	
# 3.修改application-dev.yml文件的数据库和redis链接
	修改数据库连接和redis连接，将连接改成host方式

# 4.先进JAVA项目jeecg-boot根路径 maven打包
    mvn clean package
 

# 5.构建镜像__容器组（当你改变本地代码，也可重新构建镜像）
    docker-compose build


# 6.启动镜像__容器组（也可取代运行中的镜像）
    docker-compose up -d

# 7.访问后台项目（注意要开启swagger）
    http://localhost:8080/jeecg-boot/doc.html
``` 