package org.jeecg.modules.business.service.impl.receipt.handle.add.trade;

import org.jeecg.modules.business.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 抽象处理MessageType的业务Service
 *
 * <AUTHOR>
 *
 * @date 2020年9月8日 下午2:51:37
 */
public abstract class AbstractHandleMessageTypeService {
	/**
	 * 操作用户
	 */
	protected static final String OPERATE_USER = "RECEIPT_USER";

	/**
	 * 日志工具类
	 */
	protected Logger logger = LoggerFactory.getLogger(getClass());
	/**
	 * 出入库单 API
	 */
	@Autowired
	protected IStockHeadTypeService stockTypeApi;
	/**
	 * 核注单接口服务
	 */
	@Autowired
	protected INemsInvtHeadService nemsInvtApi;
	/**
	 * 核放单服务
	 */
	@Autowired
	protected IPassPortHeadService passApi;
	/**
	 * 报关单服务
	 */
	@Autowired
	protected IDecHeadService decApi;
	/**
	 * 申报表
	 */
	@Autowired
	protected IAppHeadTypeService appHeadTypeApi;

	@Autowired
	protected RedisTemplate<String, Object> redisTemplate;

	@Autowired
	protected IEdiReceiptService ediReceiptService;
	@Autowired
	protected IEdiStatusHistoryService ediStatusHistoryService;
	@Autowired
	protected IPtsEmsHeadService ptsEmsHeadService;

}
