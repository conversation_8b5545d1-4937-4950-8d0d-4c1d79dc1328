package org.jeecg.modules.business.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PtsEmsAexgWeightStatisticsVO {
    /**
     * 直接出口重量(A)
     */
    private BigDecimal zjckWeight;
    /**
     * 深加工结转出口重量(B)
     */
    private BigDecimal sjgjzckWeight;
    /**
     * 成品退换出口重量(C)
     */
    private BigDecimal cpthckWeight;
    /**
     * 成品退换进口重量(D)
     */
    private BigDecimal cpthjkWeight;
    /**
     * 出口保税成品重量合计(M)
     */
    private BigDecimal ckbscpWeightSum;

}
