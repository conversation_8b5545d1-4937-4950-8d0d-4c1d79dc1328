<template>
	<j-modal :disableSubmit="disableSubmit" :title="title" :visible="visible" :width="width" cancelText="关闭"
		selfCloseAction="closePop" @cancel="handleCancel">
		<template slot="footer">
			<a-button type="default" @click="handleCancel">关闭</a-button>
		</template>

		<a-spin :spinning="confirmLoading">
			<a-card :bordered="false">
				<!-- 查询区域 -->
				<div class="table-page-search-wrapper">
					<a-form layout="inline" @keyup.enter.native="searchQuery">
						<a-row :gutter="24">
							<a-col :md="12" :sm="24" :xl="6" :xxl="6">
								<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="模版名称">
									<a-input v-model="queryParam.tplName" placeholder="请输入模版名称"></a-input>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24" :xl="6" :xxl="6">
								<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出口标识">
									<a-select v-model="queryParam.ieFlag" allowClear placeholder="请选择" showSearch>
										<a-select-option value="I">进口</a-select-option>
										<a-select-option value="E">出口</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24" :xl="6" :xxl="6">
								<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="进出境关别">
									<j-dict-select-tag v-model="queryParam.outPortCode" dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
																		 placeholder="请选择，支持搜索" type="node-limit" />
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24" :xl="6" :xxl="6">
								<span class="table-page-search-submitButtons" style="float: left;overflow: hidden;">
									<a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
									<a-button icon="reload" style="margin-left: 8px" type="primary" @click="searchReset">重置</a-button>
									<a style="margin-left: 8px" @click="handleToggleSearch">
										{{ toggleSearchStatus ? '收起' : '展开' }}
										<a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
									</a>
								</span>
							</a-col>
							<template v-if="toggleSearchStatus">
								<a-col :md="12" :sm="24" :xl="6" :xxl="6">
									<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="运输方式">
										<j-dict-select-tag v-model="queryParam.shipTypeCode" dictCode="trans_type"
																			 placeholder="请选择，支持搜索" type="node-limit" />
									</a-form-item>
								</a-col>
								<a-col :md="12" :sm="24" :xl="6" :xxl="6">
									<a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申报地海关">
										<j-dict-select-tag v-model="queryParam.declarePlace" dictCode="erp_customs_ports,name,customs_port_code, 1=1 order by customs_port_code"
																			 placeholder="请选择，支持搜索" type="node-limit" />
									</a-form-item>
								</a-col>
							</template>
						</a-row>
					</a-form>
				</div>
				<!-- 查询区域-END -->

				<!-- 操作按钮 + 列表展示-->
				<div style="position: relative; top: -5px">
					<vxe-grid id="vxe-gridA" ref="xGrid" :column-config="{ isCurrent: true, isHover: true }" :columns="defColumns"
						:custom-config="{ storage: true }" :data="dataSource" :loading="loading" :pager-config="false"
						:row-config="{ keyField: 'id', isCurrent: true, isHover: true }" :show-overflow=true
						:toolbar-config="tableToolbar" :transfer="true" border class="xGrid-style" height="300" highlightHoverRow
						resizable row-id="id" show-overflow="tooltip" size="mini" @cell-click="cellClick"
						@checkbox-change="checkboxChangeEvent" @checkbox-all="checkboxChangeEvent" @cell-dblclick="cellDblclick">
						<template v-slot:toolbar_buttons>
							<div class="table-operator">
								<a-button icon="plus" type="primary" @click="handleAdd">新增</a-button>
								<a-button :loading="defaultLoading" icon="setting" type="primary" @click="handleDefault">设置默认</a-button>
								<a-tooltip slot="action" title="针对进口和出口，可以分别设置一条默认配置。">
									<a-icon style="margin-right:5px" theme="twoTone" type="question-circle" />
								</a-tooltip>
								<a-button v-if="selectedRowKeys.length > 0" ghost icon="delete" type="primary" @click="batchDel">批量删除
								</a-button>
							</div>
						</template>
						<template #isDefaultSlot="{ row }">
							<a-tag v-if="row.isDefault" color="#52c41a">是</a-tag>
						</template>
						<template #operate="{ row }">
							<a-dropdown>
								<a class="ant-dropdown-link" @click.stop=""> <a-icon type="setting" /></a>
								<a-menu slot="overlay">
									<a-menu-item>
										<a @click="handleEdit(row)">编辑</a>
									</a-menu-item>
									<a-menu-item>
										<a @click="handleDetail(row)">详情</a>
									</a-menu-item>
									<a-menu-item>
										<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(row)">
											<a>删除</a>
										</a-popconfirm>
									</a-menu-item>
								</a-menu>
							</a-dropdown>
						</template>
					</vxe-grid>
				</div>
			</a-card>
		</a-spin>
		<!-- AI配置新增编辑 -->
		<ai-setting-edit-modal ref="modalForm" @ok="handleAiSettingEditModalOk" />
	</j-modal>
</template>
<script>
import { XGridMixin } from "@/mixins/XGridMixin";
import { ajaxGetDictItems } from "@/api/api";
import { _postAction, deleteAction, getAction } from "@/api/manage";
import AiSettingEditModal from "@/views/Business/customs-declaration/components/AiSettingEditModal.vue";

export default {
	name: "AiSettingModal",
	mixins: [XGridMixin],
	components: {
		AiSettingEditModal
	},
	data() {
		return {
			title: "报关单AI配置",
			width: 1100,
			visible: false,
			confirmLoading: false,
			disableSubmit: false,
			defaultLoading: false,
			disableMixinCreated: true,
			tableToolbar: {
				perfect: true,
				refresh: {
					query: () => this.loadData(1)
				},
				zoom: false,
				custom: true,
				slots: {
					buttons: 'toolbar_buttons'
				}
			},
			/* 数据源 */
			dataSource: [],
			model: {},
			labelCol: {
				xs: { span: 5 },
				// sm: { span: 7 },
				xxl: { span: 5 },
				xl: { span: 9 }
			},
			wrapperCol: {
				xs: { span: 16 },
			},
			shipTypeCodeOptions: [],
			outPortCodeOptions: [],
			defColumns: [
				{
					type: 'checkbox',
					field: 'checkbox',
					align: 'center',
					width: 50,
					fixed: 'left',
				},
				{
					title: '默认',
					field: 'isDefault',
					width: 50,
					ellipsis: true,
					align: 'center',
					slots: {
						default: 'isDefaultSlot'
					}
				},
				{
					title: '模版名称',
					field: 'tplName',
					width: 160,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '进出口标识',
					field: 'ieFlag',
					align: 'center',
					width: 80,
					ellipsis: true,
					formatter: function ({ cellValue, row, column }) {
						if (cellValue == 'I') {
							return "进口";
						} else if (cellValue == 'E') {
							return "出口";
						} else {
							return cellValue;
						}
					}
				},
				{
					title: '进出境关别',
					field: 'outPortCode',
					width: 120,
					ellipsis: true,
					align: 'center',
					formatter: this.showOutPortCodeText
				},
				{
					title: '运输方式',
					field: 'shipTypeCode',
					width: 120,
					ellipsis: true,
					align: 'center',
					formatter: this.showShipTypeCodeText
				},
				{
					title: '申报地海关',
					align: 'center',
					field: 'declarePlace',
					width: 120,
					ellipsis: true,
					formatter: this.showOutPortCodeText
				},
				{
					title: '收发货人',
					field: 'optUnitName',
					width: 160,
					ellipsis: true,
					align: 'center'
				},
				// {
				// 	title: '价格基准',
				// 	field: 'priceReference',
				// 	width: 100,
				// 	ellipsis: true,
				// 	align: 'center',
				// 	formatter: function ({ cellValue, row, column }) {
				// 		if (cellValue == '1') {
				// 			return "单价";
				// 		} else if (cellValue == '2') {
				// 			return "总价";
				// 		} else {
				// 			return cellValue;
				// 		}
				// 	}
				// },
				{
					title: '创建人',
					field: 'createBy',
					width: 100,
					ellipsis: true,
					align: 'center'
				},
				{
					title: '创建日期',
					field: 'createDate',
					width: 150,
					ellipsis: true,
					align: 'center'
				},
				{
					field: 'operate',
					title: '操作',
					width: 50,
					fixed: 'right',
					align: 'center',
					slots: {
						default: 'operate'
					}
				}
			],
			url: {
				list: '/DecHead/dec-head/listAiSettings',
				deleteBatch: '/DecHead/dec-head/deleteAiConfigBatch',
				setDefault: '/DecHead/dec-head/setDefaultAiConfig'
			}
		}
	},
	created() {
		this.initDictData('trans_type')
		this.initDictData('erp_customs_ports,name,customs_port_code')
		this.loadData()
	},
	methods: {
		// 加载字典值
		initDictData(dictCode) {
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length > 0) {
				if (dictCode.includes('trans_type')) {
					this.shipTypeCodeOptions = dictOptions
				} else if (dictCode == 'erp_customs_ports,name,customs_port_code') {
					this.outPortCodeOptions = dictOptions
				}
			} else {
				//根据字典Code, 初始化字典数组
				ajaxGetDictItems(dictCode, null).then((res) => {
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						// this.initDictData(dictCode)
						let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
						if (dictOptions != null && dictOptions.length > 0) {
							if (dictCode.includes('trans_type')) {
								this.shipTypeCodeOptions = dictOptions
							} else if (dictCode == 'erp_customs_ports,name,customs_port_code') {
								this.outPortCodeOptions = dictOptions
							}
						}
					}
				})
			}
		},
		show() {
			this.visible = true
			this.loadData()
		},
		loadData() {
			if (!this.url.list) {
				this.$message.error('请设置url.list属性!')
				return
			}
			this.loading = true
			getAction(this.url.list, this.queryParam)
				.then((res) => {
					if (res.success) {
						this.dataSource = res.result.records || res.result
						this.initSelects()
					} else {
						this.$message.warning(res.message || res)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		/**
		 * 设置默认
		 */
		handleDefault() {
			if (this.selectedRowKeys.length !== 1) {
				this.$message.warning('请选择一条记录！')
				return
			}
			if (this.selectionRows[0].isDefault) {
				this.$message.warning('此条配置已经为默认！')
				return
			}
			let that = this
			that.$confirm({
				title: '操作确认',
				content: '确定要将此条配置设置为默认吗？',
				onOk: function () {
					that.defaultLoading = true
					_postAction(that.url.setDefault, {
						ids: that.selectedRowKeys.join(',')
					}).then(res => {
						if (res.success) {
							that.$message.success(res.message || res)
							that.loadData()
						} else {
							that.$message.warning(res.message || res)
						}
					})
						.finally(() => {
							that.defaultLoading = false
						})
				},
				onCancel: function () {
				}
			})
		},
		/**
		 * 新增
		 */
		handleAdd: function () {
			this.$refs.modalForm.add()
			this.$refs.modalForm.title = '新增'
			this.$refs.modalForm.disableSubmit = false
		},
		handleEdit: function (record) {
			this.$refs.modalForm.edit(record)
			this.$refs.modalForm.title = '编辑'
			this.$refs.modalForm.disableSubmit = false
		},
		handleDetail: function (record) {
			this.$refs.modalForm.edit(record)
			this.$refs.modalForm.title = '详情'
			this.$refs.modalForm.disableSubmit = true
		},
		searchReset() {
			this.queryParam = {}
			this.loadData(1)
		},
		handleDelete: function (record) {
			var that = this;
			deleteAction(that.url.deleteBatch, {
				ids: record.id
			}).then((res) => {
				if (res.success) {
					that.$message.success(res.message)
					that.loadData()
					that.onClearSelected()
				} else {
					that.$message.warning(res.message)
				}
			});
		},
		batchDel: function () {
			if (!this.url.deleteBatch) {
				this.$message.error('请设置url.deleteBatch属性!')
				return
			}
			if (this.selectedRowKeys.length <= 0) {
				this.$message.warning('请选择一条记录！')
				return
			} else {
				var ids = ''
				for (var a = 0; a < this.selectedRowKeys.length; a++) {
					ids += this.selectedRowKeys[a] + ','
				}
				var that = this
				this.$confirm({
					title: '确认删除',
					content: '是否删除选中数据?',
					onOk: function () {
						that.loading = true
						deleteAction(that.url.deleteBatch, { ids: ids })
							.then(res => {
								if (res.success) {
									that.$message.success(res.message)
									that.loadData()
									that.onClearSelected()
								} else {
									that.$message.warning(res.message)
								}
							})
							.finally(() => {
								that.loading = false
							})
					}
				})
			}
		},
		handleAiSettingEditModalOk() {
			this.loadData()
		},
		async cellDblclick({ row }) {
			this.handleEdit(row)
		},
		close() {
			this.queryParam = {}
			this.visible = false
		},
		handleCancel() {
			this.close()
		},
		showShipTypeCodeText({ cellValue, row, column }) {
			return this.getText(cellValue, this.shipTypeCodeOptions)
		},
		showOutPortCodeText({ cellValue, row, column }) {
			return this.getText(cellValue, this.outPortCodeOptions)
		},
		getText(value, arr) {
			var text
			if (value == null) {
				text = null
			} else {
				for (let i = 0; i < arr.length; i++) {
					if (value == arr[i].value) {
						text = arr[i].text
						break
					}
				}
			}
			return text
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';

/deep/ .data-rule-invalid {
	background: #f4f4f4;
	color: #bababa;
}

/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin-bottom: 10px
}

/deep/ .table-page-search-wrapper .table-page-search-submitButtons {
	margin-bottom: 16px
}

.vxe-grid /deep/ .vxe-toolbar {
	height: 32px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-header--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-table .vxe-body--column {
	height: 28px;
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button {
	padding: 0;
	min-width: 23px;
	height: 23px;
}

/deep/ .table-operator .ant-btn[data-v-1e361672] {
	margin: 5px 8px 0 0
}

.xGrid-style /deep/ .vxe-tools--operate .vxe-button .vxe-button--icon {
	min-width: 8px;
	margin-bottom: 1px;
}

/deep/ .vxe-grid--toolbar-wrapper {
	height: 34px;
}

/deep/ .ant-card-body {
	padding-top: 4px;
}

/deep/ .ant-modal-body {
	padding: 5px;
}
</style>