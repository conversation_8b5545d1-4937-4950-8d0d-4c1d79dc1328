package org.jeecg.modules.business.entity.receipt.vo.trade;

import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * 加工贸易报文回执
 * (实为海关通用报文格式 ZJ@20220909)
 *
 * <AUTHOR>
 *
 * @date 2020年6月18日 下午3:48:54
 */
@Data
public class PackageReceipt {

	/**
	 * 信封内容(报文头)
	 */
	private EnvelopInfo envelopInfo;

	/**
	 * 业务内容
	 */
	private DataInfo dataInfo;

	/**
	 * 信封内容(报文头)
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月18日 下午3:54:43
	 */
	@Data
	public static class EnvelopInfo {
		/**
		 * 报文唯一编号
		 */
		private String messageId;
		/**
		 * 报文类型
		 */
		private String messageType;
		/**
		 * 发送方编号
		 */
		private String senderId;
		/**
		 * 接收方编号
		 */
		private String receiverId;
		/**
		 * 发送时间
		 */
		private String sendTime;

		/**
		 * 报文文件名
		 */
		private String fileName;
	}

	/**
	 * 业务内容
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月18日 下午3:55:35
	 */
	@Data
	public static class DataInfo {
		/**
		 * 子包信息
		 */
		private PocketInfo pocketInfo;
		/**
		 * 业务数据
		 */
		private BussinessData bussinessData;
	}

	/**
	 * 数据子包信息
	 */
	@Data
	public static class PocketInfo {
		/**
		 * 子包信息
		 */
		private String pocketId;
		private int totalPocketQty;
		private int curPocketNo;
		private boolean isUnStructured;
	}

	/**
	 * 业务数据
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月22日 下午3:29:43
	 */
	@Getter
	public static class BussinessData {
		/**
		 * 报文类型
		 */
		private MessageType messageType;

		/**
		 * SET INV201节点
		 *
		 * @param messageType 报文类型
		 */
		public void setInv201(MessageType messageType) {
			this.messageType = messageType;
		}

		/**
		 * SET INV202节点
		 *
		 * @param messageType 报文类型
		 */
		public void setInv202(MessageType messageType) {
			this.messageType = messageType;
		}

		/**
		 * SET INV211节点
		 *
		 * @param messageType 报文类型
		 */
		public void setInv211(MessageType messageType) {
			this.messageType = messageType;
		}

		/**
		 * SET SAS201 申报表 节点
		 *
		 * @param messageType
		 */
		public void setSas201(MessageType messageType) {
			this.messageType = messageType;
		}

		/**
		 * SET SAS211 出入库单 节点
		 *
		 * @param messageType
		 */
		public void setSas211(MessageType messageType) {
			this.messageType = messageType;
		}

		/**
		 * SET SAS221 核放单节点
		 * @param messageType
		 */
		public void setSAS221(MessageType messageType) {
			this.messageType = messageType;
		}
		/**
		 * SET EML211 加贸手册节点
		 * @param messageType
		 */
		public void setEML211(MessageType messageType) {
			this.messageType = messageType;
		}
	}

	/**
	 * 报文类型
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月22日 下午3:31:26
	 */
	@Data
	public static class MessageType {
		/**
		 * 清单审批回执
		 */
		private HdeApprResult hdeApprResult;

		/**
		 * 检查信息
		 */
		private List<CheckInfo> checkInfo;
		/**
		 * 保税清单基本
		 */
		private  BondInvtBsc bondInvtBsc;
		/**
		 * 核注清单生成报关单回执信息
		 */
		private  InvApprResult invApprResult;
		/**
		 * 保税清单明细集合
		 */
		private List<BondInvtDt> bondInvtDt;
		/**
		 * 物流账册明细集合
		 */
		private List<BwsDt> bwsDt;

		/**
		 * SAS201 申报表表头
		 */
		private SasDclBsc sasDclBsc;

		/**
		 * SAS201 申报表表体
		 */
		private List<SasDclDt> sasDclDt;

		/**
		 * SAS211 出入库单表头
		 */
		private SasStockBsc sasStockBsc;

		/**
		 * SAS211 出入库单表体
		 */
		private List<SasStockDt> sasStockDt;

		/**
		 * SAS221 核放单表头
		 */
		private SasPassportBsc sasPassportBsc;
		/**
		 * EML211手册表头
		 */
		private EmlPutrecBsc emlPutrecBsc;
	}

	/**
	 * 清单审批回执
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月22日 下午3:33:16
	 */
	@Data
	public static class HdeApprResult {
		/**
		 * 企业录入编号
		 */
		private String etpsPreentNo;
		/**
		 * 业务编号
		 */
		private String businessId;
		/**
		 * 业务类型
		 */
		private String typecd;
		/**
		 * 处理结果
		 */
		private String manageResult;
	}

	/**
	 * 检查信息
	 */
	@Data
	public static class CheckInfo {
		/**
		 * 检查信息
		 */
		private String note;
	}

	/**
	 * 核注清单生成报关单回执信息
	 *
	 * <AUTHOR>
	 *
	 * @date 2020年6月23日 下午1:59:53
	 */
	@Data
	public static class InvApprResult {
		/**
		 * 核注清单数据中心统一编号
		 */
		private String invPreentNo;
		/**
		 * 报关单统一编号
		 */
		private String entrySeqNo;
		/**
		 * 处理结果(1:生成成功;2:生成失败)
		 */
		private String manageResult;
	}
}
